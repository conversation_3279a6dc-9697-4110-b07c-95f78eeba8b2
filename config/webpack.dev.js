const path = require('path');
const fs = require('fs');
const merge = require('webpack-merge');
const webpack = require('webpack');
const common = require('./webpack.common.js');
const InjectManifest = require('workbox-webpack-plugin').InjectManifest;
const nodeExternals = require('webpack-node-externals');

module.exports = merge.merge(common, {
  mode: 'development',
  devtool: 'source-map',
  devServer: {
    disableHostCheck: true,
    compress: true,
    host: 'offline.askfora.com',
    https: {
      key: fs.existsSync('../certs/server.key') ? fs.readFileSync('../certs/server.key') : '',
      cert: fs.existsSync('../certs/server.crt') ? fs.readFileSync('../certs/server.crt') : '',
    },
    port: 9000,
    proxy: {
      '/analytics*': 'http://localhost:8000/',
      '/api/v1/projects*': 'http://localhost:8000/',
      '/commands*': 'http://localhost:8000/',
      '/goauth*': 'http://localhost:8000/',
      '/gopay*': 'http://localhost:8000/',
      '/msg*': 'http://localhost:8000/',
      '/notify*': 'http://localhost:8000/',
      '/people/*': 'http://localhost:8000/',
      '/ping*': 'http://localhost:8000/',
      '/saml/*': 'http://localhost:8000/',
      '/settings*': 'http://localhost:8000/',
      '/shortcuts*': 'http://localhost:8000/',
      '/sockjs-node*': 'http://localhost:8000/',
    },
  },
  module: {
    rules: [
      {
        test: /\.jsx?$/,
        exclude: /node_modules/,
        enforce: 'pre',
        use: 'source-map-loader',
      },
      {
        test: /\.css$/,
        use: [
          { loader: 'style-loader' },
          { loader: 'css-loader', options: { sourceMap: true }}
        ]
      },
      {
        test: /\.less$/,
        use: [
          { loader: 'style-loader' },
          { loader: 'css-loader', options: { url: false, sourceMap: true } },
          { loader: 'less-loader', 
            options: { 
              lessOptions: { 
                math: 'always' ,
                sourceMap: true,
                webpackImporter: true,
              }
            }
          }
        ]
      }
    ]
  },
  plugins: [
    new InjectManifest({ 
      swSrc: path.resolve(__dirname, '..', 'src', 'web', 'sw.js') ,
      swDest: 'service-worker.js',
      dontCacheBustURLsMatching: /askfora/,
      excludeChunks: [
        'app',
        'askfora',
        'share',
        'static_info',
        'www',
      ],
      exclude: [
        /\.map$/,
        /manifest$/,
        /\.htaccess$/,
        /service-worker\.js$/,
        /sw\.js$/,
        /\.js$/,
        /\.ts$/,
        /\.tsx$/,
        /node_modules/,
      ],
      maximumFileSizeToCacheInBytes: 5242880,
      mode: 'development',
    }),
  ],
});
