const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CompressionPlugin = require("compression-webpack-plugin");
const version = require('../package.json').version;
const InjectManifest = require('workbox-webpack-plugin').InjectManifest;
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require("terser-webpack-plugin");

__webpack_public_path__ = '/';

const PUBLIC_PATH = '/';

const prod = true;

module.exports = {
  mode: prod ? 'production' : 'development', 
  devtool: prod ? undefined : 'source-map',
  context: path.resolve(__dirname, '..', 'src', 'web'),
  entry: {
    home: [path.resolve(__dirname, '..', 'src', 'web', 'home.tsx')],
  },
  output: {
    chunkFilename: '[name].js',
    path: path.resolve(__dirname, '..', 'lib', 'homepage'),
    publicPath: PUBLIC_PATH,
    globalObject: 'this',
    pathinfo: false,
    trustedTypes: true,
    assetModuleFilename: '[path][name][ext]',
    sourceMapFilename: '[path][name][ext].map',
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      './themes/default/assets': path.resolve(__dirname, '..', 'node_modules', 'semantic-ui-less', 'themes', 'default', 'assets'),
      '../../theme.config$': path.resolve(__dirname, '..', 'src/semantic-theme/theme.config'),
      './definitions/assets/images': path.resolve(__dirname, '..', 'src/web/images'),
      'source-map-loader': path.resolve(__dirname, 'noop-loader.js'),
    },
    fallback: { 
      fs: false,
      __dirname: false,
      zlib: require.resolve("browserify-zlib"),
      assert: require.resolve("assert/"),
      path: require.resolve("path-browserify"),
      stream: require.resolve("stream-browserify"),
      os: require.resolve("os-browserify/browser"),
      child_process: false,
      buffer: require.resolve('buffer'),
    },
  },
  module: {
    rules: [
      {
        test: /\.m?js/,
        resolve: {
          fullySpecified: false
        }
      },
      { 
        test: /[\.d]?\.tsx?$/, 
        exclude: [/..\/samples/, /..\/mobile/, /..\/local/, /..\/deploy/, /..\/certs/, /..\/test/, /..\/tools/, /..\/cache/],
        include: [/..\/src\/web/, /..\/src\/lang/, /..\/src\/types/],
        use: [
          { 
            loader: 'ts-loader',
            options: { 
              context: path.resolve(__dirname, '..'),
              configFile: path.resolve(__dirname, '..', 'tsconfig.web.json'),
              logLevel: 'info',
            } 
          },
        ]
      },
      { test: /\.node$/, use: "node-loader"},
      {
        test: /\.jpe?g$|\.gif$|\.ico$|\.png$|\.svg$/,
        type: 'asset/resource',
        generator: { filename: 'images/[name][ext]' },
        exclude: [path.resolve(__dirname, '..', 'src', 'web', 'icons'), path.resolve(__dirname, '..', 'src', 'web', 'images')],
      },
      {
        test: /\.woff(2)?(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        type: 'asset/resource',
        generator: { filename: 'fonts/[name][ext]' }
      },
      {
        test: /\.(ttf|eot)(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        type: 'asset/resource',
        generator: { filename: 'fonts/[name][ext]' }
      },
      {
        test: /\.otf(\?.*)?$/,
        type: 'asset/resource',
        generator: { filename: 'fonts/[name][ext]' }
      },
      {
        test: /icons\/favicon\.ico/,
        type: 'asset/resource',
        generator: { filename: '[name][ext]' },
      },
      {
        test: /icons\/icon.*\.png/,
        type: 'asset/resource',
      },
      {
        test: /images\/.*/,
        type: 'asset/resource',
      },
      {
        test: /contract\.pdf/,
        type: 'asset/resource',
      },
      {
        test: /\.txt$/,
        type: 'asset/source',
      },
      { 
        test: /\.css$/, 
        use: [
          { loader: 'style-loader' }, 
          { loader: 'css-loader' }
        ] 
      },
      {
        test: /\.less$/,
        use: [
          { loader: 'style-loader' },
          { loader: 'css-loader', options: { url: false } },
          { loader: 'less-loader', options: { lessOptions: { math: 'always'} } } ,
        ]
      },
    ],
  },
  optimization: {
    splitChunks: {
      chunks: 'all',
      minSize: 20000,
      maxSize: 1000000,
      minRemainingSize: 0,
      minChunks: 2,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      enforceSizeThreshold: 50000,
      cacheGroups: {
        defaultVendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true
        }
      } 
    },
    minimizer: prod ? [
      new TerserPlugin({
        exclude: /jimp/,
        terserOptions: {
          keep_fnames: true,
          compress: {
            reduce_funcs: false,
          },
          format: {
            semicolons: false,
            keep_quoted_props: true,
          },
        },
      }),
      new CssMinimizerPlugin(),
    ] : undefined,
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer'],
    }),
    new webpack.DefinePlugin({
      'process.browser': 'true',
      'process.env.PACKAGEVERSION': JSON.stringify(version),
    }),
    new HtmlWebpackPlugin({
      hash: true,
      title: 'AskFora',
      chunks: ['home'],
      template: path.resolve(__dirname, '..', 'src', 'web', 'home.html'),
      filename: 'index.html',
      favicon: path.resolve(__dirname, '..', 'src', 'web', 'icons', 'favicon.ico'),
      scriptLoad: 'defer',
    }),
    new InjectManifest({ 
      swSrc: path.resolve(__dirname, '..', 'src', 'web', 'sw.js') ,
      swDest: 'service-worker.js',
      dontCacheBustURLsMatching: /askfora/,
      excludeChunks: [
        'app',
        'askfora',
        'share',
        'static_info',
        'www',
      ],
      exclude: [
        /\.map$/,
        /manifest$/,
        /\.htaccess$/,
        /service-worker\.js$/,
        /sw\.js$/,
        /\.js$/,
        /\.ts$/,
        /\.tsx$/,
        /node_modules/,
      ],
      mode: prod ? 'production' : 'development',
      manifestTransforms: [
        (me, c) => {
          const manifest = me.map(e => {
            if (e.url.includes('LICENSE')) {
              e.url = '/LICENSE';
              e.revision = null;
            }
            return e;
          });
          return { manifest, warnings: []};
        },
      ],
    }),
    new MiniCssExtractPlugin({filename: "[name].css"}),
 
  ],
};
