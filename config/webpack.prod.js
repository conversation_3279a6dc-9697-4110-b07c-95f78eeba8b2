const path = require('path');
const merge = require('webpack-merge');
const common = require('./webpack.common.js');
const InjectManifest = require('workbox-webpack-plugin').InjectManifest;
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require("terser-webpack-plugin");

module.exports = merge.merge(common, {
  mode: 'production',
  module: {
    rules: [
      { 
        test: /\.css$/, 
        use: [
          { loader: 'style-loader' }, 
          { loader: 'css-loader' }
        ] 
      },
      {
        test: /\.less$/,
        use: [
          { loader: 'style-loader' },
          { loader: 'css-loader', options: { url: false } },
          { loader: 'less-loader', options: { lessOptions: { math: 'always'} } } ,
        ]
      },
    ]
  },
  optimization: {
    minimizer: [
      new TerserPlugin({
        exclude: /jimp/,
        terserOptions: {
          keep_fnames: true,
          compress: {
            reduce_funcs: false,
          },
          format: {
            semicolons: false,
            keep_quoted_props: true,
          },
        },
      }),
      new CssMinimizerPlugin(),
    ],
  },
  plugins: [
    new InjectManifest({ 
      swSrc: path.resolve(__dirname, '..', 'src', 'web', 'sw.js') ,
      swDest: 'service-worker.js',
      dontCacheBustURLsMatching: /askfora/,
      excludeChunks: [
        'app',
        'askfora',
        'share',
        'static_info',
        'www',
      ],
      exclude: [
        /\.map$/,
        /manifest$/,
        /\.htaccess$/,
        /service-worker\.js$/,
        /sw\.js$/,
        /\.js$/,
        /\.ts$/,
        /\.tsx$/,
        /node_modules/,
      ],
      mode: 'production',
      manifestTransforms: [
        (me, c) => {
          const manifest = me.map(e => {
            if (e.url.includes('LICENSE')) {
              e.url = '/LICENSE';
              e.revision = null;
            }
            return e;
          });
          return { manifest, warnings: []};
        },
      ],
    }),
    new MiniCssExtractPlugin({filename: "[name].css"}),
  ],
});
