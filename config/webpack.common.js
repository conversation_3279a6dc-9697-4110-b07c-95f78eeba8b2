const path = require('path');
const webpack = require('webpack');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const CompressionPlugin = require("compression-webpack-plugin");
const version = require('../package.json').version;

__webpack_public_path__ = '/';

const PUBLIC_PATH = '/';

module.exports = {
  // node: { fs: 'empty' },
  context: path.resolve(__dirname, '..', 'src', 'web'),
  entry: {
    // profile: [path.resolve(__dirname, '..', 'src', 'web', 'profile.tsx')],
    www: [path.resolve(__dirname, '..', 'src', 'web', 'www.tsx')],
    // askfora: [path.resolve(__dirname, '..', 'src', 'web', 'askfora.tsx')],
    // app: [path.resolve(__dirname, '..', 'src', 'web', 'app.tsx')],
    // share: [path.resolve(__dirname, '..', 'src', 'web', 'share.tsx')],
    // static_info: [path.resolve(__dirname, '..', 'src', 'web', 'static.tsx')],
    // skills: [path.resolve(__dirname, '..', 'src', 'web', 'skills.tsx')],
  },
  output: {
    chunkFilename: '[name].js',
    path: path.resolve(__dirname, '..', 'lib', 'public'),
    publicPath: PUBLIC_PATH,
    globalObject: 'this',
    pathinfo: false,
    trustedTypes: true,
    assetModuleFilename: '[path][name][ext]',
    sourceMapFilename: '[path][name][ext].map',
  },
  resolve: {
    extensions: ['.ts', '.tsx', '.js', '.jsx'],
    alias: {
      './themes/default/assets': path.resolve(__dirname, '..', 'node_modules', 'semantic-ui-less', 'themes', 'default', 'assets'),
      '../../theme.config$': path.resolve(__dirname, '..', 'src/semantic-theme/theme.config'),
      './definitions/assets/images': path.resolve(__dirname, '..', 'src/web/images'),
      'source-map-loader': path.resolve(__dirname, 'noop-loader.js'),
    },
    fallback: { 
      fs: false,
      __dirname: false,
      zlib: require.resolve("browserify-zlib"),
      assert: require.resolve("assert/"),
      path: require.resolve("path-browserify"),
      stream: require.resolve("stream-browserify"),
      os: require.resolve("os-browserify/browser"),
      child_process: false,
      buffer: require.resolve('buffer'),
    },
  },
  module: {
    rules: [
      {
        test: /\.m?js/,
        resolve: {
          fullySpecified: false
        }
      },
      { 
        test: /[\.d]?\.tsx?$/, 
        exclude: [/..\/samples/, /..\/mobile/, /..\/local/, /..\/deploy/, /..\/certs/, /..\/test/, /..\/tools/, /..\/cache/],
        include: [/..\/src\/web/, /..\/src\/lang/, /..\/src\/types/],
        use: [
          { 
            loader: 'ts-loader',
            options: { 
              context: path.resolve(__dirname, '..'),
              configFile: path.resolve(__dirname, '..', 'tsconfig.web.json'),
              logLevel: 'info',
            } 
          },
        ]
      },
      { test: /\.node$/, use: "node-loader"},
      {
        test: /\.jpe?g$|\.gif$|\.ico$|\.png$|\.svg$/,
        type: 'asset/resource',
        generator: { filename: 'images/[name][ext]' },
        exclude: [path.resolve(__dirname, '..', 'src', 'web', 'icons'), path.resolve(__dirname, '..', 'src', 'web', 'images')],
      },
      {
        test: /\.woff(2)?(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        type: 'asset/resource',
        generator: { filename: 'fonts/[name][ext]' }
        /*use: [
          { 
            loader: 'file-loader',
            options: {
              name: '/fonts/[name].[ext]',
            }
          }
        ]*/
      },
      {
        test: /\.(ttf|eot)(\?v=[0-9]\.[0-9]\.[0-9])?$/,
        type: 'asset/resource',
        generator: { filename: 'fonts/[name][ext]' }
      },
      {
        test: /\.otf(\?.*)?$/,
        type: 'asset/resource',
        generator: { filename: 'fonts/[name][ext]' }
      },
      {
        test: /icons\/favicon\.ico/,
        type: 'asset/resource',
        generator: { filename: '[name][ext]' },
      },
      {
        test: /icons\/icon.*\.png/,
        type: 'asset/resource',
      },
      {
        test: /images\/.*/,
        type: 'asset/resource',
      },
      {
        test: /contract\.pdf/,
        type: 'asset/resource',
      },
      {
        test: /\.txt$/,
        type: 'asset/source',
      },
    ],
  },
  optimization: {
    splitChunks: {
      chunks: 'all',
      minSize: 20000,
      maxSize: 1000000,
      minRemainingSize: 0,
      minChunks: 2,
      maxAsyncRequests: 30,
      maxInitialRequests: 30,
      enforceSizeThreshold: 50000,
      cacheGroups: {
        defaultVendors: {
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          reuseExistingChunk: true,
        },
        default: {
          minChunks: 2,
          priority: -20,
          reuseExistingChunk: true
        }
      } 
    },
  },
  plugins: [
    new webpack.ProvidePlugin({
      process: 'process/browser',
      Buffer: ['buffer', 'Buffer'],
    }),
    new webpack.DefinePlugin({
      'process.browser': 'true',
      'process.env.PACKAGEVERSION': JSON.stringify(version),
    }),
    new HtmlWebpackPlugin({
      hash: true,
      title: 'AskFora',
      chunks: ['www'],
      template: path.resolve(__dirname, '..', 'src', 'web', 'www.html'),
      filename: 'index.html',
      favicon: path.resolve(__dirname, '..', 'src', 'web', 'icons', 'favicon.ico'),
      scriptLoad: 'defer',
      excludeChunks: ['share', 'app', 'askfora', 'profile', 'skills'],
    }),
    /*new HtmlWebpackPlugin({
      hash: true,
      title: 'AskFora',
      chunks: ['profile'],
      template: path.resolve(__dirname, '..', 'src', 'web', 'profile.html'),
      filename: 'profile.html',
      favicon: path.resolve(__dirname, '..', 'src', 'web', 'icons', 'favicon.ico'),
      scriptLoad: 'defer',
      excludeChunks: ['static', 'share', 'app', 'askfora', 'www', 'skills'],
    }),*/
    /* new HtmlWebpackPlugin({
      hash: true,
      title: 'AskFora',
      chunks: ['askfora'],
      template: path.resolve(__dirname, '..', 'src', 'web', 'askfora.html'),
      filename: 'askfora.html',
      favicon: path.resolve(__dirname, '..', 'src', 'web', 'icons', 'favicon.ico'),
      scriptLoad: 'defer',
      excludeChunks: ['static', 'share', 'app', 'www', 'profile', 'skills'],
    }),*/
    /*new HtmlWebpackPlugin({
      hash: true,
      title: 'AskFora',
      chunks: ['app'],
      template: path.resolve(__dirname, '..', 'src', 'web', 'app.html'),
      filename: 'app.html',
      favicon: path.resolve(__dirname, '..', 'src', 'web', 'icons', 'favicon.ico'),
      scriptLoad: 'defer',
      excludeChunks: ['static', 'share', 'askfora', 'www', 'profile', 'skills'],
      inject: false,
    }),
    new HtmlWebpackPlugin({
      hash: true,
      title: 'AskFora',
      chunks: ['static_info'],
      template: path.resolve(__dirname, '..', 'src', 'web', 'static.html'),
      filename: 'static.html',
      favicon: path.resolve(__dirname, '..', 'src', 'web', 'icons', 'favicon.ico'),
      scriptLoad: 'defer',
      excludeChunks: ['askfora', 'share', 'app', 'www', 'profile', 'skills'],
    }),
    new HtmlWebpackPlugin({
      hash: true,
      title: 'Share AskFora',
      chunks: ['share'],
      template: path.resolve(__dirname, '..', 'src', 'web', 'share.html'),
      filename: 'share.html',
      favicon: path.resolve(__dirname, '..', 'src', 'web', 'icons', 'favicon.ico'),
      scriptLoad: 'defer',
      excludeChunks: ['askfora', 'static', 'app', 'www', 'profile', 'skills'],
    }),
    new HtmlWebpackPlugin({
      hash: true,
      title: 'Share AskFora',
      chunks: ['skills'],
      template: path.resolve(__dirname, '..', 'src', 'web', 'skills.html'),
      filename: 'skills.html',
      favicon: path.resolve(__dirname, '..', 'src', 'web', 'icons', 'favicon.ico'),
      scriptLoad: 'defer',
      excludeChunks: ['askfora', 'static', 'app', 'www', 'profile', 'share'],
    }),*/
    // new CompressionPlugin(),
  ],
  
};
