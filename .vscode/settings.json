{"cSpell.words": ["<PERSON><PERSON><PERSON>", "ask<PERSON>ra", "auth", "context", "datastore", "disallowable", "eversign", "id", "omer", "saml", "traj<PERSON>"], "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.fixAll.tslint": "explicit", "source.organizeImports": "explicit"}, "files.exclude": {"**/.DS_Store": true, "**/.git": true, "**/node_modules": true, "**/cache/**": true, "**/deploy/**": true, "**/lib/**": true, "test/fixtures": true, "lib": true, "cache": true}, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "javascript.format.insertSpaceAfterConstructor": true, "javascript.implicitProjectConfig.experimentalDecorators": true, "javascript.suggest.completeFunctionCalls": true, "javascript.updateImportsOnFileMove.enabled": "always", "telemetry.enableCrashReporter": false, "telemetry.enableTelemetry": false, "typescript.preferences.importModuleSpecifier": "relative", "typescript.preferences.quoteStyle": "single", "workbench.editor.enablePreviewFromQuickOpen": false, "tslint.alwaysShowRuleFailuresAsWarnings": false, "typescript.tsserver.log": "off", "editor.autoClosingBrackets": "never", "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": true, "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": true, "javascript.format.insertSpaceAfterSemicolonInForStatements": false, "files.watcherExclude": {".awcache": true, "**/.eslintcache": true, ".eslintcache": true, "**/.git": true, ".git": true, "**/deploy/**": true, "**/lib/**": true, "**/cache/**": true, "debug.out": true, "deploy": true, "dev.out": true, "lib": true, "cache": true}, "javascript.suggest.autoImports": false, "javascript.suggestionActions.enabled": false, "typescript.suggest.autoImports": false, "typescript.suggest.completeJSDocs": false, "typescript.suggest.enabled": false, "typescript.suggest.paths": false, "typescript.suggestionActions.enabled": false, "javascript.suggest.paths": false, "javascript.suggest.enabled": false, "typescript.preferences.renameShorthandProperties": false, "typescript.format.insertSpaceAfterSemicolonInForStatements": false, "typescript.suggest.includeAutomaticOptionalChainCompletions": false, "typescript.disableAutomaticTypeAcquisition": true, "search.useGlobalIgnoreFiles": true, "search.exclude": {"cache": true, "deploy": true, "lib": true, "local": true, "mobile": true}, "javascript.autoClosingTags": false, "html.autoClosingTags": false, "typescript.suggest.includeCompletionsWithSnippetText": false, "typescript.autoClosingTags": false, "terminal.integrated.env.linux": {"PATH": "/home/<USER>/.nvm/versions/node/v20.18.0/bin:${env:PATH}"}, "terminal.integrated.shellIntegration.enabled": true, "npm.packageManager": "npm"}