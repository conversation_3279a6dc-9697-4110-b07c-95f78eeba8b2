{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "command": "npm run dev",
      "name": "Run Dev",
      "request": "launch",
      "type": "node",
      "sourceMaps": true,
    },
    {
      "command": "npm run offline",
      "name": "Run Offline",
      "request": "launch",
      "type": "node",
      "sourceMaps": true,
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Node: 9222 - Unit Tests",
      "restart": true,
      "sourceMaps": true,
      "port": 9222,
      "outFiles": ["${workspaceRoot}/lib/**/*.js", "${workspaceRoot}/src/**/*.js", "${workspaceRoot}/test/src/**/*.js"],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Node: 9229 - Service Layer",
      "restart": true,
      "sourceMaps": true,
      "port": 9229,
      "outFiles": ["${workspaceRoot}/src/**/*.js", "${workspaceRoot}/lib/**/*.js"],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    },
    {
      "type": "node",
      "request": "attach",
      "name": "Node: Picker",
      "processId": "${command:PickProcess}",
      "sourceMaps": true,
      "restart": true,
      "outFiles": ["${workspaceRoot}/src/**/*.js", "${workspaceRoot}/lib/**/*.js"],
      "resolveSourceMapLocations": [
        "${workspaceFolder}/**",
        "!**/node_modules/**"
      ]
    }
  ]
}
