{"name": "Acme Co", "date": "July 31, 2019", "comms": ["+1 212 555 1212", "<EMAIL>"], "terms": true, "payment": [{"label": "Account", "value": "Acme, Co"}, {"label": "Bank", "value": "Acme Bank"}, {"label": "Routing", "value": "*********"}, {"label": "Account", "value": "**********"}], "items": [{"details": "my first job which is really long and should wrap properly and then it might cause some formatting issues maybe.", "start": "7/21/2019", "end": "7/31/2019", "rate": "hours", "quantity": 40, "unit_fee": 50, "total": 2000}, {"details": "AskFora <PERSON>e\n    test", "start": "", "end": "", "rate": "fixed", "quantity": 1, "unit_fee": 200, "total": 200}, {"details": "Expenses", "start": "", "end": "", "rate": "fixed", "quantity": 1, "unit_fee": 100.53, "total": 100.53}]}