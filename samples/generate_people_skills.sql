create or replace table people.people as select displayName, comms, tags, photos, names, urls, nickName, null as id from

(select id, displayName, array_agg(distinct(comm)) as comms, nickName from `people.people_*`
cross join unnest(comms) as comm 
where comm like "%@%.%" group by displayName, nickName, id) comm_table

left join 

( select id,  array_agg(struct(tag.type as type, tag.value as value, tag.index as index, tag.start as start)) as tags from `people.people_*`
cross join unnest(tags) as tag
group by id ) tag_table on comm_table.id = tag_table.id

left join 

(select id, array_agg(photo) as photos from `people.people_*`
cross join unnest(photos) as photo
group by id ) as photo_table on comm_table.id = photo_table.id

left join

(select id, array_agg(name) as names from `people.people_*`
cross join unnest(names) as name
group by id ) name_table on comm_table.id = name_table.id

left join

(select id, array_agg(url) as urls from `people.people_*`
cross join unnest(urls) as url
group by id ) url_table on comm_table.id = url_table.id


------

create or replace table people.temp_people as select displayName, [comm_table.comm] as comms, nickName, urls, tags, photos, names from 

(select displayName, comm, nickName from `people.people`
cross join unnest(comms) as comm 
where comm like "%@%.%" group by displayName, nickName, comm) comm_table

left join 

( select comm, array_agg(struct(tag.type as type, tag.value as value, tag.index as index, tag.start as start)) as tags from `people.people`
cross join unnest(tags) as tag
cross join unnest(comms) as comm 
where comm like "%@%.%"
group by comm ) tag_table on comm_table.comm = tag_table.comm

left join 

(select comm, array_agg(photo) as photos from `people.people`
cross join unnest(photos) as photo
cross join unnest(comms) as comm 
where comm like "%@%.%"
group by comm ) as photo_table on comm_table.comm = photo_table.comm

left join 

(select comm, array_agg(name) as names from `people.people`
cross join unnest(names) as name
cross join unnest(comms) as comm 
where comm like "%@%.%"
group by comm ) name_table on comm_table.comm = name_table.comm

left join 

(select comm, array_agg(url) as urls from `people.people`
cross join unnest(urls) as url
cross join unnest(comms) as comm 
where comm like "%@%.%"
group by comm ) url_table on comm_table.comm = url_table.comm
