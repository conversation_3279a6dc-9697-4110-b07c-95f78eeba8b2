{"appId": "a6f29ba9-670f-4142-8d3c-e87c9904866c", "appRoles": [], "availableToOtherTenants": false, "displayName": "AskFora Test", "errorUrl": null, "groupMembershipClaims": null, "optionalClaims": null, "acceptMappedClaims": null, "homepage": "http://saml.offline.askfora.com:8000", "informationalUrls": {"privacy": "https://askfora.com/privacy", "termsOfService": "https://askfora.com/terms"}, "identifierUris": ["https://askfora.com/2f35ecd8-4fa7-4d3e-8453-6369c9c5fddb"], "keyCredentials": [], "knownClientApplications": [], "logoutUrl": null, "oauth2AllowImplicitFlow": false, "oauth2AllowUrlPathMatching": false, "oauth2Permissions": [{"adminConsentDescription": "Allow the application to access AskFora Test on behalf of the signed-in user.", "adminConsentDisplayName": "Access AskFora Test", "id": "67ba5595-0844-4ca6-9c7c-846178cbd1e2", "isEnabled": true, "type": "User", "userConsentDescription": "Allow the application to access AskFora Test on your behalf.", "userConsentDisplayName": "Access AskFora Test", "value": "user_impersonation"}], "oauth2RequirePostResponse": false, "objectId": "1c76ac26-ab41-4e8b-9e15-ecff4a07a1f7", "parentalControlSettings": {"countriesBlockedForMinors": [], "legalAgeGroupRule": "Allow"}, "passwordCredentials": [{"customKeyIdentifier": "QQBzAGsAZgBvAHIAYQAgAFQAZQBzAHQAIABLAGUAeQA=", "endDate": "2299-12-31T05:00:00Z", "keyId": "dc3ce4c0-621e-40a1-b711-35925253fd73", "startDate": "2019-02-15T19:11:25.9919337Z", "value": null}], "publicClient": false, "replyUrls": ["http://saml.offline.askfora.com:8000"], "requiredResourceAccess": [{"resourceAppId": "00000002-0000-0000-c000-000000000000", "resourceAccess": [{"id": "311a71cc-e848-46a1-bdf8-97ff7156d8e6", "type": "<PERSON><PERSON>"}]}], "samlMetadataUrl": null}