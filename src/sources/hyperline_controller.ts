import axios from 'axios';

import config from '../config';
import data from '../data';

import ForaUser from '../session/user';
import { User as GUser } from '../types/globals';
import { Group } from '../types/group';
import { GlobalType, Person } from '../types/items';
import { EntityType, Uid } from '../types/shared';
import { User } from '../types/user';

import { MONTHS } from '../utils/datetime';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

const LOG_NAME = 'sources.hyperline';

let enabled = false;
let headers = {Authorization: 'Bearer ', "Content-Type": "application/json" };
let server: string;
let load_server: string;

const HYPERLINE_SERVER = 'https://api.hyperline.co';
const HYPERLINE_SANDBOX_SERVER = 'https://sandbox.api.hyperline.co';

const HYPERLINE_EVENT_SERVER = 'https://ingest.hyperline.co';
const HYPERLINE_EVENT_SANDBOX_SERVER = 'https://sandbox.ingest.hyperline.co';

const HYPERLINE_CUSTOMERS = '/v1/customers';
const HYPERLINE_COUPONS = '/v1/coupons';
const HYPERLINE_EVENT = '/v1/events';
const HYPERLINE_EVENT_BATCH = '/v1/events/batch';
const HYPERLINE_CUSTOMER = id => `/v1/customers/${id}`;
const HYPERLINE_CUSTOMER_ARCHIVE = id => `/v1/customers/${id}/archive`;
const HYPERLINE_CUSTOMER_CREDIT = (id, p?) => p ? `/v1/customers/${id}/credits/${p}/topup` : `/v1/customers/${id}/credits`;
const HYPERLINE_CUSTOMER_CREDIT_BALANCE = (id, p) => `/v1/customers/${id}/credits/${p}`;
const HYPERLINE_CUSTOMER_CREDIT_USAGE = (id, p) => `/v1/customers/${id}/credits/${p}/usage`;
const HYPERLINE_PLANS = '/v1/plans';
const HYPERLINE_PLAN_INFO = id => `/v1/plans/${id}`;
const HYPERLINE_PRODUCTS = '/v1/products';
const HYPERLINE_TOKEN = '/v1/integrations/components/token';
const HYPERLINE_SUBSCRIPTION = '/v2/subscriptions';
const HYPERLINE_SUBSCRIPTION_INFO = id => `/v2/subscriptions/${id}`;
const HYPERLINE_PAUSE_SUBSCRIPTION = id => `/v1/subscriptions/${id}/pause`;
const HYPERLINE_CANCEL_SUBSCRIPTION = id => `/v1/subscriptions/${id}/cancel`;
const HYPERLINE_REACTIVATE_SUBSCRIPTION = id => `/v1/subscriptions/${id}/reactivate`;
const HYPERLINE_REFRESH_SUBSCRIPTION = (id?) => id ? `/v2/subscriptions/${id}/refresh-seat-products` : '/v1/subscriptions/refresh';
const HYPERLINE_UPDATE_SUBSCRIPTION = id => `/v1/subscriptions/${id}/update`;
const HYPERLINE_INVOICES = (id?) => id ? `/v1/invoices/${id}` : `/v1/invoices`;
const HYPERLINE_PAYMENTS = '/v1/payments';

export enum PlanType {
  Learning = 'Learning',
  Professaionl = 'Professaionl',
  Teams = 'Teams',
}

enum PaymentMethodType {
  card = 'card',
  direct_debit = 'direct_debit', 
  direct_debit_ach = 'direct_debit_ach', 
  direct_debit_bacs = 'direct_debit_bacs', 
  transfer = 'transfer', 
  transfer_automated = 'transfer_automated', 
  external = 'external',
}

export interface PaymentMethod {
  id: string;
  type: PaymentMethodType;
  last_4_digits?: number;
  expiration_date?: string; // YYYY-MM
  brand?: string;
  account_number_ending?: string;
}

export interface HyperlineCustomer {
  id: string;
  name: string;
  type: 'corporate' | 'person';
  status: 'active' | 'archived';
  currency: string; // 'USD' https://en.wikipedia.org/wiki/ISO_4217#List_of_ISO_4217_currency_codes
  country: string; // 'US' https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
  timeZone: string; // America/New_York
  external_id: Uid;
  billing_email: string;
  billing_address: {
    name: string;
    line1: string;
    line2: string;
    city: string;
    zip: string;
    state: string;
    country: string; // https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
  },
  invoice_emails: string[];
  available_payment_methods: PaymentMethodType[];
  payment_method_type: PaymentMethodType;
  current_payment_method_type: PaymentMethodType;
  current_payment_method_id: string;
  transaction_provider_id: string;
  subscriptions: HyperlineSubscription[];
  created_at: Date | string;
  updated_at: Date | string;
  deleted_at: Date | string;
  current_payment_method: PaymentMethod;  
}


export interface HyperLineProduct {
  id: string;
  type: 'flat_fee' | 'seat' | 'credit';
  name: string;
  description: string;
  description_display_interval_dates: boolean;
  public_description: string;
  translations: any;
  properties: any;
  custom_properties: any;
  accounting: any;
  is_available_on_demand: boolean;
  is_available_on_subscription: boolean;
}

export interface HyperlinePlan {
  id: string;
  name: string;
  description: string;
  commitment_interval: {
    period: 'days' | 'weeks' | 'months' | 'years';
    count: number;
  };
  renew_automatically: boolean,
  trial_interval: {
    period: 'days' | 'weeks' | 'months' | 'years';
    count: number;
  };
  products: {
    id: string;
    name: string;
    description: string;
    payment_interval: {
      period: 'days' | 'weeks' | 'months' | 'years';
      count: number;
    };
    payment_schedule: 'start' | 'end';
    type: 'flat_fee' | 'seat' | 'credit';
  }[];
}

export interface HyperlineCredit {
  product_id: string;
  current_balance: number;
  customer_id: string;
  name: string;
  low_count_threshold: number;
  last_refreshed_at: Date | string;
  created_at: Date | string;
  updated_at: Date | string;
}

export interface HyperlineCreditTopUp {
  id: string;
  product_id: string;
  price: {
    id: string;
    amount: number;
    pack_size: number;
  },
  customer_id: string;
  payment_method_id: string;
  invoice_id: string;
  event_id: string;
  type: "topup",
  source: "api",
  amount_excluding_tax: number;
  credit_count: number;
  balance_after: number;
  created_at: Date | string;
  updated_at: Date | string;
}

export interface HyperlineCoupon {
  id: string;
  name: string;
  description: string;
  created_at: Date | string;
  type: 'amount' | 'percent';
  discount_amount?: number;
  discount_percent?: number;
  currency: string;
}

export interface HyperlineSubscription {
  id: string;
  currency: string; // 'USD' https://en.wikipedia.org/wiki/ISO_4217#List_of_ISO_4217_currency_codes
  status: 'active' | 'cancelled' | 'draft' | 'errored' | 'paused' | 'pending' | 'voided';
  purchase_order: string;
  properties: any;
  customer_id: string;
  plan_id: string;
  minimum_invoice_fee: number;
  invoicing_entity_id: string;
  checkout_session_id: string;
  commitment_interval: {
    period: 'days' | 'weeks' | 'months' | 'years';
    count: number;
  },
  renew_automatically: boolean;
  activation_strategy: 'start_date' | 'manually' | 'checkout';
  starts_at: Date | string;
  paused_at: Date | string;
  reactivate_at: Date | string;
  cancel_at: Date | string;
  cancellation_strategy: 'refund_prorata' | 'refund_custom' |  'charge_prorata' | 'charge_custom' | 'end_of_period' | 'do_nothing';
  cancellation_amount: number;
  estimated_arr: number;
  current_period_started_at: Date | string;
  current_period_ends_at: Date | string;
  next_payment_at: Date | string;
  renews_at: Date | string;
  trial_ends_at: Date | string;
  trial_delay_first_invoice: boolean;
  created_at: Date | string;
  products: {
    id: string,
    count: number;
    current_period_ends_at?: Date | string;
    current_period_started_at?: Date | string;
    desceription?: string;
    description_display_interval_dates?: boolean;
    detached_at?: Date | string;
    name?: string;
    next_payment_at?: Date | string;
    payment_interval?: {period: 'months' | 'years'; count: number};
    payment_schedule?: 'start'| 'end';
    prices?: { type: 'fee'; id: string, amount: number}[];
    type: 'flat_fee' | 'seat' | 'credit';
  }[];
  coupons: Partial<{
    id: string,
    name: string;
    description: string;
    expiration_date: Date | string;
    redemption_limit: number;
    created_at: Date | string;
    type: 'amount' | 'percent';
    discount_percent: number,
    discount_amount: number,
    repeat: 'forever' | 'once' | 'custom',
    product_ids: string[];
    expires_at: Date | string;
    apply_at: Date | string;
  }>[];
  /*
  "products": [
    {
      "id": "itm_FJKlqUb8COXw55",
      "name": "Product name",
      "description": "A description of the product.",
      "description_display_interval_dates": true,
      "payment_interval": {
        "period": "months",
        "count": 1
      },
      "payment_schedule": "start",
      "type": "flat_fee",
      "prices": [
        {
          "type": "fee",
          "amount": 123
        }
      ]
    }
  ],
  "coupons": [
    {
      "id": "cou_DKL4Xcb5VSa8CQ",
      "name": "Partner discount",
      "description": "<string>",
      "expiration_date": "2023-11-07T05:31:56Z",
      "redemption_limit": 123,
      "created_at": "2023-01-20T16:04:11Z",
      "type": "amount",
      "discount_amount": 2000,
      "currency": "EUR",
      "repeat": "once",
      "product_ids": [
        "<string>"
      ],
      "expires_at": "2023-01-20T16:04:11Z",
      "apply_at": "2023-01-20T16:04:11Z"
    }
  ],*/
  plan: {
    id: string;
    name: string;
  },
  checkout_session: {
    id: string;
    status: 'opened' | 'completed' | 'cancelled' | 'errored';
    available_payment_methods: PaymentMethodType[]
    redirect_url: string;
    send_to: {
      email: string;
      message: string;
    },
    url: string;
  },
  payment_method_type: PaymentMethodType;
  payment_method: PaymentMethod;
  payment_method_strategy: 'new' | 'current' | 'external';
  available_payment_method_types: PaymentMethodType[];
}

export interface HyperlineInvoice {
  id: string,
  number: string,
  type: 'invoice' | 'credit_note' | 'document';
  document_name: string;
  status: 'draft' | 'open' | 'to_pay' | 'grace_period' | 'partially_paid' | 'paid' | 'late' | 'voided' | 'closed' | 'error' | 'missing_info' | 'archived';
  reference: string;
  purchase_order: string;
  original_invoice_id: string;
  original_invoice_number: string;
  currency: string;
  total_amount: number;
  amount_due: number;
  amount_paid: number;
  amount_fixed: number;
  amount_excluding_tax: number;
  tax_rate: number;
  tax_amount: number;
  tax_scheme: string;
  discount_amount: number;
  conversion_rate: number;
  converted_amount: number;
  converted_at: string | Date;
  payment_method_id: string;
  attempt_count: number;
  custom_note: string;
  customer: Partial<HyperlineCustomer>;
  seller: any;
  subscription_id: string;
  period_starts_at: string | Date;
  period_ends_at: string | Date;
  emitted_at: string | Date;
  due_at: string | Date;
  refunded_at: string | Date;
  grace_period_ended_at: string | Date;
  settled_at: string | Date;
}

enum SUBSCRIPTION_STATUS {
  active = 0,
  pending = 1,
  paused = 2,
  draft = 3,
  errored = 4, 
  cancelled = 5,
  voided = 6,
}

export function sortSubscription(a: Partial<HyperlineSubscription>, b: Partial<HyperlineSubscription>) {
  return SUBSCRIPTION_STATUS[a.status] - SUBSCRIPTION_STATUS[b.status];
}

config.onLoad('hyperline', async (silent: boolean) => {
  try { 
    const key = config.get('HYPERLINE_API_KEY');
    if (key) {
      enabled = true;
      headers.Authorization = `Bearer ${key}`;
      if (config.isEnvDevelopment()) {
        server = HYPERLINE_SANDBOX_SERVER;
        load_server = HYPERLINE_EVENT_SANDBOX_SERVER;
      } else if(config.isRunningOnGoogle() || process.env.HYPERLINE_FORCE_PRODUCTION) {
        server = HYPERLINE_SERVER ;
        load_server = HYPERLINE_EVENT_SERVER;
      }
    }
  } catch(e) {
    logging.errorF(LOG_NAME, 'onLoad', `Error loading hyperline`, e);
  }
});

export async function archiveCustomer(customer: Partial<HyperlineCustomer>): Promise<Partial<HyperlineCustomer>> {
  if (!enabled) return undefined;

  if (customer.status === 'archived') return customer;

  const url = `${server}${HYPERLINE_CUSTOMER_ARCHIVE(customer.id)}`;

  try {
    const r= await axios.put(url, {}, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'archiveCustomer', customer.id, `Error archiving customer`, e);
  }
  return undefined;
}

export async function restoreCustomer(customer: Partial<HyperlineCustomer>): Promise<Partial<HyperlineCustomer>> {
  if (!enabled) return undefined;

  if (customer.status === 'active') return customer;

  customer.status = 'active';

  const url = `${server}${HYPERLINE_CUSTOMER(customer.id)}`;

  try {
    const r= await axios.put(url, customer, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'restoreCustomer', customer.id, `Error restoring customer`, e);
  }
  return undefined;
}

export async function setCustomerId(customer: Partial<HyperlineCustomer>, profile: Uid): Promise<Partial<HyperlineCustomer>> {
  if (!enabled) return undefined;

  customer.external_id = profile;

  const url = `${server}${HYPERLINE_CUSTOMER(customer.id)}`;

  try {
    const r= await axios.put(url, customer, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'setCustomerId', customer.id, `Error update customer external id`, e);
  }
  return undefined;
}

export async function createCustomer(user: User, self?: Partial<Person>): Promise<Partial<HyperlineCustomer>> {
  if (!enabled) return undefined;

  if (!self) self = await data.people.getUserPerson(user, user.profile);

  const url = `${server}${HYPERLINE_CUSTOMERS}`
  const country = user.locale ? user.locale.split('-')[1] : 'US';
  const currencies = parsers.countryCurrency(country)
  const body: Partial<HyperlineCustomer> = {
    name: self.displayName,
    type: 'person',
    status: 'active',
    currency: currencies?.length ? currencies[0] :  'USD',
    country,
    timeZone: user.timeZone,
    external_id: user.profile,
    billing_email: user.email,
    invoice_emails: [user.email],
    available_payment_methods: [PaymentMethodType.card],
    payment_method_type: PaymentMethodType.card,
  };

  try {
    const r = await axios.post(url, body, { headers } );
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'createCustomer', user.profile, `Error creating customer`, e);
  }
}

export async function getCustomer(customer_id: string): Promise<Partial<HyperlineCustomer>> {
  if (!enabled || !customer_id) return undefined;

  try {
    const url = `${server}${HYPERLINE_CUSTOMER(customer_id)}`
    const r = await axios(url, { headers } );
    return r?.data;
  } catch(e) {
    if (e?.response?.status === 404)  logging.warnF(LOG_NAME, 'getCustomer', `${customer_id} Customer not found`);
    else logging.errorF(LOG_NAME, 'getCustomer', `${customer_id} Error getting customer`, e);
  }
}

export async function lookupCustomer(user_group: User | GUser | Group): Promise<Partial<HyperlineCustomer>> {
  if (!enabled) return undefined;

  try {
    const params = new URLSearchParams();
    params.set('external_id', [EntityType.User, GlobalType.User ].includes(user_group.type) ? (user_group as User).profile : user_group.id);
    const url = `${server}${HYPERLINE_CUSTOMERS}`
    const r = await axios(url, { params, headers } );
    return r?.data?.data?.length ? r.data.data[0] : undefined;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'lookupCustomer', user_group.type === EntityType.User ? (user_group as User).profile : user_group.id, `Error looking up customer`, e);
  }
}

export async function lookupUser(customer_id: string): Promise<ForaUser> {
  if (!enabled || !customer_id) return undefined;

  const customer = await getCustomer(customer_id);
  const profile = customer?.external_id;
  if (profile) {
    const user = new ForaUser(profile);
    const init = await data.users.init(user, false);
    if (init) return user;
  }
  return undefined;
}

export async function getCoupons(plan_type?: PlanType): Promise<HyperlineCoupon[]> {
  if(!enabled) return undefined ;

  try {
    const url = `${server}${HYPERLINE_COUPONS}`;
    const r = await axios(url, { headers });
    return r?.data?.data?.filter(c => !plan_type || c.name?.includes(plan_type));
  } catch(e) {
    logging.errorF(LOG_NAME, 'getCoupons', 'Error getting coupons', e);
  }

  return undefined;
}

export async function getPlans(): Promise<HyperlinePlan[]> {
  if (!enabled) return undefined;

  try {
    const url = `${server}${HYPERLINE_PLANS}`;
    const r = await axios(url, { headers });
    const plan_set = r?.data?.data;
    if (plan_set) {
      const plans = await Promise.all(plan_set.map(async plan => {
        const url = `${server}${HYPERLINE_PLAN_INFO(plan.id)}`;
        const r = await axios(url, { headers });
        return r?.data;
      }));

      return plans.filter(p => p);
    }
  } catch(e) {
    logging.errorF(LOG_NAME, 'getPlans', `Error getting plans`, e);
  }

  return undefined;
}

export async function getPlan(plan_type: PlanType, commit: 'months' | 'years' = 'months'): Promise<HyperlinePlan> {
  const plans = await getPlans();
  return plans.find(p => p.name.includes(plan_type) && p.commitment_interval?.period === commit);
}

export async function getProducts(): Promise<HyperLineProduct[]> {
  if (!enabled) return [];

  try {
    const url = `${server}${HYPERLINE_PRODUCTS}`;
    const r = await axios(url, { headers });
    return r?.data?.data;
  } catch(e) {
    logging.errorF(LOG_NAME, 'getProduct', `Error getting product`, e);
  }

  return [];
}

export async function subscriptions(customer: Partial<HyperlineCustomer>, plans?: HyperlinePlan[]): Promise<Partial<HyperlineSubscription>[]> {
  if (!enabled) return undefined;

  const plan_ids = plans ? plans.map(p => p.id) : []
  const active_subs = customer?.subscriptions?.filter(s => ['paused', 'pending', 'active', 'errored'].includes(s.status) && (!plans || plan_ids.includes(s.plan_id))).sort(sortSubscription);
  if (active_subs?.length) {
    const subs: HyperlineSubscription[] = [];
    for(const active_sub of active_subs) {
      try {
        const url = `${server}${HYPERLINE_SUBSCRIPTION_INFO(active_sub.id)}`;
        const r = await axios(url, { headers });
        subs.push(r?.data);
      } catch(e) {
        logging.errorF(LOG_NAME, 'subscription', `${customer?.id} Error getting subscription`, e);
      }
    }
    return subs;
  }
  return undefined;
}

export async function getSubscription(sub_id: string): Promise<Partial<HyperlineSubscription>> {
  let sub;
  try {
    const url = `${server}${HYPERLINE_SUBSCRIPTION_INFO(sub_id)}`;
    const r = await axios(url, { headers });
    sub = r?.data;
  } catch(e) {
    logging.errorF(LOG_NAME, 'subscription', `Error getting subscription ${sub_id}`, e);
  }

  if(sub) {
    const inv = await invoice(sub);
    if(inv?.coupons && !sub.coupons?.length) sub.coupons = inv.coupons;
  }

  return sub;
}

export function subscribed(sub: Partial<HyperlineSubscription>, group_admin?: boolean): boolean {
  return sub?.status === 'active' && (!!sub?.checkout_session || !!sub.payment_method || (group_admin && sub.payment_method_type === 'transfer'));
}

export async function subscribe(customer: Partial<HyperlineCustomer>, plan: HyperlinePlan, trial_ends_at?: string | Date, coupon?: HyperlineCoupon, coupon_expires_at?: string | Date): Promise<Partial<HyperlineSubscription>> {
  if (!enabled) return undefined;

  let url: string; 
  let body: Partial<HyperlineSubscription> = {};

  // pause any other subscription
  await pause(customer);

  let refetch = false;
  const has_sub = customer.subscriptions?.filter(s => s.plan_id === plan.id && ['paused', 'pending', 'active', 'errored'].includes(s.status)).sort(sortSubscription);
  if (has_sub?.length) {
    url = `${server}${HYPERLINE_REACTIVATE_SUBSCRIPTION(has_sub[0].id)}`;
    refetch = true;
  } else {
    const seat_product = plan.products?.find(p => p.type === 'seat');  

    let renew_automatically = true;

    let coupons: {
      id: string;
      repeat: 'once' | 'forever' | 'custom';
      apply_at: string | Date;
      expires_at: string | Date;
      product_ids: string[];
    }[] = undefined;

    if(coupon) {
      renew_automatically = false;
      coupons = [{
        id: coupon.id,
        repeat: 'once',
        apply_at: new Date().toISOString(),
        expires_at: coupon_expires_at ? new Date(coupon_expires_at).toISOString() : new Date().toISOString(),
        product_ids: plan.products.map(p => p.id),
      }];
    }

    url = `${server}${HYPERLINE_SUBSCRIPTION}`;
    body = {
      customer_id: customer.id,
      commitment_interval: plan.commitment_interval,
      renew_automatically,
      starts_at: new Date().toISOString(),
      trial_ends_at: trial_ends_at ? new Date(trial_ends_at).toISOString() : MONTHS(new Date(), 1).toISOString(),
      trial_delay_first_invoice: trial_ends_at && new Date(trial_ends_at) > new Date(),
      activation_strategy: 'start_date',
      payment_method_strategy: 'new',
      available_payment_method_types: [PaymentMethodType.card],
      cancellation_strategy: 'end_of_period',
      plan_id: plan.id,
      products: seat_product ? [{id: seat_product.id, count: 1, type: 'seat'}] : undefined,
      coupons,
    }
  }

  try {
    const r = await axios.post(url, body, { headers });
    if (!refetch) return r?.data;

    if(r?.data?.id) return getSubscription(r.data.id);
  } catch(e) {
    logging.errorF(LOG_NAME, 'subscribe', `${customer.id} Error subscribing: ${JSON.stringify(e.response.data.message)}`, e);
  }
}

export async function invoices(customer?: Partial<HyperlineCustomer>, current?: boolean): Promise<Partial<HyperlineInvoice>[]> {
  if (!enabled) return undefined;

  const url = `${server}${HYPERLINE_INVOICES()}${customer ? '?customerId=' : ''}${customer ? customer.id : ''}${current ? '&periodEnd__gte=' : ''}${current ? new Date().toISOString() : ''}`;
  try {
    const r = await axios.get(url, { headers });
    return r?.data?.data;
  } catch(e) {
    logging.errorF(LOG_NAME, 'invoices', `$Error getting invoices`, e);
  }
}

export async function invoice(subscription: Partial<HyperlineSubscription>) {
  if (!enabled) return undefined;

  let invs;
  const url = `${server}${HYPERLINE_INVOICES()}?subscriptionId=${subscription.id}`;
  try {
    const r = await axios.get(url, { headers });
    invs = r?.data?.data;
  } catch(e) {
    logging.errorF(LOG_NAME, 'invoices', `$Error getting invoices`, e);
  }

  if(invs?.length) {
    const url = `${server}${HYPERLINE_INVOICES(invs[0].id)}`;
    try {
      const r = await axios.get(url, { headers });
      return r?.data;
    } catch(e) {
      logging.errorF(LOG_NAME, 'invoices', `$Error getting invoices`, e);
    }
  }
}

export async function charge(subscription: Partial<HyperlineSubscription>) {
  if (!enabled) return undefined;

  if (subscription.status === 'active' && subscription.checkout_session?.status === 'opened') {
    const all_invoices = await invoices();

    const invoice = all_invoices?.find(i => i.subscription_id === subscription.id);

    if (invoice) {
      const url = `${server}${HYPERLINE_PAYMENTS}`
      const body = {
        type: 'one_time',
        customer_id: subscription.customer_id,
        items: [{ id:  invoice.id } ],
        charging_method: 'immediately',
      }

      try {
        const r = await axios.post(url, body, { headers });
        return r?.data;
      } catch(e) {
        logging.errorF(LOG_NAME, 'charge', `${subscription.customer_id} Error charging subscription`, e);
      }
    }
  }
}

export async function refresh(customer?: Partial<HyperlineCustomer>) {
  if (!enabled) return undefined;

  try {
    const active_sub = customer?.subscriptions?.filter(s => ['paused', 'pending', 'active', 'errored'].includes(s.status)).sort(sortSubscription);
    const url = `${server}${HYPERLINE_REFRESH_SUBSCRIPTION(active_sub?.length ? active_sub[0].id : undefined)}`;
    const r = await axios.post(url, undefined, {headers});
    return r?.data;
  } catch(e) {
    logging.errorF(LOG_NAME, 'refresh', `${customer?.id} Error refreshing subscription`, e);
  }
}

export async function update(customer: Partial<HyperlineCustomer>, sub_info: Partial<HyperlineSubscription>, count: number = 1) {
  if (!enabled) return undefined;

  const active_sub = customer.subscriptions?.filter(s => ['paused', 'pending', 'active', 'errored'].includes(s.status)).sort(sortSubscription);
  if (active_sub?.length) {
    const product = sub_info.products?.find(p => p.type === 'seat');
    const url = `${server}${HYPERLINE_UPDATE_SUBSCRIPTION(active_sub[0].id)}`;
    const body = {
      application_schedule: 'immediately',
      payment_schedule: 'immediately',
      calculation_method: 'pro_rata',
      type: 'update_count',
      payload: product ? {
        product_id: product.id,
        count, 
      } : undefined,
    }

    try {
      const r = await axios.post(url, body, {headers});
      return r?.data;
    } catch(e) {
      logging.errorF(LOG_NAME, `update`, `${customer?.id} Error updating subscription`, e);
    }
  }

  return undefined;
}

export async function cancel(customer: Partial<HyperlineCustomer>) {
  if (!enabled) return undefined;

  const active_sub = customer?.subscriptions?.filter(s => ['paused', 'pending', 'active', 'errored'].includes(s.status)).sort(sortSubscription);

  if (active_sub?.length) {
    const url = `${server}${HYPERLINE_CANCEL_SUBSCRIPTION(active_sub[0].id)}`;
    const body = {
      cancel_at: new Date().toISOString(),
      pro_rata: true,
    }

    try {
      const r = await axios.post(url, body, { headers } );
      return r?.data;
    } catch(e) {
      logging.errorF(LOG_NAME, 'cancel', `${customer?.id} Error canceling subscription`, e);
    }
  }

  return undefined;
}

export async function pause(customer: Partial<HyperlineCustomer>, sub?: Partial<HyperlineSubscription>) {
  if (!enabled) return undefined;

  const active_sub = sub ? [sub] : customer?.subscriptions?.filter(s => ['pending', 'active', 'errored'].includes(s.status)).sort(sortSubscription);

  if (active_sub?.length) {
    for(const sub of active_sub) {
      const url = `${server}${HYPERLINE_PAUSE_SUBSCRIPTION(sub.id)}`;
      try {
        const r = await axios.post(url, {}, { headers } );
        return r?.data;
      } catch(e) {
        logging.errorF(LOG_NAME, 'pause', `${customer?.id} Error pausing subcription`, e);
      }
    }
  }

  return undefined;
}

export async function reactivate(customer: Partial<HyperlineCustomer>, sub?: Partial<HyperlineSubscription>) {
  if (!enabled) return undefined;

  const active_sub = sub ? [sub] : customer?.subscriptions?.filter(s => ['paused', 'pending', 'errored', 'cancelled'].includes(s.status)).sort(sortSubscription);
  if (active_sub?.length) {
    const url = `${server}${HYPERLINE_REACTIVATE_SUBSCRIPTION(active_sub[0].id)}`;
    try {
      const r = await axios.post(url, {}, { headers } );
      return r?.data;
    } catch(e) {
      logging.errorF(LOG_NAME, 'reactivate', `${customer?.id} Error reactivating customer`, e);
    }
  }
  return undefined;
}

export function subscriptionEnabled(sub: Partial<HyperlineSubscription>, group?: Group): boolean {
  if(!sub) return false;
  if(new Date(sub.trial_ends_at) >= new Date()) return true;
  if (['pending', 'active'].includes(sub.status)) {
    if (sub.checkout_session?.status === 'completed' || sub.payment_method) return true;
  }

  if(group && sub.payment_method_type === 'transfer') return true;

  const now = new Date();
  /*if(sub.coupons?.length && sub.coupons.filter(c => 
      c.type === 'percent' && c.discount_percent === 100 &&
      ( c.repeat === 'forever' || 
        ( c.repeat === 'once' &&  c.apply_at && new Date(c.apply_at) < new Date(sub.current_period_ends_at)) ||
        ( c.repeat === 'custom' && c.apply_at && c.expires_at && new Date(c.expires_at) >= now && new Date(c.apply_at) <= now)
    )).length) return true;*/
  
  if((!sub.products?.length || (
      new Date(sub.products[0].current_period_ends_at) >= now && 
      new Date(sub.products[0].current_period_started_at) <= now
    )) && 
    sub.coupons?.length && sub.coupons.filter(c => 
    ( c.discount_percent === 100 || 
     (sub.products?.length && sub.products[0].prices?.length && sub.products[0].prices[0].amount === c.discount_amount)
    )
  ).length) return true;

  return false;
}

export async function customerGroup(user: ForaUser): Promise<{group: Group, customer}> {
  let group;

  // check groups first
  let customer: Partial<HyperlineCustomer>;
  const group_customers = user.loaded_groups ? (await Promise.all(Object.values(user.loaded_groups).map(async group => {
    const customer = await lookupCustomer(group)
    return { group, customer }
  }))).filter(c => c && c.customer) : [];

  if (group_customers.length) {
    group = group_customers[0].group;
    customer = group_customers[0].customer;
  }

  return {group, customer};
}

export async function checkSubscription(user: ForaUser, plans?: HyperlinePlan[], create_plan?: HyperlinePlan): Promise<Partial<HyperlineSubscription>> {
  if (!enabled) return undefined;

  const plan_ids = plans ? plans.map(p => p.id) : undefined;

  let {group, customer} = await customerGroup(user);

  if (!customer || 
    (plans?.length && customer?.subscriptions && !customer.subscriptions.find(s => plan_ids.includes(s)))
  ) customer = await lookupCustomer(user);

  if (!customer) {
    const self = await data.people.getUserPerson(user, user.profile);
    if (self) customer = await createCustomer(user, self);
  }

  if (customer) {
    let sub: Partial<HyperlineSubscription>;
    //at minimum have a monthly subscription
    if (!customer.subscriptions || !customer.subscriptions.length) {
      if(create_plan) {
        sub = await subscribe(customer, create_plan);
        logging.infoFP(LOG_NAME, 'checkSubscription', user.profile, `New subscription: ${JSON.stringify(sub)}`);
      }
    } else {
      const subs = await subscriptions(customer, plans);
      sub = subs && subs.length ? 
        create_plan ? subs.find(s => s.plan?.id === create_plan.id) :
        subs.sort((a,b) => subscribed(a) ? -1 : 1)[0]
        : undefined;

      // get full details
      sub = sub ? await getSubscription(sub.id) : undefined ;
      logging.infoFP(LOG_NAME, 'checkSubscription', user.profile, `Existing subscription: ${JSON.stringify(sub)}`);
    }

    if (sub) {
      if (create_plan && sub.plan?.id !== create_plan.id) {
        await pause(customer);
        sub = await subscribe(customer, create_plan, sub.trial_ends_at);
      }

      // check subscription status and adjust permissions
      let enable = false;
      switch(sub.status) {
        case 'paused':
          // check invoices
          if (create_plan) {
            await reactivate(customer, sub);
            sub = await getSubscription(sub.id);
            enable = subscriptionEnabled(sub, group);
            break;
          }
        case 'pending':
        case 'active':
          enable = subscriptionEnabled(sub, group); 
          break;
        default:
          // no access to learning or widgets
          enable = false;
          break;
      }

      if (enable) {
        // access to learning and widgets
        if (group) {
          logging.infoFP(LOG_NAME, 'checkSubscription', user?.profile, `${group.id} Enabling teams`);
          group.enableTeams();
        } else {
          logging.infoFP(LOG_NAME, 'checkSubscription', user.profile, `Enabling professional`);
          user.enablePro();
        }
      } else {
        if(sub.status === 'active') {
          await pause(customer);
          sub = await getSubscription(sub.id);
        }

        if (group) {
          logging.infoFP(LOG_NAME, 'checkSubscription', user?.profile, `${group.id} Disabling teams`);
          group.disableTeams();
        } else {
          logging.infoFP(LOG_NAME, 'checkSubscription', user.profile, `Disabling professional`);
          user.disablePro();
        }
      }

      if (group) await data.groups.save(group);
      else await data.users.quickSave(user);

      return sub;
    }
  }
  return undefined;
}

export async function customerTutorial(user: User) {
  if (!enabled) return undefined;
  
  const customer = await lookupCustomer(user);
  if (!customer) return undefined;

  const url = `${load_server}${HYPERLINE_EVENT_BATCH}`;
  const body = [
    {
      customer_id: customer.id,
      event_type: "tutorial",
      timestamp: new Date().toISOString(),
      record: {
        id: user.profile,
        active: true,
      }
    }
  ]

  try {
    const r = await axios.post(url, body, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'customerTutorial', user.profile, `Error recording customer tutorial`, e);
  }
}

export async function customerCredit(user: User, product: HyperLineProduct, credit_count: number = 1): Promise<HyperlineCreditTopUp> {
  if (!enabled) return undefined;

  let customer = await lookupCustomer(user);

  if (!customer) {
    const self = await data.people.getUserPerson(user, user.profile);
    if (self) customer = await createCustomer(user, self);
  }

  let url = `${server}${HYPERLINE_CUSTOMER_CREDIT(customer.id, product.id)}`;
  let body: any = { credit_count }

  try {
    const r = await axios.post(url, body, { headers });
    return r?.data;
  } catch(e) {
    if(e.status === 404) {
      url = `${server}${HYPERLINE_CUSTOMER_CREDIT(customer.id)}`;
      body = {
        product_id: product.id,
        current_balance: credit_count,
        name: `${product.name} Credit`,
        low_count_threshold: 0
      }
      try {
        const r = await axios.post(url, body, { headers });
        return r?.data;
      } catch(e) {
        logging.errorFP(LOG_NAME, 'customerCredit', user.profile, `Error creating customer credit for ${product.id}`, e);
      }
    } else logging.errorFP(LOG_NAME, 'customerCredit', user.profile, `Error adding customer credit for ${product.id}`, e);
  }
}

export async function customerCreditBalance(user: User, product: HyperLineProduct): Promise<HyperlineCredit> {
  if (!enabled) return undefined;

  const customer = await lookupCustomer(user);
  if (!customer) return undefined;

  const url = `${server}${HYPERLINE_CUSTOMER_CREDIT_BALANCE(customer.id, product.id)}`;

  try {
    const r = await axios(url, { headers });
    return r?.data;
  } catch(e) {
    if(e.status === 404) return undefined;
    logging.errorFP(LOG_NAME, 'getCustomerCredit', user.profile, `Error getting customer credit for ${product.id}`, e);
  }
}

export async function customerDebit(user: User, product: HyperLineProduct, usage_retained: number = 1): Promise<HyperlineCreditTopUp> {
  if (!enabled) return undefined;

  const customer = await lookupCustomer(user);
  if (!customer) return undefined;

  const url = `${server}${HYPERLINE_CUSTOMER_CREDIT_USAGE(customer.id, product.id)}`;
  const body: any = { usage_retained, event_id: 'tutorial' }

  try {
    const r = await axios.post(url, body, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'customerDebug', user.profile, `Error debiting customer credit for ${product.id} by ${usage_retained}`, e);
  }
 
}

export async function createGroup(group: Group, admin: User): Promise<Partial<HyperlineCustomer>> {
  if (!enabled) return undefined;

  const url = `${server}${HYPERLINE_CUSTOMERS}`
  const country = admin.locale ? admin.locale.split('-')[1] : 'US';
  const currencies = parsers.countryCurrency(country)
  const body: Partial<HyperlineCustomer> = {
    name: group.company_name ? group.company_name : group.name,
    type: 'corporate',
    status: 'active',
    currency: currencies?.length ? currencies[0] :  'USD',
    country,
    external_id: group.id,
    billing_email: admin.email,
    invoice_emails: [admin.email],
    available_payment_methods: [PaymentMethodType.card, PaymentMethodType.direct_debit_ach, PaymentMethodType.transfer],
    payment_method_type: PaymentMethodType.card,
  };

  try {
    const r = await axios.post(url, body, { headers } );
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'createGroup', group.id, `Error creating group subscription`, e);
  }

}

export async function lookupGroup(customer_id: string): Promise<Group> {
  if (!enabled) return undefined;

  const customer = await getCustomer(customer_id);
  const group_id = customer?.external_id;
  if (group_id) {
    const group = await data.groups.byId(group_id);
    return group;
  }
  return undefined;
}

export async function createGroupUser(group: Group, user: GUser | ForaUser) {
  if (!enabled) return undefined;

  const customer = await lookupCustomer(group);
  if (!customer) return undefined;

  const url = `${load_server}${HYPERLINE_EVENT}`;
  const body = {
    customer_id: customer.id,
    event_type: "group_user",
    timestamp: new Date().toISOString(),
    record: {
      id: user.profile,
      active: true,
    }    
  }

  try {
    const r = await axios.post(url, body, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'createGroupUser', user.profile ? user.profile : user.id, `${group.id} Error creating group user`, e);
  }
}

export async function createGroupUsers(group: Group, users: (GUser | ForaUser)[]) {
  if (!enabled) return undefined;
  
  const customer = await lookupCustomer(group);
  if (!customer) return undefined;

  const url = `${load_server}${HYPERLINE_EVENT_BATCH}`;
  const body = users.map(user => {
    return {
      customer_id: customer.id,
      event_type: "group_user",
      timestamp: new Date().toISOString(),
      record: {
        id: user.profile,
        active: true,
      }
    }
  })

  try {
    const r = await axios.post(url, body, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'createGroupUsers', group.id, `Error creating group users`, e);
  }
}

export async function disableGroupUser(group: Group, user: GUser) {
  if (!enabled) return undefined;

  const customer = await lookupCustomer(group);
  if (!customer) return undefined;

  const url = `${load_server}${HYPERLINE_EVENT}`;
  const body = {
    customer_id: customer.id,
    event_type: "group_user",
    timestamp: new Date().toISOString(),
    record: {
      id: user.profile,
      active: false,
    }    
  }

  try {
    const r = await axios.post(url, body, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, `disableGroupUser`, user.id, `${group.id} Error disabling group user`, e);
  }
}

export async function disableGroupUsers(group: Group, users: GUser[]) {
  if (!enabled) return undefined;
  
  if (!enabled) return undefined;
  const customer = await lookupCustomer(group);
  if (!customer) return undefined;

  const url = `${load_server}${HYPERLINE_EVENT_BATCH}`;
  const body = users.map(user => {
    return {
      customer_id: customer.id,
      event_type: "group_user",
      timestamp: new Date().toISOString(),
      record: {
        id: user.profile,
        active: false,
      }    
    }
  })

  try {
    const r = await axios.post(url, body, { headers });
    return r?.data;
  } catch(e) {
    logging.errorFP(LOG_NAME, `disableGroupUser`, group.id, `Error disabling group users`, e);
  }
}

export async function subscriptionToken(customer_id: string): Promise<string> {
  if (!enabled) return undefined;

  const url = `${server}${HYPERLINE_TOKEN}`
  const body = { customer_id };
  try {
    const r = await axios.post(url, body, { headers });
    return r?.data?.token;
  } catch(e) {
    logging.errorF(LOG_NAME, 'subscriptionToken', `${customer_id} Error getting subscription token`, e);
  }
}
