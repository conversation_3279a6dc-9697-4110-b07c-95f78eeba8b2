import axios, { AxiosRequestConfig } from 'axios';
import <PERSON><PERSON> from 'stripe';
import util from 'util';
import { v4 as uuid } from 'uuid';

import config from '../config';
import lang from '../lang';

import DataCache from '../session/data_cache';
import ForaUser from '../session/user';

import { InternalError, TemplateType } from '../types/globals';
import { Group } from '../types/group';
import { Person } from '../types/items';
import { StripePluginState } from '../types/plugins';
import { NotificationType, Uid } from '../types/shared';

import { formatPhone } from '../utils/format';
import { mapURL } from '../utils/funcs';
import logging from '../utils/logging';
import notify, { NotifyType } from '../utils/notify';
import parsers from '../utils/parsers';

const LOG_NAME = 'sources.StripeController';

const INTERNAL_CHARGE = 'internal_charge';

const SKIP_URLS = ['linkedin', 'twitter', '/t.co/', 'google', 'facebook', 'flickr', 'slideshare', 'askfora'];

let STRIPE_CALLBACK, STRIPE_CLIENT_ID, STRIPE_SECRET_KEY, stripe;
config.onLoad('stripe', async () => {
  if (!config.isEnvOffline()) {
    STRIPE_CALLBACK = config.get('STRIPE_CALLBACK');
    STRIPE_CLIENT_ID = config.get('STRIPE_CLIENT_ID');
    // STRIPE_PUBLIC_KEY = config.get('STRIPE_PUBLIC_KEY');
    STRIPE_SECRET_KEY = config.get('STRIPE_SECRET_KEY');

    if (STRIPE_SECRET_KEY) stripe = new Stripe(STRIPE_SECRET_KEY, { apiVersion: '2025-05-28.basil' });
  }
});

const SERVICE_CATEGORIES = ['art_and_graphic_design', 'consulting', 'professional_services', 'web_development', 'other'];

function stripeConnectUrl(email_group: Group) {
  return mapURL('/?connect%20stripe', email_group);
}

function stripeAuthUrl(context: string, name: string, email: string, phone: string = null, url: string = null, category: string = null, desc: string = null, company: string = null, locale: string = null) {
  if (!name) return `https://connect.stripe.com/oauth/authorize?response_type=code&client_id=${STRIPE_CLIENT_ID}&scope=read_write&state=${context}`;

  const names = name.split(' ');
  const first = names[0];
  let last = '';
  if (names.length > 1) last = names[names.length - 1];

  let user_params = `stripe_user[first_name]=${first}&stripe_user[last_name]=${last}&stripe_user[email]=${email}&`;
  if (locale) locale = locale.slice(-2).toUpperCase();
  else locale = 'US';
  if (phone) {
    const fmt_phone = formatPhone(phone, locale);
    if (fmt_phone && fmt_phone.length) {
      user_params += `stripe_user[phone_number]=${fmt_phone.slice(-10)}&`;
      user_params += `stripe_user[country]=${locale}&`;
    }
  } else if (locale) user_params += `stripe_user[country]=${locale.slice(-2).toUpperCase()}&`;
  if (url) user_params += `stripe_user[url]=${url}&`;
  if (category) user_params += `stripe_user[product_category]=${category}&`;
  else user_params += 'stripe_user[product_category]=other';
  if (desc) user_params += `stripe_user[product_description]=${desc}&`;
  if (company) user_params += `stripe_user[business_type]=company&stripe_user[business_name]=${company}`;
  else user_params += 'stripe_user[business_type]=individual';
  // requested_capabilities[]=transfers&
  const capabilities = locale === 'US' ? '&suggested_capabilities[]=card_payments&suggested_capabilities[]=tax_reporting_us_1099_misc' : '';

  return `https://connect.stripe.com/express/oauth/authorize?redirect_uri=${STRIPE_CALLBACK}&client_id=${STRIPE_CLIENT_ID}&state=${context}${capabilities}&${user_params}`;
  //&${capabilities}
}

async function stripeAuth(code, refresh = false) {
  const config = {
    method: 'post',
    url: 'https://connect.stripe.com/oauth/token',
    data: {
      client_secret: STRIPE_SECRET_KEY,
      code,
      grant_type: refresh ? 'refresh_token' : 'authorization_code',
    },
  } as AxiosRequestConfig;

  try {
    const res = await axios(config);
    return res ? res.data : res;
  } catch (err) {
    logging.errorF(LOG_NAME, 'stripeAuth', `Error getting Stripe token: ${util.format(err.response.data)}`, err);
  }
}

async function countries() {
  if (config.isEnvOffline()) return ['US'];
  let starting_after = undefined;
  let country_specs = [];
  let has_more;
  while (!starting_after || has_more) {
    const specs = await stripe.countrySpecs.list({limit: 100, starting_after});
    if (specs && specs.data) {
      country_specs = country_specs.concat(specs.data);
      has_more = specs.has_more;
      if (has_more) starting_after = specs.data[specs.data.length - 1].id;
      else break;
    } else break;
  }
  return country_specs.map(s => s.id);
}

async function accountInfo(id) {
  if (config.isEnvOffline()) return lang.stripe.ACCOUNT_STUB(id);
  return stripe.accounts.retrieve(id);
}

async function handleError(err: any, contractor_stripe_id: string, contractor_name: string, contractor_user: ForaUser) {
  if (err && err.message) {
    if (err.message.includes('debit')) {
      return lang.stripe.ACCOUNT_DEBIT_ERROR;
    } else if (err.message.includes('account')) {
      // Your account cannot currently make charges. To find out why charge creation is currently disabled, look at the `requirements.disabled_reason` property on this account (/v1/accounts/acct_1MA2X92fXurnXGeE).

      const info = await accountInfo(contractor_stripe_id);
      if ((info.requirements.errors && info.requirements.errors.length && true) || 
        (info.requirements.disabled_reason && info.requirements.disabled_reason.length && true)) {
        await contractor_user.loadGroupsFromDatastore(true);
        const group = contractor_user.emailGroup();

        await notify(contractor_user, {
          email: {
            rcpts: [{Email: contractor_user.email, Name: contractor_name}],
          },
          type: NotificationType.Payment,
          template: TemplateType.Error,
          variables: {
            firstname: contractor_name.split(' ')[0],
            url: mapURL('/settings/accounts', group),
          },
        }, NotifyType.EmailOnly, null, false, false);
        return lang.stripe.CONTRACTOR_ERROR(contractor_name);
      }
    }
  } 
}

async function charge(skills: string, pay_token: string, contractor_stripe_id: string, amount: number, fee: number, receipt_email: string, project_id: Uid, return_url: string) {
  if (config.isEnvOffline()) {
    logging.infoF(LOG_NAME, 'charge', `Offline charging obo ${contractor_stripe_id} ${amount} + ${amount * fee} fee for ${skills} to ${receipt_email}`);
    return lang.stripe.PAYINTENT_STUB(skills, receipt_email, contractor_stripe_id, (amount + amount * fee) * 100);
  }

  logging.infoF(LOG_NAME, 'charge', `Charging ${pay_token} o/b/o ${contractor_stripe_id} ${amount} + ${amount * fee} fee for ${skills} to ${receipt_email}`);

  const info = await accountInfo(contractor_stripe_id);
  if (!info) throw new InternalError(404, 'Stripe account not found ');

  if (pay_token === contractor_stripe_id || pay_token === config.get('STRIPE_ACCOUNT')) {
    // can't charge yourself or the platform
    const internal = {
      // id: config.get('STRIPE_ACCOUNT'),
      object: 'askfora', 
      amount: amount * 100 + amount * fee * 100,
      currency: 'usd',
      description: `${skills} Job Payment and Fee`,
      statement_descriptor: 'AskFora Job Payment',
      on_behalf_of: contractor_stripe_id,
      receipt_email,
      charges: { data: [ { id: INTERNAL_CHARGE } ] },
    };

    logging.infoF(LOG_NAME, 'charge', `Escrow ${JSON.stringify(internal)}`);

    return internal;
  }  else if(pay_token.startsWith('acct_')) {
    const charge = await stripe.charges.create({
      amount: amount * 100 + amount * fee * 100,
      // application_fee_amount: amount * fee * 100,
      currency: 'usd',
      description: `${skills} Job Payment and Fee`,
      statement_descriptor: 'AskFora Job Payment',
      on_behalf_of: info.default_currency === 'usd' ? contractor_stripe_id : undefined,
      receipt_email,
      source: pay_token,
      metadata: { project_id },
    }); //, {stripeAccount: contractor_stripe_id});

    logging.infoF(LOG_NAME, 'charge', `Escrow ${JSON.stringify(charge)}`);

    if (!charge.on_behalf_of) charge.on_behalf_of = contractor_stripe_id;

    return charge;
  } else {
    let intent;
    if(pay_token.startsWith('pi_')) intent = await stripe.paymentIntents.retrieve(pay_token); //, {stripeAccount: contractor_stripe_id});
    else {
      const pi = pay_token === 'create' ? 
        {
          amount: amount * 100 + amount * fee * 100,
          // application_fee_amount: amount * fee * 100,
          currency: 'usd',
          // automatic_payment_methods: { enabled: true },
          payment_method_types: ['card'],
          description: `${skills} Job Payment and Fee`,
          statement_descriptor: 'AskFora Job Payment',
          on_behalf_of: info.default_currency === 'usd' ? contractor_stripe_id : undefined,
          receipt_email,
          setup_future_usage: 'on_session',
          metadata: { project_id },
        }
        : {
          amount: amount * 100 + amount * fee * 100,
          // application_fee_amount: amount * fee * 100,
          currency: 'usd',
          payment_method: pay_token,
          confirmation_method: 'manual',
          confirm: true,
          return_url,
          description: `${skills} Job Escrow and Fee`,
          statement_descriptor: 'AskFora Job Payment',
          on_behalf_of: info.default_currency === 'usd' ? contractor_stripe_id : undefined,
          receipt_email,
          metadata: { project_id },
        }

      intent = await stripe.paymentIntents.create(pi); //, {stripeAccount: contractor_stripe_id});
    }

    if (intent.status === 'requires_payment_method' ||
      (intent.status === 'requires_action' && intent.next_action.type === 'use_stripe_sdk')) {
      return {
        requires_action: true,
        client_secret: intent.client_secret,
      }
    } else if(intent.status === 'succeeded') {
      if (!intent.on_behalf_of) intent.on_behalf_of = contractor_stripe_id;
    }

    logging.infoF(LOG_NAME, 'charge', `Escrow ${JSON.stringify(intent)}`);

    return intent;
  }
}

async function pay(skills, charge_id, contractor_stripe_id, amount) {
  if (config.isEnvOffline()) {
    logging.infoF(LOG_NAME, 'pay', `Offline releasing payment to ${contractor_stripe_id} ${amount} for ${skills}`);
    return lang.stripe.PAY_STUB(contractor_stripe_id, amount * 100, charge_id);
  }

  logging.infoF(LOG_NAME, 'pay', `Releasing payment to ${contractor_stripe_id} ${amount} for ${skills}`);

  const transfer = {
    amount: amount * 100,
    currency: 'usd',
    description: `${skills} Project Payment`,
    destination: contractor_stripe_id,
  };

  if (charge_id) transfer['source_transaction'] = charge_id;

  const payment_transfer = await stripe.transfers.create(transfer); //, {stripeAccount: contractor_stripe_id});
  logging.infoF(LOG_NAME, 'pay', `payment_transfer = ${JSON.stringify(payment_transfer)}`);

  return payment_transfer;
}

async function refund(charge, contractor_stripe_id, amount) {
  if (config.isEnvOffline()) {
    logging.infoF(LOG_NAME, 'refund', `Offline refunding payment of ${charge} for ${amount}`);
    return lang.stripe.REFUND_STUB(charge, amount * 100);
  }

  if ([lang.project.SKIP_CHARGE.id, INTERNAL_CHARGE].includes(charge)) {
    logging.infoF(LOG_NAME, 'refund', `Mocking refund of internal charge ${charge} for ${amount}`);
    return lang.stripe.REFUND_STUB(charge, amount * 100);
  }

  if (charge) {
    logging.infoF(LOG_NAME, 'refund', `Refunding payment of ${charge} for ${amount}`);
    let refund_charge = null;
    try { 
      refund_charge = await stripe.refunds.create({ charge, amount: amount * 100 }); //, {stripeAccount: contractor_stripe_id});
    } catch(e) {
      logging.warnF(LOG_NAME, 'refund', `Error issuing refund, lookig for existing refund`, e);
      const result = await stripe.refunds.list({limit: 1, charge}); // , {stripeAccount: contractor_stripe_id});
      if (result && result.data && result.data.length) refund_charge = result.data[0];
    }
    logging.infoF(LOG_NAME, 'refund', `refund_charge = ${JSON.stringify(refund_charge)}`);

    return refund_charge;
  } else logging.infoF(LOG_NAME, 'refund', `Not refunding payment of ${amount} with no ch;arge`);
}

function setupContext(user: ForaUser, me: Person, locale: string, cache: DataCache): StripePluginState {
  let name = me ? me.displayName : user.name;

  let email = user.email;
  let phone = null;
  let url = null;
  let desc = null;
  let company = null;

  if (me) {
    for (const comm of me.comms) {
      const emails = parsers.findEmail(comm);
      if (!email && emails && emails.length) email = emails[0];

      const phones = parsers.findPhone(comm, locale);
      if (!phone && phones && phones.length) phone = phones[0]; // .slice(-10);
    }

    for (const index in me.urls) {
      const iurl = me.urls[index];
      let skip = false;
      for (const sindex in SKIP_URLS) {
        if (iurl.indexOf(SKIP_URLS[sindex]) !== -1) {
          skip = true;
          break;
        }
      }
      if (skip) continue;
      url = iurl;
      break;
    }
  }

  // figure out company name from contracts
  if (cache) {
    for (const index in cache.contracts) {
      const contract = cache.contracts[index];
      if (user.profile === contract.contractor_id) {
        if (contract.contractor_name.indexOf(name) === -1) company = name;
        else if (name.indexOf(' ') === -1) name = contract.contractor_name;
        email = contract.contractor_email;
        break;
      }
    }

    // get desc from project skills
    for (const index in cache.projects) {
      const project = cache.projects[index];
      if (project.candidates) {
        for (const candidate of project.candidates) {
          if (candidate.askfora_id === user.profile) {
            desc = project.skills;
            break;
          }
        }
      }
    }
  }
    
  return {
    context: uuid(),
    name,
    email,
    phone,
    url,
    category: null,
    desc,
    company,
    locale,
    detail_step: 0,
    country_confirmed: false,
  } as StripePluginState;
}

async function login(stripe_id): Promise<string> {
  const login_link = await stripe.accounts.createLoginLink(stripe_id);
  if (login_link) return login_link.url;
  return null;
}

export default {
  charge,
  handleError,
  pay,
  refund,
  setupContext,
  stripeConnectUrl,
  stripeAuth,
  stripeAuthUrl,
  login,
  countries,
  accountInfo,
  SERVICE_CATEGORIES,
};
