/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

export interface ISourceMetadata {
  disabledError: boolean;
  enabled: boolean;
  firstRunDone: boolean;
  historyDone: boolean;
  historyPageToken: string;
  newDone: boolean;
  nextPageToken: string;
  nextSyncToken: string;

  hasHistoryPageToken(): boolean;

  hasNextPageToken(): boolean;

  hasNextSyncToken(): boolean;

  restart(): void;
}
