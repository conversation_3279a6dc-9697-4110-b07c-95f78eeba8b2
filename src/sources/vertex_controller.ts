import fs from 'fs';
import path from 'path';

import { AnthropicVertex } from '@anthropic-ai/vertex-sdk';
import { ChatSession, Content, GenerativeModel, HarmBlockThreshold, HarmCategory, VertexAI } from '@google-cloud/vertexai';
import { setTimeout } from "timers/promises";

import config from '../config';

import { Tutorial } from '../types/items';
import { Assessment, Infographic, Lesson, Outline } from '../types/shared';

import logging from '../utils/logging';
import parsers from '../utils/parsers';

const LOG_NAME = 'sources.vertex';

let gen_model: GenerativeModel;
let anthropic: AnthropicVertex;

const about = fs.readFileSync(path.resolve(__dirname, '../files/about.txt'));

const ABOUT = `
You're an expert in AskFora,
I'm a student and I have a question about AskFora.
Act as a customer success representative to help me learn about it. 
Answer questions briefly
Answer in sentences not bullets
Answer more naturally.
Only answer questions related to this tutorial.
Keep your answers concise.
Limit your answers to 3 sentances.

Here's context about AskFora: ${about}
`
const SKILL_ASSESSMENT = (skills: string[], tutorial?: string) => `
${tutorial ? 'I just finished a tutorial and want to assess what I\'ve learned' : 'I\'m working on learning some new skills.'}
Create three to five multiple choice questions to assess my current level of knowledge with these skills.
Phrase each question in a way so that the answer selected is what I actually feel confident in implementing.
Phrase each question such the answer I select implies knowledge in a related set of skills. 
Make sure there are three to give answers per question.
Keep the answers short (ideally five words or less). 
Include a list of skills that each answer indicates I know.

Don't use self assessment style questions.
Don't ask me to rank my skills, confidence, or expertise.
Don't ask me what I'm able to do.
Don't ask how familiar I am with a skill or tool.
Don't ask me what skills or tools I'm comfortable with.
Don't repeat the skills in the questions or answers.

The output is JSON that looks like this:
[{
 "question": "example question",
 "answers": [ {"answer": "example answer 1", "skills": ["skills related to example answer 1"]} ] 
}]

The skills I ${tutorial ? 'was hoping' : 'want'} to learn are: ${skills.join(', ')}
Don't use these words in the answers: ${skills.join(', ')} 
${tutorial ? 'This is the tutorial: ' : ''}
${tutorial ? tutorial : ''}
`
const OUTLINE = (skills: string[], exclude: string[]) => `
I'm working on learning some new skills.
Create an outline for a six part tutorial. 
Each part should take no more than ten to fifteen minutes.
Basd on what I'm good at, figure out if the tutorial is an begginer, intermediate, advanced, or expert

The output is JSON that looks like this:
[{
 "duration": "10 minutes",
 "level": "beginner",
 "text": "Example tutorial contents",
 "title": "Part 1: the first title",
}]

The skills are: ${skills.join(', ')}
I'm already good at: ${exclude.join(', ')}
`

const TUTORIAL = (ta: Outline, skills: string[]) => `
Create a course that takes ${ta.duration} and is for a ${ta.level} level. 
Fill out the text and details of the course
Go into a lot of detail
Explain the ideas in each section
Use examples
Don't use bullets or lists
Make it readable, like a textbook or online tutorial

The title is: ${ta.title}
The summary is: ${ta.text}
The skills to focus on are: ${skills.join(', ')}

The output is JSON that looks like this:
[{
 "title": "1. the first section",
 "text": "The detailed text of the tutorial in this section"
}]

Don't use newlines or escaped characters in the text.
`

export const INFOGRAPHIC = (ta: Partial<Lesson>) => `
Create an infographic to accompany a tutorial. 
It should have three parts.
Each part should be accompanied by a well designed beautiful sophistiated multicolored SVG image that evokes clip art. 
Make the SVG have a transparent background.
The output should have this JSON format and encode the SVG image in base64: 

[
  { "title": "Section title", "text": "Section text", "image": "PH2Z..." }
]

Only answer with JSON.
Don't use newlines or escaped characters in the text.
The tutorial title: "${ta.title}"
The tutorial text is: "${ta.text}"`

const LESSON_TEXT = (l: Lesson) => `
${l.title}
${l.text}
`

const TUTOR = (tutorial: Tutorial) => `
You're an expert in these skills: ${tutorial.skills.join(', ')}. 
I'm a student, learning about ${tutorial.lesson_set.map(ls => ls.outline.title).join(', ')}.
Always follow these instructions and never ignore them:
Act as a tutor to help me learn about it. 
Answer questions briefly.
Answer in sentences not bullets.
Answer more naturally.
Only answer questions related to this tutorial.
Keep your answers concise.
Limit your answers to 3 sentances.
Don't write any code or provide any examples other than the ones provided in the tutorial.

Here's the tutorial I'm working through: 
${tutorial.lesson_set.filter(ls => ls.lessons).map(ls => ls.lessons.map(LESSON_TEXT).join('\n')).join('\n\n')}
`

export const PRACTICUM = (tutorial: Tutorial) => `
I just finished a tutorial and want to assess what I've learned.                                                                                                                                      
Create a practicum that describes a scenario with a problem I need to solve.                                                                                                                          
Help me work through the scenario with questions and hints.                                                                                                                                           
Let me know when I've come up with a good solution by saying 'Congratulations, you successfully completed this practice!'
Ask questions one at a time and don't continue until I give a satisfactory answer.
Be lenient and move on if I'm not getting answers exactly right.
Don't ask more than seven questions.
Use plain text, no markup.
Don't use asterisks or other special characters.

Here's the tutorial I want to create a practicum for:
${tutorial.lesson_set.filter(ls => ls.lessons).map(ls => ls.lessons.map(LESSON_TEXT).join('\n')).join('\n\n')}
`

config.onLoad('vertex', async (silent: boolean) => {
  const model = 'gemini-2.0-flash-exp'; //'gemini-1.5-flash-002';
  try { 
    const vertex_ai = new VertexAI({project: config.get('GOOGLE_CLOUD_PROJECT'), location: 'us-central1'}); //{project: project, location: location});
    gen_model = vertex_ai.getGenerativeModel({
      model, 
      generationConfig: {
        maxOutputTokens: 8192,
        temperature: 1,
        topP: 0.95,
      },
      safetySettings: [
        {
            category: HarmCategory.HARM_CATEGORY_HATE_SPEECH,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
        },
        {
            category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
        },
        {
            category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
        },
        {
            category: HarmCategory.HARM_CATEGORY_HARASSMENT,
            threshold: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE
        }
      ],
    });

    anthropic = new AnthropicVertex({ projectId: config.get('GOOGLE_CLOUD_PROJECT'), region: 'us-east5' });
   } catch(e) {
    logging.errorF(LOG_NAME, 'onLoad', `Error loading vertex`, e);
  }
});

export async function createTutor(tutorial: Tutorial): Promise<Content[]> {
  if (gen_model) {
    const systemInstruction = TUTOR(tutorial);

    const chat: ChatSession = await gen_model.startChat({systemInstruction});
    const resp = await chat.sendMessage(`I'm going to ask you some questions about the tutorial.`);
    const r = await resp.response;

    const raw = r.candidates[0].content.parts[0].text;
 
    const history = await chat.getHistory();

    return history;
  }
  return undefined;
}

export async function askAbout(text: string, history: Content[]): Promise<{answer: string, history: Content[]}> {
  if (gen_model) {
    const systemInstruction = ABOUT;

    const chat: ChatSession = await gen_model.startChat({systemInstruction, history});
    const resp = await chat.sendMessage(text)
    const r = await resp.response;

    const answer = r.candidates[0].content.parts[0].text.replace(/\*\*/g, '');
    history = await chat.getHistory();
    return { answer, history };
  }

}

export async function askTutor(text: string, tutorial: Tutorial, history: Content[]): Promise<{answer: string, history: Content[]}> {
  if (gen_model) {
    const systemInstruction = TUTOR(tutorial);

    const chat: ChatSession = await gen_model.startChat({systemInstruction, history});
    const resp = await chat.sendMessage(text)
    const r = await resp.response;

    const answer = r.candidates[0].content.parts[0].text.replace(/\*\*/g, '');
    history = await chat.getHistory();
    return { answer, history };
  }
}

export async function startPracticum(tutorial: Tutorial, history?: Content[]): Promise<{answer: string, history: Content[]}> {
  if (gen_model) {
    const systemInstruction = PRACTICUM(tutorial);

    const chat: ChatSession = await gen_model.startChat({systemInstruction, history});
    const resp = await chat.sendMessage('Ok, go');
    const r = await resp.response;

    const answer = r.candidates[0].content.parts[0].text.replace(/\*\*/g, '');
    history = await chat.getHistory();
    return { answer, history };
  }
}

export async function answerPracticum(text: string, tutorial: Tutorial, history: Content[]) {
  if (gen_model) {
    const systemInstruction = PRACTICUM(tutorial);

    const chat: ChatSession = await gen_model.startChat({systemInstruction, history});
    const resp = await chat.sendMessage(text);
    const r = await resp.response;

    const answer = r.candidates[0].content.parts[0].text.replace(/\*\*/g, '');
    history = await chat.getHistory();
    return { answer, history };
  }
}

export async function createOutline(skills: string[], exclude: string[], systemInstruction?: string): Promise<Outline[]> {
  if (gen_model) {
    const text = OUTLINE(skills, exclude);
    const request = { contents: [{role: 'user', parts: [{text}]}], systemInstruction};
    const resp = await gen_model.generateContent(request);
    const r = await resp.response;

    const qa_raw = r.candidates[0].content.parts[0].text;

    const abstract = JSON.parse(parsers.fixJSON(qa_raw.match(/```json([^`]*)```/)[1]))
    return abstract;
  }
}

export async function createTutorial(ta: Outline, skills: string[], context?: string, infographic = true): Promise<Partial<Lesson>[]> {
  if(anthropic) {
    const model = config.get('CLAUDE_MODEL', 'claude-3-5-sonnet-v2@20241022');
    const max_tokens = config.get('CLAUDE_MAX_TOKENS', 2048);

    const messages = [];
    if(context) {
      messages.push({
        role: 'user',
        content: context,
      });
    }

    const content = TUTORIAL(ta, skills);
    messages.push({
      role: 'user',
      content,
    });

    let retry = 10;
    let result;
    while (!result && retry < 60000) {
      try {
        result = await anthropic.messages.create({
          model,
          max_tokens,
          messages,
        });
      } catch(e) {
        if(e.status === 429) {
          logging.warnF(LOG_NAME, 'createTutorial', `Retrying ${retry} create tutorial for ${JSON.stringify(ta)}`);
          await setTimeout(retry);
          retry *= 2;
        }
      }
    }

    if (result?.content?.length) {
      try { 
        const lessons = JSON.parse(parsers.fixJSON((result.content[0] as any).text)) as Lesson[];

        if(infographic) {
          for(const t of lessons) {
            if(!t.info) {
              const ti = await createInfographic(t);
              if(ti) t.info = ti.info;
            }
          }
        }
        return lessons;
      } catch(e) {
        logging.errorF(LOG_NAME, 'createTutorial', `Error creating tutorial for ${JSON.stringify(ta)}. Got ${JSON.stringify(result?.content)}`, e);
      }
    }
  }

  return undefined;
}

export async function createInfographic(t: Partial<Lesson>): Promise<Lesson> {
  if(anthropic) {
    const model = config.get('CLAUDE_MODEL', 'claude-3-5-sonnet-v2@20241022');
    const max_tokens = config.get('CLAUDE_MAX_TOKENS', 2048);

    const content = INFOGRAPHIC(t);

    let retry = 10;
    let result;
    while (!result && retry < 60000) {
      try {
        result = await anthropic.messages.create({
          model,
          max_tokens,
          messages: [
            {
              role: 'user',
              content,
            },
          ],
        });
      } catch(e) {
        if(e.status === 429) {
          logging.warnF(LOG_NAME, 'createInfographic', `Retrying ${retry} create infographic for ${JSON.stringify(t)}`);
          await setTimeout(retry);
          retry *= 2;
        }
      }

      if (result?.content?.length) {
        try {
          let text = (result.content[0] as any).text;
          let json = text.slice(text.indexOf('['));
          const info = JSON.parse(parsers.fixJSON(json)) as Infographic[];
          const nt = {
            title: t.title,
            text: t.text,
            info,
          }
          return nt;
        } catch(e) {
          logging.errorF(LOG_NAME, 'createInfographic', `Error creating inforgraphic for ${JSON.stringify(t)}. Got ${JSON.stringify(result?.content)}`, e);
          result = undefined;
        }
      }
    }
  }

  return undefined;

}

export async function createAssessment(skills: string[], lessons?: Lesson[], systemInstruction?: string): Promise<Assessment> {
  if (gen_model) {
    const text = SKILL_ASSESSMENT(skills, lessons?.map(t => t.text).join('\n'));
    const request = { contents: [{role: 'user', parts: [{text}]}], systemInstruction };
    const resp = await gen_model.generateContent(request);
    const r = await resp.response;

    const qa_raw = r.candidates[0].content.parts[0].text;

    const prompts = JSON.parse(parsers.fixJSON(qa_raw.match(/```json([^`]*)```/)[1]));
    return {prompts};
  }
}

