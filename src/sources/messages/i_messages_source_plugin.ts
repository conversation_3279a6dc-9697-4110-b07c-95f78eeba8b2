/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import ForaUser from '../../session/user';
import { IEntity, Message, Person } from '../../types/items';
import { ISourceMetadata } from '../i_source_metadata';
import { ISourcePlugin } from '../i_source_plugin';

export interface IMessagesSourcePlugin extends ISourcePlugin<Message, ISourceMetadata> {
  archive(message: Message): Promise<[Message, IEntity[]]>;

  draft(user: ForaUser, recipients: Partial<Person>[], from: Person, email: string, subject: string, text: string, cc_to: Person[]): Promise<[Message, IEntity[]]>;

  markRead(message: Message): Promise<[Message, IEntity[]]>;

  send(message: Message): Promise<[Message, IEntity[]]>;
}
