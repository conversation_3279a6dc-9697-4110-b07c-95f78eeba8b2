/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { gmail_v1, google } from 'googleapis';
import util from 'util';

import { AuthProvider } from '../../../auth/auth_provider';
import config from '../../../config';
import data from '../../../data';

import { GoogleErrorHandler } from '../../../errors/google_error_handler';

import ForaUser from '../../../session/user';

import { IEntity, Message, Person } from '../../../types/items';
import { AuthLevel, AuthProviders, EntityType, Uid } from '../../../types/shared';

import { createRawMessage } from '../../../utils/format';
import { hash, messageRecipients, slimEntity } from '../../../utils/funcs';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';
import peopleUtils from '../../../utils/people';

import { AbstractGoogleSourcePlugin } from '../../a_google_source_plugin';
import { SourceKeys } from '../../index';
import { IMessagesSourcePlugin } from '../i_messages_source_plugin';
import { GoogleMessagesSourceMetadata } from './google_messages_source_metadata';

const gmail = google.gmail('v1');

const DEBUG = (require('debug') as any)('fora:sources:messages:google');

const MESSAGE_FIELDS = 'id,labelIds,internalDate,payload/headers,historyId';
let BATCH_OPTIONS;

config.onLoad('google_messages_source', async () => {
  BATCH_OPTIONS = {
    // 20,000 in 100 seconds
    // https://developers.google.com/gmail/api/v1/reference/quota
    // 250 quota units per user per second; moving average (allows short bursts)
    userQuota: config.get('GMSP_USER_QUOTA', 20000),
    userQuotaTime: config.get('GMSP_USER_QUOTA_TIME', 100000),
    parallelRequests: config.get('GMSP_PARALLEL_REQUESTS', 10),
  };
});

const LOG_NAME = 'data.sources.GoogleMessageSourcePlugin';

/**
 * Google Messages Source Plugin
 *
 *  - http://googleapis.github.io/google-api-nodejs-client/modules/_apis_gmail_v1_.html
 *  - https://github.com/google/google-api-nodejs-client/blob/master/src/apis/gmail/v1.ts
 */
export class GoogleMessageSourcePlugin extends AbstractGoogleSourcePlugin<Message, GoogleMessagesSourceMetadata> implements IMessagesSourcePlugin {
  public static providers = [AuthProviders.Google];
  private readonly refresh_ignore_ids: string[] = [];

  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(LOG_NAME, SourceKeys.GoogleMessages, EntityType.Message, user, account, user_people, user_emails, GoogleMessagesSourceMetadata, AuthProviders.Google, AuthProvider.google.getScopesForMessages(), true, true, 'received', config.get('GMSP_EXPIRATION_DAYS_BACK', 30));
  }

  /** @inheritDoc */
  async archive(fMessage: Message): Promise<[Message, IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) return [undefined, []];

    if (fMessage.draft) return this.archiveDraft(fMessage);
    else return this.archiveMessage(fMessage);
  }

  /** @inheritDoc */
  async draft(user: ForaUser, recipients: Partial<Person>[], from: Person, email: string, subject: string, text: string, cc_to: Person[] = []): Promise<[Message, IEntity[]]> {
    const raw_message = createRawMessage(recipients, from, user.email, subject, text, cc_to);

    if (!this.user.isAuthenticated(AuthLevel.Email)) {
      const message = new Message({
        archived: false,
        draft: true,
        recipient:recipients,
        subject,
        raw:raw_message.raw,
      });
      message.id = hash(JSON.stringify(message));
      return [message, []];
    }

    const args = {
      auth: this.getClient() as any,
      userId: 'me',
      requestBody: {
        message: raw_message,
      } as gmail_v1.Schema$Draft,
    } as gmail_v1.Params$Resource$Users$Drafts$Create;

    const response = await this.repeatableCallWithBackOff(gmail.users.drafts.create, 'gmail.users.drafts.create', gmail, args);
    const g_message = response.data.message;
    if (!g_message.payload) g_message.payload = { headers: [] };
    else if (!g_message.payload.headers) g_message.payload.headers = [];

    // TODO(current) - this should be changed to using the raw recipients array from above. Need to make sure its formatted correctly
    g_message.payload.headers.push({ name: 'to', value: messageRecipients(raw_message.raw).join(',') });
    // TODO(current) - need to add in the cc header
    g_message.payload.headers.push({ name: 'from', value: user.email });

    // drafts keep their id on the data
    g_message.id = response.data.id;
    return this.foraMessageFromGoogleMessage(g_message);
  }

  /** @inheritDoc */
  async markRead(message: Message): Promise<[Message, IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) return [undefined, []];

    message.read = true;

    const args = {
      auth: this.getClient() as any,
      userId: 'me',
      id: message.id,
      requestBody: { removeLabelIds: ['UNREAD'] } as gmail_v1.Schema$ModifyMessageRequest,
    } as gmail_v1.Params$Resource$Users$Messages$Modify;

    const response = await this.repeatableCallWithBackOff(gmail.users.messages.modify, 'gmail.users.messages.modify', gmail, args);
    message.read = !response.data.labelIds.includes('UNREAD');

    return [message, []];
  }

  /** @inheritDoc */
  async reload(message: Message): Promise<Message> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) return message;

    const [updates, deletes] = await this.processMessages([message.id], true);

    if (updates && updates.length && (!deletes || !deletes.length)) return new Message(updates[0] as any);
    return null;
  }

  /** @inheritDoc */
  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    if (is_history) {
      // Sync processing backwards
      if (this.metaData().historyDone) return [[], []];

      const newer_than = config.get('GMSP_HISTORY_NEWER_THAN', '30d');
      return this.googleMessagesReadUsingMessagesList(is_history, this.metaData().historyPageToken, `newer_than:${newer_than}`);
    } else {
      const [draft_updates, draft_deletes] = await this.googleMessagesSyncDrafts();

      let sync_updates, sync_deletes;

      // Sync processing forward (if statements nested for easier readability of all the possible cases)
      const newer_than = config.get('GMSP_REFRESH_NEWER_THAN', '1h');
      if (this.metaData().nextPageToken) {
        if (this.metaData().pageTokenFromSyncToken) {
          // We need to call googleMessagesReadUsingHistoryList
          [sync_updates, sync_deletes] = await this.googleMessagesReadUsingHistoryList(is_history);
        } else {
          // We need to call googleMessagesReadUsingMessagesList
          [sync_updates, sync_deletes] = await this.googleMessagesReadUsingMessagesList(is_history, this.metaData().nextPageToken, `newer_than:${newer_than}`);
        }
      } else if (this.metaData().nextSyncToken) {
        // We need to call googleMessagesReadUsingHistoryList
        [sync_updates, sync_deletes] = await this.googleMessagesReadUsingHistoryList(is_history);
      } else {
        // We need to call googleMessagesReadUsingMessagesList
        [sync_updates, sync_deletes] = await this.googleMessagesReadUsingMessagesList(is_history, null, `newer_than:${newer_than}`);
      }

      return [[...draft_updates, ...sync_updates], [...draft_deletes, ...sync_deletes]];
    }
  }

  /** @inheritDoc */
  async send(f_message: Message): Promise<[Message, IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) return [undefined, []];

    const args = {
      auth: this.getClient() as any,
      userId: 'me',
      requestBody: { id: f_message.id } as gmail_v1.Schema$Draft,
    } as gmail_v1.Params$Resource$Users$Drafts$Send;

    try {
      const response = await this.repeatableCallWithBackOff(gmail.users.drafts.send, 'gmail.users.drafts.send', gmail, args);
      if (response.data.payload) return this.foraMessageFromGoogleMessage(response.data);
      else {
        logging.warnFP(LOG_NAME, 'send', this.user.profile, `missing payload in response ${util.format(response.data)}`);
        return [undefined, []];
      }
    } catch (err) {
      logging.errorFP(LOG_NAME, 'send', this.user.profile, `Error sending message ${f_message.id}`, err);
      return [undefined, []];
    }
  }

  private async archiveDraft(f_message: Message): Promise<[Message, IEntity[]]> {
    const args = {
      auth: this.getClient() as any,
      id: f_message.id,
      userId: 'me',
    } as gmail_v1.Params$Resource$Users$Messages$Trash;

    try {
      const response = await this.repeatableCallWithBackOff(gmail.users.messages.trash, 'gmail.users.messages.trash', gmail, args);
      f_message.archived = !response.data.labelIds.includes('DRAFT');
    } catch (err) {
      // draft is already gone
      if (err.code === 400) logging.warnFP(LOG_NAME, 'archiveDraft', this.user.profile, '', err);
      else {
        logging.errorFP(LOG_NAME, 'archiveDroft', this.user.profile, '', err);
        throw err;
      }
    }

    return [f_message, []];
  }

  private async archiveMessage(f_message: Message): Promise<[Message, IEntity[]]> {
    const args = {
      auth: this.getClient() as any,
      id: f_message.id,
      userId: 'me',
      requestBody: { removeLabelIds: ['INBOX'] } as gmail_v1.Schema$ModifyMessageRequest,
    } as gmail_v1.Params$Resource$Users$Messages$Modify;

    try {
      const response = await this.repeatableCallWithBackOff(gmail.users.messages.modify, 'gmail.users.messages.modify', gmail, args);
      f_message.archived = !response.data.labelIds.includes('INBOX');
    } catch (err) {
      // draft is already gone
      if (err.code === 400) logging.warnFP(LOG_NAME, 'archiveMessage', this.user.profile, '', err);
      else {
        logging.errorFP(LOG_NAME, 'archiveMessage', this.user.profile, '', err);
        throw err;
      }
    }
    return [f_message, []];
  }

  /**
   * Convert a Google message to a AskFora message
   *
   * @param g_message - the Google message to convert
   * @returns Converted message
   * @private
   */
  private async foraMessageFromGoogleMessage(g_message: gmail_v1.Schema$Message): Promise<[Message, IEntity[]]> {
    const deletes: IEntity[] = [];
    const people_updates: Person[] = [];

    if (logging.isDebug()) DEBUG(`processing ${g_message.id} ${g_message.labelIds}`);

    let read = true;
    let draft = false;
    let trash = false;
    let spam = false;
    if (g_message.labelIds) {
      if (g_message.labelIds.includes('UNREAD')) read = false;
      if (g_message.labelIds.includes('DRAFT')) draft = true;
      if (g_message.labelIds.includes('TRASH')) trash = true;
      if (g_message.labelIds.includes('SPAM')) spam = true;
    }

    const received = new Date(0);
    received.setUTCMilliseconds(Number(g_message.internalDate));

    const save_message = new Message({
      id: g_message.id,
      threadId: g_message.threadId,
      received,
      read,
      draft,
      link: `https://mail.google.com/mail/u/${this.user.email}/#${draft ? 'drafts' : 'all'}/${g_message.id}`,
      recipient: [],
    });

    for (const header of g_message.payload.headers) {
      if (!header.name) continue;
      const header_lc = header.value ? header.value.toLowerCase() : null;
      switch (header.name.toLowerCase()) {
        case 'from':
          if (!spam) spam = parsers.spamAddress(header_lc);
          if (!spam) save_message.sender = peopleUtils.parseMessagePerson(header.value, received);
          if (logging.isDebug()) DEBUG(`\tfrom ${header.value}${spam ? ' spam' : ''}`);
          break;
        case 'to':
        case 'cc':
        case 'bcc':
          if (!spam) spam = parsers.spamAddress(header_lc);
          if (!spam) save_message.recipient = save_message.recipient.concat(header.value.split(',').filter(v => v.length).map(v => peopleUtils.parseMessagePerson(v, received)).filter(p => p));
          if (logging.isDebug()) DEBUG(`\tto/cc/bcc ${header.value}${spam ? ' spam' : ''}`);
          break;
        case 'subject':
          save_message.subject = header.value;
          if (logging.isDebug()) DEBUG(`\tabout ${header.value}`);
          break;
        case 'return-path':
        case 'reply-to':
          if (!spam) spam = parsers.spamAddress(header_lc);
          if (logging.isDebug()) DEBUG(`\treturn/reply ${header.value}${spam ? ' spam' : ''}`);
          break;
        case 'x-vr-status':
          if (header_lc !== 'ok' && header_lc !== 'whitelisted') spam = true;
          if (logging.isDebug()) DEBUG(`\tx-vr ${header.value}${spam ? ' spam' : ''}`);
          break;
        case 'list-id':
        case 'list-unsubscribe':
        case 'x-mailingid':
        case 'x-feedback-id':
          spam = true;
          if (logging.isDebug()) DEBUG(`\tlist ${header.value}${spam ? ' spam' : ''}`);
          break;
      }
    }

    if (spam || trash || !save_message.sender || this.refresh_ignore_ids.includes(save_message.id)) {
      deletes.push(save_message);
      return [null, deletes];
    } else {
      let sender = await this.personByEmail(save_message.sender.email);
      let updated_person = save_message.sender;

      if (!sender) sender = data.people.tempPerson(updated_person);
      sender = new Person(this.addPerson(sender));

      slimEntity(sender);
      save_message.sender = sender;
      const person_recipients = [];

      try {
        if (save_message.recipient) {
          for (const recipient of save_message.recipient) {
            let person = await this.personByEmail(recipient.email);
            if (!person) {
              person = peopleUtils.parseMessagePerson(recipient.email, received);
              person = data.people.tempPerson(person);
            }
              
            if (person) {
              person = new Person(this.addPerson(person));
              slimEntity(person);
              person_recipients.push(person);
            }
          }
        }

        save_message.recipient = person_recipients;
      } catch (err) {
        console.log(`Error resolving email person: ${err.stack}`);
      }

      return [save_message, deletes];
    }
  }

  /**
   * Refresh messages from Google using `gmail.users.history.list`. If the history token is 404, will return false.
   *
   * @param isHistory - Flag indicating if we are processing for history or not
   * @private
   * @async
   */
  private async googleMessagesReadUsingHistoryList(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingHistoryList', this.user.profile, 'starting');
    // const gbs: GmailBatchStream = new GmailBatchStream(this.user.tokens.access_token, BATCH_OPTIONS);
    // const gbsGmail = gbs.gmail();
    const args = {
      auth: this.getClient() as any,
      userId: this.user.profile,
      pageToken: undefined,
      startHistoryId: undefined,
    } as gmail_v1.Params$Resource$Users$History$List;

    // Will have to store multiple sync tokens, once all the pages of a sync token are done, if we have a new one, we can delete the old one.
    if (this.metaData().nextPageToken) args.pageToken = this.metaData().nextPageToken;
    else if (this.metaData().nextSyncToken &&
      this.metaData().nextSyncToken !== "NaN") args.startHistoryId = this.metaData().nextSyncToken;

    if (logging.isDebug(this.user.profile)) {
      logging.debugFP(this.log_name, 'googleMessagesReadUsingHistoryList', this.user.profile, `startHistoryId = ${args.startHistoryId}`);
      logging.debugFP(this.log_name, 'googleMessagesReadUsingHistoryList', this.user.profile, `list args = ${util.format(args)}`);
    }

    let message_ids: string[] = [];
    let response, error;
    try {  
      response = await this.repeatableCallWithBackOff(gmail.users.history.list, 'gmail.users.history.list', gmail, args);
    } catch(err) { error = err; }

    if (error) {
      logging.errorFP(LOG_NAME, 'googleMessagesReadUsingHistoryList', this.user.profile, 'Error reading messages', error);
      await new GoogleErrorHandler().handleError(this.user, error, (user, message) => {
        data.users
          .message(user, message)
          .then(() => { if (logging.isDebug(user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingHistoryList', user.profile, `Sent message to user - ${message}`) })
          .catch(err => logging.errorFP(this.log_name, 'googleMessagesReadUsingHistoryList', user.profile, `Unable to send message to user = ${message}`, err));

      }, () => { throw error });

      // if this was token related it would have thrown, so drop the sync tokes and try again
      this.metaData().nextPageToken = null;
      this.metaData().nextSyncToken = null;

      logging.warnFP(LOG_NAME, 'googleMessagesReadUsingHistoryList', this.user.profile, 'Falling back to provider read after error', error);
      return this.providerRead(is_history);
    }

    if (response && response.data) {
      this.metaData().nextPageToken = response.data.nextPageToken;
      this.metaData().nextSyncToken = response.data.historyId;

      const message_history = response.data.history;

      if (message_history) {
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingHistoryList', this.user.profile, `messagesDeleted = ${util.format(message_history.messagesDeleted)}`);
        if (message_history.messagesDeleted) {
          for (const del of message_history.messagesDeleted) this.refresh_ignore_ids.push(del.message.id);
        }

        if (message_history.messages) message_ids = message_ids.concat(message_history.messages.map(m => m.id));
      }
    }

    if (response && response.error) {
      logging.errorFP(LOG_NAME, 'googleMessagesReadUsingHistoryList', this.user.profile, `Error reading messages hsitory`, response.error);
      this.metaData().nextSyncToken = null;
      this.metaData().historyDone = false;
    }

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingHistoryList', this.user.profile, 'processMessageIdStream starting');
    const [updates, deletes] = await this.processMessages(message_ids, is_history);
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingHistoryList', this.user.profile, 'processMessageIdStream completed');
    return [updates, deletes];
  }

  /**
   * Refresh messages from Google using `gmail.users.messages.list`.
   *
   * @param is_history - Flag indicating if we are processing for history or not
   * @param page_token - Optional paging token to use
   * @param query - Optional query to use
   * @private
   * @async
   */
  private async googleMessagesReadUsingMessagesList(is_history: boolean, page_token: string, query: string): Promise<[IEntity[], IEntity[]]> {
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingMessagesList', this.user.profile, `Starting. history = ${is_history}`);
    // const gbs: GmailBatchStream = new GmailBatchStream(this.user.tokens.access_token, BATCH_OPTIONS);
    // const gbsGmail = gbs.gmail();
    const args = {
      auth: this.getClient() as any,
      userId: this.user.profile,
      requestSyncToken: true,
      q: query,
      includeSpamTrash: true, // check spam and trash for deleted messages
      pageToken: page_token,
    } as gmail_v1.Params$Resource$Users$Messages$List;

    // if (page_token) args.pageToken = page_token;
    // else args.q = query;

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingMessagesList', this.user.profile, `args = ${util.format(args)}, history = ${is_history}`);
    
    let message_ids: string[] = [];
    const response = await this.repeatableCallWithBackOff(gmail.users.messages.list, 'gmail.users.messages.list', gmail, args);

    if (response && response.data) {
      if (is_history) this.metaData().historyPageToken = response.data.nextPageToken;
      else this.metaData().nextPageToken = response.data.nextPageToken;

      const messages = response.data.messages;
      if (messages) message_ids = message_ids.concat(messages.map(m => m.id));
    }

    if (response && response.error) {
      logging.errorFP(LOG_NAME, 'googleMessagesReadUsingMessageList', this.user.profile, `Error reading messages hsitory`, response.error);
      this.metaData().nextSyncToken = null;
      this.metaData().historyDone = false;
    }

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingMessagesList', this.user.profile, `processMessageIdStream starting. history = ${is_history}`);
    const [updates, deletes] = await this.processMessages(message_ids, is_history);
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesReadUsingMessagesList', this.user.profile, `processMessageIdStream completed. history = ${is_history}`);
    return [updates, deletes];
  }

  private async googleMessagesSyncDrafts(): Promise<[IEntity[], IEntity[]]> {
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesSyncDrafts', this.user.profile, 'Starting.');
    const args = {
      auth: this.getClient() as any,
      userId: this.user.profile,
      includeSpamTrash: true, // check spam and trash for deleted messages
    } as gmail_v1.Params$Resource$Users$Messages$List;

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesSyncDrafts', this.user.profile, `args = ${util.format(args)}`);

    let message_ids: string[] = [];
    const response = await this.repeatableCallWithBackOff(gmail.users.drafts.list, 'gmail.users.drafts.list', gmail, args);

    if (response && response.data) {
      const messages = response.data.drafts;
      if (messages) message_ids = message_ids.concat(messages.map(m => m.message ? m.message.id : m.id));
    }

    if (response && response.error) {
      logging.errorFP(LOG_NAME, 'googleMessageSyncDrafts', this.user.profile, `Error reading messages hsitory`, response.error);
      this.metaData().nextSyncToken = null;
      this.metaData().historyDone = false;
    }
    
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesSyncDrafts', this.user.profile, 'processMessageIdStream starting.');
    const [updates, deletes] = await this.processMessages(message_ids, true);
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleMessagesSyncDrafts', this.user.profile, 'processMessageIdStream completed.');

    const existing = await data.messages.drafts(this.user);
    const remote_ids = updates.map(e => e.id);
    const delete_ids = deletes.map(e => e.id);
    for (const message of existing) {
      if (!remote_ids.includes(message.id) && !delete_ids.includes(message.id)) deletes.push(message);
    }

    return [updates, deletes];
  }

  /**
   * Process a stream of message IDs
   *
   * @param gbs - GmailBatchStream instance
   * @param gbs_gmail - GmailBatchStream gmail client
   * @param messageIdStream - Message ID stream (highland stream)
   * @param is_history - Flag indicating if we are processing for history or not
   * @private
   * @async
   */
  private async processMessages(message_ids: string[], is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processMessages', this.user.profile, 'Starting');

    const auth = this.getClient();
    const userId = this.user.profile;
    const format = 'metadata';
    const fields = MESSAGE_FIELDS;

    const run_ids = message_ids.slice();

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processMessages', this.user.profile, 'processing messages started');
    const f_messages: {[key: string]: Message} = {};
    const updates = {};
    const deletes = {};
 
    while (run_ids.length) {
      // Run in batches of 100. Use quota of 5 (for users.messages.get)
      const responses: {error?: any; data: gmail_v1.Schema$Message}[] = await Promise.all(run_ids.splice(0,100).map(async id => {
          try {  
            const response = await this.repeatableCallWithBackOff(gmail.users.messages.get, 'gmail.users.messages.get', gmail, { auth, userId, id, format, fields })
            return response;
          } catch(error) {
            return {error};
          }
        } 
      ));

      const errors = responses.filter(r => r && r.error).map(r => r.error);
      const g_messages = responses.filter(r => r && r.data).map(r => r.data);

      for (const error of errors) {
        logging.errorFP(LOG_NAME, 'processMessages', this.user.profile, 'Error processing messages', error);
        await new GoogleErrorHandler().handleError(this.user, error, (user, message) => {
          data.users
            .message(user, message)
            .then(() => { if (logging.isDebug(user.profile)) logging.debugFP(this.log_name, 'processMessages', user.profile, `Sent message to user - ${message}`) })
            .catch(err => logging.errorFP(this.log_name, 'processMessages', user.profile, `Unable to send message to user = ${message}`, err));

        }, () => { throw error });
      }

      if (logging.isDebug(this.user.profile)) {
        logging.debugFP(this.log_name, 'processMessages', this.user.profile, `new page token = ${is_history ? this.metaData().historyPageToken : this.metaData().nextPageToken}`);
        logging.debugFP(this.log_name, 'processMessages', this.user.profile, `messages length = ${g_messages.length}`);
      }

      // When reading forward, use the google history ID
      if (!is_history) {
        // Don't be confused with the name history. Our history is if we are reading backwards. Google history(historyId) is their sync token
        // Update our history ID if we got a new higher one.
        const old_sync = this.metaData().nextSyncToken;
        const new_sync = Math.max.apply(null, [old_sync].concat(g_messages.map(m => m.historyId)).map(id => parseInt(id, 10)));

        if (new_sync === -Infinity && g_messages.length) {
          // If we received messages but the historyId was -Infinity, remove it. Not sure this will ever happen
          this.metaData().nextSyncToken = null;
          if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processMessages', this.user.profile, `historyId -Infinity removing ${old_sync}`);
        } else if (!old_sync || isNaN(parseInt(old_sync, 10)) || parseInt(old_sync, 10) < new_sync) {
          if (!isNaN(new_sync)) this.metaData().nextSyncToken = new_sync.toString();
          if (this.metaData().nextPageToken) this.metaData().pageTokenFromSyncToken = true;
          if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processMessages', this.user.profile, `historyId ${old_sync} now ${new_sync}`);
        } else {
          if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processMessages', this.user.profile, `historyId didn't change ${old_sync}`);
        }
      }

      for (const g_message of g_messages) {
        const [f_message, f_deletes] = await this.foraMessageFromGoogleMessage(g_message);
        if (f_message) f_messages[f_message.id] = f_message;
        for (const f_delete of f_deletes) deletes[f_delete.id] = f_delete;
      }
    }

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processMessageIdStream', this.user.profile, 'processing messages completed');

    return [[...Object.values(f_messages), ...this.new_people], Object.values(deletes)];
  }
}
