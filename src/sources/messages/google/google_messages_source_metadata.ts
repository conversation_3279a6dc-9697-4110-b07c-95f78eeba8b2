/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { JsonObject, JsonProperty } from 'json2typescript';
import { AbstractSourceMetadata } from '../../a_source_metadata';

@JsonObject('GoogleMessagesSourceMetadata')
export class GoogleMessagesSourceMetadata extends AbstractSourceMetadata {
  @JsonProperty('pageTokenFromSyncToken', Boolean, true) private _pageTokenFromSyncToken = false;

  get pageTokenFromSyncToken(): boolean {
    return this._pageTokenFromSyncToken;
  }

  set pageTokenFromSyncToken(value: boolean) {
    this._pageTokenFromSyncToken = value;
  }
}
