/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Message as GraphMessage, ItemBody } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import _ from 'lodash';
import util from 'util';

import config from '../../../config';
import data from '../../../data';
import ForaUser from '../../../session/user';

import { IEntity, Message, Person } from '../../../types/items';
import { AuthLevel, AuthProviders, EntityType, Uid } from '../../../types/shared';

import { DAYS, HOURS } from '../../../utils/datetime';
import { createRawMessage } from '../../../utils/format';
import { hash, slimEntity } from '../../../utils/funcs';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';
import peopleUtils from '../../../utils/people';

import { AuthProvider } from '../../../auth/auth_provider';
import { AbstractMicrosoftSourcePlugin } from '../../a_microsoft_source_plugin';
import { MicrosoftGraphConverter } from '../../helpers/microsoft/microsoft_graph_conversions';
import MicrosoftGraphMessageHelper from '../../helpers/microsoft/microsoft_graph_message_helper';
import { SourceKeys } from '../../index';
import { IMessagesSourcePlugin } from '../i_messages_source_plugin';
import { FolderMetadata, MicrosoftMessagesSourceMetadata } from './microsoft_messages_source_metadata';

const DEBUG = (require('debug') as any)('fora:data:sources:messages:microsoft');
const FOLDER_NAMES = ['drafts', 'inbox', 'sentitems'];
const MSG_FIELDS = 'id,sender,isRead,isDraft,receivedDateTime,subject,bccRecipients,ccRecipients,toRecipients,from,replyTo,internetMessageHeaders';

const LOG_NAME = 'data.sources.MicrosoftMessageSourcePlugin';

/**
 * Microsoft Messages Source Plugin
 *
 *  - https://docs.microsoft.com/en-us/graph/api/resources/mail-api-overview?view=graph-rest-1.0
 *  - https://docs.microsoft.com/en-us/graph/api/resources/mail-api-overview?view=graph-rest-beta
 *  - PAGING and DELTA - https://docs.microsoft.com/en-us/graph/delta-query-messages
 *  - KNOWN FOLDER NAMES - https://docs.microsoft.com/en-us/graph/api/resources/mailfolder?view=graph-rest-1.0
 */
export class MicrosoftMessageSourcePlugin extends AbstractMicrosoftSourcePlugin<Message, MicrosoftMessagesSourceMetadata> implements IMessagesSourcePlugin {
  public static providers = [AuthProviders.Microsoft, AuthProviders.Msal];
  private readonly saved_people: { [key: string]: Person } = {};

  /** @inheritDoc */
  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(
      LOG_NAME,
      SourceKeys.MicrosoftMessages,
      EntityType.Message,
      user,
      account,
      user_people,
      user_emails,
      MicrosoftMessagesSourceMetadata,
      user.hasAccount(AuthProviders.Msal, account) ? AuthProviders.Msal : AuthProviders.Microsoft,
      user.hasAccount(AuthProviders.Msal, account) ? AuthProvider.msal.getScopesForMessages() : AuthProvider.microsoft.getScopesForMessages(),
      true,
      true,
      'received',
      config.get('MSFT_MSP_EXPIRATION_DAYS_BACK', 30),
    );
  }

  /** @inheritDoc */
  async archive(f_message: Message): Promise<[Message, IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) return [undefined, []];

    // NOTE - If its a draft, we may have to resort to deletion rather moving to archive folder, not sure

    const ms_message_out = await MicrosoftGraphMessageHelper.messageArchive(this.user, this.account, f_message.id);
    return this.foraMessageFromMicrosoftMessage(ms_message_out);
  }

  /** @inheritDoc */
  async draft(user: ForaUser, recipients: Partial<Person>[], from: Person, email: string, subject: string, text: string, cc_to: Person[] = []): Promise<[Message, IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) {
      const raw_message = createRawMessage(recipients, from, user.email, subject, text, cc_to);
      const message = new Message({
        archived: false,
        draft: true,
        recipient:recipients,
        subject,
        raw:raw_message.raw,
      });
      message.id = hash(JSON.stringify(message));
      return [message, []];
    }

    const ms_message_in = MicrosoftMessageSourcePlugin.foraMessageToMicrosoftMessage(recipients, from, user.email, subject, text, cc_to);
    const ms_message_out = await MicrosoftGraphMessageHelper.messageCreate(this.user, this.account, ms_message_in);
    return this.foraMessageFromMicrosoftMessage(ms_message_out);
  }

  /** @inheritDoc */
  async markRead(f_message: Message): Promise<[Message, IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) return [undefined, []];

    const ms_message_out = await MicrosoftGraphMessageHelper.messageMarkAsRead(this.user, this.account, f_message.id);
    return this.foraMessageFromMicrosoftMessage(ms_message_out);
  }

  /** @inheritDoc */
  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    if (this.isEnabled()) { if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'providerRead', this.user.profile, 'enabled'); }
    else {
      logging.warnFP(this.log_name, 'providerRead', this.user.profile, 'NOT enabled, returning');
      DEBUG('providerRead: metadata = %o', this.metaData());
      return [[], []];
    }

    const now = new Date();
    let updates: IEntity[] = [];
    let deletes: IEntity[] = [];

    try {
      for (const folder of FOLDER_NAMES) {
        const [folder_updates, folder_deletes] = await this.folderRead(is_history, folder, now);
        updates = updates.concat(folder_updates);
        deletes = deletes.concat(folder_deletes);
      }

      return [updates, deletes];
    } catch (err) {
      logging.errorFP(this.log_name, 'providerRead', this.user.profile, 'Error reading from the provider', err);
      throw new Error(`Error reading from the provider - ${util.format(err)}`);
    }
  }

  /** @inheritDoc */
  async reload(message: Message): Promise<Message> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) return message;

    const ms_message_out = await MicrosoftGraphMessageHelper.messageGet(this.user, this.account, message.id);
    if (ms_message_out) {
      const [out_message] = await this.foraMessageFromMicrosoftMessage(ms_message_out);
      return out_message;
    }
    return null;
  }

  /** @inheritDoc */
  async send(f_message: Message): Promise<[Message, IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Email)) return;

    try {
      const ms_message_out = await MicrosoftGraphMessageHelper.messageSend(this.user, this.account, f_message.id);
      return [MicrosoftGraphConverter.convertMessageFromGraphMessage(this.user, ms_message_out), []];
    } catch (err) {
      throw new Error(`Error sending message - ${err}`);
    }
  }

  private async folderRead(is_history: boolean, folder_id: string, now: Date): Promise<[IEntity[], IEntity[]]> {
    let api_path = `/me/mailFolders/${folder_id}/messages/delta`;
    const metadata = this.metaData().hasFolder(folder_id) ? this.metaData().getFolder(folder_id) : this.metaData().setupFolder(folder_id, folder_id);

    if (is_history) {
      // Sync processing backwards

      if (metadata.historyDone) return [[], []];

      if (metadata.historyPageLink) api_path = metadata.historyPageLink;
      else {
        const time_min = DAYS(now, config.get('MSFT_MSP_HISTORY_NEWER_THAN', -30)).toISOString();
        // - SELECT is off in offline because it breaks fetch_VCR - the file names become to long.
        //   Need to fork that project and handle by making the file name a hash if its over X length or something.
        // - Date filter is hard coded so it can be deterministic for testing
        if (config.isEnvOffline()) api_path += '?$filter=receivedDateTime+ge+2018-12-01T00:00:00.000Z';
        else api_path += `?$filter=receivedDateTime+ge+${time_min}&$select=${MSG_FIELDS}`;
      }
    } else {
      // Sync processing forward (if statements nested for easier readability of all the possible cases)

      if (metadata.nextPageLink) api_path = metadata.nextPageLink;
      else if (metadata.nextSyncLink) api_path = metadata.nextSyncLink;
      else {
        const time_min = HOURS(now, config.get('MSFT_MSP_REFRESH_NEWER_THAN', -1)).toISOString();
        // - SELECT is off in offline because it breaks fetch_VCR - the file names become to long.
        //   Need to fork that project and handle by making the file name a hash if its over X length or something.
        // - Date filter is hard coded so it can be deterministic for testing
        if (config.isEnvOffline()) api_path += '?$filter=receivedDateTime+ge+2018-12-01T00:00:00.000Z';
        else api_path += `?$filter=receivedDateTime+ge+${time_min}&$select=${MSG_FIELDS}`;
      }
    }

    try {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'folderRead', this.user.profile, `path = ${api_path}`);

      const response = await MicrosoftGraphMessageHelper.getResponse(this.user, this.account, api_path, 500);

      if (logging.isDebug(this.user.profile)) {
        logging.debugFP(this.log_name, 'folderRead', this.user.profile, `RESULT count = ${response.value.length}`);
        logging.debugFP(this.log_name, 'folderRead', this.user.profile, `RESULT next link = ${response['@odata.nextLink']}`);
        logging.debugFP(this.log_name, 'folderRead', this.user.profile, `RESULT delta link = ${response['@odata.deltaLink']}`);
      }

      const ms_messages: GraphMessage[] = response.value;
      MicrosoftMessageSourcePlugin.handleResponseTokens(this.user, metadata, is_history, response);

      if (logging.isDebug(this.user.profile)) {
        logging.debugFP(this.log_name, 'folderRead', this.user.profile, `METADATA historyPageToken = ${metadata.historyPageLink}`);
        logging.debugFP(this.log_name, 'folderRead', this.user.profile, `METADATA nextPageToken = ${metadata.nextPageLink}`);
        logging.debugFP(this.log_name, 'folderRead', this.user.profile, `METADATA nextSyncToken = ${metadata.nextSyncLink}`);
      }

      if (ms_messages.length) return await this.processMessages(ms_messages);
      else return [[], []];
    } catch (err) {
      // On any error, we need to reset all the new/next variables so the next time we come through we start fresh
      this.metaData().newDone = false;

      logging.errorFP(this.log_name, 'folderRead', this.user.profile, 'Error retrieving messages', err);
      throw err;
    }
  }

  /**
   * Convert a Microsoft message to a AskFora message (and ancillary objects)
   *
   * @param ms_message - the Microsoft message to convert
   * @returns Converted message
   * @private
   */
  private async foraMessageFromMicrosoftMessage(ms_message: GraphMessage): Promise<[Message, IEntity[]]> {
    const deletes: IEntity[] = [];

    // TODO(current) - Validate we don't need to check for SPAM or TRASH (not sure how, maybe folder)

    const is_spam = MicrosoftMessageSourcePlugin.isSpam(ms_message);
    const f_message = MicrosoftGraphConverter.convertMessageFromGraphMessage(this.user, ms_message);
    if (logging.isDebug()) DEBUG('foraMessageFromMicrosoftMessage: Converted incoming message = %o', f_message);

    if (is_spam || !f_message.sender) {
      deletes.push(f_message);
      return [null, deletes];
    } else {
      let sender = await this.personByEmail(f_message.sender.email);
      let updated_person = f_message.sender;

      if (!sender) sender = data.people.tempPerson(updated_person);
      sender = new Person(this.addPerson(sender));

      slimEntity(sender);
      f_message.sender = sender;
      const person_recipients = [];

      try {
        if (f_message.recipient) {
          for (const recipient of f_message.recipient) {
            if (logging.isDebug()) DEBUG('foraMessageFromMicrosoftMessage: recipient = %o', recipient);
            let person = await this.personByEmail(recipient.email);
            if (!person) {
              person = peopleUtils.parseMessagePerson(recipient.email, f_message.received);
              person = data.people.tempPerson(person);
            }

            if (person) {
              person = new Person(this.addPerson(person));
              slimEntity(person);
              person_recipients.push(person);
            }
          }
        }

        f_message.recipient = person_recipients;
      } catch (err) {
        console.log(`Error resolving email person: ${err.stack}`);
      }
      return [f_message, deletes];
    }
  }

  private async processMessages(ms_messages: GraphMessage[]): Promise<[IEntity[], IEntity[]]> {
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processMessage', this.user.profile, `starting, messages length = ${ms_messages.length}`);

    const m_messages = {};
    const updates = {};
    const deletes = {};
    for (const ms_message of ms_messages) {
      const [f_message, f_deletes] = await this.foraMessageFromMicrosoftMessage(ms_message);
      if (f_message) m_messages[f_message.id] = f_message;
      for (const f_delete of f_deletes) deletes[f_delete.id] = f_delete;
    }

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processMessage', this.user.profile, 'processing messages completed');
    return [_.concat(Object.values(m_messages), ...this.new_people), Object.values(deletes)];
  }

  static handleResponseTokens(ser: ForaUser, metadata: FolderMetadata, is_history: boolean, result: any): void {
    if (is_history) {
      metadata.historyDone = !result['@odata.nextLink'];
      metadata.historyPageLink = result['@odata.nextLink'];
    } else {
      metadata.nextPageLink = result['@odata.nextLink'];
      metadata.nextSyncLink = result['@odata.deltaLink'];

      if (result['@odata.nextLink']) metadata.newDone = false;
      else if (result['@odata.deltaLink']) metadata.newDone = true;
      else metadata.newDone = true;
    }
  }

  private static foraMessageToMicrosoftMessage(recipients: Partial<Person>[], from: Person, email: string, subject: string, text: string, cc_to: Person[]): GraphMessage {
    const ms_message = {} as GraphMessage;
    ms_message.subject = subject;
    ms_message.body = { contentType: 'text', content: text } as ItemBody;
    ms_message.from = MicrosoftGraphConverter.convertPersonToRecipient(from, email);
    ms_message.toRecipients = recipients.filter(p => !p.self).map(p => MicrosoftGraphConverter.convertPersonToRecipient(p));
    ms_message.ccRecipients = cc_to.filter(p => !p.self).map(p => MicrosoftGraphConverter.convertPersonToRecipient(p));

    return ms_message;
  }

  private static isSpam(ms_message: GraphMessage): boolean {
    let is_spam = false;

    if (ms_message.from && parsers.spamAddress(ms_message.from.emailAddress.address)) {
      if (logging.isDebug()) DEBUG('isSpam: marking message as spam due to from address %o', ms_message.from.emailAddress.address);
      is_spam = true;
    }

    if (ms_message.toRecipients) {
      for (const recipient of ms_message.toRecipients) {
        if (parsers.spamAddress(recipient.emailAddress.address)) {
          if (logging.isDebug()) DEBUG('isSpam: marking message as spam due to from address %o', recipient.emailAddress.address);
          is_spam = true;
        }
      }
    }

    if (ms_message.ccRecipients) {
      for (const recipient of ms_message.ccRecipients) {
        if (parsers.spamAddress(recipient.emailAddress.address)) {
          if (logging.isDebug()) DEBUG('isSpam: marking message as spam due to cc address %o', recipient.emailAddress.address);
          is_spam = true;
        }
      }
    }

    if (ms_message.bccRecipients) {
      for (const recipient of ms_message.bccRecipients) {
        if (parsers.spamAddress(recipient.emailAddress.address)) {
          if (logging.isDebug()) DEBUG('isSpam: marking message as spam due due to bcc address %o', recipient.emailAddress.address);
          is_spam = true;
        }
      }
    }

    if (ms_message.replyTo) {
      for (const recipient of ms_message.replyTo) {
        if (parsers.spamAddress(recipient.emailAddress.address)) {
          if (logging.isDebug()) DEBUG('isSpam: marking message as spam due due to reply-to address %o', recipient.emailAddress.address);
          is_spam = true;
        }
      }
    }

    if (ms_message.internetMessageHeaders) {
      for (const header of ms_message.internetMessageHeaders) {
        if (!header.name) continue;
        const header_lc = header.value ? header.value.toLowerCase() : null;
        switch (header.name.toLowerCase()) {
          case 'x-vr-status':
            if (header_lc !== 'ok' && header_lc !== 'whitelisted') {
              if (logging.isDebug()) DEBUG('isSpam: marking message as spam due due to x-vr-status %o', header_lc);
              is_spam = true;
            }
            break;
          case 'list-id':
          case 'list-unsubscribe':
          case 'x-mailingid':
          case 'x-feedback-id':
            if (logging.isDebug()) DEBUG('isSpam: marking message as spam due due to %o status %o', header.name, header_lc);
            is_spam = true;
            break;
        }
      }
    }

    return is_spam;
  }
}
