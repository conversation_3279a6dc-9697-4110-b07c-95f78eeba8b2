/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { <PERSON>sonConvert, JsonConverter, JsonCustomConvert, JsonObject, JsonProperty, ValueCheckingMode } from 'json2typescript';
import _ from 'lodash';
import { AbstractSourceMetadata } from '../../a_source_metadata';

@JsonObject('FolderMetadata')
export class FolderMetadata {
  @JsonProperty('historyDone', Boolean, true) historyDone: any;
  @JsonProperty('historyPageLink', String, true) historyPageLink: string;
  @JsonProperty('id', String, false) id: string;
  @JsonProperty('name', String, false) name: string;
  @JsonProperty('newDone', Boolean, true) newDone: boolean;
  @JsonProperty('nextPageLink', String, true) nextPageLink: string;
  @JsonProperty('nextSyncLink', String, true) nextSyncLink: string;

  constructor(id?: string, name?: string, historyDone = false, newDone = false, historyPageLink?: string, nextPageLink?: string, nextSyncLink?: string) {
    this.historyDone = historyDone;
    this.historyPageLink = historyPageLink;
    this.id = id;
    this.name = name;
    this.newDone = newDone;
    this.nextPageLink = nextPageLink;
    this.nextSyncLink = nextSyncLink;
  }
}

@JsonObject('FolderDictionary')
export class FolderDictionary {
  [id: string]: FolderMetadata;
}

@JsonConverter
class FolderDictionaryConverter implements JsonCustomConvert<FolderDictionary> {
  deserialize(data: any): FolderDictionary {
    const convert = new JsonConvert();
    // if (config.isSilly()) convert.operationMode = OperationMode.LOGGING;
    convert.ignorePrimitiveChecks = false; // don't allow assigning number to string etc.
    convert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL;
    const dict = new FolderDictionary();
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        dict[key] = convert.deserializeObject(data[key], FolderMetadata);
      }
    }
    return dict;
  }

  serialize(dictionary: FolderDictionary): any {
    const out = {};
    const convert = new JsonConvert();
    // if (config.isSilly()) convert.operationMode = OperationMode.LOGGING;
    convert.ignorePrimitiveChecks = false; // don't allow assigning number to string etc.
    convert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL;
    for (const key in dictionary) {
      if (dictionary.hasOwnProperty(key)) {
        const obj = dictionary[key];
        out[key] = convert.serialize(obj);
      }
    }

    return out;
  }
}

@JsonObject('MicrosoftMessagesSourceMetadata')
export class MicrosoftMessagesSourceMetadata extends AbstractSourceMetadata {
  // Have to allow null here because initially the store will have `{}` as the object
  @JsonProperty('folders', FolderDictionaryConverter, true) private _folders: FolderDictionary = new FolderDictionary();

  get folders(): FolderDictionary {
    return this._folders;
  }

  set folders(value: FolderDictionary) {
    this._folders = value;
  }

  get historyDone(): boolean {
    if (_.isEmpty(this._folders)) return false;
    else return _.every(Object.values(this._folders), folder => folder.historyDone);
  }

  set historyDone(value: boolean) {
    _.forEach(Object.values(this._folders), folder => { 
      folder.historyDone = value
    });
  }

  get historyPageToken(): string {
    return null;
  }

  set historyPageToken(value: string) {
    if (value) throw new Error('historyPageToken has children, you must set them individually');
    else _.forEach(Object.values(this._folders), folder => {
      folder.historyPageLink = value
    });
  }

  get newDone(): boolean {
    if (_.isEmpty(this._folders)) return false;
    else return _.every(Object.values(this._folders), folder => folder.newDone);
  }

  set newDone(value: boolean) {
    // DO NOT ALSO SET THESE TO NULL. `newDone = false` is called inside ensureMetadata so source controller knows to start it
    // folder.nextPageLink = null;
    // folder.nextSyncLink = null;
    _.forEach(Object.values(this._folders), folder => {
      folder.newDone = value
    });
  }

  get nextPageToken(): string {
    return null;
  }

  set nextPageToken(value: string) {
    if (value) throw new Error('nextPageToken has children, you must set them individually');
    else _.forEach(Object.values(this._folders), folder => {
      folder.nextSyncLink = value
    });
  }

  get nextSyncToken(): string {
    return null;
  }

  set nextSyncToken(value: string) {
    if (value) throw new Error('nextSyncToken has children, you must set them individually');
    else _.forEach(Object.values(this._folders), folder => {
      folder.nextSyncLink = value
    });
  }

  deleteFolder(id: string): void {
    delete this._folders[id];
  }

  getFolder(id: string): FolderMetadata {
    return this._folders[id];
  }

  getFolderIds(): string[] {
    return Object.keys(this._folders);
  }

  hasFolder(id: string): boolean {
    return !!this._folders[id];
  }

  hasHistoryPageToken(): boolean {
    const first = _.find(Object.values(this._folders), folder => !!folder.historyPageLink);
    return !!first;
  }

  hasNextPageToken(): boolean {
    const first = _.find(Object.values(this._folders), folder => !!folder.nextPageLink);
    return !!first;
  }

  hasNextSyncToken(): boolean {
    const first = _.find(Object.values(this._folders), folder => !!folder.nextSyncLink);
    return !!first;
  }

  setupFolder(id: string, name: string): FolderMetadata {
    this._folders[id] = new FolderMetadata(id, name);
    return this._folders[id];
  }
}
