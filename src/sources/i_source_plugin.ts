/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IEntity } from '../types/items';
import { EntityType } from '../types/shared';
import { ISourceMetadata } from './i_source_metadata';

export interface ISourcePlugin<T extends IEntity, M extends ISourceMetadata> {
  create(entity: T): Promise<[T, IEntity[]]>;

  dataExpirationFilters(): {name: string; op: string; val: any}[];

  dataHistoryExists(): boolean;

  dataHistoryRead(): Promise<[IEntity[], IEntity[]]>;

  dataNewExists(): boolean;

  dataNewRead(): Promise<[IEntity[], IEntity[]]>;

  delete(entity: Partial<T>): Promise<void>;

  disabledError(disabled: boolean): void;

  firstRun(): Promise<[IEntity[], IEntity[]]>;

  isDisabledError(): boolean;

  isEnabled(): boolean;

  isFirstRun(): boolean;

  metaData(): M;

  reload(entity: T): Promise<T>;

  batchReload(entity: Partial<T>[]): Promise<T[]>;

  supportsDataExpiration(): boolean;

  typeFor(): EntityType;

  update(entity: T): Promise<[T, IEntity[]]>;

  reset(): void;
}
