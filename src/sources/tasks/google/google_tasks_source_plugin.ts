/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { google, tasks_v1 } from 'googleapis';

import { AuthProvider } from '../../../auth/auth_provider';
import data from '../../../data';
import ForaUser from '../../../session/user';
import { InternalError } from '../../../types/globals';
import { IEntity, Person, Task } from '../../../types/items';
import { AuthLevel, AuthProviders, EntityType, Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { AbstractGoogleSourcePlugin } from '../../a_google_source_plugin';
import { GoogleConverter } from '../../helpers/google/google_conversions';
import { SourceKeys } from '../../index';
import { ITasksSourcePlugin, TASK_LIST_NAME, TaskStatus } from '../i_tasks_source_plugin';
import { GoogleTasksSourceMetadata } from './google_tasks_source_metadata';

const tasks = google.tasks('v1');

const LOG_NAME = 'data.sources.GoogleTasksSourcePlugin';

/**
 * Google Tasks Source Plugin
 *
 *  - http://google.github.io/google-api-nodejs-client/modules/_apis_tasks_v1_.html
 *  - https://github.com/google/google-api-nodejs-client/blob/master/src/apis/tasks/v1.ts
 *
 * Notes:
 *  - Default list in Google is @defaults, we are not using that
 *  - All our tasks will be stored in the 'AskFora' list, so that is the only
 *    one that we are retrieving
 */

export class GoogleTasksSourcePlugin extends AbstractGoogleSourcePlugin<Task, GoogleTasksSourceMetadata> implements ITasksSourcePlugin {
  public static providers = [AuthProviders.Google];

  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(LOG_NAME, SourceKeys.GoogleTasks, EntityType.Task, user, account, user_people, user_emails, GoogleTasksSourceMetadata, AuthProviders.Google, AuthProvider.google.getScopesForTasks(), false, false);
  }

  /** @inheritDoc */
  public async complete(task: Task): Promise<[Task, IEntity[]]> {
    await this.taskListEnsure();

    task.status = TaskStatus.completed;
    try {
      return this.update(task);
    } catch (err) {
      if (err.message.includes('404')) logging.warnFP(LOG_NAME, 'complete', this.user.profile, 'Ignoring missing complete task', err);
      else throw err;
    }
  }

  /** @inheritDoc */
  public async uncomplete(task: Task): Promise<[Task, IEntity[]]> {
    await this.taskListEnsure();

    task.status = TaskStatus.needsAction;
    try {
      return this.update(task);
    } catch (err) {
      if (err.message.includes('404')) logging.warnFP(LOG_NAME, 'iuncomplete', this.user.profile, 'Ignoring missing uncomplete task', err);
      else throw err;
    }
  }

  /** @inheritDoc */
  public async create(f_task_in: Task): Promise<[Task, IEntity[]]> {
    await this.taskListEnsure();

    // Convert our task to a google task and insert it
    const g_task_in = GoogleConverter.taskToGoogleTask(f_task_in, false);
    const args = {
      auth: this.getClient() as any,
      tasklist: this.metaData().taskListId,
      requestBody: g_task_in,
    } as tasks_v1.Params$Resource$Tasks$Insert;

    const g_task_out: tasks_v1.Schema$Task = await this.googleInsert(args).catch(err => {
      throw new Error(`Error creating task ${err.code} ${err.message}`);
    });

    if (g_task_out) {
      const f_task_out = GoogleConverter.taskFromGoogleTask(this.user, this.metaData().taskListId, g_task_out, f_task_in);
      return [f_task_out, []];
    }

    return [null, []];
  }

  /** @inheritDoc */
  public async delete(f_task_in: Partial<Task>): Promise<void> {
    await this.taskListEnsure();
    const args = {
      auth: this.getClient() as any,
      tasklist: f_task_in.taskListId,
      task: f_task_in.id,
    } as tasks_v1.Params$Resource$Tasks$Delete;

    await this.googleDelete(args).catch(err => {
      throw new Error(`Error deleting task ${err.code} ${err.message}`);
    });
  }

  /** @inheritDoc */
  async providerRead(_is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Organizer)) {
      logging.warnFP(this.log_name, 'providerRead', this.user.profile, `skipping due to not authenticated or improper permissions ${JSON.stringify(this.user.permissions)}`);
      return [[], []];
    }

    logging.infoFP(this.log_name, 'providerRead', this.user.profile, 'Reading from provider');
    await this.taskListEnsure();
    const task_list_id = this.metaData().taskListId;

    try {
      // Get our tasks
      const g_tasks: tasks_v1.Schema$Tasks = await this.taskListTasks();
      const items = g_tasks && g_tasks.items ? g_tasks.items : [];
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'providerRead', this.user.profile, `Found ${items.length} tasks`);

      // Convert them to Fora tasks and return them
      const f_tasks_update: Task[] = [];
      const f_tasks_delete: Task[] = [];
      if (items) {
        for (const g_task of items) {
          if (!g_task.deleted) { // g_task.status === TaskStatus.needsAction) {
            const f_task = await data.tasks.byId(this.user, g_task.id);
            f_tasks_update.push(GoogleConverter.taskFromGoogleTask(this.user, task_list_id, g_task, f_task));
          } else f_tasks_delete.push(GoogleConverter.taskFromGoogleTask(this.user, task_list_id, g_task, null, false));
        }
      }
      return [f_tasks_update, f_tasks_delete];
    } catch (err) {
      logging.errorFP(this.log_name, 'providerRead', this.user.profile, 'Error loading tasks', err);
      throw err;
    }
  }

  /** @inheritDoc */
  public async reload(f_task_in: Task): Promise<Task> {
    if (!f_task_in.taskListId) throw new InternalError(500, `Task missing taskListId`, f_task_in);

    // await this.taskListEnsure();
    const args = {
      auth: this.getClient() as any,
      tasklist: f_task_in.taskListId,
      task: f_task_in.id,
    } as tasks_v1.Params$Resource$Tasks$Get;

    const g_task_out = await this.googleGet(args).catch(err => {
      throw new InternalError(err.code, `Error reloading task ${f_task_in.id} from list ${f_task_in.taskListId}: ${err.message}`, f_task_in);
    });


    if (g_task_out) {
      const f_task_out = GoogleConverter.taskFromGoogleTask(this.user, f_task_in.taskListId, g_task_out, f_task_in);
      return f_task_out;
    }

    return null;
  }

  /** @inheritDoc */
  public async update(f_task_in: Task): Promise<[Task, IEntity[]]> {
    await this.taskListEnsure();
    const args = {
      auth: this.getClient() as any,
      tasklist: f_task_in.taskListId ? f_task_in.taskListId : this.metaData().taskListId,
      task: f_task_in.id,
      requestBody: GoogleConverter.taskToGoogleTask(f_task_in, true),
    } as tasks_v1.Params$Resource$Tasks$Update;

    const g_task_out = await this.googleUpdate(args).catch(err => {
      throw new Error(`Error updating task ${err.code} ${err.message}`);
    });

    if (g_task_out) {
      const f_task_out = GoogleConverter.taskFromGoogleTask(this.user, f_task_in.taskListId, g_task_out, f_task_in);
      return [f_task_out, []];
    } 

    return [f_task_in, []];
  }

  protected settings() {
    return this.user.settings.tasks;
  }

  private async googleDelete(args: tasks_v1.Params$Resource$Tasks$Delete): Promise<void> {
    await this.repeatableCallWithBackOff(tasks.tasks.delete, 'tasks.tasks.delete', tasks, args);
    return;
  }

  private async googleGet(args: tasks_v1.Params$Resource$Tasks$Get): Promise<tasks_v1.Schema$Task> {
    const response = await this.repeatableCallWithBackOff(tasks.tasks.get, 'tasks.tasks.get', tasks, args);
    return response ? response.data : null;
  }

  private async googleInsert(args: tasks_v1.Params$Resource$Tasks$Insert): Promise<tasks_v1.Schema$Task> {
    const response = await this.repeatableCallWithBackOff(tasks.tasks.insert, 'tasks.tasks.insert', tasks, args);
    return response ? response.data : null;
  }

  private async googleUpdate(args: tasks_v1.Params$Resource$Tasks$Update): Promise<tasks_v1.Schema$Task> {
    const response = await this.repeatableCallWithBackOff(tasks.tasks.update, 'tasks.tasks.update', tasks, args);
    return response ? response.data : null;
  }

  /**
   * Get a Google task list by its display name
   *
   * @returns {Promise<*[Error, Schema$TaskList]>}
   * @private
   * @async
   */
  private async taskListByName(): Promise<tasks_v1.Schema$TaskList> {
    const response = await this.repeatableCallWithBackOff(tasks.tasklists.list, 'tasks.tasklists.list', tasks, { auth: this.getClient() as any});

    if (response && response.data && response.data.items) {
      for (const g_task_list of response.data.items) {
        if (g_task_list.title === TASK_LIST_NAME) return g_task_list;
      }
    }

    return null;
  }

  /**
   * Create a new list in Google with the provided name
   *
   * @returns {Promise<*[Error, Schema$TaskList]>}
   * @private
   * @async
   */
  private async taskListCreate(): Promise<tasks_v1.Schema$TaskList> {
    const args = {
      auth: this.getClient() as any,
      requestBody: { title: TASK_LIST_NAME } as tasks_v1.Schema$TaskList,
    } as tasks_v1.Params$Resource$Tasklists$Insert;

    const response = await this.repeatableCallWithBackOff(tasks.tasklists.insert, 'tasks.tasklists.insert', tasks, args);
    return response ? response.data : null;
  }

  /**
   * Ensure that the Google task list with provided name exists.
   *
   * This will return the existing list or create one if necessary
   *
   * @returns {Promise<*[Error, Schema$TaskList]>}
   * @private
   * @async
   */
  private async taskListEnsure(): Promise<void> {
    try {
      // Get the list if it exists
      let g_task_list: tasks_v1.Schema$TaskList = await this.taskListByName();
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'taskListEnsure', this.user.profile, `Existing task list ${JSON.stringify(g_task_list)}`);

      // If we don't have a list, we need to create it
      if (!g_task_list) {
        g_task_list = await this.taskListCreate();
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'taskListEnsure', this.user.profile, `Created task list ${JSON.stringify(g_task_list)}`);
      }

      if (!g_task_list) throw new Error('No response creating task list');

      this.metaData().taskListId = g_task_list.id;
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'taskListEnsure', this.user.profile, `Metadata task list ID ${this.metaData().taskListId}`);
    } catch (err) {
      logging.errorFP(this.log_name, 'taskListEnsure', this.user.profile, 'Error ensuring task list', err);
      throw err;
    }
  }

  /**
   * Get the tasks for the provided task list
   *
   * @returns {Promise<*[Error, Schema$Tasks]>}
   * @private
   * @async
   */
  private async taskListTasks(): Promise<tasks_v1.Schema$Tasks> {
    try {
      const args = {
        auth: this.getClient() as any,
        maxResults: 1000, // Default is 100, don't want to have to deal with paging yet
        tasklist: this.metaData().taskListId,
      } as tasks_v1.Params$Resource$Tasks$List;

      logging.infoFP(this.log_name, 'taskListTasks', this.user.profile, `Retrieving tasks for our list (${this.metaData().taskListId})`);
      const result = await this.repeatableCallWithBackOff(tasks.tasks.list, 'tasks.tasks.list', tasks, args);
      return result.data;
    } catch (err) {
      logging.errorFP(this.log_name, 'taskListTasks', this.user.profile, 'Error reading tasks from list', err);
      throw err;
    }
  }
}
