/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { JsonObject, JsonProperty } from 'json2typescript';
import { AbstractSourceMetadata } from '../a_source_metadata';

@JsonObject('AbstractTaskSourceMetadata')
export class AbstractTaskSourceMetadata extends AbstractSourceMetadata {
  @JsonProperty('taskListId', String, true) private _taskListId: string = null;

  get taskListId(): string {
    return this._taskListId;
  }

  set taskListId(value: string) {
    this._taskListId = value;
  }
}
