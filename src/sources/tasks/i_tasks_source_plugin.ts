/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IEntity, Task } from '../../types/items';
import { ISourceMetadata } from '../i_source_metadata';
import { ISourcePlugin } from '../i_source_plugin';

export const TASK_LIST_NAME = 'AskFora';

export enum TaskStatus {
  completed = 'completed',
  needsAction = 'needsAction',
}

export interface ITasksSourcePlugin extends ISourcePlugin<Task, ISourceMetadata> {
  /**
   * Mark a task as `completed`
   *
   * @param task Task to mark complete
   * @return marked task
   */
  complete(task: Task): Promise<[Task, IEntity[]]>;
  uncomplete(task: Task): Promise<[Task, IEntity[]]>;
}
