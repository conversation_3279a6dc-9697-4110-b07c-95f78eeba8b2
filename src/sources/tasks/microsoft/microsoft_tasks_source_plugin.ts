/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { TodoTask } from '@microsoft/microsoft-graph-types';
import { AuthProvider } from '../../../auth/auth_provider';
import data from '../../../data';
import ForaUser from '../../../session/user';
import { IEntity, Person, Task } from '../../../types/items';
import { AuthLevel, AuthProviders, EntityType, Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { AbstractMicrosoftSourcePlugin } from '../../a_microsoft_source_plugin';
import { MicrosoftGraphConverter } from '../../helpers/microsoft/microsoft_graph_conversions';
import MicrosoftGraphTaskHelper from '../../helpers/microsoft/microsoft_graph_task_helper';
import { <PERSON>Keys } from '../../index';
import { ITasksSourcePlugin, TASK_LIST_NAME, TaskStatus } from '../i_tasks_source_plugin';
import { MicrosoftTasksSourceMetadata } from './microsoft_tasks_source_metadata';

const LOG_NAME = 'data.sources.MicrosoftTasksSourcePlugin';

/**
 * Microsoft Tasks Source Plugin
 *
 *  - https://docs.microsoft.com/en-us/graph/api/resources/planner-overview?view=graph-rest-1.0
 *  - https://docs.microsoft.com/en-us/graph/api/resources/outlooktask?view=graph-rest-beta
 *
 * Notes:
 *  - Tasks are not shared so we are not using Plan/Task, we are using Todo/Task
 *  - All our tasks will be stored in the 'AskFora' folder, so that is the only one that we are retrieving
 *  - The POST method always ignores the time portion of startDateTime and dueDateTime in the request body,
 *    and assumes the time to be always midnight in the specified time zone.
 *
 * To Do:
 * - TODO(current) - Add in paging support - https://docs.microsoft.com/en-us/graph/paging - @odata.nextLink
 */
export class MicrosoftTasksSourcePlugin extends AbstractMicrosoftSourcePlugin<Task, MicrosoftTasksSourceMetadata> implements ITasksSourcePlugin {
  public static providers = [AuthProviders.Microsoft, AuthProviders.Msal];
  constructor(user: ForaUser,  account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(LOG_NAME, SourceKeys.MicrosoftTasks, EntityType.Task, user, account, user_people, user_emails, MicrosoftTasksSourceMetadata, 
      user.hasAccount(AuthProviders.Msal, account) ? AuthProviders.Msal : AuthProviders.Microsoft,
      user.hasAccount(AuthProviders.Msal, account) ? AuthProvider.msal.getScopesForTasks() : AuthProvider.microsoft.getScopesForTasks(), 
      false, false);
  }

  /** @inheritDoc */
  public async complete(task: Task): Promise<[Task, IEntity[]]> {
    await this.taskListEnsure();

    task.completed = new Date();
    task.status = TaskStatus.completed;
    return this.update(task);
  }

  /** @inheritDoc */
  public async uncomplete(task: Task): Promise<[Task, IEntity[]]> {
    await this.taskListEnsure();

    task.completed = null;
    task.status = TaskStatus.needsAction;
    return this.update(task);
  }

  /** @inheritDoc */
  public async create(f_task_in: Task): Promise<[Task, IEntity[]]> {
    const ms_task_in = MicrosoftGraphConverter.convertTaskToTodoTask(f_task_in, false);

    try {
      const ms_task_out: TodoTask = await MicrosoftGraphTaskHelper.taskCreate(this.user, this.account, TASK_LIST_NAME, ms_task_in);
      return [MicrosoftGraphConverter.convertTaskFromTodoTask(this.user, this.metaData().taskListId, ms_task_out, f_task_in), []];
    } catch (err) {
      throw new Error(`Error creating task ${err}`);
    }
  }

  /** @inheritDoc */
  public async delete(fTaskIn: Partial<Task>): Promise<void> {
    try {
      await MicrosoftGraphTaskHelper.taskDelete(this.user, this.account, TASK_LIST_NAME, fTaskIn.id);
    } catch (err) {
      throw new Error(`Error deleting task ${err}`);
    }
  }

  /** @inheritDoc */
  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    if (!this.user.isAuthenticated(AuthLevel.Organizer)) {
      logging.warnFP(this.log_name, 'providerRead', this.user.profile, `skipping due to not authenticated or improper permissions ${this.user.permissions}`);
      return [[], []];
    }

    await this.taskListEnsure();

    logging.infoFP(this.log_name, 'providerRead', this.user.profile, 'Reading from provider');
    try {
      // Get our tasks
      const ms_tasks: TodoTask[] = await MicrosoftGraphTaskHelper.tasksPage(this.user, this.account, this.metaData(), is_history);
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'providerRead', this.user.profile, `Found the following tasks - ${ms_tasks}`);

      // Convert them to Fora tasks and return them
      const f_tasks_update: Task[] = [];
      const f_tasks_delete: Task[] = [];
      if (ms_tasks) {
        for (const ms_task of ms_tasks) {
          const f_task = await data.tasks.byId(this.user, ms_task.id);
          const t_task = MicrosoftGraphConverter.convertTaskFromTodoTask(this.user, this.metaData().taskListId, ms_task, f_task);

          if (ms_task.status === 'completed') f_tasks_delete.push(t_task);
          else f_tasks_update.push(t_task);
        }
      }
      return [f_tasks_update, f_tasks_delete];
    } catch (err) {
      logging.errorFP(this.log_name, 'providerRead', this.user.profile, 'Error loading tasks', err);
      throw err;
    }
  }

  /** @inheritDoc */
  public async reload(f_task_in: Task): Promise<Task> {
    const ms_task_in = MicrosoftGraphConverter.convertTaskToTodoTask(f_task_in, false);
    const ms_task_out: TodoTask = await MicrosoftGraphTaskHelper.taskGet(this.user, this.account, TASK_LIST_NAME, ms_task_in);
    return ms_task_out ? MicrosoftGraphConverter.convertTaskFromTodoTask(this.user, f_task_in.taskListId, ms_task_out, f_task_in) : null;
  }

  /** @inheritDoc */
  public async update(f_task_in: Task): Promise<[Task, IEntity[]]> {
    const ms_task_in = MicrosoftGraphConverter.convertTaskToTodoTask(f_task_in, false);
    const ms_task_out: TodoTask = await MicrosoftGraphTaskHelper.taskUpdate(this.user, this.account, TASK_LIST_NAME, ms_task_in);
    return [MicrosoftGraphConverter.convertTaskFromTodoTask(this.user, f_task_in.taskListId, ms_task_out, f_task_in), []];
  }

  // TODO(future) move up to AbstractSourcePlugin
  protected settings() {
    return this.user.settings.tasks;
  }

  /**
   * Ensure that our Microsoft task list exists.
   *
   * This will return the existing list or create one if necessary
   *
   * @returns {Promise<void>}
   * @private
   * @async
   */
  private async taskListEnsure(): Promise<void> {
    const list = await MicrosoftGraphTaskHelper.taskListEnsure(this.user, this.account, TASK_LIST_NAME);
    if (list) this.metaData().taskListId = list.id;
    else throw new Error('Task list not ensured. Not sure how we got here!');
  }
}
