/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { JsonObject, JsonProperty } from 'json2typescript';
import { ISourceMetadata } from './i_source_metadata';

// NOTE - json2typescript likes it better if all properties have a default value (null is ok)
@JsonObject('AbstractSourceMetadata')
export class AbstractSourceMetadata implements ISourceMetadata {

  get disabledError(): boolean {
    return this._disabledError;
  }

  set disabledError(value: boolean) {
    this._disabledError = value;
  }

  get enabled(): boolean {
    return this._enabled;
  }

  set enabled(value: boolean) {
    this._enabled = value;
  }

  get firstRunDone(): boolean {
    return this._firstRunDone;
  }

  set firstRunDone(value: boolean) {
    this._firstRunDone = value;
    if (!value) {
      this.nextPageToken = null;
      this.nextSyncToken = null;
    }
  }

  get historyDone(): boolean {
    return this._historyDone;
  }

  set historyDone(value: boolean) {
    this._historyDone = value;
    if (!value) this.historyPageToken = null;
  }

  get historyPageToken(): string {
    return this._historyPageToken;
  }

  set historyPageToken(value: string) {
    this._historyPageToken = value;
  }

  get newDone(): boolean {
    return this._newDone;
  }

  set newDone(value: boolean) {
    this._newDone = value;
  }

  get nextPageToken(): string {
    return this._nextPageToken;
  }

  set nextPageToken(value: string) {
    this._nextPageToken = value;
  }

  get nextSyncToken(): string {
    return this._nextSyncToken;
  }

  set nextSyncToken(value: string) {
    this._nextSyncToken = value;
  }
  @JsonProperty('disabledError', Boolean, true) private _disabledError = false;
  @JsonProperty('enabled', Boolean, true) private _enabled = false;
  @JsonProperty('firstRunDone', Boolean, true) private _firstRunDone = false;
  @JsonProperty('historyDone', Boolean, true) private _historyDone = false;
  @JsonProperty('historyPageToken', String, true) private _historyPageToken: string = null;
  @JsonProperty('newDone', Boolean, true) private _newDone = false;
  @JsonProperty('nextPageToken', String, true) private _nextPageToken: string = null;
  @JsonProperty('nextSyncToken', String, true) private _nextSyncToken: string = null;

  hasHistoryPageToken(): boolean {
    return !!this._historyPageToken;
  }

  hasNextPageToken(): boolean {
    return !!this.nextPageToken;
  }

  hasNextSyncToken(): boolean {
    return !!this.nextSyncToken;
  }

  restart(): void {
    this.firstRunDone = false;
    this.historyDone = false;
    this.newDone = false;
    this.nextSyncToken = null;
    this.nextPageToken = null;
    this.historyPageToken = null;
    this.disabledError = false;
  }
}
