import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import config from '../config';
import lang from '../lang';
import logging from '../utils/logging';

const LOG_NAME = 'sources.WiseController';

const Config = {
  baseUrlSandbox: 'https://api.sandbox.transferwise.tech',
  baseUrlLive: 'https://api.transferwise.com',
  rootUrl: undefined,
  apiKey: undefined,
  sandbox: undefined,
};

const query = function(config) {
  for (let k in config) {
    Config[k] = config[k];
  }
  Config.rootUrl = Config.sandbox ? Config.baseUrlSandbox : Config.baseUrlLive;

  if (!Config.apiKey) {
    throw new Error('apiKey is required in config');
  }

  return async function ({data = undefined, headers = {}, method = undefined, path, versionPrefix = 'v1'}) {
    try {
      if (!path) {
        throw new Error('path is required to perform a request');
      }
      path = `/${versionPrefix}${path}`;
      const url = `${Config.rootUrl}${path}`;
      const options = {
        method: method || 'GET',
        url,
        headers: {
          Authorization: `Bearer ${Config.apiKey}`,
          'Content-Type': 'application/json',
          'cache-control': 'no-cache',
          'Accept': '*/*',
        },
        json: true,
        data: null,
      };
      for (let k in headers) {
        options.headers[k] = headers[k];
      }
      if (data) {
        let d = {};
        for (const k in data) {
          if (data[k] !== undefined) d[k] = data[k];
        }
        options.data = d;
      }
      let res = await axios(options);
      return res;
    } catch (error) {
      if (error) {
        if (error.response) return error.response;
      }
      return error;
    }
  };
};

const transferwise = function (tw_config) {
  const request = query(tw_config);

  try {
    this.apiKey = tw_config.apiKey;
    this.sandbox = tw_config.sandbox;
    this.request = request;

    this.cancelTransfer = async function ({transferId, versionPrefix = 'v1'}) {
      const response = await request({
        method: 'PUT',
        path: `/transfers/${transferId}/cancel`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.convertCurrencies = async function ({
      borderlessAccountId,
      quoteId,
      uuid,
      versionPrefix = 'v1',
    }) {
      if (!uuid) {
        uuid = uuidv4();
      }
      const response = await request({
        data: {
          quoteId,
        },
        headers: {
          'X-idempotence-uuid': uuid,
        },
        method: 'POST',
        path: `/borderless-accounts/${borderlessAccountId}/conversions`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.createQuote = async function ({
      profile = undefined, 
      sourceCurrency, 
      targetCurrency, 
      sourceAmount = undefined,
      targetAmount = undefined,
      targetAccount = undefined,
      payOut = undefined,
      preferredPayIn = undefined,
      versionPrefix = 'v2'
    }) {
      if (config.isEnvOffline()) return lang.wise.FAKE_QUOTE(sourceAmount, targetCurrency);
      const response = await request({
        data: { profile, sourceCurrency, targetCurrency, sourceAmount, targetAmount, targetAccount, payOut, preferredPayIn },
        method: 'POST',
        path: `/quotes`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.createRecipientAccount = async function ({
      accountHolderName,
      currency = 'GBP',
      details,
      ownedByCustomer = false,
      profile,
      type = 'sort_code',
      versionPrefix = 'v1',
    }) {
      if (config.isEnvOffline()) {
        if (details && details.accountNumber === '**********') return lang.wise.ERROR;
        return lang.wise.FAKE_ACCOUNT(currency);
      }
      const response = await request({
        data: {
          accountHolderName,
          currency,
          details,
          ownedByCustomer,
          profile,
          type,
          versionPrefix,
        },
        method: 'POST',
        path: `/accounts`,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.createTransfer = async function ({
      customerTransactionId,
      details,
      quoteUuid,
      targetAccount,
      versionPrefix = 'v1',
    }) {
      if (config.isEnvOffline()) return lang.wise.FAKE_TRANSFER('USD');
      if (!customerTransactionId) {
        customerTransactionId = uuidv4();
      }
      const response = await request({
        data: {
          targetAccount,
          quoteUuid,
          customerTransactionId,
          details,
        },
        method: 'POST',
        path: `/transfers`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.deleteRecipientAccount = async function ({accountId, versionPrefix = 'v1'}) {
      const response = await request({
        method: 'DELETE',
        path: `/accounts/${accountId}`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.fundTransfer = async function ({
      profile,
      transferId,
      type = 'BALANCE',
      versionPrefix = 'v3',
    }) {
      if (config.isEnvOffline()) return lang.wise.FAKE_FUND;
      const response = await request({
        data: {
          type,
        },
        method: 'POST',
        path: `/profiles/${profile}/transfers/${transferId}/payments`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getDeliveryEstimate = async function({transferId, versionPrefix = 'v1'}) {
      const response = await request({
        method: 'GET',
        path: `/delivery-estimates/${transferId}`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getTransfers = async function ({
      profile = undefined,
      status = undefined,
      sourceCurrency = undefined,
      targetCurrency = undefined,
      createdDateStart = undefined,
      createdDateEnd = undefined,
      limit = undefined,
      offset = undefined,
      versionPrefix = 'v1',
    }) {
      if (config.isEnvOffline()) return [lang.wise.FAKE_TRANSFER(targetCurrency ? targetCurrency : 'USD')];
      const params = [];
      if (offset) params.push(`offset=${offset}`);
      if (limit) params.push(`limit=${limit}`);
      if (profile) params.push(`profile=${profile}`);
      if (status) params.push(`status=${status}`);
      if (sourceCurrency) params.push(`sourceCurrency=${sourceCurrency}`);
      if (targetCurrency) params.push(`targetCurrency=${targetCurrency}`);
      if (createdDateStart) params.push(`createdDateStart=${createdDateStart.toISOString()}`);
      if (createdDateEnd) params.push(`createdDateEnd=${createdDateEnd.toISOString()}`);

      const response = await request({
        method: 'GET',
        path: `/transfers/?${params.join('&')}`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getAccountRequirements = async function ({source, target, sourceAmount = 1, versionPrefix = 'v1'}) {
      if (config.isEnvOffline()) return lang.wise.FAKE_ACCOUNT_REQUIREMENTS;
      const response = await request({
        method: 'GET',
        path: `/account-requirements?source=${source}&target=${target}&sourceAmount=${sourceAmount}`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getAccountRequirementsForQuote = async function ({quoteId, versionPrefix = 'v1', type = undefined, details = undefined}) {
      if (config.isEnvOffline()) {
        if (details && details.accountNumber === '**********') return lang.wise.ERROR;
        return lang.wise.FAKE_ACCOUNT_REQUIREMENTS;
      }
      const response = await request({
        method: type && details ? 'POST' : 'GET',
        path: `/quotes/${quoteId}/account-requirements`,
        versionPrefix,
        data: type && details ? { type, details } : undefined,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getBorderlessAccounts = async function ({profileId, versionPrefix = 'v1'}) {
      if (config.isEnvOffline()) return lang.wise.FAKE_BALANCE;
      const response = await request({
        method: 'GET',
        path: `/borderless-accounts?profileId=${profileId}`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getTransferRequirements = async function ({targetAccount, quoteUuid, customerTransactionId, details, versionPrefix = 'v1'}) {
      if (config.isEnvOffline()) return lang.wise.FAKE_TRANSFER_REQUIREMENTS;
      const response = await request({
        method: 'POST',
        path: '/transfer-requirements',
        versionPrefix,
        data: { targetAccount, quoteUuid, customerTransactionId, details },
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getQuote = async function ({quoteId, versionPrefix = 'v2'}) {
      if (config.isEnvOffline()) return lang.wise.FAKE_QUOTE(0, 'USD');
      const response = await request({
        method: 'GET',
        path: `/quotes/${quoteId}`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getProfiles = async function ({versionPrefix = 'v1'} = {}) {
      if (config.isEnvOffline()) return [lang.wise.FAKE_PROFILE];
      const response = await request({method: 'GET', path: `/profiles`, versionPrefix});
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getProfileActivities = async function ({
      limit = 50,
      profileId,
      since,
      status,
      until,
      versionPrefix = 'v1',
    }) {
      let path = `/profiles/${profileId}/activities/?size=${limit}`;
      if (status) {
        path = `${path}&status=${status}`;
      }
      if (since) {
        if (since.length === 10) {
          since = `${since}T00:00:00Z`;
        }
        path = `${path}&since=${since}`;
      }
      if (until) {
        if (until.length === 10) {
          until = `${until}T00:00:00Z`;
        }
        path = `${path}&until=${until}`;
      }
      const response = await request({
        method: 'GET',
        path,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getProfilePermissions = async function ({
      profileId,
      versionPrefix = 'v1',
    }) {
      const response = await request({
        method: 'GET',
        path: `/permissions/${profileId}`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.getRecipientAccounts = async function ({
      currency = undefined,
      profile,
      versionPrefix = 'v2',
    }) {
      let path = `/accounts?profile=${profile}`;
      if (currency) {
        path = `${path}&currency=${currency}`;
      }
      const response = await request({
        method: 'GET',
        path,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    //status can be processing, funds_converted, outgoing_payment_sent, bounced_back, funds_refunded
    this.testTransferStatus = async function({transferId, status, versionPrefix = 'v1'}) {
      const response = await request({
        method: 'GET',
        path: `/simulation/transfers/${transferId}/${status}`,
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };

    this.testVerify = async function ({versionPrefix = 'v1'}) {
      const response = await request({
        method: 'GET',
        path: '/simulation/verify-profile',
        versionPrefix,
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };
    
    this.testTopUp = async function ({profileId, balanceId, currency, amount, versionPrefix = 'v1'}) {
      const response = await request({
        method: 'POST',
        path: '/simulation/balance/topup',
        versionPrefix,
        data: { profileId, balanceId, currency, amount },
      });
      if (response) {
        if (response.data) return response.data;
        if (response.message) return response.message;
      }
      return null;
    };
  } catch (err) {
    logging.errorF(LOG_NAME, 'wise', 'Initialiation error', err);
  }
};

let wise;

config.onLoad('wise', async (silent: boolean) => {
  if (config.isEnvOffline()) wise = new transferwise({apiKey: 'offline_key', sandbox: true });
  else wise = new transferwise({apiKey: config.get('WISE_API_KEY'), sandbox: !config.isEnvProduction()});
});

function getWise() { return wise }

export default getWise;
