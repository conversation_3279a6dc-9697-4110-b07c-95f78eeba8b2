/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client } from '@microsoft/microsoft-graph-client';
import 'isomorphic-fetch';
import { AuthProvider } from '../auth/auth_provider';
import { IEntity } from '../types/items';
import { AuthProviders } from '../types/shared';
import { AbstractSourcePlugin } from './a_source_plugin';
import { ISourceMetadata } from './i_source_metadata';

export abstract class AbstractMicrosoftSourcePlugin<T extends IEntity, M extends ISourceMetadata> extends AbstractSourcePlugin<T, M> {
  private graph_client: Client;

  /**
   * Convenience method to lazy create our graph client
   */
  async getGraphClient(): Promise<Client> {
    if (!this.graph_client && this.user && this.account) {
      const provider = this.user.getAccountType(this.account);
      if (provider === AuthProviders.Microsoft) this.graph_client = await AuthProvider.microsoft.getGraphClient(this.user, this.account);
      else if (provider === AuthProviders.Msal) this.graph_client = await AuthProvider.msal.getGraphClient(this.user, this.account);
    }
    return this.graph_client;
  }
}
