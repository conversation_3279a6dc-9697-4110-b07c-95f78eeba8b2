/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import _ from 'lodash';
import { setTimeout } from "timers/promises";
import util from 'util';
import { v4 as uuid } from 'uuid';

import config from '../config';

import { AuthProvider } from '../auth/auth_provider';
import DataCache from '../session/data_cache';
import ForaUser from '../session/user';

import { CourseType, ImportLog, User } from '../types/globals';
import { Group } from '../types/group';
import { Document, GlobalType, IEntity, ImportType, Person, Tag } from '../types/items';
import { AuthProviders, EntityType, JobTags, TagType, Uid } from '../types/shared';

import { stringFromBody } from '../utils/format';
import { flatten, hash, saveOneTypeValue, slimEntity } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '../errors/error_handler';

import data from '../data';
import { internalLearn } from '../routes/learn';

import { ICoursePlugin } from './i_course_plugin';
import { IImportPlugin } from './i_import_plugin';
import { ISourceMetadata } from './i_source_metadata';
import { ISourcePlugin } from './i_source_plugin';

const DEBUG = (require('debug') as any)('fora:sources');
const LOG_NAME = 'data.sources.SourceController';

export class SourceController {
  /**
   * Check all the plugins to make sure they can run with the current metadata for the user
   *
   * @param user - Fora user running for
   */
  public static checkPlugins(user: ForaUser): void {
    const pluginMap = data.plugins.sourcePlugins();
    if (logging.isDebug(user.profile)) DEBUG('checkPlugins: plugin set = %o', pluginMap);

    for (const pluginConstructor of data.plugins.sourcePlugins().values()) {
      // Construct our plugin to check for metadata
      for (const account of user.getAccountIds(pluginConstructor.providers)) {
        const plugin: ISourcePlugin<IEntity, ISourceMetadata> = new pluginConstructor(user, account);
        if (logging.isDebug(user.profile)) DEBUG('checkPlugins: checked %o', plugin.typeFor());
      }
    }
  }

  /**
   * Enable any plugins that have been disabled due to error
   *
   * @param user - Fora user running for
   */
  public static enableErrorDisabledPlugins(user: ForaUser): void {
    // We don't run for un-authenticated users
    if (!user.isAuthenticated()) return;

    for (const pluginConstructor of data.plugins.sourcePlugins().values()) {
      for (const account of user.getAccountIds(pluginConstructor.providers)) {
        const plugin: ISourcePlugin<IEntity, ISourceMetadata> = new pluginConstructor(user, account);
        if (plugin.isDisabledError()) plugin.disabledError(false);
      }
    }
  }

  public static firstRunDone(user: ForaUser, type?: EntityType ): boolean {
    for (const pluginConstructor of data.plugins.sourcePlugins().values()) {
      for (const account of user.getAccountIds(pluginConstructor.providers)) {
        const plugin: ISourcePlugin<IEntity, ISourceMetadata> = new pluginConstructor(user, account);
        if ((!type || plugin.typeFor() === type) && plugin.metaData().enabled && !plugin.metaData().firstRunDone) return false;
      }
    }
    return user.last_refresh && new Date(user.last_refresh).getTime() > 0;
  }

  /**
   * Run first run operations on all enabled plugins
   *
   * @param user - Fora user running for
   * @async
   */
  public static async firstRun(user: ForaUser, self: Partial<Person> = null, do_search = false, project: Uid = null): Promise<EntityType[]> {
    // We don't run for un-authenticated users
    if (!user.isAuthenticated() || user.isGuestAccount()) return;

    user.refreshing = true;
    await data.users.save(user, true, false, false).catch(err => logging.errorFP(LOG_NAME, 'firstRun', user.profile, 'Error saving user before firstRun', err));

    self = self && self.id ? await data.people.byId(user, self.id) : null;
    if (!self) {
      self = await data.people.getUserPerson(user, user.profile);
      if (self) await data.people.save(user, self as Person);
    }

    const {people, emails} = await SourceController.cachePeople(user);

    const pluginPromises: Promise<[IEntity[], IEntity[]]>[] = [];
    for (const pluginConstructor of data.plugins.sourcePlugins().values()) {
      for (const account of user.getAccountIds(pluginConstructor.providers)) {
        // Construct our plugin
        const plugin: ISourcePlugin<IEntity, ISourceMetadata> = new pluginConstructor(user, account, people, emails);

        // Only run plugins that are enabled
        if (plugin.isEnabled()) {
          if (plugin.isFirstRun()) {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'firstRun', user.profile, `First run for ${plugin.typeFor()}`);

            // Execute the first run logic for this plugin. May be a NO-OP
            const pfr = plugin.firstRun();
            pluginPromises.push(
              pfr.catch(
                async (err): Promise<[IEntity[], IEntity[]]> => {
                  logging.errorFP(LOG_NAME, 'firstRun', user.profile, `Error during first run for ${plugin.typeFor()}`, err);
                  plugin.disabledError(true);
                  await ErrorHandler.handleError(user, user.provider, err, data.users.message, data.users.logoutUser);

                  return [[], []];
                },
              ),
            );
          } else {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'firstRun', user.profile, `Skipping first run for ${plugin.typeFor()}`);
          }
        }
      }
    }

    // Wait for all the promises to complete
    logging.infoFP(LOG_NAME, 'firstRun', user.profile, 'Waiting for sources');
    const entitySets = await Promise.all(pluginPromises);

    // Process the results into a set of updates/deletes for the storage plugin
    let updates = [];
    let deletes = [];
    entitySets.forEach(entitySet => {
      updates = updates.concat(entitySet[0]);
      deletes = deletes.concat(entitySet[1]);
    });

    logging.infoFP(LOG_NAME, 'firstRun', user.profile, `Found ${updates.length} updates and ${deletes.length} deletes`);
    
    const update_people = updates.filter(u => u.type === EntityType.Person);
    const update_users = updates.filter(u => u.type === EntityType.User);
    updates = updates.filter(u => ![EntityType.Person, EntityType.User].includes(u.type));

    // Persist all the changes to storage (whatever that might be)
    const refresh_set = await SourceController.persistUpdatesAndDeletes(user, self, updates, deletes, true);

    const global_people = await SourceController.loadGlobalPeople(user.profile, [...Object.values(people), ...update_people]);

    // Resolve people
    logging.infoFP(LOG_NAME, 'firstRun', user.profile, 'Resolving people');
    const prefresh_set = await SourceController.resolvePeople(user, self, people, emails, update_people, global_people, true);

    // Resolve users
    logging.infoFP(LOG_NAME, 'firstRun', user.profile, 'Resolving users');
    const urefresh_set = await SourceController.resolveUsers(user, update_users, people, emails, global_people);

    if (urefresh_set.length) {
      const user_export = await data.people.export();

      if (user_export) {
        let source;
        while(!source) {
          source = await data.people.checkExport(null, user_export);
          await setTimeout(60000);
        }
        await data.people.finishExport(null, source);
      } else logging.warnF(LOG_NAME, 'reloadFunction', 'Users not exported');
    }


    // Learn anyone new
    /*for(const person of update_people) {
      logging.infoFP(LOG_NAME, 'firstRun', user.profile, `::${person.id} Learning person`);
      if (!person.self || !person.learned || !person.learned.length || person.learned.reduce((a,b) => a > b ? a : b, new Date(0)).getTime() === 0) {
        internalLearn({profile: user.profile, person: person.id, no_export: true});
      }
    }*/
   
    // handled by the update thread
    //await internalLearn({profile: user.profile, max_per_profile: update_people.length, no_export: true});
    //await internalLearn({profile: user.profile, person: self.id, force_local: true, no_export: true});

    const merged = _.uniq(refresh_set.concat(prefresh_set).concat(urefresh_set));

    // force people refresh
    if (!merged.includes(EntityType.Person)) merged.push(EntityType.Person);

    user.updatePermissions();
    return merged;
  }

  public static async loadCourse(doc: Document) {
    const course_type = doc.props ? doc.props['course_type'] as CourseType : null;
    if (course_type) {
      for(const pluginConstructor of data.plugins.coursePlugins().values()) {
        const plugin: ICoursePlugin = new pluginConstructor();
        if (plugin.courseTypes.includes(course_type)) {
          const course_data = stringFromBody(doc.body);
          const course = await plugin.processCourse(course_data);

          if (course) {
            await data.courses.save(course);
            logging.infoF(LOG_NAME, 'loadCourse', `Loaded ${course_type} course ${course.id} from ${doc.id}`);
          } else logging.warnF(LOG_NAME, 'loadCourse', `No course data for ${course_type} ${doc.id}`);

          return;
        }
      }

      logging.warnF(LOG_NAME, 'loadCourse', `Unknown course type for ${doc.id}: ${course_type}`);
    } else logging.warnF(LOG_NAME, 'loadCourse', `Unknown course type for ${doc.id}`);
  }

  /**
   * Run plugins to import people from files
   *
   * @param user - Fora user to import for
   * @param file_id - the file to import
   * @param import_type - the type of import confirmed by the user
   * @async
   */
  public static async importData(user: ForaUser, self: Partial<Person>, doc: Document, import_type: ImportType): Promise<[number, EntityType[]]> {
    if (!user.isAuthenticated()) return [0, []];

    const pluginPromises: Promise<void>[] = [];

    let imports: Person[] = [];
    const user_groups: {[key:string]: Uid[]} = {};

    self = self && self.id ? await data.people.byId(user, self.id) : null;
    if (!self) {
      self = await data.people.getUserPerson(user, user.profile);
      if (self) await data.people.save(user, self as Person);
    }

    for (const pluginConstructor of data.plugins.importPlugins().values()) {
      // Construct our plugin
      const plugin: IImportPlugin = new pluginConstructor(user, self, doc, import_type);

      if (plugin.checkImport()) {
        // Kick off the promise that pulls all the new data
        logging.infoFP(LOG_NAME, 'importData', user.profile, `Calling importDataFromPlugin on ${doc.id} type ${import_type}`);
        try {
          pluginPromises.push(SourceController.importDataFromPlugin(user, imports, user_groups, plugin));
        } catch(err) {
          logging.errorFP(LOG_NAME, 'importData', user.profile, `Error importing data from ${doc.id} type ${import_type}`, err);
        }
      }
    }

    // Wait for all the promises to complete
    await Promise.all(pluginPromises);

    logging.infoFP(LOG_NAME, 'importData', user.profile, `Found ${imports.length} people in ${doc.id} type ${import_type}`);

    // add group and import tags
    const now = new Date();
    let tags: Tag[] = [new Tag(TagType.import_id, doc.id, 0, now)];

    await user.loadGroupsFromDatastore(); 
    if (user.loaded_groups) {
      for (const g_id in user.loaded_groups) {
        const group: Group = user.loaded_groups[g_id];
        if (group.search_mandatory_tags) {
          for (const tag of group.search_mandatory_tags) {
            tags.push(new Tag(tag.type as TagType, tag.value, tag.index));
          }
          // tags = tags.concat(group.search_mandatory_tags);
        }
      }
    }

    //save import id
    for (const person of imports) {
      if (!person.tags) person.tags = [];
      for (const tag of tags) {
        const new_tags = saveOneTypeValue(person.tags, tag); 
        if (!new_tags) {
          logging.warnFP(LOG_NAME, 'importData', user.profile, `Failed to add tag ${tag} to ${person.id}`);
        }
      }
    }

    // await Learn.learnPeople(user, imports);

    let {people, emails} = await SourceController.cachePeople(user, parsers.findEmail(flatten(imports.map(i => i.comms))));

    let import_global_people = await SourceController.loadGlobalPeople(user.profile, [...Object.values(people), ...imports]);
    
    // save people
    const refresh_set = await SourceController.resolvePeople(user, self, people, emails, imports, import_global_people);

    let learn
    const learn_ids: Uid[] = [];

    const import_logs = imports.map((p, i) => {
      if (i % 500 === 0) {
        learn = uuid();
        learn_ids.push(learn);
      }

      return new ImportLog({
        id: uuid(),
        user: user.id,
        person: p.id,
        import: doc.id,
        learn, 
        groups: user_groups[p.id],
      });
    });

    logging.warnFP(LOG_NAME, 'importData', user.profile, `Writing ${import_logs.length} import logs for ${learn_ids.length} learn threads`);
    await data.imports.importLog(import_logs);

    await Promise.all(learn_ids.map(async learn_id => {
      return internalLearn({learn_id});
    }));

    return [imports.length, refresh_set];
  }
  
  public static async importUsers(user: ForaUser, doc_id: Uid, import_type: ImportType): Promise<[number, EntityType[]]> {
    // get imported people based on TagType,import_id = doc.id
    let imports = (await data.people.bigQueryByTerms(user, [doc_id]));
    const import_logs = (await data.imports.importLogs(doc_id)).filter(log => log.complete);

    logging.infoFP(LOG_NAME, 'importUsers', user.profile, `Processing ${import_logs.length} imports`);

    // TODO load user_groups
    const user_groups: {[key:string]: Uid[]} = {};

    import_logs.forEach(import_log => {
      user_groups[import_log.person] = import_log.groups;
    });

    logging.infoFP(LOG_NAME, 'importUsers', user.profile, `Found ${Object.keys(user_groups).length} user groups`);
      
    let {people, emails} = await SourceController.cachePeople(user, parsers.findEmail(flatten(imports.map(i => i.comms))));
    let import_global_people = await SourceController.loadGlobalPeople(user.profile, [...Object.values(people), ...imports]);

    // merge imported people in
    for(const import_person of imports) {
      const import_emails = parsers.findEmail(import_person.comms);
      if (import_emails) {
        for (const email of import_emails) {
          const people_ids = emails[email]; 
          if (people_ids) {
            for(const id of people_ids) {
              const match_person = people[id];
              if (match_person) peopleUtils.mergePeople(match_person, import_person, true);
            }
          }

          const global_person = import_global_people[email];
          if (global_person) peopleUtils.mergePeople(global_person, import_person, true);
        }
      }
    }

    const all_groups = await data.groups.byIds(_.uniq(flatten(Object.values(user_groups))));

    // create or map users if appropriate
    const update_users: User[] = [];
    for(const id in user_groups) {
      const groups = user_groups[id];
      const group_set = groups ? all_groups.filter(g => groups.includes(g.id)) : [];
      const domains = flatten(group_set.map(g => g.email_domain).filter(d => d));

      const person = imports.find(p => p.id === id);
      let global_users;
      let emails;
      if (person) {
        emails = parsers.findEmail(person.comms);
        global_users = await data.users.globalByEmail(emails);
      } else logging.warnFP(LOG_NAME, 'importUsers', user.profile, `Unknown person in user_groups ${id}`);

      if (global_users && global_users.length === 1) {
        logging.warnFP(LOG_NAME, 'importUsers', user.profile, `Updating existing user ${global_users[0].id} for user import ${id}`);
        update_users.push(global_users[0]);
        if (!global_users[0].groups) global_users[0].groups = user_groups[id];
        else global_users[0].groups = _.uniq([...global_users[0].groups, ...user_groups[id]]);
      } else if(emails && emails.length) {
        let create_new = true;

        let profile = id.split('/')[1];
        let email = emails.find(e => {
          const gmatch = domains.find(d => e.endsWith(d));
          if (gmatch) return true;
          return false;
        });

        if (!email) email = emails[0];

        if (global_users && global_users.length > 1) {
          logging.warnFP(LOG_NAME, 'importUsers', user.profile, `Multiple matches for  user import ${id}: ${JSON.stringify(global_users.map(u => u.id))}`);
          // find group match or create new one
          const domain_user = global_users.find(u => {
            const gmatch = domains.find(d => u.email.endsWith(d));
            if (gmatch) return true;
            return false;
          });

          if (domain_user) {
            logging.warnFP(LOG_NAME, 'importUsers', user.profile, `Updating best match existing user ${domain_user.id} for user import ${id}`);
            update_users.push(domain_user);
            create_new = false;
          }
        }

        if (create_new) {
          if (email) {
            profile = `e_${hash(email).toLowerCase()}`;
            logging.warnFP(LOG_NAME, 'importUsers', user.profile, `Creating new user ${profile} for import person ${id}`);
            update_users.push(new User({
              id: profile,
              email,
              profile,
              name: person.displayName.split(' ')[0],
              groups,
              auth_ids: [`${AuthProviders.Email}_${profile}`],
            }));
          } else {
            logging.warnFP(LOG_NAME, 'importUsers', user.profile, `No email for user import ${id}`);
          }
        }
      } else logging.warnFP(LOG_NAME, `importUsers`, user.profile, `No global user e-mail for person in user_group ${id}`); 
   }

    const urefresh_set = await SourceController.resolveUsers(user, update_users, people, emails, import_global_people);

    const import_count = imports.length;

    import_global_people = {};
    people = {};
    imports = [];

    if (urefresh_set.length) {
      const user_export = await data.people.export();

      if (user_export) {
        let source;
        while(!source) {
          source = await data.people.checkExport(null, user_export);
          await setTimeout(60000);
        }
        await data.people.finishExport(null, source);
      } else logging.warnF(LOG_NAME, 'reloadFunction', 'Users not exported');
    }

    logging.infoFP(LOG_NAME, 'importUsers', user.profile, `Done importing ${doc_id} type ${import_type} cleaning up`);

    // cleanup file
    await data.imports.delete(user, new Document({file_id: doc_id})).catch(err => logging.errorFP(LOG_NAME, 'importUsers', user.profile, `Error deleting import ${doc_id}`, err));

    logging.infoFP(LOG_NAME, 'importUsers', user.profile, `Finished import plugins for ${doc_id} type ${import_type}`);

    return [import_count, urefresh_set];
  }

  public static importTypeFromZipData(zip_data: any[]): ImportType {
    for (const pluginConstructor of data.plugins.importPlugins().values()) {
      const plugin: IImportPlugin = new pluginConstructor();
      const import_type: ImportType = plugin.checkZipType(zip_data);
      if (import_type) return import_type;
    }
  }

  /**
   * Returns true if it's the first run for all plugins, false if any have run
   *
   * @param user - Fora user to check for
   */
  public static isFirstRun(user: ForaUser): boolean {
    // We don't run for un-authenticated users
    if (!user.isAuthenticated() || user.isGuestAccount()) return false;

    for (const pluginConstructor of data.plugins.sourcePlugins().values()) {
      // Construct our plugin
      for (const account of user.getAccountIds(pluginConstructor.providers)) {
        const plugin: ISourcePlugin<IEntity, ISourceMetadata> = new pluginConstructor(user, account, {}, {});
        if (!plugin.isFirstRun()) return false;
      }
    }

    return true;
  }

  /**
   * Run refresh operations on all enabled plugins
   *
   * @param user - Fora user to run refresh for
   * @async
   */
  public static async refresh(user: ForaUser, self: Partial<Person>, reset_types: EntityType[] = []): Promise<EntityType[]> {
    // We don't run for un-authenticated users
    if (!user.isAuthenticated() || user.isGuestAccount()) return;

    self = self && self.id ? await data.people.byId(user, self.id) : null;
    if (!self) {
      self = await data.people.getUserPerson(user, user.profile);
      if (self) await data.people.save(user, self as Person);
    }

    const {people, emails} = await SourceController.cachePeople(user);

    let global_people = await SourceController.loadGlobalPeople(user.profile, [...Object.values(people)]);

    // make sure people data is clean
    logging.infoFP(LOG_NAME, 'refresh', user.profile, 'Resolving people');
    await SourceController.resolvePeople(user, self, people, emails, null, global_people);
    global_people = {};

    // clean up network people
    const net_people = await data.plugins.storagePlugin().networkPeople(user);
    await SourceController.resolvePeople(user, self, people, emails, net_people, global_people);

    const pluginPromises: Promise<[IEntity[], Partial<IEntity>[]]>[] = [];
    for (const pluginConstructor of data.plugins.sourcePlugins().values()) {
      // Construct our plugin
      for (const account of user.getAccountIds(pluginConstructor.providers)) {
        const plugin: ISourcePlugin<IEntity, ISourceMetadata> = new pluginConstructor(user, account, people, emails);

        // Only run plugins that are enabled
        if (plugin.isEnabled()) {
          if (reset_types && reset_types.includes(plugin.typeFor())) plugin.reset();

          // in case it was missed, e.g. for a second account
          let frp;
          if (!plugin.metaData().firstRunDone) {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'refresh', user.profile, `First run for ${plugin.typeFor()}`);

            // Execute the first run logic for this plugin. May be a NO-OP
            const pfr = plugin.firstRun();
            frp = pfr.catch(
              async (err): Promise<[IEntity[], IEntity[]]> => {
                logging.errorFP(LOG_NAME, 'refresh', user.profile, `Error during first run for ${plugin.typeFor()}`, err);
                plugin.disabledError(true);
                await ErrorHandler.handleError(user, user.provider, err, data.users.message, data.users.logoutUser);

                return [[], []];
              }
            );
            pluginPromises.push(frp);
          }

          // Kick off the promise that pulls all the new data
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'refresh', user.profile, `calling loadNewDataFromPlugin on ${plugin.typeFor()}`);
          pluginPromises.push(
            frp ? frp.then(() => SourceController.loadNewDataFromPlugin(user, self, plugin)) :
            SourceController.loadNewDataFromPlugin(user, self, plugin)
            );

          // Kick off the promise that pulls all the data from history
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'refresh', user.profile, `calling loadHistoricalDataFromPlugin on ${plugin.typeFor()}`);
          pluginPromises.push(
            frp ? frp.then(() => SourceController.loadHistoricalDataFromPlugin(user, self, plugin)) :
            SourceController.loadHistoricalDataFromPlugin(user, self, plugin)
          );

          // Kick off the promise that deletes old data
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'refresh', user.profile, `calling removeOldDataFromPlugin on ${plugin.typeFor()}`);
          pluginPromises.push(
            frp ? frp.then(() => SourceController.removeOldDataFromPlugin(user, plugin)) :
            SourceController.removeOldDataFromPlugin(user, plugin)
          );
        }
      }
    }

    // Wait for all the promises to complete
    logging.infoFP(LOG_NAME, 'refresh', user.profile, `Waiting on ${pluginPromises.length} sources`);
    // await Promise.all(pluginPromises);
    const entitySets = await Promise.all(pluginPromises);

    // Process the results into a set of updates/deletes for the storage plugin
    let updates = [];
    let deletes = [];
    entitySets.forEach(entitySet => {
      if (entitySet) {
        updates = updates.concat(entitySet[0]);
        deletes = deletes.concat(entitySet[1]);
      }
    });

    logging.infoFP(LOG_NAME, 'refresh', user.profile, `Found ${updates.length} updates and ${deletes.length} deletes`);

    updates = updates.filter(u => ![EntityType.Person, GlobalType.User].includes(u.type));

    // Persist all the changes to storage (whatever that might be)
    const refresh_set = await SourceController.persistUpdatesAndDeletes(user, self, updates, deletes);

    let update_people = updates.filter(u => u.type === EntityType.Person);
    let update_users = updates.filter(u => u.type === GlobalType.User);

    updates = [];
    deletes = [];

    let update_global_people = await SourceController.loadGlobalPeople(user.profile, [...Object.values(people), ...update_people]);

    // Resolve people
    logging.infoFP(LOG_NAME, 'refresh', user.profile, 'Cleaning up');
    const prefresh_set = await SourceController.resolvePeople(user, self, people, emails, update_people, update_global_people);

    // Resolve users
    logging.infoFP(LOG_NAME, 'refresh', user.profile, 'Resolving users');
    const urefresh_set = await SourceController.resolveUsers(user, update_users, people, emails, update_global_people);

    update_global_people = {};
    update_people = []
    update_users = [];

    if (urefresh_set.length) {
      const user_export = await data.people.export();

      if (user_export) {
        let source;
        while(!source) {
          source = await data.people.checkExport(null, user_export);
          await setTimeout(60000);
        }
        await data.people.finishExport(null, source);
      } else logging.warnF(LOG_NAME, 'reloadFunction', 'Users not exported');

    }

    // Learn anyone new
    for(const person of update_people) {
      logging.infoFP(LOG_NAME, 'refresh', user.profile, `::${person.id} Learning person`);
      if (!person.self || !person.learned || !person.learned.length || person.learned.reduce((a,b) => a > b ? a : b, new Date(0)).getTime() === 0) {
        internalLearn({profile: user.profile, person: person.id, no_export: true});
      }
    }

    // force people refresh
    const merged = _.uniq(refresh_set.concat(prefresh_set).concat(urefresh_set));

    try {
      await data.plugins.bigQueryPlugin().checkSchema(user.profile);
    } catch(e) {
      logging.warnFP(LOG_NAME, 'refresh', user.profile, `Schema check failed, forcing re-export`, e);
      if (!merged.includes(EntityType.Person)) merged.push(EntityType.Person);
    }

    user.updatePermissions();

    return merged;
  }

  public static async finishRefresh(user: ForaUser, update_type: string, refresh_set: EntityType[] = []): Promise<void> {
    // Set all the proper flags/etc within user
    user.last_refresh = new Date();
    user.full_refresh = [];
    user.refreshing = false;
    user.exported = true;
 
    // Persist the user only after all the plugins are done - see issue #424
    await SourceController.persistUser(user);

    logging.infoFP(LOG_NAME, update_type, user.profile, 'Refreshing caches');
    const data_cache = new DataCache(user, null, user.loaded_groups ? Object.values(user.loaded_groups) : [], `update_${user.profile}`);
    try {
      data_cache.flushCaches(user.refreshed);
      await data_cache.cleanCache();
      await data_cache.loadCache(refresh_set, null, new Error().stack, true);
      await data_cache.saveCache();
    } catch (err) {
      logging.errorFP(LOG_NAME, update_type, user.profile, 'Error updating cache', err);
    }

    // corner case - handled an account that was deleted while we were loading
    const user_exists = await data.users.globalById(user.profile); 
    if (!user_exists) {
      logging.warnFP(LOG_NAME, update_type, user.profile, 'User deleted during update');
      await data.users.deleteAccount(user.profile, user.provider);
    }

    logging.infoFP(LOG_NAME, update_type, user.profile, 'Refresh complete');
  }

  private static async cachePeople(user: ForaUser, load_comms?: string[]): Promise<{ people: {[key: string]: Person}, emails: {[key: string]: Uid[]}}> {
    const cache_people = load_comms ? await data.people.bigQueryByEmails(user, load_comms) :
      await data.plugins.storagePlugin().people(user);
    const people: {[key: string]: Person} = {};
    const emails: {[key: string]: Uid[]} = {};
    // DEBUG(`${util.format(people)}`);
    // if (people) {
      logging.infoFP(LOG_NAME, 'cachePeople', user.profile, `Caching people`);
      for (const person of cache_people) {
        if (person.id) {
          if (person.comms.includes(user.email)) person.self = true;
          people[person.id] = new Person(person);
          const email = parsers.findEmail(person.comms);
          if (email) {
            for (const e of email) {
              if (emails[e]) emails[e].push(person.id);
              else emails[e] = [person.id];
            }
          }
        } else logging.warnFP(LOG_NAME, 'cachePeople', user.profile, `Cannot cache person without id ${JSON.stringify(slimEntity(person))}`);
      }
      logging.infoFP(LOG_NAME, 'cachePeople', user.profile, `Cached ${cache_people.length} people`);
    //} else logging.infoFP(LOG_NAME, 'cachePeople', user.profile, 'No people to cache');

    return { people, emails };
  }

  private static async loadGlobalPeople(profile: Uid, people: Person[]): Promise<{[key:string]: Person}> {
    // update global users
    const emails = parsers.findEmail(flatten(people.map(p => p.comms).filter(c => c)));

    if (!emails || !emails.length) return {};

    logging.infoFP(LOG_NAME, 'loadGlobalPeople', profile, `Loading global people for ${emails ? emails.length : 0} emails`);

    let globals, vanities;

    await Promise.all([
      data.users.globalByEmail(emails).then(g => globals = g),
      data.users.vanityPeopleByEmail(emails).then(v => vanities = v),
    ]);

    const gcv: {[key:string]: string} = {};
    if (globals) globals.forEach(g => gcv[g.vanity] = g.email);

    const global_people: {[key:string]: Person} = {};
    if (vanities) vanities.forEach(v => {
      if (gcv[v.vanity]) global_people[gcv[v.vanity]] = v;
      else if(v.comms) {
        const vemails = parsers.findEmail(v.comms);
        if (vemails && vemails.length) {
          for (const ve of vemails) {
            if (!global_people[ve]) {
              global_people[ve] = v;
              break;
            }
          }
        }
      }
    });

    logging.infoFP(LOG_NAME, 'loadGlobalPeople', profile, `Loaded ${Object.keys(global_people).length} global people`);

    return global_people;
  }

  static async resolveUsers(user: ForaUser, user_people: User[], people: {[key: string]: Person}, emails: {[key: string]: Uid[]}, global_people: {[key:string]: Person}): Promise<EntityType[]> {
    logging.infoFP(LOG_NAME, 'resolveUsers', user ? user.profile : undefined, `Resolving ${user_people.length} users`);
    const existing_users_id = await data.users.globalByIds(user_people.filter(u => u && u.id).map(u => u.id));
    const existing_users_email = await data.users.globalByEmail(user_people.filter(u => u && u.email).map(u => u.email));

    const existing_users = _.uniqBy([...existing_users_id ? existing_users_id : [], ...existing_users_email  ? existing_users_email : []], 'id');

    const existing_ids = existing_users.filter(u => u && u.id).map(u => u.id);
    const existing_emails = existing_users.filter(u => u && u.email).map(u => u.email);

    // extract users that don't match id or email of existing
    const new_users = user_people.filter(u => u && (!u.id || !existing_ids.includes(u.id)) && (!u.email || !existing_emails.includes(u.email)));

    const user_id_map: {[key:string]: User} = {};
    const user_email_map: {[key:string]: User} = {};
    for (const u of user_people) {
      if (u.id) user_id_map[u.id] = u;
      if (u.email) user_email_map[u.email] = u;
    }

    const update_users: User[] = [];
    const groups = await data.groups.byIds(_.uniq(flatten(user_people.map(u => u.groups)).filter(g =>g)));

    for (const existing of existing_users) {
      let update = false;

      const new_user_id = existing.id ? user_id_map[existing.id] : null;
      const new_user_email = existing.email ? user_email_map[existing.email] : null;
      if(new_user_id) {
        if (new_user_email && new_user_email.groups && new_user_email.groups.length) {
          if (!existing.groups || !existing.groups.length) {
            existing.groups = new_user_id.groups;
            update = true;
          } else {
            for (const group of new_user_email.groups) {
              if (!existing.groups.includes(group)) {
                existing.groups.push(group);
                update = true;
              }
            }
          }
        }

        // check email, name, group
        if(existing.email !== new_user_id.email) {
          // map new email
          await data.users.mapUserEmail({id: existing.id}, new_user_id.email);
        }

        if(existing.name !== new_user_id.name) {
          existing.name = new_user_id.name;
          update = true;
        }

        if (new_user_id.groups && new_user_id.groups.length) {
          if (!existing.groups || !existing.groups.length) {
            existing.groups = new_user_id.groups;
            update = true;
          } else {
            for (const group of new_user_id.groups) {
              if (!existing.groups.includes(group)) {
                existing.groups.push(group);
                update = true;
              }
            }
          }
        }
      }
      
      if (new_user_email && (!new_user_id || new_user_email.id !== new_user_id.id)) {
        // new user with a matching email but id doesn't exist
        if (new_user_email.auth_ids) {
          if (!existing.auth_ids) {
            existing.auth_ids = new_user_email.auth_ids;
            update = true;
          } else {
            for (const auth_id of new_user_email.auth_ids) {
              if (!existing.auth_ids.includes(auth_id)) {
                existing.auth_ids.push(auth_id);
                update = true;
              }
            }
          }
        }

        if (new_user_email.groups && new_user_email.groups.length) {
          if (!existing.groups || !existing.groups.length) {
            existing.groups = new_user_id.groups;
            update = true;
          } else {
            for (const group of new_user_email.groups) {
              if (!existing.groups.includes(group)) {
                existing.groups.push(group);
                update = true;
              }
            }
          }
        }
      }

      if (update) {
        update_users.push(existing);
        if (existing.groups) existing.groups = _.uniq(existing.groups);
      }
    }

    await data.users.bulkGlobalSave([...new_users, ...update_users]);

    const now = new Date();

    // check each users entities
    const create_users = [...new_users, ...update_users, ...existing_users].map(async (global_user) =>  {
      let person = global_people[global_user.email];
      if (!person) {
        const pids = emails ? emails[global_user.email] : [];
        if (pids && pids.length === 1 && people) {
          person = people[pids[0]];
        }
      }

      if (!person) {
        person = new Person({
          displayName: global_user.name,
          comms: [global_user.email],
          tags: global_user.groups.map(id => {
            const group = groups.find(g => g.id === id);
            if (group) return new Tag(TagType.organization, group.company_name, 0, now);
            return null;
          }).filter(t => t),
        });
        person.tempId();
      }

       // init the user without tokens
      const new_user = new ForaUser(global_user.profile, global_user.auth_ids && global_user.auth_ids.length ? global_user.auth_ids[0].split('_')[0] as AuthProviders : AuthProviders.Email);
      new_user.name = global_user.name;
      new_user.email = global_user.email;
      await data.users.init(new_user, false);

      if (!new_user.isAuthenticatedNonGuest()) {
        logging.warnFP(LOG_NAME, 'resolveUsers', user ? user.profile : undefined, `Resolving unauthenticated user ${new_user.profile}`);
        if (!new_user.provider || new_user.provider === AuthProviders.Email) {
          new_user.provider = AuthProviders.Email;
          const tokens = AuthProvider.email.tokens(new_user.email);
          new_user.tokens = tokens;
        }
      }
      
      // update groups
      if (!new_user.groups) new_user.groups = {};
      if (global_user.groups) {
        global_user.groups.forEach(group_id => { 
          if (!new_user.groups[group_id]) new_user.groups[group_id] = [] 
        });
      }

      // save user
      await data.users.save(new_user, true, true, false);
      await data.users.globalRegister(new_user, 'group');
      await data.users.saveGroup(new_user, new_user.groups);

      // save self
      const self = new Person(person);
      self.self = true;

      let user_group_ids = user && user.groups && global_user.groups ? _.intersection(Object.keys(user.groups), global_user.groups) : [];
      if(!user_group_ids.length && global_user.groups) {
        for (const group of groups) {
          if (group.email_domain && global_user.groups.includes(group.id)) {
            const match = group.email_domain.find(d => new_user.email.endsWith(d));
            if (match) {
              user_group_ids = [group.id];
              break;
            }
          }
        }
      }

      await data.users.generateVanity(new_user, self, user_group_ids ? groups.find(g => user_group_ids.includes(g.id)) : undefined);

      const host_group = groups ? groups.find(g => g.host && g.host.length) : null;
      const host = host_group ? host_group.host : config.get('DEFAULT_HOST');
      const vanity_url = `https://${host}/profile/${new_user.vanity}`;

      const save = self.checkSelf(new_user.profile, new_user.vanity, vanity_url);

      await data.users.saveVanity(new_user, self);
      
      await data.people.savePerson(new_user, self);

      await data.users.save(new_user, false, false, false);
      setTimeout(1000).then(() => data.profiles.get(new_user, new_user.vanity, true));
    });

    await Promise.all(create_users);

    return create_users.length ? [EntityType.User] : [];
  }

  private static async resolvePeople(user: ForaUser, self: Partial<Person>, people: {[key: string]: Person}, emails: {[key: string]: Uid[]}, resolve_people: Person[] = null, global_people: {[key:string]: Person}, first_run = false): Promise<EntityType[]> {
    if (!resolve_people) resolve_people = Object.values(people);
    logging.infoFP(LOG_NAME, 'resolvePeople', user.profile, `Resolving ${resolve_people.length} people`);

    try {
      //cleanup
      for (const person of resolve_people) {
        const orgs = person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
        parsers.cleanTags(person.tags, person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
        if (person.comms) person.comms = _.uniq(person.comms);
        else person.comms = [];

        if (person.learned && person.learned.length) person.learned =  person.learned.map(l => new Date(l));
      }

      const save_people: { [s: string]: Person } = {};
      const remove_people: { [key: string]: Person } = {};
      const resolve_map = {}; // map from old to new
      const source_duplicates: {[key:string] : Uid[]} = {};

      // note: dont resolve self
      for (const person of resolve_people.filter(r => !r.self && (!r.comms || !r.comms.includes(user.email)))) {
        if (!(person.id in source_duplicates)) source_duplicates[person.id] = [];
        const sr = await data.people.resolveSavePerson(user, person, people, emails, source_duplicates[person.id]);
        const p_emails = parsers.findEmail(sr.save.comms);
        if (sr.save) {
          if (p_emails) {
            for (const comm of p_emails) {
              const global = global_people[comm];
              if (global) {
                peopleUtils.updateFromProfile(sr.save, global);
                break;
              }
            }
          }
          save_people[sr.save.id] = sr.save;
          if (people) people[sr.save.id] = sr.save;
          if (emails && p_emails) {
            for (const email of p_emails) {
              if (emails[email]) emails[email] = emails[email].filter(id => id !== person.id).concat([person.id]);
              else emails[email] = [person.id]
            }
          }
        }

        if (sr.remove) {
          remove_people[sr.remove.id] = sr.remove;
          resolve_map[sr.remove.id] = sr.save.id;
        } else if(person.id !== sr.save.id) {
          resolve_map[person.id] = sr.save.id;
          if (people) people[person.id] = sr.save;
          if (emails && p_emails) {
            for (const email of p_emails) {
              if (emails[email]) emails[email] = emails[email].filter(id => id !== sr.remove.id && id !== person.id).concat([person.id]);
              else emails[email] = [person.id]
            }
          }
        }
      }

      if (people) {
        const duplicate_ids = _.uniq(flatten(Object.values(source_duplicates)));
        const reloaded_people = await data.people.batchReload(user, duplicate_ids.map(id => people[id]));
        const reloaded_map: {[key:string]: Person} = {};
        for(const person of reloaded_people) reloaded_map[person.id] = person;

        //remove and merge anyone not in the reload
        for (const dupe_id in source_duplicates) {
          const dupe_ids = source_duplicates[dupe_id];
          if (!dupe_ids.length) continue;
          const dupe_person = people[dupe_id];
          const dupe_people = dupe_ids.map(id => people[id]);

          const reloaded_person = reloaded_map[dupe_id];
          if (reloaded_person) {
            //merge into reloaded
            peopleUtils.mergePeople(dupe_person, reloaded_person, true);
            if (!save_people[dupe_id]) save_people[dupe_id] = dupe_person;
            for (const dp of dupe_people) {
              const rp = reloaded_map[dp.id];
              if (!rp) {
                if (!resolve_map[dp.id]) resolve_map[dp.id] = dupe_id;
                if (!remove_people[dp.id]) remove_people[dp.id] = dp;
              }
            }
          } else {
            if (!remove_people[dupe_id]) remove_people[dupe_id] = dupe_person;
          }
        }
      }

      const ds_del: Person[] = [];
      for (const index in remove_people) {
        /* for (const comm of remove_people[index].comms) {
          data.plugins.cachePlugin().clearCacheAttVal(user.profile, EntityType.Person, 'comms', comm);
        }*/
        data.plugins.cachePlugin().clearCacheAttVal(user.profile, EntityType.Person, 'id', remove_people[index].id);
        // ds_del.push(storage.key(user.profile, remove_people[index]));
        ds_del.push(remove_people[index]);
        if (save_people[index]) delete save_people[index];
      }

      const ds_save = Object.values(save_people); //[];
      /*for (const index in save_people) {
        // ds_save.push(serialize(user.profile, save_people[index], ['tags', 'urls', 'photos', 'learned']));
        ds_save.push(save_people[index]);
      }*/

      if (self && resolve_map[self.id]) self.id = resolve_map[self.id];

      const update_tasks = await data.tasks.migratePeople(user, self, resolve_map, people, emails);
      const update_events = await data.events.migratePeople(user, self, resolve_map, people, emails);
      const update_messages = await data.messages.migratePeople(user, self, resolve_map, people, emails);
      const update_projects = await data.projects.migratePeople(user, self, resolve_map, people, emails);

      const save_entities: EntityType[] = ds_save.length || ds_del.length ? [EntityType.Person] : [];
      if (update_tasks.length) save_entities.push(EntityType.Task);
      if (update_events.length) save_entities.push(EntityType.Event);
      if (update_messages.length) save_entities.push(EntityType.Message);
      if (update_projects.length) save_entities.push(EntityType.Project);

      await data.plugins.storagePlugin().saveAll(user ? user.profile: undefined, [...ds_save, ...update_tasks, ...update_events, ...update_messages, ...update_projects]);
      await data.plugins.storagePlugin().removeAll(user.profile, ds_del);

      if (!first_run) await data.users.refreshSet(user, save_entities);
      
      const cacheExpires: number = 5 * 24 * 3600;
      const people_names = {};
      const org_people = {};

      const cache_people = resolve_people.slice();
      logging.infoFP(LOG_NAME, 'resolvePeople', user.profile, `Updating cache for ${resolve_people.length} people`);
      while(cache_people.length) {
        await Promise.all(cache_people.splice(0,50).map(async person => {
          try {
            await data.plugins.cachePlugin().clearCacheAttVal(user.profile, EntityType.Person, 'id', person.id);
            peopleUtils.mapPersonDetails(people_names, org_people, person);
          } catch (err) {
            logging.errorFP(LOG_NAME, 'loadPeople', user.profile, 'Error loading people', err);
          }
        }));
      }

      const updates = [];

      logging.infoFP(LOG_NAME, 'resolvePeople', user.profile, `Updating cache for ${Object.keys(people_names).length} people names`);
      for (const name in people_names) {
        updates.push(await data.plugins.cachePlugin().cacheAttVal(user.profile, EntityType.Person, 'people_names', name, people_names[name], cacheExpires));
      }

      logging.infoFP(LOG_NAME, 'resolvePeople', user.profile, `Updating cache for ${Object.keys(org_people).length} people orgs`);
      for (const org in org_people) {
        updates.push(await data.plugins.cachePlugin().cacheAttVal(user.profile, EntityType.Organization, 'org_people', org, org_people[org], cacheExpires));
      }

      await Promise.all(updates);

      logging.infoFP(LOG_NAME, 'resolvePeople', user.profile, 'Done resolving People');
      return save_entities;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'resolvePeople', user.profile, 'Error resolving people to datastore', err);
      return [];
    }
  }


  /**
   * Import data using plugin.
   *
   * @param user The user to import to
   * @param plugin The plugin we are using to import
   * @param doc The document to import
   * @param import_type the type of import
   */
  private static async importDataFromPlugin(user: ForaUser, people: Person[], user_people: {[key:string]: Uid[]}, plugin: IImportPlugin) {
    const imports = await plugin.importData();
    const user_groups = await plugin.createUserGroups();

    // create temp ids for people
    imports.forEach(p => {
      if (p.type === EntityType.Person) {
        if (!p.id) (p as Person).tempId();
      }
    });

    for (const u of imports as Person[]) {
      people.push(u);
      const emails = parsers.findEmail(u.comms);
      if (user_groups && user_groups.length) {
        if(emails && emails.length) {
          user_people[u.id] = user_groups;
          logging.infoFP(LOG_NAME, 'importDataFromPlugin', user.profile, `Creating user for ${u.id} in ${JSON.stringify(user_groups)}`);
        } else logging.warnFP(LOG_NAME, 'importDataFromPlugin', user.profile, `Not creating user for ${u.id} in ${JSON.stringify(user_groups)}`);
      } 
    }
  }

  /**
   * Load all historical data for the given plugin. Each plugin will implement how this is done.
   *
   * @param user The user we are operating for/on
   * @param plugin The plugin we are operating with
   * @async
   */
  private static async loadHistoricalDataFromPlugin(user: ForaUser, self: Partial<Person>, plugin: ISourcePlugin<any, any>): Promise<[IEntity[], IEntity[]]>  {
    try {
      if (logging.isDebug(user.profile)) DEBUG('loadHistoricalDataFromPlugin: %s metadata before = %O', plugin.typeFor(), plugin.metaData());
      let updates: IEntity[] = [];
      let deletes: IEntity[] = [];
      while (plugin.dataHistoryExists()) {
        // Get the "page" of data from the plugin
        const [new_updates, new_deletes] = await plugin.dataHistoryRead();
        if (logging.isDebug(user.profile)) DEBUG('loadHistoricalDataFromPlugin: %s metadata intermediate = %O', plugin.typeFor(), plugin.metaData());

        updates = updates.concat(new_updates);
        deletes = deletes.concat(new_deletes);
      }

      updates = _.uniqBy(updates, 'id');
      deletes  = _.uniqBy(deletes, 'id');

      // Persist all the changes to the data store (whatever that might be)
      // await SourceController.persistUpdatesAndDeletes(user, self, updates, deletes);
      if (logging.isDebug(user.profile)) DEBUG('loadHistoricalDataFromPlugin: %s metadata after = %O', plugin.typeFor(), plugin.metaData());
      if (updates.length || deletes.length) data.users.refreshSet(user, plugin.typeFor());
      return [updates, deletes];
    } catch (err) {
      logging.errorFP(LOG_NAME, 'loadHistoricalDataFromPlugin', user.profile, `Error reading historical entities for ${plugin.typeFor()}`, err);
    }
    return [[], []];
  }

  /**
   * Load all new data for the given plugin. Each plugin will implement how this is done.
   *
   * @param user The user we are operating for/on
   * @param plugin The plugin we are operating with
   * @async
   */
  private static async loadNewDataFromPlugin(user: ForaUser, self: Partial<Person>, plugin: ISourcePlugin<any, any>): Promise<[IEntity[], IEntity[]]>  {
    try {
      if (logging.isDebug(user.profile)) DEBUG('loadNewDataFromPlugin: %s metadata after = %O', plugin.typeFor(), plugin.metaData());
      let updates: IEntity[] = [];
      let deletes: IEntity[] = [];
      while (plugin.dataNewExists()) {
        // Get the "page" of data from the plugin
        const [new_updates, new_deletes] = await plugin.dataNewRead();

        updates = updates.concat(new_updates);
        deletes = deletes.concat(new_deletes);
      }
      if (logging.isDebug(user.profile)) DEBUG('loadNewDataFromPlugin: %s metadata after = %O', plugin.typeFor(), plugin.metaData());

      updates = _.uniqBy(updates, 'id');
      deletes  = _.uniqBy(deletes, 'id');

      if (updates.length || deletes.length) data.users.refreshSet(user, plugin.typeFor());
      return [updates, deletes];
    } catch (err) {
      logging.errorFP(LOG_NAME, 'loadNewDataFromPlugin', user.profile, `Error reading new entities for ${plugin.typeFor()}`, err);
      plugin.disabledError(true);
      await ErrorHandler.handleError(user, user.provider, err, data.users.message, data.users.logoutUser);
    }

    return [[], []];
  }

  /**
   * Notify the cache about entity types that have had changes
   *
   * @param user - Fora user running for
   * @param entityTypes - Set of types of entities that were refreshed during one of the methods above
   * @async
   */
  private static async notifyRefreshed(user: ForaUser, self: Partial<Person>, entity_types: EntityType[]): Promise<void> {
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'notifyRefresh', user.profile, `types updated ${util.format(entity_types)}`);

    const data_cache = new DataCache(user, new Person(self), user.loaded_groups ? Object.values(user.loaded_groups) : [], `update_${user.profile}`);
    await data_cache.refreshCache(entity_types);
  }

  /**
   * Persist the data to the storage plugin
   *
   * @param user - Fora user running for
   * @param updates - List of updates that need to be persisted to the store
   * @param deletes - List of deletes that need to be removed from the store
   * @async
   */
  private static async persistUpdatesAndDeletes(user: ForaUser, self: Partial<Person>, updates: IEntity[], deletes: IEntity[], first_run = false): Promise<EntityType[]> {
    // Call saveAll and removeAll in parallel
    logging.infoFP(LOG_NAME, 'persistUpdatesAndDeletes', user.profile, `Processing ${updates.length} updates and ${deletes.length} deletes`);

    // can't update and delete the same entity
    const do_update: IEntity[] = [];
    if (updates.length) {
      // const people_ids = SourceController.user_people[user.profile];

      const no_id_del = deletes.filter(d => !d.id);  
      if (no_id_del.length) {
        const no_id_types = _.uniq(no_id_del.map(d => d.schema.name));
        logging.warnFP(LOG_NAME, 'persistUpdatesAndDeletes', user.profile, `Attemping to delete ${no_id_del.length} entities with no id. ${no_id_types}`);
        deletes = deletes.filter(d => d.id !== null && d.id !== undefined);
      }

      const no_del = [];
      const delete_map = deletes.map(d => `${d.schema.name}_${d.id}`);
      for (const entity of updates) {

        if (!entity) {
          logging.warnFP(LOG_NAME, 'persistUpdatesAndDeletes', user.profile, `Cannot update null entity`);
          continue;
        }

        if (!entity.schema) {
          logging.warnFP(LOG_NAME, 'persistUpdatesAndDeletes', user.profile, `Cannot update entity ${entity.type} with no schema`);
          continue;
        }

        const tid = `${entity.schema.name}_${entity.id}`;
        if (delete_map.includes(tid)) {
          logging.warnFP(LOG_NAME, 'persistUpdatesAndDeletes', user.profile, `Cannot both update and delete ${tid}, therefore we are deleting it`);
          // no_del.push(tid);
        } else {
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'persistUpdatesAndDeletes', user.profile, `Adding ${tid}`);
          // DEBUG(`Adding ${util.format(entity)}`);
          do_update.push(entity);
        }
      }

      // if (no_del.length) deletes = deletes.filter(d => !no_del.includes(`${d.schema.name}_${d.id}`));
    }

    logging.infoFP(LOG_NAME, 'persistUpdatesAndDeletes', user.profile, `Done processing ${updates.length} updates and ${deletes.length} deletes`);
    const entity_type_sets = await Promise.all([data.plugins.storagePlugin().saveAll(user ? user.profile : undefined, do_update), data.plugins.storagePlugin().removeAll(user.profile, deletes)]);
    logging.infoFP(LOG_NAME, 'persistUpdatesAndDeletes', user.profile, `Done persisting ${updates.length} updates and ${deletes.length} deletes with types ${util.format(entity_type_sets)}`);


    // When done with the DS, merge the sets of changed EntityTypes and notify the caches
    const merged = _.uniq(entity_type_sets.reduce((a,b) => a.concat(Array.from(b)), []));
    if (!first_run) await SourceController.notifyRefreshed(user, self, merged);
    return merged;
  }

  /**
   * Persist the user to the storage plugin
   *
   * @param user - Fora user running for
   * @async
   */
  private static async persistUser(user: ForaUser): Promise<void> {
    // Save the data the plugin changed on the user
    await data.users.saveBoth(user, true, false).catch(err => logging.errorFP(LOG_NAME, 'persistUser', user.profile, 'Error saving user after operation', err));
  }

  /**
   * Remove old data for the given plugin.
   *
   * @param user - The user we are operating for/on
   * @param plugin - The plugin we are operating with
   * @async
   */
  private static async removeOldDataFromPlugin(user: ForaUser, plugin: ISourcePlugin<any, any>): Promise<[IEntity[], Partial<IEntity>[]]>  {
    try {
      if (plugin.supportsDataExpiration()) {
        // Get the filters we will use for the delete
        const filters = await plugin.dataExpirationFilters();

        // Using the filters provided to find the list of keys
        /* const keys = await data.plugins.storagePlugin().findKeysByFilter(user.profile, plugin.typeFor(), filters);
        if (keys) logging.infoFP(LOG_NAME, 'removeOldDataFromPlugin', user.profile, `Found ${keys.length} keys to delete for ${plugin.typeFor()}`);
        else logging.infoFP(LOG_NAME, 'removeOldDataFromPlugin', user.profile, `Found no keys to delete for ${plugin.typeFor()}`);

        // Delete the entries by key
        await data.plugins.storagePlugin().delete(user.profile, plugin.typeFor(), keys);*/

        const items = await data.plugins.storagePlugin().findItemsByFilter(user.profile, plugin.typeFor(), filters);
        if (items) {
          logging.infoFP(LOG_NAME, 'removeOldDataFromPlugin', user.profile, `Found ${items.length} items to delete for ${plugin.typeFor()}`);
          if (items.length) data.users.refreshSet(user, plugin.typeFor());
        }
        else logging.infoFP(LOG_NAME, 'removeOldDataFromPlugin', user.profile, `Found no items to delete for ${plugin.typeFor()}`);
        return [[], items];
      }
    } catch (err) {
      logging.errorFP(LOG_NAME, 'removeOldDataFromPlugin', user.profile, `Error removing old entities for ${plugin.typeFor()}`, err);
    }
  }
}
