/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { GoogleEventsSourcePlugin } from './events/google/google_events_source_plugin';
import { MicrosoftEventsSourcePlugin } from './events/microsoft/microsoft_events_source_plugin';
import { GoogleMessageSourcePlugin } from './messages/google/google_messages_source_plugin';
import { MicrosoftMessageSourcePlugin } from './messages/microsoft/microsoft_messages_source_plugin';
import { GooglePeopleSourcePlugin } from './people/google/google_people_source_plugin';
import { MicrosoftPeopleSourcePlugin } from './people/microsoft/microsoft_people_source_plugin';
import { GoogleTasksSourcePlugin } from './tasks/google/google_tasks_source_plugin';
import { MicrosoftTasksSourcePlugin } from './tasks/microsoft/microsoft_tasks_source_plugin';
import { GoogleUsersSourcePlugin } from './users/google/google_users_source_plugin';
import { MicrosoftUsersSourcePlugin } from './users/microsoft/microsoft_users_source_plugin';

import { CustomPeopleImportPlugin } from './people/custom/custom_people_import_plugin';
import { FacebookPeopleImportPlugin } from './people/facebook/facebook_people_import_plugin';
import { iCloudPeopleImportPlugin } from './people/icloud/icloud_people_import_plugin';
import { LinkedInPeopleImportPlugin } from './people/linkedin/linkedin_people_import_plugin';

import { AlisonCoursesSourcePlugin } from './courses/alison/alison_courses_source_plugin';
import { CourseraCoursesSourcePlugin } from './courses/coursera/coursera_courses_source_plugin';
import { edXCoursesSourcePlugin } from './courses/edx/edx_courses_source_plugin';
import { LinkedInCoursesSourcePlugin } from './courses/linkedin/linkedin_courses_source_plugin';
import { MasterClassCoursesSourcePlugin } from './courses/masterclass/masterclass_courses_source_plugin';
import { TEDCoursesSourcePlugin } from './courses/ted/ted_courses_source_plugin';
import { UdemyCoursesSourcePlugin } from './courses/udemy/udemy_courses_source_plugin';
import { UOPXCoursesSourcePlugin } from './courses/uopx/uopx_courses_source_plugin';

export enum SourceKeys {
  GoogleEvents = 'GoogleEvents',
  GoogleMessages = 'GoogleMessages',
  GooglePeople = 'GooglePeople',
  GoogleTasks = 'GoogleTasks',
  GoogleUsers = 'GoogleUsers',
  MicrosoftEvents = 'MicrosoftEvents',
  MicrosoftMessages = 'MicrosoftMessages',
  MicrosoftPeople = 'MicrosoftPeople',
  MicrosoftTasks = 'MicrosoftTasks',
  MicrosoftUsers = 'MicrosoftUsers',
}

export enum ImportKeys {
  FacebookPeople = 'FacebookPeople',
  iCloudPeople = 'iCloudPeople',
  LinkedInPeople = 'LinkedInPeople',
  CustomPeople = 'CustomPeople',
}

export enum CourseKeys {
  edXCourses = 'edXCourses',
  LinkedInCourses = 'LinkedInCourses',
  TEDCourses = 'TEDCourses',
  UOPXCourses = 'UOPXCourses',
  AlisonCourses = 'AlisonCourses',
  MasterClassCourses = 'MasterClassCourses',
  CourseraCourses = 'CourseraCourses',
  UdemyCourses = 'UdemyCourses',
}

export const SOURCES_OFFLINE = new Map<SourceKeys, any>();
export const SOURCES_ONLINE = new Map<SourceKeys, any>();
export const IMPORTS = new Map<ImportKeys, any>();
export const COURSES = new Map<CourseKeys, any>();

SOURCES_ONLINE.set(SourceKeys.GoogleEvents, GoogleEventsSourcePlugin);
SOURCES_ONLINE.set(SourceKeys.GoogleMessages, GoogleMessageSourcePlugin);
SOURCES_ONLINE.set(SourceKeys.GooglePeople, GooglePeopleSourcePlugin);
SOURCES_ONLINE.set(SourceKeys.GoogleTasks, GoogleTasksSourcePlugin);
SOURCES_ONLINE.set(SourceKeys.GoogleUsers, GoogleUsersSourcePlugin);

SOURCES_ONLINE.set(SourceKeys.MicrosoftEvents, MicrosoftEventsSourcePlugin);
SOURCES_ONLINE.set(SourceKeys.MicrosoftMessages, MicrosoftMessageSourcePlugin);
SOURCES_ONLINE.set(SourceKeys.MicrosoftPeople, MicrosoftPeopleSourcePlugin);
SOURCES_ONLINE.set(SourceKeys.MicrosoftTasks, MicrosoftTasksSourcePlugin);
SOURCES_ONLINE.set(SourceKeys.MicrosoftUsers, MicrosoftUsersSourcePlugin);

IMPORTS.set(ImportKeys.FacebookPeople, FacebookPeopleImportPlugin);
IMPORTS.set(ImportKeys.iCloudPeople, iCloudPeopleImportPlugin);
IMPORTS.set(ImportKeys.LinkedInPeople, LinkedInPeopleImportPlugin);
IMPORTS.set(ImportKeys.CustomPeople, CustomPeopleImportPlugin);

COURSES.set(CourseKeys.edXCourses, edXCoursesSourcePlugin);
COURSES.set(CourseKeys.LinkedInCourses, LinkedInCoursesSourcePlugin);
COURSES.set(CourseKeys.TEDCourses, TEDCoursesSourcePlugin);
COURSES.set(CourseKeys.UOPXCourses, UOPXCoursesSourcePlugin);
COURSES.set(CourseKeys.AlisonCourses, AlisonCoursesSourcePlugin);
COURSES.set(CourseKeys.MasterClassCourses, MasterClassCoursesSourcePlugin);
COURSES.set(CourseKeys.CourseraCourses, CourseraCoursesSourcePlugin);
COURSES.set(CourseKeys.UdemyCourses, UdemyCoursesSourcePlugin);