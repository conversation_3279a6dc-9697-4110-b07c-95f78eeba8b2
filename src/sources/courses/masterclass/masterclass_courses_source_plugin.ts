/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Course, CourseType } from '../../../types/globals';

import logging from '../../../utils/logging';

import ForaUser from '../../../session/user';
import Learn from '../../../skills/learn';

import { ICoursesSourcePlugin } from '../i_courses_source_plugin';

const LOG_NAME = 'courses.masterclass'

export class MasterClassCoursesSourcePlugin implements ICoursesSourcePlugin {

  get courseTypes() : CourseType[] { return [CourseType.MasterClass]; }

  async processCourse(data: string): Promise<Course> {
   let course;
    try {
      course = JSON.parse(data);
    } catch(e) {
      if (!e.message.includes('Unexpected token')) logging.errorF(LOG_NAME, 'processCourse', 'Error processing course', e)
      return;
    }

    const learn = new Learn(new ForaUser('anonymous'), null, 0);

    const url = `https://www.masterclass.com${course.url}`;
    const xc = await learn.learnCourse(url, course);
    if (xc && xc.skills.length) return xc;
  }

}