/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import { Course, CourseType } from '../../../types/globals';

import logging from '../../../utils/logging';

import ForaUser from '../../../session/user';
import Learn from '../../../skills/learn';

import { ICoursesSourcePlugin } from '../i_courses_source_plugin';

const LOG_NAME = 'courses.alison';

export class AlisonCoursesSourcePlugin implements ICoursesSourcePlugin {

  get courseTypes() : CourseType[] { return [CourseType.Alison]; }

  async processCourse(data: string): Promise<Course> {
   let course;
   let cdata;
    try {
      cdata = JSON.parse(data);
      course = cdata.course;
    } catch(e) {
      if (!e.message.includes('Unexpected token')) logging.errorF(LOG_NAME, 'processCourse', 'Error processing course', e)
      return;
    }

    const learn = new Learn(new ForaUser('anonymous'), undefined, 0);

    if (course.slug) {
      const url = `https://www.alison.com/course/${course.slug}`;
      const xc = await learn.learnCourse(url, cdata);
      if (xc && xc.skills.length) return xc;
    }
  }
}