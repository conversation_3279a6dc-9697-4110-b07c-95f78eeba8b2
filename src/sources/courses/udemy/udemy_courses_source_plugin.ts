/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import soup from 'jssoup';

import { Course, CourseType } from '../../../types/globals';

import ForaUser from '../../../session/user';
import Learn from '../../../skills/learn';

import { ICoursesSourcePlugin } from '../i_courses_source_plugin';

export class UdemyCoursesSourcePlugin implements ICoursesSourcePlugin {

  get courseTypes() : CourseType[] { return [CourseType.Udemy]; }

  async processCourse(data: string): Promise<Course> {
    let url;
    try {
      const s = new soup(data);
      url = `https://www.udemy.com${s.find('base').attrs.href}`;
    } catch(e) {
      return null;
    }

    if (url) {
      const learn = new Learn(new ForaUser('anonymous'), null, 0);

      const xc = await learn.learnCourse(url, data);
      if (xc && xc.skills.length) return xc;
    }
    return null;
  }

}