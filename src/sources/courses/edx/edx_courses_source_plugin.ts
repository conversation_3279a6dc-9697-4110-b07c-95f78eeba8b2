/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import { Course, CourseType } from '../../../types/globals';

import logging from '../../../utils/logging';

import ForaUser from '../../../session/user';
import Learn from '../../../skills/learn';

import { ICoursesSourcePlugin } from '../i_courses_source_plugin';

const LOG_NAME = 'courses.edx';

export class edXCoursesSourcePlugin implements ICoursesSourcePlugin {

  get courseTypes() : CourseType[] { return [CourseType.edX]; }

  async processCourse(data: string): Promise<Course> {
   let course;
    try {
      course = JSON.parse(data);
    } catch(e) {
      if (!e.message.includes('Unexpected token')) logging.errorF(LOG_NAME, 'processCourse', 'Error processing course', e)
      return;
    }

    const learn = new Learn(new ForaUser('anonymous'), undefined, 0);

    if (course.path) {
      const url = course.path.startsWith('http') ? course.path : `https://www.edx.org${course.path}`;
      const xc = await learn.learnCourse(url, course);
      if (xc && xc.skills.length) return xc;
    } 
  }
}