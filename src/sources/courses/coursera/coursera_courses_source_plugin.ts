/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import soup from 'jssoup';

import { Course, CourseType } from '../../../types/globals';

import ForaUser from '../../../session/user';
import Learn from '../../../skills/learn';

import { ICoursesSourcePlugin } from '../i_courses_source_plugin';

export class CourseraCoursesSourcePlugin implements ICoursesSourcePlugin {

  get courseTypes() : CourseType[] { return [CourseType.Coursera]; }

  async processCourse(data: string): Promise<Course> {
    let course;
    try {
      const s = new soup(data).findAll('script').find(s => s.attrs['type'] === 'application/ld+json');
      course = JSON.parse(s.text);
    } catch(e) {
      return null;
    }

    if (course) {
      const learn = new Learn(new ForaUser('anonymous'), null, 0);

      const url = course['@id'];
      const xc = await learn.learnCourse(url, data);
      if (xc && xc.skills.length) return xc;
    }
    return null;
  }

}