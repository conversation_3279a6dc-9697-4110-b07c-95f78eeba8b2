/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import soup from 'jssoup';

import { Course, CourseType } from '../../../types/globals';

import ForaUser from '../../../session/user';
import Learn from '../../../skills/learn';

import { ICoursesSourcePlugin } from '../i_courses_source_plugin';

export class LinkedInCoursesSourcePlugin implements ICoursesSourcePlugin {

  get courseTypes() : CourseType[] { return [CourseType.LinkedIn]; }

  async processCourse(data: string): Promise<Course> {
    const script = new soup(data).findAll('script').map(s => {
      try {
        return JSON.parse(s.text);
      } catch(e) {
        return null;
      }
    }).find(t => t && ['Course', 'VideoObject'].includes(t['@type']));

    if (script && (script.url || script.contentUrl)) {
      const learn = new Learn(new ForaUser('anonymous'), null, 0);

      const url = script.url ? script.url : script.contentUrl;
      const xc = await learn.learnCourse(url, data);
      if (xc && xc.skills.length) return xc;
    }
    return null;
  }

}