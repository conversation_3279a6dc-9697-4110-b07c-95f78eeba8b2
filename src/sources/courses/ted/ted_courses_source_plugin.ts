/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import { v4 as uuid } from 'uuid';

import { Course, CourseType } from '../../../types/globals';

import ForaUser from '../../../session/user';
import Learn from '../../../skills/learn';

import { ICoursesSourcePlugin } from '../i_courses_source_plugin';

export class TEDCoursesSourcePlugin implements ICoursesSourcePlugin {

  get courseTypes() : CourseType[] { return [CourseType.TED]; }

  async processCourse(data: string): Promise<Course> {
    const learn = new Learn(new ForaUser('anonymous'), null, 0);

    const url = `https://www.ted.com/talks/${uuid()}`;
    const xc = await learn.learnCourse(url, data);
    if (xc && xc.skills.length) return xc;
  }

}