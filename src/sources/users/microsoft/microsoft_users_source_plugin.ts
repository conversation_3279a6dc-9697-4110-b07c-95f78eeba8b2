import { User as MSUser } from '@microsoft/microsoft-graph-types';
import { AuthProvider } from '../../../auth/auth_provider';
import ForaUser from '../../../session/user';
import { User } from '../../../types/globals';
import { IEntity, Person, Tag } from '../../../types/items';
import { AuthProviders, EntityType, TagType, Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';
import { AbstractMicrosoftSourcePlugin } from '../../a_microsoft_source_plugin';
import MicrsoftGraphUserHelper from '../../helpers/microsoft/microsoft_graph_user_helper';
import { SourceKeys } from '../../index';
import { IUsersSourcePlugin } from '../i_users_source_plugin';
import { MicrosoftUsersSourceMetadata } from './microsoft_users_source_metadata';

const LOG_NAME = 'data.sources.MicrosoftUsersSourcePlugin';

/**
 * Microsoft Users Source Plugin
 * - https://docs.microsoft.com/en-us/graph/api/resources/users?view=graph-rest-1.0
 */

export class MicrosoftUsersSourcePlugin extends AbstractMicrosoftSourcePlugin<User, MicrosoftUsersSourceMetadata> implements IUsersSourcePlugin {
  public static providers = [AuthProviders.Microsoft, AuthProviders.Msal];

  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(LOG_NAME, SourceKeys.MicrosoftUsers, EntityType.User, user, account, user_people, user_emails, MicrosoftUsersSourceMetadata, 
      user.hasAccount(AuthProviders.Msal, account) ? AuthProviders.Msal : AuthProviders.Microsoft,
      user.hasAccount(AuthProviders.Msal, account) ? AuthProvider.msal.getScopesForUsers() : AuthProvider.microsoft.getScopesForUsers(),
      false, false);
    }

  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    let add_users: User[] = [];
    let add_people: Person[] = [];
    let del_users = [];

    let group = this.user.getAccountGroup(this.account);
    if (!group) group = this.user.emailGroup();

    try {
      const ms_users = await MicrsoftGraphUserHelper.usersGet(this.user, this.account);
      if (ms_users) {
        if (group && this.user.isAdmin(group.id)) add_users = this.usersFromUsers(ms_users, [group.id]);
        add_people = this.peopleFromUsers(ms_users, group  ? group.name : null);
      }
    } catch(e) {
      logging.errorF(LOG_NAME, 'providerRead', 'Error reading users', e);
      throw e;
    }

    for (const person of add_people) this.addPerson(person);

    return [[...add_users, ...add_people], del_users];
  }

  usersFromUsers(ms_users: MSUser[], groups: Uid[]): User[] {
    const users: User[] = [];
    for(const ms_user of ms_users) {
      if (ms_user.id && ms_user.mail) {
        const user = new User({
          id: ms_user.id,
          email: ms_user.mail.toLowerCase(),
          name: ms_user.givenName,
          profile: ms_user.id,
          locale: ms_user.preferredLanguage,
          groups,
        });
        users.push(user);
      } else logging.warnFP(LOG_NAME, 'usersFromUsers', this.user.profile, `Skipping creating ${JSON.stringify(ms_user)}`);
    }
    return users;
  }

  peopleFromUsers(ms_users: MSUser[], org: string): Person[] {
    const people: Person[] = [];
    for (const ms_user of ms_users) {
      const person = new Person({
        comms: [
          ...parsers.findEmail(ms_user.mail ? ms_user.mail : []),
          ...parsers.findPhone(ms_user.mobilePhone ? ms_user.mobilePhone : []),
        ].filter(c => c && c.length),
        displayName: ms_user.displayName,
        nickName: ms_user.givenName,
      });

      if (org) person.tags.push(new Tag(TagType.organization, org));
      if (ms_user.jobTitle && ms_user.jobTitle.length) person.tags.push(new Tag(TagType.jobTitle, ms_user.jobTitle));

      person.tempId();

      people.push(person);
    }
    return people;
  }

}

