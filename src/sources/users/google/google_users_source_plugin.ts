import { admin_directory_v1, google } from 'googleapis';

import { AuthProvider } from '../../../auth/auth_provider';
import ForaUser from '../../../session/user';

import { User } from '../../../types/globals';
import { IEntity, Person } from '../../../types/items';
import { AuthProviders, EntityType, Uid } from '../../../types/shared';

import logging from '../../../utils/logging';

import { AbstractGoogleSourcePlugin } from '../../a_google_source_plugin';
import { GoogleConverter } from '../../helpers/google/google_conversions';
import { SourceKeys } from '../../index';
import { IUsersSourcePlugin } from '../i_users_source_plugin';
import { GoogleUsersSourceMetadata } from './google_users_source_metadata';

const directory = google.admin('directory_v1');
const LOG_NAME = 'data.sources.GoogleUsersSourcePlugin';

// https://admin.googleapis.com/admin/directory/v1/users
export class GoogleUsersSourcePlugin extends AbstractGoogleSourcePlugin<User, GoogleUsersSourceMetadata> implements IUsersSourcePlugin {
  public static providers = [AuthProviders.Google];

  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(LOG_NAME, SourceKeys.GoogleUsers, EntityType.User, user, account, user_people, user_emails, GoogleUsersSourceMetadata, AuthProviders.Google, AuthProvider.google.getScopesForUsers(), false, false);
  }

  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    let add_users: User[] = [];
    let add_people: Person[] = [];
    let del_users = [];

    let group = this.user.getAccountGroup(this.account);
    if (!group) group = this.user.emailGroup();

    try {
      const auth = this.getClient();
      // version compatibility for OAuth2Client
      const service = new admin_directory_v1.Admin({auth: auth as any});

      const args = {
        auth,
        customer: 'my_customer',
      }

      const result = await this.repeatableCallWithBackOff(service.users.list, 'users.list', directory, args);

      if (result) {
        if (group && this.user.isAdmin(group.id)) add_users = GoogleConverter.usersFromGoogleUsers(result.data.users, [group.id]);
        add_people = GoogleConverter.peopleFromGoogleUsers(result.data.users, group ? group.name : null);
      }
    } catch(e) {
      logging.errorF(LOG_NAME, 'providerRead', 'Error reading users', e);
      throw e;
    }

    for (const person of add_people) this.addPerson(person);

    return [[...add_users, ...add_people], del_users];
  } 
}