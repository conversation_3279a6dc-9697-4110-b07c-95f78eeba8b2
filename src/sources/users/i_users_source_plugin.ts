/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { User } from '../../types/globals';
import { ISourceMetadata } from '../i_source_metadata';
import { ISourcePlugin } from '../i_source_plugin';

// To force a full refresh, set this into the full_refresh of the update_<profile> record
// {
//   "values": [
//     {
//       "stringValue": "com.askfora.User"
//     }
//   ]
// }

export interface IUsersSourcePlugin extends ISourcePlugin<User, ISourceMetadata> {
}
