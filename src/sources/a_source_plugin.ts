/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { JsonConvert, ValueCheckingMode } from 'json2typescript';
import util from 'util';

import ForaUser from '../session/user';

import { IEntity, Person } from '../types/items';
import { AuthProviders, EntityType, Uid } from '../types/shared';

import { DAYS } from '../utils/datetime';
import { slimEntity, stringIncludesAll } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import data from '../data';
import { ICachePlugin } from '../data/caching/i_cache_plugin';
import DataStorage from '../data/storage/data_storage';

import { ISourceMetadata } from './i_source_metadata';
import { ISourcePlugin } from './i_source_plugin';
import { SourceKeys } from './index';

const DEBUG = (require('debug') as any)('fora:sources');

export abstract class AbstractSourcePlugin<T extends IEntity, M extends ISourceMetadata> implements ISourcePlugin<T, M> {
  public static providers: AuthProviders[];

  protected cache: ICachePlugin;
  protected storage: DataStorage;
  protected new_people: Person[] = [];
  protected new_people_ids: Uid[] = [];

  protected constructor(
    protected log_name: string,
    protected _pluginKey: SourceKeys,
    protected entity_type: EntityType,
    protected user: ForaUser,
    protected account: Uid,
    protected user_people: {[key: string]: Person},
    protected user_emails: {[key: string]: Uid[]},
    protected metadata_class,
    protected required_provider: AuthProviders,
    protected required_scopes: string[][],
    protected supports_history,
    protected data_expiration_enabled,
    protected data_expiration_field = 'received',
    protected data_expiration_days_back = 30,
  ) {
    this.cache = data.plugins.cachePlugin();
    this.storage = data.plugins.storagePlugin();
    if (!this.account) this.account = this.user.profile;

    this.ensureMetadata();
  }

  create(entity: T): Promise<[T, IEntity[]]> {
    throw new Error('Not Implemented');
  }

  dataExpirationFilters(): {name: string; op: string; val: any }[] {
    if (this.data_expiration_enabled) return [{name: this.data_expiration_field, op: '<=', val: DAYS(new Date(), -Math.abs(this.data_expiration_days_back))}];
    else return [];
  }

  dataHistoryExists(): boolean {
    const exists = this.isEnabled() && !this.metaData().historyDone;
    if (logging.isDebug(this.user.profile)) DEBUG(`dataHistoryExists: ${this._pluginKey} historical data exists = %s [%s][%s]`, exists, this.isEnabled(), !this.metaData().historyDone);
    return exists;
  }

  async dataHistoryRead(): Promise<[IEntity[], IEntity[]]> {
    if (logging.isDebug(this.user.profile)) DEBUG('dataHistoryRead: %s metadata before = %o', this._pluginKey, this.user.data_sources[this._pluginKey] ? this.user.data_sources[this._pluginKey][this.account] : null);
    if (this.isEnabled() && this.supports_history) {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'dataHistoryRead', this.user.profile, 'enabled');
      const result = await this.providerRead(true);

      if (!this.metaData().hasHistoryPageToken()) this.setDataHistoryDone();
      if (result[0]) {
        for(const r of result[0]) r.account = this.account;
      }
      if (result[1]) {
        for(const r of result[1]) r.account = this.account;
      }
      return result;
    } else {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'dataHistoryRead', this.user.profile, 'NOT enabled, returning');
      return [[], []];
    }
  }

  dataNewExists(): boolean {
    const exists = this.isEnabled() && !this.metaData().newDone;
    if (logging.isDebug(this.user.profile)) DEBUG(`dataNewExists: ${this._pluginKey} new data exists = %s`, exists);
    return exists;
  }

  async dataNewRead(): Promise<[IEntity[], IEntity[]]> {
    if (logging.isDebug(this.user.profile)) DEBUG('dataNewRead: %s metadata = %o', this._pluginKey, this.user.data_sources[this._pluginKey] ? this.user.data_sources[this._pluginKey][this.account] : null);
    let new_data = null;

    if (this.isEnabled()) {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'dataNewRead', this.user.profile, 'enabled');
      new_data = this.providerRead(false);
    } else {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'dataNewRead', this.user.profile, 'NOT enabled, returning');
      new_data = [[],[]];
    }

    this.metaData().newDone = true;

    if (new_data[0]) {
      for(const r of new_data[0]) r.account = this.account;
    }
    if (new_data[1]) {
      for(const r of new_data[1]) r.account = this.account;
    }
    return new_data;
  }

  delete(entity: Partial<T>): Promise<void> {
    throw new Error('Not Implemented');
  }

  /**
   * Disabled due to an error
   */
  disabledError(disabled: boolean) {
    if (disabled) {
      logging.warnFP(this.log_name, 'disabledError', this.user.profile, `Disabling source plugin ${this.typeFor()}`);
      // this.user.data_sources[this._pluginKey] = new this.metadata_class();
      // this.metaData().enabled = false;
      this.metaData().disabledError = true;
    } else {
      // this.metaData().enabled = true;
      this.metaData().disabledError = false;
    }
  }

  async firstRun(): Promise<[IEntity[], IEntity[]]> {
    if (this.isEnabled()) {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'firstRun', this.user.profile, 'enabled');
      const initial_data = await this.providerRead(false);

      if (!this.supports_history) {
        // This plugin does not support history, make sure its flagged as such
        this.setDataHistoryDone();
      }

      // Flag that we are done with our first run
      this.setFirstRunDone();

      if (initial_data[0]) {
        for(const r of initial_data[0]) r.account = this.account;
      }
      if (initial_data[1]) {
        for(const r of initial_data[1]) r.account = this.account;
      }
      return initial_data;
    } else {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'firstRun', this.user.profile, 'NOT enabled, returning');
      return [[], []];
    }
  }

  isDisabledError(): boolean {
    return this.hasMetaData() && this.metaData().disabledError;
  }

  isEnabled(): boolean {
    return this.hasMetaData() && this.metaData().enabled;
  }

  /**
   * Is this the first run for this plugin?
   */
  isFirstRun() {
    return !this.isEnabled() || !this.metaData().firstRunDone;
  }

  hasMetaData(): boolean {
    return this.user.data_sources[this._pluginKey] && this.user.data_sources[this._pluginKey][this.account];
  }

  metaData(): M {
    if (this.hasMetaData()) return this.user.data_sources[this._pluginKey][this.account] as M;
    else throw new Error(`SourcePluginMetadataException: Missing metadata at '${this._pluginKey}'`);
  }

  /**
   * Get all items from the provider for this entity type.
   **
   * @param is_history - Flag indicating if we should read entities from history or future
   * @async
   */
  abstract providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]>;

  /**
   * Does this plugin support expiring old data?
   */
  supportsDataExpiration(): boolean {
    return this.data_expiration_enabled;
  }

  typeFor(): EntityType {
    return this.entity_type;
  }

  update(entity: T): Promise<[T, IEntity[]]> {
    throw new Error('Not Implemented');
  }

  protected ensureMetadata() {
    const has_account = this.user.hasAccount(this.required_provider, this.account);
    if (!has_account) {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'ensureMetadata', this.user.profile, `Not creating metadata - no account ${this.required_provider}.${this.account}`); 
      return;
    }

    // DEBUG('ensureMetaData: this = %j', this);
    if (logging.isDebug(this.user.profile)) DEBUG('ensureMetaData: this.user.data_sources = %j', this.user.data_sources);
    if (!this.user.data_sources[this._pluginKey] || !this.user.data_sources[this._pluginKey][this.account]) {
      if (!this.user.data_sources[this._pluginKey]) this.user.data_sources[this._pluginKey] = {};
      this.user.data_sources[this._pluginKey][this.account] = new this.metadata_class();
    } else {
      if (this.user.data_sources[this._pluginKey][this.account] instanceof this.metadata_class) {
        if (logging.isDebug(this.user.profile)) DEBUG('ensureMetaData: %s.%s already converted to class', this._pluginKey, this.account);
      } else {
        if (logging.isDebug(this.user.profile)) DEBUG('ensureMetaData: %s.%s needs conversion to class', this._pluginKey, this.account );

        const jsonConvert = new JsonConvert();
        // if (config.isSilly()) jsonConvert.operationMode = OperationMode.LOGGING;
        jsonConvert.ignorePrimitiveChecks = false; // don't allow assigning number to string etc.
        jsonConvert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL;
        try {
          if (logging.isDebug(this.user.profile)) DEBUG('ensureMetaData: Before de-serialize = %o', this.user.data_sources[this._pluginKey][this.account]);
          this.user.data_sources[this._pluginKey][this.account] = jsonConvert.deserializeObject(this.user.data_sources[this._pluginKey][this.account], this.metadata_class);
          if (logging.isDebug(this.user.profile)) DEBUG('ensureMetaData: After de-serialize = %o', this.user.data_sources[this._pluginKey][this.account]) ;
        } catch (err) {
          logging.errorFP(this.log_name, 'ensureMetadata', this.user.profile, `Resetting metadata for ${this._pluginKey}.${this.account}`, err);
          logging.warnFP(this.log_name, 'ensureMetadata', this.user.profile, `Offending JSON for ${this._pluginKey}.${this.account} = ${util.format(this.user.data_sources[this._pluginKey][this.account])}`);
          this.user.data_sources[this._pluginKey][this.account] = new this.metadata_class();
        }
      }
    }

    const metaData: ISourceMetadata = this.user.data_sources[this._pluginKey][this.account];
    const tokens = this.user.getTokens(this.required_provider, this.account);

    // We are enabled by default if the tokens include the required scopes for implementer
    if (logging.isDebug(this.user.profile)) {
      DEBUG('ensureMetaData: %s.%s required provider', this._pluginKey, this.account, this.required_provider);
      DEBUG('ensureMetaData: %s.%s has provider', this._pluginKey, this.account, has_account && tokens ? tokens.provider : 'NO TOKENS');
      DEBUG('ensureMetaData: %s.%s required scopes', this._pluginKey, this.account, this.required_scopes);
      DEBUG('ensureMetaData: %s.%s actual scopes', this._pluginKey, this.account, has_account && tokens ? tokens.scope : 'NO TOKENS');
    }

    if (metaData.disabledError) { if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'ensureMetadata', this.user.profile, 'Plugin disabled - disabledError = true'); }
    if ( has_account && tokens && this.required_scopes.reduce((passed,scope) => passed || stringIncludesAll(tokens.scope, scope), false)) {
      if (!metaData.enabled) {
        logging.infoFP(this.log_name, 'ensureMetadata', this.user.profile, 'Enabling plugin because scopes/provider are correct');
        metaData.enabled = true;
      }
    } else {
      if (metaData.enabled) {
        logging.warnFP(this.log_name, 'ensureMetadata', this.user.profile, 'Disabling plugin because scopes/provider are NOT correct');
        metaData.enabled = false;
      }
    }

    // Always set new done as we assume each time we are created its for a new run
    metaData.newDone = false;

    // If someone has hit the "force" button, restart/reset
    if (this.user.isRefreshRequested(this.typeFor())) metaData.restart();

    // This source does not do "history"; set history done to true
    if (!this.supports_history) this.setDataHistoryDone();

    if (logging.isDebug(this.user.profile)) DEBUG('ensureMetaData: %s metadata = %o', this._pluginKey, this.user.data_sources[this._pluginKey] ? this.user.data_sources[this._pluginKey][this.account] :  null);
  }

  async personById(id: Uid): Promise<Person> {
    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'personById', this.user.profile, `Looking up person ${id} in cache`);
    let person = this.user_people ? this.user_people[id] : null;
    if (!person) {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'personById', this.user.profile, `Looking up person ${id} in datastore`);
      person = await data.people.byId(this.user, id);
      if (person) {
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'personById', this.user.profile, `Adding person ${id} to cache from datastore`);
        if (person.comms && person.comms.includes(this.user.email)) person.self = true;
        this.user_people[id] = person;
        const emails = parsers.findEmail(person.comms);
        for (const email of emails) {
          if (this.user_emails[email]) this.user_emails[email].push(person.id);
          else this.user_emails[email] = [person.id];
        }
      }
    }
    return person;
  }

  async personByEmail(in_emails: string | string[]): Promise<Person> {
    const emails = in_emails ? parsers.findEmail(in_emails) : null;
    if (!emails || !emails.length) return null;

    let id: Uid;
    let person: Person;

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'personByEmail', this.user.profile, `Looking up person by email ${JSON.stringify(emails)} in cache`);
    if (this.user_emails) {
      for (const email of emails) {
        const ids = this.user_emails[email];
        if (ids) {
          for (id of ids) {
            const id_person: Person = await this.personById(id); 
            if (id_person) {
              if (person) {
                if (!person.self) peopleUtils.mergePeople(person, id_person, true);
              }
              else person = id_person;
            }
          }

          if (person) {
            if (person.id) this.user_emails[email] = [person.id]
            else logging.warnFP(this.log_name, 'personByEmail', this.user.profile, `Cannot cache merged person without id ${JSON.stringify(slimEntity(person))}`);
            break;
          } 
        }
      }
    }

    if (!person) {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'personByEmail', this.user.profile, `Looking up person by email ${JSON.stringify(emails)} in datastore`);
      const people = await data.people.byAttributeComms(this.user, emails);
      if (people) {
        for (const found_person of people) {
          if (person) {
            if (!person.self && !found_person.self) peopleUtils.mergePeople(person, found_person, true);
          } else person = found_person;

          if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'personByEmail', this.user.profile, `Adding person ${person.id} to cache from email in datastore`);
          if (person.comms && person.comms.includes(this.user.email)) person.self = true;
          person = this.addPerson(person);
        }
      }
    }

    if (!person) {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'personByEmail', this.user.profile, `Looking up person by email ${JSON.stringify(emails)} in people query`);
      const global_person = await data.people.matchByComms(this.user, emails);
      if (global_person) {
        person = new Person(global_person);
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'personById', this.user.profile, `Adding new person ${person.id} to cache from people query`);
        person.tempId();
        if (person.comms && person.comms.includes(this.user.email)) person.self = true;
        person = this.addPerson(person);
      }
    }

    return person;
  }

  addPerson(person: Person): Person {
    if (!person.id) {
      logging.warnFP(this.log_name, 'addPerson', this.user.profile, `Cannot cache person without id ${JSON.stringify(slimEntity(person))}`);
      return person;
    }

    person.updated = new Date();

    // check our temp person cache for identical matches
    if (!this.user_people[person.id]) {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'addPerson', this.user.profile, `Adding ${person.id} ${person.displayName} ${JSON.stringify(person.comms)}`);
      this.user_people[person.id] = person;
      const emails = parsers.findEmail(person.comms);
      if (emails && emails.length) {
        for (const email of emails) {
          if (!this.user_emails[email]) this.user_emails[email] = [person.id];
          else this.user_emails[email].push(person.id);
        }
      }
      if (!this.new_people_ids.includes(person.id)) {
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'addPerson', this.user.profile, `New person ${person.id} ${person.displayName} ${JSON.stringify(person.comms)}`);
        this.new_people_ids.push(person.id);
        this.new_people.push(person);
      }
    } else {
      if (!this.user_people[person.id].self) {
        const updated = peopleUtils.mergePeople(this.user_people[person.id], person, true);
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'addPerson', this.user.profile, `Updating ${person.id} ${person.displayName} ${JSON.stringify(person.comms)}`);
        if (updated && !this.new_people_ids.includes(person.id)) {
          this.new_people_ids.push(person.id);
          this.new_people.push(this.user_people[person.id]);
        }
      }

      person = this.user_people[person.id];

      const emails = parsers.findEmail(person.comms);
      for (const email of emails) {
        if (this.user_emails[email]) {
          if(!this.user_emails[email].includes(person.id)) this.user_emails[email].push(person.id);
        } else this.user_emails[email] = [person.id];
      }
    }

    return person;
  }

  async reload(entity: T): Promise<T> {
    return entity;
  }

  async batchReload(entities: Partial<T>[]): Promise<T[]> {
    return entities as T[];
  }

  protected setDataHistoryDone() {
    this.metaData().historyDone = true; // Goes through the setter
    this.metaData().historyPageToken = null; // Goes through the setter
  }

  protected setFirstRunDone() {
    this.metaData().firstRunDone = true; // Goes through the setter
  }

  public reset() {
    this.metaData().historyPageToken = null;
    this.metaData().nextPageToken = null;
    this.metaData().nextSyncToken = null;
    this.metaData().firstRunDone = false;
    this.metaData().historyDone = !this.supports_history;
    this.metaData().newDone = false;
    this.metaData().disabledError = false;
    this.metaData().enabled = true;
  }
}
