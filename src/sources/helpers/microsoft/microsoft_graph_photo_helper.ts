/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Contact, User } from '@microsoft/microsoft-graph-types';
import initials from 'initials';
import ForaUser from '../../../session/user';
import { NormalizedProviderSettings } from '../../../types/auth';
import { Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { AbstractMicrosoftGraphHelper } from './a_microsoft_graph_helper';

const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphPhotoHelper';

export default class MicrosoftGraphPhotoHelper extends AbstractMicrosoftGraphHelper {
  public static contactPhotoProxy(contact: Contact, display_name: string): string {
    let _initials = initials(display_name ? display_name : contact.displayName);
    // if (_initials && _initials.length > 2) _initials = _initials[0] + _initials[2];
    return `/api/contacts/${contact.id}/photo?fallback=${_initials}`;
  }

  static async photoStream(user: ForaUser, account: Uid, api_path: string, settings?: NormalizedProviderSettings): Promise<NodeJS.ReadableStream> {
    try {
      const client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account, settings);
      if (client) {
        const stream = await client
          .api(api_path)
          .version('v1.0')
          .getStream(null);

        logging.debugF(LOG_NAME, 'photoDataStream', `Photo stream retrieved for ${api_path}`);
        return stream;
      }
    } catch (err) {
      logging.warnF(LOG_NAME, 'photoDataStream', `Issue getting photo stream for ${api_path}`);
      throw err;
    }
  }

  public static userPhotoProxy(user: User, display_name: string): string {
    let _initials = initials(display_name ? display_name : user.displayName);
    // if (_initials && _initials.length > 2) _initials = _initials[0] + _initials[2];
    return `/api/users/${user.id}/photo?fallback=${_initials}`;
  }
}
