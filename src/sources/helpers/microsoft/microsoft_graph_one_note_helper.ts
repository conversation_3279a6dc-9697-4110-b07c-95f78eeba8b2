/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client, ResponseType } from '@microsoft/microsoft-graph-client';
import { Notebook, OnenotePage, OnenoteSection } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';
import ForaUser from '../../../session/user';
import { Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { AbstractMicrosoftGraphHelper } from './a_microsoft_graph_helper';

const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphOneNoteHelper';
export const NOTEBOOK_NAME = 'AskFora';
export const SECTION_NAME = 'Notes';

/**
 * Helper for all things Microsoft One Note
 *
 * Notes:
 * - Error Codes - https://docs.microsoft.com/en-us/graph/onenote-error-codes
 */
export default class MicrosoftGraphOneNoteHelper extends AbstractMicrosoftGraphHelper {
  static async oneNoteNotebookCreate(user: Fora<PERSON><PERSON>, account: Uid, client?: Client): Promise<OnenoteSection> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const request = client.api('/me/onenote/notebooks');
      const notebook: Notebook = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.post, request, { displayName: NOTEBOOK_NAME });

      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNoteNotebookCreate', user.profile, `Created notebook = ${util.format(notebook)}`);

      return notebook;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNoteNotebookCreate', user.profile, 'Error creating OneNote notebook', err);
      throw err;
    }
  }

  static async oneNoteNotebookDelete(user: ForaUser, account: Uid, client?: Client): Promise<void> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const notebook = await MicrosoftGraphOneNoteHelper.oneNoteNotebookGet(user, account, client);
      if (notebook) {
        const request = client.api(`/me/onenote/notebooks/${notebook.id}`);
        await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.delete, request);
        logging.infoFP(LOG_NAME, 'oneNoteNotebookDelete', user.profile, `Bucket deleted = ${notebook.id}`);
      } else logging.warnFP(LOG_NAME, 'oneNoteNotebookDelete', user.profile, `Attempted to delete a notebook that does not exist - ${user.profile}`);
    } catch (err) {
      if (err.code === '20113') return;
      // "The resource specified in the request has been deleted." - Silently succeed, trying to delete a resource that is already deleted.
      else {
        logging.errorFP(LOG_NAME, 'oneNoteNotebookDelete', user.profile, 'Error deleting OneNote notebook', err);
        throw err;
      }
    }
  }

  static async oneNoteNotebookEnsure(user: ForaUser, account: Uid, client?: Client): Promise<Notebook> {
    try {
      let notebook: Notebook = await MicrosoftGraphOneNoteHelper.oneNoteNotebookGet(user, account, client);
      if (!notebook) notebook = await MicrosoftGraphOneNoteHelper.oneNoteNotebookCreate(user, account, client);

      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNoteNotebookEnsure', user.profile, `Ensured notebook = ${util.format(notebook)}`);
      return notebook;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNoteNotebookEnsure', user.profile, 'Error ensuring OneNote notebook', err);
      throw err;
    }
  }

  static async oneNoteNotebookGet(user: ForaUser, account: Uid, client?: Client): Promise<OnenoteSection> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      const request = client.api('/me/onenote/notebooks');
      const response = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.get, request);

      const notebooks: Notebook[] = response.value;
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNoteNotebookGet', user.profile, `Notebooks = ${util.format(notebooks)}`);

      for (const notebook of notebooks) {
        if (notebook.displayName === NOTEBOOK_NAME) {
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNoteNotebookGet', user.profile, `Found notebook = ${util.format(notebook)}`);
          return notebook;
        }
      }

      logging.warnFP(LOG_NAME, 'oneNoteNotebookGet', user.profile, 'Notebook NOT found');
      return null;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNoteNotebookGet', user.profile, 'Error getting OneNote notebook', err);
      throw err;
    }
  }

  static async oneNotePageCreate(user: ForaUser, account: Uid, content: string, client?: Client): Promise<OnenotePage> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const section = await MicrosoftGraphOneNoteHelper.oneNoteSectionEnsure(user, account, client);

      // Create the page with just the JSON
      const request = client.api(`/me/onenote/sections/${section.id}/pages`).header('Content-Type', 'text/html');
      const page: OnenotePage = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.post, request, content);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNotePageCreate', user.profile, `Create page response = ${util.format(page)}`);

      // Update the title (with the newly given created time)
      await MicrosoftGraphOneNoteHelper.oneNotePageUpdateTitle(user, account, page.id, page.createdDateTime, client);

      // No need to re-read the page, just return a version with the title we just set
      // return await MicrosoftGraphOneNoteHelper.oneNotePageGet(user, page.id, client);
      page.title = page.createdDateTime;
      return page;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNotePageCreate', user.profile, 'Error creating page', err);
      throw err;
    }
  }

  static async oneNotePageDelete(user: ForaUser, account: Uid, page_id: string, client?: Client): Promise<void> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const request = client.api(`/me/onenote/pages/${page_id}`);
      await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.delete, request);
    } catch (err) {
      if (err.code === '20113') return;
      // "The resource specified in the request has been deleted." - Silently succeed, trying to delete a resource that is already deleted.
      else {
        logging.errorFP(LOG_NAME, 'oneNotePageDelete', user.profile, `Error deleting OneNote page ${page_id}`, err);
        throw err;
      }
    }
  }

  static async oneNotePageGet(user: ForaUser, account: Uid, page_id: string, client?: Client): Promise<OnenotePage> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      const request = client.api(`/me/onenote/pages/${page_id}`);
      const page = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.get, request);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNotePageGet', user.profile, `Got page = ${util.format(page)}`);
      return page;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNotePageGet', user.profile, `Error getting page ${page_id}`, err);
      throw err;
    }
  }

  static async oneNotePageGetContent(user: ForaUser, account: Uid, page_id: string, client?: Client): Promise<string> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      const request = client.api(`/me/onenote/pages/${page_id}/content?includeIDs=true`).responseType(ResponseType.TEXT);
      const content = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.get, request);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNotePageGetContent', user.profile, `Page raw content = '${util.format(content)}'`);
      return content;
    } catch (err) {
      let throw_err = err;
      if (!err.message && err.statusCode === 400) {
        // If the page does not exist, we get a really dumb 400 error with no message, lets use our own
        throw_err = new Error('Invalid Entity ID specified.');
        throw_err.code = 400;
      }
      logging.errorFP(LOG_NAME, 'oneNotePageGetContent', user.profile, `Error getting page content ${page_id}`, throw_err);
      throw throw_err;
    }
  }

  static async oneNotePages(user: ForaUser, account: Uid, client?: Client): Promise<OnenotePage[]> {
    // NOTE - default page size is 20 and this endpoint does not support `odata.maxpagesize=`
    let pages: OnenotePage[] = [];

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const section = await MicrosoftGraphOneNoteHelper.oneNoteSectionEnsure(user, account, client);

      let api_path = `/me/onenote/sections/${section.id}/pages`;
      while (api_path) {
        // If path being used is from nextLink, we don't need to apply the order by again
        let request;
        if (api_path.includes('createdDateTime')) request = client.api(api_path);
        else request = client.api(api_path).orderby('createdDateTime');

        const response = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.get, request);

        api_path = response['@odata.nextLink'];
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNotePages', user.profile, `Page list result ${util.format(response)}`);
        if (response.value) pages = pages.concat(response.value);
      }

      return pages;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNotePages', user.profile, 'Error reading OneNote pages', err);
      throw err;
    }
  }

  static async oneNotePageUpdateBody(user: ForaUser, account: Uid, page_id: string, body: string, client?: Client): Promise<void> {
    const changes = [
      {
        target: 'body',
        action: 'replace',
        content: body,
      },
    ];
    return MicrosoftGraphOneNoteHelper.oneNotePageUpdate(user, account, page_id, changes, client);
  }

  static async oneNotePageUpdateTitle(user: ForaUser, account: Uid, page_id: string, title: string, client?: Client): Promise<void> {
    const changes = [
      {
        target: 'title',
        action: 'replace',
        content: title,
      },
    ];
    return MicrosoftGraphOneNoteHelper.oneNotePageUpdate(user, account, page_id, changes, client);
  }

  static async oneNoteSectionCreate(user: ForaUser, account: Uid, client?: Client): Promise<OnenoteSection> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const notebook = await MicrosoftGraphOneNoteHelper.oneNoteNotebookEnsure(user, account, client);
      const request = client.api(`/me/onenote/notebooks/${notebook.id}/sections`);
      const section: OnenoteSection = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.post, request, { displayName: SECTION_NAME });
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNoteSectionCreate', user.profile, `Created section = ${util.format(section)}`);

      return section;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNoteSectionCreate', user.profile, 'Error creating OneNote section', err);
      throw err;
    }
  }

  static async oneNoteSectionEnsure(user: ForaUser, account: Uid, client?: Client): Promise<OnenoteSection> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      let section: OnenoteSection = await MicrosoftGraphOneNoteHelper.oneNoteSectionGet(user, account, client);
      if (!section) section = await MicrosoftGraphOneNoteHelper.oneNoteSectionCreate(user, account, client);

      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNoteSectionEnsure', user.profile, `Ensured section = ${util.format(section)}`);
      return section;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNoteSectionEnsure', user.profile, 'Error ensuring OneNote section', err);
      throw err;
    }
  }

  static async oneNoteSectionGet(user: ForaUser, account: Uid, client?: Client): Promise<OnenoteSection> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const request = client
        .api('/me/onenote/notebooks')
        .expand('sections')
        .expand('sectionGroups($expand=sections)');
      const response = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.get, request);

      const notebooks: Notebook[] = response.value;
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNoteSectionGet', user.profile, `Notebooks returned = ${util.format(notebooks)}`);

      for (const notebook of notebooks) {
        if (notebook.displayName === NOTEBOOK_NAME) {
          for (const section of notebook.sections) {
            if (section.displayName === SECTION_NAME) {
              if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNoteSectionGet', user.profile, `Found section = ${util.format(section)}`);
              return section;
            }
          }
        }
      }

      logging.warnFP(LOG_NAME, 'oneNoteSectionGet', user.profile, 'Section NOT found');
      return null;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNoteSectionGet', user.profile, 'Error getting OneNote section', err);
      throw err;
    }
  }

  private static async oneNotePageUpdate(user: ForaUser, account: Uid, page_id: string, changes: any[], client?: Client): Promise<void> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      const request = client.api(`/me/onenote/pages/${page_id}/content`).header('Content-Type', 'application/json');
      const response = await MicrosoftGraphOneNoteHelper.repeatableCallWithBackoff(request.patch, request, changes);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'oneNotePageUpdate', user.profile, `Update page = ${util.format(response)}`);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'oneNotePageUpdate', user.profile, `Error updating page ${page_id}`, err);
      throw err;
    }
  }
}
