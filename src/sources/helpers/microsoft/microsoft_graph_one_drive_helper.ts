/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client, ResponseType } from '@microsoft/microsoft-graph-client';
import { DriveItem } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';
import ForaUser from '../../../session/user';
import { Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { AbstractMicrosoftGraphHelper } from './a_microsoft_graph_helper';

const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphOneDriveHelper';

export default class MicrosoftGraphOneDriveHelper {
  static async fileContents(user: ForaUser, account: Uid, file_id: string, client?: Client): Promise<Buffer> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      // Get the file from OneDrive
      const stream = await client
        .api(`/me/drive/items/${file_id}/content`)
        .responseType(ResponseType.TEXT)
        .getStream(null);

      const read_promise = new Promise<Buffer>((resolve, reject) => {
        const buffers = [];
        stream.on('data', buffer => buffers.push(buffer));
        stream.on('end', () => resolve(Buffer.concat(buffers)));
        stream.on('error', reject);
      });
      const data = await read_promise;
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'fileContents', user.profile, `Data length = ${data.length}`);
      return data;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'fileContents', user.profile, `Error reading contents ${file_id}`, err);
      throw err;
    }
  }

  static async fileCreate(user: ForaUser, account: Uid, name: string, description: string, contents: string | Buffer, client?: Client): Promise<DriveItem> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      // Upload the file contents and create on the fly
      let file: DriveItem = await client.api(`/me/drive/special/approot:/${name}:/content`).put(contents);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'fileCreate', user.profile, `Content upload response = ${file}`);

      // Update the description
      file = await client.api(`/me/drive/items/${file.id}`).patch({ description } as DriveItem);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'fileCreate', user.profile, `Description update response = ${file}`);

      return file;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'fileCreate', user.profile, `Error creating file ${name}`, err);
      throw err;
    }
  }

  static async fileDelete(user: ForaUser, account: Uid, file_id: string, client?: Client): Promise<void> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      await client.api(`/me/drive/items/${file_id}`).delete();
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'fileDelete', user.profile, `File deleted ${file_id}`);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'fileDelete', user.profile, `Error deleting file ${file_id}`, err);
      throw err;
    }
  }

  static async fileMetadata(user: ForaUser, account: Uid, file_id: string, client?: Client): Promise<DriveItem> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      // Get the file metadata
      const file = await client.api(`/me/drive/items/${file_id}`).get();
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'fileMetadata', user.profile, `metadata = ${util.format(file)}`);
      return file;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'fileMetadata', user.profile, `Error getting file ${file_id}`, err);
      throw err;
    }
  }

  static async filesList(user: ForaUser, account: Uid, client?: Client): Promise<DriveItem[]> {
    try {
      let files: DriveItem[] = [];

      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      let api_path = '/me/drive/special/approot/children';
      while (api_path) {
        // NOTE - default page size is 200
        const response = await client.api(api_path).get();
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'filesList', user.profile, `File list page response = ${util.format(response)}`);

        api_path = response['@odata.nextLink'];
        if (response.value) files = files.concat(response.value);
      }

      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'filesList', user.profile, `File list = ${util.format(files)}`);
      return files;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'filesList', user.profile, 'Error reading list of files', err);
      throw err;
    }
  }

  static async fileUpdate(user: ForaUser, account: Uid, file_id: string, description: string, contents: string | Buffer, client?: Client): Promise<DriveItem> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      // Upload the file contents and create on the fly
      let file: DriveItem = await client.api(`/me/drive/items/${file_id}/content`).put(contents);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'fileUpdate', user.profile, `Content upload response = ${file}`);

      // Update the description
      file = await client.api(`/me/drive/items/${file.id}`).patch({ description } as DriveItem);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'fileUpdate', user.profile, `Description update response = ${file}`);

      return file;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'fileUpdate', user.profile, `Error creating file ${file_id}`, err);
      throw err;
    }
  }
}
