/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Attendee, Contact, DriveItem, EmailAddress, Event as GraphEvent, Message as MicrosoftMessage, Recipient, TodoTask, User } from '@microsoft/microsoft-graph-types';
import cheerio from 'cheerio';
import moment from 'moment-timezone';
import { Token } from 'simple-oauth2';
import util from 'util';

import ForaUser from '../../../session/user';

import { NormalizedProviderSettings, NormalizedProviderToken } from '../../../types/auth';
import { Document, Event, Message, Person, Tag, Task } from '../../../types/items';
import { AuthLevel, AuthProviders, TagType, findTypeValues } from '../../../types/shared';

import { normalizeId, saveOne, saveOneTypeValue, stringIncludesAll } from '../../../utils/funcs';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';

import { TaskStatus } from '../../tasks/i_tasks_source_plugin';
import MicrosoftGraphPhotoHelper from './microsoft_graph_photo_helper';

const DEBUG = (require('debug') as any)('fora:sources:helpers:microsoft:conversion');
const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphConverter';

export class MicrosoftGraphConverter {
  static convertEventFromGraphEvent(calendar_id: string, ms_event: GraphEvent): Event {
    const event = new Event({
      id: ms_event.id,
      calendarId: calendar_id,
      title: ms_event.subject,
      notes: '',
      start: ms_event.start ? moment.tz(ms_event.start.dateTime, ms_event.start.timeZone).toDate() : null,
      end: ms_event.end ? moment.tz(ms_event.end.dateTime, ms_event.end.timeZone).toDate() : null,
      link: ms_event.webLink,
      location: ms_event.location ? ms_event.location.displayName : null,
      htmlLink: ms_event.webLink,
    });

    // If there is a body, find the "PlainText" div and grab all the content out of that if the type is HTML
    if (ms_event.body) event.notes = ms_event.body.contentType === 'html' ? this.parseEventHtml(ms_event.body.content) : ms_event.body.content;

    return event;
  }

  static convertEventToGraphEvent(f_event: Event): GraphEvent {
    const attendees = [];
    for (const index in f_event.people) {
      const person = f_event.people[index];

      let email = person.email;
      if (!email && person.comms) {
        for (const com of person.comms) {
          const emails = parsers.findEmail(com);
          if (emails && emails.length) {
            email = emails[0];
            break;
          }
        }
      }

      if (email) attendees.push({ emailAddress: { address: email } } as Attendee);
      f_event.people[index] = { id: person.id, displayName: person.displayName, email } as Person;
    }

    return {
      id: f_event.id,
      subject: f_event.title,
      start: { dateTime: new Date(f_event.start).toISOString(), timeZone: 'UTC' },
      end: { dateTime: new Date(f_event.end).toISOString(), timeZone: 'UTC' },
      location: { displayName: f_event.location },
      body: { contentType: 'html', content: f_event.notes },
      attendees,
    } as GraphEvent;
  }

  static convertMessageFromGraphMessage(user: ForaUser, ms_message: MicrosoftMessage): Message {
    const f_message = new Message({
      id: ms_message.id,
      received: new Date(ms_message.receivedDateTime ? ms_message.receivedDateTime : 0),
      read: ms_message.isRead,
      draft: ms_message.isDraft,
      subject: ms_message.subject,
      link: ms_message.webLink,
      recipient: [],
    });

    if (ms_message.isDraft) {
      const fake_sender = { emailAddress: { name: user.name, address: user.email } } as Recipient;
      if (!ms_message.sender) ms_message.sender = fake_sender;
    }

    // Map the recipients and check for SPAM
    if (logging.isDebug()) DEBUG('convertMessageFromGraphMessage: incoming sender = %o', ms_message.sender);
    if (ms_message.sender) f_message.sender = MicrosoftGraphConverter.convertPersonFromRecipient(ms_message.sender);

    // These fields are not always given (#813, #814)
    if (ms_message.toRecipients) for (const recipient of ms_message.toRecipients) f_message.recipient.push(MicrosoftGraphConverter.convertPersonFromRecipient(recipient));
    if (ms_message.ccRecipients) for (const recipient of ms_message.ccRecipients) f_message.recipient.push(MicrosoftGraphConverter.convertPersonFromRecipient(recipient));
    if (ms_message.bccRecipients) for (const recipient of ms_message.bccRecipients) f_message.recipient.push(MicrosoftGraphConverter.convertPersonFromRecipient(recipient));

    // Filter out any recipients that don't have an email address (Not sure how but we are seeing this in real world. #734)
    f_message.recipient = f_message.recipient.filter(r => r.email);

    return f_message;
  }

  // legacy Microsoft provider only
  static convertNormalizedProviderTokenFromToken(token: Token, scope_levels: {[key:string] : string[]}): NormalizedProviderToken {
    /*
     * { token_type: 'Bearer',
     *   scope: 'email openid profile User.Read Calendars.ReadWrite Contacts.ReadWrite Tasks.ReadWrite',
     *   expires_in: 3600,
     *   ext_expires_in: 3600,
     *   access_token: 'ABC123',
     *   refresh_token: '123ABC',
     *   id_token: '',
     *   expires_at: 2018-12-03T01:07:08.541Z
     * }
     */


    const permissions = stringIncludesAll(token['scope'] as string, scope_levels[AuthLevel.OrganizerSync]) ? AuthLevel.OrganizerSync:
      stringIncludesAll(token['scope'] as string, scope_levels[AuthLevel.Organizer]) ? AuthLevel.Organizer: AuthLevel.Basic;

    const npt = new NormalizedProviderToken(AuthProviders.Microsoft, token['access_token'] as string, permissions);
    npt.expires_at = new Date(token['expires_at'] as any);
    npt.id_token = token['id_token'] as string;
    npt.refresh_token = token['refresh_token'] as string;
    npt.scope = token['scope'] as string;
    npt.token_type = token['token_type'] as string;

    return npt;
  }

  /**
   * Convert a Microsoft Contact to an AskFora Person
   *
   * @param contact - Microsoft Graph Contact to convert
   * @param tokens - Tokens to use (to grab additional information)
   * @param settings - Normalized provider settings
   * @param locale - Optional locale to use during conversion (not currently used, future)
   * @param start - Optional start date to use during conversion
   */
  static convertPersonFromContact(contact: Contact, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings, locale?: string, start: Date = new Date()): Person {
    let display = null;
    let first = null;
    let last = null;
    let tag_index = 0;
    const names = []; // all name permutations
    const tags = []; // company, skills, etc. {type:,value:}
    const comms = []; // emails, ims, phones
    const urls = []; // fb, twitter, LI
    const photos = [];

    //
    // Name information
    if(contact.fileAs && contact.fileAs.length) { 
      saveOne(names, contact.fileAs);
      if (!display) display = contact.fileAs;
    }

    if (contact.displayName && contact.displayName.length) {
      saveOne(names, contact.displayName);
      if (!display) display = contact.displayName;
    }

    if (contact.givenName && contact.givenName.length) {
      saveOne(names, contact.givenName);
      first = contact.givenName;
    }
    if (contact.surname && contact.surname.length) {
      saveOne(names, contact.surname);
      last = contact.surname;
    }
    if (contact.nickName && contact.nickName.length) {
      saveOne(names, contact.nickName);
      if (!first) first = contact.nickName;
    }

    if (first && last) {
      saveOne(names, `${first} ${last}`);
      if (!display) display = `${first} ${last}`;
    }

    if (!display && first) display = first;
    if (!display && last)  display = last;

    //
    // Organization information
    const orgs = new Set();
    const org_start = start;

    if (contact.companyName !== null && contact.companyName) {
      if (display === null) {
        saveOne(names, contact.companyName);
        orgs.add(contact.companyName);
        if (display === null) display = contact.companyName;
      }
      saveOneTypeValue(tags, new Tag(TagType.organization, contact.companyName, tag_index, org_start));
    }

    if (contact.department) {
      saveOneTypeValue(tags, new Tag(TagType.department, contact.department, tag_index, org_start));
      orgs.add(contact.department);
    }
    if (contact.jobTitle) {
      saveOneTypeValue(tags, new Tag(TagType.jobTitle, contact.jobTitle, tag_index, org_start));
      orgs.add(contact.jobTitle);
    }

    //
    // Skills
    if (contact.profession) {
      saveOneTypeValue(tags, new Tag(TagType.industry, contact.profession, tag_index, start));
      const meanings = parsers.findMeaning(contact.profession);
      for (const meaning of meanings) {
        if (names.indexOf(meaning) === -1 && !orgs.has(meaning)) {
          saveOneTypeValue(tags, new Tag(TagType.skill, meaning.toLowerCase(), 100, start, 100));
        }
      }
    }

    //
    // Phones
    if (contact.businessPhones) {
      for (const phone of contact.businessPhones) {
        saveOne(comms, phone);
      }
    }
    if (contact.homePhones) {
      for (const phone of contact.homePhones) {
        saveOne(comms, phone);
      }
    }
    saveOne(comms, contact.mobilePhone);

    //
    // Email Addresses
    if (contact.emailAddresses) {
      for (const email of contact.emailAddresses) {
        saveOne(comms, email.address ? email.address.toLowerCase() : null);
      }
    }

    const id = `people/m${normalizeId(contact.id)}`;
    if (id) saveOne(comms, id);

    // need at least one tag for skill queries to work
    if (!tags.length) tags.push(new Tag(TagType.skill));

    //
    // Photo information
    saveOne(photos, MicrosoftGraphPhotoHelper.contactPhotoProxy(contact, display));

    const person = new Person({
      id,
      displayName: display ? display : 'No Name',
      nickName: first,
      names,
      comms:comms.filter(c => !parsers.spamAddress(c)),
      tags,
      urls,
      photos,
    });
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'convertPersonFromContact', `person = ${util.format(person)}`);
    return person;
  }

  static convertPersonFromRecipient(recipient: Recipient) {
    const person = new Person({
      displayName: recipient.emailAddress.name,
      comms: [recipient.emailAddress.address],
      email: recipient.emailAddress.address,
    });

    if (!person.email || !person.displayName) {
      logging.warnF(LOG_NAME, 'convertPersonFromRecipient', `Ended up with invalid person? - [name=${person.displayName}][email=${person.email}][recipient=${JSON.stringify(recipient)}]`);
    }

    return person;
  }

  /**
   * Convert a Microsoft User to an AskFora Person
   *
   * @param user - Microsoft Graph User to convert
   * @param tokens - Tokens to use (to grab additional information)
   * @param settings - Normalized provider settings
   * @param locale - Optional locale to use during conversion (not currently used, future)
   * @param start - Optional start date to use during conversion
   */
  static convertPersonFromUser(user: User, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings, locale?: string, start: Date = new Date()): Person {
    let display = user.displayName;
    let tag_index = 0;
    const names = []; // all name permutations
    const tags = []; // company, skills, etc. {type:,value:}
    const comms = []; // emails, ims, phones
    const urls = []; // fb, twitter, LI
    const photos = [];

    //
    // Name information
    saveOne(names, user.displayName);
    saveOne(names, user.givenName);
    saveOne(names, user.surname);
    saveOne(names, user.preferredName);

    // Use the preferred name if they gave one
    if (user.preferredName) display = user.preferredName;

    // Add a `Joe C` record for Joe Cavanaugh
    if (user.givenName && user.surname) saveOne(names, `${user.givenName} ${user.surname[0]}`);

    //
    // Organization information
    const orgs = new Set();
    let org_start = start;
    if (user.hireDate) org_start = new Date(user.hireDate);

    if (user.companyName !== null && user.companyName) {
      if (display === null) {
        saveOne(names, user.companyName);
        orgs.add(user.companyName);
        display = user.companyName;
      }
      saveOneTypeValue(tags, new Tag(TagType.organization, user.companyName, tag_index, org_start ));
    }

    if (user.department) {
      saveOneTypeValue(tags, new Tag(TagType.department, user.department, tag_index, org_start ));
      orgs.add(user.department);
    }
    if (user.jobTitle) {
      saveOneTypeValue(tags, new Tag(TagType.jobTitle,  user.jobTitle, tag_index, org_start ));
      orgs.add(user.jobTitle);
    }

    if (user.responsibilities) {
      for (const responsibility of user.responsibilities) {
          saveOneTypeValue(tags, new Tag(TagType.jobDescription, responsibility, tag_index, org_start ));
        const meanings = parsers.findMeaning(responsibility);
        for (const meaning of meanings) {
          if (names.indexOf(meaning) === -1 && !orgs.has(meaning)) {
            saveOneTypeValue(tags, new Tag(TagType.skill, meaning.toLowerCase(), tag_index, org_start, 10 ));
          }
          orgs.add(meaning);
        }
      }
    }

    if (user.pastProjects) {
      for (const project of user.pastProjects) {
        const meanings = parsers.findMeaning(project);
        for (const meaning of meanings) {
          if (names.indexOf(meaning) === -1 && !orgs.has(meaning)) {
            saveOneTypeValue(tags, new Tag(TagType.skill, meaning.toLowerCase(), tag_index, org_start, 10 ));
          }
          orgs.add(meaning);
        }
      }
    }

    if (user.onPremisesDomainName) {
      saveOneTypeValue(tags, new Tag(TagType.domain,  user.onPremisesDomainName, tag_index, org_start ));
      orgs.add(user.onPremisesDomainName);
    }

    //
    // URL information
    saveOne(urls, user.mySite);

    //
    // Skills
    if (user.skills) {
      for (const skill of user.skills) {
        saveOneTypeValue(tags, new Tag(TagType.skill,  skill.toLowerCase(), tag_index, start, 100));
        const meanings = parsers.findMeaning(skill);
        for (const meaning of meanings) {
          if (names.indexOf(meaning) === -1 && !orgs.has(meaning)) {
            tag_index++;
            saveOneTypeValue(tags, new Tag(TagType.skill,  meaning.toLowerCase(), tag_index, start, 30));
          }
        }
      }
    }

    //
    // Interests
    if (user.interests) {
      for (const interest of user.interests) {
        const meanings = parsers.findMeaning(interest);
        for (const meaning of meanings) {
          if (names.indexOf(meaning) === -1 && !orgs.has(meaning)) {
            tag_index++;
            saveOneTypeValue(tags, new Tag(TagType.skill, meaning.toLowerCase(), tag_index, start, 50));
          }
        }
      }
    }

    //
    // Phones
    if (user.businessPhones) {
      for (const businessPhone of user.businessPhones) {
        saveOne(comms, businessPhone);
      }
    }
    saveOne(comms, user.mobilePhone);

    //
    // Email Addresses
    saveOne(comms, user.mail ? user.mail.toLowerCase() : null);
    saveOne(comms, user.mailNickname ? user.mailNickname.toLowerCase() : null);
    saveOne(comms, user.userPrincipalName ? user.userPrincipalName.toLowerCase() : null);

    const id = `people/r${normalizeId(user.id)}`;
    if (id) saveOne(comms, id);

    // need at least one tag for skill queries to work
    if (!tags.length) tags.push(new Tag(TagType.skill));

    //
    // Photo information
    saveOne(photos, MicrosoftGraphPhotoHelper.userPhotoProxy(user, display));

    const person = new Person({
      id,
      displayName: display ? display : user.userPrincipalName,
      nickName: user.givenName,
      email: user.userPrincipalName ? user.userPrincipalName.toLowerCase() : null,
      self: true,
      names,
      comms,
      tags,
      urls,
      photos,
    });
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'convertPersonFromUser', `person = ${util.format(person)}`);
    return person;
  }

  /**
   * Convert a Person to a Microsoft Contact
   *
   * @param person - The existing AskFora Person
   * @return newly created Contact
   */
  static convertPersonToContact(person: Person): Contact {
    const contact: Contact = {};

    // names
    const [given, middle, family] = person.displayNameToParts();
    contact.givenName = given;
    contact.surname = family;
    contact.middleName = middle;
    contact.nickName = person.nickName;

    // organization
    MicrosoftGraphConverter.mapToContact(contact, person, TagType.occupation, 'profession');
    MicrosoftGraphConverter.mapToContact(contact, person, TagType.organization, 'companyName');
    MicrosoftGraphConverter.mapToContact(contact, person, TagType.department, 'department');
    MicrosoftGraphConverter.mapToContact(contact, person, TagType.jobTitle, 'jobTitle');

    const emails = parsers.findEmail(person.comms);
    const phones = parsers.findPhone(person.comms);

    // max 3 emails for msft
    contact.emailAddresses = emails.slice(0,3).map(email => { return { name: person.displayName, address: email } as EmailAddress});
    contact.businessPhones = phones;
    contact.imAddresses = person.comms.filter(c => {
      const phone = parsers.findPhone(c);
      return !emails.includes(c) && (!phone.length || !phones.includes(phone[0])) && !parsers.findId(c)
    }).map(c => c.toLowerCase());

    return contact;
  }

  static convertPersonToRecipient(person: Partial<Person>, email_override?: string): Recipient {
    const comm_emails = person.comms ? parsers.findEmail(person.comms) : [];

    const email = email_override ? email_override : person.email ? person.email : comm_emails.length ? comm_emails[0] : undefined;

    const recipient = {} as Recipient;
    recipient.emailAddress = {} as EmailAddress;
    recipient.emailAddress.name = person.displayName;
    recipient.emailAddress.address = email;

    return recipient;
  }

  /**
   * Convert a Fora task from a Microsoft task
   *
   * @param ms_task - the Microsoft task to convert
   * @param task_list_id - the id of the Microsoft task list it is part of
   * @returns Converted task
   * @private
   */
  static convertTaskFromTodoTask(user: ForaUser, task_list_id: string, ms_task: TodoTask, f_task_in: Task): Task {
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'convertTaskFromTodoTask', `todo task = ${util.format(ms_task)}`);

    const f_task_out = new Task({
      completed: ms_task.completedDateTime ? moment.tz(ms_task.completedDateTime.dateTime, ms_task.completedDateTime.timeZone).toDate() : null,
      created: new Date(ms_task.createdDateTime),
      due: ms_task.dueDateTime ? moment.tz(ms_task.dueDateTime.dateTime, ms_task.dueDateTime.timeZone).toDate() : null,
      id: ms_task.id,
      people: [], //ms_task.assignedTo ? JSON.parse(ms_task.assignedTo) : [],
      status: ms_task.status === 'completed' ? TaskStatus.completed : TaskStatus.needsAction,
      taskListId: task_list_id,
      title: ms_task.title,
      notes: ms_task.body ? ms_task.body.content : null,
      updated: new Date(ms_task.lastModifiedDateTime),
      project: f_task_in ? f_task_in.project : null,
    });

    if (f_task_in) {
      // The POST method always ignores the time portion of startDateTime and dueDateTime in the request body, and assumes the time to be always
      // midnight in the specified time zone. So we are using what we knew about
      if (f_task_in.due && new Date(f_task_in.due).getTime()) f_task_out.due = f_task_in.due;

      // Use the people as we knew them - saves us in case someone hand edits the notes within the Microsoft UI.
      f_task_out.people = f_task_in.people;
      f_task_out.project = f_task_in.project;
    } else {
      logging.warnFP(LOG_NAME, 'convertTaskFromTodoTask', user.profile, `Unable to find matching task in DS with ID '${ms_task.id}', using all values from Microsoft`);
    }

    return f_task_out;
  }

  /**
   * Convert a Fora task to a Microsoft task
   *
   * @param f_task - the Fora task to convert
   * @param update_updated - update the last modified field in the Microsoft task?
   * @returns Converted task
   * @private
   *
   * @example Microsoft Task:
   *     { '@odata.etag': 'W/"g+LTLwogwEWaPi4or0WKBgACBrJwZA=="',
   *       id: 'AQMkADAwATM0MDAAMS04MDYyLTRhNmMtMDACLTAwCgBGAAAD6Q_PSvNrUkmiHtUZxcMMxAcAg_LTLwogwEWaPi4or0WKBgACA3W9TQAAAIPi0y8KIMBFmj4uKK9FigYAAgamW_AAAAA=',
   *       createdDateTime: '2018-12-07T03:00:25.6805437Z',
   *       lastModifiedDateTime: '2018-12-07T03:00:25.8026305Z',
   *       changeKey: 'g+LTLwogwEWaPi4or0WKBgACBrJwZA==',
   *       categories: [],
   *       assignedTo: '',
   *       hasAttachments: false,
   *       importance: 'normal',
   *       isReminderOn: false,
   *       owner: 'Joe Cavanaugh',
   *       parentFolderId: 'AQMkADAwATM0MDAAMS04MDYyLTRhNmMtMDACLTAwCgAuAAAD6Q_PSvNrUkmiHtUZxcMMxAEAg_LTLwogwEWaPi4or0WKBgACA3W9TQAAAA==',
   *       sensitivity: 'normal',
   *       status: 'notStarted',
   *       subject: 'Test Task within AskFora list',
   *       completedDateTime: null,
   *       dueDateTime: null,
   *       recurrence: null,
   *       reminderDateTime: null,
   *       startDateTime: null,
   *       body: {
   *               contentType: 'text',
   *               content: ''
   *             }
   *     }
   */
  static convertTaskToTodoTask(f_task: Task, update_updated: boolean): TodoTask {
    const ms_task = {
      title: f_task.title,
      status: f_task.status ? (f_task.status === TaskStatus.completed ? 'completed' : 'notStarted') : 'notStarted',
      body: {
        contentType: 'html',
        content: f_task.notes,
      }
    } as TodoTask;

    // parentFolderId is read-only - see https://github.com/microsoftgraph/microsoft-graph-docs/issues/2176

    if (f_task.completed) ms_task.completedDateTime = { dateTime: new Date(f_task.completed).toISOString(), timeZone: 'UTC' };
    if (f_task.created) ms_task.createdDateTime = new Date(f_task.created).toISOString();
    if (f_task.due) ms_task.dueDateTime = { dateTime: new Date(f_task.due).toISOString(), timeZone: 'UTC' };
    if (f_task.id) ms_task.id = f_task.id;

    if (update_updated) ms_task.lastModifiedDateTime = f_task.updated ? f_task.updated.toISOString() : new Date().toISOString();

    // NOTE - not using ms_task.body because it only supports HTML
    // if (f_task.people && f_task.people.length) ms_task.assignedTo = JSON.stringify(f_task.people);

    return ms_task;
  }

  static documentFromDriveItem(file: DriveItem, contents?: Buffer) {
    let props;
    try {
      props = JSON.parse(file.description);
    } catch (err) {
      props = { id: file.id };
    }

    // doc['@microsoft.graph.downloadUrl'] = file['@microsoft.graph.downloadUrl'];
    return new Document({
      id: props.id,
      file_id: file.id,
      created: new Date(file.createdDateTime),
      body: contents,
      props,
    });
  }

  /**
   * Update the provided Contact with information from the Person
   *
   * @param contact - The existing Microsoft Contact
   * @param person - The existing AskFora Person
   */
  static updateContactFromPerson(contact: Contact, person: Person): boolean {
    let changed = false;

    // names
    const [given, middle, family] = person.displayNameToParts();
    if (contact.givenName !== given) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Changing given name from ${contact.givenName} to ${given}`);
      changed = true;
      contact.givenName = given;
    }
    if (contact.surname !== family) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Changing surname from ${contact.surname} to ${family}`);
      changed = true;
      contact.surname = family;
    }
    if (contact.middleName !== middle) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Changing middle ame from ${contact.middleName} to ${middle}`);
      changed = true;
      contact.middleName = middle;
    }
    if (contact.nickName !== person.nickName) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Changing nick name from ${contact.nickName} to ${person.nickName}`);
      changed = true;
      contact.nickName = person.nickName;
    }

    // organization
    if (MicrosoftGraphConverter.mapToContact(contact, person, TagType.occupation, 'profession')) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Changed profession`);
      changed = true;
    }
    if (MicrosoftGraphConverter.mapToContact(contact, person, TagType.organization, 'companyName')) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Changed companyName`);
      changed = true;
    }
    if (MicrosoftGraphConverter.mapToContact(contact, person, TagType.department, 'department')) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Changed department`);
      changed = true;
    }
    if (MicrosoftGraphConverter.mapToContact(contact, person, TagType.jobTitle, 'jobTitle')) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Changed jobTitle`);
      changed = true;
    }

    const emails = parsers.findEmail(person.comms);
    const phones = parsers.findPhone(person.comms);
    const im_clients = person.comms.map(c => c.toLowerCase()).filter(c => {
      const phone = parsers.findPhone(c);
      return !emails.includes(c) && (!phone.length || !phones.includes(phone[0])) && !parsers.findId(c)
    });

    // emailAddresses
    if (emails) {
      if (contact.emailAddresses) {
        const remove: number[] = [];
        for (let index = 0; index < contact.emailAddresses.length; index++) {
          const email = contact.emailAddresses[index];
          if (email.address) {
            const email_index = emails.indexOf(email.address.toLowerCase());
            if (email_index !== -1) emails.splice(email_index, 1);
            else remove.push(index);
          }
        }

        if (remove.length) {
          if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Removing ${remove.length} emails`);
          changed = true;
          contact.emailAddresses = contact.emailAddresses.filter((e,i) => !remove.includes(i));
        }
      } else if (emails.length) contact.emailAddresses = [];

      // Any emails that the Person had that the Contact didn't have?
      if (emails.length) {
        if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Adding ${emails.length} emails`);
        changed = true;
        contact.emailAddresses = contact.emailAddresses.concat(emails.map(email => { return { name: person.displayName, address: email.toLowerCase() } as EmailAddress}));
      }
    } 

    if (phones) {
      if (contact.businessPhones) {
        const remove: number[] = [];
        for (let index = 0; index < contact.businessPhones.length; index++) {
          const phone = contact.businessPhones[index];
          const phone_index = phones.indexOf(phone);
          if (phone_index !== -1) phones.splice(phone_index, 1);
          else remove.push(index);
        }

        if(remove.length) {
          if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Removing ${remove.length} business phones`);
          changed = true;
          contact.businessPhones = contact.businessPhones.filter((p,i) => !remove.includes(i));
        }
      } else if (phones.length) contact.businessPhones = [];

      if (contact.homePhones) {
        const remove: number[] = [];
        for (let index = 0; index < contact.homePhones.length; index++ ) {
          const phone = contact.homePhones[index];
          const phone_index = phones.indexOf(phone);
          if (phone_index !== -1) phones.splice(phone_index, 1);
          else remove.push(index);
        }

        if(remove.length) {
          if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Removing ${remove.length} home phones`);
          changed = true;
          contact.homePhones = contact.homePhones.filter((p,i) => !remove.includes(i));
        }
      } else if (phones.length) contact.homePhones = [];

      // Any phones that the Person had that the Contact didn't have?
      if (phones.length) {
        if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Adding ${phones.length} business phones`);
        contact.businessPhones = contact.businessPhones.concat(phones);
      }
    } 

    if (im_clients) {
      if (contact.imAddresses) {
        const remove: number[] = [];
        for (let index = 0; index < contact.imAddresses.length; index++) {
          const imAddress = contact.imAddresses[index];
          const client_index = im_clients.indexOf(imAddress);
          if (client_index !== -1) im_clients.splice(client_index, 1);
          else remove.push(index);
        }

        if(remove.length) {
          if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Removing ${remove.length} im addresses`);
          changed = true;
          contact.imAddresses = contact.imAddresses.filter((m,i) => !remove.includes(i));
        }
      } else if (im_clients.length) contact.imAddresses = [];

      // Any IM addresses that the Person had that the Contact didn't have?
      if (im_clients.length) {
        changed = true;
        if (logging.isDebug()) logging.debugF(LOG_NAME, 'updateContactFromPerson', `Adding ${im_clients.length} im addresses`);
        contact.imAddresses = contact.imAddresses.concat(im_clients);
      }
    }

    return changed;
  }

  private static mapToContact(contact: Contact, person: Person, tag_name: TagType, field_name: string): boolean {
    const tags = findTypeValues(person.tags, tag_name);
    if (tags && tags.length) {
      if (contact[field_name] !== tags[0]) {
        if (logging.isDebug()) logging.debugF(LOG_NAME, 'mapToContact', `Changing ${field_name} from ${contact[field_name]} to ${tag_name} ${tags[0]}`);
        contact[field_name] = tags[0];

        return true;
      } else {
        return false;
      }
    }
  }

  private static parseEventHtml(content_html: string) {
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'parseEventHtml', `original meeting content (html): ${content_html}`);

    let parsed = '';
    if (content_html) {
      const $ = cheerio.load(content_html);
      parsed = $('div.PlainText').text();
    }

    if (logging.isDebug()) logging.debugF(LOG_NAME, 'parseEventHtml', `parsed meeting content (text): ${parsed}`);
    return parsed;
  }
}
