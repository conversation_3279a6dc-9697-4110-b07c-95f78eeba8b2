# Microsoft Utils

## Error Codes

- https://docs.microsoft.com/en-us/graph/onenote-error-codes

### Known Exceptions

#### 400

##### Incorrect or not found file

````javascript
error = { statusCode: 400,
  code: '-1, Microsoft.SharePoint.Client.InvalidClientQueryException',
  message: 'The expression "drive/items/gC7vO9ekdZxyTsMXz136mb-YT52GtRVZIbIf1XolvCM" is not valid.',
  requestId: '541dd0d1-8ed1-4591-b769-7cc2794ecc43',
  date: 2019-01-28T23:50:24.000Z,
  body:
   { code: '-1, Microsoft.SharePoint.Client.InvalidClientQueryException',
     message: 'The expression "drive/items/gC7vO9ekdZxyTsMXz136mb-YT52GtRVZIbIf1XolvCM" is not valid.',
     innerError:
      { 'request-id': '541dd0d1-8ed1-4591-b769-7cc2794ecc43',
        date: '2019-01-28T16:50:24' } } }
        ```

#### 403

##### Generic no permission

```javascript
error = {
  statusCode: 403,
  code: 'accessDenied',
  message: 'The caller does not have permission to perform the action.',
  requestId: '5ea08623-8aba-4550-987f-12e47e61e5fd',
  date: '2019-01-26T06:28:20.000Z',
  body: {
    code: 'accessDenied',
    message: 'The caller does not have permission to perform the action.',
    innerError: {
      'request-id': '5ea08623-8aba-4550-987f-12e47e61e5fd',
      date: '2019-01-25T23:28:20',
    },
  },
};
````

#### 404

##### Not found because its deleted

```javascript
error = {
  statusCode: 404,
  code: '20113',
  message: 'The specified resource has been deleted.',
  requestId: 'df82016a-c4d8-4c1d-8be3-c0b69524cf58',
  date: '2019-01-12T06:55:06.000Z',
  body: {
    code: '20113',
    message: 'The specified resource has been deleted.',
    innerError: {
      'request-id': 'df82016a-c4d8-4c1d-8be3-c0b69524cf58',
      date: '2019-01-11T23:55:06',
    },
  },
};
```

##### Not found because its not permitted

```javascript
error = {
  statusCode: 404,
  code: '30105',
  message: 'OneDrive for Business is not provisioned for this user.',
  requestId: '8e605544-bd95-42d5-8d49-c8115485ac6f',
  date: '2019-01-12T06:55:06.000Z',
  body: {
    code: '30105',
    message: 'OneDrive for Business is not provisioned for this user.',
    innerError: {
      'request-id': '8e605544-bd95-42d5-8d49-c8115485ac6f',
      date: '2019-01-11T23:55:06',
    },
  },
};
```

##### File not found

````javascript
error = { statusCode: 404,
  code: '-1, Microsoft.SharePoint.Client.ResourceNotFoundException',
  message: 'Cannot find resource for the request ujNcBkR9ZUy9MewB1ntjjiE7T_xoMdBjEPDMoHR23vA.',
  requestId: 'f360f4b3-f39d-4520-92df-6045d0574ff7',
  date: 2019-01-28T22:46:12.000Z,
  body:
   { code: '-1, Microsoft.SharePoint.Client.ResourceNotFoundException',
     message: 'Cannot find resource for the request ujNcBkR9ZUy9MewB1ntjjiE7T_xoMdBjEPDMoHR23vA.',
     innerError:
      { 'request-id': 'f360f4b3-f39d-4520-92df-6045d0574ff7',
        date: '2019-01-28T15:46:12' } } }
        ```

#### 429 Throttling

##### Short Form
```javascript
error = {
  statusCode: 429,
  timestamp:  "2019-01-18T16:11:44Z"
}
````

##### Long Form

```javascript
error = {
  statusCode: 429,
  code: 20166,
  message: 'The app has issued too many requests on behalf of this user in a short time period.',
  requestId: '8f3d172c-1148-4a02-ae17-234648e3cfd7',
  date: 'Fri Jan 18 2019 16:11:43 GMT+0000 (UTC)',
  body: {
    code: 20166,
    message: 'The app has issued too many requests on behalf of this user in a short time period.',
    innerError: {
      'request-id': '8f3d172c-1148-4a02-ae17-234648e3cfd7',
      date: '2019-01-18T16:11:43',
    },
  },
};
```




    // Examples:
    //    wreck/lib/index.js:629:11
    //      output: {
    //        payload: {
    //          error: 'invalid_grant',
    //          error_description: 'AADSTS70000: The request was denied because one or more scopes requested
    //                             are unauthorized or expired. The user must first sign in and grant the client
    //                             application access to the requested scope.\r\nTrace ID:
    //                             ba5800c7-86d5-47ee-9569-3c70b8110e00\r\nCorrelation ID: 3fbc32b2-220a-4087-9caa-7cf16d86a4de\r\nTimestamp: 2019-01-30 04:14:04Z',
    //          error_codes: [70000],
    //          timestamp: '2019-01-30 04:14:04Z',
    //          trace_id: 'ba5800c7-86d5-47ee-9569-3c70b8110e00',
    //          correlation_id:"c1972daa-8e59-447a-8574-1cba608ce64b"
    //        }
    //      }





// Boom Exception 503
[
  {
    "data": "1",
    "isBoom": true,
    "isServer": true,
    "output": "2"
  },
  {
    "isResponseError": true,
    "headers": "3",
    "res": "4",
    "payload": "5"
  },
  {
    "statusCode": 503,
    "payload": "6",
    "headers": "7"
  },
  {
    "cache-control": "8",
    "pragma": "9",
    "content-type": "10",
    "expires": "11",
    "server": "12",
    "strict-transport-security": "13",
    "x-content-type-options": "14",
    "x-ms-request-id": "15",
    "p3p": "16",
    "set-cookie": "17",
    "date": "18",
    "connection": "19",
    "content-length": "20"
  },
  {
    "_readableState": "21",
    "readable": false,
    "domain": null,
    "_events": "22",
    "_eventsCount": 2,
    "socket": "23",
    "connection": "23",
    "httpVersionMajor": 1,
    "httpVersionMinor": 1,
    "httpVersion": "24",
    "complete": true,
    "headers": "3",
    "rawHeaders": "25",
    "trailers": "26",
    "rawTrailers": "27",
    "aborted": false,
    "upgrade": false,
    "url": "28",
    "method": null,
    "statusCode": 503,
    "statusMessage": "29",
    "client": "23",
    "_consuming": true,
    "_dumped": false,
    "req": "30"
  },
  {
    "error": "31",
    "error_description": "32",
    "error_codes": "33",
    "timestamp": "34",
    "trace_id": "15",
    "correlation_id": "35"
  },
  {
    "statusCode": 503,
    "error": "29",
    "message": "36"
  },
  {
    
  },
  "no-cache, no-store",
  "no-cache",
  "application/json; charset=utf-8",
  "-1",
  "Microsoft-IIS/10.0",
  "max-age=31536000; includeSubDomains",
  "nosniff",
  "bb87a83e-17d4-4131-b6a4-df83f8119700",
  "CP=\\"DSP CUR OTPi IND OTRi ONL FIN\\"",
  [
    "37",
    "38",
    "39"
  ],
  "Wed, 06 Feb 2019 13:56:05 GMT",
  "close",
  "424",
  {
    "objectMode": false,
    "highWaterMark": 16384,
    "buffer": "40",
    "length": 0,
    "pipes": null,
    "pipesCount": 0,
    "flowing": false,
    "ended": true,
    "endEmitted": true,
    "reading": false,
    "sync": true,
    "needReadable": false,
    "emittedReadable": false,
    "readableListening": false,
    "resumeScheduled": false,
    "destroyed": false,
    "defaultEncoding": "41",
    "awaitDrain": 0,
    "readingMore": false,
    "decoder": null,
    "encoding": null
  },
  {
    
  },
  {
    "_tlsOptions": "42",
    "_secureEstablished": true,
    "_securePending": false,
    "_newSessionPending": false,
    "_controlReleased": true,
    "_SNICallback": null,
    "servername": "43",
    "alpnProtocol": false,
    "authorized": true,
    "authorizationError": null,
    "encrypted": true,
    "_events": "44",
    "_eventsCount": 9,
    "connecting": false,
    "_hadError": false,
    "_handle": "45",
    "_parent": null,
    "_host": "43",
    "_readableState": "46",
    "readable": true,
    "domain": null,
    "_writableState": "47",
    "writable": false,
    "allowHalfOpen": false,
    "_bytesDispatched": 790,
    "_sockname": null,
    "_pendingData": null,
    "_pendingEncoding": "28",
    "_server": null,
    "ssl": "45",
    "_requestCert": true,
    "_rejectUnauthorized": true,
    "parser": null,
    "_httpMessage": "30"
  },
  "1.1",
  [
    "48",
    "8",
    "49",
    "9",
    "50",
    "10",
    "51",
    "11",
    "52",
    "12",
    "53",
    "13",
    "54",
    "14",
    "55",
    "15",
    "56",
    "16",
    "57",
    "37",
    "57",
    "38",
    "57",
    "39",
    "58",
    "18",
    "59",
    "19",
    "60",
    "20"
  ],
  {
    
  },
  [
    
  ],
  "",
  "Service Unavailable",
  {
    "domain": null,
    "_events": "61",
    "_eventsCount": 2,
    "output": "62",
    "outputEncodings": "63",
    "outputCallbacks": "64",
    "outputSize": 0,
    "writable": true,
    "_last": true,
    "upgrading": false,
    "chunkedEncoding": false,
    "shouldKeepAlive": false,
    "useChunkedEncodingByDefault": true,
    "sendDate": false,
    "_removedConnection": false,
    "_removedContLen": false,
    "_removedTE": false,
    "_contentLength": null,
    "_hasBody": true,
    "_trailer": "28",
    "finished": true,
    "_headerSent": true,
    "socket": "23",
    "connection": "23",
    "_header": "65",
    "agent": "66",
    "method": "67",
    "path": "68",
    "_ended": true,
    "res": "4",
    "timeoutCb": null,
    "upgradeOrConnect": false,
    "parser": null,
    "maxHeadersCount": null
  },
  "temporarily_unavailable",
  "AADSTS70012: A transient error has occurred. Please try again.\\r\\nTrace ID: bb87a83e-17d4-4131-b6a4-df83f8119700\\r\\nCorrelation ID: cce822c5-2fbc-4f88-808e-2bb6fd5fbb78\\r\\nTimestamp: 2019-02-06 13:56:06Z",
  [
    70012
  ],
  "2019-02-06 13:56:06Z",
  "cce822c5-2fbc-4f88-808e-2bb6fd5fbb78",
  "Response Error: 503 Service Unavailable",
  "fpc=AfYOonwUgopHnkvThq521s-SQvbqAQDJuxHXOozWCA; expires=Fri, 08-Mar-2019 13:56:06 GMT; path=/; secure; HttpOnly",
  "x-ms-gateway-slice=prod; path=/; secure; HttpOnly",
  "stsservicecookie=ests; path=/; secure; HttpOnly",
  {
    "head": null,
    "tail": null,
    "length": 0
  },
  "utf8",
  {
    "pipe": false,
    "secureContext": "69",
    "isServer": false,
    "requestCert": true,
    "rejectUnauthorized": true
  },
  "login.microsoftonline.com",
  {
    "close": "70"
  },
  {
    "_parent": "71",
    "_secureContext": "69",
    "reading": true,
    "owner": "23",
    "writeQueueSize": 0
  },
  {
    "objectMode": false,
    "highWaterMark": 16384,
    "buffer": "72",
    "length": 0,
    "pipes": null,
    "pipesCount": 0,
    "flowing": true,
    "ended": false,
    "endEmitted": false,
    "reading": true,
    "sync": false,
    "needReadable": true,
    "emittedReadable": false,
    "readableListening": false,
    "resumeScheduled": false,
    "destroyed": false,
    "defaultEncoding": "41",
    "awaitDrain": 0,
    "readingMore": false,
    "decoder": null,
    "encoding": null
  },
  {
    "objectMode": false,
    "highWaterMark": 16384,
    "finalCalled": true,
    "needDrain": false,
    "ending": true,
    "ended": true,
    "finished": false,
    "destroyed": false,
    "decodeStrings": false,
    "defaultEncoding": "41",
    "length": 0,
    "writing": false,
    "corked": 0,
    "sync": false,
    "bufferProcessing": false,
    "writecb": null,
    "writelen": 0,
    "bufferedRequest": null,
    "lastBufferedRequest": null,
    "pendingcb": 1,
    "prefinished": false,
    "errorEmitted": false,
    "bufferedRequestCount": 0,
    "corkedRequestsFree": "73"
  },
  "Cache-Control",
  "Pragma",
  "Content-Type",
  "Expires",
  "Server",
  "Strict-Transport-Security",
  "X-Content-Type-Options",
  "x-ms-request-id",
  "P3P",
  "Set-Cookie",
  "Date",
  "Connection",
  "Content-Length",
  {
    
  },
  [
    
  ],
  [
    
  ],
  [
    
  ],
  "POST /common/oauth2/v2.0/token HTTP/1.1\\r\\nAccept: application/json\\r\\nAuthorization: Basic MzMxMTZhYmMtN2U0MC00YjIxLThmNzAtYjIxODJiZjNiOTE5OmNzS0VMTTM4MzgoKHZzcGt2R09KNChf\\r\\nContent-Type: application/x-www-form-urlencoded\\r\\ncontent-length: 496\\r\\nHost: login.microsoftonline.com\\r\\nConnection: close\\r\\n\\r\\n",
  {
    "domain": null,
    "_events": "74",
    "_eventsCount": 1,
    "defaultPort": 443,
    "protocol": "75",
    "options": "76",
    "requests": "77",
    "sockets": "78",
    "freeSockets": "79",
    "keepAliveMsecs": 1000,
    "keepAlive": false,
    "maxSockets": null,
    "maxFreeSockets": 256,
    "maxCachedSessions": 100,
    "_sessionCache": "80"
  },
  "POST",
  "/common/oauth2/v2.0/token",
  {
    "context": "81",
    "singleUse": true
  },
  [
    null,
    null,
    null,
    null
  ],
  {
    "reading": true,
    "owner": "23",
    "onread": null,
    "onconnection": null,
    "writeQueueSize": 0
  },
  {
    "head": null,
    "tail": null,
    "length": 0
  },
  {
    "next": "82",
    "entry": null
  },
  {
    
  },
  "https:",
  {
    "maxSockets": null,
    "path": null
  },
  {
    
  },
  {
    "login.microsoftonline.com:443:::::::::": "83"
  },
  {
    
  },
  {
    "map": "84",
    "list": "85"
  },
  {
    
  },
  {
    "next": null,
    "entry": null
  },
  [
    "23"
  ],
  {
    "login.microsoftonline.com:443:::::::::": "86"
  },
  [
    "87"
  ],
  {
    "type": "88",
    "data": "89"
  },
  "login.microsoftonline.com:443:::::::::",
  "Buffer"
]