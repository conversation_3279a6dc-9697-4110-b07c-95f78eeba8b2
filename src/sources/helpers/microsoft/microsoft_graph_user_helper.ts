
import { Client } from '@microsoft/microsoft-graph-client';
import { User } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';
import ForaUser from '../../../session/user';
import { Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { AbstractMicrosoftGraphHelper } from './a_microsoft_graph_helper';

const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphUserHelper';

export default class MicrosoftGraphUserHelper extends AbstractMicrosoftGraphHelper {
  static async usersGet(user: ForaUser, account: Uid, client?: Client): Promise<User[]> {

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      let api_path = 'users';

      let users = [];
      while (api_path) {
        const response = await client.api(api_path).get();
        api_path = response['@odata.nextLink'];
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'contactCreate', user.profile, `Got users ${util.format(response)}`);
        if (response.value) users = users.concat(response.value);
      }
      return users;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'usersGet', user.profile, `Error getting users`, err);
      throw err;
    }
 
  }
}
 