/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client } from '@microsoft/microsoft-graph-client';
import { Message } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';
import ForaUser from '../../../session/user';
import { Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { AbstractMicrosoftGraphHelper } from './a_microsoft_graph_helper';

const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphMessageHelper';

export default class MicrosoftGraphMessageHelper extends AbstractMicrosoftGraphHelper {
  static async messageArchive(user: ForaUser, account: Uid, message_id: string, client?: Client): Promise<Message> {
    return MicrosoftGraphMessageHelper.messageMove(user, account, message_id, 'archive', client);
  }

  static async messageCreate(user: Fora<PERSON>ser, account: Uid, message_in: Message, client?: Client): Promise<Message> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const message_out = await client.api('/me/messages').post(message_in);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'messageCreate', user.profile, `Created message ${util.format(message_out)}`);
      return message_out;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'messageCreate', user.profile, `Error creating message ${util.format(message_in)}`, err);
      throw err;
    }
  }

  static async messageGet(user: ForaUser, account: Uid, message_id: string, client?: Client): Promise<Message> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const message_out = await client.api(`/me/messages/${message_id}`).get();
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'messageGet', user.profile, `Read message ${util.format(message_out)}`);
      return message_out;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'messageGet', user.profile, `Error reading message ${message_id}`, err);
      throw err;
    }
  }

  static async messageMarkAsRead(user: ForaUser, account: Uid, message_id: string, client?: Client): Promise<Message> {
    return MicrosoftGraphMessageHelper.messageUpdate(user, account, message_id, { isRead: true }, client);
  }

  static async messageMove(user: ForaUser, account: Uid, message_id: string, destination_id: string, client?: Client): Promise<Message> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const message_out = await client.api(`/me/messages/${message_id}/move`).post({ destinationId: 'archive' });
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'messageMove', user.profile, `Moved message ${util.format(message_out)}`);
      return message_out;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'messageMove', user.profile, `Error moving message ${message_id}`, err);
      throw err;
    }
  }

  static async messageSend(user: ForaUser, account: Uid, message_id: string, client?: Client): Promise<Message> {
    // Note - the post method requires an argument but the send endpoint doesn't want a body, just
    // the ID in the path. It also doesn't return anything so we must do two calls (send, read)
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      await client.api(`/me/messages/${message_id}/send`).post({});
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'messageSend', user.profile, `Sent message ${message_id}`);
      return await MicrosoftGraphMessageHelper.messageGet(user, account, message_id, client);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'messageSend', user.profile, `Error sending message ${message_id}`, err);
      throw err;
    }
  }

  static async messageUpdate(user: ForaUser, account: Uid, message_id: string, changes: Message, client?: Client): Promise<Message> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const message_out = await client.api(`/me/messages/${message_id}`).patch(changes);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'messageUpdate', user.profile, `Updated message ${util.format(message_out)}`);
      return message_out;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'messageUpdate', user.profile, `Error updating message ${message_id}`, err);
      throw err;
    }
  }
}
