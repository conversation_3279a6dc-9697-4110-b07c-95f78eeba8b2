/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Batch<PERSON>equestContent, BatchRequestStep, Client } from '@microsoft/microsoft-graph-client';
import { Contact } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';
import ForaUser from '../../../session/user';
import { Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { MicrosoftPeopleSourceMetadata } from '../../people/microsoft/microsoft_people_source_metadata';
import { AbstractMicrosoftGraphHelper } from './a_microsoft_graph_helper';

const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphContactHelper';

export default class MicrosoftGraphContactHelper extends AbstractMicrosoftGraphHelper {
  static async contactCreate(user: <PERSON><PERSON><PERSON><PERSON>, account: Uid, contact_in: Contact, client?: Client): Promise<Contact> {
    const api_path = '/me/contacts';

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const contact_out = await client.api(api_path).post(contact_in);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'contactCreate', user.profile, `Created contact ${util.format(contact_out)}`);
      return contact_out;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'contactCreate', user.profile, `Error creating contact ${util.format(contact_in)}`, err);
      throw err;
    }
  }

  static async contactGet(user: ForaUser, account: Uid, contact_id: string, client?: Client): Promise<Contact> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const api_path = `/me/contacts/${contact_id}`;

      const contact = await client.api(api_path).get();
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'contactGet', user.profile, `Got contact = ${util.format(contact)}`);
      return contact;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'contactGet', user.profile, `Error getting contact ${contact_id}`, err);
      throw err;
    }
  }

  static async contactsGet(user: ForaUser, account: Uid, contact_ids: string[], client?: Client): Promise<Contact[]> {
    const contacts: Contact[] = [];
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      const id_set = contact_ids.slice();
      while(id_set.length) {
        const id_fetch = id_set.splice(0,50);
        const batch_requests:BatchRequestStep[] = id_fetch.map((contact_id, index) => {
          return {
            id: `${index}`,
            request: new Request(`/me/contacts/${contact_id}`)
          } as BatchRequestStep;
        });
        const batch_request_content = new BatchRequestContent(batch_requests);

        const content = await batch_request_content.getContent();

        const response = await client.api("/$batch").post(content);
        if (response) {
          //const batch_response_content = new BatchResponseContent(response);
          // const responses = batch_response_content.getResponses();
          if (response.responses) {
            for (const r of response.responses) {
              if (r.status === 200 && r.body) contacts.push(r.body);
            }
          }
        }
      }
    } catch(err) {
      logging.errorFP(LOG_NAME, 'contactGet', user.profile, `Error getting contacts ${contact_ids}`, err);
      throw err;
    }
     return contacts;
  }

  static async contactDelete(user: ForaUser, account: Uid, contact_id: string, client?: Client): Promise<void> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const api_path = `/me/contacts/${contact_id}`;

      await client.api(api_path).delete();
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'contactDelete', user.profile, `Deleted contact ${contact_id}`);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'contactDelete', user.profile, `Error deleting contact ${contact_id}`, err);
      throw err;
    }
  }

  static async contacts(user: ForaUser, account: Uid, client?: Client): Promise<Contact[]> {
    let contacts: Contact[] = [];

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      let api_path = '/me/contacts/';

      while (api_path) {
        const response = await client.api(api_path).get();
        api_path = response['@odata.nextLink'];
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'contacts', user.profile, `Contact list result page ${util.format(response)}`);
        if (response.value) contacts = contacts.concat(response.value);
      }

      return contacts;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'contacts', user.profile, 'Error reading Contacts', err);
      throw err;
    }
  }

  static async contactsPage(user: ForaUser, account: Uid, metadata: MicrosoftPeopleSourceMetadata, is_history: boolean, client?: Client): Promise<Contact[]> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const api_path = MicrosoftGraphContactHelper.getPathFromTokens(user, metadata, is_history, '/me/contacts/delta');
      const response = await client.api(api_path).get();
      MicrosoftGraphContactHelper.handleResponseTokens(user, metadata, is_history, response);
      return response.value;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'contactsPage', user.profile, 'Error reading contact from list', err);
      throw err;
    }
  }

  static async contactUpdate(user: ForaUser, account: Uid, contact_in: Contact, client?: Client): Promise<Contact> {
    const api_path = `/me/contacts/${contact_in.id}`;

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const contact_out = await client.api(api_path).patch(contact_in);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'contactUpdate', user.profile, `Updated contact ${util.format(contact_out)}`);
      return contact_out;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'contactUpdate', user.profile, `Error updating contact ${util.format(contact_in)}`, err);
      throw err;
    }
  }
}
