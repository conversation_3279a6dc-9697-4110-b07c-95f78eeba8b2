/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client } from '@microsoft/microsoft-graph-client';
import 'isomorphic-fetch';
import { setTimeout } from "timers/promises";
import { AuthProvider } from '../../../auth/auth_provider';
import ForaUser from '../../../session/user';
import { NormalizedProviderSettings } from '../../../types/auth';
import { AuthProviders, Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { ISourceMetadata } from '../../i_source_metadata';

const LOG_NAME = 'data.utils.microsoft.AbstractMicrosoftGraphHelper';
const ONE_NOTE_RETRY = [
  // '10001', // An unexpected error occurred and the request failed.
  '10002', // The service is not currently available.
  '10003', // The current user's account has exceeded the maximum number of active requests. Your app will have to repeat the request.
  // '10004', // The service can't create a page in the requested section because that section is protected by a password.
  // '10005', // The request contains more than the maximum number of image tags in which the data-render-src attribute contains a PDF. See Add images and files.
  // '10006', // The OneNote API was unable to create a page in the specified section because that section is corrupt.
  '10007', // The server is too busy to handle the incoming request at this moment. Please try again later.
  // '10008', // One or more of the document libraries on the user or group's OneDrive contains more than 5000 OneNote items (notebooks, sections, section groups), and cannot be queried using the API.
  // '10012', // Unable to create or update the entity because the library that contains the notebook requires items to be checked out before they can be edited.
  // '10013', // One or more of the document libraries on the user or group's OneDrive contains more than 20,000 items and cannot be indexed for querying using the API.
  '10014', // Azure Key Vault is too busy to handle the incoming request at this moment. Please try again later.
  '10015', // SharePoint is currently unavailable. Please try again later.
  // '10016', // Document library on the user or group’s OneDrive exceeded unique security scopes threshold limit. The maximum number of unique security scopes set for a library cannot exceed 50,000.
  // '10017', // Bad Request.
  // '19999', // The request failed because an undetermined error occurred.

  // 20001 to 29999 are all calling application level errors, none should be retried

  // '30101', // The user account has exceeded its OneDrive quota. See OneDrive.
  // '30102', // Nothing more can be added to the requested section because it has reached its maximum size.
  '30103', // Resource consumption is too high for the request.
  // '30104', // The user account has been suspended.
  '30105', // The user's personal OneDrive for Business site is not provisioned, which is required to access notebooks. The OneNote service will provision the site now. This process may take several minutes.
  '30106', // OneDrive for Business is being provisioned for the user.
  '30108', // The user's personal OneDrive for Business could not be retrieved.
  // '30109', // Some users in the request do not exist.
  // '30110', // Student Information Services has not been registered for this tenant.
  // '30111', // There is a generic error with Student Information Services.
  // '30112', // Multiple users affected by the request had the same username.
  // '30113', // The notebook is not configured to allow invites.
  // '30114', // There is a required parameter missing.

  // 40001 to 49999 are all permission related errors, none should be retried
];

export abstract class AbstractMicrosoftGraphHelper {
  static async getGraphClient(user: ForaUser, account: Uid, settings?: NormalizedProviderSettings) {
    const provider = user.getAccountType(account);
    if (provider === AuthProviders.Microsoft) return await AuthProvider.microsoft.getGraphClient(user, account, settings);
    else if (provider === AuthProviders.Msal) return await AuthProvider.msal.getGraphClient(user, account, settings);
  }

  static getPathFromTokens(user: ForaUser, metadata: ISourceMetadata, is_history: boolean, default_path?: string) {
    let path = default_path;

    if (is_history) {
      // Sync processing backwards
      if (metadata.historyPageToken) path = metadata.historyPageToken;
    } else {
      // Sync processing forward (if statements nested for easier readability of all the possible cases)

      if (metadata.nextPageToken) path = metadata.nextPageToken;
      else if (metadata.nextSyncToken) path = metadata.nextSyncToken;
    }

    logging.debugF(LOG_NAME, 'getPathFromTokens', `Path = ${path} for ${user.profile}`);
    return path;
  }

  static async getResponse(user: ForaUser, profile: Uid, api_path: string, page_size: number, client?: Client): Promise<any> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, profile);
      return await client
        .api(api_path)
        .header('Prefer', `odata.maxpagesize=${page_size}`)
        .get();
    } catch (err) {
      logging.errorF(LOG_NAME, 'getRawResponse', `Error running GET for ${api_path} for ${user.profile}`, err);
      throw err;
    }
  }

  static handleResponseTokens(user: ForaUser, metadata: ISourceMetadata, is_history: boolean, result: any): void {
    if (is_history) {
      metadata.historyDone = !result['@odata.nextLink'];
      metadata.historyPageToken = result['@odata.nextLink'];
    } else {
      metadata.nextPageToken = result['@odata.nextLink'];
      metadata.nextSyncToken = result['@odata.deltaLink'];

      if (result['@odata.nextLink']) metadata.newDone = false;
      else if (result['@odata.deltaLink']) {
        metadata.firstRunDone = true;
        metadata.newDone = true;
      } else {
        metadata.firstRunDone = true;
        metadata.newDone = true;
      }
    }
  }

  protected static async repeatableCallWithBackoff(call: any, this_arg: any, args?: any): Promise<any> {
    let retry = 10;

    while (true) {
      try {
        return await await call.bind(this_arg, args)();
      } catch (err) {
        if (ONE_NOTE_RETRY.includes(err.code) && retry < 60000) {
          await setTimeout(retry);
          retry *= 2;
        } else throw err;
      }
    }
  }
}
