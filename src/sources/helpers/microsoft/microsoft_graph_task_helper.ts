/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client } from '@microsoft/microsoft-graph-client';
import { TodoTask, TodoTaskList } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';
import ForaUser from '../../../session/user';
import { Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { MicrosoftTasksSourceMetadata } from '../../tasks/microsoft/microsoft_tasks_source_metadata';
import { AbstractMicrosoftGraphHelper } from './a_microsoft_graph_helper';

const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphTaskHelper';

export default class MicrosoftGraphTaskHelper extends AbstractMicrosoftGraphHelper {
  static async taskCreate(user: Fora<PERSON><PERSON>, account: Uid, list_name: string, task_in: TodoTask, client?: Client): Promise<TodoTask> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const list = await MicrosoftGraphTaskHelper.taskListEnsure(user, account, list_name, client);
      const task_out: TodoTask = await client
        .api(`/me/todo/lists/${list.id}/tasks`)
        .version('v1.0')
        .post(task_in);
      logging.debugF(LOG_NAME, 'taskCreate', `Create task response = ${util.format(task_out)} for ${user.profile}`);
      return task_out;
    } catch (err) {
      logging.errorF(LOG_NAME, 'taskCreate', `Error creating task for ${user.profile}`, err);
      throw err;
    }
  }

  static async taskDelete(user: ForaUser, account: Uid, list_name: string, task_id: string, client?: Client): Promise<void> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const list = await MicrosoftGraphTaskHelper.taskListEnsure(user, account, list_name, client);
      await client
        .api(`/me/todo/lists/${list.id}/tasks/${task_id}`)
        .version('v1.0')
        .delete();
    } catch (err) {
      logging.errorF(LOG_NAME, 'taskDelete', `Error deleting task for ${user.profile}`, err);
      throw err;
    }
  }

  static async taskGet(user: ForaUser, account: Uid, list_name: string, task_in: TodoTask, client?: Client): Promise<TodoTask> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const list = await MicrosoftGraphTaskHelper.taskListEnsure(user, account, list_name, client);
      const task_out: TodoTask = await client
        .api(`/me/todo/lists/${list.id}/tasks/${task_in.id}`)
        .version('v1.0')
        .get();
      logging.debugF(LOG_NAME, 'taskGet', `Get task response = ${util.format(task_out)} for ${user.profile}`);
      return task_out;

    } catch(err) {
      if (err['statusCode'] && [404, 400].includes(err['statusCode'])) return null;
      logging.errorF(LOG_NAME, 'taskGet', `Error getting task for ${user.profile}`, err);
      throw err;
    }
  }

  static async taskListByName(user: ForaUser, account: Uid, list_name: string, client?: Client): Promise<TodoTaskList> {
    if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
    const lists = await MicrosoftGraphTaskHelper.taskLists(user, account,client);
    for (const task_list of lists) {
      if (task_list.displayName === list_name) {
        return task_list;
      }
    }

    return null;
  }

  static async taskListCreate(user: ForaUser, account: Uid, list_name: string, client?: Client): Promise<TodoTaskList> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const list: TodoTaskList = await client
        .api('/me/todo/lists')
        .version('v1.0')
        .post({ displayName: list_name });
      logging.debugF(LOG_NAME, 'taskListCreate', `Created task list ${util.format(list)} for ${user.profile}`);
      return list;
    } catch (err) {
      logging.errorF(LOG_NAME, 'taskListCreate', `Error creating task list ${list_name} for ${user.profile}`, err);
      throw err;
    }
  }

  static async taskListDelete(user: ForaUser, account: Uid, list_name: string, client?: Client): Promise<void> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const list: TodoTaskList = await MicrosoftGraphTaskHelper.taskListByName(user, account, list_name, client);
      if (list) {
        await client.api(`/me/todo/lists/${list.id}`).delete();
        logging.debugF(LOG_NAME, 'taskListDelete', `Deleted task list ${util.format(list)} for ${user.profile}`);
      }
    } catch(err) {
      logging.errorF(LOG_NAME, 'taskListDelete', `Error deleting task list ${list_name} for ${user.profile}`, err);
      throw err;
    }
  }

  static async taskListEnsure(user: ForaUser, account: Uid, list_name: string, client?: Client): Promise<TodoTaskList> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      // Get the folder if it exists
      let list: TodoTaskList = await MicrosoftGraphTaskHelper.taskListByName(user, account, list_name, client);

      // If we don't have a list, we need to create it
      if (!list) list = await MicrosoftGraphTaskHelper.taskListCreate(user, account, list_name, client);

      return list;
    } catch (err) {
      logging.errorF(LOG_NAME, 'taskListEnsure', `Error ensuring task list for ${user.profile}`, err);
      throw err;
    }
  }

  static async taskLists(user: ForaUser, account: Uid, client?: Client): Promise<TodoTaskList[]> {
    let lists: TodoTaskList[] = [];

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      let api_path = '/me/todo/lists';
      while (api_path) {
        const response = await client
          .api(api_path)
          .version('v1.0')
          .get();
        api_path = response['@odata.nextLink'];
        logging.debugF(LOG_NAME, 'taskLists', `Task list result ${util.format(response)} for ${user.profile}`);
        if (response.value) lists = lists.concat(response.value);
      }

      return lists;
    } catch (err) {
      logging.errorF(LOG_NAME, 'taskLists', `Error reading list of task lists for ${user.profile}`, err);
      throw err;
    }
  }

  static async tasksPage(user: ForaUser, account: Uid, metadata: MicrosoftTasksSourceMetadata, is_history: boolean, client?: Client): Promise<TodoTask[]> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const api_path = MicrosoftGraphTaskHelper.getPathFromTokens(user, metadata, is_history, `/me/todo/lists/${metadata.taskListId}/tasks`);
      const response = await client
        .api(api_path)
        .version('v1.0')
        .get();
      MicrosoftGraphTaskHelper.handleResponseTokens(user, metadata, is_history, response);
      return response.value;
    } catch (err) {
      logging.errorF(LOG_NAME, 'tasksPage', `Error reading tasks from list for ${user.profile}`, err);
      throw err;
    }
  }

  static async taskUpdate(user: ForaUser, account:Uid, list_name: string, task_in: TodoTask, client?: Client): Promise<TodoTask> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const list = await MicrosoftGraphTaskHelper.taskListEnsure(user, account, list_name, client);
      const task_out: TodoTask = await client
        .api(`/me/todo/lists/${list.id}/tasks/${task_in.id}`)
        .version('v1.0')
        .patch(task_in);
      logging.debugF(LOG_NAME, 'taskUpdate', `Updated task response = ${util.format(task_out)} for ${user.profile}`);
      return task_out;
    } catch (err) {
      logging.errorF(LOG_NAME, 'taskUpdate', `Error updating task for ${user.profile}`, err);
      throw err;
    }
  }
}
