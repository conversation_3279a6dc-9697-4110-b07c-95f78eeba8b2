/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client } from '@microsoft/microsoft-graph-client';
import { Calendar, Event } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';
import ForaUser from '../../../session/user';
import { Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import { AbstractMicrosoftGraphHelper } from './a_microsoft_graph_helper';

const LOG_NAME = 'data.utils.microsoft.MicrosoftGraphCalendarHelper';

export default class MicrosoftGraphCalendarHelper extends AbstractMicrosoftGraphHelper {
  static async calendarEventCancel(user: ForaUser, account: Uid, event_id: string, comment?: string, client?: Client): Promise<void> {
    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const api_path = `/me/events/${event_id}/cancel`;
      await client
        .api(api_path)
        .version('beta')
        .post({ comment });

      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'calendarEventCancel', user.profile, `Cancelled event ${event_id} for ${user.profile}`);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'calendarEventCancel', user.profile, `Error cancelling event ${event_id} for ${user.profile}`, err);
      throw err;
    }
  }

  static async calendarEventCreate(user: ForaUser, account: Uid, event_in: Event, calendar_id?: string, client?: Client): Promise<Event> {
    let api_path;
    if (calendar_id && calendar_id !== 'primary') api_path = `/me/calendars/${calendar_id}/events`;
    else api_path = '/me/calendar/events';

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const event_out = await client.api(api_path).post(event_in);
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'calendarEventCreate', user.profile, `Created event ${util.format(event_out)} for ${user.profile}`);
      return event_out;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'calendarEventCreate', user.profile, `Error creating event ${event_in} for calendar '${calendar_id}' for ${user.profile}`, err);
      throw err;
    }
  }

  static async calendarEventGet(user: ForaUser, account: Uid, event_in: Event, calendar_id?: string, client?: Client): Promise<Event> {
    const api_path = `/me/events/${event_in.id}`;

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      const event_out = await client.api(api_path).get();
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'calendarEventGet', user.profile, `Got event ${util.format(event_out)} for ${user.profile}`);
      return event_out;
    } catch (err) {
      if (err['statusCode'] && [400, 404].includes(err['statusCode'])) return null;
      logging.errorFP(LOG_NAME, 'calendarEventGet', user.profile, `Error getting event ${event_in} for calendar '${calendar_id}' for ${user.profile}`, err);
      throw err;
    }
  }

  static async calendarEvents(user: ForaUser, account: Uid, calendar_id?: string, client?: Client): Promise<Event[]> {
    let events: Event[] = [];

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      let api_path = calendar_id ? `/me/calendars/${calendar_id}/events` : '/me/events';
      while (api_path) {
        const response = await client.api(api_path).get();
        api_path = response['@odata.nextLink'];
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'calendars', user.profile, `Event list result page ${util.format(response)} for ${user.profile}`);
        if (response.value) events = events.concat(response.value);
      }

      return events;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'calendars', user.profile, `Error reading Events for ${user.profile}`, err);
      throw err;
    }
  }

  static async calendars(user: ForaUser, account: Uid, client?: Client): Promise<Calendar[]> {
    let calendars: Calendar[] = [];

    try {
      if (!client) client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

      let api_path = '/me/calendars';
      while (api_path) {
        const response = await client.api(api_path).get();
        api_path = response['@odata.nextLink'];
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'calendars', user.profile, `Calendar list result page ${util.format(response)} for ${user.profile}`);
        if (response.value) calendars = calendars.concat(response.value);
      }

      return calendars;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'calendars', user.profile, `Error reading Calendars for ${user.profile}`, err);
      throw err;
    }
  }
}
