/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { File } from '@google-cloud/storage';
import { Credentials } from 'google-auth-library';
import { admin_directory_v1, people_v1, tasks_v1 } from 'googleapis';
import _ from 'lodash';
import util from 'util';
import ForaUser from '../../../session/user';
import { NormalizedProviderToken } from '../../../types/auth';
import { User } from '../../../types/globals';
import { Document, Person, Tag, Task } from '../../../types/items';
import { AuthLevel, AuthProviders, TagType, Uid, findTypeTag, findTypeValue, findTypeValues, findTypesAtIndex } from '../../../types/shared';
import * as funcs from '../../../utils/funcs';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';
import peopleUtils from '../../../utils/people';
import { TaskStatus } from '../../tasks/i_tasks_source_plugin';

const DEBUG = (require('debug') as any)('fora:sources:helpers:google:conversion');
const LOG_NAME = 'data.utils.google.GoogleConverter';

export class GoogleConverter {
  static convertCloudStorageFileToDoc(user: ForaUser, file: File, metadata: any, contents: Buffer): Document {
    const doc = new Document({
      id: file.name,
      file_id: file.name,
      created: new Date(file.metadata.timeCreated),
      props: metadata.metadata,
      body: contents,
    });

    if (logging.isDebug(user ? user.profile :null)) logging.debugFP(LOG_NAME, 'convertCloudStorageFileToDoc', user ? user.profile : null, `Returned doc = ${util.format(doc)}`);
    return doc;
  }

  static convertNormalizedProviderTokenFromCredentials(token: Credentials, scope_levels: {[key:string] : string[]}): NormalizedProviderToken {
    /*
     * Schema of Credentials
     * refresh_token?: string | null;
     * expiry_date?: number | null;
     * access_token?: string | null;
     * token_type?: string | null;
     * id_token?: string | null;
     */
    const permissions = funcs.stringIncludesAll(token['scope'] as string, scope_levels[AuthLevel.OrganizerSync]) ? AuthLevel.OrganizerSync:
      funcs.stringIncludesAll(token['scope'] as string, scope_levels[AuthLevel.Organizer]) ? AuthLevel.Organizer: AuthLevel.Basic;
    const npt = new NormalizedProviderToken(AuthProviders.Google, token.access_token, permissions);
    npt.expires_at = new Date(token.expiry_date);
    npt.id_token = token.id_token;
    npt.refresh_token = token.refresh_token;
    npt.scope = token['scope']; // There is a scope returned but its not in the Credentials interface
    npt.token_type = token.token_type;

    if (logging.isDebug()) {
      DEBUG('convertNormalizedProviderTokenFromCredentials: incoming tokens = %o', token);
      DEBUG('convertNormalizedProviderTokenFromCredentials: outgoing tokens = %o', npt);
    }
    return npt;
  }

  static convertNormalizedProviderTokenToCredentials(token: NormalizedProviderToken): Credentials {
    /*
     * Schema of Credentials
     * refresh_token?: string | null;
     * expiry_date?: number | null;
     * access_token?: string | null;
     * token_type?: string | null;
     * id_token?: string | null;
     */
    const credentials = {
      refresh_token: token.refresh_token,
      expiry_date: token.expires_at ? new Date(token.expires_at).getTime() : 0,
      access_token: token.access_token,
      token_type: token.token_type,
      id_token: token.id_token,
    } as Credentials;
    credentials['scope'] = token.scope; // There is a scope returned but its not in the Credentials interface

    if (logging.isDebug()) {
      DEBUG('convertNormalizedProviderTokenToCredentials: incoming tokens = %o', token);
      DEBUG('convertNormalizedProviderTokenToCredentials: outgoing tokens = %o', credentials);
    }
    return credentials;
  }

  static dateFromGoogleDate(g_date: { day?: number; month? : number; year?: number}): Date {
    const date = new Date(0);

    // order here matters
    date.setDate(g_date.day);
    date.setMonth(g_date.month - 1);
    date.setFullYear(g_date.year);
    logging.debugF(LOG_NAME, 'dateFromGoogleDate', `${JSON.stringify(g_date)} => ${date}`);
    return date;
  }

  static googleDateFromDate(date: Date): { day: number; month: number; year: number } {
    return {
      day: date.getDate(),
      month: date.getMonth() + 1,
      year: date.getFullYear(),
    }
  }

  /**
   * Convert a Google task to a AskFora task
   *
   * @param user - Fora user we are running with
   * @param g_task - the Google task to convert
   * @param taskListId - the id of the Google task list it is part of
   * @param f_task_in - the Fora task as we last knew it
   * @returns Converted task
   * @private
   */
  static taskFromGoogleTask(user: ForaUser, taskListId: string, g_task: tasks_v1.Schema$Task, f_task_in: Task, expect_task = true): Task {
    const f_task_out = new Task({
      id: g_task.id,
      taskListId,
      title: g_task.title,
      status: g_task.status,
      completed: new Date(g_task.completed),
      updated: new Date(g_task.updated),
      due: new Date(g_task.due),
      notes: g_task.notes && g_task.notes[0] === '[' ? '' : g_task.notes,
      people: g_task.notes && g_task.notes[0] === '[' ? JSON.parse(g_task.notes) : [],
      project: f_task_in ? f_task_in.project : null,
    });

    if (f_task_in) {
      // Google forgets the timestamp so use the one we know about
      if (f_task_in.due && new Date(f_task_in.due).getTime()) f_task_out.due = f_task_in.due;
      f_task_out.created = f_task_in.created;

      // Use the people as we knew them - saves us in case someone hand edits the notes within the Google UI.
      f_task_out.people = _.uniqBy(f_task_out.people.concat(f_task_in.people).filter(p => p), 'id');
      f_task_out.project = f_task_in.project;
    } else if (expect_task) {
      logging.warnFP(LOG_NAME, 'taskFromGoogleTask', user.profile, `Unable to find matching task in DS with ID '${g_task.id}', using all values from Google`);
    }

    return f_task_out;
  }

  /**
   * Convert a Fora task to a Google task
   *
   * @param f_task - the Fora task to convert
   * @param update_updated - update the updated field in the Google task?
   * @returns Converted task
   */
  static taskToGoogleTask(f_task: Task, update_updated?: boolean): tasks_v1.Schema$Task {
    const due_date = new Date(f_task.due);
    const due = due_date.getTime() ? due_date.toISOString() : null;
    const g_task = {
      id: f_task.id,
      due,
      title: f_task.title,
      status: f_task.status ? f_task.status : TaskStatus.needsAction,
      notes: f_task.notes,
    } as tasks_v1.Schema$Task;

    if (update_updated) g_task.updated = f_task.updated ? new Date(f_task.updated).toISOString() : new Date().toISOString();
    // if (f_task.people && f_task.people.length) g_task.notes = JSON.stringify(f_task.people);

    return g_task;
  }

  /**
   * Convert a Google person to a AskFora person
   *
   * @param connection - Google person
   * @param locale - user locale for parsing phone numbers
   * @param @optional start - default date for tags and skills
   * @returns Array of updated fields
   */
  static personFromGooglePerson(connection: people_v1.Schema$Person, locale?: string, start: Date = new Date()): Person {
    let display = null;
    let nickname = null;
    let first = null;
    const names: string[] = []; // all name permutations
    const tags: Tag[] = []; // company, skills, etc. {type:,value:}
    const comms: string[] = []; // emails, ims, phones
    const urls: string[] = []; // fb, twitter, LI
    const photos: string[] = [];

    // TODO events
    // TODO relations
    // TODO address, residence
    // TODO org location
    // TODO Biography, Tagline
    // TODO birthday
    // TODO preferred comms

    if (connection.names) {
      for (const name of connection.names) {
        if (display === null) display = name.displayName;
        if (first === null) first = name.givenName;
        funcs.saveOne(names, name.displayName);
        // funcs.saveOne(names, name.displayNameLastFirst);
        funcs.saveOne(names, name.familyName);
        funcs.saveOne(names, name.givenName);
        funcs.saveOne(names, name.middleName);

        // if (name.givenName && name.familyName) funcs.saveOne(names, `${name.givenName} ${name.familyName[0]}`);

        if (name.honorificPrefix) {
          funcs.saveOne(names, `${name.honorificPrefix} ${name.displayName}`);
          if (name.honorificSuffix) funcs.saveOne(names, `${name.honorificPrefix} ${name.displayName} ${name.honorificSuffix}`);
        }

        if (name.honorificSuffix) funcs.saveOne(names, `${name.displayName} ${name.honorificSuffix}`);

        // TODO: phonetic?
      }
    }

    if (connection.nicknames) {
      for (const nick of connection.nicknames) {
        funcs.saveOne(names, nick.value);
        if (display === null) display = nick.value;
        if (nickname === null) nickname = nick.value;
      }
    }

    const orgs = new Set();
    let tag_index = 0;
    if (connection.organizations) {
      let addName = false;
      if (display === null) addName = true;
      for (let tag_index = 0; tag_index < connection.organizations.length; tag_index ++) {
        const org = connection.organizations[tag_index];
        let org_start: Date = new Date();

        if (org.startDate) org_start = GoogleConverter.dateFromGoogleDate(org.startDate);

        if (org.name !== null && org.name) {
          if (addName) {
            funcs.saveOne(names, org.name);
            orgs.add(org.name);
            if (display === null) display = org.name;
          }
          if (org.name) {
            funcs.saveOneTypeValue(tags, new Tag( TagType.organization, org.name, tag_index, org_start ), !!org.startDate);
            orgs.add(org.name);

            if (org.type) funcs.saveOneTypeValue(tags, new Tag( org.type as unknown as TagType, org.name, tag_index, org_start ), !!org.startDate);
          }
        }

        if (org.department) {
          funcs.saveOneTypeValue(tags, new Tag( TagType.department, org.department, tag_index, org_start ), !!org.startDate);
          orgs.add(org.department);
        }
        if (org.title) {
          funcs.saveOneTypeValue(tags, new Tag(TagType.jobTitle,  org.title, tag_index, org_start ), !!org.startDate);
          orgs.add(org.title);
        }
        if (org.jobDescription) {
          const meaning = parsers.findMeaning(org.jobDescription);
          for (const mean of meaning) {
            if (!names.includes(mean)) { // && !orgs.has(mean)) {
              funcs.saveOneTypeValue(tags, new Tag( TagType.jobDescription, mean, tag_index, org_start ), !!org.startDate);
            }
            orgs.add(mean);
          }
        }
        if (org.symbol) {
          funcs.saveOneTypeValue(tags, new Tag(TagType.ticker, org.symbol, tag_index, org_start ), !!org.startDate);
          orgs.add(org.symbol);
        }
        if (org.domain) {
          funcs.saveOneTypeValue(tags, new Tag(TagType.domain, org.domain, tag_index, org_start ), !!org.startDate);
          orgs.add(org.domain);
        }
      }
    }

    // TODO location
    // connection.userDefined;

    if (connection.urls) {
      for (const url of connection.urls) funcs.saveOne(urls, url.value.toLowerCase());
    }

    if (connection.photos) {
      for (const photo of connection.photos) funcs.saveOne(photos, photo.url);
    }

    // TODO check for really long values - like paragraphs - and break them up into bags of words
    if (connection.occupations) {
      for (const occupation of connection.occupations) {
        funcs.saveOneTypeValue(tags, new Tag( TagType.occupation, occupation.value, tag_index, start ));
        tag_index++;

        const meaning = parsers.findMeaning(occupation.value);
        for (const mean of meaning) {
          if (!names.includes(mean)) { // && !orgs.has(mean)) {
            funcs.saveOneTypeValue(tags, new Tag(TagType.skill, mean.toLowerCase(), 200, start, 100 ));
          }
        }
      }
    }

    if (connection.skills) {
      for (const skill of connection.skills) {
        const meaning = parsers.findMeaning(skill.value);
        if (!meaning.length) meaning.push(skill.value)
        for (const mean of meaning) {
          if (!names.includes(mean)) {
            funcs.saveOneTypeValue(tags, new Tag(TagType.skill, mean.toLowerCase(), 200, start, 100 ));
          }
        }
      }
    }

    if (connection.biographies) {
      for (const bio of connection.biographies) {
        const meaning = parsers.findMeaning(bio.value);
        if (!meaning.length) meaning.push(bio.value);
        for (const mean of meaning) {
          if (!names.includes(mean)) {
            tag_index++;
            funcs.saveOneTypeValue(tags, new Tag( TagType.skill, mean.toLowerCase(), 200, start, 100));
          }
        }
      }
    }

    if (connection.braggingRights) {
      for (const brag of connection.braggingRights) {
        const meaning = parsers.findMeaning(brag.value);
        if (!meaning.length) meaning.push(brag.value)
        for (const mean of meaning) {
          if (!names.includes(mean)) {
            funcs.saveOneTypeValue(tags, new Tag( TagType.skill,  mean.toLowerCase(), 200, start,  100 ));
          }
        }
      }
    }

    if (connection.phoneNumbers) {
      for (const phone of connection.phoneNumbers) {
        let phone_value:string = null
        if (phone.canonicalForm) {
          const phones = parsers.findPhone(phone.canonicalForm, locale);
          if (phones && phones.length) phone_value = phones[0];
        }

        if (!phone_value && phone.value) {
          const phones = parsers.findPhone(phone.value, locale);
          if (phones && phones.length) phone_value = phones[0];
        }
        
        if (phone_value && typeof phone_value === 'string' && phone_value.length) funcs.saveOne(comms, phone_value);
      }
    }

    if (connection.emailAddresses) {
      for (const email of connection.emailAddresses) {
        funcs.saveOne(comms, email ? email.value.toLowerCase() : null);

        if (email.displayName) {
          const [d,n,p] = funcs.permuteName(email.displayName);
          if (!display || display.length < d.length) display = d;
          funcs.saveOne(names, d);
          for (const nn of n) funcs.saveOne(names, nn);
          for (const pn of p) funcs.saveOne(names, pn);
        }

        const mp = peopleUtils.parseMessagePerson(email.value);
        if (mp) {
          if (!display) display = mp.displayName;
          for (const mp_name of mp.names) funcs.saveOne(names, mp_name);
        }
      }
    }

    if (connection.imClients) {
      for (const imc of connection.imClients) {
        if (typeof imc.username === 'string') funcs.saveOne(comms, imc.username);
      }
    }

    let id:string = null;
    if (connection.resourceName.startsWith('people/c')) id = connection.resourceName;

    if (id) funcs.saveOne(comms,id);

    // if(connection.events !== undefined) logging.infoF(LOG_NAME, 'makeSavePerson', 'Events: %s', connection.events[0]);
    // if(connection.relations !== undefined) logging.infoF(LOG_NAME< 'makeSavePerson', Rel: %s', connection.relations[0]);

    // need at least one tag for skill queries to work
    if (!tags.length) tags.push(new Tag(TagType.skill));

    return new Person({
      id,
      displayName: display ? display : 'No Name',
      nickName: first,
      names,
      comms,
      tags,
      urls,
      photos,
    });
  }

  static googleNameFromPerson(person: Person): people_v1.Schema$Name {
    let displayName: string = person.displayName;

    if (!displayName) {
      if (person.nickName) displayName = person.nickName;
      else if (person.names && person.names.length) displayName = person.names.sort((a,b) => b.length - a.length)[0];
      else if (person.email) {
        displayName = person.email
          .split('@')[0]
          .trim()
          .replace(/^['"]|['"]$/g, '')
          .replace(/^<|>$/g, '')
          .replace(/\./g, ' ');
      } else {
        displayName = 'UNKNOWN';
      }
    }

    const [firstName, middleName, lastName] = person.displayNameToParts();

    if (!person.nickName) person.nickName = firstName;

    return {
      displayName,
      displayNameLastFirst: `${lastName} ${firstName}`,
      familyName: lastName,
      givenName: firstName,
      middleName,
    };
  }

  static googleOrganizationFromTags(tags: Tag[]) {
    const tag = findTypeTag(tags, TagType.organization);
    const department = findTypeValue(tags, TagType.department);
    const title = findTypeValue(tags, TagType.jobTitle);
    const jobDescription = findTypeValue(tags, TagType.jobDescription);
    const symbol = findTypeValue(tags, TagType.ticker);
    const domain = findTypeValue(tags, TagType.domain);

    const org = {
      name: tag.value,
      startDate: GoogleConverter.googleDateFromDate(tag.start),
      metadata: { primary: false, verified: false, source: { type: 'CONTACT' } },
      type: TagType.work,
    };

    if (department) org[TagType.department] = department;
    if (title) org[TagType.title] = title;
    if (jobDescription) org[TagType.jobDescription] = jobDescription;
    if (symbol) org[TagType.symbol] = symbol;
    if (domain) org[TagType.domain] = domain;

    return org;
  }

  /**
   * Convert a Fora Person to a Google Person
   *
   * @param person - the Fora person to convert
   * @returns Converted person
   */
  static personToGooglePerson(person: Person): people_v1.Schema$Person  {
    const emails = parsers.findEmail(person.comms);
    const phones = parsers.findPhone(person.comms);
    const imc = person.comms.filter(c => {
      const phone = parsers.findPhone(c);
      return !emails.includes(c) && (!phone.length || !phones.includes(phone[0])) && !parsers.findId(c)
    }).map(c => c.toLowerCase());

    const emailAddresses = emails.map(value => { return { value }});
    const phoneNumbers = phones.map(value => { return { value }});
    const imClients = imc.map(username => { return { username }});
    const name = GoogleConverter.googleNameFromPerson(person); 

    const organizations = [];
    const occupations = [];
    for (const tag of person.tags) {
      if (tag.type as TagType === TagType.organization) {
        const tags = findTypesAtIndex(person.tags, tag.index);
        logging.debugF(LOG_NAME, 'personToGooglePerson', `Mapping tags ${JSON.stringify(tags)}`);
        organizations.push(GoogleConverter.googleOrganizationFromTags(tags));
      }
      else if (tag.type as TagType === TagType.occupation) occupations.push({ value: tag.value });
    }

    const urls = person.urls.map(value => { return { value } } );
    // const skills = findTypeValues(person.tags, TagType.skill).map(value => { return { value } });

    return {
      resourceName: person.id,
      names: [name],
      nicknames: [{ value: person.nickName }],
      emailAddresses,
      organizations,
      occupations,
      phoneNumbers,
      urls,
      // skills,
      imClients,
    };
  }

    /**
   * Update data in a Google person with an AskFora
   *
   * @param google_person - the Google person to update
   * @param person - the Fora person to convert
   * @returns Converted person
   */
  static updateGooglePerson(google_person: people_v1.Schema$Person, person: Person) {
    const gdiff = ['names'];

    google_person.names = [GoogleConverter.googleNameFromPerson(person)];

    // nicknames
    let nick = person.nickName;
    if (google_person.nicknames) {
      const remove_nicks: number[] = [];
      for (let index = 0; index < google_person.nicknames.length; index++) {
        const nn = google_person.nicknames[index];
        if (nn.value === nick) {
          nick = null;
          break;
        } else remove_nicks.push(index);
      }

      if (remove_nicks.length) {
        gdiff.push('nicknames');
        google_person.nicknames = google_person.nicknames.filter((n,i) => !remove_nicks.includes(i));
      }
    } else google_person.nicknames = [];

    if (nick) {
      gdiff.push('nicknames');
      google_person.nicknames.push({value: nick});
    }

    // organizations
    const orgs = findTypeValues(person.tags, TagType.organization);
    if (google_person.organizations) {
      const remove_orgs: number[] = [];
      for (let index = 0; index <  google_person.organizations.length; index++) {
        const org = google_person.organizations[index];
        const mindex = orgs.indexOf(org.name);
        if (mindex !== -1) orgs.splice(mindex, 1);
        else remove_orgs.push(index);
      }

      if (remove_orgs.length) {
        gdiff.push('organizations');
        google_person.organizations = google_person.organizations.filter((o,i) => !remove_orgs.includes(i));
      }
    } else if (orgs.length) google_person.organizations = [];

    if (orgs.length) {
      gdiff.push('organizations');
  
      const organizations = [];
      for (const tag of person.tags) {
        if (tag.type as TagType === TagType.organization && orgs.includes(tag.value)) {
          const tags = findTypesAtIndex(person.tags, tag.index);
          logging.debugF(LOG_NAME, 'updateGooglePerson', `Mapping tags ${JSON.stringify(tags)}`);
          organizations.push(GoogleConverter.googleOrganizationFromTags(tags));
        }
      }
    }

    const occs = findTypeValues(person.tags, TagType.occupation);
    if (google_person.occupations) {
      const remove_occs: number[] = [];
      for (let index = 0; index <  google_person.occupations.length; index++) {
        const occ = google_person.occupations[index];
        const mindex = occs.indexOf(occ.value);
        if (mindex !== -1) occs.splice(mindex, 1);
        else remove_occs.push(index);
      }

      if (remove_occs.length) {
        gdiff.push('occupations');
        google_person.occupations = google_person.occupations.filter((o,i) => !remove_occs.includes(i));
      }
    } else if(occs.length) google_person.occupations = [];

    if (occs.length) {
      gdiff.push('occupations');
      google_person.occupations = google_person.occupations.concat(occs.map(value => { return { value }}));
    }

    // skills
    /* const skills = findTypeValues(person.tags, TagType.skill);
    if (google_person.skills) {
      const remove_skills: number[] = [];
      for (let index = 0; index < google_person.skills.length; index++) {
        const skill = google_person.skills[index];
        const mindex = skills.indexOf(skill.value.toLowerCase());
        logging.debugF(LOG_NAME, 'updateGooglePerson', `Matching skill ${skill.value.toLowerCase()} in ${skills}: ${mindex}`);
        if (mindex !== -1) skills.splice(mindex, 1);
        else remove_skills.push(index);
      }

      if (remove_skills.length) {
        gdiff.push('skills');
        google_person.skills = google_person.skills.filter((u,i) => !remove_skills.includes(i));
      }
    } else if (skills.length) google_person.skills = [];

    if (skills.length) {
      gdiff.push('skills');
      google_person.skills = google_person.skills.concat(skills.map(value => { return {value}}));
    }*/

    // urls
    const urls = person.urls.slice();
    if (google_person.urls) {
      const remove_urls: number[] = [];
      for (let index = 0; index < google_person.urls.length; index++) {
        const url = google_person.urls[index];
        const mindex = urls.indexOf(url.value.toLowerCase());
        logging.debugF(LOG_NAME, 'updateGooglePerson', `Matching url ${url.value.toLowerCase()} in ${urls}: ${mindex}`);
        if (mindex !== -1) urls.splice(mindex, 1);
        else remove_urls.push(index);
      }

      if (remove_urls.length) {
        gdiff.push('urls');
        google_person.urls = google_person.urls.filter((u,i) => !remove_urls.includes(i));
      }
    } else if (urls.length) google_person.urls = [];

    if (urls.length) {
      gdiff.push('urls');
      google_person.urls = google_person.urls.concat(urls.map(value => { return {value}}));
    }

    // comms
    const emails = parsers.findEmail(person.comms);
    const phones = parsers.findPhone(person.comms);
    const imc = person.comms.map(c => c.toLowerCase()).filter(c => {
      const phone = parsers.findPhone(c);
      return !emails.includes(c) && (!phone.length || !phones.includes(phone[0])) && !parsers.findId(c)
    });

    // emailAddresses
    if (google_person.emailAddresses) {
      const remove_emails: number[] = [];
      for (let index = 0; index < google_person.emailAddresses.length; index++) {
        const email = google_person.emailAddresses[index];
        const mindex = emails.indexOf(email.value.toLowerCase());
        if (mindex !== -1) emails.splice(mindex, 1);
        else remove_emails.push(index);
      }
      if (remove_emails.length) {
        gdiff.push('emailAddresses');
        google_person.emailAddresses= google_person.emailAddresses.filter((e,i) => !remove_emails.includes(i));
      }
    } else if (emails.length) google_person.emailAddresses = [];

    if (emails.length) {
      gdiff.push('emailAddresses');
      google_person.emailAddresses = google_person.emailAddresses.concat( emails.map(value => { return { value } }));
    }
    
    if (google_person.phoneNumbers) {
      const remove_phones: number[] = [];
      for (let index = 0; index < google_person.phoneNumbers.length; index++) {
        const phone = google_person.phoneNumbers[index];
        const g_phone = parsers.findPhone(phone.value);
        const mindex = g_phone ? phones.indexOf(g_phone[0]) : -1;
        logging.debugF(LOG_NAME, 'updateGooglePerson', `comparing person ${phones} to google ${parsers.findPhone(phone.value)}: ${mindex}`);
        if (mindex !== -1) phones.splice(mindex, 1);
        else remove_phones.push(index);
      }

      if (remove_phones.length) {
        gdiff.push('phoneNumbers');
        google_person.phoneNumbers = google_person.phoneNumbers.filter((p, i) => !remove_phones.includes(i));
      }
    } else if (phones.length) google_person.phoneNumbers = [];

    if (phones.length) {
      gdiff.push('phoneNumbers');
      google_person.phoneNumbers = google_person.phoneNumbers.concat(phones.map(value => { return { value } }));
    }

    if (google_person.imClients) {
      const remove_ims: number[] = [];
      for (let index = 0; index < google_person.imClients.length; index++) {
        const mindex = imc.indexOf(google_person.imClients[index].username.toLowerCase());
        if (mindex !== -1) imc.splice(mindex, 1);
        else remove_ims.push(index);
      }

      if (remove_ims.length) {
        gdiff.push('imClients');
        google_person.imClients = google_person.imClients.filter((c,i) => !remove_ims.includes(i));
      }
    } else if (imc.length) google_person.imClients = [];

    if (imc.length) {
      gdiff.push('imClients');
      google_person.imClients = google_person.imClients.concat(imc.map(username => { return { username } }));
    }

    return _.uniq(gdiff);
  }

  static usersFromGoogleUsers(g_users: admin_directory_v1.Schema$User[], groups: Uid[]): User[] {
    const users: User[] = [];

    for (const g_user of g_users.filter(g => !g.archived && !g.suspended)) {
      const user = new User({
        id: g_user.id,
        email: g_user.primaryEmail,
        profile: g_user.id,
        name: g_user.name.givenName,
        groups,
        auth_ids: [`${AuthProviders.Google}_${g_user.id}`],
      });

      users.push(user);
    }

    return users;
  }

  static peopleFromGoogleUsers(g_users: admin_directory_v1.Schema$User[], org: string): Person[] {
    const people: Person[] = [];

    for (const g_user of g_users.filter(g => !g.archived && !g.suspended)) {
      const person = new Person({
        comms: [
          ...parsers.findEmail(g_user.emails ? g_user.emails.map(e => e.address) : []), 
          ...parsers.findEmail(g_user.recoveryEmail ? g_user.recoveryEmail : []),
          ...parsers.findPhone(g_user.recoveryPhone ? g_user.recoveryPhone : []),
        ].filter(c => c && c.length),
        displayName: g_user.name.fullName,
        nickName: g_user.name.givenName,
      });

      if (org) person.tags.push(new Tag(TagType.organization, org));

      person.tempId();

      people.push(person);
    }

    return people;
  }
}
