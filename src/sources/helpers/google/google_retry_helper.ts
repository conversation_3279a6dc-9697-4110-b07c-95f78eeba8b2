/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { setTimeout } from "timers/promises";
import util from 'util';
import { InternalError } from '../../../types/globals';
import logging from '../../../utils/logging';

const LOG_NAME = 'GoogleRetryHelper';

const RETRY_CODES = [403, 429, 500, 503, 4];
const PERMISSION_ERROR = 'Insufficient Permission';
const NOT_AUTHORIZED_ERROR = 'Not Authorized to access this resource/api';
const SUCCESS_CODES = [200, 202, 204];
const HANDLE_CODES = [404, 403];
const MULTIPLE_ERRORS = [413];

const SCODES = ['EHOSTUNREACH', 'ETIMEDOUT','ECONNRESET', 'EAI_AGAIN', 'EPIPE', 'ENOTFOUND', 'INTERNAL', 'DEADLINE_EXCEEDED', 'socket', 'deadline', 'rate', 'exceeded', 'truncated', 'connect', 'disconnected'];
const ICODES = [1,4,13,14,403,429,443,500, 503,504];

export function retryError(err) {
  let lmessage = err.message ? err.message.toLowerCase() : null;
  if (MULTIPLE_ERRORS.includes(err.code) || MULTIPLE_ERRORS.includes(err.statusCode)) return false;

  return SCODES.includes(err.code as string) || SCODES.includes(err.statusCode as string) || ICODES.includes(err.code as number) || 
    (lmessage && 
      (lmessage.includes('socket') || lmessage.includes('deadline') || lmessage.includes('rate') || lmessage.includes('truncated') 
       || lmessage.includes('exceeded') || lmessage.includes('connect') || lmessage.includes('timeout')
       || lmessage.includes('network') || lmessage.includes('disconnected')));
}

export class GoogleRetryHelper {
  static async repeatableCallWithBackOff(call: any, callName: string, thisArg: any, args: any): Promise<any> {
    let response = null;
    let retry = 10;

    while (response === null) {
      try {
        response = await call.bind(thisArg, args)();
      } catch (err) {
        if (retryError(err) && retry < 60000 && err.message !== PERMISSION_ERROR && err.message !== NOT_AUTHORIZED_ERROR) {
          await setTimeout(retry);
          retry *= 2;
        } else if (HANDLE_CODES.includes(err.code)) {
          logging.warnF(LOG_NAME, callName, `Handled status ${err.code} ${err.message}`);
          return null;
        } else {
          throw err;
        }
      }
    }

    if (response) {
      if (SUCCESS_CODES.includes(response.status)) return response;
      else if (response.error) {
        const args_no_auth = {};
        Object.assign(args_no_auth, args);
        delete args_no_auth['auth'];
        throw new InternalError(response.error.code, `Error calling ${callName} with args ${args_no_auth}: ${response.error.message}`);
      }
      else throw new Error(`Unexpected response from Google call '${callName}' :: ${util.format(response)}`);
    } else {
      throw new Error(`Unexpected response from Google call '${callName}' :: nothing returned`);
    }
  }
}
