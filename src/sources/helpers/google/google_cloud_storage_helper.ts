/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Bucket, File, GetBucketsResponse, GetFileMetadataResponse, GetFilesResponse, Storage } from '@google-cloud/storage';
import { setTimeout } from "timers/promises";
import util from 'util';

import config from '../../../config';

import ForaUser from '../../../session/user';

import { Uid } from '../../../types/shared';

import logging from '../../../utils/logging';

const LOG_NAME = 'data.utils.google.GoogleCloudStorageHelper';

export class GoogleCloudStorageHelper {
  static async bucketDelete(user: ForaUser, account: Uid, storage: Storage): Promise<void> {
    try {
      const bucket = await GoogleCloudStorageHelper.bucketGet(user, account, storage);
      if (bucket) {
        await bucket.delete();
        logging.infoFP(LOG_NAME, 'bucketDelete', user ? user.profile : null, `Bucket deleted = ${bucket.name}`);
      } else logging.warnFP(LOG_NAME, 'bucketDelete', user ? user.profile : null, `Attempted to delete a bucket that does not exist - ${user ? user.profile : ''}`);
    } catch (err) {
      const name = GoogleCloudStorageHelper.bucketName(user, account);
      logging.errorFP(LOG_NAME, 'bucketDelete', user ? user.profile : null, `Error deleting bucket - ${name}`, err);
      throw err;
    }
  }

  static async bucketEnsure(user: ForaUser, account: Uid, storage: Storage): Promise<Bucket> {
    try {
      let bucket = await GoogleCloudStorageHelper.bucketGet(user, account, storage);
      if (!bucket) bucket = await GoogleCloudStorageHelper.bucketCreate(user, account, storage);

      logging.debugFP(LOG_NAME, 'bucketEnsure', user ? user.profile : null, `Ensured bucket = ${bucket.name}`);
      return bucket;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'bucketEnsure', user ? user.profile : null, 'Error ensuring bucket', err);
      throw err;
    }
  }

  static async file(user: ForaUser, account: Uid, storage: Storage, name: string): Promise<File> {
    try {
      const bucket = await GoogleCloudStorageHelper.bucketEnsure(user, account, storage);
      return await bucket.file(name);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'fileGetMetadata', user ? user.profile : null, `Error getting file metadata- ${name}`, err);
      throw err;
    }
  }

  static async fileContents(user: ForaUser, file: File): Promise<Buffer> {
    try {
      const stream = await file.createReadStream();

      const read_promise = new Promise<Buffer>((resolve, reject) => {
        const buffers = [];
        stream.on('data', buffer => buffers.push(buffer));
        stream.on('end', () => resolve(Buffer.concat(buffers)));
        stream.on('error', reject);
      });
      return await read_promise;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'fileContents', user ? user.profile : null, `Error getting file contents - ${file.name}`, err);
      throw err;
    }
  }

  static async globalFileCreate(storage: Storage, name: string, properties: { [key: string]: string }, contents: string | Buffer): Promise<File> {
    return GoogleCloudStorageHelper.fileCreate(null, null, storage, name, properties, contents); 
  }

  static async fileCreate(user: ForaUser, account: Uid, storage: Storage, name: string, properties: { [key: string]: string }, contents: string | Buffer): Promise<File> {
    let retry = 10;
    while(retry < 512000) {
      try {
        const bucket = await GoogleCloudStorageHelper.bucketEnsure(user, account, storage);
        let file = bucket.file(name);

        await file.save(contents, {
          gzip: true, // Support for HTTP requests made with `Accept-Encoding: gzip`
          metadata: {
            cacheControl: 'no-cache',
            metadata: properties,
          },
        });

        // // Setting the metadata during save does not appear to be working
        // await file.setMetadata({metadata: properties});

        // Re-read the file and send it back to the caller
        file = await GoogleCloudStorageHelper.file(user, account, storage, name);
        if (logging.isDebug(user ? user.profile: null)) logging.debugFP(LOG_NAME, 'fileCreate', user ? user.profile : null, `Created file = ${file.id}`);
        return file;
      } catch (err) {
        if (['EHOSTUNREACH','ECONNRESET'].includes(err.code) || (err.message && err.message.includes('Retry'))) {
          logging.warnFP(LOG_NAME, 'fileCreate', user ? user.profile : null, `Error creating file ${name} - retrying ${retry}`, err);
          await setTimeout(retry);
          retry *= 2;
        } else {
          logging.errorFP(LOG_NAME, 'fileCreate', user ? user.profile : null, `Error creating file ${name}`, err);
          throw err;
        }
      }
    }
  }

  static async fileStream(user: ForaUser, account: Uid, storage: Storage, name: string, properties: { [key: string]: string }): Promise<{file: File, url: string}> {
    let retry = 10;
    while(retry < 512000) {
      try {
        const bucket = await GoogleCloudStorageHelper.bucketEnsure(user, account, storage);
        let file = bucket.file(name);

        await bucket.setCorsConfiguration([{
          method: ['PUT', 'POST', 'OPTIONS'],
          origin: ['https://askfora.com', 'https://askfora.ngrok.io', 'https://group.askfora.ngrok.io', 'http://localhost'],
          maxAgeSeconds: 30, 
          responseHeader: ['cache-control', 'authorization', 'x-requested-with', 'x-goog-content-sha256', 'accept', 'origin', 'content-type', 'access-control-allow-origin', 'location', 'content-length', 'x-goog-resumable'],
        }]);

        const [url] = await file.createResumableUpload({
          metadata: {
            cacheControl: 'no-cache',
            metadata: properties,
          },
        });

        return {file, url};
      } catch (err) {
        if (['EHOSTUNREACH','ECONNRESET'].includes(err.code) || (err.message && err.message.includes('Retry'))) {
          logging.warnFP(LOG_NAME, 'fileCreate', user ? user.profile : null, `Error creating file ${name} - retrying ${retry}`, err);
          await setTimeout(retry);
          retry *= 2;
        } else {
          logging.errorFP(LOG_NAME, 'fileCreate', user ? user.profile : null, `Error creating file ${name}`, err);
          throw err;
        }
      }
    }
  }

  static async fileDelete(user: ForaUser, account: Uid, storage: Storage, name: string): Promise<void> {
    let retry = 10;
    while(retry < 512000) {
      try {
        const bucket = await GoogleCloudStorageHelper.bucketEnsure(user, account, storage);
        await bucket.file(name).delete();
        break;
      } catch (err) {
        if (['EHOSTUNREACH','ECONNRESET'].includes(err.code) || (err.message && err.message.includes('Retry'))) {
          logging.warnFP(LOG_NAME, 'fileDelete', user ? user.profile : null, `Error deleting file ${name} - retrying ${retry}`, err);
          await setTimeout(retry);
          retry *= 2;
        } else {
          logging.errorFP(LOG_NAME, 'fileDelete', user ? user.profile : null, `Error deleting file - ${name}`, err);
          throw err;
        }
      }
    }
  }

  static async fileMetadata(user: ForaUser, account: Uid, file: File): Promise<any> {
    // Metadata is not exported from typing
    let response: GetFileMetadataResponse;
    try {
      response = await file.getMetadata();
    } catch (err) {
      logging.warnFP(LOG_NAME, 'fileMetadata', user ? user.profile : null, `Error getting file metadata - ${file.name}`, err);
      throw err;
    }

    if (response && response[0]) {
      const metadata = response[0];
      logging.debugFP(LOG_NAME, 'fileMetadata', user ? user.profile : null, `File metadata = ${util.format(metadata)}`);
      return metadata;
    } else {
      logging.warnFP(LOG_NAME, 'fileMetadata', user ? user.profile : null, `Unknown bucket response - ${util.format(response)}`);
      throw new Error('Unknown file metadata response');
    }
  }

  static async files(user: ForaUser, account: Uid, storage: Storage): Promise<File[]> {
    let response: GetFilesResponse;
    try {
      const bucket = await GoogleCloudStorageHelper.bucketEnsure(user, account, storage);
      response = await bucket.getFiles({ autoPaginate: true });
    } catch (err) {
      logging.errorFP(LOG_NAME, 'files', user ? user.profile : null, 'Error reading files', err);
      throw err;
    }

    if (response && response[0]) {
      const files = response[0];
      logging.debugFP(LOG_NAME, 'files', user ? user.profile : null, `Files length = ${util.format(files.length)}`);
      return files;
    } else {
      logging.warnFP(LOG_NAME, 'files', user ? user.profile : null, `Unknown bucket response - ${util.format(response)}`);
      throw new Error('Unknown file list response');
    }
  }

  static async fileUpdate(user: ForaUser, account: Uid, storage: Storage, name: string, properties: { [key: string]: string }, contents: string | Buffer): Promise<File> {
    return GoogleCloudStorageHelper.fileCreate(user, account, storage, name, properties, contents);
  }

  private static bucketName(user: ForaUser, account: Uid): string { 
    // add global project name and restrict to 63 max characters total
    return `${config.get('GOOGLE_CLOUD_PROJECT')}-${account ? account : user ? user.profile.toLowerCase().replace(/\./g, '_').replace(/-$/, 'z') : 'people-export'}`.slice(0,63).replace(/-$/, '');
  }

  private static async bucketCreate(user: ForaUser, account: Uid, storage: Storage): Promise<Bucket> {
    let response;
    const bucket_name = GoogleCloudStorageHelper.bucketName(user, account);
    try {
      response = await storage.createBucket(bucket_name);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'bucketCreate', user ? user.profile : null, `Error creating bucket ${bucket_name}`, err);
      throw err;
    }

    if (response && response[0]) {
      const bucket = response[0];
      logging.debugFP(LOG_NAME, 'bucketCreate', user ? user.profile : null, `Created bucket = ${bucket.name}`);
      return bucket;
    } else {
      logging.warnFP(LOG_NAME, 'bucketCreate', user ? user.profile : null, `Unknown bucket response - ${util.format(response)}`);
      throw new Error('Unknown bucket create response');
    }
  }

  private static async bucketGet(user: ForaUser, account: Uid, storage: Storage): Promise<Bucket> {
    let response: GetBucketsResponse;

    const bucket_name = GoogleCloudStorageHelper.bucketName(user, account);

    try {
      response = await storage.getBuckets({prefix: bucket_name, maxResults: 1});
    } catch (err) {
      logging.errorFP(LOG_NAME, 'bucketGet', user ? user.profile : null, 'Error getting bucket', err);
      throw err;
    }

    return response?.length ? response[0][0] : null;

    /*if (response && response[0]) {
      for (const bucket of response[0]) {
        if (bucket.name === bucket_name) {
          logging.debugFP(LOG_NAME, 'bucketGet', user ? user.profile : null, `Found bucket = ${bucket.name}`);
          return bucket;
        }
      }

      logging.warnFP(LOG_NAME, 'bucketGet', user ? user.profile : null, `Bucket ${bucket_name} NOT found`);
    } else {
      logging.warnFP(LOG_NAME, 'bucketGet', user ? user.profile : null, `Unknown bucket response - ${util.format(response)}`);
      throw new Error('Unknown bucket list response');
    }*/

    // return null;
  }
}
