/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import { IEntity, ImportType } from '../types/items';
import { Uid } from '../types/shared';

export interface IImportPlugin {
  checkImport(): boolean;

  checkZipType(data: any[]): ImportType;

  importData(): Promise<IEntity[]>;

  createUserGroups(): Promise<Uid[]>;
}
