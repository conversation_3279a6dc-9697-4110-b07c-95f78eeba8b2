/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { OAuth2Client } from 'google-auth-library';
import { AuthProvider } from '../auth/auth_provider';
import { IEntity } from '../types/items';
import { AuthProviders } from '../types/shared';
import { AbstractSourcePlugin } from './a_source_plugin';
import { GoogleRetryHelper } from './helpers/google/google_retry_helper';
import { ISourceMetadata } from './i_source_metadata';

export abstract class AbstractGoogleSourcePlugin<T extends IEntity, M extends ISourceMetadata> extends AbstractSourcePlugin<T, M> {
  private client: OAuth2Client;

  /**
   * Convenience method to lazy create our client
   */
  getClient(): OAuth2Client {
    if (!this.client) this.client = AuthProvider.google.getAuthClient(this.user.getTokens(AuthProviders.Google, this.account));
    return this.client;
  }

  protected async repeatableCallWithBackOff(call: any, callName: string, thisArg: any, args: any): Promise<any> {
    return GoogleRetryHelper.repeatableCallWithBackOff(call, callName, thisArg, args);
  }
}
