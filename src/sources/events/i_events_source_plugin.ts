/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Event } from '../../types/items';
import { ISourceMetadata } from '../i_source_metadata';
import { ISourcePlugin } from '../i_source_plugin';

export interface IEventsSourcePlugin extends ISourcePlugin<Event, ISourceMetadata> {
  /**
   * Mark an event as `cancelled`
   *
   * @param event Event to mark cancelled
   * @return marked Event
   */
  cancel(event: Event): Promise<Event>;
}
