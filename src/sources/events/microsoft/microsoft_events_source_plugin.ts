/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client } from '@microsoft/microsoft-graph-client';
import { Attendee, Calendar, Event as GraphEvent, Recipient } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';

import config from '../../../config';
import data from '../../../data';

import ForaUser from '../../../session/user';

import { Event, IEntity, Person } from '../../../types/items';
import { AuthProviders, EntityType, TagType, Uid } from '../../../types/shared';

import { DAYS } from '../../../utils/datetime';
import { slimEntity } from '../../../utils/funcs';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';

import { AbstractMicrosoftSourcePlugin } from '../../a_microsoft_source_plugin';
import MicrosoftGraphCalendarHelper from '../../helpers/microsoft/microsoft_graph_calendar_helper';
import { MicrosoftGraphConverter } from '../../helpers/microsoft/microsoft_graph_conversions';
import { SourceKeys } from '../../index';
import { CalendarMetadata } from '../google/google_events_source_metadata';
import { IEventsSourcePlugin } from '../i_events_source_plugin';
import { MicrosoftEventsSourceMetadata } from './microsoft_events_source_metadata';

import { AuthProvider } from '../../../auth/auth_provider';

const LOG_NAME = 'data.sources.MicrosoftEventsSourcePlugin';

/**
 * Microsoft Events Source Plugin
 *
 *  - https://docs.microsoft.com/en-us/graph/api/resources/calendar?view=graph-rest-1.0
 *  - https://docs.microsoft.com/en-us/graph/api/resources/calendar?view=graph-rest-beta
 *  - https://docs.microsoft.com/en-us/graph/api/resources/event?view=graph-rest-1.0
 *  - PAGING - https://docs.microsoft.com/en-us/graph/delta-query-events
 */
export class MicrosoftEventsSourcePlugin extends AbstractMicrosoftSourcePlugin<Event, MicrosoftEventsSourceMetadata> implements IEventsSourcePlugin {
  public static providers = [AuthProviders.Microsoft, AuthProviders.Msal];

  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(
      LOG_NAME,
      SourceKeys.MicrosoftEvents,
      EntityType.Event,
      user,
      account,
      user_people,
      user_emails,
      MicrosoftEventsSourceMetadata,
      user.hasAccount(AuthProviders.Msal, account) ? AuthProviders.Msal : AuthProviders.Microsoft,
      user.hasAccount(AuthProviders.Msal, account) ? AuthProvider.msal.getScopesForEvents() : AuthProvider.microsoft.getScopesForEvents(),
      true,
      true,
      'end',
      config.get('MSFT_ESP_EXPIRATION_DAYS_BACK', 30),
    );
  }

  /** @inheritDoc */
  async cancel(event: Event): Promise<Event> {
    try {
      await MicrosoftGraphCalendarHelper.calendarEventCancel(this.user, event.id, 'Cancelled by AskFora');
      return event;
    } catch (err) {
      throw new Error(`Error cancelling event ${err}`);
    }
  }

  /** @inheritDoc */
  async create(f_event_in: Event): Promise<[Event, IEntity[]]> {
    // grab email from attendees and strip to just id and displayName for saving
    const attendees = [];
    for (const index in f_event_in.people) {
      const person = f_event_in.people[index];

      if (person.comms) {
        let email = null;
        for (const commsIndex in person.comms) {
          const e = parsers.findEmail(person.comms[commsIndex]);
          if (e && e.length) {
            email = e[0];
            break;
          }
        }
        if (email) attendees.push({ emailAddress: { address: email } } as Attendee);
      }

      f_event_in.people[index] = { id: person.id, displayName: person.displayName } as Person;
    }

    let calendar_id;
    if (f_event_in.calendarId && f_event_in.calendarId !== 'primary') calendar_id = f_event_in.calendarId;
    else calendar_id = 'primary';

    try {
      const ms_event_in = MicrosoftGraphConverter.convertEventToGraphEvent(f_event_in);
      const ms_event_out = await MicrosoftGraphCalendarHelper.calendarEventCreate(this.user, this.account, ms_event_in, calendar_id);
      const f_event_out = MicrosoftGraphConverter.convertEventFromGraphEvent(calendar_id, ms_event_out);
      f_event_out.people = f_event_in.people;

      return [f_event_out, []];
    } catch (err) {
      throw new Error(`Error creating event ${err}`);
    }
  }

  /** @inheritDoc */
  async reload(f_event_in: Event): Promise<Event> {
    try {
      const ms_event_in = MicrosoftGraphConverter.convertEventToGraphEvent(f_event_in);
      const ms_event_out = await MicrosoftGraphCalendarHelper.calendarEventGet(this.user, this.account, ms_event_in, f_event_in.calendarId);
      const f_event_out = MicrosoftGraphConverter.convertEventFromGraphEvent(f_event_in.calendarId, ms_event_out);
      f_event_out.people = f_event_in.people;

      return f_event_out;
    } catch (err) {
      throw new Error(`Error reloading event ${err}`);
    }
  }

  /** @inheritDoc */
  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    const client: Client = await this.getGraphClient();
    const calendars: Calendar[] = await MicrosoftGraphCalendarHelper.calendars(this.user, this.account, client);
    if (calendars && calendars.length) {
      this.updateCalendarMetadata(calendars);
      const [updates, deletes] = await this.microsoftCalendarsGet(calendars, is_history);
      this.setFirstRunDone();
      return [updates, deletes];
    }
  }

  updateCalendarMetadata(ms_calendars: Calendar[]) {
    const sync_ids = [];
    if (ms_calendars && ms_calendars.length) {
      for (const ms_calendar of ms_calendars) {
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `calendar = ${util.format(ms_calendar)}`);

        sync_ids.push(ms_calendar.id);

        let sync_cal = false;
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `user.settings = ${util.format(this.user.settings)}`);
        if (!this.user.settings.calendars.sources[ms_calendar.id]) {
          if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `settings do not exist for calendar ${ms_calendar.id}`);

          if (!this.metaData().hasCalendar(ms_calendar.id)) {
            if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `metaData does not exist for calendar ${ms_calendar.id}, creating`);
            this.metaData().setupCalendar(ms_calendar.id, ms_calendar.name, true, true);
          } else {
            if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `metaData exists for calendar ${ms_calendar.id}, using`);
          }
        } else if (!this.metaData().hasCalendar(ms_calendar.id) || !this.user.settings.calendars.sources[ms_calendar.id].sync === undefined) {
          if (!this.metaData().hasCalendar(ms_calendar.id)) {
            if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `metaData does not exist for calendar ${ms_calendar.id}, creating`);
            this.metaData().setupCalendar(ms_calendar.id, ms_calendar.name, true, sync_cal);
          }
          this.metaData().getCalendar(ms_calendar.id).sync = true;
        } else if (!this.metaData().getCalendar(ms_calendar.id)) {
          if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `settings exist but not metaData for calendar ${ms_calendar.id}`);

          const name = this.user.settings.calendars.sources[ms_calendar.id].name;
          const selected = this.user.settings.calendars.sources[ms_calendar.id].selected;
          sync_cal = this.user.settings.calendars.sources[ms_calendar.id].sync;

          this.metaData().setupCalendar(ms_calendar.id, name, selected, sync_cal);
        } else {
          this.metaData().getCalendar(ms_calendar.id).name = ms_calendar.name;
        }
      }
    }

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `metadata before delete = ${util.format(this.metaData())}`);

    // Remove any old calendars we might know about
    const old_ids = this.metaData().getCalendarsIds();
    for (const old_id of old_ids) if (!sync_ids.includes(old_id)) this.metaData().deleteCalendar(old_id);

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `metadata after delete = ${util.format(this.metaData())}`);
  }

  private async addEventPerson(save_event: Event, ms_recipient: Recipient): Promise<Person> {
    if (!ms_recipient.emailAddress) return null;
    if (ms_recipient.emailAddress.name && ms_recipient.emailAddress.name.toLowerCase() === 'unknownorganizer') return null;
    if (ms_recipient.emailAddress.address && ms_recipient.emailAddress.address.toLowerCase().indexOf('unknownorganizer') > -1) return null;

    let person = await this.personByEmail(ms_recipient.emailAddress.address);

    if (!person) {
      person = new Person({
        displayName: ms_recipient.emailAddress.name,
        comms: [ms_recipient.emailAddress.address],
        self: this.user.email === ms_recipient.emailAddress.address,
      });
      
      person.tempId();

      // get a slightly better date on domain tags
      const start = new Date(save_event.start);
      person.tags
        .filter(t => t.type === TagType.domain)
        .map(tag => {
          if (new Date(tag.start) > start) tag.start = start;
        });

      person = this.addPerson(person);
    }

    return new Person(person);
  }

  private calendar(id: string): CalendarMetadata {
    return this.metaData().getCalendar(id);
  }

  private async microsoftCalendarsGet(ms_calendars: Calendar[], is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    const ms_calendars_enabled = ms_calendars.length ? ms_calendars.filter(ms_calendar => this.metaData().isCalendarEnabled(ms_calendar.id)) : [];
    logging.infoFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `Fetching events from ${ms_calendars_enabled.length} enabled calendar.`);

    let updates: IEntity[] = [];
    let deletes: IEntity[] = [];
    for (const ms_calendar of ms_calendars_enabled) {
      const [cal_updates, cal_deletes] = await this.microsoftGetEventsForCalendar(ms_calendar.id, is_history);
      updates = updates.concat(cal_updates);
      deletes = deletes.concat(cal_deletes);
    }

    if (!is_history) {
      const ms_calendars_disabled = ms_calendars.length ? ms_calendars.filter(ms_calendar => !this.metaData().isCalendarEnabled(ms_calendar.id)) : [];
      for (const ms_calendar of ms_calendars_disabled) {
        const [cal_updates, cal_deletes] = await this.microsoftGetEventsForCalendar(ms_calendar.id, false);
        updates = updates.concat(cal_updates);
        deletes = deletes.concat(cal_deletes);
      }
    }
    
    const old_events = await data.events.eventsByDate(this.user, null, DAYS(new Date(), config.get('GESP_HISTORY_TIME_MIN', -540)));
    deletes = deletes.concat(old_events);

    return [updates, deletes];
  }

  private async microsoftGetEventsForCalendar(calendar_id: string, is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    let api_path = `/me/calendars/${calendar_id}/calendarView/delta`;

    const now = new Date();

    if (is_history) {
      // Sync processing backwards

      if (this.calendar(calendar_id).historyDone) return [[], []];
      if (this.calendar(calendar_id).historyPageToken) api_path = this.calendar(calendar_id).historyPageToken;
      else {
        const time_max = DAYS(now, config.get('MSFT_ESP_HISTORY_TIME_MAX', 0)).toISOString();
        const time_min = DAYS(now, config.get('MSFT_ESP_HISTORY_TIME_MIN', -540)).toISOString();
        api_path += `?startDateTime=${time_min}&endDateTime=${time_max}`;
      }
    } else {
      // Sync processing forward

      if (this.calendar(calendar_id).nextPageToken) api_path = this.calendar(calendar_id).nextPageToken;
      else if (this.calendar(calendar_id).nextSyncToken) api_path = this.calendar(calendar_id).nextSyncToken;
      else {
        const time_max = DAYS(now, config.get('MSFT_ESP_REFRESH_TIME_MAX', 90)).toISOString();
        const time_min = DAYS(now, config.get('MSFT_ESP_REFRESH_TIME_MIN', -7)).toISOString();
        api_path += `?startDateTime=${time_min}&endDateTime=${time_max}`;
      }
    }

    try {
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `path = ${api_path}`);

      const result = await MicrosoftGraphCalendarHelper.getResponse(this.user, this.account, api_path, 500);

      if (logging.isDebug(this.user.profile)) {
        logging.debugFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `RESULT count = ${result.value.length}`);
        logging.debugFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `RESULT next link = ${result['@odata.nextLink']}`);
        logging.debugFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `RESULT delta link = ${result['@odata.deltaLink']}`);
      }

      const ms_events: GraphEvent[] = result.value;

      if (is_history) {
        this.calendar(calendar_id).historyDone = !result['@odata.nextLink'];
        this.calendar(calendar_id).historyPageToken = result['@odata.nextLink'];
      } else {
        if (result['@odata.nextLink']) this.calendar(calendar_id).newDone = false;
        else if (result['@odata.deltaLink']) this.calendar(calendar_id).newDone = true;
        else this.calendar(calendar_id).newDone = true;

        this.calendar(calendar_id).nextSyncToken = result['@odata.deltaLink'];
        this.calendar(calendar_id).nextPageToken = result['@odata.nextLink'];
      }

      if (logging.isDebug(this.user.profile)) {
        logging.debugFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `METADATA historyPageLink = ${this.calendar(calendar_id).historyPageToken}`);
        logging.debugFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `METADATA nextPageLink = ${this.calendar(calendar_id).nextPageToken}`);
        logging.debugFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `METADATA nextSyncLink = ${this.calendar(calendar_id).nextSyncToken}`);
      }
      if (ms_events.length) return await this.processEvents(calendar_id, ms_events);
      else return [[], []];
    } catch (err) {
      this.calendar(calendar_id).nextSyncToken = null;
      this.calendar(calendar_id).nextPageToken = null;
      if (err.code === 404) logging.errorFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `Could not find calendar ${calendar_id}.`, err);
      else logging.errorFP(this.log_name, 'microsoftCalendarRead', this.user.profile, `Error retrieving events for ${calendar_id}`, err);
      throw err;
    }
  }

  private async processEvents(calendar_id: string, ms_events: GraphEvent[]): Promise<[IEntity[], IEntity[]]> {
    const events_del: Event[] = [];
    const entities_add: IEntity[] = [];

    const now = new Date();
    const time_min = DAYS(now, config.get('MSFT_ESP_HISTORY_TIME_MIN', -540));
    const time_max = DAYS(now, config.get('MSFT_ESP_REFRESH_TIME_MAX', 90));

    for (const ms_event of ms_events) {
      const f_event = MicrosoftGraphConverter.convertEventFromGraphEvent(calendar_id, ms_event);
      if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'processEvents', this.user.profile, `Converted event = ${util.format(f_event)} }`);
      entities_add.push(f_event);

      if (ms_event.isCancelled || f_event.end < time_min || f_event.start > time_max) events_del.push(f_event);
      else {
        const event_attendees: Attendee[] = [];

        if (ms_event.attendees && ms_event.attendees.length) {
          for (const ms_attendee of ms_event.attendees) {
            if (ms_attendee.type !== 'resource') event_attendees.push(ms_attendee);
          }
        }

        if (ms_event.organizer) event_attendees.push(ms_event.organizer);

        const people_ids_added = [];
        for (const person of event_attendees) {
          const new_person = await this.addEventPerson(f_event, person);
          if (new_person && new_person.id) {
            if (!people_ids_added.includes(new_person.id)) {
              slimEntity(new_person);
              f_event.people.push(new_person);
              people_ids_added.push(new_person.id);
            }
          }
        }
      }
    }

    return [[...entities_add, ...this.new_people], events_del];
  }
}
