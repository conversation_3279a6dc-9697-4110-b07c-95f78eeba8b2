/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { <PERSON>sonConvert, JsonConverter, JsonCustomConvert, JsonObject, JsonProperty, OperationMode, ValueCheckingMode } from 'json2typescript';
import _ from 'lodash';
import config from '../../../config';
import { AbstractSourceMetadata } from '../../a_source_metadata';
import { ISourceMetadata } from '../../i_source_metadata';

@JsonObject('CalendarMetadata')
export class CalendarMetadata {
  @JsonProperty('historyDone', Boolean, true) historyDone: any;
  @JsonProperty('historyPageToken', String, true) historyPageToken: string;
  @JsonProperty('id', String, false) id: string;
  @JsonProperty('name', String, false) name: string;
  @JsonProperty('newDone', Boolean, true) newDone: boolean;
  @JsonProperty('nextPageToken', String, true) nextPageToken: string;
  @JsonProperty('nextSyncToken', String, true) nextSyncToken: string;
  @JsonProperty('selected', Boolean, false) selected: boolean;
  @JsonProperty('sync', Boolean, false) sync: boolean;

  constructor(id?: string, name?: string, selected = false, sync = false, historyDone = false, newDone = false, historyPageToken?: string, nextPageToken?: string, nextSyncToken?: string) {
    this.historyDone = historyDone;
    this.historyPageToken = historyPageToken;
    this.id = id;
    this.name = name;
    this.newDone = newDone;
    this.nextPageToken = nextPageToken;
    this.nextSyncToken = nextSyncToken;
    this.selected = selected;
    this.sync = sync;
  }
}

@JsonObject('CalendarDictionary')
export class CalendarDictionary {
  [id: string]: CalendarMetadata;
}

@JsonConverter
export class CalendarDictionaryConverter implements JsonCustomConvert<CalendarDictionary> {
  deserialize(data: any): CalendarDictionary {
    const convert = new JsonConvert();
    if (config.isSilly()) convert.operationMode = OperationMode.LOGGING;
    convert.ignorePrimitiveChecks = false; // don't allow assigning number to string etc.
    convert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL;
    const dict = new CalendarDictionary();
    for (const key in data) {
      if (data.hasOwnProperty(key)) {
        dict[key] = convert.deserializeObject(data[key], CalendarMetadata);
      }
    }
    return dict;
  }

  serialize(dictionary: CalendarDictionary): any {
    const out = {};
    const convert = new JsonConvert();
    if (config.isSilly()) convert.operationMode = OperationMode.LOGGING;
    convert.ignorePrimitiveChecks = false; // don't allow assigning number to string etc.
    convert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL;
    for (const key in dictionary) {
      if (dictionary.hasOwnProperty(key)) {
        const obj = dictionary[key];
        out[key] = convert.serialize(obj);
      }
    }

    return out;
  }
}

@JsonObject('GoogleEventsSourceMetadata')
export class GoogleEventsSourceMetadata extends AbstractSourceMetadata implements ISourceMetadata {
  // Have to allow null here because initially the store will have `{}` as the object
  @JsonProperty('calendars', CalendarDictionaryConverter, true) private _calendars: CalendarDictionary = new CalendarDictionary();

  get calendars(): CalendarDictionary {
    return this._calendars;
  }

  set calendars(value: CalendarDictionary) {
    this._calendars = value;
  }

  get historyDone(): boolean {
    if (_.isEmpty(this._calendars)) return false;
    else return _.every(Object.values(this._calendars), cal => !cal.sync || cal.historyDone);
  }

  set historyDone(value: boolean) {
    _.forEach(this._calendars, cal => {
      cal.historyDone = value
    });
  }

  get historyPageToken(): string {
    return null;
  }

  set historyPageToken(value: string) {
    if (value) throw new Error('historyPageToken has children, you must set them individually');
    else _.forEach(Object.values(this._calendars), calendar => {
      calendar.historyPageToken = value
    });
  }

  get newDone(): boolean {
    if (_.isEmpty(this._calendars)) return false;
    else return _.every(Object.values(this._calendars), cal => !cal.sync || cal.newDone);
  }

  set newDone(value: boolean) {
    _.forEach(this._calendars, cal => {
      cal.newDone = value
    });
  }

  get nextPageToken(): string {
    return null;
  }

  set nextPageToken(value: string) {
    if (value) throw new Error('nextPageToken has children, you must set them individually');
    else _.forEach(Object.values(this._calendars), calendar => {
      calendar.nextPageToken = value
     });
  }

  get nextSyncToken(): string {
    return null;
  }

  set nextSyncToken(value: string) {
    if (value) throw new Error('nextSyncToken has children, you must set them individually');
    else _.forEach(Object.values(this._calendars), calendar => {
      calendar.nextSyncToken = value
    });
  }

  deleteCalendar(id: string): void {
    delete this._calendars[id];
  }

  getCalendar(id: string): CalendarMetadata {
    return this._calendars[id];
  }

  getCalendarsIds(): string[] {
    return Object.keys(this._calendars);
  }

  hasCalendar(id: string): boolean {
    return !!this._calendars[id];
  }

  hasHistoryPageToken(): boolean {
    const first = _.find(Object.values(this._calendars), calendar => !!calendar.historyPageToken);
    return !!first;
  }

  hasNextPageToken(): boolean {
    const first = _.find(Object.values(this._calendars), calendar => !!calendar.nextPageToken);
    return !!first;
  }

  hasNextSyncToken(): boolean {
    const first = _.find(Object.values(this._calendars), calendar => !!calendar.nextSyncToken);
    return !!first;
  }

  isCalendarEnabled(id: string): boolean {
    return this._calendars[id] && this._calendars[id].sync;
  }

  setupCalendar(id: string, name: string, selected: boolean, sync: boolean): void {
    this._calendars[id] = new CalendarMetadata(id, name, selected, sync);
  }
}
