/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { calendar_v3, google } from 'googleapis';
import util from 'util';

import ForaUser from '../../../session/user';

import { Event, IEntity, Person } from '../../../types/items';
import { AuthProviders, EntityType, TagType, Uid } from '../../../types/shared';

import { DAYS, MINUTES } from '../../../utils/datetime';
import { slimEntity } from '../../../utils/funcs';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';

import { AuthProvider } from '../../../auth/auth_provider';
import config from '../../../config';
import data from '../../../data';
import { AbstractGoogleSourcePlugin } from '../../a_google_source_plugin';
import { SourceKeys } from '../../index';
import { IEventsSourcePlugin } from '../i_events_source_plugin';
import { CalendarMetadata, GoogleEventsSourceMetadata } from './google_events_source_metadata';

const calendar = google.calendar('v3');
const LOG_NAME = 'data.sources.GoogleEventsSourcePlugin';

/**
 * Google Events Source Plugin
 *
 *  - http://google.github.io/google-api-nodejs-client/modules/_apis_calendar_v3_.html
 *  - https://github.com/google/google-api-nodejs-client/blob/master/src/apis/calendar/v3.ts
 */
export class GoogleEventsSourcePlugin extends AbstractGoogleSourcePlugin<Event, GoogleEventsSourceMetadata> implements IEventsSourcePlugin {
  public static providers = [AuthProviders.Google];

  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(LOG_NAME, SourceKeys.GoogleEvents, EntityType.Event, user, account, user_people, user_emails, GoogleEventsSourceMetadata, AuthProviders.Google, AuthProvider.google.getScopesForEvents(), true, true, 'end', config.get('GESP_EXPIRATION_DAYS_BACK', 30));
  }

  /** @inheritDoc */
  async cancel(event: Event): Promise<Event> {
    const args = {
      auth: this.getClient() as any,
      calendarId: event.calendarId,
      eventId: event.id.slice(event.calendarId.length + 1),
      requestBody: {
        start: { dateTime: new Date(event.start).toISOString() },
        end: { dateTime: new Date(event.end).toISOString() },
        status: 'cancelled',
      } as calendar_v3.Schema$Event,
    } as calendar_v3.Params$Resource$Events$Update;

    const response = await this.repeatableCallWithBackOff(calendar.events.update, 'calendar.events.update', calendar, args);
    return this.foraEventFromGoogleEvent(event.calendarId, response.data);
  }

  async create(f_event_in: Event): Promise<[Event, IEntity[]]> {
    // grab email from attendees and strip to just id and displayName for saving
    const attendees = [];
    for (const index in f_event_in.people) {
      const person = f_event_in.people[index];

      if (person.comms) {
        let email = null;
        for (const commsIndex in person.comms) {
          const e = parsers.findEmail(person.comms[commsIndex]);
          if (e && e.length) {
            email = e[0];
            break;
          }
        }
        if (email) attendees.push({ email });
      }

      f_event_in.people[index] = { id: person.id, displayName: person.displayName } as Person;
    }

    const args = {
      auth: this.getClient() as any,
      calendarId: 'primary',
      requestBody: {
        summary: f_event_in.title,
        start: f_event_in.start,
        end: f_event_in.end,
        location: f_event_in.location,
        attendees,
      } as calendar_v3.Schema$Event,
    } as calendar_v3.Params$Resource$Events$Insert;

    const g_event_out: calendar_v3.Schema$Event = await this.googleInsert(args);
    const f_event_out = this.foraEventFromGoogleEvent('primary', g_event_out);
    f_event_out.people = f_event_in.people;
    return [f_event_out, []];
  }

  /** @inheritDoc */
  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    const g_calendars: calendar_v3.Schema$CalendarList = await this.googleCalendarList();
    if (g_calendars) {
      this.updateCalendarMetadata(g_calendars);
      const [updates, deletes] = await this.googleCalendarsRead(g_calendars, is_history);
      this.setFirstRunDone();
      return [updates, deletes];
    }
  }

  updateCalendarMetadata(g_calendars: calendar_v3.Schema$CalendarList) {
    const sync_ids = [];
    if (g_calendars.items) {
      for (const g_calendar of g_calendars.items) {
        if (!g_calendar.deleted && g_calendar.accessRole !== "freeBusyReader") sync_ids.push(g_calendar.id);

        let sync_cal = false;
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `user.settings = ${util.format(this.user.settings)}`);
        if (!this.user.settings.calendars.sources[g_calendar.id]) {
          if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `settings do not exist for calendar ${g_calendar.id}`);

          if (!this.metaData().hasCalendar(g_calendar.id)) {
            if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `metaData does not exist for calendar ${g_calendar.id}, creating`);

            let selected = false;
            if (g_calendar.accessRole === 'owner' || g_calendar.primary) {
              selected = !g_calendar.hidden && g_calendar.selected;
              sync_cal = true;
            }

            this.metaData().setupCalendar(g_calendar.id, g_calendar.summary, selected, sync_cal);
          } else {
            if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `metaData exists for calendar ${g_calendar.id}, using`);
          }
        } else if (!this.metaData().hasCalendar(g_calendar.id) || !this.user.settings.calendars.sources[g_calendar.id].sync === undefined) {
          if (!this.metaData().hasCalendar(g_calendar.id)) {
            if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `metaData does not exist for calendar ${g_calendar.id}, creating`);
            this.metaData().setupCalendar(g_calendar.id, g_calendar.summary, !g_calendar.hidden && g_calendar.selected, sync_cal);
          }
          this.metaData().getCalendar(g_calendar.id).sync = true;
        } else if (!this.metaData().getCalendar(g_calendar.id)) {
          if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'updateCalendarMetadata', this.user.profile, `settings exist but not metaData for calendar ${g_calendar.id}`);

          let name = this.user.settings.calendars.sources[g_calendar.id].name;
          if (g_calendar.summaryOverride) name = g_calendar.summaryOverride;

          const selected = this.user.settings.calendars.sources[g_calendar.id].selected;
          sync_cal = this.user.settings.calendars.sources[g_calendar.id].sync;

          this.metaData().setupCalendar(g_calendar.id, name, selected, sync_cal);
        } else {
          if (g_calendar.summaryOverride) {
            this.metaData().getCalendar(g_calendar.id).name = g_calendar.summaryOverride;
          } else {
            this.metaData().getCalendar(g_calendar.id).name = g_calendar.summary;
          }
        }
      }
    }

    // Remove any old calendars we might know about
    const old_ids = this.metaData().getCalendarsIds();
    for (const old_id of old_ids) {
      if (!sync_ids.includes(old_id)) this.metaData().deleteCalendar(old_id);
    }
  }

  private async addEventPerson(save_event: Event, event_person: Person): Promise<Person> {
    let person: Person = null;

    if (event_person.displayName && event_person.displayName.toLowerCase() === 'unknownorganizer') return null;
    if (event_person.email && event_person.email.toLowerCase().indexOf('unknownorganizer') > -1) return null;

    if (event_person.id) person = await this.personById(event_person.id);
    else person = await this.personByEmail(event_person.email);

    if (!person) {
      person = new Person({
        displayName: event_person.displayName,
        comms: [event_person.email],
        self: this.user.email === event_person.email,
      });
      
      person.tempId();

      // get a slightly better date on domain tags
      const start = new Date(save_event.start);
      person.tags
        .filter(t => t.type === TagType.domain)
        .map(tag => {
          if (new Date(tag.start) > start) tag.start = start;
        });

      person = this.addPerson(person);
    }

    return new Person(person);
  }

  private calendar(id: string): CalendarMetadata {
    return this.metaData().getCalendar(id);
  }

  private foraEventFromGoogleEvent(calendar_id: string, g_event: calendar_v3.Schema$Event): Event {
    return new Event({
      id: `${calendar_id}_${g_event.id}`,
      calendarId: calendar_id,
      title: g_event.summary, // ? g_event.summary.substring(0, 1500) : null,
      notes: g_event.description, // ? g_event.description.substring(0, 1500) : null,
      start: g_event.start.dateTime ? new Date(g_event.start.dateTime) : MINUTES(new Date(g_event.start.date), this.user.offset),
      end: g_event.end.dateTime ? new Date(g_event.end.dateTime) : MINUTES(new Date(g_event.end.date), this.user.offset),
      link: g_event.htmlLink,
      location: g_event.location, // ? g_event.location.substring(0, 1500) : '',
    });
  }

  private async googleCalendarList(): Promise<calendar_v3.Schema$CalendarList> {
    try {
      const response = await this.repeatableCallWithBackOff(calendar.calendarList.list, 'calendar.calendarList.list', calendar, { auth: this.getClient() as any});
      return response ? response.data : null;
    } catch (err) {
      logging.errorFP(this.log_name, 'googleCalendarList', this.user.profile, 'Unable to get list of calendars', err);
      throw err;
    }
  }

  private async googleCalendarRead(calendar_id: string, is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    const args = {
      auth: this.getClient() as any,
      alwaysIncludeEmail: true,
      calendarId: calendar_id,
      // orderBy:'startTime',
      // requestSyncToken:true,
      singleEvents: true,
      showDeleted: true,
      syncToken: undefined,
      timeMax: undefined,
      timeMin: undefined,
      pageToken: undefined,
      maxResults: 2500,
    } as calendar_v3.Params$Resource$Events$List;

    const now = new Date();

    const calendarMd = this.calendar(calendar_id);

    if (is_history) {
      // Sync processing backwards

      if (calendarMd && calendarMd.historyDone) return [[], []];
      if (calendarMd && calendarMd.historyPageToken) args.pageToken = calendarMd.historyPageToken;
      else {
        args.timeMax = DAYS(now, config.get('GESP_HISTORY_TIME_MAX', 0)).toISOString();
        args.timeMin = DAYS(now, config.get('GESP_HISTORY_TIME_MIN', -540)).toISOString();
      }
    } else {
      // Sync processing forward

      if (calendarMd && calendarMd.nextPageToken) args.pageToken = calendarMd.nextPageToken;
      else if (calendarMd && calendarMd.nextSyncToken) args.syncToken = calendarMd.nextSyncToken;
      else {
        args.timeMax = DAYS(now, config.get('GESP_REFRESH_TIME_MAX', 90)).toISOString();
        args.timeMin = DAYS(now, config.get('GESP_REFRESH_TIME_MIN', -7)).toISOString();
      }
    }

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'googleCalendarRead', this.user.profile, `args = ${util.format(args)}`);
    try {
      const response = await this.repeatableCallWithBackOff(calendar.events.list, 'calendar.events.list', calendar, args);
      const g_events: calendar_v3.Schema$Events = response.data;

      if (calendarMd) {
        if (is_history) {
          if (g_events.nextPageToken) {
            calendarMd.historyDone = false;
            calendarMd.historyPageToken = g_events.nextPageToken;
          } else {
            calendarMd.historyDone = true;
            calendarMd.historyPageToken = null;
          }
        } else {
          if (g_events.nextPageToken) {
            calendarMd.newDone = false;
            calendarMd.nextPageToken = g_events.nextPageToken;
          } else if (g_events.nextSyncToken) {
            calendarMd.newDone = true;
            calendarMd.nextSyncToken = g_events.nextSyncToken;
          } else {
            // TODO is this the correct processing in this context?
            calendarMd.newDone = true;
            calendarMd.nextSyncToken = null;
          }
        }
      }

      if (g_events.items) {
        if (g_events.accessRole !== "none" && g_events.accessRole !== "freeBusyReader") return await this.processEvents(calendar_id, g_events.items);
        else {
          const events_del: Event[] = [];
          for (const g_event of g_events.items) {
            const f_event = this.foraEventFromGoogleEvent(calendar_id, g_event);
            events_del.push(f_event);
          }

          return [[], events_del];
        }

      }

      return [[], []];
    } catch (err) {
      if (!calendarMd) {
        logging.errorFP(this.log_name, 'googleCalendarRead', this.user.profile, `missing calendar ${calendar_id}`, new Error('missing calendar'));
        // don't throw if we somehow just missed the calendar
        return [[], []];
      } else {
        calendarMd.nextSyncToken = null;
        calendarMd.nextPageToken = null;
      }

      if (err.code === 410 && err.message.slice(0, 4) === 'Sync') {
        logging.infoFP(this.log_name, 'googleCalendarRead', this.user.profile, `Re-Syncing calendar ${calendar_id}: ${err.message}`);
        return this.googleCalendarRead(calendar_id, is_history);
      } else if (err.code === 404) logging.errorFP(this.log_name, 'googleCalendarRead', this.user.profile, `Could not find calendar ${calendar_id}.`, err);
      else logging.errorFP(this.log_name, 'googleCalendarRead', this.user.profile, `Error retrieving events for ${calendar_id}`, err);
      throw err;
    }
  }

  private async googleCalendarsRead(g_calendars: calendar_v3.Schema$CalendarList, is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    const g_calendars_enabled = g_calendars.items ? g_calendars.items.filter(gCal => this.metaData().isCalendarEnabled(gCal.id)) : [];
    logging.infoFP(this.log_name, 'googleCalendarsRead', this.user.profile, `Fetching events from ${g_calendars_enabled.length} enabled calendar.`);

    let updates: IEntity[] = [];
    let deletes: IEntity[] = [];
    for (const g_calendar of g_calendars_enabled) {
      const [cal_updates, cal_deletes] = await this.googleCalendarRead(g_calendar.id, is_history);
      updates = updates.concat(cal_updates);
      deletes = deletes.concat(cal_deletes);
    }

    if (!is_history) {
      const g_calendars_disabled = g_calendars.items ? g_calendars.items.filter(gCal => !this.metaData().isCalendarEnabled(gCal.id)) : [];
      for (const g_calendar of g_calendars_disabled) {
        const [cal_updates, cal_deletes] = await this.googleCalendarRead(g_calendar.id, false);
        updates = updates.concat(cal_updates);
        deletes = deletes.concat(cal_deletes);
      }
    }

    // delete data older than hitory time
    const old_events = await data.events.eventsByDate(this.user, null, DAYS(new Date(), config.get('GESP_HISTORY_TIME_MIN', -540)));
    deletes = deletes.concat(old_events);
    
    return [updates, deletes];
  }

  private async googleInsert(args: calendar_v3.Params$Resource$Events$Insert): Promise<calendar_v3.Schema$Event> {
    const response = await this.repeatableCallWithBackOff(calendar.events.insert, 'calendar.events.insert', calendar, args);
    return response.data;
  }

  private async processEvents(calendar_id: string, g_events: calendar_v3.Schema$Event[]): Promise<[IEntity[], IEntity[]]> {
    const events_del: Event[] = [];
    const entities_add: IEntity[] = [];

    const now = new Date();
    const time_min = DAYS(now, config.get('GESP_HISTORY_TIME_MIN', -540));
    const time_max = DAYS(now, config.get('GESP_REFRESH_TIME_MAX', 90));

    for (const g_event of g_events) {
      const f_event = this.foraEventFromGoogleEvent(calendar_id, g_event);

      if (g_event.status === 'cancelled' || f_event.end < time_min || f_event.start > time_max) events_del.push(f_event);
      else {
        const event_people = [];

        if (g_event.attendees && g_event.attendees.length) {
          for (const att of g_event.attendees) {
            if (att.email && !att.resource) event_people.push(att);
          }
        }

        if (g_event.organizer) event_people.push(g_event.organizer);

        entities_add.push(f_event);

        const people_ids_added = [];
        for (const person of event_people) {
          const new_person = await this.addEventPerson(f_event, person);
          if (new_person && new_person.id) {
            if (!people_ids_added.includes(new_person.id)) {
              slimEntity(new_person);
              f_event.people.push(new_person);
              people_ids_added.push(new_person.id);
            }
          }
        }
      }
    }

    return [[...entities_add, ...this.new_people], events_del];
  }

  public async reload(f_event_in: Event): Promise<Event> {
    const args = {
      auth: this.getClient() as any,
      calendarId: f_event_in.calendarId,
      eventId: f_event_in.id.slice(f_event_in.calendarId.length + 1),
      alwaysIncludeEmail: true,
    } as calendar_v3.Params$Resource$Events$Get;

    const response = await this.repeatableCallWithBackOff(calendar.events.get, 'calendar.events.get', calendar, args);
    return response ? this.foraEventFromGoogleEvent(f_event_in.calendarId, response.data) : null;
  }
}
