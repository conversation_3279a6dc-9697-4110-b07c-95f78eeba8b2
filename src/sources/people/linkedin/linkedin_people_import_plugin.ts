/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import fs from 'fs';
import path from 'path';

import ForaUser from '../../../session/user';

import { FileType, PersonParseMap } from '../../../types/globals';
import { Document, Person, Tag } from '../../../types/items';
import { EducationLevel, Imports, JobTags, TagType, Uid, findTypeValues } from '../../../types/shared';

import { stringFromBody } from '../../../utils/format';
import { mergeTags, saveOneTypeValue } from '../../../utils/funcs';
import { Meanings, addMeaning, checkMapCSV, fileType, peopleFromCSV, saveMeaningTags } from '../../../utils/imports';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';

import { IImportPlugin } from '../../i_import_plugin';

const LOG_NAME = 'LinkedInPeopleImportPlugin';
const ROOT = '../../../';

export class LinkedInPeopleImportPlugin implements IImportPlugin {
  connections_import_map: PersonParseMap[] = null;
  contacts_import_map: PersonParseMap[] = null;
  recommendations_import_map: PersonParseMap[] = null;
  profile_import_map: PersonParseMap[] = null;

  constructor(public user: ForaUser = null, public self: Partial<Person> = null, public doc: Document = null, public import_type: string = null) {
    try {
      this.connections_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'linkedin_connections_import_map.json')).toString());
      this.contacts_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'linkedin_contacts_import_map.json')).toString());
      this.recommendations_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'linkedin_recommendations_import_map.json')).toString());
      this.profile_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'linkedin_profile_import_map.json')).toString());
    } catch (err) {
      logging.errorFP(LOG_NAME, 'import_map', user ? user.profile : null, 'Error loading import map', err);
    }
  }

   async createUserGroups(): Promise<Uid[]> {
    return null;
  }

  checkImport(): boolean {
    try {
      if (this.import_type === Imports.LinkedIn) return true;
      if ( this.doc && this.doc.props && this.doc.props['file_type'] === FileType.CSV) {
        const csv = stringFromBody(this.doc.body);
        if (this.doc.props.originalname) {
          switch(this.doc.props.originalname.toLowerCase()) {
            case 'connections.csv':
              return this.connections_import_map && checkMapCSV(csv, this.connections_import_map) === this.connections_import_map.length;
            case 'contacts.csv':
              return this.contacts_import_map && checkMapCSV(csv, this.contacts_import_map) === this.contacts_import_map.length;
            case 'profile.csv':
              return this.profile_import_map && checkMapCSV(csv, this.profile_import_map) === this.profile_import_map.length;
            case 'recommendations received.csv':
            case 'recommendations_received.csv':
            case 'recommendations given.csv':
            case 'recommendations_given.csv':
              return this.recommendations_import_map && checkMapCSV(csv, this.recommendations_import_map) === this.recommendations_import_map.length;
          }
        }

        return ( this.connections_import_map && checkMapCSV(csv, this.connections_import_map) === this.connections_import_map.length ) ||
               ( this.contacts_import_map && checkMapCSV(csv, this.contacts_import_map) === this.contacts_import_map.length ) ||
               ( this.recommendations_import_map && checkMapCSV(csv, this.recommendations_import_map) === this.recommendations_import_map.length ) ||
               ( this.profile_import_map && checkMapCSV(csv, this.profile_import_map) === this.profile_import_map.length );
      }
    } catch (e) {
      logging.warnFP(LOG_NAME, 'checkImport', this.user.profile, 'not a LinkedIn file');
    }
    return false;
  }

  checkZipType(data: any[]): Imports {
    for (const zfile of data) {
      if (zfile.path && zfile.path.toLowerCase().includes('connections.csv')) return Imports.LinkedIn;
    }
  }

  fileImport(file_name: string, csv: string): Person[] {
    let people: Person[] = [];
    switch(file_name.toLowerCase()) {
      case 'connections.csv': 
        people = peopleFromCSV('linkedin_connections', csv, this.connections_import_map);
        break;
      case 'contacts.csv': 
        people = peopleFromCSV('linkedin_contacts', csv, this.contacts_import_map);
        break;
      case 'profile.csv': {
          const profiles = peopleFromCSV('linkedin_profile', csv, this.profile_import_map)
          if (profiles && profiles.length) {
            const profile = profiles[0];
            if (!profile.comms) profile.comms = [];
            if (!parsers.findEmail(profile.comms)) profile.comms.push(this.user.email);
            people = [profile];
          }
        }
      case 'recommendations received.csv':
      case 'recommendations_received.csv': {
          const rec = peopleFromCSV('linkedin_recommendations_received', csv, this.recommendations_import_map);
          const profile = new Person(this.self);
          const lnames = profile.names.map(n => n.toLowerCase());
          for (const r of rec) {
            const rec_skills = r.tags.filter(t => t.type === TagType.skill);
            r.tags = r.tags.filter(t => t.type !== TagType.skill);
            for (const t of rec_skills) {
              if (!lnames.includes(t.value)) saveOneTypeValue(profile.tags, t);
            }
          }
          people = rec.concat([profile]);
        }
      case 'recommendations given.csv': 
      case 'recommendations_given.csv': 
        people = peopleFromCSV('linkedin_recommendations_given', csv, this.recommendations_import_map);
        break;
      default:
        if (file_name.toLowerCase().endsWith('.html')) {
          const profile = new Person(this.self);
          const skills = parsers.parseHTML(csv);
          const meanings: Meanings = {};

          const lnames = (profile.names ? profile.names.map(n => n.toLowerCase()) : []).concat(['self']);
          const jobs = profile.tags ? findTypeValues(profile.tags, JobTags).map(j => j.toLowerCase()) : [];
          const ignore = [...lnames, ...jobs, ...EducationLevel];

          addMeaning(meanings, skills, 10, ignore);
          const learn_tags = saveMeaningTags(meanings, ignore, 1, 0);
          mergeTags(profile.tags, learn_tags);
          people = [profile];
        } else {
          logging.warnFP(LOG_NAME, 'importData', this.user.profile, `Unknown file ${file_name}`);
        }
    }

    return people;
  }

  async importData(): Promise<Person[]> {
    let people: Person[] = [];
    if (this.user && this.doc && this.import_type === Imports.LinkedIn && 
      this.contacts_import_map && this.connections_import_map && this.profile_import_map && this.recommendations_import_map) {
      const [file_type, data] =  await fileType(this.doc);
      logging.infoFP(LOG_NAME, 'importData', this.user.profile, `importing ${file_type} from ${this.doc.id}`);

      switch (file_type) {
        case FileType.CSV:
          if (this.doc.props && this.doc.props.originalname) {
            const csv: string = stringFromBody(this.doc.body);
            people = people.concat(this.fileImport(this.doc.props.originalname.toLowerCase(), csv));
          }
          break;
        case FileType.ZIP:
          for (const zfile of data) {
            if (zfile.path) people = people.concat(this.fileImport(zfile.path, stringFromBody(zfile.data)));
          }
          break;
      }
    }

    for (const person of people) {
      saveOneTypeValue(person.tags, new Tag(TagType.import_type, 'linkedin', 0, new Date()));
    }

    logging.infoFP(LOG_NAME, 'importData', this.user.profile, `imported ${people.length} people from ${this.doc.id}`);

    return people;
  }
}
