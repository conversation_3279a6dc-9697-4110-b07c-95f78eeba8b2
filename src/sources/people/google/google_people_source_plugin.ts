/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { google, people_v1 } from 'googleapis';
import _ from 'lodash';
import util from 'util';

import { AuthProvider } from '../../../auth/auth_provider';
import ForaUser from '../../../session/user';

import { InternalError } from '../../../types/globals';
import { IEntity, Person } from '../../../types/items';
import { AuthProviders, EntityType, Uid } from '../../../types/shared';

import { mergeTags } from '../../../utils/funcs';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';
import peopleUtils from '../../../utils/people';

import { AbstractGoogleSourcePlugin } from '../../a_google_source_plugin';
import { GoogleConverter } from '../../helpers/google/google_conversions';
import { SourceKeys } from '../../index';
import { IPeopleSourcePlugin } from '../i_people_source_plugin';
import { GooglePeopleSourceMetadata } from './google_people_source_metadata';

const people = google.people('v1');
const LOG_NAME = 'data.sources.GooglePeopleSourcePlugin';

const PERSON_FIELDS = [
  'biographies',
  'names',
  'nicknames',
  'organizations',
  'occupations',
  'skills',
  'interests',
  'braggingRights',
  'emailAddresses',
  'imClients',
  'phoneNumbers',
  'relations',
  'events',
  'urls',
  'metadata',
  'photos',
].join(',');

/**
 * Google People Source Plugin
 *
 *  - http://google.github.io/google-api-nodejs-client/modules/_apis_people_v1_.html
 *  - https://github.com/google/google-api-nodejs-client/blob/master/src/apis/people/v1.ts
 *  - https://developers.google.com/people/api/rest/v1/people.connections/list
 */
export class GooglePeopleSourcePlugin extends AbstractGoogleSourcePlugin<Person, GooglePeopleSourceMetadata> implements IPeopleSourcePlugin {
  private remove_people = [];
  public static providers = [AuthProviders.Google];

  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(LOG_NAME, SourceKeys.GooglePeople, EntityType.Person, user, account, user_people, user_emails, GooglePeopleSourceMetadata, AuthProviders.Google, AuthProvider.google.getScopesForPeople(), false, false);
  }

  /** @inheritDoc */
  async create(person: Person): Promise<[Person, IEntity[]]> {
    const comms = person.comms ? parsers.findEmail(person.comms) : null;
    if (!comms || !comms.length) throw new InternalError(500, 'Cannot create or update Google contact without email', person);
    const deletes: Person[] = [];

    // existing people with google ids may need to be updated within google
    if (person.id && person.id.startsWith('people/c')) {
      const args = {
        auth: this.getClient() as any,
        resourceName: person.id,
        personFields: PERSON_FIELDS,
      } as people_v1.Params$Resource$People$Get;

      // fetch the latest google person
      let g_person: people_v1.Schema$Person;
      try {
        const result = await this.repeatableCallWithBackOff(people.people.get, 'people.get', people, args);
        g_person = result ? result.data : null;
      } catch (err) {
        if (err.code === 404) g_person = null;
        else throw err;
      }

      // if the google person still exists, update
      if (g_person) {
        if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'create', this.user.profile, `Found maching google person for ${person.id} is ${g_person.resourceName}`);
        const diff = GoogleConverter.updateGooglePerson(g_person, person);

        if (diff.length) {
          if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'create', this.user.profile, `Updating google person ${person.id} with ${JSON.stringify(diff)}`);
          const args2 = {
            auth: this.getClient() as any,
            resourceName: g_person.resourceName,
            updatePersonFields: _.join(diff),
            requestBody: g_person,
          } as people_v1.Params$Resource$People$Updatecontact;

          const update_result = await this.repeatableCallWithBackOff(people.people.updateContact, 'people.updateContact', people, args2);
          g_person = update_result.data;
          if (g_person) {
            if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'create', this.user.profile, `Updated google person ${person.id} to ${g_person.resourceName}`);
            return [person, deletes];
          }
        }
      } else {
        if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'create', this.user.profile, `Deleting google person ${person.id} not found at google`);
        // exiting google person was deleted at google, remove from data store and clear id to create a new one
        deletes.push({ id: person.id, type: EntityType.Person } as Person);
        person.id = null;
      }
    } else if(person.id !== null && person.id !== undefined) {
      logging.warnFP(LOG_NAME, 'create', this.user.profile, `Not saving person ${person.id} to Google`); 
      return [person, []];
    }

    const new_google_person: people_v1.Schema$Person = GoogleConverter.personToGooglePerson(person);
    const args = {
      auth: this.getClient() as any,
      // parent: 'people/me',
      resource: new_google_person,
    } as people_v1.Params$Resource$People$Createcontact;

    try {
      const result = await this.repeatableCallWithBackOff(people.people.createContact, 'people.createContact', people, args);
      if (result) {
        const new_person = GoogleConverter.personFromGooglePerson(result.data, this.user.locale);
        if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'create', this.user.profile, `Created new goole person from ${person.id} to ${new_person.id} as ${result.data.resourceName}`);
        if (!new_person.tags) new_person.tags = [];
        peopleUtils.mergePeople(new_person, person, true);
        new_person.self = person.self;
        mergeTags(new_person.tags, person.tags);
        return [new_person, deletes];
      } else return [person, []];
    } catch (err) {
      logging.errorFP(this.log_name, 'create', this.user.profile, `Error creating contact ${JSON.stringify(new_google_person)}`, err);
      // TODO(current), we should be returning the deletes here and not a []?
      return [person, !person.id || deletes.length && deletes[0].id !== person.id ? deletes : []];
    }
  }

  async delete(person: Partial<Person>): Promise<void> {
    if (person.id && person.id.startsWith('people/c')) {
      const args = {
        auth: this.getClient() as any,
        resourceName: person.id,
      } as people_v1.Params$Resource$People$Deletecontact;

      try {
        const result = await this.repeatableCallWithBackOff(people.people.deleteContact, 'people.deleteContact', people, args);
      } catch (err) {
        if (err.code === 404) {
          // Person deleted at Google. Nothing to do
        } else throw err;
      }
    }
  }

  /** @inheritDoc */
  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    const args = {
      auth: this.getClient() as any,
      resourceName: 'people/me',
      pageSize: is_history ? 2000 : 100,
      requestSyncToken: true,
      personFields: PERSON_FIELDS,
      pageToken: undefined,
      syncToken: undefined,
    } as people_v1.Params$Resource$People$Connections$List;

    if (is_history) {
      // Sync processing backwards
      if (this.metaData().historyDone) return [[], this.remove_people.filter(p => !this.new_people_ids.includes(p.id))];
      if (this.metaData().historyPageToken) args.pageToken = this.metaData().historyPageToken;
    } else {
      // Sync processing forward
      if (this.metaData().nextPageToken) args.pageToken = this.metaData().nextPageToken;
      else if (this.metaData().nextSyncToken) args.syncToken = this.metaData().nextSyncToken;
      else args.pageSize = 2000;
    }

    if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'providerRead', this.user.profile, `args = ${util.format(args)}`);
    try {
      const result = await this.repeatableCallWithBackOff(people.people.connections.list, 'people.connections.list', people, args);
      if (!result) throw new Error('Call to people.connections.list returned null');

      const g_people: people_v1.Schema$ListConnectionsResponse = result.data;
      if (!g_people) throw new Error('Call to people.connections.list returned no data');

      if (is_history) {
        if (g_people.nextPageToken) {
          this.metaData().historyDone = false;
          this.metaData().historyPageToken = g_people.nextPageToken;
        } else {
          this.metaData().nextSyncToken = g_people.nextSyncToken;
          this.setDataHistoryDone();
        }
      } else {
        if (g_people.nextPageToken) {
          this.metaData().newDone = false;
          this.metaData().nextPageToken = g_people.nextPageToken;
        } else if (g_people.nextSyncToken) {
          this.metaData().newDone = true;
          this.metaData().firstRunDone = true;
          this.metaData().nextPageToken = null;
          this.metaData().nextSyncToken = g_people.nextSyncToken;
          this.setDataHistoryDone();
        } else {
          // TODO is this the correct processing in this context?
          this.metaData().newDone = true;
          this.metaData().firstRunDone = true;
          this.metaData().nextSyncToken = null;
          this.setDataHistoryDone();
        }
      }

      if (g_people.connections && g_people.connections.length) {
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'providerRead', this.user.profile, `${g_people.connections.length} connection changes returned`);
        return this.processPeople(g_people.connections);
      } else {
        logging.infoFP(this.log_name, 'providerRead', this.user.profile, 'No people changes returned');
        return [[], this.remove_people.filter(p => !this.new_people_ids.includes(p.id))];
      }
    } catch (err) {
      this.metaData().nextSyncToken = null;
      this.metaData().nextPageToken = null;
      if ((err.code === 410 || err.code === 400) && err.message.slice(0, 4) === 'Sync') {
        logging.infoFP(this.log_name, 'providerRead', this.user.profile, `Re-Syncing people: ${err.message}`);
        return this.providerRead(is_history);
      }

      logging.errorFP(this.log_name, 'providerRead', this.user.profile, 'Error loading people', err);
      throw err;
    }
  }

  private async processPeople(gPeople: people_v1.Schema$Person[]): Promise<[Person[], Person[]]> {
    const deletes: Person[] = [];
    const updates: Person[] = [];

    for (const connection of gPeople) {
      const person = GoogleConverter.personFromGooglePerson(connection, this.user.locale);

      if (connection.metadata.deleted) {
        deletes.push(person);
        this.cache.clearCacheAttVal(this.user.profile, this.typeFor(), 'comms', person);
      } else {
        if (person.comms.length > 0) person.comms = person.comms.filter(c => !parsers.spamAddress(c));
        updates.push(person);
        this.addPerson(person);
      }
    }

    return [updates, deletes.concat(this.remove_people).filter(p => p && !this.new_people_ids.includes(p.id))];
  }

  public reset() {
    super.reset();
    // remove all people/c
    const remove_comms = [];
    for (const comm in this.user_emails) {
      if (this.user_emails[comm]) {
        const remove = this.user_emails[comm].filter(c => c.startsWith('people/c'));
        if (remove.length) {
          for (const id in remove) {
            this.remove_people.push(this.user_people[id]);
            delete this.user_people[id];
          }
          if (remove.length === this.user_emails[comm].length) remove_comms.push(comm);
        }
      }
    }

    const remove_ids = [];
    for (const id in this.user_people) {
      if (id.startsWith('people/c')) {
        const person = this.user_people[id];
         const email = person.comms && person.comms.length ? parsers.findEmail(person.comms) : null;
         if (!email || !email.length) {
           remove_ids.push(id);
           this.remove_people.push(this.user_people[id]);
         }
      }
    }

    for (const id of remove_ids) delete this.user_people[id];
    for (const comm of remove_comms) delete this.user_emails[comm];
  }

  async reload(f_person_in: Person): Promise<Person> {
    if (f_person_in.id && f_person_in.id.startsWith('people/c')) {
      const args = {
        auth: this.getClient() as any,
        resourceName: f_person_in.id,
        personFields: PERSON_FIELDS,
      } as people_v1.Params$Resource$People$Get;

      // fetch the latest google person
      let g_person: people_v1.Schema$Person;
      try {
        const result = await this.repeatableCallWithBackOff(people.people.get, 'people.get', people, args);
        g_person = result ? result.data : null;
      } catch (err) {
        if (err.code === 404) g_person = null;
        else throw err;
      }

      // if the google person still exists, update
      if (g_person) {
        const new_person = GoogleConverter.personFromGooglePerson(g_person, this.user.locale);
        if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'create', this.user.profile, `Found goole person ${new_person.id}`);
        peopleUtils.mergePeople(new_person, f_person_in, true);
        if (!new_person.tags) new_person.tags = [];
        new_person.self = f_person_in.self;
        return new_person;
      }
    }
    return null;
  }

  async batchReload(f_people: Partial<Person>[]): Promise<Person[]> {
    const source_people: Person[] = [];
    const find_people = f_people ? f_people.filter(p => p.id && p.id.startsWith('people/c')) : [];
    const auth = this.getClient() as any;
    while (find_people.length) {
      const find_set: {[key:string]: Partial<Person>} = {};
      for (const p of find_people.splice(0,200)) find_set[p.id] = p;
      const args = {
        auth,
        resourceNames: Object.keys(find_set),
        personFields: PERSON_FIELDS,
      } as people_v1.Params$Resource$People$Get;

      // fetch the latest google person
      try {
        const result = await this.repeatableCallWithBackOff(people.people.getBatchGet, 'people.getBatchGet', people, args);
        if (result && result.data && result.data.responses) {
          for (const r of result.data.responses.filter(x => x.httpStatusCode === 200)) {
            const new_person = GoogleConverter.personFromGooglePerson(r.person, this.user.locale);
            peopleUtils.mergePeople(new_person, find_set[new_person.id], true);
            source_people.push(new_person);
          }
        }
      } catch (err) {
        logging.errorFP(LOG_NAME, 'batchReload', this.user.profile, `Error batch reloading people ${f_people.map(p => p.id)}`, err);
        throw err;
      }
    }

    return source_people;
  }
}
