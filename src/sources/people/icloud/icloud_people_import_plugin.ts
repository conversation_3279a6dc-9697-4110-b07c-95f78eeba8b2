/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import fs from 'fs';
import path from 'path';

import ForaUser from '../../../session/user';

import { FileType, PersonParseMap } from '../../../types/globals';
import { Document, Person, Tag } from '../../../types/items';
import { Imports, TagType, Uid } from '../../../types/shared';

import * as funcs from '../../../utils/funcs';
import * as imports from '../../../utils/imports';
import logging from '../../../utils/logging';

import { IImportPlugin } from '../../i_import_plugin';

const LOG_NAME = 'iCloudPeopleImportPlugin';
const ROOT = '../../../';

// tslint:disable-next-line:class-name
export class iCloudPeopleImportPlugin implements IImportPlugin {
  import_map: PersonParseMap[] = null;

  constructor(public user: ForaUser = null, public self: Partial<Person> = null, public doc: Document = null, public import_type: string = null) {
    try {
      this.import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'icloud_import_map.json')).toString());
    } catch (err) {
      logging.errorFP(LOG_NAME, 'import_map', user ? user.profile : null, 'Error loading import map', err);
    }
  }
  
  async createUserGroups(): Promise<Uid[]> {
    return null;
  }

  checkImport(): boolean {
    return this.import_type === Imports.iCloud;
  }

  checkZipType(data: any[]): Imports {
    for (const zfile of data) {
      if (zfile.path && zfile.path.toLowerCase().includes('vcard')) return Imports.iCloud;
    }
  }

  async importData(): Promise<Person[]> {
    let people: Person[] = [];
    if (this.user && this.doc && this.import_type === Imports.iCloud && this.import_map) {
      const [file_type, data] =  await imports.fileType(this.doc);

      switch (file_type) {
        case FileType.VCF: 
          {
            const vcard = imports.bufferFromBody(this.doc.body);
            people = people.concat(imports.peopleFromVCF('icloud', vcard, this.import_map));
            break;
          }
        case FileType.ZIP:
          for (const zfile of data) {
            if (zfile.path && zfile.path.toLowerCase().includes('.vcf')) {
              people = people.concat(imports.peopleFromVCF('icloud', imports.bufferFromBody(zfile.data), this.import_map));
              break;
            }
          }
          break;
      }
    }

    for (const person of people) {
      funcs.saveOneTypeValue(person.tags, new Tag(TagType.import_type, 'icloud', 0, new Date()));
    }

    return people;
  }
}
