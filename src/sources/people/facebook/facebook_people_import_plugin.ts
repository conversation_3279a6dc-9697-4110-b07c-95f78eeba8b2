/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import fs from 'fs';
import path from 'path';
import util from 'util';

import ForaUser from '../../../session/user';

import { FileType, PersonParseMap } from '../../../types/globals';
import { Document, Person, Tag } from '../../../types/items';
import { Imports, TagType, Uid } from '../../../types/shared';

import * as format from '../../../utils/format';
import * as funcs from '../../../utils/funcs';
import * as imports from '../../../utils/imports';
import logging from '../../../utils/logging';

import { IImportPlugin } from '../../i_import_plugin';


const LOG_NAME = 'FacebookPeopleImportPlugin';
const ROOT = '../../../';
const DEBUG = (require('debug') as any)('fora:sources:people:facebook');

export class FacebookPeopleImportPlugin implements IImportPlugin {
  html_import_map: PersonParseMap[] = null;
  json_import_map: PersonParseMap[] = null;
  json_friends_import_map: PersonParseMap[] = null;
  json_sent_import_map: PersonParseMap[] = null;
  json_received_import_map: PersonParseMap[] = null;

  constructor(public user: ForaUser = null, public self: Partial<Person> = null, public doc: Document = null, public import_type: string = null) {
    try {
      this.html_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'facebook_html_import_map.json')).toString());
      this.json_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'facebook_json_import_map.json')).toString());
      this.json_friends_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'facebook_json_friends_import_map.json')).toString());
      this.json_sent_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'facebook_json_sent_import_map.json')).toString());
      this.json_received_import_map = JSON.parse(fs.readFileSync(path.resolve(__dirname, ROOT, 'files', 'import_maps', 'facebook_json_received_import_map.json')).toString());
    } catch (err) {
      logging.errorFP(LOG_NAME, 'import_map', user ? user.profile : null, 'Error loading import map', err);
    }
  }

  async createUserGroups(): Promise<Uid[]> {
    return null;
  }

  checkImport(): boolean {
    return this.import_type === Imports.Facebook;
  }

  checkZipType(data: any[]): Imports {
    for (const zfile of data) {
      if (zfile.path && zfile.path.toLowerCase().includes('about_you')) return Imports.Facebook;
    }
  }

  async importData(): Promise<Person[]> {
    let people: Person[] = [];
    if (this.user && this.doc && this.import_type === Imports.Facebook && this.html_import_map && this.json_import_map) {
      const [file_type, data] =  await imports.fileType(this.doc);
      logging.infoFP(LOG_NAME, 'importData', this.user.profile, `importing ${file_type} from ${this.doc.id}`);

      switch (file_type) {
        case FileType.HTML:
          if (this.doc.props && this.doc.props.originalname) {
            const file_name = this.doc.props.originalname.toLowerCase();
            if (file_name.includes('your_address_books.html') ||
                file_name.includes('friends.html') ||
                file_name.includes('received_friend_requests.html') ||
                file_name.includes('sent_friend_requests.html')
            ) {
              const html: string = format.stringFromBody(this.doc.body);
              people = people.concat(imports.peopleFromHTML('facebook_html', html, this.html_import_map));
            }
          }
          break;
        case FileType.JSON:
          if (this.doc.props && this.doc.props.originalname) {
            const file_name = this.doc.props.originalname.toLowerCase();
            if (file_name.includes('your_address_books.json')) {
              const json: string = format.stringFromBody(this.doc.body);
              people = people.concat(imports.peopleFromJSON('facebook_json', json, this.json_import_map));
            }
            if (file_name.includes('friends.json')) {
              const json: string = format.stringFromBody(this.doc.body);
              people = people.concat(imports.peopleFromJSON('facebook_friends_json', json, this.json_friends_import_map));
            }
            if (file_name.includes('received_friend_requests.json')) {
              const json: string = format.stringFromBody(this.doc.body);
              people = people.concat(imports.peopleFromJSON('facebook_received_json', json, this.json_received_import_map));
            }
            if (file_name.includes('sent_friend_requests.json')) {
              const json: string = format.stringFromBody(this.doc.body);
              people = people.concat(imports.peopleFromJSON('facebook_sent_json', json, this.json_sent_import_map));
            }
          }
          break;
        case FileType.ZIP:
          for (const zfile of data) {
            if (zfile.path) {
              const file_name = zfile.path.toLowerCase();
              if (file_name.includes('your_address_books.html') ||
                  file_name.includes('friends.html') ||
                  file_name.includes('received_friend_requests.html') ||
                  file_name.includes('sent_friend_requests.html')
              ) {
                people = people.concat(imports.peopleFromHTML('facebook_html', format.stringFromBody(zfile.data), this.html_import_map));
              } else if (file_name.includes('your_address_books.json')) {
                people = people.concat(imports.peopleFromJSON('facebook_json', format.stringFromBody(zfile.data), this.json_import_map));
              } else if (file_name.includes('friends.json')) {
                people = people.concat(imports.peopleFromJSON('facebook_friends_json', format.stringFromBody(zfile.data), this.json_friends_import_map));
              } else if (file_name.includes('received_friends_requests.json')) {
                people = people.concat(imports.peopleFromJSON('facebook_received_json', format.stringFromBody(zfile.data), this.json_received_import_map));
              } else if (file_name.includes('sent_friend_requests.json')) {
                people = people.concat(imports.peopleFromJSON('facebook_sent_json', format.stringFromBody(zfile.data), this.json_sent_import_map));
              }
            }
          }
          break;
      }
    }

    for (const person of people) {
      funcs.saveOneTypeValue(person.tags, new Tag(TagType.import_type, 'facebook', 0, new Date()));
    }

    logging.infoFP(LOG_NAME, 'importData', this.user.profile, `imported ${people.length} people from ${this.doc.id}`);
    if (logging.isDebug(this.user.profile)) DEBUG(util.inspect(people));

    return people;
  }
}
