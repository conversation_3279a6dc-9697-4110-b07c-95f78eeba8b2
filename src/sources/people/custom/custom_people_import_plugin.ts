/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import ForaUser from '../../../session/user';

import * as format from '../../../utils/format';
import * as funcs from '../../../utils/funcs';
import * as imports from '../../../utils/imports';
import logging from '../../../utils/logging';

import { FileType, ImportMaps } from '../../../types/globals';
import { Document, Person, Tag } from '../../../types/items';
import { Imports, TagType, Uid } from '../../../types/shared';
import { IImportPlugin } from '../../i_import_plugin';

const LOG_NAME = 'CustomPeopleImportPlugin';

export class CustomPeopleImportPlugin implements IImportPlugin {
  constructor(public user: ForaUser = null, public self: Partial<Person> = null, public doc: Document = null, public import_type: string = null) {
  }

  async getCustomMap(): Promise<[ImportMaps, Uid]> {
    for (const group of Object.values(this.user.loaded_groups)) {
      if (group.import_maps) {
        if (group.import_maps[this.import_type]) return [group.import_maps[this.import_type], group.id]
      }
    }

    return [null, null];
  }

  async createUserGroups(): Promise<Uid[]> {
    const [custom_map, group_id] = await this.getCustomMap();
    return custom_map && group_id && custom_map.users ? [group_id] : null;
  }

  checkImport(): boolean {
    return !Object.values(Imports).includes(this.import_type as Imports);
  }

  checkZipType(_data: any[]): Imports {
    return null;
  }

  async importData(): Promise<Person[]> {
    let people: Person[] = [];

    if (this.user && this.doc && !Object.values(Imports).includes(this.import_type as Imports)) {
      await this.user.loadGroupsFromDatastore();

      const [custom_map, group_id] = await this.getCustomMap();

      if (!custom_map) return people;


      let [file_type] = await imports.fileType(this.doc, custom_map.ext);

      if (custom_map.ext && custom_map.ext !== file_type) {
        // handle types that are generically zip files
        if (file_type === FileType.ZIP) file_type = custom_map.ext;
        else {
          // TODO: error
        }
      }

      switch (file_type) {
        case FileType.HTML:
          people = people.concat(imports.peopleFromHTML(custom_map.name, format.stringFromBody(this.doc.body), custom_map.mapping, custom_map.lookup));
          if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'importData', this.user.profile, `imported ${people.length} from html`);
          break;
        case FileType.JSON:
          people = people.concat(imports.peopleFromJSON(custom_map.name, format.stringFromBody(this.doc.body), custom_map.mapping, custom_map.lookup));
          if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'importData', this.user.profile, `imported ${people.length} from json`);
          break;
        case FileType.CSV:
          people = people.concat(imports.peopleFromCSV(custom_map.name, format.stringFromBody(this.doc.body), custom_map.mapping, custom_map.lookup));
          if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'importData', this.user.profile, `imported ${people.length} from csv`);
          break;
        case FileType.VCF:
          people = people.concat(imports.peopleFromVCF(custom_map.name, imports.bufferFromBody(this.doc.body), custom_map.mapping, custom_map.lookup));
          if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'importData', this.user.profile, `imported ${people.length} from vcf`);
          break;
        case FileType.XLSX:
          people = people.concat(imports.peopleFromXLSX(custom_map.name, imports.bufferFromBody(this.doc.body), custom_map.mapping, custom_map.lookup));
          if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'importData', this.user.profile, `imported ${people.length} from xlsx`);
          break;
        default:
          // TODO: error
          logging.warnFP(LOG_NAME, 'importData', this.user.profile, `imported ${people.length} from ${file_type}`);
          break;
      }

      for (const person of people) {
        funcs.saveOneTypeValue(person.tags, new Tag(TagType.import_type, this.import_type, 0, new Date()));
        funcs.saveOneTypeValue(person.tags, new Tag(TagType.import_group, group_id, 0, new Date()));
      }
    }

    return people;
  }
}
