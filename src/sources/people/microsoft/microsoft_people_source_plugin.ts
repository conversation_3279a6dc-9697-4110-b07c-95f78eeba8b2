/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Contact } from '@microsoft/microsoft-graph-types';
import { AuthProvider } from '../../../auth/auth_provider';
import ForaUser from '../../../session/user';
import { IEntity, Person } from '../../../types/items';
import { AuthLevel, AuthProviders, EntityType, Uid } from '../../../types/shared';
import logging from '../../../utils/logging';
import parsers from '../../../utils/parsers';
import peopleUtils from '../../../utils/people';
import { AbstractMicrosoftSourcePlugin } from '../../a_microsoft_source_plugin';
import MicrosoftGraphContactHelper from '../../helpers/microsoft/microsoft_graph_contact_helper';
import { MicrosoftGraphConverter } from '../../helpers/microsoft/microsoft_graph_conversions';
import { SourceKeys } from '../../index';
import { IPeopleSourcePlugin } from '../i_people_source_plugin';
import { MicrosoftPeopleSourceMetadata } from './microsoft_people_source_metadata';

const LOG_NAME = 'data.sources.MicrosoftPeopleSourcePlugin';

/**
 * Microsoft People Source Plugin
 *
 *  - https://docs.microsoft.com/en-us/graph/api/resources/contact?view=graph-rest-1.0
 *  - https://docs.microsoft.com/en-us/graph/api/resources/contact?view=graph-rest-beta
 *  - PAGING - https://docs.microsoft.com/en-us/graph/api/contact-delta?view=graph-rest-1.0
 */
export class MicrosoftPeopleSourcePlugin extends AbstractMicrosoftSourcePlugin<Person, MicrosoftPeopleSourceMetadata> implements IPeopleSourcePlugin {
  public static providers = [AuthProviders.Microsoft, AuthProviders.Msal];
  private remove_people = [];

  constructor(user: ForaUser, account: Uid, user_people: {[key: string]: Person} = {}, user_emails: {[key: string]: Uid[]} = {}) {
    super(LOG_NAME, SourceKeys.MicrosoftPeople, EntityType.Person, user, account, user_people, user_emails, MicrosoftPeopleSourceMetadata, 
      user.hasAccount(AuthProviders.Msal, account) ? AuthProviders.Msal : AuthProviders.Microsoft,
      user.hasAccount(AuthProviders.Msal, account) ? AuthProvider.msal.getScopesForPeople() : AuthProvider.microsoft.getScopesForPeople(),
      false, false);
  }

  /** @inheritDoc */
  async create(f_person: Person): Promise<[Person, IEntity[]]> {
    const deletes: Person[] = [];

    if (!this.user.isAuthenticated(AuthLevel.Organizer)) {
      if (f_person.id && f_person.id.startsWith('people/m')) {
        deletes.push({ id: f_person.id, type: EntityType.Person } as Person);
        if (f_person.comms) f_person.comms = f_person.comms.filter(c => c !== f_person.id);
        else f_person.comms = [];
        f_person.tempId();
      }
      return [f_person, deletes];
    }

    //
    // Existing Person that exists within Microsoft Graph
    const client = await this.getGraphClient();
    let ms_contact_returned: Contact = null;

    if (f_person.id && f_person.id.startsWith('people/m')) {
      // Get the existing Contact
      try {
        const ms_contact: Contact = await MicrosoftGraphContactHelper.contactGet(this.user, f_person.id.slice('people/m'.length), this.account, client);

        // TODO(current) - Make this smarter, so we aren't updating all the fields that didn't change
        if (MicrosoftGraphConverter.updateContactFromPerson(ms_contact, f_person)) {
          ms_contact_returned = await MicrosoftGraphContactHelper.contactUpdate(this.user, this.account, ms_contact, client);
        } else {
          return [f_person, []];
        }
      } catch (err) {
        if (err.statusCode === 404 || err.statusCode === 400) {
          // Existing Microsoft person was deleted at Microsoft, remove from data store and clear id to create a new one
          deletes.push({ id: f_person.id, type: EntityType.Person } as Person);
          f_person.id = null;
        } else {
          logging.errorFP(this.log_name, 'create', this.user.profile, `Error getting/updating existing contact ${f_person.id}`, err);
          throw err;
        }
      }
    } else if(f_person.id !== null && f_person.id !== undefined) {
      logging.warnFP(LOG_NAME, 'create', this.user.profile, `Not saving person ${f_person.id} to Microsoft`); 
      return [f_person, []];
    }

    //
    // Creation (either new or delete case above)
    if (!ms_contact_returned) {
      try {
        const ms_contact: Contact = MicrosoftGraphConverter.convertPersonToContact(f_person);
        ms_contact_returned = await MicrosoftGraphContactHelper.contactCreate(this.user, this.account, ms_contact, client);
      } catch (err) {
        logging.errorFP(this.log_name, 'create', this.user.profile, `Error creating contact ${f_person.id}`, err);
        return [f_person, deletes];
      }
    }

    if (ms_contact_returned) {
      const f_person_out = MicrosoftGraphConverter.convertPersonFromContact(ms_contact_returned, this.user.tokens);
      if (!f_person_out.tags) f_person_out.tags = [];
      peopleUtils.mergePeople(f_person_out, f_person, true);
      f_person_out.self = f_person.self;
      return [f_person_out, deletes];
    } 

    // default to 
    logging.warnFP(this.log_name, 'create', this.user.profile, `Contact not updated or created at Microsoft ${f_person.id}`);
    return [f_person, []];
  }

  async delete(f_person: Partial<Person>): Promise<void> {
    const client = await this.getGraphClient();

    if (f_person && f_person.id.startsWith('people/m')) {
      try {
        await MicrosoftGraphContactHelper.contactDelete(this.user, f_person.id.slice('people/m'.length), this.account, client);
      } catch (err) {
        if (err.code === 404) {
          // Existing  Microsoft person was deleted as Microsoft, nothing to do
        } else {
          logging.errorFP(this.log_name, 'delete', this.user.profile, `Error deleting contacts ${f_person.id}`, err);
        }
      }
    }
  }

  /** @inheritDoc */
  async providerRead(is_history: boolean): Promise<[IEntity[], IEntity[]]> {
    try {
      const ms_contacts = await MicrosoftGraphContactHelper.contactsPage(this.user, this.account, this.metaData(), is_history);
      if (ms_contacts && ms_contacts.length) {
        if (logging.isDebug(this.user.profile)) logging.debugFP(this.log_name, 'providerRead', this.user.profile, `${ms_contacts.length} connection changes returned`);
        const [new_updates, new_deletes] = await this.peopleFromContacts(ms_contacts);
        if (new_updates) {
          for (const p of new_updates) this.addPerson(p);
        }
        return [new_updates, this.remove_people.filter(p => !this.new_people_ids.includes(p.id))];
      } else {
        logging.infoFP(this.log_name, 'providerRead', this.user.profile, 'No people changes returned');
        return [[], this.remove_people.filter(p => !this.new_people_ids.includes(p.id))];
      }
    } catch (err) {
      if (err.code === 'SyncStateNotFound' && (this.metaData().nextSyncToken || this.metaData().nextPageToken)) {
        logging.warnFP(this.log_name, 'providerRead', this.user.profile, 'Error syncing people, starting over', err);
        this.metaData().nextSyncToken = null;
        this.metaData().nextPageToken = null;
        await this.reset();
        return this.providerRead(is_history);
      }
      logging.errorFP(this.log_name, 'providerRead', this.user.profile, 'Error loading people', err);
      throw err;
    }
  }

  /**
   * Convert an array of MS Contacts to a Fora Person
   *
   * @param contacts - Contacts to convert
   * @private
   * @async
   */
  private async peopleFromContacts(contacts: Contact[]): Promise<[Person[], Person[]]> {
    const persons: Person[] = [];
    for (const contact of contacts) persons.push(MicrosoftGraphConverter.convertPersonFromContact(contact, this.user.tokens));
    return [persons, []];
  }

  public reset() {
    super.reset();
    // remove all people/m
    const remove_comms = [];
    for (const comm in this.user_emails) {
      if (this.user_emails[comm]) {
        const remove = this.user_emails[comm].filter(c => c.startsWith('people/m'));
        if (remove.length) {
          for (const id in remove) {
            this.remove_people.push(new Person(this.user_people[id]));
            delete this.user_people[id];
          }
          if (remove.length === this.user_emails[comm].length) remove_comms.push(comm);
        }
      }
    }

    const remove_ids = [];
    for (const id in this.user_people) {
      if (id.startsWith('people/m')) {
        const person = this.user_people[id];
         const email = person.comms && person.comms.length ? parsers.findEmail(person.comms) : null;
         if (!email || !email.length) {
           remove_ids.push(id);
           this.remove_people.push(new Person(person));
         }
      }
    }

    for (const id of remove_ids) delete this.user_people[id];
    for (const comm of remove_comms) delete this.user_emails[comm];
  }

  async reload(f_person: Person): Promise<Person> {
    // Existing Person that exists within Microsoft Graph
    const client = await this.getGraphClient();

    if (f_person.id && f_person.id.startsWith('people/m')) {
      // Get the existing Contact
      try {
        const ms_contact: Contact = await MicrosoftGraphContactHelper.contactGet(this.user, this.account, f_person.id.slice('people/m'.length), client);
        if (ms_contact) {
          const f_person_out = MicrosoftGraphConverter.convertPersonFromContact(ms_contact, this.user.tokens);
          if (!f_person_out.tags) f_person_out.tags = [];
          peopleUtils.mergePeople(f_person_out, f_person, true);
          f_person_out.self = f_person.self;
          return f_person_out;
        }
      } catch (err) {
        if (err.statusCode === 404 || err.statusCode === 400) {
          // Existing Microsoft person was deleted at Microsoft, remove from data store and clear id to create a new one
          return null;
        } else {
          logging.errorFP(this.log_name, 'create', this.user.profile, `Error reloading existing contact ${f_person.id}`, err);
          throw err;
        }
      }
    }

    return null;
  }

  async batchReload(f_people: Partial<Person>[]): Promise<Person[]> {
    const client = await this.getGraphClient();

    const source_people: Person[] = [];
    const find_people = f_people ? f_people.filter(p => p.id && p.id.startsWith('people/m')) : [];
    if (find_people.length) {
      const find_set: {[key:string]: Partial<Person>} = {};
      for (const p of find_people) find_set[p.id] = p;
      const ms_ids = Object.keys(find_set).map(id => id.slice('people/m'.length));

      let ms_contacts: Contact[];
      try {
        ms_contacts = await MicrosoftGraphContactHelper.contactsGet(this.user, this.account, ms_ids, client);
      } catch(err) {
        logging.errorFP(this.log_name, 'batchReload', this.user.profile, `Error batch reloading contact ${f_people.map(p => p.id)}`, err);
        throw err;
      }

      if (ms_contacts) {
        for(const ms_contact of ms_contacts) {
          const f_person_out = MicrosoftGraphConverter.convertPersonFromContact(ms_contact, this.user.tokens);
          if (!f_person_out.tags) f_person_out.tags = [];
          const f_person = find_set[f_person_out.id];
          peopleUtils.mergePeople(f_person_out, f_person, true);
          f_person_out.self = f_person.self;
          source_people.push(f_person_out);
        } 
      }
    }

    return source_people;
  }
}
