import Dialog, { Topics } from '../session/dialog';

import { Action, PersonParseMap, TemplateType, Topic } from './globals';
import { Group } from './group';
import { Candidate, Contract, Document, Event, GlobalType, IEntity, ImportType, Location, Message, Person, Project, SavedFilter, Task } from './items';
import { AuthContext, AuthLevel, AuthProviders, BasicInfo, EntityType, Info, PersonInfo, ProjectCandidateState, ProjectDraftSend, Relation, Shortcut, TaskInfo, TimeVal, Uid } from './shared';

export interface Plugin {
  description: string;
  examples: string[];

  // should probably not be bound here.
  findPersonPrompt?: any;
  addPerson?: any;
  isActive: (d: Dialog) => boolean;
  keywordMatch: (actions: Action[], message: string, raw_message: string, group_host: Group) => boolean;
  name: string;
  requiresAuth: boolean;
  reserved: string[];
  runAction: (dialog: Dialog) => void;
  setAction: (dialog: Dialog, message: string, raw_message: string) => boolean;
  setPrompt: (d: Dialog) => void;
  setupContract?: any;
  shortcuts?: (authenticated?: boolean) => Shortcut;
  reset_context?: string[];
}

export class AddReminderPluginState {
  people: Partial<Person>[];
  act_people: Partial<PersonInfo>[];
  act_names: string[];
  title: string;
  date: TimeVal;
  saving?: boolean;
  saved_task?: Task;
  saved_message?: Message;
  prompted?: boolean;
  meeting?: boolean;
}

export class AskIntroPluginState {
  contact: Person = null;
  context: string = null;
  draft: Message = null;
  drafting = false;
  id: Uid = null;
  loading = false;
  names: any[] = null;
  target: string = null;
}

export class ConnectPluginState {
  candidates: Partial<Person>[] = null
  global_search = false;
  global_search_complete = false;
  has_email = false;
  ignore_draft = false;
  project: Partial<Project> | undefined = undefined;
  loading = false;
  matching = false;
  matched = false;
  matches: Partial<Person>[] = null;
  message: string = null;
  notes: string[] = [];
  person: Partial<Person> = null;
  prompted = false;
  first_prompt = false;
  require_email?: boolean;
  saved_note: Task = null;
  saved_person: Partial<Person> = null;
  done_saving = false;
  saving = false;
  searching = false;
}

export class ContractPluginState {
  contract_id: Uid = undefined; //contract to load
  doc: Contract = undefined; // contract we're working on
  was_signed = false; // track what we have in cache for contract signed
  was_declined = false; // track what we have in cache for contract signed
  edit = false; // whether to open for the user
  loading = false; // loading contract
  saving = false; // saving contract
  saved = false; // saved contract
  notifying = false;
  notified = false; 
  signing = false; 
  cmd: string = undefined; // contract command
  next_topic; Topic = undefined; // topic to return to

  // when fully signed
  project_contractor: Partial<Person> = undefined;
  project_client: Partial<Person> = undefined;
  project_ask_deposit: boolean = undefined;
  project_amount: number = undefined;
  project_service_fee: number = undefined;
}

export class ContractorPluginState {
  skills?: string[] = undefined;
  skill_prompt?: number;
  profile?: Partial<Person> = null;
  saving?: boolean = false;
}

export class CreatePluginState {
  saved?: IEntity;
  create_type: EntityType = undefined;
  duplicate_type?: EntityType = undefined;
  empty_type?: EntityType | GlobalType = undefined;
  filters?: SavedFilter[];
  skills?: string[];
  label?: string;
  pending?: string[];
}

export class DashboardState {
  person: Person = null;
  when: Date = null;
  ids: Uid[] = null;
  loading = false;
  loaded=  false;
  related: {[key: string]: Person} = null;
  info: PersonInfo = null;
  timeout: Date = null;
  connections: Uid[] = null;
}

export class DoPluginState {
  answers?: string[] = null;
  context?: string = null;
  // done? = false;
  draft_link?: string = null;
  drafting? = false;
  ids?: Uid[] = null;
  info?: Info = null;
  loading? = false;
  message?: string = null;
  // processing? = false;
  prompt?: BasicInfo = null;
  recipient?: Partial<Person>[] = null;
  requires_auth?: AuthLevel = undefined;
  return_to?: Topic = Topics.DEFAULT;
  send? = false;
  sticky? = false;
  clear? = false;
  hide? = false;
  close? = false;
  update: IEntity = null;
  act_on: EntityType;
}

export class ImportsPluginState {
  custom_maps: {
    [key: string]: {
      ext: string;
      mapping: PersonParseMap;
      name: string;
    };
  };
  data?: any;
  docs?: Document[];
  file_id?: string;
  import_type?: ImportType;
  loading_maps: boolean;
  account: string;
}

export class EmailLoginPluginState {
  sent?: boolean;
  failed?: boolean;
  seed?: string;
  factor?: number;
}

export class FindPluginState {
  urls?: string[];
  discover_people?: Partial<Person>[];
  learned_people?: Person[];
  match_people?: Partial<Person>[];
}

export class InitPluginState {
  auth_context?: AuthContext = null;
  completed? = false;
  processing? = false;
  provider?: AuthProviders = null;
  existing?: AuthProviders = null;
  account?: Uid = null;
  topic?: Topic = null;
  profile?: Partial<Person> = null;
  vanity?: string = null;
  redirect?: string = null;
  card?: boolean = false;
  size?: {height: number, width: number} = null;
}

export class LookupWhenPluginState {
  act_people?: string[] = null;
  entityType?: EntityType[] = null;
  time?: Relation[] = null;
}

export class MakeIntroPluginState {
  context?: string[] = null;
  draft?: Message = null;
  ids?: Uid[] = [];
  loading? = false;
  names?: string[] = null;
  resolve?: Partial<PersonInfo>[][] = null;
  people?: Partial<Person>[] = null;
  connection?: boolean = false;
  connecting?: boolean = false;
  requested?: boolean = false;
  project_id?: Uid = null;
  intro_id?: Uid = null;
}

export class PaymentPluginState {
  stripe_countries?: string[] = null;
}

export class PeopleLoadPluginState {
  fill = false;
  ids?: Uid[] = null;
  people?: Person[] = null;
  timeout: Date = null;
}

export enum PersonInfoSearchType {
  Name = 'name',
  Network = 'network',
  Event = 'event',
  Skill = 'skill',
}

export class PersonInfoPluginState {
  search_term: string;
  found_people: Person[];
  people_found = false;
  person_search = false;
  name_search = false;
  name_ids: { [key: string]: Partial<Person> } = null;
  ids: Uid[] = null;
  all_people?: Partial<Person>[] = null;
  refine_people?: Partial<Person>[] = null;
  skill_search_started = false;
  hide? = false;
  filters?: SavedFilter[];

  search_type: PersonInfoSearchType;
}

export class ProjectPluginState {
  public added = false;
  public close = false;
  public contractor: Partial<Person> = undefined;
  public new_contract: Partial<Contract> = undefined;
  public done_searching = false;
  // for sending invites
  public draft_note: string = null;
  public draft_send: ProjectDraftSend = null;
  public draft_url: string = null;
  public drafting = false;
  public auto_send = false;
  // setup
  public durationDefaulted = false;
  public durationDefaultedPrompted = false;
  public feeDefaulted = false;
  public feeDefaultedPrompted = false;
  public load_ids: Uid[] = [];
  public load_emails: string[] = [];
  public load_referrers: Uid[] = [];
  public searched_client: string = null;
  public contracting = false;
  public related_entity: {id: Uid, type: EntityType} = undefined;
  public related_info: Info;
  // loading shared project
  public load = false; // load from url
  public loading = false;
  public loaded = false;
  // finding candidates
  public loading_people = false;
  public saved_people = false;
  public select = false;
  // contracting nda review
  public nda = false;
  public open = false;
  public person_name: string[] = [];
  public people: Partial<Candidate>[] = [];
  public project: Project; // the project we're editing
  // saving project updates
  public updated = false;
  public save = false;
  public saved = false;
  public saved_topic: Topic = null;
  public saving = false;
  public search_help: Topic = null;
  public search_prompt = false;
  public searching = false;
  public send_nda = false;
  public sending = false;
  public sent = false;
  public escrow_prompt: {
    info: Info[];
    reply: BasicInfo[];
   } = null;
  // updating candidates
  public state: ProjectCandidateState;
  public answer: string = null;
  public timeout: Date = null;

  public payment_error: any = null;
  public logging_in = false;
}

export class ReviewPluginState {
  public entity_type: EntityType = null;
  public id: Uid = null;
  public self = null;
}

export class SettingsPluginState {
  calendar: { type: EntityType; value: string } = null;
  enable = false;
  saved = true;
  saving = false;
  type: EntityType = null;
}

export class SharePluginState {
  url: string;
  type: EntityType;
  people?: Partial<Person>[];
  message?: Message;
}

export class StripePluginState {
  countries?: string[] = null;
  category: string = undefined;
  company: string = undefined;
  context: string = undefined;
  dashboard_link: string = undefined;
  dashboard_loading = undefined;
  desc: string = undefined;
  detail_step = 0;
  email: string = undefined;
  name: string = undefined;
  phone: string = undefined;
  url: string = undefined;
  deleting = false;
  deleted = false;
  project: Project = undefined;
  locale: string = undefined;
  country_confirmed = false;
  account_id: string = undefined;
}

export class WisePluginState {
  location?: Partial<Location> = undefined;
  currency?: string = undefined;
  profile?: number = undefined;
  accountHolderName?: string = undefined;
  account_type?: string = undefined;
  details?: any = undefined;
  skipped: string[] = undefined;
  quoteId?: any = undefined;
  requirements?: any[] = undefined;
  loading?: boolean = undefined;
  field?: string = undefined;
  connecting?: boolean = undefined;
  connected?: boolean = undefined;
  error?: string = undefined;
  deleted?: boolean = undefined;
  deleting?: boolean = undefined;
}

export class TakeNotesPluginState implements TaskInfo {
  added? = false;
  created: Date = new Date();
  completed = false;
  done? = false;
  due?: Date = null;
  title: string = null;
  edit? = false;
  events?: Event[] = [];
  id: string = null;
  notes: string = null;
  people?: Partial<PersonInfo>[] = [];
  saving? = false;
  project?: Uid = null;
  type: EntityType.Task = EntityType.Task;
  updated? = false;
}

export class WhatsNextPluginState {
  event_count: number;
  event_people_ids: Uid[];
  event_time: Date;
  full_day: boolean;
  next: Partial<IEntity>[];
  next_types: (EntityType|GlobalType)[];
  people_ids: Uid[];
  people?: Partial<Person>[];
  quip: string;
  total_time: number; // time in ms
  filters?: SavedFilter[];
}

export class WelcomePluginState {
  template?: TemplateType; 
  next_step?: TemplateType;
  text?: string;
  // subject?: string;
}

export class Context {
  ask_intro?: AskIntroPluginState;
  create?: CreatePluginState;
  connect?: ConnectPluginState;
  contract?: ContractPluginState;
  contractor?: ContractorPluginState;
  dashboard?: DashboardState;
  do?: DoPluginState;
  error?: any;
  email_login?: EmailLoginPluginState;
  feedback?: any;
  find?: FindPluginState;
  find_people?: any;
  find_skills?: any;
  imports?: ImportsPluginState;
  init?: InitPluginState;
  load_people?: any;
  load_person?: any;
  make_intro?: MakeIntroPluginState;
  payment?: PaymentPluginState;
  people_load?: PeopleLoadPluginState;
  person_info?: PersonInfoPluginState;
  project?: ProjectPluginState;
  record_note?: TakeNotesPluginState;
  reminder?: AddReminderPluginState;
  review?: ReviewPluginState;
  self_intro?: { draft: Message };
  settings?: SettingsPluginState;
  share?: SharePluginState;
  stripe?: StripePluginState;
  wise?: WisePluginState;
  whats_next?: WhatsNextPluginState;
  when?: LookupWhenPluginState;
  welcome?: WelcomePluginState;
}
