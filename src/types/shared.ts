import _ from 'lodash';

export type Uid = string;

export enum EntityType {
  Person = 'com.askfora.Person',
  Ask = 'com.askfora.Ask',
  Note = 'com.askfora.Note',
  Task = 'com.askfora.Task',
  User = 'com.askfora.User',
  Event = 'com.askfora.Event',
  Message = 'com.askfora.Message',
  Location = 'com.askfora.Location',
  Contract = 'com.askfora.Contract',
  Project = 'com.askfora.Project',
  None = 'com.askfora.None',
  Organization = 'com.askfora.Organization',
  Calendar = 'com.askfora.Calendar',
  UnresolvedPerson = 'com.askfora.UnresolvedPerson',
  UnresolvedOrg = 'com.askfora.UnresolvedOrg',
  Document = 'com.askfora.Document',
  Payment = 'com.askfora.Payment',
  Recommendation = 'com.askfora.Recommendation',
  Settings = 'com.askfora.Settings',
  Expert = 'com.askfora.Expert',
  Filter = 'com.askfora.Filter',
  SavedFilter = 'com.askfora.SavedFilter',
  Analysis = 'com.askfora.Analysis',
  Goal = 'com.askfora.Goal',
  Skillset = 'com.askfora.Skillset',
  Plan = 'com.askfora.Plan',
  Tutorial = 'com.askfora.Tutorial',

  ItemCache = 'com.askfora.ItemCache', // cache of all item ids, names, update

  Develop = 'com.askfora.Develop',
  AnalysisPlan = 'com.askfora.AnalysisPlan',
}

export enum TagType {
  import_group = 'import_group',
  import_type = 'import_type',
  import_id = 'import_id',
  PDL_ID = 'PDL_ID',
  range = 'range',
  skill = 'skill',
  organization = 'organization',
  department = 'department',
  jobTitle = 'jobTitle',
  jobDescription = 'jobDescription',
  industry = 'industry',
  domain = 'domain',
  occupation = 'occupation',
  job = 'job',
  default = 'default',
  work = 'work',
  school = 'school',
  ticker = 'ticker',
  title = 'title',
  idea = 'idea',
  symbol = 'symbol',
  meta = 'meta',
  event = 'event',
  location = 'location',
  label = 'label',
  office = 'office',
  degree = 'degree',
  person = 'person',

  // for selecting, cacluated
  none = 'none',
  tenure = 'tenure',
  experience = 'experience',
  education = 'education',
  state = 'state',
  region = 'region',
  country = 'country',

  // for compatibility
  jobtitle = 'jobtitle',
  jobdescription = 'jobdescription',

  // for testing
  group = 'group',
  skill_x = 'skill_x',
}

export function computedTag(tag: TagType) { 
  return [TagType.experience, TagType.tenure].includes(tag);
}

// let use pick to seach these
export const FilterTags = [
  // { key: 'name', text: 'Name',  value: 'name' },
  { key: 'organization', text: 'Organization', value: 'organization' },
  { key: 'department', text: 'Department', value: 'department' },
  { key: 'jobTitle', text: 'Job Title', value: 'jobTitle' },
  { key: 'jobDescription', text: 'Job Desc', value: 'jobDescription' },
  { key: 'industry', text: 'Industry', value: 'industry' },
  // { key: 'domain', text: 'Domain', value: 'domain' },
  { key: 'occupation', text: 'Occupation', value: 'occupation' },
  // { key: 'job', text: 'Job', value: 'job' },
  // { key: 'default', text: 'Default', value: 'default' },
  // { key: 'work', text: 'Work', value: 'work' },
  { key: 'school', text: 'School', value: 'school' },
  { key: 'education', text: 'Education', value: 'education' },
  //{ key: 'title', text: 'Title', value: 'title' },
  // { key: 'idea', text: 'Keyword', value: 'idea' },
  // { key: 'meta', text: 'Bio', value: 'meta' },
  // { key: 'event', text: 'Last Met', value: 'event' },
  { key: 'location', text: 'Location', value: 'location' },
  // { key: 'label', text: 'List', value: 'label' },
  { key: 'experience', text: 'Yrs Experience', value: 'experience' },
  { key: 'tenure', text: 'Tenure',  value: 'tenure' },
  { key: 'keyword', text: 'Keyword',  value: 'keyword' },
  { key: 'event', text: 'Last Met',  value: 'event' },
  { key: 'person', text: 'Name', value: 'person' },
]

// always search these
export const GlobalFilterTags = [
  TagType.domain, TagType.job, TagType.work, TagType.title, TagType.default, TagType.skill, TagType.jobtitle, TagType.jobdescription
]

export const EqualityRelOptions = [
  { key: '=', text: 'is', value: '=' }, 
  { key: '~', text: 'like', value: '~' }, 
  { key: '<>', text: 'is not', value: '<>' }, 
]

export const InequalityRelOptions = [
  { key: '<', text: '<', value: '<' },
  { key: '>', text: '>', value: '>' }, 
]

export const RelOptions = [
  { key: '><', text: 'in', value: '><' }, 
  { key: '^', text: 'first', value: '^' }, 
  { key: '$', text: 'last', value: '$' }, 
  { key: '!', text: 'upcoming', value: '!' }, 
...EqualityRelOptions,
...InequalityRelOptions,
]

export function TagRelOptions(tag: TagType | EntityType): {key:string, text: string, value: string}[] {
  switch(tag) {
    case TagType.organization:
    case TagType.department:
    case TagType.jobTitle:
    case TagType.jobtitle:
    case TagType.jobDescription:
    case TagType.jobdescription:
    case TagType.industry:
    case TagType.occupation:
    case TagType.location:
    case TagType.school:
      return EqualityRelOptions;
    case TagType.experience:
    case TagType.tenure:
    case TagType.education:
      return [...EqualityRelOptions, ...InequalityRelOptions];
  }

  return RelOptions;
}

export const EducationLevel = [
  'school',
  'ged',
  'freshman',
  'sophomore',
  'junior',
  'high',
  'senior',
  'second',
  'secondary',
  'secondary institution',
  'primary secondary education',
  'post-secondary institution',
  'higher education',
  'trade',
  'associate',
  'bachelor',
  'bachelor of law',
  'mba',
  'master',
  'masters',
  'masters of business',
  'masters of business administration',
  'masters of business and administration',
  'jd',
  'doc',
  'docter',
  'doctorate',
  'doctor of jurisprudence',
  'phd',
  'post',
];

export function TagOptions(tag: TagType | EntityType):  {key:string, text: string, value: string}[] {
  switch(tag) {
    case TagType.education:
      return [
        { key: 'ged', text: 'GED', value: 'ged' },
        { key: 'high school', text: 'High School', value: 'high school' },
        { key: 'secondary school', text: 'Secondary School', value: 'secondary school' },
        { key: 'trade school', text: 'Trade School', value: 'trade school' },
        { key: 'associates', text: 'Associates', value: 'associates' },
        { key: 'bachelors', text: 'Bachelors', value: 'bachelors' },
        { key: 'masters', text: 'Masters', value: 'masters' },
        { key: 'doctorate', text: 'Doctorate', value: 'doctorate' },
        { key: 'post-doc', text: 'Post Doc', value: 'post-doc' },
      ];
    case TagType.experience:
    case TagType.tenure:
      return Array.from({length: 50}, (_, i) => i + 1).map(i => { return { key: `${i}`, text: `${i}`, value: `${i}`} });
  }
}

export const JobTags = [
  TagType.job,
  TagType.occupation,
  TagType.organization,
  TagType.department,
  TagType.jobTitle,
  TagType.jobDescription,
  TagType.work,
  TagType.school,
  TagType.domain,
  TagType.title,
  TagType.range,
  TagType.symbol,
];

export const OrgTags = [
  TagType.organization,
  TagType.occupation,
  TagType.department,
  TagType.jobTitle,
  TagType.jobDescription,
  TagType.ticker,
];

export const RoleTags = [
  TagType.department,
  TagType.jobTitle,
  TagType.jobDescription,
  TagType.domain,
  TagType.occupation,
  TagType.title,
];

export const IndustryTags = [
  TagType.department,
  TagType.jobTitle,
  TagType.jobDescription,
  TagType.domain,
  TagType.occupation,
  TagType.industry,
];

export enum ProjectRate {
  hourly = 'hourly',
  daily = 'daily',
  fixed = 'fixed',
  sourcing = 'sourcing',
}

export enum ProjectSourcingType {
  full = 'full',
  part = 'part',
  contract = 'contract',
}

export enum ProjectDraftSend {
  draft = 'draft',
  send = 'send',
}

/**
 * These tell the client explicitly which action to take - much like a redirect would do on web.
 */
export enum Operation {
  Authenticate = 'Authenticate',
  SetupPayments = 'SetupPayments',
}

export type PersonId = string;

export interface StripeConfig {
  options?: any;
  account?: string;
  fee: number;
  key: string;
  state?: any;
}

export interface AnalyticsConfig {
  trackid: string;
  user_id: string;
  date: Date;
}

export interface VanityCheck {
  status: string;
}

export interface SkillStat {
  value: string | null;
  num: number;
  weight: number;
}

export interface Stats {
  first_count: number;  //first connections
  second_count: number;  //second connections
  group_count: number;  //group connections
  fora_count: number; // fora users minus user_coutn
  global_count: number; // all contacts
  user_count: number; //queried users (self, second, and group)
}

export enum Widget {
  Configure = 'configure',
  Share = 'share',
  Meet = 'meet',
  Mail = 'mail',
  Explore = 'explore',
  Request = 'request',
  Categorize = 'categorize',
  Analyze = 'analyze'
}

export const BASIC_WIDGETS = [];
export const PRO_WIDGETS = [Widget.Share, Widget.Meet, Widget.Mail, Widget.Explore, Widget.Request, Widget.Categorize, Widget.Analyze];

export const SETTING_ENABLED = 'enabled';
export const CALENDAR_SELECTED = 'selected';
export const CALENDAR_SYNC = 'sync';
export const PROJECT_ARCHIVED = 'archived';
export const PROJECT_OPEN = 'open';
// export const PROJECT_AS_CLIENT = 'as_client';

export enum ViewProjects {
  Manage = 'manage',
  Contract = 'contract',
  Admin = 'admin',
}

export enum ToggleSetting {
  Calendars = 'calendars',
  Tasks = 'tasks',
  Notes = 'notes',
  Info = 'info',
  Messages = 'messages',
  Projects = 'projects',
  People = 'people',
  Accounts = 'accounts',
  Archives = 'archives',
  Profile = 'profile',
  Notify = 'notify',
  Contracts = 'contracts',
}

export enum NotificationType {
  Project_Invite = 'project.invite',
  Project_Expert = 'project.expert',
  Project_Proposal = 'project.proposal',
  Project_Found = 'project.found',
  Project_Accepted = 'project.accepted',
  Project_Answer = 'project.answer',
  Project_Recommended = 'project.recommended',
  Project_Declined = 'project.declined',
  Project_Selected = 'project.selected',
  Project_SetupPayment = 'project.setup_payment',
  Project_Ready = 'project.ready',
  Project_Deposit = 'project.deposit',
  Project_Escrow = 'project.escrow',
  Project_Progress = 'project.update',
  Project_Closed = 'project.closed',
  Project_Submit = 'project.submit',
  Project_Complete = 'project.complete',
  Project_Paid = 'project.paid',
  Project_PayRequested = 'project.pay_requested',
  Project_Followup = 'project.followup',
  Project_Update = 'project.update',
  Project_Reminder = 'project.reminder',
  Contract_Ready = 'contract.ready',
  Contract_Signed = 'contract.signed',
  Introduction = 'introduction',
  Connection = 'connection',
  Reminder = 'reminder',
  Meeting = 'meeting',
  Note = 'note',
  Event = 'event',
  Draft = 'draft',
  Followup = 'followup',
  Digest = 'digest',
  Message = 'message',
  Import = 'import',
  Onboarding = 'onboarding',
  Recommendation = 'recommendation',
  Survey = 'survey',
  Delete = 'delete',
  Learn = 'learn',
  Payment = 'payment',
  Code = 'code',
  Goal = 'goal',
  Subscription = 'subscription',
  Tutorial = 'tutorial',
  ReferralCredit = 'referral_credit',
  Referral = 'referral',
}

export const NotifyEmailOnly:string[] = [
  NotificationType.Project_Found,
  NotificationType.Project_Closed,
  // NotificationType.Project_Escrow,
  NotificationType.Project_Recommended,
  NotificationType.Project_Expert,
  NotificationType.Project_Invite,
  NotificationType.Project_Proposal,
];

export enum ProfileStatus {
  Basic = 'basic',
  Ready = 'ready',
  Premier = 'premier',
}

export enum NotificationEmail {
  Never = 'never',
  Now = 'now',
  Daily = 'daily',
  Sunday = 'sunday',
  Monday = 'monday',
  Tuesday = 'tuesday',
  Wednesday = 'wednesday',
  Thursday = 'thursday',
  Friday = 'friday',
  Saturday = 'saturday',
}

export interface NotificationSetting {
  type: NotificationType;
  email: NotificationEmail;
  background: boolean;
  chat: boolean;
}

export enum PushSetting {
  // need to ask the user about background push
  Ask = 'ask', // notificatons on
  AskQuiet = 'ask_quiet', // notifications off

  // user has been asked about background push but it's off
  Notify = 'notify', // notifications on
  Quiet = 'quiet', // notifications off

  // user has background push on
  Push = 'push', // notifications on
  PushQuiet = 'push_quiet', // notifications off
}

export enum Imports {
  Facebook = 'facebook',
  iCloud = 'icloud',
  LinkedIn = 'linkedin',
}

export enum Uploads {
  Contact = 'contact',
  Card = 'card',
  Custom = 'custom',
}

// Available authentication context states
export enum AuthContext {
  AuthChat = 'chat',
  // AuthMobile = 'mobile',
  AuthSyncOrganizer = 'chat_full_organizer',
  AuthNoEmail = 'chat_no_email',
  AuthNoOrganizer = 'chat_no_organizer',

  // setup directory sync
  AuthConnect = 'chat_connect',

  // setup chat api
  AuthAPI = 'chat_api',

  // workflow
  Connect = 'connect',
  Contract = 'contract',
  Contractor = 'contractor',
  Intro = 'intro',
  People = 'people',
  Project = 'project',
  ProjectCreate = 'project_create',
  ProjectExpert = 'project_expert',
  Network = 'network',
  Settings = 'settings',

  // v2
  App = 'app',
  Signup = 'signup',
  Learn = 'learn',
  Webinar = 'webinar',
  Apply = 'apply',
  Info = 'info',
  Breadwinners = 'breawinners',
}

// groups with custom providers can turn off access to certain features
export enum DisallowableAuthContext {
  Calendar = 'calendar',
  CalendarSync = 'calendar_sync',
  Contact = 'contact',
  ContactSync = 'contact_sync',
  File = 'file',
  Mail = 'mail',
  MailSync = 'mail_sync',
  Note = 'note',
  Offline = 'offline',
  Task = 'task',
  TaskSync = 'task_sync',
}

export const AuthContextPriority = {};
AuthContextPriority[AuthContext.Settings] = 0;
AuthContextPriority[AuthContext.Network] = 1;
AuthContextPriority[AuthContext.Intro] = 2;
AuthContextPriority[AuthContext.People] = 3;
AuthContextPriority[AuthContext.Project] = 4;
AuthContextPriority[AuthContext.ProjectCreate] = 5;
AuthContextPriority[AuthContext.ProjectExpert] = 6;
AuthContextPriority[AuthContext.Contractor] = 7;
AuthContextPriority[AuthContext.Contract] = 8;
AuthContextPriority[AuthContext.Connect] = 9;
AuthContextPriority[AuthContext.AuthAPI] = 10;
AuthContextPriority[AuthContext.AuthConnect] = 11;
AuthContextPriority[AuthContext.AuthNoOrganizer] = 12;
AuthContextPriority[AuthContext.AuthNoEmail] = 13;
AuthContextPriority[AuthContext.AuthSyncOrganizer] = 14;
// AuthContextPriority[AuthContext.AuthMobile] = 15;
AuthContextPriority[AuthContext.AuthChat] = 16;

// Available access levels, each maps to an AuthPermission
export enum AuthLevel {
  OrganizerSync = 'organizer_sync',
  Organizer = 'organizer',
  Email = 'email',
  Basic = 'basic',
  Demo = 'demo',
}

export const AuthLevelPriority = {};

AuthLevelPriority[AuthLevel.Demo] = 0;
AuthLevelPriority[AuthLevel.Basic] = 1;
AuthLevelPriority[AuthLevel.Organizer] = 2;
AuthLevelPriority[AuthLevel.OrganizerSync] = 3;
AuthLevelPriority[AuthLevel.Email] = 4;

// Available permissions
export enum AuthPermissions {
  Full = 'full',
  None = 'none',
  Demo = 'demo',
  Guest = 'guest',
}

// Supported providers
export enum AuthProviders {
  Google = 'google',
  Microsoft = 'microsoft',
  Msal = 'msal',
  Offline = 'offline',
  Okta = 'okta',
  Saml = 'saml',
  Email = 'email',
  Slack = 'slack',
  Stripe = 'stripe',
  Wise = 'wise',
}

export interface AuthClientNameInfo {
  name: string;
  provider: AuthProviders;
}

export interface AuthClientInfo extends AuthClientNameInfo {
  access_type?: string;
  clientId: string;
  context: AuthContext;
  scope: string;
}

export interface AuthClientContext extends AuthClientInfo {
  group?: Uid;
  permissions?: BasicInfo[];
  url?: string;
  command?: ReplyCommand;
}

export enum NoteTab {
  Personal = 'personal',
  Job = 'job',
}

let IGNORE_WORDS = [];
let MASK_WORDS = [];
let REMAP_WORDS = {};

export function loadWords(i, m, r) {
  IGNORE_WORDS = i;
  MASK_WORDS = m;
  REMAP_WORDS = r;
}

export function mapSkill(s: string): string {
  if(REMAP_WORDS && REMAP_WORDS[s]) return REMAP_WORDS[s];
  return s;
}

export function ignore(text: string): boolean {
  const ltext = text.toLowerCase();
  for (const x of ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9']) {
    if (ltext.includes(x)) return true;
  }

  if (IGNORE_WORDS.includes(ltext)) return true;
  if (MASK_WORDS.includes(ltext)) return true;

  /*for (const part of ltext.split(/[ :-=.@]/)) {
    if (MASK_WORDS.includes(part)) return true;
  }*/

  return false;
}

export function formatJob(tags): ExtraTag {
  if (!tags.length) return null;
  let job_extra = '';
  let job_type = null;
  let start = new Date(0);
  const index = tags[0].index;

  const symbol = findTypeTag(tags, TagType.symbol, true);

  const job = findTypeTag(tags, TagType.job, true);
  if (job && job.value && job.value.length && (symbol && symbol.value)) {
    return { tags, value:job.value, type: symbol ? symbol.value as TagType : TagType.default, start: new Date(job.start), index};
  }

  const job_tags = findTypeTags(tags, JobTags);

  const occupation = findTypeValue(job_tags, TagType.occupation);
  const work = findTypeValue(job_tags, TagType.work);
  const company = findTypeValue(job_tags, TagType.organization);
  const school = findTypeValue(job_tags, TagType.school);
  const department = findTypeValue(job_tags, TagType.department);
  const job_title = findTypeValue(job_tags, TagType.jobTitle);
  const alt_job_title = findTypeValue(job_tags, TagType.jobtitle);
  const title = findTypeValue(job_tags, TagType.title);
  const job_desc = findTypeValue(job_tags, TagType.jobDescription);
  const alt_job_desc = findTypeValue(job_tags, TagType.jobdescription);

  for (const jt of job_tags.filter(t => t.type !== TagType.range)) {
    const jts = new Date(jt.start);
    if (jts > start) start = jts;
  }

  if (occupation) {
    job_extra += occupation;
    job_type = TagType.occupation;
  }

  if (work && work !== occupation) {
    job_extra += `${job_extra.length ? ' ' : ''}${work}`;
    job_type = TagType.occupation;
  }

  if (job_title) {
    job_extra += `${job_extra.length ? ' ' : ''}${job_title}`;
    job_type = TagType.jobTitle;
  } else if (alt_job_title) {
    job_extra += `${job_extra.length ? ' ' : ''}${alt_job_title}`;
    job_type = TagType.jobTitle;
  } else if(title) {
    job_extra += `${job_extra.length ? ' ' : ''}${title}`;
    job_type = TagType.jobTitle;
  }

  if (company && company !== work && company !== occupation) {
    if (job_title || occupation || work) job_extra += ` at ${company.trim()}`;
    else job_extra += `${company.trim()}`;
    job_type = TagType.organization;
  }

  if (school && school !== work && school !== occupation) {
    if (job_title || occupation) job_extra += ` from ${school.trim()}`;
    else job_extra += `${school.trim()}`;
    job_type = TagType.school;
  }

  if (department) {
    if (company || school) job_extra += `, ${department.trim()}`;
    else job_extra += ` in ${department.trim()}`;
    job_type = TagType.department;
  }

  if (job_desc) {
    job_extra += ` (${job_desc.trim()})`;
    if (!job_type) job_type = TagType.default;
  } else if (alt_job_desc) {
    job_extra += ` (${alt_job_desc.trim()})`;
    if (!job_type) job_type = TagType.default;
  }

  return ((symbol && symbol.value) || job_type) && job_extra && job_extra.length ?
    { tags, value: job_extra, type: symbol ? symbol.value as TagType : job_type, start, index }
    : null;
}

export function tagPart(tags, part: TagType | TagType[]) {
  if (tags) {
    let type_tags = tags.filter(t => ![TagType.range, TagType.symbol].includes(t.type));
    if (type_tags.length === 1 && type_tags[0].type === TagType.occupation) type_tags = tagParts(type_tags[0].value);
    const tp = Array.isArray(part) ? type_tags.find(t => part.includes(t.type)) : type_tags.find(t => t.type === part);
    if (tp) return tp.value;
    else {
      const type = tags.find(t => t.type === TagType.symbol);
      const occupation = type_tags.find(t => t.type === TagType.occupation);
      if (occupation && type && 
          ((Array.isArray(part) && part.includes(type.value)) ||
          type.value === part)) return occupation.value;
    }
  }
  return '';
}

export function tagParts(value): Partial<ExtraTag>[] {
  let occupation;
  let jobTitle;
  let organization;
  let department;
  let jobDescription;

  let school = false;
  let force_department = false;

  let parts = value.split(/ at | from /i).map(p => p.trim());
  if (parts.length > 1) {
    const at_index = value.indexOf(' at ');
    const from_index = value.indexOf(' from ');
    if (at_index === -1 || (from_index !== -1 && from_index < at_index)) school = true;
  }

  if (parts[parts.length - 1].includes(',') || parts[parts.length - 1].includes(' in ')) {
    const comma = parts[parts.length - 1].split(/,| in /);
    if (comma.length > 1) force_department = true;
    parts[parts.length - 1] = comma[0];
    parts = parts.concat(comma.slice(1));
  }

  const desc = parts[parts.length - 1].match(/\(([^)]*)\)/);
  if (desc && desc[1]) {
    parts[parts.length - 1] = parts[parts.length - 1].slice(desc, desc.index).trim();
    jobDescription = desc[1].trim();
  }

  switch(parts.length) {
    case 0: 
    case 1: 
      occupation = value.trim();
      break;
    case 4:
      if (!jobDescription) jobDescription = parts[3].trim();
      else occupation = parts[3].trim();
      // no break;
    case 3:
      department = parts[2].trim();
      // no break;
    case 2:
      jobTitle = parts[0].trim();
      if (force_department && !department) department = parts[1].trim();
      else organization = parts[1].trim();
      break;    
    default:
      jobTitle = parts[0].trim();
      organization = parts[1].trim();
      department = parts[2].trim();
      if (!jobDescription) {
        jobDescription = parts[3].trim();
        occupation = parts.slice(4).join(',').trim();
      } else occupation = parts.slice(3).join(',').trim();
  }

  return [
    { type: TagType.jobTitle, value: jobTitle },
    { type: school ? TagType.school : TagType.organization, value: organization },
    { type: TagType.department, value: department }, 
    { type: TagType.jobDescription, value: jobDescription },
    { type: TagType.occupation, value: occupation }
  ].filter(t => t.value);
}

/**
 * Find all instances in array that have the provided type
 * @param arr - Array of objects to check
 * @param type_name - Type name to search for
 * @param last
 */
export function findTypeIndex(arr: PersonTag[], type_name: TagType | TagType[], last = false) {
  let start: Date = new Date(0);
  let rindex = null;
  const types = Array.isArray(type_name) ? type_name : [type_name];

  for (const index in arr) {
    const a = arr[index];
    if (types.includes(a.type)) {
      if (!last) {
        rindex = a.index;
        break;
      }

      if (new Date(a.start) >= start) {
        start = new Date(a.start);
        rindex = a.index;
      }
    }
  }

  if (rindex) return parseInt(rindex, 10);
  return rindex;
}

export function findTypeTag(arr: PersonTag[], type_name: TagType | TagType[], last = false) {
  if (!Array.isArray(type_name)) type_name = [type_name];
  let start = new Date(0);
  let tag = null;

  for (const index in arr) {
    const a = arr[index];
    if (type_name.includes(a.type)) {
      if (!last) return a;

      if (new Date(a.start) > start) {
        start = new Date(a.start);
        tag = a;
      }
    }
  }

  return tag;
}

export function findTypeValue(arr: PersonTag[], type_name: TagType | TagType[], last = false) {
  const tag = findTypeTag(arr, type_name, last);
  return tag ? tag.value : null;
}

export function findTypeTags(arr: PersonTag[], type_name: TagType | TagType[]): PersonTag[] {
  const type_names: TagType[] = Array.isArray(type_name) ? type_name as TagType[] : type_name = [type_name as TagType];
  return arr.filter(a => type_names.includes(a.type));
}

export function findTypeValues(arr: PersonTag[], type_name: TagType | TagType[]): any[] {
  const type_names: TagType[] = Array.isArray(type_name) ? type_name as TagType[] : type_name = [type_name as TagType];
  return arr.filter(a => type_names.includes(a.type)).map(a => a.value).filter(v => v && v.length);
}

export function findTypeIndexes(arr: PersonTag[]): number[] {
  return _.uniq(arr.map(a => a.index).filter(i => i !== null && i !== undefined && !isNaN(i))).sort((a,b)  => a - b);
}

export function findTypesAtIndex(arr: PersonTag[], findex: number): PersonTag[] {
  return arr.filter(a => a.index === findex);
}

export function removeTags(old_tags: PersonTag[], new_tags: Partial<PersonTag>[]): PersonTag[] {
  const remove_indexes: number[] = [];

  for (let i = 0; i < old_tags.length; i++) {
    const ot = old_tags[i];
    let found = false;
    for (const nt of new_tags) {
      if (ot.type === nt.type && ot.value === nt.value) {
        found = true;
        break;
      }
    }
    if (!found) remove_indexes.push(i);
  }

  return old_tags.filter((t,i) => !remove_indexes.includes(i));
}

export function newExtraTag(type: TagType, value: string, start?: Date): ExtraTag {
  const parts = tagParts(value);
  if (!start) start = new Date();
  const new_tag: ExtraTag = {
    type,
    tags: parts.map(t => {
      return {
        type: t.type as TagType,
        value: t.value as string,
        start,
        index:0,
      }
    }).concat([{
        type: TagType.symbol,
        value: type as string,
        start,
        index:0,
      }
    ]),
    value,
    start,
    index:0,
  };

  return new_tag;
}

export function removeExtraTag(in_tags: ExtraTag[], index: number): ExtraTag[] {
  const tags = in_tags.filter(t => t.tags).slice().sort((a,b) => a.index - b.index);
  tags.splice(index, 1);
  return tags;
}

export function updateExtraTagDates(in_tags: ExtraTag[], index: number, start: Date, end?: Date): ExtraTag[] {
  const tags = in_tags.filter(t => t.tags).slice().sort((a,b) => a.index - b.index);
  const d = new Date(start);
  tags[index].start = d
  let range;
  if (tags[index].tags) {
    for( const tag of tags[index].tags) {
      if (end && tag.type === TagType.range) {
        tag.start = new Date(end);
        range = true;
      } else tag.start = d;
    }
    if (range && tags[index].tags.length === 1) {
      tags[index].tags.push({type: tags[index].type, index: tags[index].index, start, value: tags[index].value});
    }
  }
  if (end && !range) tags[index].tags.push({type: TagType.range, index: tags[index].index, start: new Date(end), value: ''});
  return tags;
}

export function editExtraTag(tag: ExtraTag, value: string, tag_types?: TagType[] | TagType) {
  const symbol_range = tag.tags ? tag.tags.filter(t => [TagType.symbol, TagType.range].includes(t.type)) : [];
  const job_tags = tag.tags ? tag.tags.filter(t => JobTags.includes(t.type)) : [];

  if (tag_types) {
    if (!Array.isArray(tag_types)) tag_types = [tag_types];
    const existing = tag.tags.find(t => tag_types.includes(t.type));
    if (existing) existing.value = value;
    else if(job_tags.length === 1 && job_tags[0].type === TagType.occupation) {
      const parts = tagParts(tag.value);
      const epart = parts.find(t => tag_types.includes(t.type));
      if (epart) epart.value = value;
      else parts.push({type: tag_types[0], value});
      tag.tags = parts.map(t => { return {
          type: t.type,
          value: t.value,
          index: tag.index,
          start: tag.start,
        }
      }).concat(symbol_range);
    } else tag.tags.push({type: tag_types[0], value, index: tag.index, start: tag.start});
    const formatted = formatJob(tag.tags);
    if (formatted) Object.assign(tag, formatted);
  } else {
    tag.value = value;
    const parts = tagParts(tag.value);
    tag.tags = parts.map(t => { return {
        type: t.type,
        value: t.value,
        index: tag.index,
        start: tag.start,
      }
    }).concat(symbol_range);
  }
}

export function reorderExtraTags(in_tags: ExtraTag[], source: number, destination: number): ExtraTag[] { 
  const tags = in_tags.filter(t => t.tags).slice().sort((a,b) => a.index - b.index);
  const [move_tag] = tags.splice(source, 1);
  tags.splice(destination, 0, move_tag);

  spaceTags(tags);

  return tags;
}

export function spaceTags(tags: ExtraTag[]) {
  for( let i = 1; i < tags.length; i++) {
    if (tags[i].index <= tags[i - 1].index) tags[i].index = tags[i-1].index + 1;
    if (i < tags.length - 1 && tags[i + 1].index <= tags[i].index) tags[i + 1].index = tags[i].index + 1;
  }

  for (const etag of tags) {
    if (etag.tags) {
      for( const tag of etag.tags) tag.index = etag.index;
    }
  }
}

export function cleanFilter(filter: Filter) {
  const filter_tags = FilterTags.map(ft => ft.key).concat([TagType.skill]);
  filter.conditions = filter.conditions.filter(c => filter_tags.includes(c.att));
}

export function filterSkills(filter: Filter, rel?: Relation[]): string[] {
  return filter.conditions ? _.uniq(filter.conditions.filter(c => c.att === TagType.skill && (!rel || rel.includes(c.rel))).map(c => c.value as string[]).reduce((a,b) => a.concat(b), [])).filter(s => s) : [];
}

export function filterTags(filter: Filter, rel?: Relation[], exclude: TagType[] = [TagType.skill]): string[] {
  return filter.conditions ? _.uniq(filter.conditions.filter(c => c.att in TagType && !exclude.includes(c.att as TagType) && (!rel || rel.includes(c.rel))).map(c => c.value as string[]).reduce((a,b) => a.concat(b), [])) : [];
}

export interface ForaUserSettings {
  active: EntityType;
  authenticated: boolean;
  email: string;
  referral?: string;
  beta: boolean;
  calendars: { 
    enabled: boolean;
    disallowed: boolean;
    sources: {
      [key: string]: { 
        name: string; 
        selected: boolean; 
        sync: boolean; 
      };
    };
  };
  imports: { 
    disallowed: boolean; 
    enabled: boolean; 
    unauthorized?: boolean; 
    useFallback?: boolean 
  };
  info: { 
    enabled: boolean; 
    profile?: boolean; 
    accounts?: boolean; 
    notify?: boolean; 
    contracts?: boolean; 
    archives? : boolean;
    filter?: string | null;
    show?: string;
  };
  messages: { 
    disallowed: boolean; 
    enabled: boolean 
  };
  notes: {
    active?: Partial<TaskInfo>;
    edit_people?: boolean;
    disallowed: boolean;
    enabled: boolean;
    filter?: string | null;
    unauthorized?: boolean;
    useFallback?: boolean;
    hide_info_msg?: boolean;
    show_completed?: boolean;
    tab?: NoteTab;
  };
  people: { 
    active?: Partial<PersonInfo>; 
    disallowed: boolean; 
    edit?: boolean; 
    enabled: boolean; 
    filter?: string | null; 
  };
  analyses: {
    active?: Partial<AnalysisInfo>;
    disallowed: boolean;
    enabled: boolean;
    filter?: string | null;
  };
  development: {
    active?: Partial<PlanInfo>;
    disallowed: boolean;
    enabled: boolean;
    filter?: string | null;
  };
  learning: {
    active?: Partial<GoalInfo>;
    disallowed: boolean;
    enabled: boolean;
  };
  skillsets?: {
    active?: Partial<SkillsetInfo>;
    disallowed: boolean;
    enabled: boolean;
  };
  projects: {
    active?: Partial<ProjectInfo>;
    // as_client?: boolean;
    view?: ViewProjects;
    // expert?: boolean;
    filter?: string;
    // show_count?: number;
    skip_payment?: boolean;
    skip_contracting?: boolean;
    sourcing?: boolean;
  };
  widgets: Widget[];
  providers: AuthProviders[];
  push: PushSetting;
  notifications: NotificationSetting[];
  accounts: AuthProviders[];
  templates: Uid[];
  tasks: { disallowed: boolean; enabled: boolean; filter?: string | null };
  tutorial?: {
    id?: Uid;
    lesson?: number;
    assessed?: number[];
  }
}

export interface Subscription {
  token: string;
  mode: 'production' | 'sandbox';
  period: 'days' | 'weeks' | 'months' | 'years';
  subscribed?: boolean;
  trial?: Date;
  payment_due?: boolean;
  current_period_started_at: Date;
  current_period_ends_at: Date;
  seats?: number;
  has_payment?: boolean;
  affiliate?: string;
  coupon?: boolean;
}

export interface GroupSettings {
  id: Uid;
  admin?: boolean;
  host?: boolean;
  hostname?: string;
  company: string;
  name: string;
  notifications: NotificationSetting[];
  provider: AuthProviders;
  accounts: AuthProviders[];
  imports: boolean;
  redirect: string;
}

export enum Relation { 
  '<' = '<', // before, less than
  '>' ='>',  // after, greater than
  '=' = '=', // equals
  '<>' = '<>', // not equals
  '><' = '><', // in, between
  '^' = '^', // start, first
  '$' = '$', // end, last
  '~' = '~', // recent past or similar
  '!' = '!', // upcoming future
}

export enum InvRelation {
  '<' = '>',
  '>' = '<',
  '=' = '<>',
  '<>' = '=',
  '^' = '$',
  '$' = '^',
  '~' = '!',
  '!' = '~',
}

export const RelPriority = [
  Relation['='],
  Relation['<>'],
  Relation['<'],
  Relation['^'],
  Relation['~'],
  Relation['>'],
  Relation['$'],
  Relation['!'],
  Relation['><'],
];

export interface TimeVal {
  year?: boolean;
  month?: boolean;
  week?: boolean;
  day?: boolean;
  start: Date;
  end?:  Date;
  text: string;
}

export interface Condition {
  att: TagType | EntityType;
  value: string[] | TimeVal[];
  rel: Relation;
}

export interface Filter {
  id?: Uid;
  name?: string;
  network?: boolean;
  conditions: Condition[];
}

export interface Extra {
  link?: string;
  type: string | TagType; // mail | call | link | 
}

export interface ExtraTag { // extends PersonTag {
  start: Date;
  tags: PersonTag[];
  value: string;
  type: TagType;
  index: number;
}

export interface Info {
  title: string;
  added?: boolean;
  archived?: boolean;
  closed?: boolean;
  deleted?: boolean;
  edit?: boolean;
  focus?: EntityType;
  id?: Uid;
  name?: string;
  people?: Partial<PersonInfo>[];
  select?: boolean;
  refresh?: boolean;
  type: EntityType;
  updated?: boolean;
  update_date?: Date;
  card?: boolean;
  size?: {height: number, width: number};
  cache?: boolean;
}

export interface PaymentInfo extends Info {
  requires_action: boolean;
  client_secret: string;
  fee: number;
  key: string;
  account: string;
}

export interface UndefinedPersonInfo extends Info {
  email?: string;
  id: string;
  name: string;
  people: Partial<PersonInfo>[];
}

export interface OrganizationInfo extends Info {
  id: string;
  name: string;
  type: EntityType;
}

export interface PersonInfo extends Info {
  bio?: string;
  comms?: Extra[];
  contracts: ContractInfo[];
  description: BasicInfo[];
  extra: ExtraTag[];
  id: string;
  askfora_id?: string;
  image: string;
  edit_image?: string;
  links: Extra[];
  meta: string;
  name: string;
  network?: boolean;
  groups?: Uid[];
  nick: string;
  notes?: string;
  // notes: NoteInfo[];
  orgs: string[];
  skills: {value: string, weight: number}[];
  projects: ProjectInfo[];
  templates?: ProjectInfo[];
  related?: Partial<PersonInfo>[];
  related_ids?: Uid[];
  recommendation?: string;
  analyses?: AnalysisInfo[];
  asks?: AskInfo[];
  goals?: GoalInfo[]; 
  self?: boolean;
  tutorials?: Partial<TutorialInfo>[];
  certificates?: Partial<CertificateInfo>[];
  tasks: TaskInfo[];
  vanity?: string;
  type: EntityType.Person;
  status?: ProfileStatus;
  learned?: boolean;
}

export interface CandidateInfo extends PersonInfo {
  answer: string;
  public: boolean;
  groups: Uid[];
  askfora_id?: Uid;
  ready?: boolean;
  state?: ProjectCandidateState;
  refer_by?: Partial<PersonInfo>[];
}

export interface PersonUpdate {
  id: Uid;
  name?: string;
  meta?: string;
  comms?: string[];
  tags?: ExtraTag[];
  links?: string[];
  skills?: string[];
  recommendation? : string;
  bio?: string;
  answer?: string;
  public?: boolean;
  groups?: Uid[];
  askfora_id?: Uid;
  ready?: boolean;
  state?: ProjectCandidateState;
  refer_by?: Partial<PersonUpdate>[];
}

export interface UnresolvedPerson {
  name: string,
  comms: string[],
  type: EntityType.UnresolvedPerson,
  people: Partial<PersonInfo>[]
}

export interface ContractInfo extends Info {
  client_email: string;
  client_name: string;
  client_signed: boolean;
  contractor_email: string;
  contractor_name: string;
  contractor_signed: boolean;
  id: Uid;
  is_client: boolean;
  url: string;
  canceled: boolean;
}

export interface NotifyInfo extends Info {
  description?: string;
  image?: string;
  link?: string;
}

export interface EventInfo extends NotifyInfo {
  end: number | Date;
  // calendarIds: string;
  start: number | Date;
}

export interface TaskInfo extends NotifyInfo {
  due?: Date;
  completed: boolean;
  created: Date;
  id: string;
  notes: string;
  project?: Uid;
  type: EntityType.Task;
}

/* export interface NoteInfo extends Info {
  created: Date; // date
  id: string;
  notes: string[];
  type: EntityType.Note;
} */

export interface MessageInfo extends NotifyInfo {
  read: boolean;
  received: Date;
  recipient: PersonInfo[];
  sender: PersonInfo;
  subject: string;
  type: EntityType.Message;
}

export enum ProjectCandidateState {
  FOUND = 'found', // Candidate initial state
  INVITED = 'invited', // User wants to invite ths Candidate
  SENT = 'sent', // Invitation sent, not reminded yet
  REMINDED = 'reminded', // Candidate reminded about invite
  OPENED = 'opened', // Candidate opened
  ABANDONED = 'abandoned', // Candidate abandoned before NDA
  VIEWED = 'viewed', // Candidate viewed
  RECOMMENDED = 'recommended', // Candidate forwarded
  ACCEPTED = 'accepted', // Candidate indicated interest (accepted invite)
  DECLINED = 'declined', // Candidate explicitly declined
  REJECTED = 'rejected', // User rejected Candidate
  SELECTED = 'selected', // User picked this Candidate as Contractor
  SUBMITTED = 'submitted', // Caontractor submitted job
  PAYMENT_REQUESTED = 'request_payment', // Candidate requested payment?? (TODO(otrajman) is this correct)
}

export const CandidateStateOrder = [
  ProjectCandidateState.FOUND,
  ProjectCandidateState.INVITED,
  ProjectCandidateState.SENT,
  ProjectCandidateState.REMINDED,
  ProjectCandidateState.OPENED,
  ProjectCandidateState.ABANDONED,
  ProjectCandidateState.VIEWED,
  ProjectCandidateState.RECOMMENDED,
  ProjectCandidateState.ACCEPTED,
  ProjectCandidateState.DECLINED,
  ProjectCandidateState.REJECTED,
  ProjectCandidateState.SELECTED,
  ProjectCandidateState.SUBMITTED,
  ProjectCandidateState.PAYMENT_REQUESTED,
];

export interface Payment {
  created: Date;
  amount: number;
}

export interface ProjectInfo extends Info {
  admin?: boolean;
  candidates?: Partial<CandidateInfo>[];
  client?: Partial<PersonInfo>;
  completed: boolean;
  confidential: boolean;
  contract: string;
  contractor?: Partial<CandidateInfo>;
  duration: number;
  end: Date;
  escrow: any;
  fee: number;
  id: string;
  me_candidate?: Partial<CandidateInfo>;
  network: boolean;
  notes: string;
  requirements: string;
  deliverables: string;
  background: string;
  payment: any;
  proposal: boolean;
  profile: boolean;
  template: boolean;
  expert: boolean;
  accepted: boolean;
  viewed: boolean;
  declined: boolean;
  progress: number;
  rate: ProjectRate;
  refund: any;
  service_fee?: number;
  skills: string;
  skill_set: string[];
  searched_skills: string[];
  searching: boolean;
  start: Date;
  flex_dates: boolean;
  no_referrals: boolean;
  no_fee: boolean;
  simple_invite: boolean;
  update_date: Date;
  activity_date: Date;
  suggested?: Uid[];
  type: EntityType.Project | EntityType.Expert;
  public: boolean;
  groups: Uid[];
  sourcing_url?: string;
  sourcing_type?: ProjectSourcingType;
}

export interface RecommendationInfo extends Info {
  id: Uid;
  type: EntityType.Recommendation;
  from: Partial<PersonInfo>;
  to: Partial<PersonInfo>;
  when: Date;
  text: string;
  public: boolean;
}

export interface ContractInfo extends Info {
  type: EntityType.Contract;
  client_email: string;
  client_name: string;
  client_signed: boolean;
  contractor_email: string;
  contractor_name: string;
  contractor_signed: boolean;
  url: string;
}

export interface CategoryInfo {
  id: Uid;
  label: string;
  manual: boolean;
  skills: {value: string; weight: number}[];
  score?: number;
  color?: Color;
  count?: number;
}

export interface Match {
  label:string;  //category
  id: string,  //category
  score: number
}

export interface SkillsetInfo extends Info {
  type: EntityType.Skillset,
  skills: string[];
}


export interface PlanInfo extends Info {
  type: EntityType.Plan,
  skills: string[];
  assigned: Partial<PersonInfo>[];
  unassigned: Partial<PersonInfo>[];
  course_filter: string[];
}

export interface Fit {
  [key:string]: { // candidate
    [key:string]:  // category
       number;  // primary skill fit
      //cscore: number,  // combined fit
      /*related: {
        [key:string]: number;  // fit for each related skill
      };*/ 
    //}
  } 
}

export interface FocusFit {
  [key:string]:  // candidate
     number;  // primary skill fit
}

export interface CategorySkills {
  [key:string]: { // category
    [key:string]: // skill
    string[] // related skills
  }
}

export interface MappedCandidate {
  id: Uid, // candidate
  vanity: string,
  matches: Match[]
}

export interface Assessment {
  prompts: {
    question: string;
    answers: {
      answer: string;
      skills: string[]
    }[]
  } []
}

export interface Outline {
  duration: string;
  level: string;
  text: string;
  title: string;
}

export interface Lesson {
  title: string;
  text: string;
  info: Infographic[];
}

export interface Infographic {
  title: string;
  text: string;
  image: string;
}

export interface LessonSet {
  outline: Outline;
  lessons: Lesson[];
  assessment: Assessment;
}

export interface CertificateInfo {
  id: Uid;
  title: string;
  skills: string[];
  lessons: string[];
  completed: Date;
  name: string;
  vanity: string;
  image: string;
  links: Extra[];
  sponsor?: {
    name: string;
    url: string;
    logo: string;
  }
}

export interface TutorialInfo {
  id: Uid;
  title: string;
  skills: string[];
  preassessment: Assessment;
  lesson_set: LessonSet[];
  status?: {
    lesson?: number;
    preassessed?: boolean;
    assessed?: number[];
    practiced?: Date;
  };
  practice?: {
    hash: string;
    value: { role: string; parts: { text: string; }[]}[];
    count: number;
  };
  assessments?: {
    hash: string;
    value: { role: string; parts: { text: string; }[]}[];
    count: number;
  }[];
  sponsor?: {
    name: string;
    url: string;
    logo: string;
  }
}

export interface TutorParams {
  offset?: number; // timezone offset
  timeZone?: string; // timezone string
  locale?: string; // locale
  tutorial?: Uid; // tutorial context
  lesson?: number; // lesson number
  message?: string; // messag to send to tutor
  catalog?: boolean; // load all tutorials
  skills?: {value: string, weight: number}[]; // skills to set
  register?: string; // email to register
  interest?: boolean; // just interested
  code?: string; // registration code
  referred_by?: string; // referral code
  shared?: boolean; // user shared
  practice?: boolean; // run a practice
  done?: boolean;
  status?: { // update status
    lesson?: number;
    assessed?: number[];
    practiced?: Date;
  };
  assessment?: { 
    role: string; 
    parts: { text: string; }[]
  }[];
  logout?: boolean; // loogout
  subscribe?: boolean;  // start subscription 
  unsubscribe?: boolean; // cancel subscription
  name: string;
  links: Extra[];
}

export interface TutorReply {
  tutorial?: TutorialInfo;
  reply: string;
}

export interface TutorRegister {
  email: string, 
  guest: boolean, 
  subscription: Subscription, 
  affiliate: string,
  user_id: string,
  me: PersonInfo,
}

export interface AnalysisInfo extends Info {
  type: EntityType.Analysis,
  id: Uid,
  x?: TagType;
  y?: TagType;
  categories: CategoryInfo[];
  target_skills: CategoryInfo[];
  focus_skill: CategoryInfo;
  candidates: Partial<CandidateInfo>[];
  filters: Filter[];
}

export interface AskInfo extends Info {
  type: EntityType.Ask,
  id: Uid,
  public: boolean,
  shared: {type: 'user'|'group', id: Uid}[],
  projects: Uid[],
  categories: CategoryInfo[];
  filters: Filter[];
  candidates?: Partial<CandidateInfo>[];
  chat: ServerChat[]; 
  created?: Date;
  update_date?: Date;
}

export interface CourseInfo extends Info {
  title: string,
  id: Uid,
  skills: string[];
  link: string;
  completed?: Date;
}

export interface GoalInfo extends Info {
  id: Uid;
  category: Partial<CategoryInfo>;
  score: number;
  experts: Partial<PersonInfo>[];
  assigned? : Date;
  assessed?: boolean;
  courses: {
    id: Uid;
    link: string;
    skills: string[];
    title: string;
    score?: number;
    completed?: Date;
    ignore?: boolean;
  } [];
}

export interface Reply {
  id?: Uid;
  label: string;
  link?: string;
  people?: Partial<PersonInfo>[];
  read?: boolean;
  redirect?: boolean;
  text?: string;
  date?: Date;
  image?: string;
}

export interface BasicInfo extends Reply {
  calendar?: string;
  message?: boolean;
}

export type PartialInfo = Partial<Info> | Partial<Info>[];


export interface ReplyCommand {
  id?: string;
  cmd?: string;
  data?;
  entity?: PartialInfo | PartialInfo[];
  forget?: boolean;
  url?: string;
  wait?: boolean;
  link?: boolean;
  reset?: boolean;
  //socket?: boolean;
}


export interface ServerInfo {
  id: Uid; // unique id  for response
  clear?: boolean; // clear previous chats
  close?: boolean; // close open items without saving
  info: Info[]; // show or save entities
  select?: boolean; // select pick
  add?: boolean; // newly created info
}

export interface ServerChatResponse extends ServerInfo {
  ping: number; // ping back in seconds

  reply?: Reply[]; // replies to the user
  answers?: string[]; // buttons with answers
  hint?: string; // input box hint
  suggestion?: string; // text in the input box

  hide?: number; // hide the chat
  sticky?: boolean; // show replies again after user hides chat
  unstick?: boolean; // remove sticky

  max?: number; // max size of chat window, 100 = full
  page?: number; // number of pages in reply

  open?: {type: EntityType, id: Uid}; // v2 open an item

  redirect?: string; // redirect the browser
  quick_replies?: { [key: string]: ServerChatResponse }; // pre-canned replies
  post_url?: string; // post back to this url
  command?: ReplyCommand; // send a command back
  callback?: any; // notification of chat
  local_chat?: any; // for local replies
  refresh?: boolean; // refresh list of people

  logout?: boolean; 
}


export interface ServerChatBody {
  data?: any;
  entities: Info[];
  id?: string;
  locale?: string;
  message?: string;
  offset?: number;
  ping?: number;
  timeZone?: string;
  max?: number;
  command?: boolean;
  link?: boolean;
  sent?: boolean;
}

export enum ChatComponent {
  Mail = 'mail',
  Meet = 'meet',
  Search = 'search',
  Configure = 'configure',
  Share = 'share',
  Explore = 'explore',
  Project = 'project',
  Analysis = 'analysis',
}

export interface ComponentChat {
  id: string;
  component: ChatComponent;
  data?: any;
}

export type ServerChat = ServerChatResponse | ServerChatBody | ComponentChat;

export interface PersonTag {
  index: number;
  start: Date;
  type: TagType;
  value: string;
}

export interface Shortcut {
  name: string;
  value: string;
}

export interface WelcomeInfo {
  icon? : string;
  border_color?: string;
  color?: any; // SemanticCOLORS;
  description?: string[];
  default?: number;
  mobile_description?: string[];
  heading?: string;
  image?: any;
  subheading?: string;
  text_color?: string;
  width?: string;
}

export interface StartInfo {
  path: string;
  title: string;
  subtitle: string;
  content: WelcomeInfo[];
  skills: string[];
  project?: Uid;
  link_to: string;
  link_text: string;
}

export enum Color {
  red = 'red',
  green = 'green',
  blue = 'blue', 
  yellow ='yellow',
  orange = 'orange',
  brown = 'brown',
  violet = 'violet',
  grey = 'grey',
  pink = 'pink',
  teal = 'teal',
}

export const ANONYMOUS_ID = 'people/anonymous';

export function EMAIL_TRACKING(s,i) { return `utm_campaign=share_link&utm_source=email&utm_medium=email&utm_term=${encodeURI(s)}&utm_id=${encodeURI(i)}`; }
export function TWITTER_TRACKING(s,i) { return `utm_campaign=share_link&utm_source=twitter&utm_medium=web&utm_term=${encodeURI(s)}&utm_id=${encodeURI(i)}`; }
export function FACEBOOK_TRACKING(s,i) { return `utm_campaign=share_link&utm_source=facebook&utm_medium=web&utm_term=${encodeURI(s)}&utm_id=${encodeURI(i)}`; }
export function LINKEDIN_TRACKING(s,i) { return `utm_campaign=share_link&utm_source=linkedin&utm_medium=web&utm_term=${encodeURI(s)}&utm_id=${encodeURI(i)}`; }
export function DEFAULT_TRACKING(s,i) { return `utm_campaign=share_link&utm_source=askfora&utm_medium=web&utm_term=${encodeURI(s)}&utm_id=${encodeURI(i)}`; }
