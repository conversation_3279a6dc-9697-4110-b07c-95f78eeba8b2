/*export type Profile = {
  email?: string; // `mail` if not present in the assertion
  ID?: string;
  issuer?: string;
  mail?: string; // InCommon Attribute urn:oid:0.9.2342.19200300.100.1.3
  nameID?: string;
  nameIDFormat?: string;
  nameQualifier?: string;
  sessionIndex?: string;
  spNameQualifier?: string;
  getAssertion(): any; // get the assertion XML parsed as a JavaScript object
  getAssertionXml(): string; // get the raw assertion XML
  getSamlResponseXml(): string; // get the raw SAML response XML
} & {
  [attributeName: string]: string; // arbitrary `AttributeValue`s
};
*/