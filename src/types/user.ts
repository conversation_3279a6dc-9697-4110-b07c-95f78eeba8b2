import { NormalizedProviderToken } from './auth';
import { Tracking } from './globals';
import { IAvroSchema, IEntity, ImportType } from './items';
import { AuthPermissions, AuthProviders, EntityType, ForaUserSettings, Uid } from './shared';

import { bindFields } from '../utils/funcs';

export const FORA_PROFILE = 'anonymous';
export const FORA_GUEST_ID = 'people/guest';


export class User implements IEntity {
  readonly type: EntityType = EntityType.User;
  readonly nonIndexedFields: string[] = ['accounts', 'notify', 'tokens', 'settings', 'data_sources', 'bio'];
  readonly schema?: IAvroSchema = {
    type: 'record',
    namespace: 'com.askfora',
    name: 'User',
    doc: 'AskFora user cookie',
    fields: [
      { name: 'id', type: 'string' },
      { name: 'profile', type: 'string' },
      { name: 'name', type: 'string' },
      { name: 'email', type: 'string' },
      { name: 'exported', type: 'boolean' },
      { name: 'start', type: 'date' },
      { name: 'last', type: 'date' },
      { name: 'last_refresh', type: 'date' },
      { name: 'refreshing', type: 'boolean' },
      { name: 'offset', type: 'long' },
      { name: 'locale', type: 'string' },
      { name: 'timeZone', type: 'string' },
      { name: 'provider', type: 'string' },
      { name: 'groups', type: 'bytes', isArray: false },
      { name: 'notify', type: 'bytes', isArray: true },
      { name: 'permissions', type: 'bytes', isArray: false },
      { name: 'tokens', type: 'bytes', isArray: false },
      { name: 'full_refresh', type: 'bytes', isArray: true },
      { name: 'refreshed', type: 'bytes', isArray: true },
      { name: 'calendars', type: 'bytes', isArray: false },
      { name: 'settings', type: 'bytes', isArray: false },
      { name: 'accounts', type: 'bytes', isArray: false },
      { name: 'data_sources', type: 'bytes', isArray: false },
      { name: 'imports', type: 'bytes', isArray: true },
      { name: 'vanity', type: 'string' },
      { name: 'bio', type: 'string' },
    ],
  };

  accounts: { [key: string]: {[key:string]: NormalizedProviderToken} } = undefined; // Map of account data - top level key is the provider
  calendars: { [key: string]: { name: string; selected: boolean; sync: boolean } } = undefined;
  data_sources: { [key: string]: { [key:string] : any } } = undefined; // Metadata for all data sources available
  email: string = undefined;
  exported: boolean; // did an export
  full_refresh: EntityType[] = undefined; // List of entities that need a full refresh
  groups: { [key: string]: string[] } = undefined; // Map of Groups user is a member of, with permissions for that group
  id: string = undefined;
  last: Date = undefined; // new Date(0);
  last_refresh: Date = undefined; // new Date(0);
  locale: string = undefined; // Locale - user's language, region and any special variant preferences
  messages: string[] = undefined;
  name: string = undefined;
  notify: any[] = undefined; // Firebase notification tokens
  offset: number = undefined; // minutes from GMT
  permissions: { demo?: AuthPermissions; email: AuthPermissions; organizer: AuthPermissions } = undefined;
  profile: Uid = undefined; // Users profile ID. Comes from the ID of the user that is logged in via OAuth
  provider: AuthProviders = undefined; // The provider this user is using for OAuth
  // push: string = undefined; // @deprecated
  refreshed: EntityType[] = undefined; // List of types of data that has recently been refreshed for the user
  refreshing: boolean = undefined; // Flag indicating that we are refreshing data for the user
  self = false;
  settings: ForaUserSettings = undefined;
  start: Date = undefined; // new Date();
  vanity: string = undefined;
  bio: string = undefined;
  timeZone: string = undefined;
  imports: { id: Uid; date: Date; type: ImportType }[] = undefined;
  tracking?: Tracking;
  affiliate?: string;

  isAnonymousAccount() { return !this.profile || this.profile === FORA_PROFILE }
  isGuestAccount(): boolean { return !this.profile || this.profile.startsWith('g-') }

  constructor(values?: Partial<User>) {
    if (values && Object.keys(values).length) bindFields(values, this);
  }
  account: string;
}