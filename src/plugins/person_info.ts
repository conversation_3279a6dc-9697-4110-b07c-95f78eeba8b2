import _ from 'lodash';
import util from 'util';
import { v4 as uuid } from 'uuid';

import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { dehydrate<PERSON><PERSON>, Person, SavedFilter } from '../types/items';
import { PersonInfoPluginState, PersonInfoSearchType, Plugin } from '../types/plugins';
import { EntityType, PersonInfo, Relation, TagType, Uid } from '../types/shared';

import { checkFilter } from '../utils/filter';
import { arraysIntersectCount, flatten, makeIdMap } from '../utils/funcs';
import { promptInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import { AuthProvider } from '../auth/auth_provider';
import data from '../data';
import { lookupSkill } from '../skills';

const DEBUG = (require('debug') as any)('fora:plugins.PersonInfo');
const LOG_NAME = 'plugins.person_info';
const PERSON_NAME = 'Person Info';
const PERSON_DESC = 'Get information about a person';

const topics = [
  TOPIC('PERSON_INFO'), 
  TOPIC('PI_FIND_PERSON'), 
  TOPIC('PI_REFINE_PERSON'), 
  TOPIC('PI_FOUND_PERSON'), 
  TOPIC('PI_SKILL_SEARCH')
];

// quick check on whether user is asking about a person
function keywordMatch(actions, message, raw_message) {
  // don't match on tell / what
  if (parsers.checkKeyword(message, lang.person_info.NOT_INQUIRY_START, true) && parsers.checkKeyword(message, lang.person_info.NOT_INQUIRY)) return false;

  if (parsers.checkKeyword(message, lang.person_info.INQUIRY_START, true) && parsers.checkKeyword(message, lang.person_info.INQUIRY_KEY)) return true;
  
  if (parsers.checkKeyword(message, [...lang.person_info.SEARCH, ...lang.person_info.FIND, ...lang.person_info.LIST], true)) return true;

  const entity = Dialog.getActionValues(actions, ActionType.ENTITY)
    .filter(e => e.people && e.people.length)
    .map(e => e.name);
  const org = Dialog.getActionValues(actions, ActionType.ORG).map(e => e.name);
  const skill = Dialog.getActionValues(actions, ActionType.SKILL);

  // short cut - list skill
  if (parsers.checkKeyword(message, lang.person_info.INQUIRY_START, true) && skill.length) return true;

  // short cut - just a name or org
  if (raw_message.length && (entity.join(' ') === raw_message || org.join(' ') === raw_message)) return true;

  // list import
  if(parsers.checkKeyword(message, lang.person_info.LIST, true) && message.split(' ').length > 1) return true;
    
  return !!(parsers.checkKeyword(message, lang.person_info.PERSON_INFO) && 
    (entity.length || org.length || 
      Dialog.checkActions(actions, [ActionType.PLACE, ActionType.SKILL, ActionType.ENTITY_TYPE, ActionType.DATE, ActionType.PERSON, ActionType.RANGE])));
}

// look for person attribute in the message and recent dialog replies
// e.g. people in events, tasks or messages
function setAction(dialog: Dialog, message: string) {
  const curr_dialog = dialog.currentDialog();

  // check for list results first
  if (isActive(dialog)) {
    if(message === 'list') {
      if (dialog.context.person_info) {
        if (dialog.context.person_info.all_people) {
          dialog.addReplies(dialog.context.person_info.all_people, curr_dialog ? curr_dialog.next_topic : null);
          if (dialog.context.person_info.filters) dialog.appendReplies(dialog.context.person_info.filters, curr_dialog ? curr_dialog.next_topic : null);
        }
        if (!(curr_dialog && curr_dialog.next_topic)) dialog.context.person_info.refine_people = null; // dialog.clearContext('refine_people');
        dialog.setTopic(Topics.PERSON_INFO);

      } else {
        dialog.reset();
        return false;
      }
    }
    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;
    return true;
  }

  const people = dialog.actionValues(ActionType.ENTITY).filter(e => (e.people && e.people.length) || e.type === EntityType.UnresolvedPerson); // .map(e => e.name);
  const orgs = dialog.actionValues(ActionType.ORG); // .map(e => e.name);

  // check for list skilled users
  const skill = dialog.actionValues(ActionType.SKILL); // .map(e => e.name);
  const is_list = parsers.checkKeyword(message, lang.person_info.LIST, true);
  if ((is_list && message.split(' ').length > 1) || skill.length && !people.length && !orgs.length && parsers.checkKeyword(message, lang.person_info.FIND, true)) {
    const full_skill = dialog.message.message.split(' ').slice(1).join(' ');
    if (skill.length || full_skill.length) {
      // override skills
      if (is_list) {
        dialog.clearActions(ActionType.SKILL);
        dialog.addAction(ActionType.CMD, 'list');
      }
      dialog.addAction(ActionType.SKILL, full_skill);
    }

    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = full_skill;

    dialog.setTopic(Topics.PI_SKILL_SEARCH);
  }

  const is_search = parsers.checkKeyword(message, [...lang.person_info.SEARCH, ...lang.person_info.FIND, ...lang.person_info.LIST], true);
  if (people.length || orgs.length) {
    if (!isActive(dialog)) dialog.setTopic(Topics.PERSON_INFO);

    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();

    if (is_search) {
      dialog.addAction(ActionType.CMD, 'search');
      const full_skill = dialog.message.message.split(' ').slice(1).join(' ');
      if (skill.length) {
        dialog.clearActions(ActionType.SKILL);
        dialog.addAction(ActionType.SKILL, full_skill);
      }
      if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = full_skill;
    } else if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;
  }

  const entities = dialog.getRepliesType(EntityType.Person);
  if (entities.length) {
    if (!isActive(dialog)) dialog.setTopic(Topics.PERSON_INFO);

    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;
  }

  /*if (dialog.actionValues(ActionType.PLACE).length || dialog.actionValues(ActionType.SKILL).length) {
    if (!isActive(dialog)) dialog.setTopic(Topics.PERSON_INFO);

    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;
    
    return true;
  }*/

  const events = dialog.getRepliesType(EntityType.Event, true);
  if (events.length && events[0].people && events[0].people.length > 1) {
    if (!isActive(dialog)) dialog.setTopic(Topics.PERSON_INFO);

    const event = events[0];
    dialog.newDialog();
    for (const index in event.people) {
      if (index in event.people) {
        const person = event.people[index];
        person.type = EntityType.Person;
        if (!person.self) dialog.appendReplies(person);
      }
    }
    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;
  }

  // TODO TASK

  const msgs = dialog.getRepliesType(EntityType.Message, true);
  if (msgs.length) {
    if (!isActive(dialog)) dialog.setTopic(Topics.PERSON_INFO);

    const msg = msgs[0];
    dialog.newDialog();

    let person = msg.sender;
    if (!person.self) {
      dialog.appendReplies(person);
      person.type = EntityType.Person;
    }

    if (msg.recipient) {
      for (const index in msg.recipient) {
        if (index in msg.recipient) {
          person = msg.recipient[index];
          if (!person.self) {
            dialog.appendReplies(person);
            person.type = EntityType.Person;
          }
        }
      }
    }

    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;

  }

  const filters = dialog.filters;
  if (filters && filters.length) {
    if (!isActive(dialog)) dialog.setTopic(Topics.PERSON_INFO);

    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    dialog.context.person_info.filters = filters.map(f => new SavedFilter(f));
  }

  const active = isActive(dialog);
  if (active) {
    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;
  }

  return active;
}

function isActive(dialog) {
  return dialog.checkTopic([Topics.PERSON_INFO, Topics.PI_FIND_PERSON, Topics.PI_REFINE_PERSON, Topics.PI_SKILL_SEARCH]);
}

function findPersonPrompt(dialog: Dialog, find_person: string | Partial<PersonInfo>[] = null) {
  dialog.context.person_info = new PersonInfoPluginState();
  if (find_person) {
    dialog.clearActions(ActionType.ENTITY);
    if (Array.isArray(find_person)) {
      dialog.addAction(ActionType.ENTITY, { name: 'found_people', type: EntityType.Person, people: find_person });
    } else {
      dialog.addAction(ActionType.ENTITY, { name: find_person, type: EntityType.UnresolvedPerson });
      dialog.context.person_info.search_term = find_person as string;
    }
  }
  dialog.nextTopic(dialog.topic);
  return findPerson(dialog);
}

function personInfo(dialog: Dialog) {
  const curr_dialog = dialog.currentDialog();
  const people = dialog.actionValues(ActionType.ENTITY);
  const orgs = dialog.actionValues(ActionType.ORG);
  const places = dialog.actionValues(ActionType.PLACE);
  const skills = dialog.actionValues(ActionType.SKILL);
  const dates = dialog.actionValues(ActionType.DATE);

  // first time through, go to find person then come back to person info
  // TODO - can we just check the context for refine_people?
  if (!dialog.context.person_info.refine_people && (dates.length || people.length || orgs.length || places.length || (skills.length && skills[0].toLowerCase() !== 'list'))) {
    dialog.nextTopic(dialog.topic);
    return findPerson(dialog);
  } else if (curr_dialog && curr_dialog.replies.length) {
    const ids = dialog.getRepliesType(EntityType.Person).filter(r => r.id).map(r => r.id);
    dialog.addFilters({ id: uuid(), conditions: [{ att: EntityType.Person, rel: Relation['='], value: ids }] });

    // check for person
    if (!dialog.context.person_info.person_search) {
      dialog.context.person_info.person_search = true;
      dialog.context.person_info.people_found = false;
      dialog.runAsync('person_info', async () => {
        const filters = dialog.context.person_info ? dialog.context.person_info.filters : undefined;
        const people = await dialog.people.byId(ids);
        if (people) dialog.context.person_info.found_people = people.filter(p => !p.network && !p.self && 
          (!filters || !filters.length || checkFilter(p, filters, dialog.cache))).map(p => new Person(p));
        dialog.context.person_info.people_found = true;
      });
    }

    // if (!dialog.context.person_info.people_found) dialog.quickPing();
  } else if (dialog.context.person_info.person_search) {
    if (dialog.context.person_info.people_found) {
      dialog.addReplies(dialog.context.person_info.found_people, curr_dialog ? curr_dialog.next_topic : null);
      if (dialog.context.person_info.filters) dialog.appendReplies(dialog.context.person_info.filters, curr_dialog ? curr_dialog.next_topic : null);
      // dialog.clearPing();
    } // else dialog.quickPing();
  } else {
    // nothing found
    dialog.context.person_info.people_found = true;
    // dialog.clearPing();
  }

  return false;
}

function skillSearch(dialog: Dialog): void {
  const curr_dialog = dialog.currentDialog();
  const act_skills = dialog.actionValues(ActionType.SKILL);
  const list_cmd = dialog.actionValues(ActionType.CMD).find(c => c === 'list');

  if (dialog.context.person_info.people_found) {
    // clear the ping
    dialog.appendReplies(dialog.context.person_info.found_people, curr_dialog ? curr_dialog.next_topic : Topics.PERSON_INFO);
    if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
    if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;
    if (dialog.context.person_info.filters) dialog.appendReplies(dialog.context.person_info.filters, curr_dialog ? curr_dialog.next_topic : null);
    // dialog.context.person_info.hide = dialog.context.person_info.found_people.length > 0;
    dialog.setTopic(Topics.PERSON_INFO);
  } else if (dialog.context.person_info.skill_search_started) {
    // We are already running a skill set search, quick ping and return
    // dialog.quickPing();
  } else {
    // Kick off a search for people with the skills requested
    dialog.context.person_info.skill_search_started = true;
    dialog.context.person_info.people_found = false;

    const filters = dialog.context.person_info ? dialog.context.person_info.filters : null;
    let filter_skills = [];
    if (filters) {
      filters.forEach(f => {
        if (f.conditions) {
          f.conditions.filter(c => [Relation['='], Relation['~']].includes(c.rel)  && c.att === TagType.skill).forEach(c => {
            if (c.value && Array.isArray(c.value) && c.value.length && typeof c.value[0] === 'string') {
              filter_skills = filter_skills.concat(c.value as string[]);
            }
          });
        }
      });
    }

    const skills = [...act_skills, ...filter_skills];

    // Build an array of skills
    let skill_set: string[] = [list_cmd ? dialog.message.message.slice(5).trim() : dialog.message.message.trim()];
    for (let index = 0; index < skills.length; index++) {
      const skill_words = skills[index].split(' ');
      if (index === 0 && skill_words[0] === 'list') {
        skill_set = skill_set.concat(skill_words.slice(1));
      } else skill_set = skill_set.concat(skill_words);
    }

    skill_set = _.uniq(skill_set.map(t => list_cmd ? t : t.toLowerCase()).filter(t => !parsers.ignore(t, false, false)));
    logging.infoF(LOG_NAME, 'skillSearch', `Skill set from message ${util.format(skill_set)}`);

    dialog.context.person_info.search_type = PersonInfoSearchType.Skill;

    dialog.runAsync('person_info', async () => {
      logging.infoF(LOG_NAME, 'skillSearch', `Searching skills ${util.format(skill_set)}`);
      // For the skills that were asked for, call the skill service to find all the synonyms of the skills
      const search_skills = [];
      if (!list_cmd) {
        const found_skills = await lookupSkill(skill_set, dialog.user.locale);

        for (const skill of found_skills) {
          search_skills.push(skill.skill);
          if (skill.synonyms) for (const synonym of skill.synonyms) search_skills.push(synonym);
          if (skill.initialisms) for (const initials of skill.initialisms) search_skills.push(initials);
        }
      }

      logging.infoF(LOG_NAME, 'skillSearch', `Expanded skills ${util.format(search_skills)}`);

      if (logging.isDebug(dialog.user.profile)) {
        DEBUG('skillSearch: group IDS %O', dialog.user.groups);
        DEBUG('skillSearch: group loaded %O', dialog.user.loaded_groups);
      }

      const mandatory_tags = dialog.user.mandatory_tags;
      const group_search_ids = dialog.user.search_groups;
      const conditions = dialog.user.group_conditions;

      logging.infoF(LOG_NAME, 'skillSearch', `Find by skills`);

      for (const condition of conditions) {
        dialog.addFilters({ id: uuid(), network: true, conditions: [...condition, { att: TagType.skill, rel: Relation['~'], value: skill_set }]});
        dialog.addFilters({ id: uuid(), network: true, conditions: [...condition ,{ att: TagType.skill, rel: Relation['~'], value: search_skills}]});
      }

      // Find all people that match skills, optional mandatory tags, and groups
      let returned_people: Partial<Person>[] = await dialog.search.searchSkills([...skill_set, ...search_skills], {
        network: list_cmd ? 'exclude' : 'include', require_email: false, mandatory_tags, group_search_ids});

      logging.infoF(LOG_NAME, 'skillSearch', `Returned ${returned_people.length} people`);
      if (dialog.context.person_info) {
        for (let i = 0; i < returned_people.length; i++) {
          const person = returned_people[i];
          if (!person.id) returned_people[i] = await dialog.people.temp(person, true);
        }

        logging.infoF(LOG_NAME, 'skillSearch', `Sorted ${returned_people.length} people`);
        dialog.context.person_info.found_people = returned_people.filter(p => !p.self && (list_cmd || checkFilter(p, filters, dialog.cache, null, [TagType.skill]))).map(p => dehydratePerson(p, skill_set));

        logging.infoF(LOG_NAME, 'skillSearch', `Found ${dialog.context.person_info.found_people.length} people`);

        dialog.context.person_info.people_found = true;
      } else logging.infoF(LOG_NAME, 'skillSearch', `Lost context`);
    });
  }
}

function findPerson(dialog: Dialog) {
  let curr_dialog = dialog.currentDialog();
  const act_people: { id: Uid; name: string; people: Partial<PersonInfo>[] }[] = dialog.actionValues(ActionType.ENTITY);
  const act_orgs = dialog.actionValues(ActionType.ORG).map(e => e.name);
  // const places = dialog.actionValues(ActionType.PLACE);
  const skills = dialog.actionValues(ActionType.SKILL);
  const search_cmd = dialog.actionValues(ActionType.CMD).find(c => c === 'search');
  const events = dialog.actionValues(ActionType.DATE);
  dialog.clearActions();

  dialog.clearDialog(true);
  dialog.setTopic(Topics.PI_FIND_PERSON);

  const found_people = act_people ? act_people.find(p => p.id === 'found_people') : null;
  if (!dialog.context.person_info.search_term && found_people) {
    dialog.context.person_info.search_term = act_people[0].name;
  }

  let find_people: boolean = act_people.length > 0 || act_orgs.length > 0;
  const find_skills: boolean = skills.length > 0;

  // if we resolved to people and skills, figure out which one based on message case
  if (find_people) {
    logging.infoFP(LOG_NAME, 'findPerson', dialog.user.profile, 'resolving find people and skills');
    const matched_people = flatten(act_people.filter(p => p.people && p.people.length).map(p => p.people.filter(p => p.id)));
      
    const matched_names = act_people.map(p => p.name);
    const people_names = matched_people.map(p => p.name);

    // look for exact full name of found people regardless of case
    let has_exact_match = dialog.context.person_info.search_term ? parsers.checkKeyword(dialog.context.person_info.search_term.toLowerCase(), people_names.map(p => p.toLowerCase())) : false;

    if (has_exact_match) {
      logging.infoFP(LOG_NAME, 'findPerson', dialog.user.profile, `resolving find people exact ${dialog.context.person_info.search_term.toLowerCase()}`);
    }

    // look for case match of matched names
    if (!has_exact_match && dialog.context.person_info.search_term) {
      has_exact_match = parsers.checkKeyword(dialog.context.person_info.search_term, matched_names);
    }

    if (has_exact_match) {
      logging.infoFP(LOG_NAME, 'findPerson', dialog.user.profile, `resolving find people match ${JSON.stringify(matched_names)}`);
      find_people = has_exact_match;
    }
  
    dialog.context.person_info.search_type = PersonInfoSearchType.Name;
  }

  if (!dialog.context.person_info.ids) {
    if (find_people && !dialog.context.person_info.name_search) {
      dialog.runAsync('person_info', async () => {
        const people_ids: { [key: string]: Partial<Person> } = {};
        let all_names = [];
        const new_names = [];
        for (const ap of act_people) {
          if (ap.people && ap.people.length) {
            const ap_pp: Partial<Person>[] = ap.people.map(p => {
              all_names.push(p.name);
              return { id: p.id, displayName: p.name };
            });
            Object.assign(people_ids, makeIdMap(ap_pp));
          } else new_names.push(ap.name);
        }

        let network = false;

        if (new_names.length) {
          all_names = all_names.concat(new_names);
          let name_people = await dialog.people.findByName(new_names);
          dialog.context.person_info.search_type = PersonInfoSearchType.Name;
          if (!name_people || !name_people.length) {
            const mandatory_tags = dialog.user.mandatory_tags;
            const group_search_ids = dialog.user.search_groups;
            const conditions = dialog.user.group_conditions;

            name_people = await dialog.search.searchByName(new_names, true, mandatory_tags, group_search_ids);
            network = true;
            dialog.context.person_info.search_type = PersonInfoSearchType.Network;

            if (name_people && name_people.length) {
              for(const condition of conditions) {
                dialog.addFilters({network: true, conditions: condition});
              }

              const save_network: Person[] = []

              name_people.forEach(r => {
                if(r.network || !r.id) {
                  r.tempId();
                  save_network.push(new Person(r));
                }
              });

              if (save_network.length) await data.people.saveAll(dialog.user, save_network);
            }
          }
          Object.assign(people_ids, makeIdMap(name_people));
        }

        const ids = Object.keys(people_ids);
        if (ids.length) dialog.addFilters({ id: uuid(), network, conditions: [{ att: EntityType.Person, rel: Relation['='], value: ids }] });

        const a_name_ids: { [key: string]: Partial<Person> } = {};
        let a_ids: Uid[] = [];

        Object.assign(a_name_ids, people_ids);
        a_ids = Object.keys(people_ids);
        if (act_orgs && act_orgs.length) {
          dialog.addFilters({id: uuid(), conditions: [{att: TagType.organization, rel: Relation['='], value: act_orgs}]});

          for (const org of act_orgs) {
            const org_people = await dialog.people.findByOrg(act_orgs);
            const org_people_ids = makeIdMap(org_people);

            Object.assign(a_name_ids, org_people_ids);

            const opids: string[] = Object.keys(org_people_ids);
            if (!a_ids.length && !new_names.length) a_ids = opids;
            else if (!all_names.join(' ').includes(org)) a_ids = a_ids.filter(p => opids.includes(p));
          }
        }

        if (dialog.context.person_info) {
          dialog.context.person_info.name_ids = a_name_ids;
          dialog.context.person_info.ids = a_ids;
        }
      });
      return;
    } else if(dialog.context.person_info.name_search) {
      return;
    } else if (events && events.length) {      
      dialog.context.person_info.search_type = PersonInfoSearchType.Event;

    } else if(find_skills) {
      dialog.setTopic(Topics.PI_SKILL_SEARCH);
      skillSearch(dialog);
      return;
    } else {
      // not found
      dialog.context.person_info.ids = [];
    }
  }

  const reply_people: Partial<Person>[] = [];
  switch (dialog.context.person_info.ids.length) {
    case 1:
      reply_people.push(new Person(Object.values(dialog.context.person_info.name_ids)[0]));
    // no break
    case 0:
      if (curr_dialog && curr_dialog.next_topic) dialog.setTopic(curr_dialog.next_topic);
      dialog.appendReplies(reply_people, Topics.PI_FOUND_PERSON);
      if (!isActive(dialog)) dialog.clearContext('person_info');
      return dialog.currentPlugin().runAction(dialog);
  }

  const full_names = [];
  const names = [];

  for (const ap of act_people.filter(ap => ap.name)) {
    const lname = ap.name.toLowerCase();
    if (full_names.includes(lname)) {
      full_names.push(lname);
      for (const n of lname.split(' ')) {
        if (!names.includes(n)) names.push(n);
      }
    }
  }

  dialog.context.person_info.ids.sort((a, b) => {
    let a_match = 0;
    let b_match = 0;
    const a_name = dialog.context.person_info.name_ids[a].displayName ? dialog.context.person_info.name_ids[a].displayName.toLowerCase() : undefined;
    const b_name = dialog.context.person_info.name_ids[b].displayName ? dialog.context.person_info.name_ids[b].displayName.toLowerCase() : undefined;

    if (a_name && full_names.includes(a_name)) a_match += 10;
    if (b_name && full_names.includes(b_name)) b_match += 10;

    for (const n of names) {
      if (a_name.includes(n)) a_match++;
      if (b_name.includes(n)) b_match++;
    }
    
    if (a_match !== b_match) return b_match - a_match;

    if (a.startsWith('people/c') || a.startsWith('people/m') || a.startsWith('people/s')) return -1;
    if (b.startsWith('people/c') || b.startsWith('people/m') || b.startsWith('people/s')) return 1;
    if (a.startsWith('people/r')) return 1;
    return -1;
  });

  const refine_people: Partial<Person>[] = [];
  for (const id of dialog.context.person_info.ids) {
    refine_people.push(new Person({ id, displayName: dialog.context.person_info.name_ids[id].displayName, type: EntityType.Person }));
  }
  
  if (dialog.context.person_info.search_term) {
    const keywords = dialog.context.person_info.search_term.toLowerCase().split(' ');
    refine_people.sort((a,b) => {
      const a_names = a.displayName ? a.displayName.toLowerCase().split(' ') : [];
      const b_names = b.displayName ? b.displayName.toLowerCase().split(' ') : [];
      return arraysIntersectCount(b_names, keywords) - arraysIntersectCount(a_names, keywords);
    });
  }

  // clear the ping
  // if (dialog.message.ping) delete dialog.message.ping;

  if (act_people.length || act_orgs.length) return refinePerson(dialog, refine_people, search_cmd);

  if (curr_dialog && curr_dialog.next_topic) dialog.setTopic(curr_dialog.next_topic);
  dialog.appendReplies(refine_people, Topics.PI_FOUND_PERSON);
  if (isActive(dialog)) dialog.clearContext('person_info');
  else dialog.resetProcessing();
  return dialog.currentPlugin().runAction(dialog);
}

function refinePerson(dialog: Dialog, people = null, search_cmd = false) {
  if (dialog.message.ping > 0) return;

  // we're already in a find person thread, use this as a refinement
  const curr_dialog = dialog.currentDialog();
  const act_people = dialog.actionValues(ActionType.ENTITY);
  const bool = dialog.actionValues(ActionType.BOOL);
  dialog.clearActions();

  if (people) {
    dialog.setTopic(Topics.PI_REFINE_PERSON);
    dialog.context.person_info.all_people = people.slice();
    dialog.context.person_info.refine_people = people.slice();
    if (!search_cmd) return false; // go to first prompt
  } else if (!dialog.context.person_info.refine_people) return false;

  const all_people = dialog.context.person_info.all_people;
  const refine_people = dialog.context.person_info.refine_people;
  let reply_people = [];
  let name = null;
  let id = null;

  if (act_people && act_people.length) {
    if (act_people[0].people && act_people[0].people.length) id = act_people[0].people[0].id;
    if (!id && act_people[0].name) name = act_people[0].name.toLowerCase();
  }

  if (!id && !name && dialog.context.person_info.search_term) name = dialog.context.person_info.search_term.toLowerCase();

  if (bool.length) {
    // yes/no confirm this is the right person
    if (bool[0]) reply_people.push(refine_people[0]);
    else {
      refine_people.shift();
      if (refine_people.length > 0) {
        dialog.clearDialog(true);
        return false; // prompt again
      }
    }
  } else if (id && all_people) {
    const person = all_people.find(p => p.id === id);
    if (person) reply_people.push(person);
  } else if (name) {
    const people = refine_people.filter(p => 
      p.displayName.toLowerCase().includes(name) || p.names.map(n => n.toLowerCase()).includes(name));
    if (people) reply_people = reply_people.concat(people);
  }

  dialog.context.person_info.refine_people = null; // dialog.clearContext('refine_people');
  if (curr_dialog && curr_dialog.next_topic) dialog.setTopic(curr_dialog.next_topic);
  dialog.appendReplies(reply_people, Topics.PI_FOUND_PERSON);
  dialog.clearContext('person_info');
  return dialog.currentPlugin().runAction(dialog);
}

function runAction(dialog: Dialog) {
  if (!dialog.context.person_info) dialog.context.person_info = new PersonInfoPluginState();
  if (!dialog.context.person_info.search_term) dialog.context.person_info.search_term = dialog.message.message;

  switch (dialog.topic) {
    case Topics.PERSON_INFO:
      return personInfo(dialog);
    case Topics.PI_FIND_PERSON:
      return findPerson(dialog);
    case Topics.PI_REFINE_PERSON:
      return refinePerson(dialog);
    case Topics.PI_SKILL_SEARCH:
      return skillSearch(dialog);
  }

  return false;
}

function setPrompt(dialog) {
  const curr_dialog = dialog.currentDialog();

  switch (dialog.topic) {
    case Topics.PI_REFINE_PERSON:
      if (curr_dialog && dialog.context.person_info.refine_people && dialog.context.person_info.refine_people.length) {
        if (!curr_dialog.prompt.length) {
          dialog.addPrompt(promptInfo(lang.person_info.REFINE(dialog.context.person_info.refine_people[0]), null, null, [dialog.getPersonInfo(dialog.context.person_info.refine_people[0])] ));
          dialog.addAnswers(lang.person_info.REFIND_ANSWERS);
          dialog.addHint(lang.person_info.REFINE_HINT);
        }
      } else {
        if (!dialog.isAuthenticated()) {
          const providers = AuthProvider.clientAuthNameSet(null, dialog.group_host);
          dialog.addPrompt(lang.person_info.NOT_FOUND_REG_REQUIRED(providers.map(p => p.name)), true);
          dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
        } else if (dialog.user.refreshing) dialog.addPrompt(lang.person_info.NOT_FOUND_LEARNING, true);
        else dialog.lastPrompt('person_info', lang.person_info.NOT_FOUND, {clear:true});
        dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.PERSON_INFO:
      if (!dialog.context.person_info || dialog.context.person_info.people_found) {
        const found_people: Partial<Person>[] = dialog.getRepliesType(EntityType.Person);
        dialog.newDialog({ next_topic: curr_dialog ? curr_dialog.next_topic : null });
        dialog.clearPing();
        const select = curr_dialog && curr_dialog.next_topic && 
            curr_dialog.next_topic !== Topics.PERSON_INFO &&
            curr_dialog.next_topic !== Topics.PI_FOUND_PERSON &&
            curr_dialog.next_topic !== Topics.DEFAULT;
        for (const index in found_people) {
          const person = found_people[index];
          const info = dialog.getPersonInfo(person);
          info.select = select;
          dialog.addInfo(info);
        }

        if (select) dialog.noPing();

        if (found_people.length === 0) {
          if (!dialog.isAuthenticated()) {
            const providers = AuthProvider.clientAuthNameSet(null, dialog.group_host);
            dialog.addPrompt(lang.person_info.NOT_FOUND_REG_REQUIRED(providers.map(p => p.name)), true);
            dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
          } else if (dialog.user.refreshing) dialog.addPrompt(lang.person_info.NOT_FOUND_LEARNING, true);
          else dialog.lastPrompt('person_info', lang.person_info.NOT_FOUND, {clear:true});
        } else if(found_people.length === 1) {
          let prompt = promptInfo(lang.person_info.REFINE(found_people[0]), null, null, [dialog.getPersonInfo(found_people[0])]);
          switch(dialog.context.person_info.search_type) {
            case PersonInfoSearchType.Name:
              prompt = promptInfo(lang.person_info.REFINE(found_people[0]), null, null, [dialog.getPersonInfo(found_people[0])]);
              break;
            case PersonInfoSearchType.Network:
              prompt = promptInfo(lang.person_info.REFINE_NETWORK(found_people[0]), null, null, [dialog.getPersonInfo(found_people[0])]);
              break;
            case PersonInfoSearchType.Event:
              prompt = promptInfo(lang.person_info.REFINE_EVENT(found_people[0]), null, null, [dialog.getPersonInfo(found_people[0])]);
              break;
            case PersonInfoSearchType.Skill:
              prompt = promptInfo(lang.person_info.REFINE_SKILL(found_people[0]), null, null, [dialog.getPersonInfo(found_people[0])]);
              break;
            default:
              break;
          }
          dialog.lastPrompt('person_info', prompt);
        } else if (found_people.length > 1) {
          let prompt = lang.person_info.FOUND;
          switch(dialog.context.person_info.search_type) {
            case PersonInfoSearchType.Name:
              prompt = lang.person_info.FOUND;
              break;
            case PersonInfoSearchType.Network:
              prompt = lang.person_info.FOUND_NETWORK;
              break;
            case PersonInfoSearchType.Event:
              prompt = lang.person_info.FOUND_EVENT;
              break;
            case PersonInfoSearchType.Skill:
              prompt = lang.person_info.FOUND_SKILL;
              break;
          }

          dialog.lastPrompt('person_info', prompt);
        }

        if (select) {
          dialog.setTopic(Topics.PI_REFINE_PERSON);
          dialog.clearPing();
        }      
      } else if(!dialog.message.ping)  dialog.newDialog({ next_topic: curr_dialog ? curr_dialog.next_topic : null });
      break;
  }
}

export default {
  name: PERSON_NAME,
  description: PERSON_DESC,
  examples: lang.person_info.PERSON_EG,
  reserved: lang.person_info.PERSON_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: () => {
    return null;
  },
  findPersonPrompt,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
