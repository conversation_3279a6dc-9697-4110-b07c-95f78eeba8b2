import lang from '../lang';
import Dialog, { TOPIC, Topics } from '../session/dialog';
import { Plugin } from '../types/plugins';
import parsers from '../utils/parsers';

const PRIVACY_NAME = 'Privacy';
const PRIVACY_DESC = 'View the AskFora privacy policy';
TOPIC('PRIVACY');

function keywordMatch(actions, message, raw_message) {
  if (parsers.checkKeyword(message, lang.privacy.PRIVACY_RESERVED.concat(lang.privacy.PRIVACY_KWDS), true)) return true;
  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  if (!(dialog.isAuthenticated() && dialog.isMaskedTopic()) && !dialog.message.ping) {
    dialog.setTopic(Topics.PRIVACY);
    return true;
  }
  return false;
}

function setPrompt(dialog) {
  dialog.addPrompt(lang.privacy.PRIVACY_POLICY, true);
  dialog.setTopic(Topics.DEFAULT);
}

export default {
  name: PRIVACY_NAME,
  description: PRIVACY_DESC,
  examples: [],
  reserved: lang.privacy.PRIVACY_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive: () => { return false; },
  runAction: (d) => { },
  setPrompt,
  shortcuts: lang.privacy.PRIVACY_SHORTCUTS,
} as Plugin;

Dialog.registerPlugin(Topics.PRIVACY, module.exports.default);
