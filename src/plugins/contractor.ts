import _ from 'lodash';
import { AuthProvider } from '../auth/auth_provider';
import config from '../config';
import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Person, Tag } from '../types/items';
import { ContractorPluginState, Plugin } from '../types/plugins';
import { AuthContext, TagType } from '../types/shared';

import * as funcs from '../utils/funcs';
import parsers from '../utils/parsers';

const CONTRACTOR_NAME = 'Contractor';
const CONTRACTOR_DESC = 'Register new contractor';
const LOG_NAME = 'plugins.contractor';

const topics = [
  TOPIC('CONTRACTOR_REGISTER'),
  TOPIC('CONTRACTOR_PROFILE'),
  TOPIC('CONTRACTOR_START'),
  TOPIC('CONTRACTOR_BIO'),
  TOPIC('CONTRACTOR_WELCOME'),
  TOPIC('REGISTER_CONTRACTOR'),
  TOPIC('CONTRACTOR_AUTH_CANCELLED'),
];

function setAction(dialog: Dialog, message, raw_message) {
  if (dialog.checkTopic(Topics.CONTRACTOR_BIO)) {
    if (!dialog.message.ping) dialog.addAction(ActionType.RAW, raw_message);
  } else if (parsers.checkKeyword(message, lang.contractor.DONE)) dialog.setTopic(Topics.CONTRACTOR_PROFILE);
  else if(parsers.checkKeyword(message, lang.contractor.BIO_KWD)) dialog.setTopic(Topics.CONTRACTOR_BIO);
  return isActive(dialog);
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function keywordMatch(action, message, raw_message) {
  return parsers.checkKeyword(message, lang.contractor.BIO_KWD, true);
}

function _contractorProfile(dialog: Dialog) {
  const start = new Date();
  const group = dialog.getGroup();
  const host = group && group.host? group.host : config.get('DEFAULT_HOST', 'askfora.com');
  const urls = [`https://${host}`]
  const temp_person = new Person({
    tags: dialog.context.contractor.skills.map((value, i) => new Tag(TagType.skill, value.toLowerCase(), i + 200, start, 100)),
    urls,
  })

  dialog.runAsync('contractor', async () => {
    const contractor_profile = await dialog.people.temp(temp_person);
    if (dialog.context.contractor) dialog.context.contractor.profile = contractor_profile;
  });

  dialog.setTopic(Topics.REGISTER_CONTRACTOR);
}

function _contractorBio(dialog: Dialog) {
  if(!dialog.context.contractor.saving) {
    const [bio] = dialog.actionValues(ActionType.RAW);
    if (bio && bio.length) {
      dialog.context.contractor.profile = null;
      const new_me = new Person(dialog.me);
      if (new_me.bio && new_me.bio.length) new_me.bio = `${new_me.bio}\n${bio}`;
      else new_me.bio = bio;

      dialog.context.contractor.saving = true;
      dialog.runAsync('contract', async () => {
        dialog.context.contractor.profile = await dialog.setSelf(new_me);
      });
    } else {
      // dialog.clearPing();
      return;
    }
  }

  if (dialog.context.contractor.profile) {
    // dialog.clearPing();
    dialog.setTopic(Topics.CONTRACTOR_WELCOME);
  } // else dialog.quickPing();
}

function runAction(dialog: Dialog) {
  if (!dialog.context.contractor) {
    dialog.context.contractor = { skills: [], skill_prompt: 0 } as ContractorPluginState;
  }

  switch(dialog.topic) {
    case Topics.CONTRACTOR_PROFILE: return _contractorProfile(dialog);
    case Topics.CONTRACTOR_START: 
      if (dialog.me && dialog.me.bio && dialog.me.bio.length) dialog.setTopic(Topics.CONTRACTOR_WELCOME);
      // else dialog.clearPing();
      break;
    case Topics.CONTRACTOR_BIO: return _contractorBio(dialog);
    case Topics.CONTRACTOR_WELCOME: break;
    case Topics.REGISTER_CONTRACTOR: 
      // dialog.clearPing();
      break;
    case Topics.CONTRACTOR_AUTH_CANCELLED:
      break;
    case Topics.CONTRACTOR_REGISTER:
    default: 
      if (!dialog.isAuthenticated()) dialog.createGuest();
      if(dialog.context.contractor.profile) {
        if (dialog.context.contractor.skills) {
          if (!dialog.context.contractor.profile.tags) dialog.context.contractor.profile.tags = [];
          let index = 200;
          const start = new Date();
          for (const value of dialog.context.contractor.skills) {
            funcs.saveOneTypeValue(dialog.context.contractor.profile.tags, new Tag(TagType.skill, value.toLowerCase(), index, start, 100));
            index++;
          }
        }
        dialog.setTopic(Topics.REGISTER_CONTRACTOR); 
      }
      else if(!parsers.checkKeyword(dialog.message.message.toLowerCase(), lang.project.SUGGEST_SKILLS_ELSE, true)){
        const skills = dialog.message.message.split(',').map(s => s.trim()); //dialog.actionValues(ActionType.SKILL);

        dialog.context.contractor.skills = _.uniq<string>([
          ...dialog.context.contractor.skills,
          ...skills,
        ]);
      }
  }
}

function setPrompt(dialog: Dialog) {
  if (dialog.message.ping) return;

  switch (dialog.topic) {
    case Topics.CONTRACTOR_START:
    case Topics.CONTRACTOR_BIO:
      if (!dialog.isProcessing(false)) { //()) {
        dialog.addPrompt(lang.contractor.BIO_PROMPT(dialog.me.nickName), true);
        dialog.addInfo(dialog.getPersonInfo());
        // dialog.noPing();
        dialog.setTopic(Topics.CONTRACTOR_BIO);
      }
      break;
    case Topics.CONTRACTOR_WELCOME:
      dialog.addPrompt(lang.contractor.WELCOME(dialog.me.nickName), true);
      dialog.addPrompt(lang.contractor.SHARE);
      dialog.addInfo(dialog.getPersonInfo());
      dialog.reset('contractor');
      break;
    case Topics.REGISTER_CONTRACTOR: 
      if(dialog.context.contractor) {
        if(dialog.context.contractor.profile) {
          const providers = AuthProvider.clientAuthSet(AuthContext.Contractor, dialog.group_host);
          const names = providers.map(p => p.name);
          dialog.addPrompt(lang.contractor.LOGIN, true);
          dialog.addPrompt(lang.init.REGISTER_PICK(names));
          dialog.addAnswers(names);
          for (const provider of providers) dialog.addQuickReply(provider.name, [], {redirect: provider.url});
          dialog.addInfo(dialog.getPersonInfo(dialog.context.contractor.profile));
          // dialog.clearPing();
          dialog.setClear();
          dialog.setSticky();
          return;
        } // else dialog.quickPing();
      } else dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.CONTRACTOR_AUTH_CANCELLED:
      dialog.addPrompt(lang.contractor.AUTH_CANCELLED(AuthProvider.clientAuthInfo(AuthContext.Project, dialog.context.init.provider, dialog.group_host)), true);
      break;
    /* case Topics.CONTRACTOR_REGISTER:
      if (!dialog.context.contractor || !dialog.context.contractor.skills || !dialog.context.contractor.skills.length) {
        dialog.addPrompt(lang.contractor.INTRO, true);
        dialog.addAnswers(lang.project.SUGGEST_SKILLS);
        break;
      } // else don't break
      */
    default: 
      if (!dialog.isAuthenticated() || dialog.isGuestAccount()) {
        dialog.addPrompt(lang.contractor.ADD_SKILLS[dialog.context.contractor.skill_prompt], true);
        dialog.addAnswers(lang.contractor.ANSWERS);
        dialog.context.contractor.skill_prompt = (dialog.context.contractor.skill_prompt + 1) % lang.contractor.ADD_SKILLS.length;
        // dialog.noPing();
      }
  }
}

export default {
  name: CONTRACTOR_NAME,
  description: CONTRACTOR_DESC,
  examples: [],
  reserved: [],
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: lang.contractor.CONTRACTOR_SHORTCUT,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);