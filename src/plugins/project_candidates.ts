import _ from 'lodash';

import { ActionType, TemplateType } from '../types/globals';
import { Contract, Person, Project, projectPerson } from '../types/items';
import { ContractPluginState, Plugin, ProjectPluginState } from '../types/plugins';
import { AuthLevel, CandidateInfo, EntityType, NotificationType, ProjectCandidateState, ProjectInfo, ProjectRate, PushSetting, Uid, UndefinedPersonInfo } from '../types/shared';

import { createRawMessage, draftFolderLink, localeDowMonthDay, messageLink } from '../utils/format';
import { arraysIntersect, arraysIntersectCount, flatten } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import { AuthProvider } from '../auth/auth_provider';
import config from '../config';
import data from '../data';
import lang from '../lang';
import { SERVICE_FEE } from '../routes/gopay';
import Dialog, { TOPIC, Topics } from '../session/dialog';
import ForaUser from '../session/user';

import connect from './connect';
import personInfo from './person_info';

const DEBUG = (require('debug') as any)('fora:plugins:project_candidate');
const LOG_NAME = 'plugins.project_candidate';
const PROJECT_CANDIDATE_NAME = 'Project Candidate';
const PROJECT_CANDIDATE_DESC = 'Update job candidates';

const PROJECT_CANDIDATE_RESERVED = [

].concat([
  lang.project.PROJECT_ADD,
  lang.project.PROJECT_SEARCH,
  lang.project.PROJECT_SELECT,
  lang.project.PROJECT_UNSELECT,
  lang.project.PROJECT_SEND,
  lang.project.PROJECT_DRAFT
]);

const topics = [
  TOPIC('PROJECT_ADD'), // add a candidate
  TOPIC('PROJECT_CANDIDATE_NO_EMAIL'), // add a candidate
  TOPIC('PROJECT_CANDIDATE_NO_CANDIDATE'), // add a candidate
  TOPIC('PROJECT_SELECT'), // select a candidate
  TOPIC('PROJECT_MATCHING'), // Do the matching logic
  TOPIC('PROJECT_MATCHING_NO_MATCH'), // Handle no candidates found
  TOPIC('CONFIRM_SELECT_CONTRACTOR'), // select workflow
  TOPIC('CONTRACTOR_SELECT_ERROR'), // select error
  TOPIC('PROJECT_CONTRACTOR_SELECTED'), //  send back an updated project
  TOPIC('PROJECT_CONTRACTOR_UNSELECT'), // clear selected contractor
  TOPIC('PROJECT_CANDIDATE_REJECT'), // clear selected contractor
  TOPIC('PROJECT_UNSELECT_ERROR'), // clear selected contractor
  TOPIC('PROJECT_CANDIDATE_LOAD'), // ping while loading
  TOPIC('PROJECT_CONFIRM_SEND'), // confirm if to send the candidate  link
  TOPIC('PROJECT_SEND_LATER'), // confirm if to send the candidate  link
  TOPIC('PROJECT_CONFIRM_SEND_PROPOSAL'), // confirm if to send the client link
  TOPIC('PROJECT_PROPOSAL_REPLIED'), // client already replied
  TOPIC('PROJECT_SEND_INVITE'), // send the candidate  link
  TOPIC('PROJECT_DRAFT_INVITE'), // draft mail for user to send to candidate
  TOPIC('DRAFT_INVITE'), // give user the draft invite
  TOPIC('PROJECT_TEST_INVITE'), // for testing, auto accept invites
  TOPIC('INVITE_ERROR'), // for testing, auto accept invites
];

export default {
  name: PROJECT_CANDIDATE_NAME,
  description: PROJECT_CANDIDATE_DESC,
  examples: [],
  reserved: PROJECT_CANDIDATE_RESERVED,
  requiresAuth: false,
  keywordMatch,
  isActive,
  runAction,
  setAction,
  setPrompt,
  // shortcuts: () => { return null; },
  shortcuts: lang.project.EXPERT_SHORTCUT,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);

function keywordMatch(actions, message): boolean {
  if (parsers.checkKeyword(message, [
    lang.project.PROJECT_SEARCH,
    lang.project.PROJECT_ADD,
    lang.project.PROJECT_SELECT,
    lang.project.PROJECT_UNSELECT,
    lang.project.PROJECT_SEND,
    lang.project.PROJECT_DRAFT,
    lang.project.PROJECT_SHARE_PROPOSAL,
    lang.project.PROJECT_SHARE_QUESTION,
    lang.project.PROJECT_REJECT,
  ], true)) {
    return true;
  }

  return false;
}

function isActive(dialog: Dialog): boolean {
  return dialog.checkTopic(topics);
}

function setAction(dialog: Dialog, message, _raw_message): boolean {
  if (!isActive(dialog) && dialog.isMaskedTopic()) return false;

  if (dialog.checkTopic([Topics.PROJECT_CONFIRM_SEND, Topics.PROJECT_CONFIRM_SEND_PROPOSAL])) {
    const bool = dialog.actionValues(ActionType.BOOL);
    if (!bool || !bool.length) {
      if (parsers.checkKeyword(message, lang.project.CANDIDATES_SEND, true)) dialog.addAction(ActionType.BOOL, true);
    }
    return true;
  }

  // search for candidates
  if (parsers.checkKeyword(message, lang.project.PROJECT_SEARCH, true) &&
    dialog.actionValues(ActionType.PROJECT).length &&
    dialog.isAuthenticated() && !dialog.isGuestAccount()) {
    dialog.setTopic(Topics.PROJECT_MATCHING);
    return true;
  }

  // add a candidate
  if ((parsers.checkKeyword(message, lang.project.PROJECT_ADD, true) || dialog.checkTopic(Topics.PROJECT_ADD)) &&
    dialog.actionValues(ActionType.PROJECT).length &&
    (dialog.actionValues(ActionType.ENTITY).length || dialog.actionValues(ActionType.PERSON).length || dialog.actionValues(ActionType.COMMS).length)) {
    dialog.setTopic(Topics.PROJECT_ADD);
    return true;
  }

  // send an invite
  if (parsers.checkKeyword(message, lang.project.PROJECT_SEND, true)) {
    if (dialog.message.data) dialog.addAction(ActionType.RAW, dialog.message.data);
    if (config.get('TEST_AUTO_ACCEPT') && (dialog.isAuthenticated(AuthLevel.Demo) || config.isEnvDevelopment())) dialog.setTopic(Topics.PROJECT_TEST_INVITE);
    else dialog.setTopic(Topics.PROJECT_SEND_INVITE);
  }

  if (parsers.checkKeyword(message, lang.project.PROJECT_SHARE_PROPOSAL, true)) {
    dialog.addAction(ActionType.BOOL, true);
    dialog.setTopic(Topics.PROJECT_CONFIRM_SEND_PROPOSAL);
  }

  if (parsers.checkKeyword(message, lang.project.PROJECT_SHARE_QUESTION, true)) {
    dialog.addAction(ActionType.BOOL, true);
    dialog.setTopic(Topics.PROJECT_SEND_INVITE);
  }

  // draft an invite for user to send
  if (parsers.checkKeyword(message, lang.project.PROJECT_DRAFT, true)) {
    if (dialog.message.data) dialog.addAction(ActionType.RAW, dialog.message.data);
    if (config.get('TEST_AUTO_ACCEPT') && (dialog.isAuthenticated(AuthLevel.Demo) || config.isEnvDevelopment())) dialog.setTopic(Topics.PROJECT_TEST_INVITE);
    else dialog.setTopic(Topics.PROJECT_DRAFT_INVITE);
  }

  // select a candidate
  if (parsers.checkKeyword(message, lang.project.PROJECT_SELECT, true) &&
    dialog.actionValues(ActionType.PROJECT).length &&
    (dialog.actionValues(ActionType.ENTITY).length || dialog.actionValues(ActionType.PERSON).length)) {
    dialog.setTopic(Topics.PROJECT_SELECT);
    return true;
  }

  // unselect a candidate
  if (parsers.checkKeyword(message, lang.project.PROJECT_UNSELECT, true) &&
    dialog.actionValues(ActionType.PROJECT).length) {
    dialog.setTopic(Topics.PROJECT_CONTRACTOR_UNSELECT);
    return true;
  }

  if (parsers.checkKeyword(message, lang.project.PROJECT_REJECT, true) &&
    dialog.actionValues(ActionType.PROJECT).length) {
    dialog.setTopic(Topics.PROJECT_CANDIDATE_REJECT);
    return true;
  }

  return isActive(dialog);
}

// every function gets the project and people arguments loaded into
// dialog.context.project.project and dialog.context.project.people
function _loadProjectCandidate(dialog: Dialog): boolean {
  if (!dialog.context.project.saved_topic) {
    dialog.context.project.select = dialog.checkTopic(Topics.PROJECT_ADD);
    dialog.context.project.saved_topic = dialog.topic;
    dialog.setTopic(Topics.PROJECT_CANDIDATE_LOAD);
  }

  const project = dialog.context.project;

  const notes = dialog.actionValues(ActionType.RAW);
  if (notes && notes.length) dialog.context.project.draft_note = notes[0];

  let load_ids: Uid[] = [];
  let load_referrers: Uid[] = [];
  let load_emails: string[] = [];
  let update_people: Partial<CandidateInfo>[] = [];

  const req_people: Partial<CandidateInfo>[] = dialog.actionValues(ActionType.PERSON);
  if (req_people && req_people.length) {
    for (const req_person of req_people) {
      if (req_person.id) {
        load_ids.push(req_person.id);
        update_people.push(req_person);
      }
    }

    load_referrers = load_referrers.concat(
        flatten(req_people.filter(p => p.refer_by && p.refer_by.length).map(p => p.refer_by.map(r => r.vanity ? `profile/${r.vanity}` : r.id)))
      );
  } else {
    const req_candidates: Partial<UndefinedPersonInfo>[] = dialog.actionValues(ActionType.ENTITY);
    if (req_candidates && req_candidates.length) {
      project.person_name = [];
      for (const req_candidate of req_candidates) {
        if (req_candidate.id === 'found_people') project.person_name.push(req_candidate.name);
        if (req_candidate.people) {
          const people = req_candidate.people.filter(p => p) as Partial<CandidateInfo>[];
          const id_people = people.filter(p => p.id);
          update_people = update_people.concat(id_people);
          load_ids = load_ids.concat(id_people.map(p => p.id));
          load_emails = load_emails.concat(
              flatten(people.filter(p => !p.id && p.comms)
            .map(p => p.comms.filter(c => c.type === 'mail' && c.link).map(c => c.link.split(':')[1])))
          );
          load_referrers = load_referrers.concat(
              flatten(people.filter(p => p.refer_by && p.refer_by.length).map(p => p.refer_by.map(r => r.vanity ? `profile/${r.vanity}` : r.id)))
          );
        }
      }
    }
  }

  const req_comms = dialog.actionValues(ActionType.COMMS);
  if (req_comms && req_comms.length) {
    const req_emails = parsers.findEmail(req_comms); 
    if (req_emails && req_emails.length) load_emails = load_emails.concat(req_emails);
  }

  // List of user ids that
  load_ids = load_ids.concat(dialog.getRepliesType(EntityType.Person).map(p => p.id).filter(id => id));

  // don't include the built in contacts
  // const have_ids = project.project && project.project.candidates ? project.project.candidates.map(c => c.id) : [];
  // load_ids = _.uniq(load_ids.filter(i => !i.includes('people/f') && !have_ids.includes(i) && dialog.me && i !== dialog.me.id));

  if (!project.project) {
    const projects: ProjectInfo[] = dialog.actionValues(ActionType.PROJECT);
    if (projects && projects.length && projects[0].type === EntityType.Project) {
      project.load_ids = _.uniq(load_ids);
      project.load_emails = load_emails;
      project.load_referrers = _.uniq(load_referrers);
      const r_project = dialog.projects.receiveProject(projects[0], null, update_people);
      if (!r_project || !r_project.project || !r_project.project.id) {
        logging.warnFP(LOG_NAME, '_loadProjectCandidate', dialog.user.profile, `Received no project for ${projects[0].id}`);
        dialog.setTopic(Topics.PROJECT_MISSING);
        return;
      } else {
        project.loading = true;
        dialog.runAsync('project', async () => {
          const p = await dialog.projects.get(r_project.project);
          if (dialog.context.project) {
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_loadProjectCandidate', dialog.user.profile, `project loaded ${projects[0].id}`);
            dialog.context.project.project = p;
            dialog.context.project.updated = dialog.context.project.updated || r_project.updated;
            dialog.context.project.loaded = true;
          } else logging.warnFP(LOG_NAME, '_loadProjectCandidate', dialog.user.profile, `project context missing while loading ${p.id}`);
        });
        return;
      }
    }

    if (!project.loading || project.loaded) {
      logging.warnFP(LOG_NAME, '_loadProjectCandidate', dialog.user.profile, `Lost project while loading`);
      dialog.setTopic(Topics.PROJECT_MISSING);
    }
    // else dialog.quickPing();
    return;
  }

  if (dialog.context.project.loaded && !project.loading_people) {
    dialog.resetProcessing();
    project.loading_people = true;
    project.people = project.load_ids.length > 0 || project.load_emails.length > 0 ? null : [];
    if (project.load_ids.length || project.load_emails.length) {
      const existing_people = project.project && project.project.candidates ? project.project.candidates.filter(c => (c.id && project.load_ids.includes(c.id)) || (c.comms && _.intersection(c.comms, project.load_emails).length > 0)) : [];
      const have_ids = existing_people.map(c => c.id).filter(i => i);
      const have_emails = parsers.findEmail(flatten(existing_people.filter(c => c.comms).map(c => c.comms)));
      project.load_ids = project.load_ids.filter(i => !have_ids.includes(i));
      project.load_emails = project.load_emails.filter(e => !have_emails.includes(e));
      if (project.load_ids.length || project.load_emails.length || project.load_referrers.length) {
        dialog.runAsync('project', async () => {
          const id_persons = project.load_ids.length ? await dialog.people.byId(project.load_ids) : [];
          const email_persons = project.load_emails.length ? await dialog.people.findByComms(project.load_emails) : [];
          const referrers = project.load_referrers.length ? await dialog.people.byId(project.load_referrers) : [];
          for (const referrer of referrers) {
            if (referrer.vanity) {
              const ru = await data.users.globalByVanity(referrer.vanity);
              if (ru) referrer.askfora_id = ru.profile;
            }
          }
          const persons = _.uniqBy(existing_people.concat([...id_persons, ...email_persons]), p => p.id).filter(p => !p.self);
          if (dialog.context.project) {
            if (persons) {
              dialog.context.project.people = persons.map(person => projectPerson(project.project, person));
              if (project.load_emails && project.load_emails.length) {
                dialog.context.project.people = dialog.context.project.people.filter(p => arraysIntersect(p.comms, project.load_emails));
              }
              dialog.context.project.people.sort((a, b) => {
                let a_cnt = arraysIntersectCount(a.names, project.person_name)
                let b_cnt = arraysIntersectCount(b.names, project.person_name)
                if (a_cnt > 0 && b_cnt > 0) return b_cnt - a_cnt;
                if (a_cnt > 0) return -1;
                return 1;
              });

              if (dialog.context.project.project.candidates) {
                for (const person of dialog.context.project.project.candidates) {
                  if (person.refer_by && person.refer_by.length) {
                    const refer_ids = flatten(person.refer_by.map(r => { return [r.id].concat(r.vanity ? [`profile/${r.vanity}`] : []) }));
                    const vanities = person.refer_by.map(r => r.vanity);
                    person.refer_by = referrers.filter(r => refer_ids.includes(r.id) || vanities.includes(r.vanity));
                  }
                }
              }
            } else dialog.context.project.people = existing_people;
          }
        });
        return;
      } else {
        project.loading_people = true;
        project.people = existing_people;
      }
    } else {
      project.loading_people = true;
      project.people = [];
    }
  }

  if (dialog.context.project.loaded && project.loading_people && project.people) {
    const curr_dialog = dialog.currentDialog();
    if (curr_dialog && curr_dialog.next_topic === Topics.FOUND_PERSON) {
      if (curr_dialog.replies.length) {
        dialog.context.project.people = dialog.context.project.people.filter(p => p.id === curr_dialog.replies[0].id);
      }
      else dialog.context.project.people = []
      dialog.clearDialog();
    }

    // select from multiple
    else if (dialog.context.project.people && dialog.context.project.select && dialog.context.project.people.length > 1) {
      dialog.clearActions(ActionType.ENTITY);
      dialog.addAction(ActionType.ENTITY, {
        name: dialog.context.project.person_name[0],
        type: EntityType.UnresolvedPerson,
        people: dialog.context.project.people.map(p => { return { id: p.id, name: p.displayName } })
      });
      personInfo.findPersonPrompt(dialog);
      return false;
    } else if (project.load_emails && project.load_emails.length) {
      dialog.context.project.people = [
        new Person({
          comms: project.load_emails,
          displayName: project.person_name.join(' '),
        })
      ];
    }

    if (dialog.topic === Topics.PROJECT_CANDIDATE_LOAD) {
      dialog.resetProcessing();
      dialog.setTopic(dialog.context.project.saved_topic);
    }
  }
}

function _addProjectCandidate(dialog: Dialog): boolean {
  const project = dialog.context.project;

  if (!_canInvite(dialog)) {
    dialog.setTopic(Topics.INVITE_ERROR);
    return;
  }

  if (!project || !project.people.length) {
    dialog.setTopic(Topics.PROJECT_CANDIDATE_NO_CANDIDATE);
    dialog.nextTopic(Topics.PROJECT_ADD);
    return false;
  }

  // check people are valid
  project.people = project.people.filter(person => {
    const email = person.comms ? parsers.findEmail(person.comms) : null;
    return email && email.length
  });

  if (!project || !project.people.length) {
    dialog.setTopic(Topics.PROJECT_CANDIDATE_NO_EMAIL);
    return false;
  }

  let add_people = project.people.filter(p => !p.id);

  if (add_people.length) {
    const people: Partial<Person>[] = dialog.getRepliesType(EntityType.Person);
    if (people && people.length) {
      const comms = flatten(people.map(p => p.comms.filter(c => c !== dialog.user.email)));
      add_people = add_people.filter(p => !arraysIntersect(p.comms, comms));
      project.people = project.people.filter(p => p.id).concat(people).concat(add_people);
    }

    if (add_people.length) return connect.addPerson(dialog, add_people[0]);
  }

  if (!dialog.context.project.saving) {
    project.loading_people = false;
    dialog.newDialog();
    _doSave(dialog, async p => {
      if (p && dialog.context.project) {
        dialog.context.project.project = p;
      }
    }, true);
  }

  return true;
}

function _projectSkillSearch(dialog: Dialog) {
  if (dialog.context.project.project.skills && dialog.context.project.project.skills.length &&
    !dialog.context.project.searching && !dialog.context.project.done_searching) {
    dialog.context.project.done_searching = false;
    dialog.context.project.searching = true;
    dialog.context.project.people = null;
    dialog.context.project.project.searched_skills = null;
    dialog.context.project.project.suggested = null;
    dialog.context.project.project.searching = true;
    dialog.context.project.project.last_update = new Date();
    dialog.context.project.project.last_activity = dialog.context.project.project.last_update;

    if (dialog.isAuthenticated() && !dialog.isGuestAccount()) {
      // For the skills that were asked for, call the skill service to find all the synonyms of the skills
      dialog.runAsync('project', async () => {
        let project = dialog.context.project.project;
        await dialog.projects.create(project);
        const people = await dialog.projects.searchCandidates(project).catch(err => {
          logging.errorFP(LOG_NAME, 'projectSkillSearch', dialog.user.profile, `Error searching for candidates for ${project.id}`, err);
          dialog.asyncError(err);
        });

        if (people) logging.infoFP(LOG_NAME, 'projectSkillSearch', dialog.user.profile, `Finished searching for candidates for ${project.id} found ${people.length}`);

        let old_candidate_ids = project.candidates ? project.candidates.slice().map(c => c.id) : [];

        if (!project.expert) {
          const connections = await dialog.people.findIntroductions(project.searched_skills ? project.searched_skills : project.skills.split(' '), 20);
          const candidate_comms = (project.candidates ? flatten(project.candidates.map(c => c.comms)) : []).concat(
            people ? flatten(people.map(c => c.comms)) : []);
          project.suggested = connections.filter(c => !arraysIntersect(candidate_comms, c.comms)).map(c => `profile/${c.vanity}`);
        }

        if (people) project = await dialog.projects.saveSearchCandidates(project, people);
        else {
          project.last_update = new Date();
          project.searching = false;
          if(project.suggested && project.suggested.length) await dialog.projects.update(project);
        }
        if (dialog.context.project) {
          dialog.context.project.project = project;
          dialog.context.project.done_searching = true;
          dialog.context.project.searching = false;
          dialog.context.project.loading_people = false;
          dialog.context.project.people = people ? people.filter(p => !old_candidate_ids.includes(p.id)).map(person => projectPerson(dialog.context.project.project, person)) : [];
        }
      });
      _sendSafeProject(dialog, dialog.context.project.project);
    } else {
      // Not authenticated or is a guest account
      dialog.context.project.done_searching = true;
      dialog.context.project.loading_people = true;
      dialog.context.project.searching = false;
      dialog.context.project.people = [];
      _sendSafeProject(dialog, dialog.context.project.project);
    }
  }

  if ((!dialog.context.project.project.skills || !dialog.context.project.project.skills.length) && dialog.checkTopic(Topics.PROJECT_MATCHING)) {
    dialog.setTopic(Topics.PROJECT_MATCHING_NO_MATCH);
    return _sendSafeProject(dialog, dialog.context.project.project);
  }

  if (dialog.checkTopic(Topics.PROJECT_MATCHING)) {
    if (dialog.context.project.people) {
      dialog.context.project.people = null;
      dialog.newDialog();
      _doSave(dialog);
    } else if (dialog.context.project.saved) _sendSafeProject(dialog, dialog.context.project.project);
  }
}

function _canInvite(dialog: Dialog): boolean {
  const project = dialog.context.project.project;
  return !project.progress && !project.completed && (!project.escrow || project.escrow.id === lang.project.SKIP_ESCROW.id); // (!project.contractor || !project.contractor.id);
}

function _testAutoAccept(dialog: Dialog) {
  const project = dialog.context.project.project;
  // const people = dialog.context.project.people;

  if (project.candidates) {
    for (const candidate of project.candidates) {
      switch (candidate.state) {
        case ProjectCandidateState.INVITED:
          candidate.state = ProjectCandidateState.SENT;

          // Testing/demo mode, automatically accept on behalf of candidates
          setTimeout(() => {
            candidate.state = ProjectCandidateState.ACCEPTED;

            // update project and let the client know
            dialog.runAsync('project', async () => {
              const p = await dialog.projects.update(project);
              if (!dialog.isAuthenticated(AuthLevel.Demo)) {
                const to_email = await dialog.projects.clientEmail(p);
                const email = {
                  rcpts: [{ Name: project.client.displayName, Email: to_email }],
                  subject: lang.project.ACCEPTED_SUBJECT(project, project.me_candidate),
                  message: lang.project.ACCEPTED_MESSAGE(project, project.me_candidate, dialog.projects.getUrl(project, to_email)),
                };
                await dialog.projects.notify(p, new ForaUser(project.client.askfora_id), candidate, email, {
                  group: dialog.getNotifyGroup(to_email),
                  notification: project.expert ? NotificationType.Project_Answer : NotificationType.Project_Accepted
                }).catch(e => dialog.asyncError(e));
              }
            });
          });
          break;
      }
    }
  }
}

// confirm if to invite all found candidates
function _confirmSend(dialog: Dialog) {
  const bool = dialog.actionValues(ActionType.BOOL);
  if (bool && bool.length) {
    if (bool[0]) {
      dialog.context.project.people = dialog.context.project.project.candidates.filter(c => [ProjectCandidateState.FOUND, ProjectCandidateState.INVITED].includes(c.state));
      dialog.setTopic(Topics.PROJECT_SEND_INVITE);
      return _sendInvite(dialog);
    } else dialog.setTopic(Topics.PROJECT_SEND_LATER);
  }
}

// send an invite to the candidate
function _sendInvite(dialog: Dialog) {
  const project = dialog.context.project.project;
  const people = dialog.context.project.people;

  if (dialog.context.project.saving) {
    if (dialog.context.project.saved) {
      if (!dialog.context.project.sending) {
        dialog.context.project.sending = true;
        const notification_type = project.expert ? NotificationType.Project_Expert : NotificationType.Project_Invite;
        let template;
        if (project.expert) template = null;
        else if(project.rate === ProjectRate.sourcing) template = TemplateType.Sourcing;
        else if (dialog.context.project.draft_note && dialog.context.project.draft_note.length) template = TemplateType.Custom
        else if(project.escrow) template = TemplateType.NoFee;
        else if(project.rate === ProjectRate.fixed) template = TemplateType.Fixed;
        else template = null;

        if (people && people.length) {
          dialog.runAsync('project', async () => {
            for (const person of people) {
              const user = person.askfora_id ? new ForaUser(person.askfora_id) : null;
              if (user) await data.users.init(user, false);
              const to_email = user && user.email ? [user.email] : parsers.findEmail(person.comms);
              if (to_email && to_email.length) {
                if (person.refer_by && person.refer_by.length) {
                  for (const referrer of person.refer_by) {
                    const refer_user = referrer.askfora_id ? new ForaUser(referrer.askfora_id) : null;
                    if (refer_user) await data.users.init(refer_user, false);

                    const referrals = await data.projects.findReferral(refer_user, project.id);
                    const referral = referrals ? referrals.find(referral => referral.referrer === referrer.askfora_id && referral.candidate && referral.candidate.id === person.id) : null;
                    if (referral) {
                      logging.infoFP(LOG_NAME, 'sendInvite', dialog.user.profile, `Skipping repeat referral from ${referrer.askfora_id} for ${person.id} project ${project.id} because of referral ${referral.id}`);
                      continue;
                    }

                    if (refer_user) await data.projects.saveReferral(refer_user, project.id, person);

                    const remail = refer_user && refer_user.email ? [refer_user.email] : parsers.findEmail(referrer.comms);
                    const email = {
                      rcpts: [{ Name: referrer.displayName, Email: remail[0] }],
                      ccs: [{ Name: dialog.me.displayName, Email: dialog.user.email }],
                      subject: project.expert ? lang.project.EXPERT_SUBJECT(project) : lang.project.INVITE_SUBJECT(project),
                      message: dialog.context.project.draft_note ?
                        dialog.context.project.draft_note.replace(/\(candidate\)/g, person.displayName) : null,
                    }

                    await dialog.projects.notify(project, refer_user, person, email, { group: dialog.getNotifyGroup(remail[0]), notification: notification_type, template, referrer }).catch(e => dialog.asyncError(e));
                  }
                } else if (!person.network || person.vanity || person.askfora_id) {
                  const email = {
                    rcpts: [{ Name: person.displayName, Email: to_email[0] }],
                    ccs: person.network ? [] : [{ Name: dialog.me.displayName, Email: dialog.user.email }],
                    subject: project.expert ? lang.project.EXPERT_SUBJECT(project) : lang.project.INVITE_SUBJECT(project),
                    message: dialog.context.project.draft_note,
                  }
                  await dialog.projects.notify(project, new ForaUser(person.askfora_id), person, email, { group: dialog.getNotifyGroup(to_email[0]), notification: notification_type, template }).catch(e => dialog.asyncError(e));
                } else logging.warnFP(LOG_NAME, 'sendInvite', dialog.user.profile, `Not sending invitation to ${person.id} for project ${project.id}`);
              }
            }
            dialog.context.project.sent = true;
          });
        } else dialog.context.project.sent = true;
      }

      //dialog.quickPing();
    } // else dialog.quickPing();
  } else {
    if (_canInvite(dialog)) {
      let invited = false;
      if (project.candidates) {
        for (const candidate of project.candidates) {
          for (const person of people) {
            if (person.id === candidate.id) {
              person.askfora_id = candidate.askfora_id;
              person.refer_by = candidate.refer_by;
              candidate.state = ProjectCandidateState.SENT;
              invited = true;
            }
          }
        }
      }

      if (invited) {
        dialog.context.project.project.client.reminder = null;
        dialog.context.project.project.last_update = new Date();
        dialog.context.project.project.last_activity = dialog.context.project.project.last_update;
      }
      _doSave(dialog);
    } else dialog.setTopic(Topics.INVITE_ERROR);
  }
}

function _sendProposal(dialog: Dialog) {
  const project = dialog.context.project.project;

  const person = dialog.context.project.project.client;
  const to_email = parsers.findEmail(person.comms);
  if (to_email && to_email.length) {
    const email_group = dialog.groupByEmail(to_email[0]);
    const auth_group = email_group && email_group.provider ? email_group : dialog.group_host;
    const providers = AuthProvider.clientAuthNameSet(null, auth_group);

    const email = {
      rcpts: [{ Name: person.displayName, Email: to_email[0] }],
      ccs: [{ Name: dialog.me.displayName, Email: dialog.user.email }],
      subject: lang.project.INVITE_PROPOSAL_SUBJECT(project),
      message: null,
    };

    const url = dialog.projects.getUrl(project, email.rcpts[0].Email);
    const start = localeDowMonthDay(project.start, dialog.user.locale, dialog.user.timeZone);

    project.viewed = true;
    project.last_update = new Date();
    project.last_activity = project.last_update;
    dialog.context.project.sending = true;
    email.message = lang.project.INVITE_PROPOSAL_MESSAGE(project, person, dialog.context.project.draft_note, start, providers.map(p => p.name), url);

    if (person.askfora_id) {
      dialog.projects.updateClient(project, true).then(() => {
        dialog.projects.notify(project, new ForaUser(person.askfora_id), dialog.me, email, { group: dialog.getNotifyGroup(email.rcpts[0].Email), notification: NotificationType.Project_Proposal }).catch(e => dialog.asyncError(e));
      }).catch(e => dialog.asyncError(e));
    } else {
      dialog.projects.notify(project, null, dialog.me, email, { group: dialog.getNotifyGroup(email.rcpts[0].Email), notification: NotificationType.Project_Proposal }).catch(e => dialog.asyncError(e));
    }
  }

  _sendSafeProject(dialog, project);
}

// draft invite for user to send
function _draftInvite(dialog: Dialog) {
  const project = dialog.context.project.project;
  const people = dialog.context.project.people;

  if (_canInvite(dialog)) {
    if (!dialog.context.project.drafting && !dialog.context.project.draft_url) {
      if (project.candidates) {
        let sent = false;
        for (const candidate of project.candidates) {
          for (const person of people) {
            if (person.id === candidate.id) {
              person.askfora_id = candidate.askfora_id;
              candidate.state = ProjectCandidateState.SENT;
              sent = true;
            }
          }
        }
        if (sent) {
          dialog.context.project.project.client.reminder = null;
          dialog.context.project.project.last_update = new Date();
          dialog.context.project.project.last_activity = dialog.context.project.project.last_update;
        }
      }
      // if the user has email permissions and there's one recipient
      //   set a flag drafting, send the project back but loop until we have a url
      // if there are many recipients (still with email perms) create them all but just return a link to the drafts folder
      // without email permissions, if there's one recipient
      //   create a mailto: link for the one recipient
      // without email perms and many recipients, create for just the first recipient
      for (const person of people) {
        const to_email = parsers.findEmail(person.comms);
        if (to_email && to_email.length) {
          const email_group = dialog.groupByEmail(to_email[0]);
          const auth_group = email_group && email_group.provider ? email_group : dialog.group_host;
          const providers = AuthProvider.clientAuthNameSet(null, auth_group);

          const email = {
            rcpts: [{ Name: person.displayName, Email: to_email[0] }],
            ccs: [{ Name: dialog.me.displayName, Email: dialog.user.email }],
            subject: lang.project.INVITE_SUBJECT(project),
            message: null,
          };

          const url = dialog.projects.getUrl(project, email.rcpts[0].Email);
          const start = localeDowMonthDay(project.start, dialog.user.locale, dialog.user.timeZone);

          email.message = lang.project.DRAFT_MESSAGE(project, person, dialog.context.project.draft_note, start, providers.map(p => p.name), url);
          if (dialog.isAuthenticated(AuthLevel.Email)) {
            dialog.context.project.drafting = true;
            dialog.runAsync('project', async () => {
              const draft = await dialog.messages.create(dialog.me, person, email.subject, email.message, [lang.about.ABOUT_INFO[0]]);
              if (dialog.context.project && people.length === 1) dialog.context.project.draft_url = draft.link;
              else dialog.context.project.draft_url = draftFolderLink(dialog.user.provider, dialog.user.email);
              dialog.context.project.drafting = false;
            });
          } else {
            if (people.length === 1) {
              const draft = createRawMessage([person], dialog.me, dialog.me.email, email.subject, email.message, [lang.about.ABOUT_INFO[0]]);
              dialog.context.project.draft_url = messageLink(draft.raw);
            } else {
              const draft = createRawMessage([], dialog.me, dialog.me.email, email.subject, email.message, [lang.about.ABOUT_INFO[0]]);
              dialog.context.project.draft_url = messageLink(draft.raw);
              break;
            }
          }
        }
      }
    }

    if (dialog.context.project.draft_url) {
      _doSave(dialog);
      return;
    }
    else if (dialog.context.project.drafting) {
      // dialog.quickPing();
      return;
    }
  }

  dialog.setTopic(Topics.INVITE_ERROR);
}

// client selects a candidate for the project
function _selectCandidate(dialog) {
  const project = dialog.context.project.project;
  const candidate = dialog.context.project.people[0];

  if (!project.contractor) {
    if (candidate && project) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'selectCandidate', dialog.user.profile, `Selecting ${candidate.id} for ${project.id}`);
      dialog.setTopic(Topics.CONFIRM_SELECT_CONTRACTOR);
      dialog.context.project.contractor = candidate;
      candidate.state = ProjectCandidateState.SELECTED;
      project.last_update = new Date();
      project.last_activity = project.last_update;
    } else {
      project.last_update = new Date();
      project.last_activity = project.last_update;
      dialog.setTopic(Topics.CONTRACTOR_SELECT_ERROR);
      _sendSafeProject(dialog, project);
    }
    dialog.clearPing();
    return;
  } else if (project.contractor.askfora_id !== candidate.askfora_id) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'selectCandidate', dialog.user.profile, `Not selecting ${candidate.id} for ${project.id} because ${project.contractor.id} is already selected`);
    // reset the project candidate state
    for (const pc of project.candidates) {
      if (pc.id === candidate.id) {
        pc.state = ProjectCandidateState.ACCEPTED;
        break;
      }
    }
    project.last_update = new Date();
    project.last_activity = project.last_update;
    dialog.setTopic(Topics.CONTRACTOR_SELECT_ERROR);
    _sendSafeProject(dialog, project);
    _doSave(dialog);
  }
}

// Client confirms contractor
function _selectContractor(dialog: Dialog) {
  if (!dialog.context.project.project || !dialog.context.project.contractor) {
    logging.warnFP(LOG_NAME, '_selectContractor', dialog.user.profile, `No project${dialog.context.project?.contractor ? '' : ' contractor'}`);
    dialog.setTopic(Topics.PROJECT_MISSING);
    return;
  }

  const project = dialog.context.project.project;
  let save_contractor = null;
  let save_candidate = null;

  const bool = dialog.actionValues(ActionType.BOOL);
  if (bool && bool.length) {
    if (bool[0]) {
      const candidate = project.candidates.find(c => c.id === dialog.context.project.contractor.id);
      if (candidate) {
        candidate.state = ProjectCandidateState.SELECTED;
        project.contractor = projectPerson(project, candidate);
        save_candidate = candidate;
        save_contractor = project.contractor;
      }
      project.contract = null;
    } else {
      // unselect the candidate
      project.contractor = null;
      project.contract = null;
      const candidate = project.candidates.find(c => c.state === ProjectCandidateState.SELECTED);
      if (candidate) candidate.state = ProjectCandidateState.ACCEPTED;
    }
  }

  // whether to save the contractor
  let do_save = false;
  if (save_contractor && save_contractor.network) {
    save_candidate.network = false;
    save_contractor.network = false;
    do_save = true;
  }

  // look for a contract
  let do_notify = false;
  if (project.contractor && !project.contract) {
    do_notify = true;

    if (!dialog.context.project.contracting) {
      dialog.context.project.contracting = true;
      dialog.runAsync('project', async () => {
         // update contractors who were out of network to in-network
        if (do_save) {
          logging.warnFP(LOG_NAME, 'selectContractor', dialog.user.profile, `Saving contractor`);
          await dialog.people.save(new Person(save_contractor));
        }

        let contract: Partial<Contract> = await dialog.projects.setupContract(project);

        // handle skip contract
        if (contract) {
          if (!contract.id) {
            dialog.context.project.new_contract = await dialog.contracts.create(contract);
            contract = dialog.context.project.new_contract;
          } else if (!contract.client_reviewed) dialog.context.project.new_contract = contract;

          if (dialog.context.project && dialog.context.project.project) dialog.context.project.project.contract = contract.id;
        } else {
          dialog.clearContext('project')
          dialog.setTopic(Topics.CONTRACT_MISSING);
        }
      });
    }
  }

  if (project.contractor && !project.contract) {
    // dialog.quickPing();
    return;
  } else {
    // dialog.clearPing();
    do_notify = dialog.context.project && dialog.context.project.project.contractor && !dialog.context.project.new_contract;
  }

  if (do_notify) {
    if ( 
      ((!project.contractor.ready && !dialog.context.project.new_contract) ||
      (dialog.context.project.new_contract && dialog.context.project.new_contract.client_reviewed && 
        !dialog.context.project.new_contract.contractor_signed))
        || (!project.escrow && project.proposal)) {
      project.select_notice = true;
      project.last_update = new Date();
      project.last_activity = project.last_update;
    }
  }

  dialog.setTopic(Topics.PROJECT_CONTRACTOR_SELECTED);

  // go save then notify
  _doSave(dialog, async () => {
    if (do_notify) dialog.projects.notifySelected(project, dialog.context.project.new_contract).catch(e => dialog.asyncError(e));
    if (dialog.context.project) dialog.context.project.saved_people = true;
  }, true);
}

function _unselectContractor(dialog: Dialog) {
  if (dialog.context.project.saving) {
    // dialog.quickPing();
    return;
  }

  const project = dialog.context.project.project;
  if (project) {
    project.last_update = new Date();
    project.last_activity = project.last_update;
    const person = project.contractor;
    if (!project.escrow || (project.escrow.id === lang.project.SKIP_ESCROW.id && !project.escrow.charges /*progress && !project.completed*/)) {
      project.select_notice = null;

      if (project.group_settings && 'service_fee' in project.group_settings) project.service_fee = project.group_settings.service_fee;
      else project.service_fee = SERVICE_FEE;

      project.progress = 0;

      let delete_contract;
      if (project.contract) {
        const contract = dialog.cache.contracts[project.contract];
        if (contract && !(contract.client_signed && contract.contractor_signed)) delete_contract = contract;
        project.contract = null;
      }

      if (project.candidates) {
        for (const candidate of project.candidates) {
          if (candidate.state === ProjectCandidateState.SELECTED) {
            candidate.state = ProjectCandidateState.ACCEPTED;
            break;
          }
        }
      }

      project.contractor = null;

      _doSave(dialog, async p => {
        if (delete_contract) await dialog.contracts.delete(delete_contract);
        if (person) await dialog.projects.notify(p, new ForaUser(person.askfora_id), person);
        _sendSafeProject(dialog, p);
      }, true);
    } else {
      _sendSafeProject(dialog, project);
      dialog.setTopic(Topics.PROJECT_UNSELECT_ERROR);
    }
  } else {
    logging.warnFP(LOG_NAME, '_unselectContractor', dialog.user.profile, `No project`);
    dialog.setTopic(Topics.PROJECT_MISSING);
  }
}

function _rejectCandidate(dialog: Dialog) {
  if (dialog.context.project.saving) {
    // dialog.quickPing();
    return;
  }

  _doSave(dialog, async p => {
    _sendSafeProject(dialog, p);
  }, true);
}

function _doSave(dialog: Dialog, c?: (project: Project) => Promise<void>, add_candidates = false) { // : Promise<Project> {
  dialog.runAsync('project', async () => {
    dialog.context.project.saved = false;
    dialog.context.project.saving = true;

    const project = dialog.context.project.project;

    if (add_candidates && dialog.context.project.people) {
      await dialog.projects.saveCandidates(dialog.context.project.project,
        dialog.context.project.people, ProjectCandidateState.FOUND, dialog.context.project.people.map(p => p.id), -1);
    }

    let p;
    if (project.client.self) {
      p = await dialog.projects.update(project, !project.archived);
      if (p.contractor) {
        // don't use our person id for them
        const contractor_person = new Person(p.contractor);
        contractor_person.id = null;
        await dialog.projects.createForUser(new ForaUser(p.contractor.askfora_id), contractor_person, p, false);
      } else {
        for (const candidate of project.candidates.filter(c => c.askfora_id)) {
          // don't use our person id for them
          const candidate_person = new Person(candidate);
          candidate_person.id = null;
          await dialog.projects.createForUser(new ForaUser(candidate.askfora_id), candidate_person, p, false);
        }
      }
    } else {
      p = await dialog.projects.update(project, false);
      await dialog.projects.updateShared(project);
    }

    if (dialog.context.project) {
      dialog.context.project.project = p;
      dialog.context.project.saved = true;
    }
    if (c) c(p);
  });
}

function _checkSaved(dialog: Dialog) {
  const project = dialog.context.project.project;

  // show the project
  if (dialog.context.project.saving) {
    if (dialog.context.project.saved) {
      _sendSafeProject(dialog, project);
      return true;
    } else {
      // dialog.quickPing();
      return false;
    }
  }

  return true;
}

// remove sensitive info before sending project
function _sendSafeProject(dialog: Dialog, project: Project) {
  const is_contractor: boolean = project.client && (!dialog.me || project.client.id !== dialog.me.id);
  const reply_project: ProjectInfo = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, !is_contractor);
  dialog.addReplies(reply_project);
}

function runAction(dialog: Dialog) {
  if (!dialog.context.project) dialog.context.project = new ProjectPluginState();

  const bool = dialog.actionValues(ActionType.BOOL);
  if (dialog.checkTopic(Topics.PROJECT_CONFIRM_SEND_PROPOSAL)) {
    if (bool && bool.length) dialog.context.project.auto_send = bool[0];
  }

  if (dialog.checkTopic(Topics.PROJECT_CANDIDATE_LOAD) || !dialog.context.project.project) _loadProjectCandidate(dialog);

  if (!_checkSaved(dialog)) return;

  switch (dialog.topic) {
    case Topics.PROJECT_CONFIRM_SEND: 
      return _confirmSend(dialog);

    case Topics.PROJECT_CONFIRM_SEND_PROPOSAL:
      if ((bool && bool.length && bool[0]) || (dialog.context.project.auto_send
        && (dialog.context.project.project && !dialog.context.project.project.accepted && !dialog.context.project.project.declined) )
        ) return _sendProposal(dialog);
      else if(dialog.context.project.project && 
        (dialog.context.project.project.accepted || dialog.context.project.project.declined) ) {
          dialog.setTopic(Topics.PROJECT_PROPOSAL_REPLIED);
      } else dialog.setTopic(Topics.PROJECT_SEND_LATER);
      break;

    // Find matching candidates
    case Topics.PROJECT_MATCHING: return _projectSkillSearch(dialog);

    // Add a candidate
    case Topics.PROJECT_ADD: return _addProjectCandidate(dialog);

    // Select a candidate
    case Topics.PROJECT_SELECT: return _selectCandidate(dialog);

    // Select a contractor
    case Topics.PROJECT_CONTRACTOR_SELECTED:
      if (dialog.context.project.saved_people) _sendSafeProject(dialog, dialog.context.project.project);
      // else dialog.quickPing();
      return;

    case Topics.CONFIRM_SELECT_CONTRACTOR: return _selectContractor(dialog);

    // Unselect a contractor
    case Topics.PROJECT_CONTRACTOR_UNSELECT: return _unselectContractor(dialog);

    // Reject candidate
    case Topics.PROJECT_CANDIDATE_REJECT: return _rejectCandidate(dialog);

    // Send an invite from Fora
    case Topics.PROJECT_SEND_INVITE: return _sendInvite(dialog);

    // Draft invite
    case Topics.PROJECT_DRAFT_INVITE: return _draftInvite(dialog);

    // For testing, auto accept anyone invited
    case Topics.PROJECT_TEST_INVITE: return _testAutoAccept(dialog);

    case Topics.PROJECT_MATCHING_NO_MATCH: return;
  }
}

function setPrompt(dialog: Dialog) {
  const project_infos: ProjectInfo[] = dialog.getRepliesType(EntityType.Project) as ProjectInfo[];
  let project_info: ProjectInfo = null;
  if (project_infos.length) project_info = project_infos[0];

  switch (dialog.topic) {
    case Topics.PROJECT_MATCHING:
      if (!dialog.context.project.search_prompt) {
        if (project_info.expert) dialog.addPrompt(lang.project.EXPERT_SEARCHING, true);
        else dialog.addPrompt(lang.project.SEARCHING, true);
        // dialog.addAnswers(lang.imports.IMPORTS_CMD);
        dialog.context.project.search_prompt = true;
        if (project_info) dialog.addInfo(project_info);
      } /*else if (!dialog.message.ping && dialog.context.project.project) {
        if (!dialog.context.project.search_help) dialog.context.project.search_help = Topics.FAQ;
        dialog.setTopic(dialog.context.project.search_help);
        dialog.currentPlugin().setPrompt(dialog);
        dialog.context.project.search_help = dialog.topic;
        dialog.setTopic(Topics.PROJECT_MATCHING);
      }*/ else if (project_info && dialog.context.project.done_searching) {
        project_info.updated = true;
        dialog.addInfo(project_info, true);
        if (dialog.user.settings.push !== PushSetting.Notify) {
          const connect = dialog.isAuthenticated(AuthLevel.Basic);

          if (dialog.context.project.project.candidates.length) {
            if (project_info.expert) dialog.addPrompt(lang.project.EXPERT_FOUND);
            else dialog.addPrompt(lang.project.SEARCH_FOUND);
          } else if (!dialog.isGuestAccount()) {
            if (project_info.expert) dialog.addPrompt(lang.project.EXPERT_NONE(connect));
            else dialog.addPrompt(lang.project.SEARCH_NONE(connect));
          }
          if (connect) dialog.addAnswers(lang.init.REG_CONNECT_CONTACTS);
          dialog.addAnswers(lang.imports.IMPORTS_TUTORIAL_TYPE_ANSWERS.map(a => `Import ${a}`));
        }
        dialog.reset('project');
      }
      break;
    case Topics.PROJECT_MATCHING_NO_MATCH:
      if (project_info) {
        project_info.added = true;
        dialog.addInfo(project_info, true);
        if (project_info.candidates && project_info.candidates.length && !dialog.isGuestAccount()) {
          if (project_info.expert) dialog.addPrompt(lang.project.EXPERT_FOUND);
          else dialog.addPrompt(lang.project.SEARCH_FOUND);
          dialog.addAnswers(lang.imports.IMPORTS_TUTORIAL_TYPE_ANSWERS.map(a => `Import ${a}`));
          dialog.reset('project');
        } else {
          dialog.setHide();
          dialog.setTopic(Topics.PROJECT_STEPS);
          // dialog.delayPing();
          dialog.reRun();
        }
      } else if (dialog.context.project) {
        if (dialog.context.project.draft_url) {
          dialog.lastPrompt('project', lang.project.DRAFT_PROMPT(dialog.context.project.draft_url), { clear: true });
        }
      }
      break;
    case Topics.PROJECT_ADD:
      if (project_info) {
        project_info.updated = true;
        dialog.addInfo(project_info, true);
        if (dialog.context.project.people && dialog.context.project.people.length) {
          dialog.addPrompt(lang.project.CANDIDATE_ADDED(dialog.context.project.people));
        }
        dialog.reset('project');
      }
      break;
    case Topics.PROJECT_CANDIDATE_NO_CANDIDATE:
      dialog.addPrompt(lang.project.MISSING_CANDIDATE, true);
      dialog.clearContext('project');
      dialog.newDialog();
      break;
    case Topics.PROJECT_CANDIDATE_NO_EMAIL:
      dialog.addPrompt(lang.project.MISSING_EMAIL, true);
      dialog.reset('project');
      break;
    case Topics.CONFIRM_SELECT_CONTRACTOR:
      if (!dialog.context.project.contracting) {
        dialog.addPrompt(lang.project.CONFIRM_SELECT(dialog.context.project.contractor), true);
        dialog.addAnswers(lang.project.SELECT_ANSWERS);
        dialog.setSticky();
      }
      break;

    case Topics.CONTRACTOR_SELECT_ERROR:
      if (dialog.context.project && dialog.context.project.project) {
        project_info.updated = true;
        dialog.addInfo(project_info, project_info.contract !== null);
        dialog.addPrompt(lang.project.ERROR_SELECT(dialog.context.project.project.contractor), true);
        dialog.reset('project');
      }
      break;
    case Topics.PROJECT_CONTRACTOR_SELECTED:
      if (project_info) {
        let keep_topic = false;
        project_info.updated = true;
        dialog.addInfo(project_info, project_info.contract !== null);
        if (project_info.contractor) {
          dialog.addPrompt(lang.project.SELECTED_PROMPT(project_info, dialog.context.project.new_contract !== undefined));
          if (dialog.context.project.new_contract) {
            dialog.setTopic(Topics.CONTRACT_SIGN);
            dialog.context.contract = {
              contract_id: dialog.context.project.new_contract.id,
              doc: dialog.context.project.new_contract,
              loading: true,
            } as ContractPluginState;
            keep_topic = true;
          }
        } else dialog.setHide();

        dialog.reset('project', keep_topic);
        if (keep_topic) dialog.reRun();
      }
      break;
    case Topics.PROJECT_CONTRACTOR_UNSELECT:
      if (project_info) {
        project_info.updated = true;
        dialog.addInfo(project_info, true);
        dialog.addPrompt(lang.project.UNSELECT_SUCCESS);
        dialog.reset('project');
      }
      break;
    case Topics.PROJECT_CANDIDATE_REJECT:
      if (project_info) {
        project_info.updated = true;
        dialog.addInfo(project_info, true);
        dialog.addPrompt(lang.project.REJECT_SUCCESS);
        dialog.reset('project');
      }
      break;
    case Topics.PROJECT_UNSELECT_ERROR:
      if (project_info) {
        dialog.addInfo(project_info, true);
        dialog.addPrompt(lang.project.UNSELECT_ERROR(project_info.contractor.nick), true);
        dialog.reset('project');
      }
      break;
    case Topics.PROJECT_SEND_LATER:
      dialog.newDialog();
      if (dialog.context.project.project.client.askfora_id === dialog.user.profile) dialog.lastPrompt('project', lang.project.SEND_LATER);
      else dialog.lastPrompt('project', lang.project.SEND_LATER_PROPOSAL);
      dialog.setHide(10000);
      break;
    case Topics.PROJECT_SEND_INVITE:
      if (!dialog.context.project) dialog.reset('project');
      else if (!dialog.context.project.sent) break;
    // don't break
    case Topics.PROJECT_CONFIRM_SEND_PROPOSAL:
      if (project_info && dialog.context.project && dialog.context.project.people) {
        project_info.updated = true;
        dialog.addInfo(project_info, true);
        dialog.lastPrompt('project', project_info.proposal ?
          lang.project.SEND_PROPOSAL_PROMPT(project_info.client) :
          project_info.expert ? lang.project.EXPERT_PROMPT(dialog.context.project.people.reduce((a, b) => a || b.network, false)) :
            lang.project.SEND_PROMPT(dialog.context.project.people.reduce((a, b) => a || b.network, false)));
        dialog.setHide(10000);
      } else dialog.reset('project');
      break;
    case Topics.PROJECT_PROPOSAL_REPLIED:
      dialog.clearDialog(true);
      if (dialog.context.project.project) {
        const client = dialog.context.project.project && dialog.context.project.project.client ?  dialog.context.project.project.client.displayName : 'Client';
        if(dialog.context.project.project.accepted) dialog.lastPrompt('project', lang.project.PROPOSAL_ACCEPTED_PROMPT(client));
        else if(dialog.context.project.project.declined) dialog.lastPrompt('project', lang.project.PROPOSAL_DECLINED_PROMPT(client));
        else dialog.lastPrompt('prompt', lang.project.PROPOSAL_REPLIED_PROMPT);
      } else dialog.lastPrompt('prompt', lang.project.PROPOSAL_REPLIED_PROMPT);
      break;
    case Topics.PROJECT_DRAFT_INVITE:
      if (project_info) {
        if (dialog.context.project.draft_url) {
          dialog.addInfo(project_info, true);
          dialog.lastPrompt('project', lang.project.DRAFT_PROMPT(dialog.context.project.draft_url));
        }
      }
      break;

    case Topics.DRAFT_INVITE:
      if (project_info) {
        if (dialog.context.project && dialog.context.project.draft_url) {
          dialog.addInfo(project_info, true);
          dialog.lastPrompt('project', lang.project.DRAFT_PROMPT(dialog.context.project.draft_url));
        }
      }
      break;

    case Topics.INVITE_ERROR:
      dialog.addPrompt(lang.project.INVITE_ERROR_STARTED, true);
      dialog.reset('project');
      break;
  }
}
