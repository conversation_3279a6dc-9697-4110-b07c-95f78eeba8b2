import lang from '../lang';
import Dialog, { TOPIC, Topics } from '../session/dialog';
import { Plugin } from '../types/plugins';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

const ABOUT_NAME = 'About';
const ABOUT_DESC = 'Information about AskFora';

const ABOUT_RESERVED = ['about'];

function ABOUT_SHORTCUT(a) {
  if (!a) return { name: 'About', value: 'About' };
  else return null;
}

TOPIC('ABOUT');

function keywordMatch(actions, message, raw_message) {
  if (parsers.checkKeyword(message, lang.about.ABOUT_KWDS, true)) return true;
  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  if (!(dialog.isAuthenticated() && dialog.isMaskedTopic()) && !dialog.message.ping) {
    dialog.setTopic(Topics.ABOUT);
    return true;
  }
  return false;
}

function setPrompt(dialog: Dialog) {
  dialog.addPrompt(lang.about.ABOUT_ASK_FORA, true);
  for (const index in lang.about.ABOUT_INFO) {
    dialog.addInfo(peopleUtils.getPersonInfo(null, lang.about.ABOUT_INFO[index]));
  }
  dialog.setTopic(dialog.isAuthenticated() ? Topics.DEFAULT : Topics.INIT);
  dialog.reset(null, true);
}

export default {
  name: ABOUT_NAME,
  description: ABOUT_DESC,
  examples: lang.about.ABOUT_EG,
  reserved: ABOUT_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive: () => { return false; },
  runAction: (d) => { },
  setPrompt,
  shortcuts: ABOUT_SHORTCUT,
} as Plugin;

Dialog.registerPlugin(Topics.ABOUT, module.exports.default);
