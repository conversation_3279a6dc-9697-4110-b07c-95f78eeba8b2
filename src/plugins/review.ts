import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Plugin, ReviewPluginState } from '../types/plugins';
import { EntityType } from '../types/shared';

import parsers from '../utils/parsers';
import personInfo from './person_info';

const REVIEW_NAME = 'Review';

TOPIC('REVIEW');
const REVIEW_RESERVED = lang.review.REVIEW_RESERVED;

function keywordMatch(actions, message: string, _raw_message) {
  return parsers.checkKeyword(message, lang.review.REVIEW_KWD, true) && !parsers.checkKeyword(message, lang.review.REVIEW_IGNORE);
}

function setAction(dialog: Dialog, _message, _raw_message): boolean {
  dialog.setTopic(Topics.REVIEW);
  return true;
}

function runAction(dialog: Dialog) {
  if (!dialog.context.review) {
    // Need at least an entity type
    let entity_type = dialog.actionValues(ActionType.ENTITY_TYPE);
    if (!entity_type.length) entity_type = [EntityType.Person];

    // Find a person, or default to "me"
    let id = null;
    let self = false;
    const act_people = dialog.actionValues(ActionType.ENTITY);
    if (act_people.length && act_people[0].people && act_people[0].people.length) {
      id = act_people[0].people[0].id;
      if (dialog.me && id === dialog.me.id) self = true;
    } else if (dialog.me) {
      id = dialog.me.id;
      self = true;
    }

    dialog.context.review = {
      id,
      self,
      entity_type: entity_type[0],
    } as ReviewPluginState;

    if (!id && dialog.me) return personInfo.findPersonPrompt(dialog);
  }

  if (!dialog.context.review.id) {
    const curr_dialog = dialog.currentDialog();
    if (curr_dialog.next_topic === Topics.FOUND_PERSON) {
      const found_people = dialog.getRepliesType(EntityType.Person);
      if (found_people.length) dialog.context.review.id = found_people[0].id;
    }
  }
}

function setPrompt(dialog: Dialog) {
  if (dialog.context.review) {
    if (dialog.context.review.id) {
      const info = {
        type: EntityType.Person,
        self: dialog.context.review.self,
        id: dialog.context.review.id,
        focus: dialog.context.review.entity_type,
      };
      dialog.addInfo(info, true);
    } else dialog.addPrompt(lang.review.NOT_FOUND, true);
  }

  dialog.clearContext('review');
  dialog.setTopic(Topics.DEFAULT);
}

export default {
  name: REVIEW_NAME,
  description: lang.review.REVIEW_DESC,
  examples: lang.review.REVIEW_EG,
  reserved: REVIEW_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive: d => {
    return d.checkTopic(Topics.REVIEW);
  },
  runAction,
  setPrompt,
  shortcuts: _a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(Topics.REVIEW, module.exports.default);
