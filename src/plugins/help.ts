import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Plugin } from '../types/plugins';
import { EntityType } from '../types/shared';

import { randomPhrase } from '../utils/funcs';
import { promptInfo } from '../utils/info';
import parsers from '../utils/parsers';

const HELP_NAME = 'Help';

function HELP_SHORTCUT(a) {
  if (!a) return { name: 'Show me the FAQ', value: 'Show me the FAQ' };
  else return { name: 'Help', value: 'Help' };
}

const topics = [
  TOPIC('HELP'),
  TOPIC('FAQ'),
  TOPIC('HELP_MENU_TOP'),
  TOPIC('HELP_MENU'),
  TOPIC('HELP_CONNECT'),
  TOPIC('HELP_MERGE'),
  TOPIC('CLICK_TUTORIAL'),
  TO<PERSON><PERSON>('TUTORIAL'),
  TOPIC('TUTORIAL_FREELANCING'),
  TOPIC('CONNECT_TUTORIAL'),
  TOPIC('TIP'),
  TOPIC('SHARE'),
  TOPIC('HELP_SEARCH'),
  TOPIC('HELP_TERMS'),
];

function keywordMatch(actions, message, raw_message) {
  if (
    parsers.checkKeyword(message, lang.help.HELP_KWDS, true) ||
    parsers.checkKeyword(message, lang.help.FAQ_START, true) ||
    parsers.checkKeyword(message, lang.help.HELP_MENU, true) ||
    parsers.checkKeyword(message, lang.help.HELP_ALL, true) ||
    parsers.checkKeyword(message, lang.help.HELP_TIP, true) ||
    parsers.checkKeyword(message, lang.help.HELP_FREELANCING, true) ||
    parsers.checkKeyword(message, lang.help.HELP_CONNECT, true, true) ||
    parsers.checkKeyword(message, lang.help.HELP_SEARCH, true, true) ||
    parsers.checkKeyword(message, lang.help.HELP_TERMS_KWD, true, true) ||
    message === lang.help.HELP_CONNECT_ME
  ) {
    return true;
  }

  return false;
}

function setAction(dialog: Dialog, message, raw_message) {

  if (dialog.checkTopic(Topics.HELP_MENU)) {
    if (parsers.checkKeyword(message, 'Help', true)) dialog.setTopic(Topics.HELP_MENU_TOP);
    else {
      for (const option of lang.help.HELP_MENU_OPTIONS) {
        if (parsers.checkKeyword(message, option.keywords)) {
          if(option.prompt) return true;
          else if(option.help_topic) break;
          else return false;
        }
      }
    }
  }
  ///////////////////////
  // commands to start help

  // Tip
  if (parsers.checkKeyword(message, lang.help.HELP_TIP)) {
    dialog.setTopic(Topics.TIP);
    return true;
  }

  // FAQ
  if (parsers.checkKeyword(message, lang.help.FAQ_START, true)) {
    dialog.setTopic(Topics.FAQ);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.LEARN_MORE, true)) {
    if (dialog.user.isAuthenticatedNonGuest()) dialog.setTopic(Topics.FAQ);
    else dialog.setTopic(Topics.CLICK_TUTORIAL);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.HELP_PROFILE, true) && 
    (dialog.user.isGuestAccount() || !dialog.user.isAuthenticated())) {
    dialog.addAction(ActionType.BOOL, true);
    dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Settings);
    dialog.setTopic(Topics.SETTINGS);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.HELP_FREELANCING, true)) {
    dialog.setTopic(Topics.TUTORIAL_FREELANCING);
    return true;
  }

  // Connect
  if (parsers.checkKeyword(message, lang.help.HELP_CONNECT, true, true) || message === lang.help.HELP_CONNECT_ME) {
    dialog.setTopic(Topics.HELP_CONNECT);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.HELP_SEARCH, true)) {
    dialog.setTopic(Topics.HELP_SEARCH);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.HELP_MENU, true)) {
    dialog.setTopic(Topics.HELP_MENU_TOP);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.HELP_TERMS_KWD, true)) {
    dialog.setTopic(Topics.HELP_TERMS);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.HELP_ELSE, true)) {
    dialog.setTopic(Topics.HELP_FEEDBACK);
    return true;
  }

  // Help with commands, default
  if (!dialog.isMaskedTopic() && !dialog.message.ping && !isActive(dialog)) {
    dialog.setTopic(Topics.HELP);

    const basic_message = message.replace(/[.?!]$/, '');
    const message_match = lang.help.HELP_ALL.filter(l => basic_message.indexOf(l) === 0);
    const words = basic_message
      .split(' ')
      .slice(message_match.length ? message_match[0].split(' ').length : 1)
      .filter(w => !lang.help.IGNORE.includes(w))
      .filter(w => w.length > 2);
    const plugins = Dialog.getPlugins(); // module.exports.plugins;

    const all = [];
    const action_indexes: string[] = [];

    for (const index in plugins) {
      const plugin = plugins[index];
      all.push(index);

      for (const reserved of plugin.reserved) {
        let added = false;
        for (const word of words) {
          if (reserved.includes(word)) {
            if (!action_indexes.includes(index)) action_indexes.push(index);
            added = true;
            break;
          }
        }
        if (added) break;
      }
    }

    if (action_indexes.length === 0) dialog.addAction(ActionType.CMD, all);
    else dialog.addAction(ActionType.CMD, action_indexes);

    return true;
  }

  // by default, send a prompt back
  if (isActive(dialog)) return true;


  return false;
}

function isActive(dialog: Dialog) {
  if (dialog.checkTopic([Topics.HELP, Topics.HELP_MENU, Topics.TUTORIAL, Topics.CONNECT_TUTORIAL, Topics.HELP_CONNECT, Topics.HELP_MERGE])) return true;

  if (dialog.checkTopic(Topics.FAQ)) {
    for (const index in lang.help.FAQ_KWD) {
      if (dialog.message.message.indexOf(lang.help.FAQ_KWD[index]) > -1) return true;
    }
  }

  if (dialog.checkTopic(Topics.SHARE)) return true;
}

function runAction(dialog: Dialog) {
  // detect end of FAQ
  let kwd_index = -1;

  switch(dialog.topic) {
    case Topics.FAQ:
      for (const index in lang.help.FAQ_KWD) {
        if (dialog.message.message.indexOf(lang.help.FAQ_KWD[index]) > -1) {
          kwd_index = parseInt(index, 10);
          break;
        }
      }

      if (kwd_index >= lang.help.FAQ_HINTS.length - 1) dialog.setTopic(Topics.REGISTER_NO_EMAIL);
      break;
    case Topics.HELP_CONNECT:
      dialog.setTopic(Topics.REGISTER);
      break;
  }
}

function setPrompt(dialog: Dialog) {
  const plugins = Dialog.getPlugins();
  const cmds = dialog.actionValues(ActionType.CMD);

  let lmessage = '';
  if (dialog.message.message) {
    dialog.logMessage();
    lmessage = dialog.message.message.toLowerCase();
  }

  switch (dialog.topic) {
    case Topics.HELP:

      if (cmds.length) {
        let examples = [lang.help.HELP_EXAMPLES];
        for (const cmd of cmds) {
          if (plugins[cmd].name !== HELP_NAME) examples = examples.concat(plugins[cmd].examples);
        }
        examples.push(lang.help.HELP_FEEDBACK);

        dialog.addPrompt(examples, true);
        dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.TUTORIAL:
      if (dialog.isAuthenticated() && !dialog.isGuestAccount()) {
        let name = dialog.me ? ` ${dialog.me.nickName}` : null;
        if (!name) name = dialog.user.name ? ` ${dialog.user.name.split(' ')[0]}` : '';
        dialog.addPrompt(lang.help.TUTORIAL(name), true);
        dialog.addInfo(dialog.getPersonInfo());
        dialog.addAnswers(lang.help.TUTORIAL_ANSWERS);
        dialog.reset('init');
      }
      break;
    case Topics.TUTORIAL_FREELANCING:
      if (dialog.isAuthenticated() && !dialog.isGuestAccount()) {
        dialog.addPrompt(lang.help.TUTORIAL_FREELANCING, true);
        dialog.reset('init');
      }
      break;
    case Topics.CONNECT_TUTORIAL:
      dialog.addPrompt(lang.help.CONNECT_TUTORIAL, true);
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.FAQ:
      if (!dialog.message.ping) {
        let kwd_index = -1;
        for (const index in lang.help.FAQ_KWD) {
          if (dialog.message.message.indexOf(lang.help.FAQ_KWD[index]) > -1) {
            kwd_index = parseInt(index, 10);
            break;
          }
        }

        if (kwd_index === -1) {
          dialog.addPrompt(lang.help.FAQ, true);
          dialog.addHint(lang.help.FAQ_HINTS[0]);
          dialog.addSuggestion(lang.help.FAQ_HINTS[0]);
        } else {
          dialog.addPrompt(lang.help.FAQ_ANSWERS[kwd_index], true);

          const max = dialog.isAuthenticated() ? 2 : 1;
          if (kwd_index < lang.help.FAQ_HINTS.length - max) {
            dialog.addHint(lang.help.FAQ_HINTS[kwd_index + 1]);
            dialog.addSuggestion(lang.help.FAQ_HINTS[kwd_index + 1]);
          } else {
            dialog.setTopic(Topics.DEFAULT);
          }
        }
      }
      break;

    case Topics.CLICK_TUTORIAL:
      dialog.addPrompt(lang.help.CLICK_TUTORIAL, true);
      dialog.setTopic(Topics.INIT);
      break;

    case Topics.TIP:
      {
        let examples = [];
        for (const index in plugins) examples = examples.concat(plugins[index].examples);
        dialog.addPrompt(`Tip: ${randomPhrase(examples)}`, true);
        dialog.setTopic(Topics.DEFAULT);
        break;
      }
    case Topics.SHARE:
      dialog.lastPrompt(null, lang.help.SHARE);
      break;
    case Topics.HELP_MENU_TOP: 
      dialog.newDialog();
      dialog.addPrompt(lang.help.HELP_MENU_TOP, true);
      dialog.addAnswers(lang.help.HELP_MENU_ANSWERS);
      dialog.setTopic(Topics.HELP_MENU);
      break;
    case Topics.HELP_MENU: {
        dialog.newDialog();
        let prompt = lang.help.HELP_MENU_TOP;
        let answers = lang.help.HELP_MENU_ANSWERS;
        const lmessage = dialog.message.message.toLowerCase();

        for (const option of lang.help.HELP_MENU_OPTIONS) {
          if (parsers.checkKeyword(lmessage, option.keywords, true)) {
            prompt = option.prompt;
            answers = option.answers.concat(['Help']);
            break;
          }
        }
        dialog.addPrompt(prompt, true);
        dialog.addAnswers(answers);
        // dialog.noPing();
      }
      break;
    case Topics.HELP_MERGE:
      dialog.addPrompt(lang.help.HELP_MERGE, true);
      dialog.setTopic(Topics.REGISTER_CONNECT);
      // dialog.quickPing();
    /*case Topics.HELP_CONNECT:
      const providers = AuthPrvider.clientAuthSet();
      dialog.addPrompt(lang.help.HOW_TO(provders.map(p => p.name)), true);
      dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
      break;*/
    case Topics.HELP_SEARCH:
      dialog.clearFilters();
      dialog.lastPrompt('help', lang.help.HELP_SEARCH_PROMPT, {clear: true});
      dialog.setTopic(Topics.FIND_PERSON);
      break;
    case Topics.HELP_TERMS:
      dialog.newDialog();
      if (parsers.checkKeyword(lmessage, lang.help.HELP_TERMS_DATA)) {
        dialog.lastPrompt('help', promptInfo(lang.help.HELP_TERMS_DATA_PROMPT, '/data', lang.help.HELP_DATA));
      } else 
      if (parsers.checkKeyword(lmessage, lang.help.HELP_TERMS_PRIVACY)) {
        dialog.lastPrompt('help', promptInfo(lang.help.HELP_TERMS_PRIVACY_PROMPT, '/privacy', lang.help.HELP_PRIVACY));
      } else 
      if (parsers.checkKeyword(lmessage, lang.help.HELP_TERMS_SECURITY)) {
        dialog.lastPrompt('help', promptInfo(lang.help.HELP_TERMS_SECURITY_PROMPT, '/security', lang.help.HELP_SECURITY));
      } else 
      if (parsers.checkKeyword(lmessage, lang.help.HELP_TERMS_ABOUT)) {
        dialog.lastPrompt('help', promptInfo(lang.help.HELP_TERMS_ABOUT_PROMPT, '/about', lang.help.HELP_ABOUT));
      } else 
      if (parsers.checkKeyword(lmessage, lang.help.HELP_TERMS_BLOG)) {
        dialog.lastPrompt('help', promptInfo(lang.help.HELP_TERMS_BLOG_PROMPT, 'https://blog.askfora.com', lang.help.HELP_BLOG));
      } else 
      if (parsers.checkKeyword(lmessage, lang.help.HELP_TERMS_TERMS)) {
        dialog.lastPrompt('help', promptInfo(lang.help.HELP_TERMS_PROMPT, '/terms', lang.help.HELP_TERMS));
      } else dialog.lastPrompt('help', lang.help.HELP_TERMS_PROMPT);
  }
}

export default {
  name: HELP_NAME,
  description: lang.help.HELP_DESC,
  examples: [lang.help.HELP_FEEDBACK],
  reserved: lang.help.HELP_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: HELP_SHORTCUT,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default);
