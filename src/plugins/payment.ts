import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';
import stripe from '../sources/stripe_controller';

import { ActionType } from '../types/globals';
import { Location } from '../types/items';
import { Plugin } from '../types/plugins';
import parsers from '../utils/parsers';

TOPIC('CONNECT_PAYMENT');

function keywordMatch(actions, message) {
  return parsers.checkKeyword(message, lang.payment.CONNECT_PAYMENT, true);
}

function setAction(dialog: Dialog, message, raw_message) {
  dialog.setTopic(Topics.CONNECT_PAYMENT);
  return true;
}

function runAction(dialog: Dialog) {
  if (!dialog.context.payment) {
    dialog.context.payment = {};
    dialog.runAsync('payment', async () => {
      dialog.context.payment.stripe_countries = await stripe.countries();
    });
  }

  if (!dialog.context.payment.stripe_countries) {
    return true;
  }

  const location = dialog.actionValues(ActionType.LOCATION) as Partial<Location>[];
  if (location && location.length) {
    if ((location[0].name && dialog.context.payment.stripe_countries.includes(location[0].name)) ||
      (location[0].native && dialog.context.payment.stripe_countries.includes(location[0].native)) ||
       (location[0].code && dialog.context.payment.stripe_countries.includes(location[0].code))
      ) {
      dialog.context.stripe = stripe.setupContext(dialog.user, dialog.me, location[0].code, dialog.cache);
      dialog.context.stripe.country_confirmed = true;
      dialog.setTopic(Topics.STRIPE_ACCOUNT);
    } else {
      dialog.context.wise = {skipped: []};
      dialog.context.wise.location = location[0] as Partial<Location>;
      dialog.setTopic(Topics.WISE_CURRENCY);
    }
    dialog.clearDialog(true);
    dialog.reRun();
    return;
  }
}

function setPrompt(dialog:Dialog) {
  if (!dialog.isProcessing() || dialog.isDoneProcessing()) { 
    dialog.addPrompt(lang.payment.PAYMENT_PROMPT, true);
    if (dialog.user.location) dialog.addAnswers(dialog.user.location.name);
  }
}

export default {
  name: lang.payment.PAYMENT_NAME,
  description: lang.payment.PAYMENT_DESCRIPTION,
  examples: [],
  reserved: [],
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive: (dialog: Dialog) => { return dialog.checkTopic(Topics.CONNECT_PAYMENT); },
  runAction,
  setPrompt,
  shortcuts: a => { return null; },
} as Plugin;

Dialog.registerPlugin(Topics.CONNECT_PAYMENT, module.exports.default);