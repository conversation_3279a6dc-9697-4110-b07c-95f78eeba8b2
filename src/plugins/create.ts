import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { Action, Category, Plan } from '../types/globals';
import { Analysis, Ask, GlobalType, Goal, Person, Tag } from '../types/items';
import { Plugin } from '../types/plugins';
import { AskInfo, Color, EntityType, Relation, TagType, Uid, cleanFilter, filterSkills } from '../types/shared';

import { SECONDS } from '../utils/datetime';
import { embedCategory } from '../utils/filter';
import { localeDowMonthDay } from '../utils/format';
import { arrayIncludes, flatten, saveOneTypeValue, stripPuncs } from '../utils/funcs';
import { analysisInfo, askInfo, goalInfo, planInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import data from '../data';
import { scoreCandidateCategories } from '../skills';

const LOG_NAME = 'plugins.create';

const topics = [
  TOPIC('CREATE'),
  TOPIC('CREATE_PROMPT'),
  TOPIC('CREATE_COPY'),
  TOPIC('CREATE_INPUT'),
]

function keywordMatch(actions: Action[], message: string, raw_message: string) {
  if (parsers.checkKeyword(message, lang.create.CREATE_KWD, true)) return true;
  return false;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function setAction(dialog: Dialog, message: string, raw_message: string) {
  if (dialog.isProcessing(false) && isActive(dialog)) return true;

  const create_type = parsers.findKeyword(message, lang.create.CREATE_TYPES);

  logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Create type ${create_type}`);

  // copy from passed in list or last replies
  let filters = isActive(dialog) ? dialog.filters.slice() :
    dialog.getLastRepliesType(EntityType.SavedFilter, true);

  logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Filters ${JSON.stringify(filters)}`);

  // make filters if no recent ones
  if (!isActive(dialog) && !filters.length) {
    dialog.clearFilters([...lang.create.CREATE_KWD, ...lang.create.CREATE_TYPES]);
    filters = dialog.filters.filter(f => f.conditions.length).map(f => { return { conditions: f.conditions}});
    filters.forEach(cleanFilter);
    filters = filters.filter(f => f.conditions && f.conditions.length);
    logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Clean filters ${JSON.stringify(filters)}`);
  }

  const filter_skills = filters.map(f => filterSkills(f)).reduce((a,b) => a && b ? a.concat(b) : a ? a : b ? b : [], []);
  if (!filter_skills.length) {
    const skills = dialog.checkTopic(Topics.CREATE_PROMPT) ? raw_message : parsers.extractLast(lang.create.CONTEXT, message, undefined, false, true);
    if (skills && skills.length) {
      const value = skills.split(',').map(s => stripPuncs(s));
      filters = [{id: uuid(), name: skills, conditions:[{
        att: TagType.skill, rel: Relation['~'], value,
      }]}];
    }
    logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Added skills ${JSON.stringify(filters)}`);
  }

  filters.forEach(cleanFilter);

  logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Re-clean filters ${JSON.stringify(filters)}`);

  if (create_type && !dialog.context.create) {
    switch(create_type) {
      default:
      case lang.create.PEOPLE:
      case lang.create.SEARCH:
        if (!isActive(dialog)) {
          if (filters && filters.length) dialog.setTopic(Topics.CREATE_COPY);
          else dialog.setTopic(Topics.CREATE);
        }
        dialog.context.create = { create_type: EntityType.Ask, filters };
        break;
      
      case lang.create.PLAN:
      case lang.create.DEVELOP:
      case lang.create.DEVELOPMENT:
        if (dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
          dialog.context.create = { create_type: EntityType.Plan, filters };
          if (!isActive(dialog)) {
            if (filters && filters.length) dialog.setTopic(Topics.CREATE_COPY);
            else dialog.setTopic(Topics.CREATE);
          } 
        } else dialog.reset('create');
        break;

      case lang.create.TALENT:
      case lang.create.ANALYZE:
      case lang.create.ANALYSIS:
        dialog.context.create = { create_type: EntityType.Analysis, filters };
        if (!isActive(dialog)) {
          if (filters && filters.length) dialog.setTopic(Topics.CREATE_COPY);
          else dialog.setTopic(Topics.CREATE);
        } 
        break;

      case lang.create.GOAL:
      case lang.create.LEARNING:
        logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Initialize ${JSON.stringify(filters)}`);
        dialog.context.create = { create_type: EntityType.Goal, filters };
        if (!isActive(dialog)) {
          if (filters && filters.length) dialog.setTopic(Topics.CREATE_COPY);
          else dialog.setTopic(Topics.CREATE);
        }
        break;
 
    }
  } else if(dialog.checkTopic([Topics.CREATE_PROMPT, Topics.CREATE_INPUT]) && dialog.context.create) {
    logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Add filters ${JSON.stringify(filters)} to ${JSON.stringify(dialog.context.create.filters)}`);
    if (dialog.context.create.filters) {
      dialog.context.create.filters = dialog.context.create.filters.concat(filters);
    } else dialog.context.create.filters = filters; 
  }

  filterToSkills(dialog);

  return isActive(dialog);
}

function filterToSkills(dialog: Dialog) {
  let filters = dialog.context.create ? dialog.context.create.filters : [];
  const filter_skills = filters.map(f => filterSkills(f)).reduce((a,b) => a && b ? a.concat(b) : a ? a : b ? b : [], []);
  const lskills = filter_skills.map(s => s.toLowerCase());

  logging.infoFP(LOG_NAME, 'filterToSkills', dialog.user.profile, `Filter skills ${JSON.stringify(filter_skills)}`);

  const orgs = flatten(filters.filter(f => 
      f.conditions && f.conditions.find(c => c.att === TagType.organization)
    ).map(f => f.conditions))
    .filter(c => !arrayIncludes(c.value.map(s => s.toLowerCase()), lskills));

  logging.infoFP(LOG_NAME, 'filterToSkills', dialog.user.profile, `Orgs ${JSON.stringify(orgs)}`);

  let skill_orgs = _.uniq([...filter_skills ? filter_skills : [], ...flatten(orgs.map(c => c.value as string[]))]).filter(s => 
    ![...lang.create.CREATE_KWD, ...lang.create.CREATE_TYPES].includes(s));

  logging.infoFP(LOG_NAME, 'filterToSkills', dialog.user.profile, `Skill orgs ${JSON.stringify(skill_orgs)}`);

  skill_orgs = skill_orgs.filter(s => {
    const match = skill_orgs.find(o => o !== s && o.includes(s));
    return match === undefined;
  }).map(s => s.toLowerCase());

  logging.infoFP(LOG_NAME, 'filterToSkills', dialog.user.profile, `Filter skill orgs ${JSON.stringify(skill_orgs)}`);

  filters.forEach(f => {
    if (f.conditions) f.conditions = f.conditions.filter(c => c.att !== TagType.skill);
  });

  filters = filters.filter(f => f.conditions && f.conditions.length);

  logging.infoFP(LOG_NAME, 'filterToSkills', dialog.user.profile, `Filter filters ${JSON.stringify(filters)}`);

  if (skill_orgs.length) {
    logging.infoFP(LOG_NAME, 'filterToSkills', dialog.user.profile, `Adding skills ${JSON.stringify(skill_orgs)} to skills ${JSON.stringify(dialog.context.create.skills)}`);
    if (dialog.context.create && dialog.context.create.skills) {
      dialog.context.create.skills = _.uniq([...dialog.context.create.skills, ...skill_orgs]);
      if (!dialog.context.create.label) dialog.context.create.label = _.startCase(filter_skills.join(', '));
    } else if (dialog.context.create) {
      dialog.context.create.skills = skill_orgs;
      if (!dialog.context.create.label) dialog.context.create.label = _.startCase(filter_skills.join(', '));
    }
  }

  dialog.clearFilters();
}

async function runGoal(dialog: Dialog) {
  const goals = await dialog.goals.load();
  const cat_ids = goals.filter(g => !g.deleted).map(g => g.category.id);

  const skills = dialog.context.create.skills;
  if (!skills) {
    dialog.context.create.empty_type = EntityType.Goal;
    return;
  }


  const add_skills = await dialog.skills.group(skills);

  logging.infoFP(LOG_NAME, 'runGoal', dialog.user.profile, `Generating categories for ${JSON.stringify(skills)}`);
  const categories =  await dialog.skills.categories(skills, add_skills, skills.length, cat_ids, false);
  logging.infoFP(LOG_NAME, 'runGoal', dialog.user.profile, `Found ${categories.length} categories`);
  const matches = await scoreCandidateCategories(categories, dialog.me);
  logging.infoFP(LOG_NAME, 'runGoal', dialog.user.profile, `Recommending ${matches ? matches.length : 0} categories`);

  if (matches && matches.length) {
    /*const valid_ids = goals ? flatten(goals.filter(g => g.deleted && g.courses)
      .map(g => g.courses.filter(c => !c.ignore && !c.completed).map(c => c.id))) : [];
    const course_ids = goals ? flatten(goals.filter(g => g.courses)
      .map(g => g.courses.map(c => c.id)))
      .filter(id => !valid_ids.includes(id)) : [];*/

    const cat_goals = matches.map(category => new Goal({
      id: uuid(),
      category: {
        id: category.id,
        manual: true,
        skills: category.skills,
        weights: category.weights,
        label: category.label,
        score: category.score,
      },
    }));

    //const cat_goals = await dialog.goals.recommend(matches, course_ids, dialog.group_host?.courses);
    logging.infoFP(LOG_NAME, 'runGoal', dialog.user.profile, `Recommended ${cat_goals ? cat_goals.length : 0} goals`);
    if (cat_goals && cat_goals.length) { // && cat_goals[0].courses && cat_goals[0].courses.length) {
        /*const deleted = goals.find(g => g.category.id === cat_goals[0].category.id);
        if (deleted) await dialog.goals.delete(deleted);*/
        if (dialog.context.create.label) cat_goals[0].title = dialog.context.create.label;
        const goal = await dialog.goals.save(cat_goals[0]);
        if (dialog.context.create) dialog.context.create.saved = goal;
    } else dialog.context.create.empty_type = GlobalType.Course;
    // } else dialog.context.create.duplicate_type = EntityType.Goal;
  } else dialog.context.create.empty_type = EntityType.Goal;
}

async function runAnalysis(dialog: Dialog) {
  const skills = dialog.context.create.skills;
  const filters = dialog.context.create.filters;

  if (skills?.length) {
    const title  = `${skills.map(s => _.startCase(s)).join(', ')}`;

    const has_cats: Uid[] = [];
    const target_skills: Category[] = [];
    for(const skill of skills) {
      const add_skills = await dialog.skills.expand(skill);
      const add_cats = await dialog.skills.categories([skill], add_skills.map(s => s.toLowerCase()), 1, has_cats, false)
      if(add_cats?.length) {
        has_cats.push(add_cats[0].id);
        add_cats[0].id = uuid();
        add_cats[0].color = Object.values(Color)[target_skills.length % 10]
        target_skills.push(add_cats[0]);
      }
    }


    const candidates = dialog.getLastRepliesType(EntityType.Person);
    const categories = candidates && candidates.length ? await dialog.skills.peopleCategories(candidates) : undefined;

    // TODO smarter about count
    target_skills.forEach(t => t.count = candidates.length / target_skills.length);
    const analysis = await dialog.analyses.create(new Analysis({
      title, 
      categories: categories ? categories.map(embedCategory) : null, 
      filters, 
      target_skills, 
      candidates
    }));

    if(dialog.context.create) dialog.context.create.saved = analysis;
  }
}

function runAction(dialog: Dialog) {
  //check previous dialog for filter
  logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, 'Running create');

  if (!dialog.context.create) { // && !dialog.isProcessing()) 
    dialog.reset('create');
    return;
  }

  const skill_orgs = dialog.context.create.skills;
  const filters = dialog.context.create.filters;

  const stub_title  = skill_orgs && skill_orgs.length ? `${skill_orgs.map(s => _.startCase(s)).join(', ')}` : `${localeDowMonthDay(new Date(), dialog.user.locale, dialog.user.timeZone)}`;

  logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Skills ${JSON.stringify(skill_orgs)} filters ${JSON.stringify(filters)}`);

  if (dialog.checkTopic(Topics.CREATE_COPY)) {
    dialog.setTopic(Topics.CREATE);
    const candidates = dialog.getLastRepliesType(EntityType.Person);
   
    switch(dialog.context.create.create_type) {
      case EntityType.Ask:
        dialog.runAsync('create', async () => {
          const title = '';
          const ask = await dialog.asks.create(new Ask({title, filters, candidates}));
          if(dialog.context.create) dialog.context.create.saved = ask;
        });
        break;

      case EntityType.Analysis:
        if (skill_orgs) dialog.runAsync('create', () => runAnalysis(dialog));
        break;
      
      case EntityType.Goal:
        if (dialog.context.create && ( dialog.context.create.saved || dialog.context.create.empty_type || dialog.context.create.duplicate_type)) break;

        if(dialog.context.create.skills && dialog.context.create.skills.length) {
        logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Run goals ${JSON.stringify(dialog.context.create.skills)}`);
          dialog.context.create.pending = lang.create.GOALS_PENDING.slice();
          dialog.runAsync('create', () => runGoal(dialog));
        } else {
          logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Prompt`);
          dialog.setTopic(Topics.CREATE_PROMPT);
        }
        break;
      
      case EntityType.Plan:
        if (skill_orgs?.length) {
          dialog.runAsync('create', async () => {
            let title = stub_title; // `${stub_title} Development Plan`;
            const add_skills = await dialog.skills.group(skill_orgs);
            const categories = await dialog.skills.categories(skill_orgs, add_skills, 1, [], false);
            let skills = skill_orgs;
            if (categories && categories.length) {
              // title = categories[0].label;
              skills = _.uniq([...skills, ...categories[0].skills]);
            }
            const plan = await data.plans.create(dialog.group_host.id, new Plan({title, skills, course_filter: [], assigned: [], unassigned: []}));
            if(dialog.context.create) dialog.context.create.saved = plan;
          });
        }
        break;
    }
  }

  const curr_dialog = dialog.currentDialog();
  if (!curr_dialog || !curr_dialog.replies || !curr_dialog.replies.length) {
    // const stub_title = `from ${localeDowMonthDay(new Date(), dialog.user.locale, dialog.user.timeZone)}`;
    switch(dialog.context.create.create_type) {
      case EntityType.Ask:
        dialog.runAsync('create', async () => {
          const title = '';
          const ask = await dialog.asks.create(new Ask({title}));
          if(dialog.context.create) dialog.context.create.saved = ask;
        });
        break;

      case EntityType.Analysis:
        if (dialog.context.create && (!dialog.context.create.filters || !dialog.context.create.filters.length)) {
          dialog.setTopic(Topics.CREATE_PROMPT);
          break;
        } else dialog.setTopic(Topics.CREATE);

        dialog.runAsync('create', () => runAnalysis(dialog));
        break;

      case EntityType.Goal:
        if (dialog.context.create && ( dialog.context.create.saved || dialog.context.create.empty_type || dialog.context.create.duplicate_type)) break;

        if (dialog.context.create && (!dialog.context.create.filters || !dialog.context.create.filters.length)) {
          logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Prompt no filters`);
          dialog.setTopic(Topics.CREATE_PROMPT);
        } else if(dialog.context.create.skills && dialog.context.create.skills.length) {
          if (dialog.checkTopic(Topics.CREATE_INPUT)) {
            logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Input new skills`);
            dialog.runAsync('create', async () => {
              const now = new Date();
              const me = new Person(dialog.me);
              dialog.context.create.skills.map(value => saveOneTypeValue(me.tags, new Tag(TagType.skill, value, 100, now)));
              await dialog.people.save(me);
            });

            if (dialog.isDoneProcessing()) {
              logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Clear skills and prompt`);
              dialog.context.create.skills = undefined;
              dialog.context.create.label = undefined;
              dialog.setTopic(Topics.CREATE_PROMPT);
            }
          } else if(!dialog.isProcessing()) {
            logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Run goals with skills ${JSON.stringify(dialog.context.create.skills)}`);
            dialog.setTopic(Topics.CREATE);
            dialog.context.create.pending = lang.create.GOALS_PENDING.slice();
            dialog.runAsync('create', () => runGoal(dialog)); 
          }
        }
        break;

      case EntityType.Plan:
        if (dialog.context.create && (!dialog.context.create.filters || !dialog.context.create.filters.length)) {
          dialog.setTopic(Topics.CREATE_PROMPT);
          break;
        } else dialog.setTopic(Topics.CREATE);

        dialog.runAsync('create', async () => {
          let title = stub_title; // `${stub_title} Development Plan`;
          const add_skills = await dialog.skills.group(skill_orgs);
          const categories = await dialog.skills.categories(skill_orgs, add_skills, 1, [], false);
          let skills = skill_orgs;
          if (categories && categories.length) {
            // title = categories[0].label;
            skills = _.uniq([...skills, ...categories[0].skills]);
          }
          const plan = await data.plans.create(dialog.group_host.id, new Plan({title, skills, course_filter: [], assigned: [], unassigned: []}));
          if(dialog.context.create) dialog.context.create.saved = plan;
        });
        break;
    }
  }

  if(dialog.context.create.saved) {
    dialog.addReplies(dialog.context.create.saved);
    // dialog.clearPing();
  } // else dialog.quickPing();
}

function setPrompt(dialog: Dialog) {
  if (dialog.checkTopic(Topics.CREATE_PROMPT)) {
    // dialog.clearPing();
    switch(dialog.context.create.create_type) {
      case EntityType.Analysis:
        dialog.addPrompt(lang.create.CREATE_PROMPT_ANALYSIS, true);
        dialog.addHint(lang.create.ANALYSIS_HINT);
        break;
      case EntityType.Goal:
        if (dialog.me.skills.filter(s => !parsers.mask(s)).length < 12) {
          const curr_dialog = dialog.currentDialog();
          if (curr_dialog && curr_dialog.topic === Topics.DISCOVER) {
            dialog.addPrompt(lang.create.GOALS_MORE_SKILLS, true);
            dialog.addHint(lang.create.GOAL_SKILLS_HINT);
            dialog.setTopic(Topics.CREATE_INPUT);
          } else {
            dialog.newDialog();
            if (!dialog.filters) dialog.filters =[];
            dialog.filters.push({conditions: [{att: EntityType.Person, rel: Relation['='], value: ['self']}]});
            dialog.setTopic(Topics.DISCOVER);
            dialog.nextTopic(Topics.CREATE_PROMPT);
            // dialog.internalPing();
            dialog.reRun();
          }
        } else {
          const replies = dialog.getLastRepliesType(EntityType.Person);
          dialog.addPrompt(lang.create.CREATE_PROMPT_GOALS, true);
          if (replies && replies.length && replies[0].self) dialog.addInfo(replies);
          dialog.addHint(lang.create.GOAL_HINT);
        }
        break;
      case EntityType.Plan:
        dialog.addPrompt(lang.create.CREATE_PROMPT_PLAN, true);
        dialog.addHint(lang.create.PLAN_HINT);
    }
    return;
  }

  const analyses = dialog.getRepliesType(EntityType.Analysis, true);
  const asks = dialog.getRepliesType(EntityType.Ask, true);
  const goals = dialog.getRepliesType(EntityType.Goal, true);
  const plans = dialog.getRepliesType(GlobalType.Plan, true);

  if (asks && asks.length) {
    const ask = asks[0];
    const ask_info: AskInfo = askInfo(ask);
    dialog.addInfo(ask_info, true);
    dialog.setOpen(ask_info);
    dialog.setHide(1);
  } else if (analyses && analyses.length) {
    const analysis = analyses[0];
    const analysis_info = analysisInfo(analysis);
    dialog.lastPrompt('create', lang.create.CREATE_EDIT, {open: analysis_info, sticky: true});
    dialog.addInfo(analysis_info);
  } else if (goals && goals.length) {
    const goal = goals[0];
    const goal_info = goalInfo(goal);
    dialog.lastPrompt('create', lang.create.NEW_GOAL, {clear: true, open: goal_info, hide: 30000});
    dialog.addInfo(goal_info);
  } else if(plans &&plans.length) {
    const plan = plans[0];
    const plan_info = planInfo(plan, {});
    dialog.lastPrompt('create', lang.create.NEW_PLAN, { clear: true, open: plan_info, hide: 30000});
    dialog.addInfo(plan_info);
  } else if(dialog.context.create.duplicate_type) {
    switch(dialog.context.create.duplicate_type) {
      case EntityType.Goal:
        dialog.newDialog();
        dialog.lastPrompt('create', lang.create.DUPLICATE_GOAL);
        dialog.setTopic(Topics.CREATE_PROMPT);
        break;
      default:
        dialog.setHide(1);
    }
  } else if(dialog.context.create.empty_type) {
    switch(dialog.context.create.empty_type) {
      case EntityType.Goal:
        dialog.newDialog();
        dialog.lastPrompt('create', lang.create.EMPTY_GOAL);
        dialog.setTopic(Topics.CREATE_PROMPT);
        dialog.context.create = { create_type: EntityType.Goal };
        break;
      case GlobalType.Course:
        dialog.newDialog();
        dialog.lastPrompt('create', lang.create.EMPTY_COURSES);
        dialog.context.create = { create_type: EntityType.Goal };
        dialog.setTopic(Topics.CREATE_PROMPT);
        break;
      default:
        dialog.setHide(1);
    }
  } else if(dialog.context.create.pending && dialog.context.create.pending.length) {
    const curr_dialog = dialog.currentDialog();
    if (!curr_dialog || SECONDS(curr_dialog.date, 9.4) < new Date()) {
      const prompt = dialog.context.create.pending.splice(0,1)[0];
      dialog.context.create.pending.push(prompt);
      dialog.addPrompt(prompt, true, true, true);
    }
  }
}

export default {
  name: lang.create.CREATE_NAME,
  description: lang.create.CREATE_DESCRIPTION,
  examples: [],
  reserved: lang.create.RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: undefined,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);