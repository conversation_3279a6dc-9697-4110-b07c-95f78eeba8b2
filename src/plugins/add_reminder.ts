import _ from 'lodash';

import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Message, Person, Task } from '../types/items';
import { Plugin } from '../types/plugins';
import { AuthLevel, EntityType } from '../types/shared';

import { createRawMessage, draftMessageLink, messageLink } from '../utils/format';
import { taskInfo } from '../utils/info';
import parsers from '../utils/parsers';

import personInfo from './person_info';

const topics = [
  TOPIC('ADD_REMINDER'),
  TOPIC('REMINDER_ADDED'),
  TOPIC('REMINDER_CONTEXT'),
  TOPIC('FOLLOW_UP'),
  // TOPIC('MEET'),
  TOPIC('MESSAGE_ADDED'),
]

function keywordMatch(actions, message, _raw_message) {
  return !parsers.checkKeyword(message, lang.add_reminder.ADD_REMINDER_IGNORE, true) && parsers.checkKeyword(message, lang.add_reminder.ADD_REMINDER_KWDS);
}

function setAction(dialog: Dialog, message, raw_message) {
  if (dialog.message.link) return false;
  if (dialog.message.ping && (!dialog.context.reminder || !dialog.context.reminder.saving)) return false;

  let context = null;
  if (isActive(dialog)) context = raw_message;
  else context = parsers.extract(lang.add_reminder.CONTEXT, raw_message);

  if (context && context.length) dialog.addAction(ActionType.RAW, context);

  if (!isActive(dialog)) {
    if (parsers.checkKeyword(message, lang.add_reminder.FOLLOWUP_KWD, true, true)) dialog.setTopic(Topics.FOLLOW_UP);
    // else if (parsers.checkKeyword(message, lang.add_reminder.MEET_KWD, true, true)) dialog.setTopic(Topics.MEET);
    else dialog.setTopic(Topics.ADD_REMINDER);
  }

  return true;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function draftFollowup(dialog: Dialog): Partial<Message> {
  const events = dialog.getLastRepliesType(EntityType.Event);
  const title = dialog.context.reminder.title;
  const found_people = dialog.context.reminder.people;
  return lang.add_reminder.FOLLOWUP_DRAFT(events && events.length? events[0] : undefined, title, found_people, dialog.user);
}

function draftMeeting(dialog: Dialog) {
  const title = dialog.context.reminder.title;
  const found_people = dialog.context.reminder.people;
  return lang.add_reminder.MEET_DRAFT(title, found_people, dialog.user);
}

function createTask(dialog: Dialog): Task {
 const task = new Task({ title: dialog.context.reminder.title });
  let due_date = null;

  if (dialog.context.reminder.date) {
    due_date = new Date(dialog.context.reminder.date.start);
    if (dialog.context.reminder.date.day) due_date.setUTCHours(8 + dialog.message.offset / 60);
  }

  const found_people = dialog.context.reminder.people;
  if (found_people.length) {
    const people = [];
    for (const index in found_people) {
      const person = found_people[index];
      if (!person.self) people.push({ id: person.id, displayName: person.displayName, network: person.network, photos: person.photos });
    }

    // notes = notes.concat(Array.from(new Set(people)));
    task.people = people;
  }

  if (due_date) task.due = new Date(due_date);

  return task;
}

function runAction(dialog: Dialog) {
  const title = dialog.actionValues(ActionType.RAW);
  const act_names = dialog.actionValues(ActionType.ENTITY).map(e => e.name);
  const act_people = dialog.actionValues(ActionType.PERSON);
  const date = dialog.actionValues(ActionType.DATE);

  if (!dialog.context.reminder) {
    dialog.context.reminder = {
      people: [],
      act_people,
      act_names,
      title: title.length ? title[0].trim() : null,
      date: date.length ? date[0] : null,
      meeting: dialog.checkTopic(Topics.MEET),
    };
  }


  if (dialog.checkTopic(Topics.REMINDER_CONTEXT)) {
    const bool = dialog.getActions(ActionType.BOOL);
    if (bool && bool.length && bool[0].context === dialog.message.message) {
      if(bool[0].value) {
        dialog.context.reminder.prompted = true;
      } else {
        dialog.context.reminder.title = '';
        dialog.setTopic(dialog.currentDialog().next_topic);
      }
    } else {
      const context = dialog.actionValues(ActionType.RAW).join('\n');
      if (dialog.context.reminder.title) dialog.context.reminder.title += context;
      else dialog.context.reminder.title = context;
      dialog.setTopic(dialog.currentDialog().next_topic);
    }
  }

  let task: Task;
  let message: Partial<Message>;


  if (!dialog.context.reminder.saving) {
    let found_people = dialog.context.reminder.act_people;
    if (!found_people) dialog.getRepliesType(EntityType.Person);
    if (!found_people.length) found_people = dialog.getLastRepliesType(EntityType.Person);
    dialog.context.reminder.people = _.uniqBy(dialog.context.reminder.people.concat(found_people.map(p => new Person({
      id: p.id,
      displayName: p.name,
      comms: p.comms ? p.comms.filter(c => c.type === 'mail').map(c => c.link.slice(7)) : [],
    }))), 'id');

    if (dialog.context.reminder.act_names.length) {
      dialog.clearActions();

      return personInfo.findPersonPrompt(dialog, dialog.context.reminder.act_names.pop());
    }

    if (dialog.context.reminder.title !== undefined && dialog.context.reminder.title !== null) {
      switch(dialog.topic) {
        case Topics.FOLLOW_UP:
          message = draftFollowup(dialog);
          break;
        case Topics.MEET:
          message = draftMeeting(dialog);
          break;
        default: 
          task = createTask(dialog);
          break;
          
      }
    } else {
      dialog.nextTopic();
      dialog.setTopic(Topics.REMINDER_CONTEXT);
      return;
    }
  }

  if (dialog.context.reminder.saving) {
    if (dialog.context.reminder.saved_task) {
      dialog.addReplies(dialog.context.reminder.saved_task, Topics.REMINDER_ADDED);
      dialog.clearContext('reminder');
    } else if (dialog.context.reminder.saved_message) {
      dialog.addReplies(dialog.context.reminder.saved_message, Topics.MESSAGE_ADDED)
      dialog.clearContext('reminder');
    } // else { dialog.quickPing(); }
  } else if (task) {
    dialog.context.reminder.saving = true;
    dialog.runAsync('reminder', async () => {
      const t = await dialog.tasks.create(task);
      if (dialog.context.reminder) {
        dialog.context.reminder.saved_task = t;
      }
    });
    // dialog.quickPing();
  } else if(message) {
    dialog.context.reminder.saving = true;

    if (dialog.isAuthenticated(AuthLevel.Email)) {
      dialog.runAsync('reminder', async () => {
        const draft = await dialog.messages.create(dialog.me, dialog.context.reminder.people, message.subject, message.raw);
        if (dialog.context.reminder) {
          if (draft) dialog.context.reminder.saved_message = draft; 
        }
      }) //.catch(err => dialog.asyncError(err));
      // dialog.quickPing();
    } else {
      const draft = createRawMessage(dialog.context.reminder.people, dialog.me, dialog.me.email, message.subject, message.raw);
      dialog.addReplies(new Message(draft), Topics.MESSAGE_ADDED);
      dialog.clearContext('reminder');
      dialog.setTopic(Topics.MESSAGE_ADDED);
    }
  }

  return true;
}

// not found person returns here...fix or handle that
function setPrompt(dialog: Dialog) {
  switch(dialog.topic) {
    case Topics.REMINDER_CONTEXT:
      if (dialog.context.reminder.prompted) dialog.addPrompt(lang.add_reminder.REMINDER_LISTEN, true, false, true);
      else if(dialog.context.reminder.meeting) dialog.addPrompt(lang.add_reminder.REMINDER_MEET_CONTEXT, true, false, true);
      else dialog.addPrompt(lang.add_reminder.REMINDER_CONTEXT, true, false, true);
      break;
    case Topics.FOLLOW_UP:
      break;
    case Topics.MEET:
      break;
    case Topics.MESSAGE_ADDED:
      if (!dialog.context.reminder) {
        const messages = dialog.getRepliesType(EntityType.Message);
        if (messages && messages.length) {
          let draft;
          if (messages[0].id) draft = draftMessageLink(dialog.user.provider, dialog.user.email, messages[0]);
          else draft = messageLink(messages[0].raw);
          // TODO: support sending drafts
          dialog.lastPrompt('reminder', lang.add_reminder.DRAFT(draft));
        }
      }
      break;
    default:
      if (!dialog.context.reminder) {
        const new_task = dialog.getRepliesType(EntityType.Task);
        if (new_task.length) {
          const task_info = taskInfo(new_task[0], false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
          task_info.added = true;

          dialog.addInfo(task_info);
          dialog.setHide();
          dialog.reset();
        } else {
          dialog.addPrompt(lang.add_reminder.REMINDER_PROMPT, true);
          dialog.addAnswers(lang.defaultanswers.NEVERMIND);
          // dialog.clearPing();
          dialog.setSticky();
        }
        break;
      }
  }
}

export default {
  name: lang.add_reminder.REMINDER_NAME,
  description: lang.add_reminder.REMINDER_DESC,
  examples: [], // lang.add_reminder.REMINDER_EG,
  reserved: lang.add_reminder.REMINDER_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  // shortcuts: lang.add_reminder.REMINDER_SHORTCUT,
  shortcuts: () => { return null; },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
