import Dialog, { /*MIN_BG_PING,*/ TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Plugin } from '../types/plugins';

import { MINUTES } from '../utils/datetime';
import logging from '../utils/logging';

const LOG_NAME = 'plugins.ping';
const PING_NAME = 'Ping';
const PING_DESC = 'Force client to check back in a few seconds';

TOPIC('PING');

function keywordMatch(actions, message, raw_message) {
  if (raw_message == null || raw_message.length === 0) return true;
  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  // if (!dialog.message.ping || dialog.message.ping < MIN_BG_PING || !dialog.isAuthenticated() || dialog.topic === Topics.PING || 
  if (dialog.isProcessing()) return false;

  if (!dialog.isMaskedTopic()) {
    dialog.addAction(ActionType.CMD, Topics.PING);
    dialog.setTopic(Topics.NOOP); // true);
  }

  const now = new Date();
  const curr_dialog = dialog.currentDialog();
  let last_date = now;
  if (curr_dialog) last_date = new Date(curr_dialog.date);
  if (dialog.message.ping) logging.infoF(LOG_NAME, 'setAction', `Ping at ${now} from ${last_date}`);
  if (MINUTES(last_date, 10) < now) dialog.setTopic(Topics.WHATS_NEXT);

  return false;
}

function runAction(dialog: Dialog) {
  // special accelerated ping (due to redirect)
  dialog.clearDialog();
  // dialog.quickPing();
}

export default {
  name: PING_NAME,
  description: PING_DESC,
  examples: [],
  reserved: [],
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive: d => {
    return !!d.message.ping && !d.isProcessing();
  },
  runAction,
  setPrompt: d => {
    return false;
  },
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(Topics.PING, module.exports.default);
