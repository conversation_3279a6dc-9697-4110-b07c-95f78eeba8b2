import lang from '../lang';
import Dialog, { TOPIC, Topics } from '../session/dialog';
import { Plugin } from '../types/plugins';

const SLACK_NAME = 'Slack';
const SLACK_DESC = 'Connect to Slack';

const topics = [
  TOPIC('SLACK_ADDED'),
  TOPIC('SLACK_WELCOME'),
];

function keywordMatch(actions, message) {
  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  return true;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function runAction(dialog: Dialog) {
  return true;
}

function setPrompt(dialog: Dialog) {
  switch (dialog.topic) {
    case Topics.SLACK_WELCOME: return dialog.lastPrompt('init', lang.slack.WELCOME);
    case Topics.SLACK_ADDED: return dialog.lastPrompt('init', lang.slack.ADDED);
  }
}

export default {
  name: SLACK_NAME,
  description: SLACK_DESC,
  examples: [],
  reserved: [],
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default);

