import { default as client, default as lang } from '../lang';

import { ActionType } from '../types/globals';
import { Event, Message, Person } from '../types/items';
import { Plugin } from '../types/plugins';
import { AuthLevel, EntityType, Relation } from '../types/shared';

import { localeDate } from '../utils/format';
import { mostRecent, nextUp } from '../utils/funcs';
import { promptInfo } from '../utils/info';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import personInfo from './person_info';

TOPIC('LOOKUP_WHEN');

function keywordMatch(actions, message, raw_message) {
  if (parsers.checkKeyword(message, client.lookup_when.LOOKUP_KWD, true)) return true;
  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  dialog.setTopic(Topics.LOOKUP_WHEN);

  // is a person involved?
  // var people = dialog.actionValues(funcs.actionTypes.ENTITY).map(e => e.name);

  return true;
}

function isActive(dialog) {
  return dialog.checkTopic(Topics.LOOKUP_WHEN);
}

function runAction(dialog: Dialog) {
  const act_people = dialog.actionValues(ActionType.ENTITY).map(e => e.name);
  let entityType = dialog.actionValues(ActionType.ENTITY_TYPE);
  let time = dialog.actionValues(ActionType.RANGE);

  // if there's any act_people, based on time, find matching entities
  if (!dialog.context.when) {
    dialog.clearContext();
    dialog.context.when = {};
    dialog.context.when.act_people = act_people ? act_people : [];
    dialog.context.when.entityType = entityType;
    dialog.context.when.time = time;
  }

  if (dialog.context.when.act_people.length) {
    // const curr_dialog = dialog.currentDialog();

    // first time through, go to find person then come back to person info
    if (act_people.length) return personInfo.findPersonPrompt(dialog);

    let foundPeople = dialog.getRepliesType(EntityType.Person);
    let entities = [];
    let att = null;

    time = dialog.context.when.time;
    entityType = dialog.context.when.entityType;

    if (time !== null && time.length) {
      for (const index in time) {
        if (time[index] === Relation['^']) att = 'start';
        else if (time[index] === Relation['$']) att = 'end';
      }
    }

    // figure out what entities to search, and the right attributes
    if (entityType !== null && entityType.length) {
      switch (entityType[0]) {
        case EntityType.Event:
          for (const index in foundPeople) {
            const events = dialog.cache.events.filter(e => e.people && e.people.find(p => p.id === foundPeople[index].id));
            entities = entities.concat(events);
          }
          if (att === null) att = 'start';
          break;
        case EntityType.Task:
          for (const index in foundPeople) {
            const tasks = Object.values(dialog.cache.tasks).filter(t => t.people && t.people.find(p => p.id === foundPeople[index].id));
            entities = entities.concat(tasks);
          }
          if (att === null) att = 'due';
          break;
        case EntityType.Message:
          if (!dialog.isAuthenticated(AuthLevel.Email)) break;
          for (const index in foundPeople) {
            const messages = dialog.cache.messages.filter(m => 
              (m.sender && m.sender.id === foundPeople[index].id) ||
              (m.recipient && m.recipient.find(p => p.id === foundPeople[index].id)));
            entities = entities.concat(messages);
          }
          att = 'received';
          break;
        case EntityType.Location:
          break;
        case EntityType.Person:
          break;
      }
    }

    // default to most recent, switch on time
    let time_type = Relation['~'];
    if (time !== null && time.length) time_type = time[0];

    let match_event = null;
    switch (time_type) {
      case Relation['~']:
        match_event = mostRecent(entities, att);
        break;
      case Relation['!']:
        match_event = nextUp(entities, att);
        break;
    }

    if (match_event) foundPeople = foundPeople.concat([match_event]);

    dialog.addReplies(foundPeople);
  } else {
    // TODO: rethink how this works
    // search by time domain, filter based on event type
    if (entityType !== null && entityType.length) {
      const curr_dialog = dialog.currentDialog();
      const context = curr_dialog.replies.length ? curr_dialog.replies[0] : null;
      switch (entityType[0]) {
        case EntityType.Event:
          // check last Dialog for person context
          if (context && context.type === EntityType.Person) dialog.setTopic(Topics.PERSON_INFO);
          break;
        case EntityType.Task:
          break;
        case EntityType.Message:
          // check last Dialog for person context
          if (context && context.type === EntityType.Person) dialog.setTopic(Topics.PERSON_INFO);
          break;
        case EntityType.Location:
          // check last Dialog for person context
          if (context && context.type === EntityType.Person) dialog.setTopic(Topics.PERSON_INFO);
          break;
        case EntityType.Person:
          // check last Dialog for event context
          // if(context && context.type == EntityType.Event) dialog.setTopic(Topics.PERSON_INFO);
          break;
      }
    }
  }

  return true;
}

function setPrompt(dialog: Dialog) {
  if (!dialog.isAuthenticated(AuthLevel.Organizer)) {
    dialog.addPrompt(lang.init.REG_REQUIRED(dialog.user.provider), true);
    dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
    dialog.setTopic(Topics.DEFAULT);
    return;
  }

  const curr_dialog = dialog.currentDialog();
  const prompt = [];

  if (curr_dialog !== null && curr_dialog.replies.length) {
    // TODO - check on setting to default topic
    dialog.setTopic(Topics.DEFAULT);

    const people: Person[] = [];
    const events: Event[] = [];
    const messages: Message[] = [];

    for (const index in curr_dialog.replies) {
      const entity = curr_dialog.replies[index];
      switch (entity.type) {
        case EntityType.Person:
          people.push(entity);
          break;
        case EntityType.Event:
          events.push(entity);
          break;
        case EntityType.Message:
          messages.push(entity);
          break;
      }
    }

    if (events.length) {
      const event = events[0];
      // calendar

      let time_type = Relation['~'];

      const time = dialog.context.when.time;
      if (time !== null && time.length) time_type = time[0];

      let details = `On ${localeDate(event.start, dialog.user.locale, dialog.user.timeZone)}`;
      if (event.location) details += ` at ${event.location}`;

      const event_people = event.people ? event.people.filter(p => !p.self).map(p => peopleUtils.getPersonInfo(dialog.user.profile, dialog.me, p)) : [];
      if (time_type === Relation['~']) {
        if (event_people.length) {
          prompt.push(promptInfo(`${details} you met about`, event.link, event.title, event_people));
          dialog.addAnswers(lang.lookup_when.FINISHED_ANSWERS);
        } else {
          prompt.push(promptInfo(`${details} you had an event on`, event.link, event.title));
          dialog.addAnswers(lang.lookup_when.EVENT_ANSWERS);
        }
      } else {
        if (event_people.length) {
          prompt.push(promptInfo(`${details} you're meeting about`, event.link, event.title, event_people));
          dialog.addAnswers(lang.lookup_when.MEETING_ANSWERS);
        } else prompt.push(promptInfo(`${details} you have an event on`, event.link, event.title));
      }
    } else if (messages.length) {
      const message = messages[0];
      // email
      const lastemail = localeDate(message.received, dialog.user.locale, dialog.user.timeZone);
      const people_info = people.map(p => peopleUtils.getPersonInfo(dialog.user.profile, dialog.me, p));

      prompt.push(promptInfo(lang.whats_next.LAST_EMAIL(lastemail), lang.whats_next.MAIL_LINK(message.id), message.subject, people_info));
      dialog.addAnswers(lang.whats_next.MESSAGE_ANSWERS(message.draft));
    } else if (people.length) {
      const person = people[0];
      prompt.push(`I couldn't find anything about ${person.displayName}`);
    } else prompt.push("I found something that I can't understand.");
  } else prompt.push("Sorry, I couldn't find anything.");

  dialog.addPrompt(prompt);

  dialog.clearContext('when');

  dialog.setTopic(Topics.DEFAULT);
}

export default {
  name: client.lookup_when.LOOKUP_NAME,
  description: client.lookup_when.LOOKUP_DESC,
  examples: [], // lang.lookup_when.LOOKUP_EG,
  reserved: client.lookup_when.LOOKUP_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(Topics.LOOKUP_WHEN, module.exports.default, true);
