import lang from '../lang';
import Dialog, { TOPIC, Topics } from '../session/dialog';
import { Plugin } from '../types/plugins';
import parsers from '../utils/parsers';

const TOS_NAME = 'Terms of Service';
const TOS_DESC = 'View the AskFora terms of service';
const TOS_EG = []; // "Terms of service" ];

TOPIC('TOS');

function keywordMatch(actions, message, raw_message) {
  if (parsers.checkKeyword(message, lang.tos.TOS_KWDS, true)) return true;
  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  if (!(dialog.isAuthenticated() && dialog.isMaskedTopic()) && !dialog.message.ping) {
    dialog.setTopic(Topics.TOS);
    return true;
  }
  return false;
}

function setPrompt(dialog: Dialog) {
  dialog.addPrompt(lang.tos.TERMS, true);
  dialog.setTopic(Topics.DEFAULT);
}

export default {
  name: TOS_NAME,
  description: TOS_DESC,
  examples: TOS_EG,
  reserved: lang.tos.TOS_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive: () => { return false; },
  runAction: (d) => { },
  setPrompt,
  shortcuts: lang.tos.TOS_SHORTCUT,
} as Plugin;

Dialog.registerPlugin(Topics.TOS, module.exports.default);
