import { v4 as uuid } from 'uuid';

import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Task } from '../types/items';
import { Plugin, TakeNotesPluginState } from '../types/plugins';
import { EntityType, PersonInfo, TaskInfo } from '../types/shared';

import { HOURS, MINUTES } from '../utils/datetime';
import { localeDate } from '../utils/format';
import parsers from '../utils/parsers';

import addReminder from './add_reminder';

const NOTES_NAME = 'Take Notes';
const NOTES_DESC = 'Record notes with references to people or add reminders';

const topics = [TOPIC('TAKE_NOTES'), TOPIC('EVENT_NOTES'), TOPIC('SAVE_NOTES'), TOPIC('REMINDER_NOTES')];

function keywordMatch(actions, message, _raw_message) {
  if (parsers.checkKeyword(message, lang.take_notes.NOTES, true) || parsers.checkKeyword(message, lang.take_notes.SAVE, true)) return true;
  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  if (dialog.message.link) return false;
  if (dialog.checkTopic(Topics.TAKE_NOTES)) {
    if (parsers.checkKeyword(message, lang.take_notes.SAVE, true)) {
      if (dialog.actionValues(ActionType.TASK).length) {
        dialog.setTopic(Topics.SAVE_NOTES);
        return true;
      }
    } else {
      const note = parsers.extract(lang.take_notes.CONTEXT, raw_message);
      if (note && note.length) {
        dialog.addAction(ActionType.RAW, note);
        return true;
      }
    }

    // create a reminder
    if (!parsers.checkKeyword(message, ['what'], true) && parsers.checkKeyword(message, lang.take_notes.ADD_REMINDER)) {
      dialog.setTopic(Topics.REMINDER_NOTES);
      return true;
    }
  } else if (dialog.checkTopic(Topics.EVENT_NOTES)) {
    return true;
  } else if (parsers.checkKeyword(message, lang.take_notes.NOTES, true)) {
    dialog.setTopic(Topics.TAKE_NOTES);
    return true;
  } else if (parsers.checkKeyword(message, lang.take_notes.SAVE, true)) {
    if (dialog.actionValues(ActionType.TASK).length) {
      dialog.setTopic(Topics.SAVE_NOTES);
      return true;
    }
  } else if (dialog.checkTopic(Topics.SAVE_NOTES) && dialog.context.record_note) return dialog.context.record_note.saving;

  return false;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function runAction(dialog: Dialog) {
  const act_entities = dialog.message.command ? [] : dialog.actionValues(ActionType.ENTITY);
  const act_people = dialog.actionValues(ActionType.PERSON);
  const notes: TaskInfo[] = dialog.actionValues(ActionType.TASK);

  if (dialog.checkTopic(Topics.SAVE_NOTES)) {
    if (notes && notes.length && notes[0].type === EntityType.Task) {
      const user_note = notes[0];
      dialog.context.record_note = user_note;
      const current_note = dialog.cache.tasks[user_note.id];

      dialog.context.record_note.saving = true;
      if (current_note && current_note.id) {
        current_note.due = user_note.due;
        current_note.title = user_note.title;
        current_note.notes = user_note.notes;
        current_note.people = user_note.people ? user_note.people.map(p => { return {id: p.id} }) : [];
        dialog.runAsync('record_note', async () => {
          if (current_note.people && current_note.people.length) {
            const people = await dialog.people.byId(current_note.people.map(p => p.id));
            current_note.people = people.map(p => { return {
              id: p.id,
              displayName: p.displayName,
              nickName: p.nickName,
              network: p.network,
              photos: p.photos,
            }});
          }

          await dialog.tasks.update(current_note);
          if (dialog.context.record_note) dialog.context.record_note.done = true;
        });
      } else {
        const new_note = new Task({
          created: user_note.created,
          due: user_note.due,
          id: user_note.id,
          title: user_note.title,
          notes: user_note.notes,
          people: user_note.people ? user_note.people.map(p => { return { id: p.id} }) : [],
        });

        dialog.runAsync('record_note', async () => {
          if (new_note.people && new_note.people.length) {
            const people = await dialog.people.byId(new_note.people.map(p => p.id));
            new_note.people = people.map(p => { return {
              id: p.id,
              displayName: p.displayName,
              nickName: p.nickName,
              network: p.network,
              photos: p.photos,
            }});
          }

          await dialog.tasks.create(new_note);
          if (dialog.context.record_note) dialog.context.record_note.done = true;
        });
      }
      dialog.context.record_note.edit = false;
      dialog.context.record_note.updated = true;
    }

    if (dialog.context.record_note.saving) {
      if (dialog.context.record_note.done) {
        dialog.addReplies(dialog.context.record_note);
        dialog.clearContext('record_note');
        // dialog.clearPing();
      } /*else {
        dialog.quickPing();
      }*/
    }
    return;
  }

  // if (dialog.message.ping) return;
  let record: TakeNotesPluginState = dialog.context.record_note;
  if (!record) {
    const now = new Date();
    let people: Partial<PersonInfo>[] = [];

    for (const person of act_people) {
      people.push(person);
    }

    for (const entity of act_entities) {
      if (entity.people && entity.people.length) {
        people = people.concat(entity.people.filter(p => p.id !== dialog.me.id && !p.self).map(p => dialog.getPersonInfo(p)));
      }
    }

    const new_notes = dialog.actionValues(ActionType.RAW);

    record = new TakeNotesPluginState();
    record.id = uuid();
    record.added = true;
    record.people = people;
    record.created = new Date();

    const time_title = localeDate(record.created, dialog.user.locale, dialog.user.timeZone);
    record.title = new_notes && new_notes.length ? new_notes[0] : time_title;

    for (const item in dialog.cache.events) {
      const event = dialog.cache.events[item];
      const start = new Date(event.start);
      const end = new Date(event.end);
      // event starts in 10 min or ended 10 min ago and isn't all day
      if (HOURS(start, 6) > end && now > MINUTES(start, -10) && now < MINUTES(end, 10)) {
        record.events.push(event);
      }
    }
    record.events.sort((a, b) => {
      const a_start = new Date(a.start);
      const b_start = new Date(b.start);
      if (a_start !== b_start) return a_start > b_start ? -1 : 1;
      if (a.people && b.people && a.people.length !== b.people.length) return a.people.length > b.people.length ? -1 : 1;
      if (b.notes && b.notes.length) return 1;
      return -1;
    });

    dialog.context.record_note = record;
    dialog.context.record_note.saving = true;

    dialog.setTopic(Topics.SAVE_NOTES);
    dialog.runAsync('record_note', async () => {
      const new_note = new Task({
        created: record.created,
        due: record.due,
        id: record.id,
        title: record.title,
        notes: record.notes,
        people: record.people ? record.people.map(p => { return { id: p.id} }) : [],
      });

      await dialog.tasks.create(new_note);
      if (dialog.context.record_note) dialog.context.record_note.done = true;
    });
  }

  if (dialog.checkTopic(Topics.REMINDER_NOTES)) {
    const curr_dialog = dialog.currentDialog();
    if (curr_dialog && curr_dialog.replies.length && curr_dialog.replies[0].next_topic === Topics.REMINDER_ADDED) {
      const task = curr_dialog.replies[0];
      if (task.people) {
        record.people = record.people ? record.people.concat(task.people) : task.people;
      }

      const title = task.title && task.title.length ? `${task.title}\n` : '';
      const notes = task.notes && task.notes.length ? `${task.notes}\n` : '';
      record.notes = `${record.notes}\n${title}${notes}`;
      dialog.setTopic(Topics.TAKE_NOTES);
    } else return addReminder.runAction(dialog);
  } else {
    let people: Partial<PersonInfo>[] = [];
    if (dialog.checkTopic(Topics.EVENT_NOTES)) {
      const bool = dialog.actionValues(ActionType.BOOL);
      const event = record.events.shift();
      if (bool && bool.length) {
        if (bool[0] && event) {
          const title = event.title && event.title.length ? `${event.title}\n` : '';
          const notes = event.notes && event.notes.length ? `${event.notes}\n` : '';
          record.notes = `${record.notes ? record.notes + '\n' : ''}${title}${notes}`;
          people = people.concat(event.people ? event.people.filter(p => p.id !== dialog.me.id).map(p => dialog.getPersonInfo(p)) : []);
        }
        record.events = [];
      }
    }

    if (notes && notes.length) {
      record = notes[0];
      record.events = [];
      dialog.context.record_note = record;
    }

    record.people = record.people ? record.people.concat(people) : people;
  }

  return true;
}

function setPrompt(dialog: Dialog) {
  if (dialog.message.ping) return;

  switch (dialog.topic) {
    case Topics.EVENT_NOTES:
    case Topics.TAKE_NOTES:
      if (dialog.context.record_note.events && dialog.context.record_note.events.length) {
        dialog.addPrompt(lang.take_notes.EVENT_PROMPT(dialog.context.record_note.events[0].title), true);
        if (dialog.context.record_note.events.length > 1) dialog.addAnswers(lang.take_notes.EVENT_ANSWERS);
        else dialog.addAnswers(lang.take_notes.EVENT_ANSWERS_LAST);
        dialog.setTopic(Topics.EVENT_NOTES);
      } else {
        const note: TaskInfo = dialog.context.record_note;
        note.edit = true;
        dialog.addInfo(note, true);
        dialog.setOpen(note);
        // dialog.addHint(lang.take_notes.PEOPLE_HINT);
        dialog.setHide();
        // dialog.noPing();
        dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.SAVE_NOTES:
      if (!dialog.context.record_note) {
        const tasks = dialog.getRepliesType(EntityType.Task);
        if (tasks && tasks.length) {
          const task = tasks[0] as TaskInfo;
          dialog.addInfo(task, true);
          dialog.setOpen(task);
        }
        dialog.reset();
        dialog.setHide();
      }
      break;
  }
}

export default {
  name: NOTES_NAME,
  description: NOTES_DESC,
  examples: [], // lang.take_notes.NOTES_EG,
  reserved: lang.take_notes.NOTES_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  // shortcuts: lang.take_notes.NOTES_SHORTCUT,
  shortcuts: () => { return null; },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
