import config from '../config';
import data from '../data';
import lang from '../lang';

import DataCache from '../session/data_cache';
import Dialog, { TOPIC, Topics } from '../session/dialog';
import ForaUser from '../session/user';

import { ActionType, TemplateType } from '../types/globals';
import { Message, Person, Project, projectPerson } from '../types/items';
import { Plugin } from '../types/plugins';
import { AuthContext, AuthLevel, AuthProviders, EntityType, NotificationType, PersonInfo, ProjectCandidateState, ProjectInfo } from '../types/shared';

import { draftMessageLink, messageLink } from '../utils/format';
import { mapURL, slimEntity } from '../utils/funcs';
import { promptInfo } from '../utils/info';
import logging from '../utils/logging';
import notify, { NotifyType } from '../utils/notify';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import personInfo from './person_info';

import { AuthProvider } from '../auth/auth_provider';
import { searchCandidates } from '../routes/update';
import Learn from '../skills/learn';

const LOG_NAME = 'plugins.Introduce';
const INTRO_NAME = 'Introduce';
const INTRO_DESC = 'Make an introduction';

const topics = [
  TOPIC('MAKE_INTRO'),
  TOPIC('PROMPT_INTRO'),
  TOPIC('PROMPT_CONNECTION'),
  TOPIC('MAKE_CONNECTION'),
  TOPIC('ACCEPT_CONNECTION'),
  TOPIC('VIEW_CONNECTION'),
  TOPIC('INVALID_INTRO'),
  TOPIC('INVALID_CONNECTION'),
  TOPIC('AUTH_INTRO'),
  TOPIC('AUTH_CONNECTION'),
  TOPIC('ASK_CONNECTION'),
];

function keywordMatch(actions, message, _raw_message) {
  if ((parsers.checkKeyword(message, lang.introduce.INTRO, true) || parsers.checkKeyword(message, lang.introduce.CONNECTION_INTRO)) && 
      // ((parsers.checkKeyword(message, lang.introduce.CONJUNCTION) || Dialog.checkActions(actions, [ActionType.ENTITY, ActionType.PERSON]))  &&
      !parsers.checkKeyword(message, lang.introduce.NOT_INTRO)) {
    return true;
  }
  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  if (dialog.message.link &&
    !(dialog.context && dialog.context.make_intro && dialog.context.make_intro.intro_id )) return false;
  
  if (dialog.checkTopic(Topics.ACCEPT_CONNECTION) && parsers.checkKeyword(message, lang.introduce.VIEW_PROFILE)) {
    dialog.setTopic(Topics.VIEW_CONNECTION);
    return true;
  }

  if (isActive(dialog)) return true;

  const apeople = dialog.actionValues(ActionType.PERSON);
  const upeople = dialog.actionValues(ActionType.ENTITY).filter(e => e.type === EntityType.UnresolvedPerson);

  if (apeople.length === 0 && upeople.length === 0) {
    if (dialog.isAuthenticatedNonGuest()) {
      dialog.setTopic(Topics.ASK_CONNECTION);
      return true;
    }
  }

  if (apeople.length === 1 || upeople.length === 1) {
    if (message === lang.introduce.CONNECTION_INTRO) {
      if (dialog.isAuthenticatedNonGuest()) dialog.setTopic(Topics.PROMPT_CONNECTION);
      else dialog.setTopic(Topics.AUTH_CONNECTION);
    } else dialog.setTopic(Topics.PROMPT_INTRO);
    return true;
  }

  const names = upeople.filter(e => e.people && e.people.length).map(e => e.name);
  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Found names ${names}`);
  if (names.length >= 2) {
    const index_a = raw_message.indexOf(names[0]);
    const index_b = raw_message.indexOf(names[1]);

    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `${names[0]} at ${index_a} ${names[1]} at ${index_b}`);
    if ((index_a > 0 || (upeople[0].people && upeople[0].people.length > 0)) && (index_b > 0 || (upeople[1].people && upeople[1].people.length > 0))) {
      let check_words = null;
      if (index_a === -1) check_words = new RegExp(`${names[1]}`);
      else if (index_b === -1) check_words = new RegExp(`${names[0]}`);
      else if (index_a < index_b) check_words = new RegExp(`${names[0]} .*${lang.introduce.CONJ_REG} .*${names[1]}`);
      else check_words = new RegExp(`${names[1]} .*${lang.introduce.CONJ_REG} .*${names[0]}`);

      if (raw_message.match(check_words)) {
        dialog.setTopic(Topics.MAKE_INTRO);
        dialog.addAction(ActionType.RAW, parsers.extract(lang.introduce.CONTEXT, raw_message));
        return true;
      }
    }
  }

  if (dialog.checkTopic(Topics.ACCEPT_CONNECTION)) return true;

  if (!dialog.isAuthenticated(AuthLevel.Organizer) || apeople.length || upeople.length) {
    dialog.setTopic(Topics.MAKE_INTRO);
    return true;
  }

  return false;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function _acceptConnection(dialog: Dialog) {
  const bool = dialog.actionValues(ActionType.BOOL);
  if (bool && bool.length) {
    if (bool[0]) {
      // make the intro
      dialog.context.make_intro.connecting = true;
      dialog.runAsync('make_intr', async () => {
        if (!dialog.context.make_intro && !dialog.context.make_intro.intro_id) {
          dialog.setTopic(Topics.INVALID_INTRO);
          return;
        }

        const intro = await dialog.people.acceptIntroduction(dialog.context.make_intro.intro_id);

        if (!intro) {
          dialog.setTopic(Topics.INVALID_INTRO);
          return;
        }

        let person;

        //check for existing contact
        const matches = await dialog.people.findByComms(dialog.context.make_intro.people[0].comms);

        if (matches && matches.length) {
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'acceptConnection',  dialog.user.profile, `Found matches for ${dialog.context.make_intro.people[0].comms}: ${JSON.stringify(matches.map(m => m.id))}`);
          for (const match of matches) {
            if (person) peopleUtils.mergePeople(person, match, true);
            else person = new Person(match);
          }
          peopleUtils.mergePeople(person, dialog.context.make_intro.people[0]);
        } else person = new Person(dialog.context.make_intro.people[0]);

        person.network = false;
        dialog.context.make_intro.people[0] = await dialog.people.create(new Person(dialog.context.make_intro.people[0]));

        const user = new ForaUser(intro.requested_by);
        await data.users.init(user, false);
        const data_cache = new DataCache(user, null, [], `connect_${user.profile}`);
        await data_cache.loadCache();

        let new_person;
        const umatches = await data.people.byAttributeComms(user, dialog.context.make_intro.people[1].comms);
        if (umatches && umatches.length) {
          for (const umatch of umatches) {
            if (new_person) peopleUtils.mergePeople(new_person, umatch, true);
            else new_person = new Person(umatch);
          }

          peopleUtils.mergePeople(new_person, dialog.context.make_intro.people[1]);
        } else {
          new_person = new Person(dialog.context.make_intro.people[1])
          new_person.id = null;
        }
        new_person.network = false;
        new_person = await data.people.newPerson(user, new_person, dialog.cache); 

        // learn online about this person and create/update google contact
        if (!config.isEnvOffline()) {
          const learn = new Learn(user);
          new_person = await learn.learnPerson(new_person);
        }


        dialog.context.make_intro.people[1] = new_person;
        await data_cache.cachePerson(new_person);
        await data_cache.saveCache();

        let job = mapURL('', dialog.getGroup());
        let job_name = '';

        if (intro.project) {
          job = mapURL(`/job/${intro.project}`, dialog.getGroup());

          // add new user as candidate and invite them
          const project = await dialog.projects.get(new Project({id: intro.project}), true);
          if (project) {
            job_name = project.title;

            let candidate;
            if (project.candidates) {
              candidate = project.candidates.find(c => c.askfora_id === dialog.user.profile);
              if (!candidate) candidate = project.candidates.find(c => c.id === dialog.me.id);
              if (!candidate) candidate = project.candidates.find(c => c.id === new_person.id);
              if (!candidate) candidate = project.candidates.find(c => c.comms && c.comms.includes(dialog.user.email));
            } else project.candidates = [];

            if (candidate) {
              candidate.id = dialog.me.id;
              candidate.askfora_id = dialog.user.profile;
              candidate.comms = [dialog.user.email];
            } else {
              project.me_candidate = projectPerson(project, {
                id: dialog.me.id,
                displayName: dialog.me.displayName,
                comms : [dialog.user.email],
                askfora_id: dialog.user.profile,
                ready: dialog.user.hasAccount(AuthProviders.Stripe) || dialog.user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment),
                tags: dialog.me.tags,
                network:false,
                state:ProjectCandidateState.INVITED,
              });

              // check for a contract and whether contracting as group
              if (dialog.user.loaded_groups) {
                for (const index in dialog.cache.contracts) {
                  const contract = dialog.cache.contracts[index];
                  if (project.client.comms.includes(contract.client_email)) {
                    for (const group of Object.values(dialog.user.loaded_groups)) {
                      if (group.name.toLowerCase() === contract.contractor_name.toLowerCase() ||
                          group.company_name.toLowerCase() === contract.contractor_name.toLowerCase()) {
                            project.me_candidate.as_group = group.id;
                            break;
                      }
                    }
                    break;
                  }
                }
              }

              const add_candidate = projectPerson(project, project.me_candidate);
              add_candidate.comms = [dialog.user.email];
              add_candidate.id = null;
              project.candidates.push(add_candidate);
            }

            project.last_update = new Date();
            project.last_activity = project.last_update;

            await dialog.projects.create(project, false);
            await dialog.projects.updateClient(project);

            // rerun search, including this user's contacts
            searchCandidates(intro.requested_by, dialog.context.make_intro.people[0], intro.project, [dialog.user.profile]);
          }
        }

        const template = intro.project ? null : TemplateType.Networking;

        // const url = funcs.mapURL(`${config.get('NOTIFICATION_URL')}`, dialog.getGroup());
        const subject = lang.introduce.CONNECTION_ACCEPTED_SUBJECT;
        const message = lang.introduce.CONNECTION_ACCEPTED_EMAIL(dialog.context.make_intro.people);

        /* const webpush = {
          notification: {
            tag: intro.id,
            title: lang.introduce.NOTIFY_CONNECT_ACCEPT,
            body: subject,
            click_action: url
          },
        }; */

        const email = {
          rcpts: [
            { Name: dialog.context.make_intro.people[0].displayName, Email: user.email },
            { Name: dialog.context.make_intro.people[1].displayName, Email: dialog.user.email }
          ],
          subject,
          message,
        };

        const variables = {
          to_first_name: dialog.me.displayName.split(' ')[0],
          from_first_name: dialog.context.make_intro.people[0].displayName.split(' ')[0],
          job_name,
          job,
        };

        await notify(user, { email, type: NotificationType.Connection, variables, template }, NotifyType.PushAndEmail, dialog.group_host, false);
        
        dialog.context.make_intro.connection = true;
      });
    } else dialog.clearContext('make_intro');
  } else if (!dialog.context.make_intro.requested) {
    dialog.context.make_intro.requested = true;
  }
  
  return true;
}

function runAction(dialog: Dialog) {
  const names = [];
  let note = dialog.actionValues(ActionType.RAW);
  let curr_dialog = dialog.currentDialog();
  const ids = [];
  const resolve: Partial<PersonInfo>[][] = [];

  switch(dialog.topic) {
    case Topics.VIEW_CONNECTION:
    case Topics.AUTH_INTRO:
    case Topics.INVALID_INTRO:
      // dialog.clearPing();
      return;

    case Topics.ASK_CONNECTION:
      return;

    case Topics.PROMPT_CONNECTION:
      if (!ids.length) ids.push(dialog.me.id);
      if (!names.length) names.push(dialog.me.displayName);
      break;

    case Topics.AUTH_CONNECTION:
      if (dialog.context.init && dialog.context.init.profile) {
        dialog.context.make_intro = {
          names: [dialog.context.init.profile.displayName],
          resolve: null,
          ids: [`profile/${dialog.context.init.profile.vanity}`],
          people: null, // links to people
          context: ['Fora recommended we connect'],
          draft: null,
          connection: true,
          project_id: null,
        };
        // dialog.clearPing();
        return;
      } else {
        note = ['Fora recommended we connect'];
      }
      break;

    default:
      //noop, keep going
      break;
  }

  const act_people = dialog.actionValues(ActionType.PERSON);
  if (act_people && act_people.length && act_people[0].type === EntityType.Person && act_people[0].id) {
    const person_info = act_people[0] as Partial<PersonInfo>;
    if (person_info.id) ids.push(person_info.id);
    else if (person_info.name) resolve.push([{name: person_info.name}]);
    if (person_info.name) names.push(person_info.name);
  }

  if (ids.length < 2 && names.length < 2 && resolve.length < 2) {
    const entities = dialog.actionValues(ActionType.ENTITY);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Entities ${JSON.stringify(entities)}`);
    for (const entity of entities) {
      if (entity.people && entity.people.length) {
        if (entity.people.filter(p => p.id).length) {
          const resolve_set = [];
          for (const person of entity.people) {
            if (!ids.includes(person.id)) {
              ids.push(person.id);
              if (person.name) names.push(person.name);
              else names.push(entity.name);
              resolve_set.push(person);
            }
          }
          if (resolve_set.length) resolve.push(resolve_set);
        } else {
          names.push(entity.name);
          resolve.push([{name: entity.name}]);
        }
      }
    }
  }

  if (logging.isDebug(dialog.user.profile)) {
    logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Act people ${JSON.stringify(names)}`);
    logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Ids ${JSON.stringify(ids)}`);
  }

  if (!dialog.context.make_intro) {
    if (names.length /*+ ids.length*/ < 2 && !dialog.checkTopic([Topics.PROMPT_INTRO, Topics.AUTH_CONNECTION])) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Bailing - not enough people`);
      dialog.addReplies(Dialog.emptyItems(3));
      return true;
    }

    const [project] = dialog.actionValues(ActionType.PROJECT) as ProjectInfo[];
    dialog.context.make_intro = {
      names: names.filter(n => {
        return !resolve.find(r => r.find(p => p.name === n));
      }).splice(0, 2), // names we don't need to resolve, first two only
      resolve,
      ids: ids.filter(id => {
        return !resolve.find(r => r.find(p => p.id === id));
      }).splice(0,2),
      people: null, // links to people
      context: project && dialog.cache.projects[project.id] ? [dialog.cache.projects[project.id].skills] : note, // context for note
      draft: null,
      connection: dialog.checkTopic([Topics.PROMPT_CONNECTION, Topics.AUTH_CONNECTION]),
      project_id: project ? project.id : null,
    };

    dialog.clearDialog();
  }

  if (dialog.checkTopic(Topics.PROMPT_INTRO)) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Prompt Intro ${JSON.stringify(dialog.context.make_intro.names)} with ${JSON.stringify(names)}`);
    if (dialog.context.make_intro && dialog.context.make_intro.names.length === 1 && names.length) {
      dialog.context.make_intro.names.push(names[0]);
      dialog.setTopic(Topics.MAKE_INTRO);
    } else return;
  }

  if (curr_dialog && curr_dialog.next_topic === Topics.FOUND_PERSON) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Found Person ${curr_dialog.replies.length} ids ${dialog.context.make_intro.ids ? dialog.context.make_intro.ids.length : 0} names ${dialog.context.make_intro.names ? dialog.context.make_intro.names.length : 0}`);
    if (curr_dialog.replies.length) {
      if (!dialog.context.make_intro.ids.includes(curr_dialog.replies[0].id)) dialog.context.make_intro.ids.push(curr_dialog.replies[0].id);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Ids ${JSON.stringify(dialog.context.make_intro.ids)}`);
      // dialog.context.make_intro.ids = dialog.context.make_intro.ids;
      dialog.clearDialog();
    } else {
      // person not found
      dialog.clearDialog();
      dialog.addReplies(Dialog.emptyItems(3));
      // dialog.clearPing();
      dialog.clearContext('make_intro');
      return true;
    }
  }

  // skip looking up by name people we know
  /* if (dialog.context.make_intro.names.length == 2 && dialog.context.make_intro.ids.length > 0) {
    dialog.context.make_intro.names.splice(0, dialog.context.make_intro.ids.length);
  } */

  // find each person in turn
  if (dialog.context.make_intro.resolve && dialog.context.make_intro.resolve.length) {
    dialog.clearActions();

    const find_person = dialog.context.make_intro.resolve.shift();

    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Find Person ${find_person} ids ${JSON.stringify(dialog.context.make_intro.ids)} names ${JSON.stringify(dialog.context.make_intro.names)} resolve ${JSON.stringify(dialog.context.make_intro.resolve)}`);
    return personInfo.findPersonPrompt(dialog, find_person);
  }

  if (dialog.context.make_intro.ids && dialog.context.make_intro.ids.length === 2) {
    if (!dialog.context.make_intro.loading) {
      dialog.context.make_intro.loading = true;
      dialog.runAsync('make_intro', async () => {
        const people = await dialog.people.byId(dialog.context.make_intro.ids);
        if (dialog.context.make_intro) {
          people.map(p => slimEntity(p));
          dialog.context.make_intro.people = people;
          dialog.context.make_intro.ids = [];
        }
      });
    }

    if (!dialog.context.make_intro.people) {
      return false;
    }
  }

  if (dialog.checkTopic(Topics.ACCEPT_CONNECTION)) return _acceptConnection(dialog);

  if (!dialog.context.make_intro.draft && dialog.context.make_intro.people) {
    if (dialog.context.make_intro.people.length) {
      if (dialog.context.make_intro.connection) {
        const bool = dialog.actionValues(ActionType.BOOL);
        if (bool && bool.length && bool[0] && !dialog.context.make_intro.requested) {
          dialog.runAsync('make_intro', async () => {
            const cache_project = dialog.context.make_intro.project_id ? dialog.cache.projects[dialog.context.make_intro.project_id] : null;
            const project = cache_project ? await dialog.projects.get(cache_project) : null;
            const intro_to_user = await data.users.globalByVanity(dialog.context.make_intro.people[1].vanity);
            if (intro_to_user) {
              const intro = await dialog.people.createIntroduction(intro_to_user.id, project);
              const url = mapURL(`${config.get('NOTIFICATION_URL')}/intro/${intro.id}`, dialog.getGroup());
              const subject = lang.introduce.CONNECTION_SUBJECT;
              const message = lang.introduce.CONNECTION_EMAIL(dialog.context.make_intro.people, dialog.context.make_intro.context, url);
              const template = project ? null : TemplateType.Networking;

              /* const webpush = {
                notification: {
                  tag: intro.id,
                  title: lang.introduce.NOTIFY_CONNECT_REQUEST,
                  body: subject,
                  click_action: url,
                },
              }; */

              const email = {
                rcpts: [{ Name: dialog.context.make_intro.people[1].displayName, Email: intro_to_user.email }],
                subject,
                message,
              };

              const variables = {
                to_first_name: dialog.context.make_intro.people[1].displayName.split(' ')[0],
                from_first_name: dialog.me.displayName.split(' ')[0],
                from_full_name: dialog.me.displayName,
                job_name: project ? project.title: '',
                intro_link: url,
              };

              await notify(new ForaUser(intro_to_user.id), { email, type: NotificationType.Introduction, variables, template }, NotifyType.EmailOnly, dialog.group_host, false);
            } else logging.warnFP(LOG_NAME, 'runAction', dialog.user.profile, `Can't make intro to person without vanity ${JSON.stringify(dialog.context.make_intro.people[1])}`);
            dialog.context.make_intro.requested = true;
          });
          dialog.clearDialog(true);
        } else if(!dialog.isProcessing() || dialog.isDoneProcessing()) return;
        else if(!bool) {
          dialog.setTopic(Topics.PROMPT_CONNECTION);
          return;
        }
      } else {
        const message = lang.introduce.INTRO_EMAIL(dialog.me.nickName, dialog.context.make_intro.people, dialog.context.make_intro.context);
        const people = dialog.context.make_intro.people;

        curr_dialog = dialog.addReplies(dialog.context.make_intro.people);
        dialog.context.make_intro.people = [];

        dialog.runAsync('makee_intro', async () => {
          const draft: Message = await dialog.messages.create(dialog.me, people, lang.introduce.INTRO_SUBJECT, message);
          if (draft && dialog.context.make_intro.draft === null) {
            dialog.context.make_intro.draft = draft;
          }
        });
      }
    }

    return false;
  }

  if (curr_dialog && curr_dialog.replies.length === 2) {
    // check for draft
    if (dialog.context.make_intro.draft) {
      const message = dialog.context.make_intro.draft;
      dialog.appendReplies(message);
      return true;
    }
  }

  // fail
  dialog.addReplies(Dialog.emptyItems(3));
  return true;
}

function draftInfo(user: ForaUser, people: PersonInfo[], message: Message) {
  let url = null;
  if (message.id) url = draftMessageLink(user.provider, user.email, message);
  else url = messageLink(message.raw);
  return promptInfo(lang.introduce.DRAFT, url, lang.introduce.EDIT_SEND, people);
}

function setPrompt(dialog: Dialog) {
  switch(dialog.topic) {
    case Topics.ASK_CONNECTION:
      dialog.setTopic(Topics.MAKE_INTRO);
      dialog.addPrompt(lang.introduce.PROMPT_BOTH, true);
      return;
    case Topics.PROMPT_CONNECTION:
      if (dialog.context.make_intro) {
        if (dialog.context.make_intro.people && dialog.context.make_intro.people.length > 1) {
          dialog.clearDialog();
          dialog.addPrompt(lang.introduce.CONFIRM_CONNECTION(dialog.context.make_intro.people[1]));
          const person_info = dialog.getPersonInfo(dialog.context.make_intro.people[1]);
          dialog.addInfo(person_info);
          dialog.setOpen(person_info);
          dialog.addAnswers(lang.introduce.CONFIRM_ANSWERS);
          dialog.setSticky();
          // dialog.clearPing();
          dialog.setTopic(Topics.MAKE_CONNECTION);
          return;
        } else if (!dialog.context.make_intro.loading) dialog.reset('make_intro');
      } else dialog.reset('make_intro');
      return;

    case Topics.MAKE_CONNECTION:
      if (dialog.context.make_intro.requested) {
        dialog.clearDialog();
        dialog.lastPrompt('make_intro', lang.introduce.CONNECTION_REQUESTED(dialog.context.make_intro.people[1]));
      } // else dialog.quickPing();
      return;

    case Topics.ACCEPT_CONNECTION:
      if (dialog.context.make_intro) {
        if ((!dialog.isProcessing(false) || dialog.isDoneProcessing()) && !dialog.context.make_intro.connection) {
          dialog.clearDialog();
          dialog.addPrompt(lang.introduce.CONFIRM_ACCEPT_CONNECTION(dialog.context.make_intro.people[0]));
          const person = dialog.getPersonInfo(dialog.context.make_intro.people[0]);
          person.focus = EntityType.Person;
          dialog.addInfo(person);
          dialog.addAnswers(lang.introduce.CONFIRM_ANSWERS_PROFILE);
          dialog.setSticky();
          // dialog.clearPing();
          return;
        }

        if (dialog.context.make_intro.connection) {
          dialog.clearDialog();
          const person = dialog.getPersonInfo(dialog.context.make_intro.people[0]);
          person.added = true;
          dialog.lastPrompt('make_intro', lang.introduce.CONNECTION_SUCCESS(person), {clear: true});
          dialog.addInfo(person);
        } else if (dialog.context.make_intro.connecting) {
          dialog.clearDialog();
          dialog.addPrompt(lang.introduce.CONFIRM_ACCEPT_WAIT);
          // dialog.quickPing();
          dialog.context.make_intro.connecting = false; // don't repeat
        } // else dialog.quickPing();
      } else {
        dialog.clearDialog();
        dialog.reset();
      }
      return;

    case Topics.VIEW_CONNECTION:
      if (dialog.context.make_intro) {
        if ((!dialog.isProcessing() || dialog.isDoneProcessing()) /*getPing()*/ && !dialog.context.make_intro.connection) {
          dialog.clearDialog();
          dialog.addPrompt(lang.introduce.CONFIRM_CONNECTION(dialog.context.make_intro.people[0]));
          const person_info = dialog.getPersonInfo(dialog.context.make_intro.people[0]);
          person_info.focus = EntityType.Person;
          dialog.addInfo(person_info);
          dialog.addAnswers(lang.introduce.CONFIRM_ANSWERS_PROFILE);
          dialog.setSticky();
          // dialog.clearPing();
          dialog.setTopic(Topics.ACCEPT_CONNECTION);
          return;
        }
      } else {
        dialog.clearDialog();
        dialog.reset();
      }
      return;

    case Topics.AUTH_INTRO:
      if (!dialog.message.ping) {
        const providers = AuthProvider.clientAuthSet(AuthContext.Intro, dialog.group_host);
        dialog.addPrompt(lang.introduce.AUTH_INTRO(providers.map(p => p.name)), true);
        dialog.addPrompt(lang.init.PERMISSIONS_ORGANIZER());
        dialog.addAnswers(providers.map(p => p.name));
        for (const provider of providers) {
          const qrs = AuthProvider.contextPermissions(AuthContext.Intro);
          qrs.push(lang.init.REGISTER_REPLY_LINK(provider));
          dialog.addQuickReply(provider.name, [] /* qrs */, {redirect: provider.url});
        }
        // dialog.clearPing();
        dialog.setSticky();
      }
      return;
    
    case Topics.INVALID_INTRO:
      dialog.addPrompt(lang.introduce.INVALID_INTRO, true);
      dialog.reset('make_intro');
      return;

    case Topics.AUTH_CONNECTION:
      if (!dialog.message.ping) {
        const providers = AuthProvider.clientAuthSet(AuthContext.Connect, dialog.group_host);
        if (dialog.context.init && dialog.context.init.profile) {
          const profile = dialog.getPersonInfo(dialog.context.init.profile);
          profile.vanity = dialog.context.init.vanity;
          dialog.addPrompt(lang.introduce.AUTH_CONNECTION(dialog.context.init.profile, providers.map(p => p.name)), true);
          dialog.addInfo(profile);
        } else dialog.addPrompt(lang.introduce.AUTH_CONNECTION(null, providers.map(p => p.name)), true);
        dialog.addPrompt(lang.init.PERMISSIONS_ORGANIZER());
        dialog.addAnswers(providers.map(p => p.name));
        for (const provider of providers) {
          const qrs = AuthProvider.contextPermissions(AuthContext.Connect);
          qrs.push(lang.init.REGISTER_REPLY_LINK(provider));
          dialog.addQuickReply(provider.name, [] /* qrs */, {redirect: provider.url});
        }
        // dialog.clearPing();
        dialog.setSticky();
      }
      return;
    
    case Topics.INVALID_CONNECTION:
      dialog.addPrompt(lang.introduce.INVALID_CONNECTION, true);
      dialog.reset('make_intro');
      return;

  }

  if (!dialog.isAuthenticated(AuthLevel.Organizer)) {
    dialog.clearDialog();
    dialog.addPrompt(lang.init.REG_REQUIRED(dialog.user.provider), true);
    dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
    dialog.reset('make_intro');
    return;
  }

  if (dialog.checkTopic(Topics.PROMPT_INTRO) && dialog.context.make_intro && 
      dialog.context.make_intro.names && dialog.context.make_intro.names.length == 1) {
    dialog.clearDialog();
    dialog.addPrompt(lang.introduce.PROMPT(dialog.context.make_intro.names[0]), true);
    return;
  }

  const curr_dialog = dialog.currentDialog();

  if (curr_dialog !== null && curr_dialog.replies.length === 3 && curr_dialog.next_topic !== Topics.FOUND_PERSON) {
    const people = dialog.getRepliesType(EntityType.Person);
    const message = dialog.getRepliesType(EntityType.Message);

    if (people.length === 2 && message.length === 1) {
      const people_info = people.map(p => dialog.getPersonInfo(p));
      dialog.addPrompt(draftInfo(dialog.user, people_info, message[0]), true);
    } else dialog.addPrompt(lang.introduce.MISSING, true);

    dialog.reset('make_intro');
  }
}

export default {
  name: INTRO_NAME,
  description: INTRO_DESC,
  examples: lang.introduce.INTRO_EG,
  reserved: lang.introduce.INTRO_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
