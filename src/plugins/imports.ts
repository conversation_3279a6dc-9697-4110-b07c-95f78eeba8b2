
import { TemplateType } from '../types/globals';
import { Person } from '../types/items';
import { ImportsPluginState, Plugin } from '../types/plugins';
import { EntityType, Imports, NotificationType } from '../types/shared';

import * as funcs from '../utils/funcs';
import logging from '../utils/logging';
import notify, { NotifyType } from '../utils/notify';
import parsers from '../utils/parsers';

import data from '../data';
import lang from '../lang';
import { internalImport } from '../routes/imports';
import { internalLearn } from '../routes/learn';
import Dialog, { TOPIC, Topics } from '../session/dialog';

const IMPORTS_NAME = 'Imports';
const IMPORTS_DESC = 'Imports files to create people';

const LOG_NAME = 'plugins.imports';

const topics = [
  TOPIC('IMPORTS'), 
  TOPIC('IMPORTS_ACCOUNT'), 
  TOPIC('IMPORTS_ERROR'), 
  TOPIC('IMPORTS_LIST'), 
  TOPIC('IMPORTS_PROMPT'), 
  TOPIC('IMPORTS_TUTORIAL')
];

function keywordMatch(_actions, message, _raw_message) {
  return parsers.checkKeyword(message, lang.imports.IMPORTS_KWD, true) || parsers.checkKeyword(message, lang.imports.IMPORTS_LIST, true);
}

function setAction(dialog: Dialog, message, _raw_message) {
  if (dialog.message.link) return false;
  if (!dialog.context.imports) dialog.context.imports = new ImportsPluginState();

  if (parsers.checkKeyword(message, lang.imports.IMPORTS_LIST, true)) {
    dialog.setTopic(Topics.IMPORTS_LIST);
  }

  // if (dialog.checkTopic([Topics.IMPORTS_PROMPT, Topics.IMPORTS_TUTORIAL])) {
  // get the import type
  if (!dialog.context.imports.import_type) {
    if (message.length) {
      const standard_import_types = Object.values(Imports);
      if (parsers.checkKeyword(message, standard_import_types)) {
        for (const import_type of standard_import_types) {
          if (message.includes(import_type)) {
            dialog.context.imports.import_type = import_type;
            break;
          }
        }
      } else {
        // if (!Object.values(ImportType).includes(message))
        let found_import = false;
        if (dialog.context.imports.custom_maps) {
          for (const cmap in dialog.context.imports.custom_maps) {
            if (cmap) {
              const custom_map = dialog.context.imports.custom_maps[cmap];
              if (custom_map && custom_map.name && custom_map.name.toLowerCase() === message || cmap.toLowerCase() === message) {
                found_import = true;
                dialog.context.imports.import_type = cmap;
                break;
              }
            }
          }
        }
        if (!found_import && !parsers.checkKeyword(message, lang.imports.ACCOUNT_KWD)) dialog.setTopic(Topics.IMPORTS_ERROR);
      }
    }
    // return true;
  } else if(dialog.checkTopic(Topics.IMPORTS_ACCOUNT) && !dialog.context.imports.account) {
    switch (dialog.context.imports.import_type) {
      case Imports.Facebook:
        try {
          let account;
          if (message.startsWith('http')) {
            const url = new URL(message);
            account = url.pathname.slice(1);
          } else if (message.startsWith('@') || message.startsWith('/')) {
            const m = message.match(/[a-zA-Z0-9_]/);
            if (m) account = message.slice(m.index);
          } else if (message.includes('/')) {
            const i = message.indexOf('/');
            if (i >= 0) account = message.slice(i + '/'.length);
          } else account = message;

          if (account.endsWith('/')) account = account.slice(0, -1)

          if (account && !account.match(/[^a-zA-Z0-9. -_~!$'()*+,;=:@]/)) {
            dialog.context.imports.account = `https://facebook.com/${encodeURI(account.split(' ')[0])}`;
          }
        } catch(e) {
          logging.warnFP(LOG_NAME, 'setAction', dialog.user.profile, `Error parsing linkedin account ${message}`, e);
        }
        break;
      case Imports.LinkedIn:
        try {
          let account;
          if (message.startsWith('http')) {
            const url = new URL(message);
            const i = url.pathname.indexOf('in/');
            if (i >= 0) account = url.pathname.slice(i + 'in/'.length);
          } else if (message.startsWith('in/') || message.startsWith('/in/')) {
            const i = message.indexOf('in/');
            if (i >= 0) account = message.slice(i + 'in/'.length);
          } else if (message.startsWith('@') || message.startsWith('/')) {
            const m = message.match(/[a-zA-Z0-9_]/);
            if (m) account = message.slice(m.index);
          } else account = message;

          if (account.endsWith('/')) account = account.slice(0, -1)

          if (account && !account.match(/[^a-zA-Z0-9. -_~!$'()*+,;=:@]/)) {
            dialog.context.imports.account = `https://linkedin.com/in/${encodeURI(funcs.stripPuncs(account, true).split(' ')[0])}`;
          }
        } catch(e) {
          logging.warnFP(LOG_NAME, 'setAction', dialog.user.profile, `Error parsing linkedin account ${message}`, e);
        }
        break;
      case Imports.iCloud:
        try {
          const emails = parsers.findEmail(message);
          if (emails && emails.length) dialog.context.imports.account = emails[0];
        } catch(e) {
          logging.warnFP(LOG_NAME, 'setAction', dialog.user.profile, `Error parsing iCloud account ${message}`, e);
        }
        break;
      default:
        try {
          const url = new URL(message);
          dialog.context.imports.account = url.href;
        } catch(e) {
          logging.warnFP(LOG_NAME, 'setAction', dialog.user.profile, `Error parsing url ${message}`, e);
        }
    }
  }

  // if (dialog.checkTopic(Topics.IMPORTS_PROMPT) dialog.setTopic(Topics.IMPORTS);
  // }

  if (!isActive(dialog)) {
    if (parsers.checkKeyword(message, lang.imports.ACCOUNT_KWD)) dialog.setTopic(Topics.IMPORTS_ACCOUNT);
    else dialog.setTopic(Topics.IMPORTS);
  }

  return true;
}

function isActive(dialog: Dialog): boolean {
  return dialog.checkTopic(topics);
}

function runAction(dialog: Dialog) {
  if (dialog.topic === Topics.IMPORTS_LIST) {
    if (dialog.message.ping) {
      if (dialog.context.imports) {
        if (dialog.context.imports.docs) {
          dialog.addReplies(dialog.context.imports.docs);
          if (!dialog.context.imports.docs.length) dialog.addReplies(Dialog.emptyItems(1));
          dialog.clearContext('imports');
        }
      } else dialog.addReplies(Dialog.emptyItems(1));
    } else {
      dialog.runAsync('imports', async () => {
        const docs = await data.imports.load(dialog.user);
        if (dialog.context.imports) dialog.context.imports.docs = docs;
      });
    }
    return;
  }

  if (dialog.context.imports && dialog.context.imports.file_id) {
    if (dialog.context.imports.import_type) {
      internalImport(dialog.user, dialog.me, dialog.context.imports.file_id, dialog.context.imports.import_type);
      dialog.clearContext('imports');
      dialog.setTopic(Topics.IMPORTS);
    } else {
      // prompt for file type
      //
      // check for groups with custom import maps
      if (!dialog.context.imports.loading_maps) {
        dialog.context.imports.loading_maps = true;

        if (dialog.context.imports) {
          const custom_maps = {};
          for (const gid in dialog.user.loaded_groups) {
            const group = dialog.user.loaded_groups[gid];
            if (group.import_maps) Object.assign(custom_maps, group.import_maps);
          }
          dialog.context.imports.custom_maps = custom_maps;
        }
      }

      if (!dialog.context.imports.custom_maps) dialog.reRun();

      dialog.setTopic(Topics.IMPORTS_PROMPT);
    }
  } else if(dialog.checkTopic(Topics.IMPORTS_ACCOUNT)) {
    if (dialog.context.imports.import_type && dialog.context.imports.account) {
      let new_me = new Person(dialog.me);
      if (dialog.context.imports.account.startsWith('http')) funcs.saveOne(new_me.urls, dialog.context.imports.account);
      else if(dialog.context.imports.account.includes('@')) funcs.saveOne(new_me.comms, dialog.context.imports.account);
      dialog.runAsync('imports', async () => {
        new_me.learned = [new Date(0)];
        const save_person = await dialog.setSelf(new_me);
        await internalLearn({profile: dialog.user.profile, person: save_person.id});
      });
    }
  } else {
    // give instructions
    dialog.setTopic(Topics.IMPORTS_TUTORIAL);
    if (dialog.context.imports.import_type) {
      const notification = {
        email: { rcpts: [{Name: dialog.me ? dialog.me.displayName : dialog.user.name, Email: dialog.user.email}] },
        type: NotificationType.Import,
        template: dialog.context.imports.import_type as TemplateType,
        variables: { firstname: dialog.me ? dialog.me.nickName : dialog.user.name },
      }
      notify(dialog.user, notification, NotifyType.EmailOnly, dialog.getGroup(), true, false);
    }
  }
}

function setPrompt(dialog: Dialog) {
  const curr_dialog = dialog.currentDialog();
  const import_type = dialog.context.imports ? dialog.context.imports.import_type : null;
  const account = dialog.context.imports ? dialog.context.imports.account : null;

  switch (dialog.topic) {
    case Topics.IMPORTS_LIST:
      if (curr_dialog && curr_dialog.replies && curr_dialog.replies.length && !dialog.message.ping) {
        dialog.newDialog();
        let found = false;
        for (const doc of curr_dialog.replies) {
          if (doc.type === EntityType.Document) {
            found = true;
            dialog.addPrompt(lang.imports.IMPORTS_LIST_DOC(doc));
          }
        }
        if (!found) dialog.addPrompt(lang.imports.IMPORTS_LIST_NONE);
        dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.IMPORTS:
      dialog.lastPrompt('imports', lang.imports.IMPORTS_PROGRESS, { clear: true });
      break;
    case Topics.IMPORTS_PROMPT:
      // TODO: check group for types
      if (!dialog.message.ping) {
        const answers = lang.imports.IMPORTS_TUTORIAL_TYPE_ANSWERS.slice();
        if (dialog.context.imports.custom_maps) {
          for (const cmap in dialog.context.imports.custom_maps) {
            const custom_map = dialog.context.imports.custom_maps[cmap];
            if (!answers.includes(custom_map.name)) answers.push(custom_map.name);
          }
        }
        dialog.addPrompt(lang.imports.IMPORTS_PROMPT_TYPE, true);
        dialog.addAnswers(answers);
      }
      break;
    case Topics.IMPORTS_ERROR:
      dialog.addPrompt(lang.imports.IMPORTS_PROMPT_ERROR, true);
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.IMPORTS_ACCOUNT:
      if (account) {
        dialog.lastPrompt('imports', lang.imports.IMPORTS_ACCOUNT_IMPORTING(import_type), { clear: true, hide: 10000});
        dialog.addInfo(dialog.getPersonInfo(dialog.me));
      } else if (import_type) {
        dialog.addPrompt(lang.imports.IMPORTS_ACCOUNT(import_type), true);
        dialog.addHint(lang.imports.IMPORTS_ACCOUNT_HINT(import_type));
      } else {
        dialog.addPrompt(lang.imports.IMPORTS_ACCOUNT_TYPE, true);
        dialog.addAnswers(lang.imports.IMPORTS_ACCOUNT_TYPE_ANSWERS);
      }
      break;
    case Topics.IMPORTS_TUTORIAL:
      switch (import_type) {
        case Imports.Facebook:
          dialog.lastPrompt('imports', lang.imports.IMPORTS_TUTORIAL_FACEBOOK, { clear: true} );
          break;
        case Imports.iCloud:
          dialog.lastPrompt('imports', lang.imports.IMPORTS_TUTORIAL_ICLOUD, { clear: true} );
          break;
        case Imports.LinkedIn:
          dialog.lastPrompt('imports', lang.imports.IMPORTS_TUTORIAL_LINKEDIN, { clear: true} );
          break;
        default:
          dialog.addPrompt(lang.imports.IMPORTS_TUTORIAL_TYPE, true);
          dialog.addAnswers(lang.imports.IMPORTS_TUTORIAL_TYPE_ANSWERS);
          break;
      }
      break;
  }
}

export default {
  name: IMPORTS_NAME,
  description: IMPORTS_DESC,
  examples: lang.imports.EXAMPLES,
  reserved: [...lang.imports.IMPORTS_KWD, ...lang.imports.IMPORTS_LIST],
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: _a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
