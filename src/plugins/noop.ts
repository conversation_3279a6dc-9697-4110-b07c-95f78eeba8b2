import Dialog, { TOPIC, Topics } from '../session/dialog';
import { Plugin } from '../types/plugins';

const NOOP_NAME = 'Noop';
const NOOP_DESC = 'Do nothing';

TOPIC('NOOP');

export default {
  name: NOOP_NAME,
  description: NOOP_DESC,
  examples: [],
  reserved: [],
  requiresAuth: false,
  keywordMatch: (a, m, r) => {
    return false;
  },
  setAction: (d, m, r) => {
    return false;
  },
  isActive: (dialog: Dialog) => {
    return dialog.checkTopic(Topics.NOOP);
  },
  runAction: (d) => { },
  setPrompt: d => {
    return false;
  },
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(Topics.NOOP, module.exports.default);
