import _ from 'lodash';

import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';
import ForaUser from '../session/user';
import { Topic } from '../types/globals';

import { ActionType } from '../types/globals';
import { IEntity, Message, Person, Project, projectPerson, Tag, Task } from '../types/items';
import { ConnectPluginState, Plugin } from '../types/plugins';
import { AuthLevel, EntityType, PersonInfo, TagType } from '../types/shared';

import { camelCase, draftMessageLink, messageLink } from '../utils/format';
import { arraysIntersect, flatten, saveOne, saveOneTypeValue, slimEntity, } from '../utils/funcs';
import { promptInfo, taskInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

const LOG_NAME = 'plugins.connect';

const CONNECT_NAME = 'Connect';
const CONNECT_DESC = 'Record a new connection';

const CONNECT_RESERVED = [
  'add [someone name phone email at company]',
  // 'meet',
  // 'meeting',
  'met [someone name phone email at company]',
  'learn [about someone]',
  'contact',
];

function CONNECT_SHORTCUT(a) {
  if (a) return { name: 'Add contact', value: 'Add contact ' };
  else return null;
}

const topics = [
  TOPIC('CONNECT'),
  TOPIC('CONNECT_PICK'),
  TOPIC('CONNECT_PROJECT'),
  TOPIC('CONNECT_INFO'),
  TOPIC('SELF_INTRO'),
  TOPIC('CONNECT_REG_REQUIRED'),
  TOPIC('CONNECT_EMAIL_REQUIRED')
];

function keywordMatch(_actions, message, _raw_message) {
  return parsers.checkKeyword(message, lang.connect.INTRO, true);
}

function setAction(dialog: Dialog, message, raw_message) {
  if (dialog.message.link) return false;

  dialog.clearFilters();

  if (dialog.checkTopic(Topics.SELF_INTRO)) return true;
  // if (dialog.message.ping === HANDOFF_PING) dialog.clearPing();

  let names = [];
  let init = false;

  // check for create person context
  if (!dialog.context.connect) {
    init = true;
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Creating new connect context with message '${raw_message}'`);
    dialog.context.connect = new ConnectPluginState();
    dialog.context.connect.message = raw_message;

    // pre-processor includes all caps words in names even if they're first and reserved
    // need to filter those out pf any entities and the message
    const words = raw_message.split(' ');
    if (words.length && words[0].length && parsers.checkKeyword(words[0], lang.connect.INTRO)) {
      // fix entities
      const entities = dialog.actionValues(ActionType.ENTITY);
      for (const entity of entities) {
        const lname = entity.name;
        for (const kwd of lang.connect.INTRO) {
          if (lname.indexOf(kwd) === 0) {
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Name ${lname} matches '${kwd}'`);
            entity.name = entity.name.slice(kwd.length).trim();
            if (entity.name.length === 0) {
              dialog.removeActions(ActionType.ENTITY, entity);
              if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Removing entity with name ${lname}`);
            }
            break;
          }
        }
      }
    }

    // fix message
    for (const kwd of lang.connect.INTRO.slice().sort((a,b) => b.length - a.length)) {
      if (message.indexOf(kwd) === 0) {
        dialog.context.connect.message = dialog.context.connect.message.slice(kwd.length).trim();
        break;
      }
    }
  } else if (raw_message && raw_message.length) dialog.context.connect.message = raw_message;

  if ((dialog.actionValues(ActionType.ENTITY).length  || 
    (parsers.checkKeyword(message, lang.connect.INTRO, true) && dialog.context.connect.message && dialog.context.connect.message.length))
    && !dialog.checkTopic(Topics.CONNECT_PICK)) {
    dialog.context.connect.prompted = false;
    dialog.setTopic(Topics.CONNECT);

    // add name permutations
    names = dialog.actionValues(ActionType.ENTITY).map(e => e.name);
  }


  const new_names = parsers.findNames(dialog.context.connect.message);
  const add_names = [];

  if (new_names) {
    for (let index = 0; index < new_names.length; index++) {
      let check_name = new_names[index];
      if (index < new_names.length - 1) {
        const index_a = dialog.context.connect.message.indexOf(check_name);
        const index_b = dialog.context.connect.message.indexOf(new_names[index + 1]);
        if (index_a + check_name.length + 1 === index_b) {
          check_name += ` ${new_names[index + 1]}`;
          index++;
        }
      }
      if (names.indexOf(check_name) === -1) add_names.push(check_name);
    }

    for (const index in add_names) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Adding names ${add_names}`);
      dialog.addAction(ActionType.ENTITY, { name: add_names[index], type: EntityType.UnresolvedPerson });
    }
  }

  const emails = parsers.findEmail(dialog.context.connect.message.toLowerCase());
  const phones = parsers.findPhone(dialog.context.connect.message, dialog.message.locale);
  const ids = parsers.findId(dialog.context.connect.message);
  const comms = dialog
    .actionValues(ActionType.ENTITY)
    .map(e => e.comms)
    .filter(c => c);

  let matches = comms && comms.length ? flatten(comms.filter(c => c.length)) : [];
  if (emails) matches = matches.concat(emails);
  if (phones) matches = matches.concat(phones);
  if (ids) matches = matches.concat(ids);
  if (matches && matches.length) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Adding comms ${matches}`);
    dialog.addAction(ActionType.COMMS, matches);
  }

  const bool = dialog.getActions(ActionType.BOOL);
  const acomms = dialog.getActions(ActionType.COMMS);
  const context = [...add_names, ...acomms.map(c => c.context).filter(c => c), ...matches, ...init ? lang.connect.CONTEXT : [], ...bool && bool.length ? [bool[0].context] : []];

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Extracting note from ${context}`);

  const note = dialog.checkTopic(Topics.CONNECT_PICK) && bool && bool.length ? null : parsers.extractLast(context, dialog.context.connect.message);
  if (note && note.length) {
    // last change to add a name
    if (!dialog.context.connect.person && !add_names.length) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Adding names ${add_names}`);
      dialog.addAction(ActionType.ENTITY, { name: camelCase(note), type: EntityType.UnresolvedPerson });
    }
    else {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Adding note ${note}`);
      dialog.addAction(ActionType.TASK, note);
    }
  }

  if (!isActive(dialog)) dialog.setTopic(Topics.CONNECT_INFO);

  return true;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function _selfIntro(dialog: Dialog) {
  const bool = dialog.actionValues(ActionType.BOOL);

  if (bool.length && bool[0]) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Sending intro`);
    const people: Person[] = dialog.getRepliesType(EntityType.Person);
    const note: Task[] = dialog.getRepliesType(EntityType.Task);
    if (people.length) {
      const person: Person = people[0];
      const message = lang.connect.MEET_EMAIL(dialog.me.nickName, person.nickName, note[0] ? note[0].notes : null);
      dialog.context.self_intro = { draft: null };

      dialog.runAsync('self_intro', async () => {
        const draft = await dialog.messages.create(dialog.me, person, lang.connect.MEET_SUBJECT, message);
        if (dialog.context.self_intro) {
          if (dialog.context.self_intro.draft == null) dialog.context.self_intro.draft = draft;
        }
      });
      // dialog.quickPing();
      return false;
    }
  } else if (dialog.context.self_intro) {
    if (dialog.context.self_intro.draft) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `Created draft intro`);
      const message = dialog.context.self_intro.draft;
      // message.type = EntityType.Message;
      dialog.appendReplies(message);
      dialog.clearContext('self_intro');
    } else {
      // dialog.quickPing();
      return false;
    }
  }
  return true;
}

function _checkSaving(dialog: Dialog) {
  if (dialog.context.connect.saving) {
    if (dialog.context.connect.done_saving) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'checkSaving', dialog.user.profile, `Done saving`);
      let topic: Topic = Topics.CONNECT;
      if (dialog.context.connect.has_email) {
        if (dialog.context.connect.ignore_draft) {
          if (dialog.context.connect.project) topic = Topics.CONNECT_PROJECT;
          else {
            const curr_dialog = dialog.currentDialog();
            if (curr_dialog && curr_dialog.next_topic) topic = curr_dialog.next_topic;
          }
        }
        else topic = Topics.SELF_INTRO;
      }
      const replies: Partial<IEntity>[] = [dialog.context.connect.saved_person];
      if (dialog.context.connect.saved_note) replies.push(dialog.context.connect.saved_note);
      if (dialog.context.connect.project) replies.push(dialog.context.connect.project);
      dialog.addReplies(replies, topic);
      dialog.clearContext('connect');
      // dialog.clearPing();
    } // else dialog.quickPing();

    return true;
  }

  return false;
}

function _preparePerson(dialog: Dialog) {
  if (dialog.message.ping) return;

  // Look for matching people
  // parse lines/actions for name, org, comms, etc.
  const ents = dialog.actionValues(ActionType.ENTITY);
  const at_names = ents.map(e => e.name);
  const orgs = dialog.actionValues(ActionType.ORG);
  const comms = dialog.actionValues(ActionType.COMMS);
  const notes = dialog.actionValues(ActionType.TASK);

  let person = dialog.context.connect.person;
  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Preparing names: ${at_names} orgs: ${JSON.stringify(orgs)} comms: ${comms} notes: ${notes}`);

  if (!dialog.context.connect.searching && (!dialog.context.connect.candidates || !dialog.context.connect.candidates.length) && 
    (!dialog.message.command || !person)) {
    const names = [];

    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, 'First prepare');

    const proj = dialog.actionValues(ActionType.PROJECT);
    if (proj.length) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, 'Setting project flags, no draft, require email');
      dialog.context.connect.project = proj[0];
      dialog.context.connect.ignore_draft = true;
      dialog.context.connect.require_email = true;
    } else if (!dialog.isAuthenticated(AuthLevel.Organizer)) {
      dialog.context.connect.ignore_draft = true;
    }

    let person_name = { pos: dialog.context.connect.message.length, value: '' };
    if (ents.length) { // && ents[0].type === EntityType.UnresolvedPerson && ents[0].people && ents[0].people.length) {
      ents.sort((a,b) => {
        if (a.type == EntityType.UnresolvedPerson) {
          if (b.type == EntityType.UnresolvedPerson) {
            if (a.people && a.people.length) {
              if (a.id === 'found_people') return -1;
              else if (b.id === 'found_people') return 1;
              if (b.people && b.people.length) return b.people.length - a.people.length;
              return -1;
            }
            return 1;
          }
          return -1;
        } 
        return 1;
      });

      const ent_names = ents.map(e => e.name).filter(n => n && n.length);
      let value = '';
      if (ent_names.length) {
        value = ent_names[0];
        for(const en of ent_names) {
          if (en.startsWith(value)) value = en;
        }
      }

      person_name = { pos: dialog.context.connect.message.length, value };
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Found person name ${JSON.stringify(person_name)}`);
      dialog.context.connect.matches = flatten(ents.filter(e => e.type === EntityType.UnresolvedPerson && e.people).map(e => e.people.map(p => {
        return { id: p.id, displayName: p.name };
      })));
    } 

    if(at_names.length && !dialog.message.command) {
      // look for names that match the message exactly, starting after the first space
      for (const name of at_names) {
        const pos = dialog.context.connect.message.indexOf(name);
        while (names.length && names[names.length -1].pos >= pos) names.splice(-1)
        names.push({ pos, value: name });
      }

      if (names.length) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Parse name candidates ${JSON.stringify(names)}`);

        // get the lowest value index name, anything after that is an org
        for (const name of names) {
          if (name.pos >= 0 && name.pos < person_name.pos) person_name = name;
        }

        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Found person name ${JSON.stringify(person_name)} at lowest postion`);
      } else logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `No name candidates in message ${dialog.context.connect.message}`);
    }

    // names after the person are orgs
    const start_org = person_name.pos + person_name.value.length + 1;
    for (const name of names) {
      if (name.pos > start_org) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Found org ${JSON.stringify(name)}`);
        orgs.push({name:name.value});
      }
    }

    // permute the person name
    const emails = comms ? parsers.findEmail(comms) : [];
    if (!person && (person_name.value.length || emails.length)) {
      person = new Person({
        displayName: person_name.value.length ? person_name.value : null,
        comms: comms.filter(c => c !== dialog.user.email),
      });
      person.tempId();
      slimEntity(person);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Remembering person ${JSON.stringify(person)}`);
      dialog.context.connect.person = person;

      // clear command flag if we got a person and a project
      if (proj.length) dialog.message.command = false;
    }
  }

  if (person && !dialog.checkTopic(Topics.CONNECT_PICK)) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, 'Updating person');
    let re_prompt = false;
    for (const org of orgs) {
      const new_org = saveOneTypeValue(person.tags, new Tag(TagType.organization, org.name, 0, new Date()));
      if (new_org) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Re-prompt after new org ${JSON.stringify(org)}`);
        re_prompt = true;
      }
    }

    for (const comm of comms) {
      if (parsers.findEmail(comm)) dialog.context.connect.has_email = true;
      const new_comm = saveOne(person.comms, comm);
      if (new_comm) {
        if (dialog.checkTopic(Topics.CONNECT_EMAIL_REQUIRED)) dialog.setTopic(Topics.CONNECT);
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Re-prompt after new org ${JSON.stringify(comm)}`);
        dialog.context.connect.searching = false;
        dialog.context.connect.matching = false;
        re_prompt = true;
      }
    }

    // TODO handle notes better
    if (notes && notes.length) {
      dialog.context.connect.notes = dialog.context.connect.notes.concat(notes.filter(n => n.length));
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, `Re-prompt after notes ${notes}`);
      // re_prompt = true;
      dialog.context.connect.prompted = false;
    }
      
    if (re_prompt) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'preparePerson', dialog.user.profile, 'Re-prompting');

      dialog.context.connect.loading = false;
      dialog.context.connect.candidates = null;
      dialog.context.connect.global_search = false;
      dialog.context.connect.global_search_complete = false;
      dialog.context.connect.prompted = false;
    }
  }
}

function _findPerson(dialog: Dialog) {
  // if (dialog.context.connect.matched) return true;

  if (!dialog.context.connect.loading && !dialog.context.connect.global_search) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, 'Not loading or global search');
    if (dialog.context.connect.person && (!dialog.context.connect.candidates || !dialog.context.connect.candidates.length)) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, 'No candidates');
      if ((dialog.context.connect.person.comms && dialog.context.connect.person.comms.length) ||
          (dialog.context.connect.person.names && dialog.context.connect.person.names.length)) {
        if (!dialog.context.connect.searching) {
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `Searching by comms ${dialog.context.connect.person.comms}`);
          dialog.context.connect.searching = true;
          dialog.context.connect.matching = false;
          dialog.runAsync('connect', async () => {
            let comm_matches: Partial<Person>[];
            let name_matches: Partial<Person>[];

            let matches = dialog.context.connect.person.comms && dialog.context.connect.person.comms.length ? await dialog.people.findByComms(dialog.context.connect.person.comms) : [];
            if (matches) matches.forEach(m => slimEntity(m));
            comm_matches = matches.filter(m => !m.self);
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `Comm matches ${JSON.stringify(comm_matches)}`);

            matches = dialog.context.connect.person.names && dialog.context.connect.person.names.length ? await dialog.people.findByName(dialog.context.connect.person.names) : [];
            if (matches) matches.forEach(m => slimEntity(m));
            name_matches = matches.filter(m => !m.self);
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `Name matches ${JSON.stringify(name_matches)}`);

            // if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `Adding matches comm: ${JSON.stringify(comm_matches)} name: ${JSON.stringify(name_matches)}`);
            matches = dialog.context.connect.matches;

            if (matches && matches.length) {
              const match_ids = matches.map(m => m.id);
              // only keep matches that are in comm and name
              const comm_ids  = comm_matches && comm_matches.length ? comm_matches.map(m => m.id) : match_ids;
              const name_ids  = name_matches && name_matches.length ? name_matches.map(m => m.id) : match_ids;

              const all_ids = [...comm_ids, ...name_ids];

              dialog.context.connect.matches = matches.filter(m => all_ids.includes(m.id));
            } else {
              if (comm_matches && comm_matches.length && name_matches && name_matches.length) {
                dialog.context.connect.matches = _.intersectionBy(comm_matches, name_matches, m => m.id);
              } else if(comm_matches && comm_matches.length) dialog.context.connect.matches = comm_matches;
              else dialog.context.connect.matches = name_matches;
            }

            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `Intersecting matches ${JSON.stringify(dialog.context.connect.matches)}`);

            if(dialog.context.connect) dialog.context.connect.matching = true;
          });
        }

        if (!dialog.context.connect.matching) {
          // dialog.quickPing();
          return false;
        }


      } else if (!dialog.context.connect.matches) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `No comms, empty matches`);
        dialog.context.connect.matches = [];
      }

      if (!dialog.context.connect.matches) dialog.context.connect.matches = []; 

      const perfect_match = dialog.context.connect.matches.find(p => _.intersection(p.comms, dialog.context.connect.person.comms).length > 0);

      if (!perfect_match && !dialog.context.connect.global_search_complete) {
        if (dialog.context.connect.global_search) {
          // dialog.quickPing();
          return false;
        }

        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `Searching globally for matches to ${JSON.stringify(dialog.context.connect.person)}`);

        dialog.context.connect.global_search = true;
        dialog.context.connect.global_search_complete = false;
        dialog.resetProcessing();
        dialog.runAsync('connect', async () => {
          const match = await dialog.search.globalSearch(dialog.context.connect.person);
          if (dialog.context.connect) {
            if (match) {
              slimEntity(match);
              if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `Matched globally with ${JSON.stringify(match)}`);
              const new_match = await dialog.people.temp(match, true);
              if (dialog.context.connect) {
                if (dialog.context.connect.matches) dialog.context.connect.matches.push(new_match);
                else dialog.context.connect.matches = [new_match];
                dialog.context.connect.global_search_complete = true;
              }
            } else if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `No global match`);
            dialog.context.connect.global_search_complete = true;
          }
        });

        // dialog.quickPing();
        return false;
     } else {
        dialog.context.connect.global_search = true;
        dialog.context.connect.global_search_complete = true;
      }
    }

    if (dialog.context.connect.global_search_complete && !dialog.context.connect.matched && dialog.context.connect.matches !== null && dialog.context.connect.matches.length > 0) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'findPerson', dialog.user.profile, `Updating matched to ${dialog.context.connect.matched}`);

      dialog.context.connect.matches.sort((a,b) => {
        const comms_match_a = _.intersection(a.comms, dialog.context.connect.person.comms).length;
        const comms_match_b = _.intersection(b.comms, dialog.context.connect.person.comms).length;
        const display_match_a = a.displayName ===  dialog.context.connect.person.displayName;
        const display_match_b = b.displayName ===  dialog.context.connect.person.displayName;
        const names_match_a =  _.intersection(a.names, dialog.context.connect.person.names).length;
        const names_match_b =  _.intersection(b.names, dialog.context.connect.person.names).length;

        if (comms_match_a > 0 && comms_match_b > 0) {
          if (display_match_a && display_match_b) {
            if (names_match_a > 0 && names_match_b > 0) return names_match_b - names_match_a;
            else if(names_match_a > 0) return -1;
            return 1;
          } else if(display_match_a) return -1;
          return 1;
        }

        return comms_match_b - comms_match_a;
      });
      dialog.setTopic(Topics.CONNECT_PICK);
      dialog.context.connect.matched = true;
      // dialog.quickPing();
      return false;
    }
  }

  if ((dialog.context.connect.searching || dialog.context.connect.global_search) && 
      (!dialog.context.connect.matches || !dialog.context.connect.matches.length) &&
      !dialog.context.connect.global_search_complete) {
    // dialog.quickPing();
    return false;
  }

  return true;
}

function _pickPerson(dialog: Dialog) {
  if (dialog.message.command) return false;

  if (dialog.context.connect.matches && dialog.context.connect.matches.length) {
    dialog.context.connect.loading = true;
    const ids = dialog.context.connect.matches.map(m => m.id);
    dialog.context.connect.matches = null;
    dialog.context.connect.candidates = null;
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'pickPerson', dialog.user.profile, `Loading pick list from ${ids}`);
    dialog.resetProcessing();
    dialog.runAsync('connect', async () => {
      const people = await dialog.people.byId(ids.filter(id => id));
      people.forEach(p => slimEntity(p));
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'pickPerson', dialog.user.profile, `Loaded pick list of ${people.length} people`);
      if (dialog.context.connect && people.length) {
        dialog.context.connect.loading = false;
        dialog.context.connect.candidates = _.uniqBy(people.filter(p => !p.self), 'id');
        dialog.context.connect.prompted = false;
      } else {
        dialog.context.connect.loading = false;
        dialog.context.connect.prompted = false;
        dialog.setTopic(Topics.CONNECT);
      }
    });

    return false;
  }

  const entities = dialog.actionValues(ActionType.ENTITY);
  const people = dialog.actionValues(ActionType.PERSON);
  const bool = dialog.actionValues(ActionType.BOOL);

  const picks = [...entities && entities.length && entities[0].people ? entities[0].people : [], ...people];
  if (dialog.checkTopic(Topics.CONNECT_PICK) && picks && picks.length && dialog.context.connect.candidates && (!bool || !bool.length)) {
    const candidate = dialog.context.connect.candidates.find(c => 
      arraysIntersect(picks.map(p => p ? p.id : null).filter(f => f), c.comms));

    if (candidate) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'pickPerson', dialog.user.profile, `User picked ${candidate}`);
      peopleUtils.mergePeople(candidate, dialog.context.connect.person, true);
      dialog.context.connect.person = candidate;
      dialog.clearActions();
      dialog.addAction(ActionType.BOOL, true);
      dialog.setTopic(Topics.CONNECT);
      return true;
    } else {
      dialog.clearActions();
      dialog.addAction(ActionType.BOOL, false);
    }
  }

  if ((!bool || !bool.length) && !dialog.context.connect.saving && dialog.context.connect.candidates && dialog.context.connect.candidates.length && 
    (!dialog.context.connect.searching || dialog.context.connect.global_search_complete)) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'pickPerson', dialog.user.profile, `Prompt to pick from ${dialog.context.connect.candidates.length}`);
    if (!dialog.checkTopic(Topics.CONNECT_PICK)) {
      dialog.context.connect.prompted = false;
      dialog.setTopic(Topics.CONNECT_PICK);
    }
    return false;
  }

  if (bool && bool.length) {
    if(bool[0]) {
      if (dialog.context.connect.candidates && dialog.context.connect.candidates.length === 1) {
        dialog.context.connect.person = dialog.context.connect.candidates[0];
      }
    } else {
      // dialog.context.connect.candidates = null;
      dialog.context.connect.candidates = [];
      dialog.context.connect.matches = null;
      dialog.context.connect.matching = false;
      dialog.context.connect.matched = false;
      dialog.context.connect.prompted = false;
      dialog.clearActions(ActionType.BOOL);
    }

    dialog.setTopic(Topics.CONNECT);
  }

  if (dialog.context.connect.loading && !dialog.context.connect.candidates) {
    return false;
  }

  return true;
}

function _savePerson(dialog: Dialog) {
  if (dialog.context.connect.person && dialog.context.connect.require_email &&
    (!dialog.context.connect.person.comms || !parsers.findEmail(dialog.context.connect.person.comms).length)) {
    dialog.setTopic(Topics.CONNECT_EMAIL_REQUIRED);
    return;
  } else if (dialog.checkTopic(Topics.CONNECT_EMAIL_REQUIRED)) {
    dialog.context.connect.prompted = false;
    dialog.setTopic(Topics.CONNECT);
  }

  if (!dialog.context.connect.saving && dialog.message.ping) return;
  const bool = dialog.actionValues(ActionType.BOOL);

  // response to "any other info" - if negative then don't save
  if (bool.length && (dialog.context.connect.person || (dialog.context.connect.candidates && dialog.context.connect.candidates.length))) {
    if ((dialog.checkTopic(Topics.CONNECT) && bool[0]) || dialog.checkTopic(Topics.CONNECT_PICK)) {
      if (!dialog.context.connect.saving) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'savePerson', dialog.user.profile, `Saving ${JSON.stringify(dialog.context.connect.person)}`);
        dialog.context.connect.saving = true;

        if (dialog.checkTopic(Topics.CONNECT_PICK) && bool[0]) {
          if (!dialog.context.connect.person.id && (dialog.context.connect.candidates && dialog.context.connect.candidates.length)) {
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'savePerson', dialog.user.profile, `Defaulting save to ${JSON.stringify(dialog.context.connect.candidates[0])}`);
            dialog.context.connect.person = dialog.context.connect.candidates[0];
          }
        }

        dialog.context.connect.prompted = false;
        dialog.setTopic(Topics.CONNECT);

        dialog.runAsync('connect', async () => {
          const p = await dialog.people.create(dialog.context.connect.person); //, false);
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'savePerson', dialog.user.profile, `Saved to contacts ${JSON.stringify(p)}`);
          if (dialog.context.connect) {
            dialog.context.connect.saved_person = new Person(p);
            slimEntity(p);
            const n = await dialog.tasks.create(
              new Task({
                title: `Note for ${p.displayName}`,
                notes: dialog.context.connect.notes.join('\n'),
                people: [p],
            }));
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'savePerson', dialog.user.profile, `Saved note ${JSON.stringify(n)}`);
            if (dialog.context.connect) dialog.context.connect.saved_note = n;

            if (dialog.context.connect.project) {
              const proj = await dialog.projects.get(new Project(dialog.context.connect.project), true);
              if (proj) {
                if (!proj.candidates) proj.candidates = [projectPerson(proj, p)];
                else {
                  const exists = proj.candidates.find(c => c.id === p.id || arraysIntersect(c.comms, p.comms));
                  if (!exists) proj.candidates.push(projectPerson(proj, p));
                }

                dialog.context.connect.project = await dialog.projects.update(proj, proj.client.self ? true : false);
              }
            }
            
            dialog.context.connect.done_saving = true;
          }
        });
      }
    } else if (dialog.context.connect.candidates && dialog.context.connect.candidates.length) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'savePerson', dialog.user.profile, `Reset candidates and person`);
      dialog.context.connect.candidates = [];
      dialog.context.connect.person = null;
    } else {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'savePerson', dialog.user.profile, `Exiting connect plugin`);
      dialog.setTopic(Topics.DEFAULT);
      dialog.clearContext('connect');
    }
  }
}

export function addPerson(dialog: Dialog, person: Person, has_email = true, ignore_draft = true) {
  logging.infoFP(LOG_NAME, 'addPerson', dialog.user.profile, `Adding person ${person.displayName} ${person.id}`);
  dialog.context.connect = new ConnectPluginState();
  dialog.context.connect.person = person;
  dialog.context.connect.message = person.displayName;
  dialog.context.connect.has_email = has_email;
  dialog.context.connect.ignore_draft = ignore_draft;
  dialog.nextTopic(dialog.topic);
  dialog.setTopic(Topics.CONNECT);
  return runAction(dialog);
}

function runAction(dialog: Dialog) {
  if (dialog.checkTopic(Topics.CONNECT_REG_REQUIRED)) return;

  if (dialog.checkTopic(Topics.SELF_INTRO)) return _selfIntro(dialog);

  if (_checkSaving(dialog)) return;

  _preparePerson(dialog);

  if (!_findPerson(dialog)) return;

  if (!_pickPerson(dialog)) return;

  _savePerson(dialog);
}

function draftInfo(user: ForaUser, contact: PersonInfo, message: Message) {
  let url = null;
  if (message.id) url = draftMessageLink(user.provider, user.email, message);
  else url = messageLink(message.raw);
  return promptInfo(lang.connect.DRAFT, url, lang.connect.EDIT_SEND, [contact]);
}

function setPrompt(dialog: Dialog) {
  const selections = [];

  // check for yes or done and save user
  switch (dialog.topic) {
    case Topics.SELF_INTRO:
      if (!dialog.context.self_intro) {
        const people = dialog.getRepliesType(EntityType.Person);
        const message = dialog.getRepliesType(EntityType.Message);

        if (people.length && message.length) dialog.addPrompt(draftInfo(dialog.user, dialog.getPersonInfo(people[0]), message[0]), true);
        else dialog.lastPrompt('connect', 'All set.', {clear: true});
        // dialog.defaultPing();
        // dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.CONNECT_INFO:
      if (dialog.message.ping) return;
      dialog.addPrompt(lang.connect.CONNECT_PROMPT, true);
      dialog.addAnswers(lang.defaultanswers.NEVERMIND);
      dialog.addHint(lang.connect.CONNECT_HINT);
      dialog.setTopic(Topics.CONNECT);
      dialog.setSticky();
      // dialog.clearPing();
      break;
    case Topics.CONNECT_EMAIL_REQUIRED:
      dialog.addInfo(dialog.getPersonInfo(dialog.context.connect.person, null, [], [], [], []), true);
      dialog.addPrompt(lang.connect.PROMPT_EMAIL(dialog.context.connect.person.nickName));
      dialog.setSticky();
      // dialog.clearPing();
      break;
    case Topics.CONNECT:
      if (dialog.context.connect) {
        if (dialog.context.connect.saving) {
          // if (!dialog.message.ping) 
          dialog.addPrompt(lang.connect.CONNECTING_WAIT, true, true, true);
        } else if (!dialog.context.connect.prompted && !dialog.context.connect.loading &&
          !dialog.message.command && dialog.context.connect.person && 
          ((!dialog.context.connect.searching && !dialog.context.connect.global_search) || dialog.context.connect.global_search_complete) && 
          (!dialog.context.connect.matches || !dialog.context.connect.matches.length)) { 
          dialog.context.connect.prompted = true;
          dialog.addInfo(dialog.getPersonInfo(dialog.context.connect.person, null, [], [], [], []), true, true, true);
          if (!dialog.context.connect.first_prompt) {
            dialog.context.connect.first_prompt = true;
            dialog.addPrompt(lang.connect.CONFIRM(dialog.context.connect.person.nickName));
          } else dialog.addPrompt(lang.connect.RECONFIRM(dialog.context.connect.person.nickName));
          dialog.addAnswers(lang.connect.CONFIRM_ANSWERS);
          dialog.setSticky();
        } 
      } else {
        const people = dialog.getRepliesType(EntityType.Person);
        const notes = dialog.getRepliesType(EntityType.Task);
        const projects = dialog.getRepliesType(EntityType.Project);
        const curr_dialog = dialog.currentDialog();
        dialog.setTopic(curr_dialog.next_topic);
        if (isActive(dialog)) {
          if (people.length) {
            const person: Person = people[0];
            const person_info = dialog.getPersonInfo(person);
            person_info.added = true;

            const note: Task = notes[0];
            if (note) {
              const note_info = taskInfo(note, false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
              note_info.added = true;
              dialog.addInfo(note_info);
              if (!person_info.tasks) person_info.tasks = [];
              person_info.tasks.push(note_info);
            }

            const project: Project = projects?.length ? projects[0] : undefined;
            if (project) {
              const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self || project.admin);
              dialog.addInfo(project_info);
            }

            dialog.addInfo(person_info);
            if (dialog.checkTopic(Topics.CONNECT_PROJECT)) dialog.setTopic(Topics.DEFAULT);
            else dialog.addPrompt(lang.connect.CONNECTING(person.nickName));
            if (dialog.checkTopic(Topics.SELF_INTRO)) {
              dialog.addPrompt(lang.connect.DRAFT_PROMPT);
              dialog.addAnswers(lang.connect.DRAFT_ANSWERS);
            } else if (dialog.checkTopic(topics)) {
              dialog.setTopic(Topics.DEFAULT);
            }
          } else {
            dialog.lastPrompt('connect', lang.connect.ACK, {clear: true});
          }
        } else {
          dialog.clearContext('connect');
          dialog.reRun();
        }
      }
      break;
    case Topics.CONNECT_PICK:
      if (!dialog.context.connect.prompted && dialog.context.connect.candidates) {
        dialog.context.connect.prompted = true;
        // Prompt to pick a candidate
        for (const index in dialog.context.connect.candidates) {
          const pi = dialog.getPersonInfo(dialog.context.connect.candidates[index]);
          pi.select = true;
          selections.push(pi);
        }
        dialog.addInfo(selections, true, true, true);
        dialog.setSticky();
        // dialog.clearPing();

        if (dialog.context.connect.candidates.length === 1) {
          dialog.addPrompt(lang.connect.CONFIRM_MATCH);
          dialog.addAnswers(lang.connect.MATCH_ANSWERS);
        } else {
          dialog.addPrompt(lang.connect.SELECT_MATCH);
          dialog.addAnswers(lang.connect.SELECT_ANSWERS);
        }
      }
      break;
    case Topics.CONNECT_REG_REQUIRED:
      dialog.addPrompt(lang.init.REG_REQUIRED(dialog.user.provider), true);
      dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
      dialog.setTopic(Topics.DEFAULT);
      break;
  }
  return false;
}

export default {
  name: CONNECT_NAME,
  description: CONNECT_DESC,
  examples: lang.connect.CONNECT_EG,
  reserved: CONNECT_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: CONNECT_SHORTCUT,
  addPerson,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
