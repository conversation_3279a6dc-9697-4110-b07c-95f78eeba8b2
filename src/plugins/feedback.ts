import util from 'util';
import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Message, Person } from '../types/items';
import { Plugin } from '../types/plugins';
import { AuthLevel } from '../types/shared';

import * as funcs from '../utils/funcs';
import mail from '../utils/mail';
import parsers from '../utils/parsers';

const FEEDBACK_NAME = 'Feedback';
const FEEDBACK_DESC = 'Give feedback on the product';

const topics = [
  TOPIC('FEEDBACK'),
  TOPIC('HELP_FEEDBACK'),
  TOPIC('PROJECT_FEEDBACK'),
];

function keywordMatch(actions, message, raw_message) {
  if (parsers.checkKeyword(message, lang.feedback.FEEDBACK_KWDS)) return true;
  return false;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function setAction(dialog: Dialog, _message, _raw_message) {
  // if ((!dialog.isAuthenticated() || !dialog.isMaskedTopic()) && !dialog.message.ping) {
  if (!dialog.message.ping && (dialog.checkTopic(Topics.DEFAULT) || dialog.last_error)) {
    dialog.setTopic(Topics.FEEDBACK);
    return true;
  }
  return isActive(dialog);
}

function runAction(dialog: Dialog) {
  let message: string = dialog.message.message;
  if (message.length === 0 || message.toLowerCase() === 'feedback') message = null;

  const log: string = dialog.message.data;
  const error = dialog.last_error ? util.format(dialog.last_error) : null;

  if (!dialog.context.feedback) {
    const profile = funcs.mapURL(`/profile/${dialog.user.vanity}`, dialog.getGroup());
    dialog.context.feedback = {
      message: dialog.checkTopic(Topics.HELP_FEEDBACK) ? null : message,
      user: {name: dialog.user.name, email: dialog.user.email, locale: dialog.user.locale, id: dialog.user.profile, profile},
      log,
      error,
      dialog: [],
    };

    if (dialog.checkTopic([Topics.FEEDBACK, Topics.HELP_FEEDBACK])) return;
  }

  if (!dialog.context.feedback.message) {
    dialog.context.feedback.message = message;
    if (!dialog.checkTopic(Topics.HELP_FEEDBACK)) return;
  } 

  if (dialog.context.feedback.message && dialog.context.feedback.message.length) {
    const bool = dialog.actionValues(ActionType.BOOL);
    if (bool.length || dialog.checkTopic(Topics.HELP_FEEDBACK)) {
      if (bool.length && bool[0]) dialog.context.feedback.dialog = dialog.history;

      const rcpt = new Person({ displayName: 'Fora', comms: ['<EMAIL>'] });
      const mail_rcpts = [{ Name: 'Fora', Email: '<EMAIL>' }];
      const subject = `Feedback from ${dialog.user.name}`;
      const message = JSON.stringify(dialog.context.feedback, null, 2);

      if (dialog.isAuthenticated(AuthLevel.Email)) {
        dialog.messages
          .create(dialog.me, rcpt, subject, message)
          .then((draft: Message) => {
            if (draft) dialog.messages.send(draft).catch(e => dialog.asyncError(e));
            else mail(mail_rcpts, [], subject, message);
          })
          .catch(e => dialog.asyncError(e));
      } else {
        mail(mail_rcpts, [], subject, message);
        // notify(dialog.user, notification, NotifyType.EmailOnly).catch(err => dialog.asyncError(err));
      }

      dialog.clearContext('feedback');
    }
  }

  return true;
}

function setPrompt(dialog: Dialog) {
  if (dialog.context.feedback) {
    switch(dialog.topic) {
      case Topics.HELP_FEEDBACK:
        dialog.addPrompt(lang.feedback.HELP_FEEDBACK, true);
        break;
      default:
        if (dialog.context.feedback.message) {
          dialog.addPrompt(lang.feedback.FEEDBACK_SENSITIVE, true);
          dialog.addAnswers(lang.feedback.FEEDBACK_ANSWERS);
        } else dialog.addPrompt(lang.feedback.FEEDBACK_PROMPT, true);
        break;
    }
  } else {
    switch(dialog.topic) {
      case Topics.HELP_FEEDBACK:
        dialog.lastPrompt('feedback', lang.feedback.HELP_FEEDBACK_CONFIRM, {clear:true});
        break;
      default: 
        dialog.lastPrompt('feedback', lang.feedback.FEEDBACK_CONFIRM, {clear:true});
        break;
    }
  }
}

export default {
  name: FEEDBACK_NAME,
  description: FEEDBACK_DESC,
  examples: [], //lang.feedback.FEEDBACK_EG,
  reserved: lang.feedback.FEEDBACK_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: lang.feedback.FEEDBACK_SHORTCUTS,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default);
