import _ from 'lodash';
import util from 'util';

import lang from '../lang';

import { ActionType, Topic } from '../types/globals';
import { Ask, Candidate, Contract, Person, Project, ProjectGroupSettings, dehydratePerson, projectPerson } from '../types/items';
import { Plugin, ProjectPluginState } from '../types/plugins';
import { AuthContext, AuthLevel, AuthProviders, ChatComponent, EntityType, PersonInfo, ProjectCandidateState, ProjectInfo, ProjectRate, ProjectSourcingType, TagType, UndefinedPersonInfo } from '../types/shared';

import { SECONDS, getEndDate, getStartDate } from '../utils/datetime';
import { checkState, flatten, slimEntity, stripPuncs } from '../utils/funcs';
import { askInfo } from '../utils/info';

import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import { AuthProvider } from '../auth/auth_provider';
import data from '../data';
import { DISCOUNT_FEE, SERVICE_FEE } from '../routes/gopay';
import Dialog, { TOPIC, Topics } from '../session/dialog';
import { FORA_PROFILE } from '../types/user';
import personInfo from './person_info';

const DEBUG = (require('debug') as any)('fora:plugins:project');

const LOG_NAME = 'plugins.project_create';

const topics = [
  TOPIC('PROJECT'), // generic topic
  TOPIC('SOURCING'), // generic topic
  TOPIC('PROJECT_JOB_EXPERT'), // job or expert request?
  TOPIC('PROJECT_REPEAT'), // generic topic
  TOPIC('PROJECT_SKILLS'), // get skills for project
  TOPIC('PROJECT_EXPERT_SKILLS'), // get skills for project
  TOPIC('PROJECT_GOAL'), // get goals
  TOPIC('PROJECT_EXPERT_GOAL'), // get goals
  TOPIC('PROJECT_RATE'), // ge trate
  TOPIC('PROJECT_FEE'), // get fee
  TOPIC('PROJECT_DURATION'), // get duration
  TOPIC('CREATE_PROJECT'), // ping while creating
  TOPIC('PROJECT_CREATE_SAVING'), // ping while saving
  TOPIC('PROJECT_LOGIN'), // prompt to login
  TOPIC('PROJECT_FIRSTRUN'), // Tell the user that we are searching their network and ask them to enable notifications
  TOPIC('PROJECT_STEPS'), // next step
  TOPIC('PROJECT_PROPOSE'), //create a proposal
  TOPIC('PROJECT_TEMPLATE'), //create a proposal
  TOPIC('PROJECT_PROPOSE_TEMPLATE'), //create propoasl from template
  TOPIC('PROJECT_EXPERT'), //find an expert
  TOPIC('PROJECT_EXPERT_LOAD_RELATED'), //load details from a related entity
  TOPIC('PROJECT_CONTRACTOR'), //contractor create entry point
  TOPIC('TEMPLATE_LINK'),
];

const UPDATE_PROJECT_DESC_FIELDS = [
  'confidential',
  'duration',
  'end',
  'info',
  'network',
  'notes',
  'requirements',
  'deliverables',
  'background',
  'rate',
  'skills',
  'start',
  'flex_dates',
  'fee',
  'title',
];

// don't need a project for these
const IGNORE_ENTITY_TOPICS = [
  Topics.PROJECT_SKILLS,
  Topics.PROJECT_EXPERT_SKILLS,
  Topics.PROJECT_GOAL,
  Topics.PROJECT_EXPERT_GOAL,
  Topics.PROJECT_FEE,
  Topics.PROJECT_DURATION,
  Topics.PROJECT_RATE,
];

function keywordMatch(_actions, message) {
  return (
    parsers.checkKeyword(message, lang.project.PROJECT_KWD, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_REPEAT, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_PROPOSE, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_TEMPLATE, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_PROPOSE_TEMPLATE, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_EXPERT, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_CONTRACTOR, true) 
  );
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function setAction(dialog: Dialog, message, raw_message) {
  if (isActive(dialog) && message === 'nevermind') {
    dialog.reset('project');
    dialog.setTopic(Topics.DEFAULT);
    return true;
  }

  if (isActive(dialog) && lang.project.PROJECT_KWD.includes(message)) {
    dialog.removeActions(ActionType.SKILL, message);
  }

  if ((parsers.checkKeyword(message, lang.project.PROJECT_KWD) && !isActive(dialog)) ||
    (dialog.checkTopic(Topics.PROJECT_JOB_EXPERT) && parsers.checkKeyword(message, lang.project.PROJECT_JOB_CHOICE))) {

    // We matched one of the keywords but we are not the active plugin, so we are just starting the journey
    if(dialog.checkTopic(Topics.PROJECT_JOB_EXPERT)) {
      dialog.removeActions(ActionType.SKILL);
      const context = parsers.extract(lang.project.CONTEXT, raw_message);
      if (context && context.length) {
        dialog.addAction(ActionType.RAW, context);
        dialog.removeActions(ActionType.ENTITY, {name: context});
      }
      dialog.message.message = ''; // clear message
      
      dialog.setTopic(Topics.PROJECT);
    } else {
      const context = parsers.extract(lang.project.CONTEXT, raw_message);
      if (context && context.length) {
        if (!isActive(dialog)) {
          const parts = context.split(' ');
          let match = [];
          const skills = dialog.actionValues(ActionType.SKILL).map(s => s.toLowerCase());
          for(const p of parts) {
            match.push(p.toLowerCase());
            if (skills.includes(match.join(' '))) break;
          }
          if (match.length == parts.length) dialog.addAction(ActionType.SKILL, context);
        }
        else dialog.addAction(ActionType.RAW, context);
        dialog.removeActions(ActionType.ENTITY, {name: context});
        dialog.setTopic(Topics.PROJECT);
      }

      const kparts = flatten(lang.project.PROJECT_KWD.map(k => k.split(' ')));

      const apeople = dialog.actionValues(ActionType.PERSON);
      const upeople = dialog.actionValues(ActionType.ENTITY).filter(p => 
        p.id !== 'found_people' || !parsers.checkKeyword('search',  kparts));
      if ((upeople && upeople.length) || (apeople && apeople.length)) {
        if (dialog.message.command && parsers.checkKeyword(message, lang.project.FROM_PROFILE)) {
          dialog.addAction(ActionType.CMD, lang.project.FROM_PROFILE);
        }
        dialog.setTopic(Topics.PROJECT);
      }
      else if(parsers.checkKeyword(message, lang.project.PROJECT_CREATE)) dialog.setTopic(Topics.PROJECT);
      // TODO: check sourcing is enabled on the group and has permission
      else if(parsers.checkKeyword(message, lang.project.PROJECT_SOURCING_CREATE)) dialog.setTopic(Topics.SOURCING);
      else if(!parsers.checkKeyword(message, [...lang.project.PROJECT_TEMPLATE, ...lang.project.PROJECT_PROPOSE_TEMPLATE, ...lang.project.PROJECT_EXPERT], true)) {
        dialog.setTopic(Topics.PROJECT_JOB_EXPERT);
      }
    }

    // remove keywords from skills and entities
    for (const kwd of lang.project.PROJECT_KWD) {
      if (message.indexOf(kwd) === 0) {
        dialog.removeActions(ActionType.SKILL, kwd, true);
        dialog.removeActions(ActionType.ENTITY, {name: kwd});
        const parts = kwd.split(' ');
        let ex_index = 0;
        for (let i = 0; i < parts.length; i++) {
          const part = parts[i];
          const index = message.lastIndexOf(part);
          if (index === ex_index) {
            let rpart = part;
            dialog.removeActions(ActionType.SKILL, part, true);
            dialog.removeActions(ActionType.ENTITY, {name: part});
            for (let j = i + 1; j < parts.length; j++) {
              rpart = `${rpart} ${parts[j]}`;
              dialog.removeActions(ActionType.SKILL, rpart, true);
              dialog.removeActions(ActionType.ENTITY, {name: rpart});
            }
          }
          ex_index = message.indexOf(`${part} `) + part.length + 1;
        }

        // break;
      }
    }
  }

  // capture goals
  if (dialog.checkTopic([Topics.PROJECT_GOAL, Topics.PROJECT_EXPERT_GOAL])) {
    if (raw_message.length && !lang.project.PROJECT_KWD.includes(raw_message.toLowerCase())) dialog.addAction(ActionType.RAW, raw_message);
    return true;
  }

  // save during create
  if (parsers.checkKeyword(message, lang.project.PROJECT_SAVE, true)) {
    dialog.addAction(ActionType.CMD, lang.project.CMD_PROJECT_SAVE);
  }

  // duplicate
  if (parsers.checkKeyword(message, lang.project.PROJECT_REPEAT, true) &&
    dialog.actionValues(ActionType.PROJECT).length) {
      dialog.setTopic(Topics.PROJECT_REPEAT);
      return true;
  }

  if (dialog.checkTopic(Topics.PROJECT_CONTRACTOR)) {
    if (parsers.checkKeyword(message, lang.project.PROJECT_PROPOSE, true)) {
      dialog.removeActions(ActionType.SKILL);
      dialog.removeActions(ActionType.ENTITY);
      dialog.setTopic(Topics.PROJECT_PROPOSE);
      return true;
    }

    if (parsers.checkKeyword(message, lang.project.PROJECT_TEMPLATE, true)) {
      dialog.removeActions(ActionType.SKILL);
      dialog.removeActions(ActionType.ENTITY);
      dialog.setTopic(Topics.PROJECT_TEMPLATE);
      return true;
    }
    return true;
  }

  // contractor side
  if (parsers.checkKeyword(message, lang.project.PROJECT_CONTRACTOR, true)) {
    dialog.setTopic(Topics.PROJECT_CONTRACTOR);
    return true;
  }

  // proposal
  if (parsers.checkKeyword(message, lang.project.PROJECT_PROPOSE, true)) {
    dialog.setTopic(Topics.PROJECT_PROPOSE);

    dialog.removeActionContext([...lang.project.PROJECT_PROPOSE, ...lang.project.PROPOSE_CONTEXT]);

    return true;
  }

  // template
  if (dialog.isAuthenticatedNonGuest() && parsers.checkKeyword(message, lang.project.PROJECT_TEMPLATE, true)) {
    dialog.removeActions(ActionType.SKILL, lang.project.PROJECT_TEMPLATE, true);
    dialog.setTopic(Topics.PROJECT_TEMPLATE);
    return true;
  }

  // propose template
  if (parsers.checkKeyword(message, lang.project.PROJECT_PROPOSE_TEMPLATE, true)) {
    dialog.setTopic(Topics.PROJECT_PROPOSE_TEMPLATE);
    return true;
  }

  // expert
  if ((parsers.checkKeyword(message, lang.project.PROJECT_EXPERT, true) && !isActive(dialog)) 
    || (dialog.checkTopic(Topics.PROJECT_JOB_EXPERT) && parsers.checkKeyword(message, lang.project.PROJECT_EXPERT_CHOICE))) {

    if(dialog.checkTopic(Topics.PROJECT_JOB_EXPERT)) {

      dialog.removeActions(ActionType.SKILL);
      const context = parsers.extract(lang.project.CONTEXT, raw_message);
      dialog.removeActions(ActionType.ENTITY, {type: EntityType.UnresolvedPerson});

      if (context && context.length) {
        dialog.addAction(ActionType.RAW, context);
        dialog.removeActions(ActionType.ENTITY, {name: context});
      }
      
      dialog.message.message = ''; // clear message
    } else {
      for (const kwd of lang.project.PROJECT_EXPERT) {
        dialog.removeActions(ActionType.SKILL, kwd);
        for (const k of kwd.split(' ')) {
          dialog.removeActions(ActionType.SKILL, k);
        }
      }
    }
    dialog.setTopic(Topics.PROJECT_EXPERT);
    return true;
  }

  // client only processing
  if (dialog.checkTopic(Topics.PROJECT) && dialog.context.project && dialog.context.project.project && dialog.context.project.project.client) {
    const project = dialog.context.project.project;
    const c_guest = _isClientGuest(project.client);
    const u_guest = _isUserGuest(dialog.me);

    logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `client guest: ${c_guest}, user_guest: ${u_guest}, client not user: ${!dialog.me || (dialog.context.project.project.client.id !== dialog.me.id)}`);

    if (c_guest) {
      if (dialog.me && !u_guest && !project.proposal) {
        const client = projectPerson(dialog.context.project.project, dialog.me);
        // We created the project when we were a guest, change the client to `me` and kick off matching again
        logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Setting client to ${JSON.stringify(client)}`);
        dialog.context.project.project.client = client;
        dialog.context.project.project.client.comms = [dialog.me.id, dialog.user.email];
        dialog.context.project.project.client.self = true;
        dialog.context.project.project.client.askfora_id = dialog.user.profile;
        slimEntity(dialog.context.project.project.client);
        dialog.runAsync('project', async () => {
          await dialog.projects.update(dialog.context.project.project);
        });
        dialog.setTopic(Topics.PROJECT_MATCHING); // hands off to project_candidates plugin
        // TODO remember to reset state variables once type is added
      }
    } else if(project.contractor) {
      // don't load selected projects for non-contractors or clients
      if (project.contractor.askfora_id === dialog.user.profile) dialog.setTopic(Topics.PROJECT_CANDIDATE);
      else if (project.client.askfora_id != dialog.user.profile) dialog.setTopic(Topics.PROJECT_EXPIRED);
    } else if (project.client.askfora_id !== dialog.user.profile) {
      // Candidate mode
      dialog.setTopic(Topics.PROJECT_CANDIDATE);
    }
  }

  if (!isActive(dialog)) dialog.setTopic(Topics.PROJECT);

  if (dialog.checkTopic(Topics.PROJECT_LOGIN)) {
    if (parsers.checkKeyword(message, lang.admin.CONNECT_POSITIVE)) dialog.setTopic(Topics.REGISTER);
    if (parsers.checkKeyword(message, lang.imports.IMPORTS_KWD)) dialog.setTopic(Topics.IMPORTS);
  }

  return true;
}

function _isClientGuest(person: Partial<Person>) {
  // TODO make this not hard coded here and in lang.init
  return person.id === lang.init.FORA_GUEST_ID;
}

function _isUserGuest(user: Person) {
  // TODO make this not hard coded here and in lang.init
  return user && user.id === lang.init.FORA_GUEST_ID;
}

function runAction(dialog: Dialog) {
  // const bool = dialog.actionValues(ActionType.BOOL);

  switch (dialog.topic) {
    case Topics.PROJECT_JOB_EXPERT:
    case Topics.CREATE_PROJECT:
      return;

    case Topics.PROJECT_CREATE_SAVING:
      return _saveSendProject(dialog);

    case Topics.PROJECT_STEPS:
      return _projectSteps(dialog);

    case Topics.PROJECT:
    case Topics.SOURCING:
    case Topics.PROJECT_SKILLS:
    case Topics.PROJECT_EXPERT_SKILLS:
    case Topics.PROJECT_GOAL:
    case Topics.PROJECT_EXPERT_GOAL:
    case Topics.PROJECT_RATE:
    case Topics.PROJECT_FEE:
    case Topics.PROJECT_DURATION:
      return _setupProject(dialog);

    case Topics.PROJECT_REPEAT:
      return _repeatProject(dialog);

    case Topics.PROJECT_PROPOSE_TEMPLATE:
      return _proposeTemplate(dialog);

    case Topics.PROJECT_PROPOSE:
      return _proposeProject(dialog);

    case Topics.PROJECT_TEMPLATE:
      return _proposeProject(dialog, true);

    case Topics.PROJECT_EXPERT:
    case Topics.PROJECT_EXPERT_LOAD_RELATED:
      return _expertProject(dialog);

    case Topics.PROJECT_FIRSTRUN:
      // Update the client
      if (dialog.me && dialog.context.project && dialog.context.project.project) {
        dialog.context.project.searching = false;
        dialog.context.project.done_searching = false;
        _sendSafeProject(dialog, dialog.context.project.project);
        // dialog.quickPing();
      }
      break;
     
    case Topics.PROJECT_LOGIN:
      if (dialog.me && dialog.context.project && dialog.context.project.project) {
        dialog.context.project.searching = false;
        dialog.context.project.done_searching = false;
        _sendSafeProject(dialog, dialog.context.project.project);
        dialog.setTopic(Topics.PROJECT_STEPS);
      }
      break;
  }
}

function setPrompt(dialog: Dialog) {
  const project_infos: ProjectInfo[] = dialog.getRepliesType(EntityType.Project) as ProjectInfo[];
  let project_info: ProjectInfo = null;
  if (project_infos.length) project_info = project_infos[0];

  let needs_clearing = true;
  // let contract_url = null;
  // const people = dialog.getRepliesType(EntityType.Person);

  switch (dialog.topic) {
    case Topics.PROJECT_JOB_EXPERT:
      dialog.clearDialog();
      // dialog.clearPing();
      dialog.addPrompt(lang.project.PROJECT_JOB_EXPERT_PROMPT);
      dialog.addAnswers(lang.project.PROJECT_JOB_EXPERT_ANSWERS);
      break;
    case Topics.CREATE_PROJECT:
      if (project_info) {
        project_info.added = true;
        dialog.addInfo(project_info, true);
        if (dialog.context.project.related_info) dialog.addInfo(dialog.context.project.related_info);
        dialog.setOpen(project_info);
        if (project_info.candidates && project_info.candidates.length && !dialog.isGuestAccount()) {
          dialog.addPrompt(lang.project.SEARCH_FOUND);
          dialog.addAnswers(lang.imports.IMPORTS_TUTORIAL_TYPE_ANSWERS.map(a => `Import ${a}`));
          dialog.reset('project');
        } else {
          dialog.setHide();
          dialog.setTopic(Topics.PROJECT_STEPS);
          // dialog.delayPing();
          dialog.reRun();
        }
      } else if (dialog.context.project) {
        if (dialog.context.project.draft_url) {
          dialog.lastPrompt('project', lang.project.DRAFT_PROMPT(dialog.context.project.draft_url), {clear:true});
        }
      }
      break;

    case Topics.PROJECT_CREATE_SAVING:
      break;

    case Topics.PROJECT_CONTRACTOR:
      if (!dialog.message.ping) {
        dialog.addPrompt(lang.project.PROJECT_CONTRACTOR_PROMPT);
        dialog.addAnswers(lang.project.PROJECT_CONTRACTOR_HINTS);
      }
      break;

    case Topics.PROJECT_PROPOSE:
      if (!dialog.message.ping && (dialog.context.project 
        && dialog.context.project.project && 
        !dialog.isProcessing(false) &&
        !dialog.context.project.project.client)) {
        if (dialog.context.project.searched_client) dialog.addPrompt(lang.project.PROPOSE_CLIENT_NOT_FOUND(dialog.context.project.searched_client), true);
        else {
          dialog.addPrompt(lang.project.PROPOSE_CLIENT, true);
          dialog.addAnswers(lang.project.CREATE_PROMPT_NEVERMIND);
        }
        dialog.setSticky();
        // dialog.clearPing();
      }
      break;

    case Topics.PROJECT_FIRSTRUN:
      /*
       * Tell the user that we're looking
       */
      dialog.newDialog();
      
      if (!dialog.context.project.search_prompt) {
        dialog.context.project.search_prompt = true;
        if (project_info.candidates && project_info.candidates.length) {
          if (project_info.candidates.length === 1) {
            dialog.addPrompt(lang.project.SEARCH_INVITE(project_info.candidates[0]), needs_clearing);
            dialog.addAnswers(lang.project.SEARCH_INVITE_ANSWERS);
            dialog.setTopic(Topics.PROJECT_CONFIRM_SEND);
          } else dialog.addPrompt(lang.project.SEARCH_ADDED, needs_clearing);
        } else {
          dialog.addPrompt(lang.project.FIRST_SEARCH);
          dialog.addAnswers(lang.imports.IMPORTS_CMD);
          dialog.setHide(30000);
        }
      }

      if (project_info) {
        project_info.added = true;
        dialog.addInfo(project_info);
        if (dialog.context.project.related_info) dialog.addInfo(dialog.context.project.related_info);
        dialog.setOpen(project_info);
      }

      if (dialog.checkTopic(Topics.PROJECT_CONFIRM_SEND)) {
        dialog.setClear();
        // dialog.context.project.saved = false;
        // dialog.context.project.saving = false;
        // dialog.reset('project');
      }
      else {
        dialog.reset('project');
        // dialog.defaultPing();
        // dialog.clearContext('project');
        // dialog.setTopic(Topics.DEFAULT);
      }
      break;

    case Topics.PROJECT_SKILLS:
    case Topics.PROJECT_EXPERT_SKILLS:
      if (dialog.message.ping || (dialog.context.project.loading_people && !dialog.context.project.people)) return;
      if (!dialog.context.project.project.skills || dialog.context.project.project.skills.length === 0) {
        if (dialog.context.project.people && dialog.context.project.people.length == 1) {
          const person = dialog.context.project.people[0];
          dialog.addPrompt(lang.project.PERSON_SKILLS_PROMPT(person), true);
          dialog.addAnswers(lang.project.CREATE_PROMPT_NEVERMIND);
          dialog.setSticky();
          // if (person.skills) dialog.addAnswers(person.skills.filter(s => !functions.IGNOREWORDS.includes(s)).slice(0, 5));
          // else dialog.addAnswers(lang.client.WELCOME_CHAT.answers);
        } else if (dialog.context.project.project.proposal) {
          const ignore = ['self', ...dialog.me.names]
          if (dialog.context.project.project.client.askfora_id === FORA_PROFILE) dialog.addPrompt(lang.project.TEMPLATE_SKILLS_PROMPT, true);
          else dialog.addPrompt(lang.project.PROPOSAL_SKILLS_PROMPT(dialog.context.project.project.client), true);
          dialog.addAnswers(dialog.me.tags.filter(t => t.type === TagType.skill && t.value && t.value.length && !ignore.includes(t.value)).sort((a,b) => b.index - a.index).map(t => t.value).slice(0,5));
        } else if(dialog.context.project.project.expert) {
          // if (!dialog.isAuthenticated()) dialog.addPrompt(lang.init.WELCOME_EXPERT, true);
          dialog.addPrompt(lang.project.EXPERT_SKILLS_PROMPT, dialog.isAuthenticated());
          // dialog.addAnswers(lang.project.SUGGEST_EXPERT);
          dialog.addHint(lang.project.EXPERT_SKILLS_HINT);
        } else {
          dialog.addPrompt(lang.project.SKILLS_PROMPT, true);
          if (dialog.context.project.project.rate !== ProjectRate.sourcing) dialog.addAnswers(lang.project.SUGGEST_SKILLS);
          dialog.addHint(lang.project.SKILLS_HINT);
          dialog.addQuickReply(lang.project.SUGGEST_SKILLS_ELSE, lang.project.SKILLS_ELSE_PROMPT);
        }
        // dialog.clearPing();
        // dialog.setSticky();
      } else dialog.addPrompt(lang.project.CONFIRM(dialog.context.project.project.skills), true);
      break;

    case Topics.PROJECT_GOAL:
    case Topics.PROJECT_EXPERT_GOAL:
      if (!dialog.message.ping) {
        if (dialog.context.project.project.expert) {
          dialog.addPrompt(lang.project.EXPERT_GOALS_PROMPT, true);
          dialog.addAnswers(lang.project.CREATE_PROMPT_NEVERMIND);
          dialog.addHint(lang.project.EXPERT_GOALS_HINT);
        } else if (dialog.context.project.project.proposal) {
          dialog.addPrompt(lang.project.PROPOSAL_GOALS_PROMPT, true);
          dialog.addAnswers(lang.project.CREATE_PROMPT_NEVERMIND);
          dialog.addHint(lang.project.PROPOSAL_GOALS_HINT);
        } else if(dialog.context.project.project.rate === ProjectRate.sourcing)  {
          dialog.addPrompt(lang.project.SOURCING_GOALS_PROMPT(dialog.context.project.project.skills), true);
          dialog.addAnswers(lang.project.CREATE_PROMPT_NEVERMIND);
          dialog.addHint(lang.project.SOURCING_GOALS_HINT);
        } else {
          dialog.addPrompt(lang.project.GOALS_PROMPT(dialog.context.project.project.skills), true);
          dialog.addAnswers(lang.project.CREATE_PROMPT_NEVERMIND);
          dialog.addHint(lang.project.GOALS_HINT);
        }
        // dialog.clearPing();
        dialog.setSticky();
      }
      break;

    case Topics.PROJECT_RATE:
      if (!dialog.message.ping) {
        const sourcing =  false; //  dialog.context.project.project.group_settings && dialog.context.project.project.group_settings.sourcing;
        let rate_prompt = lang.project.RATE_PROMPT(dialog.context.project.project.proposal, sourcing);
        let rate_answers = lang.project.RATE_ANSWERS(sourcing);
        if (dialog.context.project.project.group_settings && dialog.context.project.project.group_settings.skip_payment) {
          rate_prompt = lang.project.RATE_NO_PAY(sourcing);
          rate_answers = lang.project.RATE_FIXED_TIME(sourcing);
        }
        
        dialog.addPrompt(rate_prompt, true);
        dialog.addAnswers(rate_answers);
        dialog.addHint(lang.project.RATE_HINT);
        // dialog.clearPing();
        dialog.setSticky();
      }
      break;

    case Topics.PROJECT_FEE:
      if (!dialog.message.ping) {
        if (dialog.context.project.project.proposal)  dialog.addPrompt(lang.project.PROPOSAL_FEE_PROMPT(dialog.context.project.project.rate), true);
        else dialog.addPrompt(lang.project.FEE_PROMPT(dialog.context.project.project.rate), true);
        dialog.addHint(lang.project.FEE_HINT);
        // dialog.clearPing();
        dialog.setSticky();
      }
      break;
    case Topics.PROJECT_DURATION:
      if (!dialog.message.ping) {
        let needs_clearing = true;
        if (dialog.context.project.feeDefaulted && !dialog.context.project.feeDefaultedPrompted) {
          dialog.addPrompt(lang.project.FEE_UNKNOWN(dialog.context.project.project.fee), true);
          needs_clearing = false;
          dialog.context.project.feeDefaultedPrompted = true;
        }

        dialog.addPrompt(lang.project.DURATION_PROMPT(dialog.context.project.project.rate), needs_clearing);
        dialog.addAnswers(lang.project.DURATION_ANSWERS);
        dialog.addHint(lang.project.DURATION_HINT);
        dialog.setSticky();
        // dialog.clearPing();
      }
      break;

    case Topics.TEMPLATE_LINK:
      dialog.newDialog();
      dialog.lastPrompt('project', lang.project.TEMPLATE_LINK);
      break;

    case Topics.PROJECT_STEPS:
      if (dialog.context.project.durationDefaulted && !dialog.context.project.durationDefaultedPrompted) {
        dialog.addPrompt(lang.project.DURATION_UNKNOWN(dialog.context.project.project.rate, dialog.context.project.project.duration), true);
        needs_clearing = false;
        dialog.context.project.durationDefaultedPrompted = true;
      }

      if (dialog.isGuestAccount()) {
        if (project_info && dialog.context.project.save) {
          project_info.updated = true;
          project_info.edit = true;
          dialog.addInfo(project_info, true);
          if (dialog.context.project.related_info) dialog.addInfo(dialog.context.project.related_info);
          dialog.setOpen(project_info);
        } else {
          const project = dialog.context.project.project;
          const providers = AuthProvider.clientAuthSet(project.expert ? AuthContext.ProjectExpert : AuthContext.ProjectCreate, dialog.group_host);
          if (project.candidates && project.candidates.length)
            dialog.addPrompt(lang.project.FIRST_PROJECT_PROFILE(providers.map(p => p.name).sort(), project.candidates[0].nickName), needs_clearing);
          else if(project.expert) dialog.addPrompt(lang.project.FIRST_PROJECT_EXPERT(providers.map(p => p.name)).sort(), needs_clearing);
          else {
            if (!project.notes || !project.notes.length) {
              dialog.addPrompt(lang.project.DESCRIPTION_PROMPT, needs_clearing);
              needs_clearing = false;
            }
            dialog.addPrompt(lang.project.FIRST_PROJECT(providers.map(p => p.name).sort()), needs_clearing);
          }
          dialog.addAnswers(providers.map(p => p.name).sort());
          for (const provider of providers) {
            const qrs = AuthProvider.contextPermissions(project.expert ? AuthContext.ProjectExpert : AuthContext.ProjectCreate);
            qrs.push(lang.init.REGISTER_REPLY_LINK(provider));
            dialog.addQuickReply(provider.name, [] /* qrs */, {redirect: provider.url});
          }

          // dialog.clearPing();
          dialog.setSticky();
        }
      } else if (dialog.isAuthenticated()) {
        if (project_info) {
          if (project_info.template) {
            dialog.lastPrompt('project', lang.project.TEMPLATE_STEPS, {clear:true});
          } else if(project_info.proposal) {
            dialog.addPrompt(lang.project.PROPOSAL_STEPS(project_info.client), needs_clearing);
            dialog.addAnswers(lang.project.SEARCH_INVITE_ANSWERS);
            dialog.setTopic(Topics.PROJECT_CONFIRM_SEND_PROPOSAL);
          } else if (project_info.expert) {
            dialog.addPrompt(lang.project.EXPERT_STEPS, needs_clearing);
          } else if (!dialog.isAuthenticated(AuthLevel.Organizer) && 
            !(dialog.user.settings.people && dialog.user.settings.people.disallowed)) {
            if(!(dialog.context.project.project.candidates && dialog.context.project.project.candidates.length)) {
              const group = dialog.context.project && dialog.context.project.project &&
                dialog.context.project.project.group_settings && dialog.context.project.project.group_settings.group ?
                dialog.groupById(dialog.context.project.project.group_settings.group) : null;
              const auth = AuthProvider.clientAuthContext(project_info.expert ? AuthContext.ProjectExpert : AuthContext.ProjectCreate, dialog.user.provider, group, dialog.user.email);
              if (auth.url) dialog.addPrompt(lang.project.SEARCH_CONNECT_CONTACTS(auth.name, auth.url), needs_clearing);
              else {
                dialog.addPrompt(lang.project.SEARCH_CONNECT_CONTACTS_EMAIL, needs_clearing);
                dialog.addAnswers([lang.init.REG_CONNECT_CONTACTS, ...lang.imports.IMPORTS_TUTORIAL_TYPE_ANSWERS.map(a => `Import ${a}`)]);
              }
            }
            if (dialog.context.init) {
              dialog.context.init.auth_context = project_info.expert ? AuthContext.ProjectExpert : AuthContext.ProjectCreate;
              if (dialog.context.init.provider && !AuthProvider.isSupportedContext(dialog.context.init.provider, dialog.context.init.auth_context)) {
                delete dialog.context.init.provider;
              }
            }
            else dialog.context.init = { auth_context: project_info.expert ? AuthContext.ProjectExpert : AuthContext.ProjectCreate }
            if (dialog.context.email_login) delete dialog.context.email_login;
            dialog.setTopic(Topics.PROJECT_LOGIN);
            dialog.context.project.logging_in = true;
          } else if (project_info.candidates && project_info.candidates.length) {
            if (project_info.candidates.length === 1) {
              dialog.addPrompt(lang.project.SEARCH_INVITE(project_info.candidates[0]), needs_clearing);
              dialog.addAnswers(lang.project.SEARCH_INVITE_ANSWERS);
              dialog.setTopic(Topics.PROJECT_CONFIRM_SEND);
            }
            else dialog.addPrompt(lang.project.SEARCH_ADDED, needs_clearing);
          } else {
            if (!project_info.notes || !project_info.notes.length) dialog.addPrompt(lang.project.DESCRIPTION_PROMPT, needs_clearing);
            else dialog.addPrompt(lang.project.SEARCH_STEPS, needs_clearing);
          }
          project_info.added = true;
          dialog.addInfo(project_info);
          if (dialog.context.project && dialog.context.project.related_info) dialog.addInfo(dialog.context.project.related_info);
          dialog.setOpen(project_info);
        } else {
          dialog.addPrompt(lang.project.SEARCH_STEPS, needs_clearing);
        }
        if (dialog.checkTopic(Topics.PROJECT_STEPS)) dialog.reset('project');
        dialog.setClear();
      } else {
        logging.warnFP(LOG_NAME, 'setPrompt', dialog.user.profile, 'Project steps with anonymous account');
        dialog.addPrompt(lang.project.MISSING, needs_clearing);
        dialog.reset('project');
      }
      break;
  }
}

function _ensureProject(dialog: Dialog, proposal = false): boolean {
  if (!dialog.context.project) {
    dialog.newDialog();
    dialog.context.project = new ProjectPluginState();
    // const projects: ProjectInfo[] = dialog.actionValues(ActionType.PROJECT);
    dialog.context.project.timeout = SECONDS(new Date(), 90);

    logging.infoFP(LOG_NAME, 'ensureProject', dialog.user.profile, `setting client to ${JSON.stringify(dialog.me)}`);
    let client: Partial<Candidate>;
    let contractor: Partial<Candidate>;
    let candidates: Partial<Candidate>[];
    let rate: ProjectRate; 
    let sourcing_type: ProjectSourcingType;
    let duration: number;
    let payment;
    let fee: number;
    let group_settings: ProjectGroupSettings;
    let escrow;

    if (proposal) {
      dialog.addReplies(new Person({ id:dialog.me.id, vanity: dialog.user.vanity }));
      contractor = dehydratePerson(dialog.me);
      contractor.state = ProjectCandidateState.SELECTED;
      contractor.askfora_id = dialog.user.profile;
      candidates = [contractor];
    } else {
      client = new Person(dialog.me);
      client.self = true;
      client.askfora_id = dialog.user.profile;
      slimEntity(client);
      
      // add people
      const people = dialog.actionValues(ActionType.ENTITY);
      const filter_people = people ? people.filter(p => p.id !== 'found_people' || !lang.project.PROJECT_IGNORE_FOUND_ENTITIES.includes(p.name)) : [];
      if (filter_people && filter_people.length) {
        for (const person of filter_people) {
          if (person.people) {
            for (const candidate of person.people) {
              if (parsers.checkKeyword(person.name, candidate.name)) {
                dialog.addReplies(new Person({ id:candidate.id, vanity: candidate.vanity }));
              }
            }
          }
        }
      }

      const apeople = dialog.actionValues(ActionType.PERSON); 
      for (const candidate of apeople) {
        dialog.addReplies(new Person({ id:candidate.id, vanity: candidate.vanity }));
      }

      if (dialog.checkTopic(Topics.SOURCING)) {
        rate = ProjectRate.sourcing;
        sourcing_type = ProjectSourcingType.full;
        duration = 1;
        payment = lang.project.SKIP_PAYMENT(new Date());
        fee = 0;
        group_settings = { skip_payment: true, skip_contracting: true };
        escrow = lang.project.SKIP_ESCROW;
      }
    }

    const project = new Project({ 
      client, 
      candidates, 
      contractor, 
      service_fee: SERVICE_FEE,
      rate,
      sourcing_type,
      duration,
      payment,
      fee,
      group_settings,
      escrow,
    });

    const skills = dialog.actionValues(ActionType.SKILL);
    if (skills && skills.length) {
      //sort by length desc
      const sort_skills = skills.filter(s => !lang.project.PROJECT_SUFFIX.includes(s.toLowerCase())).sort((a,b) => b.length - a.length);
      if (sort_skills.length) {
        project.skills = sort_skills[0];
        for (const skill of sort_skills.slice(1)) {
          if (!project.skills.toLowerCase().includes(skill.toLowerCase())) {
            project.skills = `${project.skills} ${skill}`;
          }
        }
      }
    }

    dialog.context.project.project = project;
    project.proposal = proposal;
    
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'ensureProject', dialog.user.profile, `Creating new project ${JSON.stringify(dialog.context.project.project)}`);

    dialog.mapGroupProjectSettings(project);

    if (project.group_settings && 'service_fee' in project.group_settings) project.service_fee = project.group_settings.service_fee;

    const service_fee = dialog.context.project.project.contractor && !isNaN(dialog.context.project.project.contractor.service_fee) ? 
      dialog.context.project.project.contractor.service_fee : 
      project.group_settings && 'service_fee' in project.group_settings ? project.group_settings.service_fee :
      SERVICE_FEE;

    project.service_fee = service_fee;

    if (proposal) contractor.ready = dialog.user.hasAccount(AuthProviders.Stripe) || dialog.user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment);

    return true;
  } else if(dialog.isGuestAccount()) {
    const projects: ProjectInfo[] = dialog.actionValues(ActionType.PROJECT);
    if (projects && projects.length && projects[0].type === EntityType.Project) {
      const r_project = dialog.projects.receiveProject(projects[0], UPDATE_PROJECT_DESC_FIELDS);
      if (r_project && r_project.project) {
        dialog.context.project.project = r_project.project;
        dialog.context.project.updated = dialog.context.project.updated || r_project.updated;
      }
    }
  }

  if (dialog.isProcessing() /*context.project.loading*/ && new Date(dialog.context.project.timeout) < new Date()) {
    if (!dialog.isDoneProcessing()) {
      // dialog.quickPing();
      return false;
    } else if (!dialog.context.project.project) {
      if (new Date(dialog.context.project.timeout) < new Date()) {
        logging.warnFP(LOG_NAME, 'ensureProject', dialog.user.profile, 'timeout loading project');
        dialog.setTopic(Topics.PROJECT_TIMEOUT);
        dialog.resetProcessing();
      } else {
        logging.warnFP(LOG_NAME, 'ensureProject', dialog.user.profile, 'project missing');
        dialog.setTopic(Topics.PROJECT_MISSING);
        dialog.resetProcessing();
      }
      dialog.resetProcessing();
      return false;
    }
  }
  return true;
}

// save project and get a clean copy to send to user
function _saveSendProject(dialog: Dialog, next_topic: Topic = null) {
  if (!dialog.context.project) {
    dialog.setTopic(Topics.DEFAULT);
    return;
  }

  if (!dialog.isProcessing()) {
    dialog.context.project.saved_topic = next_topic ? next_topic : dialog.topic;
    dialog.setTopic(Topics.PROJECT_CREATE_SAVING);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'saveSendProject', dialog.user.profile, `saving project with next topic ${dialog.context.project.saved_topic}`);

    if (dialog.context.project.project.proposal ||
      dialog.context.project.project.client.self || 
      ( dialog.context.project.project.me_candidate && 
        checkState(dialog.context.project.project.me_candidate, ProjectCandidateState.VIEWED) >= 0)) { 
      dialog.runAsync('project', async () => {
        const project = dialog.context.project.project;
        if (dialog.context.project.new_contract) {
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'saveSendProject', dialog.user.profile, `Creating new contract for project ${project.id}`);
          const contract = await dialog.contracts.create(dialog.context.project.new_contract);
          dialog.context.project.new_contract.id = contract.id;
          project.contract = contract.id;
        }

        let p;
        if (project.proposal) {
          p = await dialog.projects.create(project, false);
          await dialog.projects.updateShared(p);
        } else p = await dialog.projects.create(project);

        if (dialog.context.project && dialog.context.project.related_entity) {
          switch(dialog.context.project.related_entity.type) {
            case EntityType.Ask: {
              const ask = dialog.context.project.related_entity as Ask;
              if (!ask.projects) ask.projects = [];
              ask.projects.push(p.id );
              if (!ask.chat) ask.chat = [];
              ask.chat.push({component: ChatComponent.Project, id: p.id});
              await dialog.asks.update(ask);
              dialog.context.project.related_info = askInfo(ask);
            }
          }
        }

        if (dialog.context.project) {
          dialog.context.project.project = p;
        }
      });
    }
  } else if (dialog.isDoneProcessing()) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'saveSendProject', dialog.user.profile, `saved project, next topic ${next_topic}`);
    dialog.resetProcessing();
    _sendSafeProject(dialog, dialog.context.project.project);
    if (dialog.context.project.saved_topic ) dialog.setTopic(dialog.context.project.saved_topic);
    dialog.context.project.saved_topic = null;
  }
}

// remove sensitive info before sending project
function _sendSafeProject(dialog: Dialog, project: Project) {
  // const is_contractor: boolean = project.client && (!dialog.me || project.client.id !== dialog.me.id);
  const reply_project: ProjectInfo = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client && project.client.askfora_id == dialog.user.profile);
  dialog.addReplies(reply_project);
}

function _repeatProject(dialog: Dialog) {
  if (!dialog.context.project) {
    dialog.newDialog();
    dialog.context.project = new ProjectPluginState();
  }

  const [template] = dialog.actionValues(ActionType.PROJECT);
  if (template && template.template) {
    dialog.context.project.project = new Project({ id:template.id} );
  } else if(template) {
    const r_project = dialog.projects.receiveProject(template, []);
    if (r_project && r_project.project) dialog.context.project.project = r_project.project;
    // dialog.context.project.project = template;
  }

  if(!dialog.context.project.project || !dialog.context.project.project.id) {
    dialog.setTopic(Topics.PROJECT_MISSING);
    return;
  }

  if (dialog.context.project.project.id) {
    logging.infoFP(LOG_NAME, 'repeatProject', dialog.user.profile, `Loading ${dialog.context.project.project.id} to repeat it`);
    const wait_for_reload = _reloadProject(dialog, template && template.template);   
    if (!wait_for_reload) return;
    logging.infoFP(LOG_NAME, 'repeatProject', dialog.user.profile, `Loaded ${JSON.stringify(dialog.context.project.project)}`);
  }

  const as_client = !dialog.context.project.project.contractor ||
    dialog.context.project.project.contractor.askfora_id !== dialog.user.profile ||
    dialog.context.project.project.client.self;

  let project;

  if (as_client) {
    const client = new Person(dialog.me);
    client.self = true;
    client.askfora_id = dialog.user.profile;
    const client_email = dialog.user.email ? [dialog.user.email] : parsers.findEmail(client.comms);
    client.comms = client_email ? client_email.concat(client.id) : [client.id];
    slimEntity(client);
    project = new Project({ client });
    logging.infoFP(LOG_NAME, 'repeatProject', dialog.user.profile, `Repeating ${dialog.context.project.project.id} as client`);
  } else {
    project = new Project({ client: dialog.context.project.project.client, proposal: true });
    logging.infoFP(LOG_NAME, 'repeatProject', dialog.user.profile, `Repeating ${dialog.context.project.project.id} as contractor`);
  }

  delete project.last_update;

  dialog.projects.copyProject(project, dialog.context.project.project);
  dialog.mapGroupProjectSettings(project);
  dialog.context.project.project = project;

  delete project.id;
  delete project.archived;
  delete project.completed;
  delete project.escrow;
  delete project.payment;
  delete project.transfer;
  delete project.refund;
  delete project.accepted;
  delete project.declined;
  delete project.viewed;

  project.title = `Copy of ${project.title}`;

  const discount_fee = dialog.context.project.project.payment &&
    dialog.context.project.project.service_fee === SERVICE_FEE ? DISCOUNT_FEE : dialog.context.project.project.service_fee;

  const service_fee = dialog.context.project.project.contractor && dialog.context.project.project.contractor.service_fee ? 
    dialog.context.project.project.contractor.service_fee : discount_fee;

  project.service_fee = service_fee;
  project.progress = 0;
  project.start = getStartDate(project.duration, project.rate);
  project.end = getEndDate(project.duration, project.rate, new Date(project.start));
  const candidates = [];
  for (const pcandidate of project.candidates) {
    const candidate = projectPerson(project, pcandidate);
    if (as_client || candidate.askfora_id === dialog.user.profile) candidates.push(candidate);
    if (as_client || !candidate.askfora_id || candidate.askfora_id !== dialog.user.profile) { 
      candidate.state = as_client ? ProjectCandidateState.INVITED : ProjectCandidateState.SELECTED;
    } else if(candidate.askfora_id === dialog.user.profile) candidate.state = ProjectCandidateState.SELECTED;
  }
  project.candidates = candidates;

  if (as_client) {
    if (project.contractor) {
      project.contractor.network = false;
      project.contractor.state = ProjectCandidateState.INVITED;
      project.candidates = [project.contractor];
      project.profile = true;
    }
    delete project.contractor;
    delete project.contract;
    delete project.select_notice;
  } else {
    project.contractor = project.candidates.find(c => c.state == ProjectCandidateState.SELECTED);
    project.contractor.self = true;
    project.proposal = true;
  }

  dialog.context.project.open = true;

  project.last_update = new Date();
  project.last_activty = new Date();

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'repeatProject', dialog.user.profile, `setup complete ${JSON.stringify(dialog.context.project.project)}`);
  dialog.setTopic(Topics.PROJECT_STEPS);

  _saveSendProject(dialog);
}

function _proposeTemplate(dialog: Dialog) {
  if (!dialog.context.project) {
    dialog.newDialog();
    dialog.context.project = new ProjectPluginState();
  }

  const [template] = dialog.actionValues(ActionType.PROJECT);
  if (template) {
    const r_project = dialog.projects.receiveProject(template, []);
    if (r_project && r_project.project) dialog.context.project.project = r_project.project;
    // dialog.context.project.project = template;
  }

  if(!dialog.context.project.project || !dialog.context.project.project.id) {
    dialog.setTopic(Topics.PROJECT_MISSING);
    return;
  }

  if (dialog.context.project.project.id) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'repeatProject', dialog.user.profile, `Loading ${dialog.context.project.project.id} to repeat it`);
    const wait_for_reload = _reloadProject(dialog);   
    if (!wait_for_reload) return;
    // dialog.context.project.loading = false;
    // dialog.context.project.loaded = false;
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'repeatProject', dialog.user.profile, `Loaded ${JSON.stringify(dialog.context.project.project)}`);
  }

  const project = new Project({client: new Person(dialog.context.project.project.client) });
  dialog.projects.copyProject(project, dialog.context.project.project);

  project.client = null;
  project.start = getStartDate(project.duration, project.rate);
  project.end = getEndDate(project.duration, project.rate, new Date(project.start));
  dialog.context.project.project = project;
  dialog.context.project.added = true;

  dialog.setTopic(Topics.PROJECT_PROPOSE);

  return _proposeProject(dialog);
}

function _proposeProject(dialog: Dialog, template = false) {
  _ensureProject(dialog, true);
  
  const project = dialog.context.project.project;

  if (template) project.client = lang.about.ABOUT_INFO[0];

  if (project.client) {
    dialog.newDialog();
    dialog.message.message = '';
    dialog.setTopic(Topics.PROJECT_SKILLS);
    return _setupProject(dialog);
  }

  _ensureProjectClient(dialog);
}

function _expertProject(dialog: Dialog) {
  dialog.createGuest();

  _ensureProject(dialog);

  const project = dialog.context.project.project;

  if (!project.expert) {
    project.expert = true;
    project.duration = 1;
    project.start = new Date();
    project.end = project.start;
    project.fee = 0;
    project.rate = ProjectRate.fixed;
    project.group_settings = { skip_payment: true, skip_contracting: true };
    project.escrow = lang.project.SKIP_ESCROW;
  }

  if (dialog.context.project.related_entity && !dialog.isProcessing()) {
    const asks = dialog.actionValues(ActionType.ASK);
    if (asks && asks.length) dialog.context.project.related_entity = { id: asks[0].id, type: asks[0].type };  

    dialog.runAsync('project', async () => {
      switch(dialog.context.project.related_entity.type) {
        case EntityType.Ask: {
          const ask = await dialog.asks.get(dialog.context.project.related_entity.id); 
          dialog.context.project.related_entity = ask;

          let skill_set = ask.categories.map(c => c.label);
          let skills = skill_set.join(' ').trim();
          if (!skills.length) {
            skills = 'New Question';
            skill_set = ['New Question'];
          }

          dialog.context.project.project.skill_set = skill_set;
          dialog.context.project.project.skills = skills;

          if (!dialog.context.project.project.candidates || !dialog.context.project.project.candidates.length) {
            dialog.context.project.project.candidates = ask.candidates.map(c => projectPerson(dialog.context.project.project, c, {state: ProjectCandidateState.FOUND})); 
          }

          break;
        }
      }
    });
    dialog.setTopic(Topics.PROJECT_EXPERT_LOAD_RELATED);
    return true;
  }

  if (dialog.checkTopic(Topics.PROJECT_EXPERT_LOAD_RELATED) && dialog.isProcessing() && !dialog.isDoneProcessing()) {
    return;
  }

  // dialog.clearPing();
  dialog.setTopic(Topics.PROJECT_EXPERT_SKILLS);
  dialog.resetProcessing();
  return _setupProject(dialog);
}

// drives initial project definition
function _setupProject(dialog: Dialog) {
  // ignore commands with projects passed in
  const errant_projects = dialog.actionValues(ActionType.PROJECT);
  if (errant_projects && errant_projects.length) return;
  if (dialog.isProcessing() && !dialog.isDoneProcessing()) return;

  dialog.createGuest();

  _ensureProject(dialog);

  const single_hire = dialog.actionValues(ActionType.CMD).includes(lang.project.FROM_PROFILE);
 
  // Ensure all the questions have been answered
  if (!dialog.context.project.project.proposal) {
    const checked_candidates = _ensureProjectCandidates(dialog, single_hire);
    if (!checked_candidates) {
      const s_res = _ensureProjectSkills(dialog, false)
      if (s_res) {
        // grab info if we can find it
        const r_res = _ensureProjectRate(dialog, false);
        const f_res = _ensureProjectFee(dialog, false);
        const d_res = _ensureProjectDuration(dialog, false);

        // We only ever assume description/goal if the message gave us everything else
        if (r_res && f_res && d_res) _ensureProjectDescription(dialog, false);
      }
      return;
    }
  }

  if (!_ensureProjectSkills(dialog)) return;
  else {
    if (parsers.checkKeyword(dialog.message.message, lang.project.PROJECT_KWD, true)) {
      // grab info if we can find it
      const r_res = _ensureProjectRate(dialog, false);
      const f_res = _ensureProjectFee(dialog, false);
      const d_res = _ensureProjectDuration(dialog, false);

      // We only ever assume description/goal if the message gave us everything else
      if (r_res && f_res && d_res) _ensureProjectDescription(dialog, false);
    }
  }

  if (!_ensureProjectDescription(dialog)) return;
  else {
    // grab info if we can find it
    // _ensureProjectRate(dialog, false);
    // _ensureProjectFee(dialog, false);
    // _ensureProjectDuration(dialog, false);
  }

  if (!_ensureProjectRate(dialog)) return;
  else {
    // grab info if we can find it
    _ensureProjectFee(dialog, false);
    _ensureProjectDuration(dialog, false);
  }

  if (!_ensureProjectFee(dialog)) return;
  else _ensureProjectDuration(dialog, false);

  if (!_ensureProjectDuration(dialog)) return;

  // All the questions have been answered.
  const project = dialog.context.project;
  // Done adding people the client wanted
  if (project.people && project.people.length) {
    // use discount fee if we start with a candidate
    project.project.service_fee = DISCOUNT_FEE;
    dialog.runAsync('project', async () => {
      if (dialog.context.project?.project) {
        const state = dialog.context.project.state ?  dialog.context.project.state : ProjectCandidateState.FOUND;
        await dialog.projects.saveCandidates(dialog.context.project.project, dialog.context.project.people, state, project.people.map(p => p.id), -1);
      }
    });
  } else {
    dialog.context.project.added = true;
    dialog.setTopic(Topics.PROJECT);
  }

  if (dialog.isDoneProcessing()) {
    dialog.context.project.added = true;
    dialog.setTopic(Topics.PROJECT);
    dialog.resetProcessing();
  }

  if (project.project.proposal && !project.project.contract) {
    for (const index in dialog.cache.contracts) {
      const contract = dialog.cache.contracts[index];
      if (project.project.contractor.comms.includes(contract.contractor_email) && 
        project.project.client.comms.includes(contract.client_email) &&
        !contract.client_declined && !contract.contractor_declined) {
        project.project.contract = contract.id;
        if (!contract.contractor_id) contract.contractor_id = dialog.user.profile;
        if (!contract.client_reviewed || !contract.contractor_reviewed ||
          !contract.client_signed || !contract.contractor_signed) dialog.context.project.new_contract = contract;
        break;
      }
    }

    // add contractor to replies so we can loop back and start the contract process
    if (!project.project.contract && !project.project.proposal) {
      const client_email = parsers.findEmail(project.project.client.comms);
      dialog.context.project.new_contract = new Contract({
        client_id: project.project.client.askfora_id,
        client_name: project.project.client.displayName,
        client_email: client_email ? client_email[0] : null,
        contractor_id: project.project.contractor.askfora_id,
        contractor_name: project.project.contractor.displayName,
        contractor_email: dialog.user.email,
        context: project.project.skills,
      });  
    }
  }

  let next_topic = Topics.PROJECT_STEPS;
  if (dialog.isGuestAccount()) next_topic = Topics.CREATE_PROJECT;
  else if(dialog.checkTopic(Topics.PROJECT) && project.project.candidates && project.project.candidates.length) {
    dialog.context.project.open = true;
  }

  logging.infoFP(LOG_NAME, 'setupProject', dialog.user.profile, `setup complete, moving on to ${next_topic}`);
  dialog.setTopic(next_topic);

  _saveSendProject(dialog);
  return false;
}

function getDefaultDuration(dialog: Dialog): number {
  switch (dialog.context.project.project.rate) {
    case ProjectRate.hourly: return 1; // hours
    case ProjectRate.daily: return 1; // days
    case ProjectRate.fixed: return 1; // hours
  }
}

function getMaxDuration(dialog: Dialog): number {
  switch (dialog.context.project.project.rate) {
    case ProjectRate.hourly: return 100000; // hours
    case ProjectRate.daily: return 4000; // days
    case ProjectRate.fixed: return 1; // hours
  }
}

function getDefaultFee(dialog: Dialog): number {
  switch (dialog.context.project.project.rate) {
    case ProjectRate.hourly: return 50;
    case ProjectRate.daily: return 400;
    case ProjectRate.fixed: return 2000;
  }
}

function _ensureProjectDescription(dialog: Dialog, resolve = true): boolean {
  _ensureProject(dialog);

  if (!resolve || dialog.checkTopic([Topics.PROJECT_GOAL, Topics.PROJECT_EXPERT_GOAL])) {
    if (dialog.context.project.project.notes === undefined) {
      const notes = dialog.actionValues(ActionType.RAW);
      const skills = dialog.getActions(ActionType.SKILL);
      const numbers = dialog.getActions(ActionType.NUMBER_ROUNDED);
      let desc = notes && notes.length ? notes[0] : null;
      if(!resolve) {
        desc  = notes && notes.length ? parsers.extractLast([
          ...skills.map(s => s.context ? s.context : s.value), 
          ...numbers.map(s => s.context ? s.context : ''),
          dialog.context.project.project.rate ? dialog.context.project.project.rate : '',
          ...dialog.context.project.project.rate ? lang.project.PROJECT_RATE_KWDS[dialog.context.project.project.rate] : []
        ], notes[0]) : null;
      }
      if (desc && desc.length) {
        dialog.context.project.project.notes = desc;
      }
    }
  }

  // If the notes are still null, we need to prompt the user (via eetPrompt)
  if (resolve && dialog.context.project.project.notes === undefined) {
    if (dialog.context.project.project.expert) dialog.setTopic(Topics.PROJECT_EXPERT_GOAL);
    else dialog.setTopic(Topics.PROJECT_GOAL);
    return false;
  } else {
    return true;
  }
}

function _ensureProjectDuration(dialog: Dialog, resolve = true) {
  _ensureProject(dialog);

  if (!resolve || dialog.checkTopic(Topics.PROJECT_DURATION)) {
    const project = dialog.context.project.project;
    if (project.duration === undefined) {
      const lc_msg = dialog.message.message.toLowerCase();
      if (resolve) {
        const duration = dialog.actionValues(ActionType.NUMBER_ROUNDED);
        if (duration && duration.length) project.duration = Math.min(duration[0], getMaxDuration(dialog));
        else {
          project.duration = getDefaultDuration(dialog);
          dialog.context.project.durationDefaulted = true;
        }
      } else if (project.rate) {
        // strict - only match numbers that align with the rate
        // TODO - This needs to change so we are not parsing anything here. We should follow the addAction/actionValues pattern but for a NUMBER/duration type item
        const duration = parsers.findDuration(lc_msg, project.rate);
        if (duration && duration.length) project.duration = Math.min(parsers.parseNumberRounded(duration[0]), getMaxDuration(dialog));
      }

      if (project.duration < 1) {
        project.duration = getDefaultDuration(dialog);
        dialog.context.project.durationDefaulted = true;
      }
    }

    if (project.duration && project.rate) {
      if (!project.start || !new Date(project.start).getTime()) project.start = getStartDate(project.duration, project.rate);
      if (!project.end || !new Date(project.end).getTime()) project.end = getEndDate(project.duration, project.rate, new Date(project.start));
    }
  }

  // If the duration is still null, we need to prompt the user (via setPrompt)
  if (resolve && dialog.context.project.project.duration === undefined) {
    dialog.setTopic(Topics.PROJECT_DURATION);
    return false;
  } else {
    return true;
  }
}

function _ensureProjectFee(dialog: Dialog, resolve = true) {
  _ensureProject(dialog);

  if (!resolve || dialog.checkTopic(Topics.PROJECT_FEE)) {
    if (dialog.context.project.project.fee === undefined) {
      if (resolve) {
        const fee = dialog.actionValues(ActionType.NUMBER_ROUNDED);
        if (fee && fee.length) dialog.context.project.project.fee = Math.min(fee[0], Number.MAX_SAFE_INTEGER);
        else {
          dialog.context.project.project.fee = getDefaultFee(dialog);
          dialog.context.project.feeDefaulted = true;
        }
      } else {
        // TODO - This needs to change so we are not parsing anything here. We should follow the addAction/actionValues pattern but for a NUMBER/duration type item
        const fee = parsers.findFee(dialog.message.message);
        if (fee && fee.length) dialog.context.project.project.fee = Math.min(parsers.parseNumberRounded(fee[0].replace(/,/g, '')), Number.MAX_SAFE_INTEGER);
      }
    }
  }

  // If the fee is still null, we need to prompt the user (via setPrompt)
  if (resolve && dialog.context.project.project.fee === undefined) {
    if (dialog.context.project.project.group_settings && dialog.context.project.project.group_settings.skip_payment) {
      dialog.context.project.project.fee = 0;
      return true;
    }
      
    dialog.setTopic(Topics.PROJECT_FEE);
    return false;
  } else {
    return true;
  }
}

function _ensureProjectRate(dialog: Dialog, resolve = true) {
  _ensureProject(dialog);

  if (!resolve || dialog.checkTopic(Topics.PROJECT_RATE)) {
    // TODO - This needs to change so we are not parsing anything here. We should follow the addAction/actionValues pattern but for a NUMBER/duration type item
    if (dialog.context.project.project.rate === undefined) {
      const rate = dialog.message.message.toLowerCase();
      if (rate in ProjectRate) dialog.context.project.project.rate = rate.split(' ')[0] as ProjectRate;
      // if (lang.project.PROJECT_RATES.includes(rate)) dialog.context.project.project.rate = rate.split(' ')[0] as ProjectRate;
      else {
        const rates = stripPuncs(rate, true).split(' ');
        for (const rate_word of rates) {
          if (rate_word in ProjectRate) {
          //if(lang.project.PROJECT_RATES.includes(rate_word)) {
            dialog.context.project.project.rate = rate_word as ProjectRate;
            break;
          }
        }

        if (!dialog.context.project.project.rate) {
          for (const rate_word of rates) {
            const rate_mapped = lang.project.PROJECT_RATE_MAP[rate_word];
            if (rate_mapped) {
              dialog.context.project.project.rate = rate_mapped;
              break;
            } 
          }
        }
      }

      if (dialog.context.project.project.rate === ProjectRate.fixed) {
        dialog.context.project.project.duration = getDefaultDuration(dialog);
      }

      if (dialog.context.project.project.rate === ProjectRate.sourcing) {
        const sourcing =  dialog.context.project.project.group_settings && dialog.context.project.project.group_settings.sourcing;
        if (sourcing) {
          if (rate.includes('contract')) dialog.context.project.project.sourcing_type = ProjectSourcingType.contract;
          else if (rate.includes('part')) dialog.context.project.project.sourcing_type = ProjectSourcingType.part;
          else  dialog.context.project.project.sourcing_type = ProjectSourcingType.full;
          dialog.context.project.project.duration = 1;
          dialog.context.project.project.payment = lang.project.SKIP_PAYMENT(new Date());
          dialog.context.project.project.fee = 0;
          dialog.context.project.project.group_settings = { skip_payment: true, skip_contracting: true };
          dialog.context.project.project.escrow = lang.project.SKIP_ESCROW;
        } else dialog.context.project.project.rate = ProjectRate.fixed;
      }
    }
  }

  // If the rate is still null, we need to prompt the user (via setPrompt)
  if (resolve && dialog.context.project.project.rate === undefined) {
    dialog.setTopic(Topics.PROJECT_RATE);
    return false;
  } else {
    return true;
  }
}

function _ensureProjectSkills(dialog: Dialog, resolve = true) {
  _ensureProject(dialog);

  if (dialog.checkTopic([Topics.PROJECT, Topics.PROJECT_SKILLS, Topics.PROJECT_EXPERT_SKILLS])) {
    if (dialog.context.project.project.skills === undefined) {

      // if we have a candidate already and we prompted, just use the raw message
      if (dialog.checkTopic([Topics.PROJECT_SKILLS, Topics.PROJECT_EXPERT_SKILLS]) && dialog.context.project.people &&
        dialog.context.project.people.length) {
        dialog.context.project.project.skills = dialog.message.message;
        dialog.context.project.project.title = dialog.message.message;
      } else {
        const ignore = lang.project.PROJECT_SUFFIX.concat(
          dialog
            .actionValues(ActionType.ENTITY)
            .filter(e => e.name && e.people && e.people.length)
            .map(e => e.name.toLowerCase()),
        );
        const act_skills = dialog.actionValues(ActionType.SKILL);
        const filtered = act_skills.filter(s => !ignore.includes(s));
        const skills = filtered.length ? filtered : act_skills;
        if (skills && skills.length) {
          if (filtered.length || !parsers.checkKeyword(dialog.message.message, lang.project.PROJECT_KWD, true)) {
            const skill_set = skills.join(' ').split(' ').filter((v, i, a) => v !== a[i + 1]);
            dialog.context.project.project.skills = skill_set.join(' ');
            if (!dialog.context.project.project.title) {
              dialog.context.project.project.title = dialog.context.project.project.skills;
            }
          }
        } else if(dialog.checkTopic([Topics.PROJECT_SKILLS, Topics.PROJECT_EXPERT_SKILLS]) && dialog.message.message.length) {
          dialog.context.project.project.skills = dialog.message.message;
          dialog.context.project.project.title = dialog.message.message;
        }
      }
    }
  } else dialog.removeActions(ActionType.SKILL);

  // If the skills are still null, we need to prompt the user (via setPrompt)
  if (resolve && dialog.context.project.project.skills === undefined) {
    // DO NOT SAVE PROJECT DURING CREATION STEPS
    if (dialog.context.project.project.expert) dialog.setTopic(Topics.PROJECT_EXPERT_SKILLS);
    else dialog.setTopic(Topics.PROJECT_SKILLS);
    return false;
  } else return true;
}

function _ensureProjectCandidates(dialog: Dialog, single_hire = false) {
  _ensureProject(dialog);

  let load_ids = [];
  const id_vanities: { [key: string]: string } = {};
  const req_candidates: Partial<UndefinedPersonInfo>[] = dialog.actionValues(ActionType.ENTITY);
  if (req_candidates && req_candidates.length && !dialog.checkTopic(IGNORE_ENTITY_TOPICS)) {
    for (const req_candidate of req_candidates) {
      if (req_candidate.people) {
        for (const person of req_candidate.people) {
          if (parsers.checkKeyword(person.name, req_candidate.name)) {
            logging.infoFP(LOG_NAME, 'ensureProjectCandidates', dialog.user.profile, `Adding candidate ${person.id}: ${person.name} matches ${req_candidate.name}`);
            load_ids.push(person.id);
            if (person.vanity) id_vanities[person.id] = person.vanity;
          }
        }
      }
    }
  }

  const apeople: Partial<PersonInfo>[] = dialog.actionValues(ActionType.PERSON);
  if (apeople) {
    for (const person of apeople.filter(p => p.id)) {
      if (!load_ids.includes(person.id)) {
        logging.infoFP(LOG_NAME, 'ensureProjectCandidates', dialog.user.profile, `Adding candidate ${person.id} from action`);
        load_ids.push(person.id);
      }
      if (person.vanity) id_vanities[person.id] = person.vanity;
    }
  }

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'ensureProjectCandidates', dialog.user.profile, `Load action ids ${util.format(load_ids)}`);

  // List of user ids that were added
  for (const person of dialog.getTopicRepliesType(EntityType.Person, [...topics, Topics.FOUND_PERSON])) {
    if (!load_ids.includes(person.id)) {
      logging.infoFP(LOG_NAME, 'ensureProjectCandidates', dialog.user.profile, `Adding candidate ${person.id} from replies`);
      load_ids.push(person.id);
    }
    if (person.vanity) id_vanities[person.id] = person.vanity;
  }

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'ensureProjectCandidates', dialog.user.profile, `Load reply ids ${util.format(load_ids)}`);

  // don't include the built in contacts
  const have_ids = dialog.context.project.project && dialog.context.project.project.candidates ? dialog.context.project.project.candidates.map(c => c.id) : [];
  load_ids = _.uniq(load_ids.filter(id => id && !id.includes('people/f') && !have_ids.includes(id) && (!dialog.me || (dialog.me && id !== dialog.me.id))));

  if (load_ids.length && !dialog.isProcessing()) {
    dialog.context.project.loading_people = true;
    dialog.context.project.people = null;
    dialog.runAsync('project', async () => {
      const persons = await dialog.people.byId(load_ids);
      if (persons && dialog.context.project) {
        const people = [];
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'ensureProjectCandidates', dialog.user.profile, `Have ids ${util.format(have_ids)}`);
        for (let person of persons) {
          if (!person.id || !person.id.startsWith('people/')) {
            if (dialog.isAuthenticatedNonGuest()) person = await dialog.projects.createProjectPerson(person, true);
            else person = await dialog.people.temp(person, true);
          }
          if (single_hire && persons.length === 1) {
            dialog.context.project.state = ProjectCandidateState.INVITED
            dialog.context.project.project.profile = true;
          }
          else dialog.context.project.state = null;
          person.comms = parsers.findEmail(person.comms);
          if (person.id) person.comms.push(person.id);
          if (id_vanities[person.id]) {
            const global_user = await data.users.globalByVanity(id_vanities[person.id]);
            if (global_user) {
              person.askfora_id = global_user.profile;
              if (global_user.email) {
                person.comms = [global_user.email];
                if (person.id) person.comms.push(person.id);
              }
            }
          }
          if (!have_ids.includes(person.id)) {
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'ensureProjectCandidates', dialog.user.profile, `Adding candidate ${util.format(person)}`);
            people.push(dehydratePerson(person, dialog.context.project.project.searched_skills ? dialog.context.project.project.searched_skills : null, dialog.isGuestAccount()));
            have_ids.push(person.id);
          }
        }
        dialog.context.project.people = people;
      } else if (dialog.context.project && !dialog.context.project.project.candidates) dialog.context.project.project.candidates = [];
    });

    return false;
  } else if(dialog.context.project.loading_people) {
    if(dialog.isDoneProcessing()) {
      dialog.resetProcessing();
      if (dialog.context.project.people) return true;
      return false;
    }
  } else if (!dialog.context.project.project.candidates) dialog.context.project.project.candidates = [];

  return true;
}

function _ensureProjectClient(dialog: Dialog) {
  _ensureProject(dialog);

  // const project = dialog.context.project;
  const curr_dialog = dialog.currentDialog();

  if (curr_dialog && curr_dialog.next_topic === Topics.PI_FOUND_PERSON) {
    if (curr_dialog.replies.length) {
      const client_id = curr_dialog.replies[0].id;
      dialog.clearDialog(true);
      // dialog.context.project.loading = true;
      dialog.runAsync('project', async () => {
        const project = dialog.context.project;
        const persons = await dialog.people.byId(client_id);
        if (persons && persons.length) {
          let person = persons[0];
          if (dialog.isAuthenticatedNonGuest()) person = await dialog.projects.createProjectPerson(person, true);
          else person = await dialog.people.temp(person, true);
          project.project.client = projectPerson(project.project, person);
          project.project.client.self = false;
          const global_user = project.project.client.askfora_id ? await data.users.globalById(project.project.client.askfora_id) : null;
          if (global_user && global_user.email) project.project.client.comms = [global_user.email, client_id];
          else {
            const users = await data.users.globalByEmail(project.project.client.comms);
            if (users && users.length) {
              project.project.client.askfora_id = users[0].profile;
              project.project.client.comms = [users[0].email, client_id];
            } else {
              const client_email = parsers.findEmail(project.project.client.comms);
              project.project.client.comms = client_email ? client_email.concat(client_id) : [client_id];
            }
          }
        } else {
          logging.warnFP(LOG_NAME, 'ensureProjectClient', dialog.user.profile, `Found person not loaded for id ${client_id}`);
        }
      });
      logging.infoFP(LOG_NAME, 'ensureProjectClient', dialog.user.profile, `Load found client ids ${client_id}`);
    } else {
      // TODO: person not found
      logging.infoFP(LOG_NAME, 'ensureProjectClient', dialog.user.profile, 'No one found');
      return true;
    }
  }

  let client_id;
  let client_name;

  const apeople: Partial<PersonInfo>[] = dialog.actionValues(ActionType.PERSON);
  if (apeople && apeople.filter(p => p.id).length) client_id = apeople.find(p => p.id).id;

  const req_client: Partial<UndefinedPersonInfo>[] = dialog.actionValues(ActionType.ENTITY);
  if (!client_id && req_client && req_client.length && !dialog.checkTopic(IGNORE_ENTITY_TOPICS) && !dialog.isProcessing()) {
    if (req_client[0].people && req_client[0].people[0] && req_client[0].people[0].id) {
      if (req_client[0].people.length > 1 || req_client.filter(c => c.people && c.people.length).length > 1) {
        // pick list
        dialog.context.project.searched_client = req_client[0].name;
        logging.infoFP(LOG_NAME, 'ensureProjectClient', dialog.user.profile, `Pick client ${JSON.stringify(req_client)}`);
        return personInfo.findPersonPrompt(dialog);
      }

      client_id = req_client[0].people[0].id
    } else {
      client_name = req_client[0].name;
    }
  }
    
  if (client_id) {
    // dialog.context.project.loading = true;
    dialog.runAsync('project', async () => {
      const project = dialog.context.project;
      if (project) {
        const persons = await dialog.people.byId(client_id);
        if (persons && persons.length) {
          let person = persons[0];
          if (dialog.isAuthenticatedNonGuest()) {
            if (person.network) person = await dialog.projects.createProjectPerson(person, true);
          } else person = await dialog.people.temp(person, true);
          project.project.client = projectPerson(project.project, person);
          project.project.client.self = false;

          const global_user = project.project.client.askfora_id ? await data.users.globalById(project.project.client.askfora_id) : null;
          if (global_user && global_user.email) project.project.client.comms = [global_user.email, client_id];
          else {
            const users = await data.users.globalByEmail(project.project.client.comms);
            if (users && users.length) {
              project.project.client.askfora_id = users[0].profile;
              project.project.client.comms = [users[0].email, client_id];
            } else {
              const client_email = parsers.findEmail(project.project.client.comms);
              project.project.client.comms = client_email ? client_email.concat(client_id) : [client_id];
            }
          }
        } else {
          logging.warnFP(LOG_NAME, 'ensureProjectClient', dialog.user.profile, `Person not found for id ${client_id}`);
        }
      }
    });
    logging.infoFP(LOG_NAME, 'ensureProjectClient', dialog.user.profile, `Load client ids ${client_id}`);
  } else if(client_name) {
    dialog.context.project.searched_client = client_name;
    logging.infoFP(LOG_NAME, 'ensureProjectClient', dialog.user.profile, `Load client ${JSON.stringify(req_client[0])}`);
    return personInfo.findPersonPrompt(dialog);
  }

  // dialog.quickPing();
}

function _projectSteps(dialog: Dialog) {
  const cmds = dialog.actionValues(ActionType.CMD);
  if( cmds && cmds.length) dialog.context.project.save = cmds.includes(lang.project.CMD_PROJECT_SAVE);

  const wait_for_ensure = _ensureProject(dialog);
  const project: Project = dialog.context.project.project;

  if (!project) {
    logging.warnFP(LOG_NAME, 'projectCmd', dialog.user.profile, `Ensure returned with no project`);
    dialog.setTopic(Topics.PROJECT_TIMEOUT);
    // dialog.clearPing();
    return;
  }

  // show the project
  if (dialog.isProcessing()) { 
    if (dialog.isDoneProcessing()) {
      _sendSafeProject(dialog, project);
      /*dialog.clearPing();
      dialog.context.project.saving = false;
      dialog.context.project.saved = false;*/
      return;
    } else {
      // dialog.quickPing();
      return false;
    }
  }

  // check that project is ready
  try { dialog.projects.checkProject(project, dialog.isAuthenticated()); } catch (e) {
    logging.warnFP(LOG_NAME, '_projectCmd', dialog.user.profile, 'error checking project', e);
    dialog.setTopic(Topics.PROJECT_INVALID);
  }

  dialog.runAsync('project', async () =>  {
    const p = await dialog.projects.update(project, !project.archived);
    if (dialog.context.project) {
      dialog.context.project.project = p;
    }
  });
}

function _reloadProject(dialog: Dialog, force_reload = false) {
  if (!dialog.context.project) {
    dialog.reset();
    return false;
  }

  if (dialog.isProcessing()) {
    if (!dialog.isDoneProcessing()) {
      return false;
    } else if (!dialog.context.project.project) {
      if (new Date(dialog.context.project.timeout) < new Date()) {
        logging.warnFP(LOG_NAME, 'reloadProject', dialog.user.profile, 'timeout loading project');
        dialog.setTopic(Topics.PROJECT_TIMEOUT);
      } else {
        logging.warnFP(LOG_NAME, 'reloadProject', dialog.user.profile, 'project missing');
        dialog.setTopic(Topics.PROJECT_MISSING);
      }
      return false;
    }

    return true;
  }

  if (!dialog.context.project.project.id) return true;

  const project = dialog.context.project.project;
  dialog.runAsync('project', async () => {
    const p = await dialog.projects.get(project, force_reload);

    if (dialog.context.project) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'reloadProject', dialog.user.profile, `project loaded ${dialog.context.project.project.id}`);
      dialog.context.project.project = p;
    } else logging.warnFP(LOG_NAME, 'reloadProject', dialog.user.profile, `project context missing while loading ${p.id}`);

    if (!dialog.context.project) dialog.reset();
  });
  return false;
}

export default {
  name: lang.project.PROJECT_NAME,
  description: lang.project.PROJECT_DESC,
  examples: lang.project.PROJECT_EG,
  reserved: lang.project.PROJECT_RESERVED,
  requiresAuth: false,
  keywordMatch,
  isActive,
  runAction,
  setAction,
  setPrompt,
  shortcuts: () => { return null; },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
