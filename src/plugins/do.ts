import config from '../config';
import data from '../data';
import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';
import ForaUser from '../session/user';
import { FORA_PROFILE } from '../types/user';

import { ActionType, TemplateType } from '../types/globals';
import { Event, Message, Person, Project, Task } from '../types/items';
import { DoPluginState, Plugin } from '../types/plugins';
import { AuthLevel, EntityType, NotificationType, PersonInfo, ProjectCandidateState, ProjectRate, Uid } from '../types/shared';

import { DAYS, HOURS } from '../utils/datetime';
import { createRawMessage, draftMessageLink, localeDate, messageLink } from '../utils/format';
import { checkState } from '../utils/funcs';
import { messageInfo, promptInfo, taskInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import stripe from '../sources/stripe_controller';

const LOG_NAME = 'plugins.do';
const DO_NAME = 'Do-er';
const DO_DESC = 'Take action on tasks.';

TOPIC('DO');

const DO_KWD = []
  .concat(lang.doit.CLEAR_KWD)
  .concat(lang.doit.DONE_KWD)
  .concat(lang.doit.IGNORE_KWD)
  .concat(lang.doit.NOT_DONE_KWD)
  .concat(lang.doit.READ_KWD)
  .concat(lang.doit.ARCHIVE_KWD)
  .concat(lang.doit.LATE_KWD)
  .concat(lang.doit.CANCEL_KWD)
  .concat(lang.doit.DRAFT_KWD)
  .concat(lang.doit.DELETE_KWD);

function keywordMatch(actions, message: string) {
  return parsers.checkKeyword(message, DO_KWD, true);
}

function setAction(dialog: Dialog, message: string) {
  if (dialog.message.link) return false;
  if (isActive(dialog)) return true;

  const act_tasks = dialog.actionValues(ActionType.TASK);
  const act_messages = dialog.actionValues(ActionType.MESSAGE);
  const act_events = dialog.actionValues(ActionType.EVENT);
  const act_notes = dialog.actionValues(ActionType.NOTE);
  const act_projects = dialog.actionValues(ActionType.PROJECT);
  const act_people = dialog.actionValues(ActionType.PERSON);
  const act_type = dialog.actionValues(ActionType.ENTITY_TYPE);
  const act_entities = dialog.actionValues(ActionType.ENTITY);

  let info = null;
  let update = null;
  if (act_tasks && act_tasks.length) {
    info = dialog.cache.tasks[act_tasks[0].id];
    update = act_tasks[0];
  }

  if (!info) {
    if (act_messages && act_messages.length) {
      for (const dmessage of dialog.cache.messages) {
        if (dmessage.id === act_messages[0].id) {
          info = dmessage;
          update = act_messages[0];
          break;
        }
      }
    }
  }

  if (!info) {
    if (act_events && act_events.length) {
      for (const devent of dialog.cache.events) {
        if (devent.id === act_events[0].id) {
          info = devent;
          update = act_events[0];
          break;
        }
      }
    }
  }

  /* if (!info) {
    info = act_notes && act_notes.length ? dialog.cache.notes[act_notes[0].id] : null;
  } */

  if (!info) {
    info = act_projects && act_projects.length ? dialog.cache.projects[act_projects[0].id] : null;
    update = act_projects[0];
  }

  if (!info) {
    if (act_people && act_people.length && act_people[0].type === EntityType.Person && act_people[0].id) {
      const person_info = act_people[0] as Partial<PersonInfo>;
      info = new Person({
        id:person_info.id,
        displayName:person_info.name,
      });
    }
  }

  // actions on types for people
  if (!info && config.isEnvDevelopment()) {
    if (act_entities && act_entities.length &&  act_entities[0].people && act_entities[0].people.length &&
        act_type && act_type.length) {
      const person_info = act_entities[0].people[0] as Partial<PersonInfo>;
      info = new Person({
        id: person_info.id,
        displayName: person_info.name,
      });
    }
  }

  // check the last dialog replies
  if (!info) {
    const info_set = dialog.getLastRepliesType([EntityType.Task, EntityType.Message, EntityType.Event, EntityType.Note, EntityType.Project, EntityType.Person], true);
    if (info_set && info_set.length) info = info_set[0];
  }

  if (info && !dialog.context.do) {
    dialog.context.do = new DoPluginState();
    dialog.context.do.return_to = dialog.topic;
    dialog.context.do.update = update;
    dialog.newDialog();
    dialog.addReplies(info, dialog.context.do.return_to);
    dialog.setTopic(Topics.DO);
  }

  if (!info) return false;

  switch (info.type) {
    case EntityType.Task:
      if (parsers.checkKeyword(message, lang.doit.IGNORE_KWD, true)) dialog.addAction(ActionType.CMD, lang.doit.IGNORE);
      else if (parsers.checkKeyword(message, lang.doit.CLEAR_KWD, true))  dialog.addAction(ActionType.CMD, lang.doit.CLEAR);
      else if (parsers.checkKeyword(message, lang.doit.DONE_KWD, true)) dialog.addAction(ActionType.CMD, lang.doit.DONE);
      else if (parsers.checkKeyword(message, lang.doit.DRAFT_KWD, true)) dialog.addAction(ActionType.CMD, lang.doit.DRAFT);
      else if (parsers.checkKeyword(message, lang.doit.DELETE_KWD, true)) dialog.addAction(ActionType.CMD, lang.doit.DELETE);
      else if (parsers.checkKeyword(message, lang.doit.NOT_DONE_KWD, true)) dialog.addAction(ActionType.CMD, lang.doit.UNDO);
      return true;
    /* case EntityType.Message:
      dialog.context.do.requires_auth = AuthLevel.Email;
      if (parsers.checkKeyword(message, lang.doit.READ_KWD)) dialog.addAction(ActionType.CMD, lang.doit.READ);
      else if (parsers.checkKeyword(message, lang.doit.ARCHIVE_KWD) || parsers.checkKeyword(message, lang.doit.DELETE_KWD)) dialog.addAction(ActionType.CMD, lang.doit.ARCHIVE);
      return true; */
    case EntityType.Event:
      dialog.context.do.requires_auth = AuthLevel.Organizer;
      if (parsers.checkKeyword(message, lang.doit.LATE_KWD)) dialog.addAction(ActionType.CMD, lang.doit.LATE);
      else if (parsers.checkKeyword(message, lang.doit.CANCEL_KWD)) dialog.addAction(ActionType.CMD, lang.doit.CANCEL);
      else if (parsers.checkKeyword(message, lang.doit.DONE_KWD)) dialog.addAction(ActionType.CMD, lang.doit.DONE);
      return true;
    case EntityType.Note:
      if (parsers.checkKeyword(message, lang.doit.DELETE_KWD)) dialog.addAction(ActionType.CMD, lang.doit.DELETE);
      else if (parsers.checkKeyword(message, lang.doit.DRAFT_KWD, true)) dialog.addAction(ActionType.CMD, lang.doit.DRAFT);
      return true;
    case EntityType.Project:
      if (parsers.checkKeyword(message, lang.doit.ARCHIVE_KWD)) dialog.addAction(ActionType.CMD, lang.doit.ARCHIVE);
      else if (parsers.checkKeyword(message, lang.doit.DELETE_KWD)) dialog.addAction(ActionType.CMD, lang.doit.DELETE);
      else if (parsers.checkKeyword(message, lang.doit.CANCEL_KWD)) dialog.addAction(ActionType.CMD, lang.doit.CANCEL);
      return true;
    case EntityType.Person:
      if (parsers.checkKeyword(message, lang.doit.DELETE_KWD)) dialog.addAction(ActionType.CMD, lang.doit.DELETE);
      return true;
    default:
      return false;
  }
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(Topics.DO);
}

function _completeDrafting(dialog) {
  if (dialog.context.do && dialog.context.do.drafting) {
    if (dialog.context.do.ids) {
      if (!dialog.context.do.loading) {
        dialog.context.do.loading = true;
        dialog.waitForRun().then(async () => {
          const r = await dialog.people.findById(dialog.context.do.ids);
          if (dialog.context.do) {
            dialog.context.do.recipient = r;
          }
        }).catch(err => dialog.asyncError(err));
      }
    }

    if (dialog.context.do.recipient) {
      dialog.context.do.ids = null;
      const recipient = dialog.context.do.recipient;
      if (!dialog.context.do.message) dialog.context.do.message = lang.doit.DRAFT_FOLLOW_UP(dialog.user.name, recipient);
      dialog.context.do.recipient = null;
      if (dialog.isAuthenticated(AuthLevel.Email)) {
        dialog.waitForRun().then(async () => {
          const draft = await dialog.messages.create(dialog.me, recipient, dialog.context.do.context, dialog.context.do.message);
          if (dialog.context.do) {
            if (draft) {
              if (dialog.context.do.send) {
                dialog.context.do.drafting = false;
                dialog.messages.send(draft);
              } else {
                dialog.context.do.draft_link = draftMessageLink(dialog.user.provider, dialog.user.email, draft);
              }
            } else dialog.context.do.drafting = false;
          }
        }).catch(err => dialog.asyncError(err));
      } else {
        const draft = createRawMessage(recipient, dialog.me, dialog.me.email, dialog.context.do.context, dialog.context.do.message);
        dialog.context.do.draft_link = messageLink(draft.raw);
        dialog.context.do.drafting = false;
      }
    }

    dialog.quickPing();
    return false;
  }

  return true;
}

async function _updateTask(dialog: Dialog) {
  const task = dialog.currentDialog().replies[0] as Task;
  const update_task = dialog.context.do ? dialog.context.do.update as Task : null;

  if (update_task) {
    task.due = update_task.due;
    task.title = update_task.title;
    task.notes = update_task.notes;
    task.people = update_task.people ? update_task.people.map(p => { return {id: p.id} }) : [];
    if (task.people && task.people.length) {
      const people = await dialog.people.byId(task.people.map(p => p.id));
      task.people = people.map(p => { return {
        id: p.id,
        displayName: p.displayName,
        nickName: p.nickName,
        network: p.network,
        photos: p.photos,
      }});
    }

    return dialog.tasks.update(task);
  }
}

function _runTask(dialog: Dialog) {
  const act = dialog.actionValues(ActionType.CMD);
  const when = dialog.actionValues(ActionType.DATE);
  const task = dialog.currentDialog().replies[0] as Task;
  const bool = dialog.actionValues(ActionType.BOOL);

  if ((!bool || !bool.length) && dialog.isProcessing()) {
    if (dialog.isDoneProcessing()) dialog.newDialog();
    else {
      // dialog.quickPing();
      return;
    }
  }

  if (act.length) {
    let due = task ? new Date(task.due) : null;

    switch (act[0]) {
      case lang.doit.DONE:
        dialog.runAsync('do', async () => {
          await _updateTask(dialog);
          const rtask = await dialog.tasks.complete(task);
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_runTask', dialog.user.profile, `completed task ${rtask.id}`);
          if (dialog.context.do) {
            dialog.context.do.info = taskInfo(rtask, false, dialog.message.offset, dialog.message.locale, dialog.message.timeZone);
            dialog.context.do.info.updated = true;
            dialog.context.do.prompt = promptInfo(lang.doit.CONFIRM_COMPLETE);
          }
        });

        dialog.context.do.prompt = promptInfo('');
        break;
      case lang.doit.UNDO:
        dialog.runAsync('do', async () => {
          await _updateTask(dialog)
          const rtask = await dialog.tasks.uncomplete(task);
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_runTask', dialog.user.profile, `completed task ${rtask.id}`);
          if (dialog.context.do) {
            dialog.context.do.info = taskInfo(rtask, false, dialog.message.offset, dialog.message.locale, dialog.message.timeZone);
            dialog.context.do.info.updated = true;
            dialog.context.do.clear = true;
          }
        });

        dialog.context.do.prompt = promptInfo('');
        break;

      case lang.doit.IGNORE:
        // delay the task for four days
        if (when && when.length) {
          task.due = when[0].start;
          if (task.due < HOURS(new Date(), 4)) task.due = HOURS(new Date(), 4);
        } else {
          if (due.getTime() === 0) due = new Date();
          task.due = DAYS(due, 4);
        }

        dialog.runAsync('do', async () => {
          const rtask = await dialog.tasks.update(task);
          if (dialog.context.do) {
            dialog.context.do.info = taskInfo(rtask, false, dialog.message.offset, dialog.message.locale, dialog.message.timeZone);
            dialog.context.do.info.updated = true;
            dialog.context.do.prompt = promptInfo(lang.doit.CONFIRM_IGNORE);
          }
        });

        break;
      case lang.doit.CLEAR:
        task.due = null;
        case lang.doit.DRAFT:
        dialog.runAsync('do', async () => {
          const rtask = await dialog.tasks.update(task);
          if (dialog.context.do) {
            dialog.context.do.info = taskInfo(rtask, false, dialog.message.offset, dialog.message.locale, dialog.message.timeZone);
            dialog.context.do.info.updated = true;
            dialog.context.do.prompt = promptInfo(lang.doit.CONFIRM_CLEAR);
          }
        });

        if (!dialog.context.do.loading) {
          dialog.context.do.loading = true;
          dialog.runAsync('do', async () => {
            await _updateTask(dialog);
            if (dialog.context.do) {
              dialog.context.do.recipient = await dialog.people.byId(task.people.map(p => p.id));
              dialog.context.do.context = task.title;
              dialog.context.do.message = lang.doit.DRAFT_FOLLOW_UP(dialog.user.name, task.people, task.notes);
            }
          });
        }
        // dialog.quickPing();
        return false;
      case lang.doit.DELETE:
        if (task.project)  {
          dialog.context.do.prompt = promptInfo(lang.client.CANNOT_DELETE_NOTE);
          break;
        } else {
          // dialog.context.do.processing = true;
          dialog.context.do.return_to = Topics.DO;
          const created_date = new Date(task.created).getTime();
          const fmt_date = !isNaN(created_date) && created_date !== 0 ? localeDate(task.created, dialog.user.locale, dialog.user.timeZone) : null;
          dialog.context.do.prompt = lang.doit.CONFIRM_DELETE_NOTE(task.title, fmt_date, task.people ? task.people : []);
          dialog.context.do.answers = lang.doit.CONFIRM_DELETE_ANSWERS('delete');
          dialog.context.do.sticky = true;
          return;
        }
    }
  } else if (bool.length) {
    dialog.newDialog();
    dialog.context.do.return_to = Topics.DEFAULT;
    dialog.context.do.prompt = promptInfo('');
    dialog.context.do.answers = null;
    dialog.context.do.sticky = false;
    dialog.context.do.clear = false;

    if (bool[0]) {
      dialog.runAsync('do', async () => {
        await dialog.tasks.delete(task);
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_runTask', dialog.user.profile, `completed task ${task.id}`);
      });

      dialog.context.do.info = taskInfo(task, false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
      dialog.context.do.info.deleted = true;
      dialog.context.do.hide = true;
    } else {
      dialog.context.do.hide = true;
    }
  } else if (dialog.isProcessing(false)) {
    dialog.context.do.prompt = promptInfo('');
  } else {
    dialog.context.do.prompt = dialog.isDoneProcessing() ? dialog.context.do.prompt : promptInfo('');
    dialog.context.do.sticky = false;
    dialog.context.do.clear = true;
    dialog.context.do.answers = [];
  }

  let rt = dialog.context.do.return_to;
  if (dialog.isProcessing(false)) { 
    rt = Topics.DO;
  }
  dialog.addReplies(dialog.currentDialog().replies, rt);

  return true;
}

function _runMessage(dialog: Dialog) {
  const act = dialog.actionValues(ActionType.CMD);
  const message = dialog.currentDialog().replies[0] as Message;

  if (dialog.isProcessing()) {
    if (dialog.isDoneProcessing()) dialog.newDialog();
    else {
      return;
    }
  }

  if (act.length) {
    switch (act[0]) {
      case lang.doit.READ:
        dialog.runAsync('do', async () => {
          await dialog.messages.read(message);
          if (dialog.context.do) {
            // dialog.context.do.done = true;
            dialog.context.do.info = messageInfo(message, false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
            if (dialog.context.do.return_to === Topics.DO) dialog.context.do.prompt = promptInfo('');
          }
        });
        break;
      case lang.doit.ARCHIVE:
        dialog.runAsync('do', async () => {
          await dialog.messages.archive(message);
          if (dialog.context.do) {
            dialog.context.do.prompt = null;
            dialog.context.do.info = messageInfo(message, false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
            dialog.context.do.info.deleted = true;
            if (dialog.context.do.return_to === Topics.DO) dialog.context.do.prompt = promptInfo('');
          }
        });
        break;
    }
  }

  let rt = dialog.context.do.return_to;
  if (dialog.isProcessing(false)) {
    dialog.context.do.prompt = promptInfo('');
    rt = Topics.DO;
  }
  dialog.addReplies(dialog.currentDialog().replies, rt);

  return true;
}

function _runEvent(dialog: Dialog) {
  const act = dialog.actionValues(ActionType.CMD);
  const event = dialog.currentDialog().replies[0] as Event;

  // if (dialog.context.do.processing) {
  if (dialog.isProcessing()) {
    if (dialog.isDoneProcessing()) dialog.newDialog();
    else {
      // dialog.quickPing();
      return;
    }
  }

  if (act.length) {
    switch (act[0]) {
      case lang.doit.LATE:
        dialog.context.do.drafting = true;
        dialog.context.do.context = 'Running late';
        dialog.context.do.message = lang.doit.RUNNING_LATE(dialog.user.name, event);
        dialog.context.do.send = true;
        dialog.context.do.ids = [];

        if (event.people) {
          for (const person of event.people) {
            if (!person.self) dialog.context.do.ids.push(person.id);
          }
        }

        return false;
      case lang.doit.CANCEL:
        dialog.runAsync('do', async () => {
          await dialog.events.cancel(event);
        });
        break;
      case lang.doit.DONE:
        // TODO(all) Need to replace this with some indication (maybe on the event itself) to indicate that the user doesn't want to be reminded about this event again
        // // attach an empty note to an event to indicate we're done
        // dialog.notes.create({id: null, notes: [], people: [], names: [], events: [item]}).catch(e => dialog.asyncError(e));
        break;
    }
  }

  let rt = dialog.context.do.return_to;
  if (dialog.isProcessing(false)) { //context.do.processing && !dialog.context.do.done) {
    // dialog.quickPing();
    rt = Topics.DO;
  }
  dialog.addReplies(dialog.currentDialog().replies, rt);

  return true;
}

function _runPerson(dialog: Dialog) {
  const act = dialog.actionValues(ActionType.CMD);
  const entity = dialog.actionValues(ActionType.ENTITY_TYPE);
  const bool = dialog.actionValues(ActionType.BOOL);
  const person = dialog.currentDialog().replies[0] as Partial<Person>;

  if (act.length) {
    // dialog.context.do.processing = true;
    dialog.context.do.return_to = Topics.DO;

    if (entity && entity.length && config.isEnvDevelopment()) {
      switch(entity[0]) {
        case EntityType.Contract:
          dialog.context.do.act_on = EntityType.Contract;
          dialog.context.do.prompt = lang.doit.CONFIRM_DELETE_CONTRACT(person);
          dialog.context.do.answers = lang.doit.CONFIRM_DELETE_ANSWERS(act[0], true);
          break;
        default: 
          dialog.context.do.return_to = Topics.DEFAULT;
          dialog.context.do.answers = null;
          dialog.context.do.clear = true;
          dialog.context.do.sticky = false;
          dialog.context.do.prompt = promptInfo('');
          break;
      }
    } else {
      dialog.context.do.prompt = lang.doit.CONFIRM_DELETE_PERSON(person);
      dialog.context.do.answers = lang.doit.CONFIRM_DELETE_ANSWERS(act[0]);
    }
    dialog.context.do.sticky = true;
  } else if (bool.length) {
    // dialog.context.do.processing = true;
    dialog.context.do.return_to = Topics.DEFAULT;
    dialog.context.do.answers = null;
    dialog.context.do.clear = true;
    dialog.context.do.sticky = false;
    dialog.context.do.prompt = promptInfo('');

    if (bool[0]) {
      dialog.runAsync('do', async () => {
        if (dialog.context.do.act_on) {
          const has_person = (await dialog.people.byId(person.id))[0];
          let other_users = null;
          if (has_person && has_person.comms) other_users = await data.users.globalByEmail(has_person.comms);
          if (other_users && other_users.length) {
            const other_ids = other_users.map(o => o.profile);
            const contract_ids: Uid[] = [];
            switch(dialog.context.do.act_on) {
              case EntityType.Contract:
                for (const contract of Object.values(dialog.cache.contracts)) {
                  if (other_ids.includes(contract.client_id) || other_ids.includes(contract.contractor_id)) {
                    if (!contract_ids.includes(contract.id)) {
                      await dialog.contracts.delete(contract);
                      contract_ids.push(contract.id);
                    }
                  }
                }

                for (const project of await dialog.projects.byContract(contract_ids)) { // Object.values(dialog.cache.projects)) {
                  //if (contract_ids.includes(project.contract)) {
                  project.contract = null;
                  project.archived = false;
                  project.completed = false;
                  if (project.escrow && project.escrow.id === lang.project.SKIP_ESCROW.id) project.escrow = lang.project.SKIP_ESCROW;
                  else project.escrow = null;
                  project.payment = null;
                  project.refund = null;
                  project.select_notice = false;
                  project.progress = 0;
                  project.accepted = false;

                  if (!project.proposal) {
                    if (project.contractor) {
                      const candidate = project.candidates.find(c => c.askfora_id === project.contractor.askfora_id);
                      if (candidate) candidate.state = ProjectCandidateState.ACCEPTED;
                      project.contractor = null;
                    }

                    project.candidates.forEach(c => {
                      if (checkState(c, ProjectCandidateState.SELECTED) >= 0) c.state = ProjectCandidateState.ACCEPTED;
                    });
                  }

                  await dialog.projects.create(project, project.client.self === true);
                  if (!project.client.self) await dialog.projects.updateShared(project);
                  //}
                }

                break;
              default:
            }
          }
          
          if (dialog.context.do) {
            dialog.context.do.prompt = null;
            dialog.context.do.answers = null;
            dialog.context.do.info = dialog.getPersonInfo();
            dialog.context.do.info.updated = true;
            // dialog.context.do.done = true;
            dialog.context.do.hide = true;
          }
        } else {
          await dialog.people.delete(person);
          if (dialog.context.do) {
            dialog.context.do.prompt = null;
            dialog.context.do.answers = null;
            dialog.context.do.info = dialog.getPersonInfo(person);
            dialog.context.do.info.archived = false;
            dialog.context.do.info.deleted = true;
            dialog.context.do.info.updated = false;
            // dialog.context.do.done = true;
            dialog.context.do.hide = true;
          }
        }
      });
    } else {
      // dialog.context.do.processing = false;
      // dialog.context.do.done = true;
      dialog.context.do.hide = true;
      // dialog.clearPing();
    }

    // if (dialog.context.do.processing && !dialog.context.do.done) dialog.quickPing();
  } else if (dialog.isProcessing(false)) { // dialog.context.do.processing && !dialog.context.do.done) {
    dialog.context.do.prompt = promptInfo('');
    // dialog.quickPing();
  }
  else {
    dialog.context.do.prompt = promptInfo('');
    dialog.context.do.sticky = false;
    dialog.context.do.answers = [];
    dialog.context.do.clear = true;
    // dialog.clearPing();
  }


  return true;
}

function _runProject(dialog: Dialog) {
  const act = dialog.actionValues(ActionType.CMD);
  const bool = dialog.actionValues(ActionType.BOOL);
  const project = dialog.currentDialog().replies[0] as Project;

  if (act.length) {
    dialog.context.do.return_to = Topics.DO;

    const new_proposal = project.proposal && project.client.self && !project.progress && !project.completed && (!project.escrow || project.escrow.id === lang.project.SKIP_ESCROW.id);

    const project_type = project.expert ? 'question' : 
      project.proposal && project.client && project.client.askfora_id == FORA_PROFILE ? 'service offering' : 'job';
    dialog.context.do.prompt = new_proposal ? lang.doit.CONFIRM_DECLINE_PROJECT(project.title, project.contractor.displayName) :
      lang.doit.CONFIRM_DELETE_PROJECT(act[0], project.title, project_type, new Date(project.start), dialog.message.locale, dialog.message.timeZone);
    dialog.context.do.answers = lang.doit.CONFIRM_DELETE_ANSWERS(new_proposal ? 'decline' : act[0]);
    dialog.context.do.sticky = true;
  } else if (bool.length) {
    // TODO: notify client or contractors
    dialog.context.do.return_to = Topics.DEFAULT;
    dialog.context.do.answers = [];
    dialog.context.do.clear = true;
    dialog.context.do.sticky = false;

    if (bool[0]) {
      // escrow is https://stripe.com/docs/api#charge_object
      // refund is https://stripe.com/docs/api#refund_object

      // TODO: archvie attached note
      if ((!project.progress || project.rate ===  ProjectRate.fixed) && 
          !project.completed && project.escrow && !(project.escrow instanceof Array) && project.escrow.id !== lang.project.SKIP_ESCROW && (!project.refund || project.refund instanceof Array)) {
        if (project.client.self) {
          dialog.context.do.prompt = lang.doit.REFUND(project.escrow.amount / 100);
          dialog.runAsync('do', async () => {
            const refund = project.escrow.charges ?
              await stripe.refund(project.escrow.charges.data[0].id, project.escrow.on_behalf_of, project.escrow.amount / 100) : 
              lang.stripe.REFUND_STUB(lang.project.SKIP_CHARGE.id, project.escrow.amount);
            project.archived = true;
            project.refund = refund;
            project.last_update = new Date();           
            project.last_activity = project.last_update;
            const updated_project = await dialog.projects.update(project);
            if (dialog.context.do) {
              dialog.context.do.info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, updated_project, updated_project.client.self === true);
              dialog.context.do.info.archived = true;
              dialog.context.do.info.deleted = false;
              dialog.context.do.hide = true;
              dialog.context.do.close = true;
            }
          });
        } else {
          dialog.context.do.prompt = lang.doit.CONTRACTOR_CANCEL(project.client.displayName);
          dialog.runAsync('do', async () => {
            const refund = project.escrow.charges ?
              await stripe.refund(project.escrow.charges.data[0].id, project.escrow.on_behalf_of, project.escrow.amount / 100) :
              lang.stripe.REFUND_STUB(lang.project.SKIP_CHARGE.id, project.escrow.amount);
            project.archived = true;
            project.refund = refund;
            project.last_update = new Date();           
            project.last_activity = project.last_update;
            const updated_project = await dialog.projects.update(project, false);
            if (dialog.context.do) {
              dialog.context.do.info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, updated_project, updated_project.client.self === true);
              dialog.context.do.info.archived = true;
              dialog.context.do.info.deleted = false;
              dialog.context.do.info.updated = false;
              dialog.context.do.hide = true;
              dialog.context.do.close = true;
            }

            const to_email = await dialog.projects.clientEmail(updated_project)
            const email = {
              rcpts: [{ Name: updated_project.client.displayName, Email: to_email }],
              subject: lang.project.CONTRACTOR_CANCEL_SUBJECT(updated_project),
              message: lang.project.CONTRACTOR_CANCEL_MESSAGE(updated_project, dialog.projects.getUrl(updated_project, to_email)),
            }

            await dialog.projects.notify(updated_project, new ForaUser(updated_project.contractor.askfora_id), updated_project.contractor, email, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Closed});
          });
        }
      } else {
        if (project.completed) {
          project.archived = true;
          project.last_update = new Date();           
          project.last_activity = project.last_update;
          dialog.runAsync('do', async () => {
            const updated_project = await dialog.projects.update(project, project.client.self === true);
            if (dialog.context.do) {
              dialog.context.do.info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, updated_project, updated_project.client.self === true);
              dialog.context.do.info.archived = true;
              dialog.context.do.info.deleted = false;
              dialog.context.do.info.updated = false;
              dialog.context.do.hide = true;
              dialog.context.do.close = true;
            }
          });
        } else {
          if (project.proposal) project.declined = true;
          project.last_update = new Date();           
          project.last_activity = project.last_update;
          dialog.runAsync('do', async () => {
            // update contractor first
            await dialog.projects.update(project);
            await dialog.projects.delete(project);
            if (dialog.context.do) {
              dialog.context.do.info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self === true);
              dialog.context.do.info.archived = false;
              dialog.context.do.info.deleted = true;
              dialog.context.do.info.updated = false;
              dialog.context.do.hide = true;
              if (project.proposal) dialog.context.do.prompt = lang.doit.CLIENT_DECLINE(project.contractor.displayName);
            }

            if (project.client.self) {
              // notify other candidates
              for (const candidate of project.candidates) {
                if (candidate.state === ProjectCandidateState.ACCEPTED || (!project.proposal && project.contractor && candidate.askfora_id == project.contractor.askfora_id)) {
                  const candidate_emails = parsers.findEmail(candidate.comms);
                  const candidate_to_email = candidate_emails  && candidate_emails.length ? candidate_emails[0] : null;
                  const email_candidate = {
                    rcpts: [{ Name: candidate.displayName, Email: candidate_to_email }],
                    ccs: [{ Name: dialog.me.displayName, Email: dialog.user.email }],
                    subject: lang.project.CONTRACT_FILLED_SUBJECT(project),
                    message: lang.project.CONTRACT_FILLED_MESSAGE(project, candidate),
                  };

                  await dialog.projects.notify(project, new ForaUser(candidate.askfora_id), candidate, email_candidate, { group: dialog.getNotifyGroup(candidate_to_email), notification: NotificationType.Project_Closed});
                }
              }

              if (project.proposal && project.contractor) {
                const emails = parsers.findEmail(project.contractor.comms);
                const contractor_email = emails && emails.length ? emails[0] : null;
                const email_contractor = {
                  rcpts: [{ Name: project.contractor.displayName, Email: contractor_email }],
                  ccs: [{ Name: dialog.me.displayName, Email: dialog.user.email }],
                  subject: lang.project.PROPOSAL_DECLINED_SUBJECT(project),
                  message: lang.project.PROPOSAL_DECLINED_MESSAGE(project),
                };

                await dialog.projects.notify(project, new ForaUser(project.contractor.askfora_id), project.contractor, email_contractor, { group: dialog.getNotifyGroup(contractor_email), notification: NotificationType.Project_Proposal, template: TemplateType.Decline});
              }
            }

          });
        }
      }

      dialog.context.do.prompt = null;
    } else {
      // dialog.context.do.processing = false;
      // dialog.context.do.done = true;
      dialog.context.do.prompt = promptInfo('');
      dialog.context.do.hide = true;
      // dialog.clearPing();
    }

    if (dialog.isProcessing(false)) { // context.do.processing && !dialog.context.do.done) {
      dialog.context.do.prompt = promptInfo('');
      // dialog.quickPing();
    }
  } else if (dialog.isDoneProcessing()) { /*context.do.processing && !dialog.context.do.done) {
    dialog.context.do.prompt = promptInfo('');
    dialog.quickPing();
  } else {*/
    dialog.context.do.prompt = promptInfo('');
    dialog.context.do.sticky = false;
    dialog.context.do.clear = true;
    dialog.context.do.answers = [];
    // dialog.clearPing();
  }

  return true;
}

function runAction(dialog: Dialog) {
  const curr_dialog = dialog.currentDialog();

  if (!_completeDrafting(dialog)) return false;

  if (curr_dialog && curr_dialog.replies.length) {
    const item = curr_dialog.replies[0];

    const act = dialog.actionValues(ActionType.CMD);
    const bool = dialog.actionValues(ActionType.BOOL);
    logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `type: ${item.type} cmd: ${JSON.stringify(act)} bool: ${JSON.stringify(bool)}}`);

    switch (item.type) {
      case EntityType.Task: return _runTask(dialog);
      case EntityType.Message: return _runMessage(dialog);
      case EntityType.Event: return _runEvent(dialog);
      case EntityType.Project: return _runProject(dialog);
      case EntityType.Person: return _runPerson(dialog);
    }
  }

  if( dialog.context.do) {
    // dialog.context.do.done = true;
    dialog.context.do.return_to = Topics.DEFAULT;
    dialog.context.do.clear = true;
  }
}

function setPrompt(dialog: Dialog) {
  // handle error conditions
  if (!dialog.context.do) {
    dialog.setTopic(Topics.DEFAULT);
    return;
  }

  if (dialog.context.do.requires_auth && !dialog.isAuthenticated(dialog.context.do.requires_auth)) {
    // should not be reachable
    dialog.clearContext('do');
    dialog.addPrompt(lang.init.REG_REQUIRED(dialog.user.provider), true);
    dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
    dialog.setTopic(Topics.DEFAULT);
    return;
  }

  let prompt = promptInfo(lang.defaultanswers.ACKNOWLEDGE());
  if (dialog.context.do.drafting) {
    if (!dialog.context.do.draft_link) return;
    prompt = lang.doit.DRAFT_PROMPT(dialog.context.do.draft_link);
  }

  if (dialog.context.do.prompt !== null) prompt = dialog.context.do.prompt;

  if (dialog.context.do.clear) {
    if (dialog.currentDialog()) {
      const curr_dialog = dialog.currentDialog();
      dialog.newDialog({
        replies:curr_dialog.replies,
        next_topic:curr_dialog.next_topic,
      });
    } else dialog.clearDialog();
  }

  if (!dialog.isProcessing() || dialog.isDoneProcessing()) { // context.do.processing || dialog.context.do.done) {
    if (dialog.context.do.info) dialog.addInfo(dialog.context.do.info);
    if (prompt.label.length) dialog.addPrompt(prompt);
  }

  if (dialog.currentDialog().replies.length) {
    // dialog.setTopic(dialog.context.do.return_to);
    if (dialog.context.do.answers) {
      dialog.addAnswers(dialog.context.do.answers);
      if (prompt.label.length) dialog.addPrompt(prompt);
    } else if (dialog.context.do.return_to !== Topics.DEFAULT) {
      if (prompt.label.length) {
        dialog.addAnswers('Next');
        dialog.addPrompt(prompt);
      }
    }
  }

  if (dialog.context.do.sticky) {
    dialog.setSticky();
    // dialog.clearPing();
  }
  else dialog.clearSticky();

 //  if (dialog.context.do.done || !dialog.context.do.processing) {
  if (!dialog.isProcessing() || dialog.isDoneProcessing())  {
    if (dialog.context.do.hide) dialog.setHide();
    if (dialog.context.do.close) dialog.setClose();
    dialog.setTopic(dialog.context.do.return_to);
    dialog.clearContext('do');
    /*if (dialog.checkTopic(Topics.DEFAULT)) dialog.defaultPing();
    else dialog.rePing();*/
  }
}

export default {
  name: DO_NAME,
  description: DO_DESC,
  examples: lang.doit.DO_EG,
  reserved: lang.doit.DO_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: _a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(Topics.DO, module.exports.default, true);