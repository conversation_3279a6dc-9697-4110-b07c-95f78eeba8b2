import { AuthProvider } from '../auth/auth_provider';
import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Plugin, SettingsPluginState } from '../types/plugins';
import { AuthContext, AuthLevel, EntityType, PROJECT_ARCHIVED, PROJECT_OPEN } from '../types/shared';

import parsers from '../utils/parsers';

const topics = [TOPIC('SETTINGS'), TOPIC('SETTINGS_EMAIL'), TOPIC('SETTINGS_ORGANIZER'), TOPIC('SETTINGS_CALENDAR'), TOPIC('SETTINGS_PROJECTS')];

function keywordMatch(_actions, message) {
  return parsers.checkKeyword(message, lang.settings.SETTINGS_KWD, true);
}

function setAction(dialog: Dialog) {
  if (dialog.message.link) return false;
  const act_bool = dialog.actionValues(ActionType.BOOL);
  const act_entity = dialog.actionValues(ActionType.ENTITY_TYPE);

  if (isActive(dialog)) return true;

  if (act_bool && act_bool.length && act_entity && act_entity.length) {
    dialog.setTopic(Topics.SETTINGS);
    return true;
  }

  return false;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function runAction(dialog: Dialog) {
  const act_bool = dialog.actionValues(ActionType.BOOL);
  const act_entity = dialog.actionValues(ActionType.ENTITY_TYPE);
  const act_cal = dialog.actionValues(ActionType.CALENDAR);

  if (!dialog.context.settings) {
    dialog.context.settings = {
      enable: act_bool && act_bool.length ? act_bool[0] : false,
      type: act_entity && act_entity.length ? act_entity[0] : null,
      calendar: act_cal && act_cal.length ? act_cal[0] : null,
    } as SettingsPluginState;

    dialog.clearActions();
  }

  const context = dialog.context.settings;

  if (context.saving) {
    // if (!context.saved) dialog.quickPing();
    // else dialog.clearPing();
    return;
  }

  const lmsg = dialog.message.message.toLowerCase();

  switch (dialog.topic) {
    case Topics.SETTINGS:
      switch (context.type) {
        case EntityType.Settings:
          dialog.context.settings.saving = true;
          dialog.user.setSetting(lang.settings.INFO, { enabled: context.enable, profile: context.enable });
          dialog.context.settings.saved = true;
          break;
        case EntityType.User:
          dialog.context.settings.saving = true;
          dialog.user.setSetting(lang.settings.INFO, { enabled: context.enable });
          dialog.context.settings.saved = true;
          break;
        case EntityType.Person:
            dialog.context.settings.saving = true;
            dialog.user.setSetting(lang.settings.PEOPLE, { enabled: context.enable });
            dialog.context.settings.saved = true;
            break;
        case EntityType.Task:
            dialog.context.settings.saving = true;
            dialog.user.setSetting(lang.settings.TASKS, { enabled: context.enable });
            dialog.context.settings.saved = true;
            break;
        case EntityType.Note:
          dialog.context.settings.saving = true;
          dialog.user.setSetting(lang.settings.NOTES, { enabled: context.enable });
          dialog.context.settings.saved = true;
          break;
        case EntityType.Message:
          if (dialog.isAuthenticated(AuthLevel.Email)) {
            dialog.context.settings.saving = true;
            dialog.user.setSetting(lang.settings.MESSAGES, { enabled: context.enable });
            dialog.context.settings.saved = true;
            // dialog.quickPing();
          } else dialog.setTopic(Topics.SETTINGS_EMAIL);
          break;
        case EntityType.Event:
          if (!dialog.isAuthenticated(AuthLevel.Organizer)) dialog.setTopic(Topics.SETTINGS_ORGANIZER);
          else if (context.calendar) {
            const calendar = dialog.events.calendar(context.calendar.value);
            if (calendar) {
              Object.assign(context.calendar, calendar);
              if (dialog.events.calendars.synced(context.calendar.value)) {
                dialog.context.settings.saving = true;
                dialog.runAsync('settings', async () => {
                  await dialog.selectCalendar(context.calendar.value, context.enable);
                  dialog.context.settings.saved = true;
                });
              } else {
                dialog.context.settings.saving = true;
                dialog.runAsync('settings', async () => {
                  await dialog.events.calendars.sync(context.calendar.value, context.enable);
                  await dialog.selectCalendar(context.calendar.value, context.enable);
                  dialog.context.settings.saved = true;
                });
              }
            }
          }
          break;
        case EntityType.Project:
          // look for archived vs open
          if (lmsg.indexOf(PROJECT_ARCHIVED) > -1) {
            dialog.context.settings.saving = true;
            dialog.user.setSetting(lang.settings.PROJECTS, { archived: context.enable });
            dialog.context.settings.saved = true;
          } else if (lmsg.indexOf(PROJECT_OPEN) > -1) {
            dialog.context.settings.saving = true;
            dialog.user.setSetting(lang.settings.PROJECTS, { open: context.enable });
            dialog.context.settings.saved = true;
          } /*else if (lmsg.indexOf(PROJECT_AS_CLIENT) > -1) {
            dialog.context.settings.saving = true;
            dialog.user.setSetting(lang.settings.PROJECTS, { as_client: context.enable });
            dialog.context.settings.saved = true;
          }*/

          if (!dialog.context.settings.saving) // dialog.quickPing();
          //else 
          dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.SETTINGS_CALENDAR:
      if (dialog.message.ping) return;
      if (context.calendar) {
        const sync = act_bool && act_bool.length && act_bool[0];
        dialog.context.settings.saving = true;
        dialog.events.calendars
          .sync(context.calendar.value, sync)
          .then(() => {
            dialog
              .selectCalendar(context.calendar.value, sync)
              .then(() => (dialog.context.settings.saved = true))
              .catch(e => dialog.asyncError(e));
          })
          .catch(e => dialog.asyncError(e));
      }
      break;
  }

  // dialog.clearContext('settings');
}

function setPrompt(dialog: Dialog) {
  if (dialog.message.ping || (dialog.context.settings.saving && !dialog.context.settings.saved)) return;

  const defaults = AuthProvider.getDefaultProviderSettings(dialog.user.provider);
  let provider_settings = dialog.group_host ? dialog.group_host.providerSettings(dialog.user.provider) : defaults;
  if (!provider_settings) provider_settings = defaults
  const url = AuthProvider.clientAuthUrl(AuthContext.AuthChat, dialog.group_host, provider_settings);
  const settings = dialog.userSettings();
  const cals = dialog.getRepliesType(EntityType.Calendar);

  switch (dialog.topic) {
    case Topics.SETTINGS:
      if (dialog.isAuthenticatedNonGuest()) {
        // send settings
        dialog.lastPrompt('settings', lang.settings.SETTINGS_PROFILE, {hide: 10000, clear: true});
      } else {
        dialog.createGuest();
        // login prompt
        const providers = AuthProvider.clientAuthNameSet(null, dialog.group_host);
        const names = providers.map(p => p.name);
        dialog.addPrompt(lang.settings.SETTINGS_LOGIN(names), true);
        dialog.addAnswers(names);
        dialog.clearSticky();
        const auth_set = AuthProvider.clientAuthSet(AuthContext.Settings, dialog.group_host);
        for (const auth of Object.values(auth_set)) {
          dialog.addQuickReply(auth.provider, 'Just a sec...', { redirect: auth.url });
        }
        return;
      }
      break;
    case Topics.SETTINGS_EMAIL:
      if (settings.projects.active) dialog.addPrompt(lang.settings.SETTINGS_PROMPT_PROJECT_EMAIL(url), true);
      else dialog.addPrompt(lang.settings.SETTINGS_PROMPT_EMAIL(url), true);
      break;
    case Topics.SETTINGS_ORGANIZER:
      dialog.addPrompt(lang.settings.SETTINGS_PROMPT_ORGANIZER(AuthProvider.clientAuthUrl(AuthContext.AuthNoEmail, dialog.group_host, provider_settings)), true);
      break;
    case Topics.SETTINGS_CALENDAR:
      if (cals && cals.length) {
        const cal = cals[0];
        dialog.addPrompt(lang.settings.SETTINGS_PROMPT_CALENDAR(cal.name), true);
        dialog.addAnswers(lang.settings.SETTINGS_ANSWERS);
        return;
      } else dialog.addPrompt(lang.defaultanswers.ACKNOWLEDGE(), true);
  }

  dialog.reset('settings');
}

export default {
  name: 'Settings',
  description: 'Update user settings',
  examples: [],
  reserved: lang.settings.SETTINGS_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: () => null,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
