import _ from 'lodash';
import lang from '../lang';

import { ActionType } from '../types/globals';
import { Message } from '../types/items';
import { AskIntroPluginState, Plugin } from '../types/plugins';
import { AuthLevel, EntityType } from '../types/shared';

import { draftMessageLink, messageLink } from '../utils/format';
import { promptInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import personInfo from './person_info';

const INTRO_NAME = 'Ask Intro';
const INTRO_DESC = 'Ask a contact for an introduction via email';

const INTRO_RESERVED = ['ask [someone for an introduction to a person about a topic]'];

const LOG_NAME = 'plugins.ask_intro';

const topics = [
  TOPIC('ASK_INTRO'),
  TOPIC('ASK_PROMPT'),
]

function keywordMatch(actions, message, _raw_message) {
  return parsers.checkKeyword(message, lang.ask_intro.ASK, true) && parsers.checkKeyword(message, lang.ask_intro.INTRO);
}

function setAction(dialog: Dialog, message, raw_message) {
  if (dialog.message.link) return false;
  if (isActive(dialog)) return true;

  const names = dialog.actionValues(ActionType.ENTITY).map(e => e.name);
  if (names.length >= 1) {
    dialog.setTopic(Topics.ASK_INTRO);
    let about = parsers.extract(lang.ask_intro.CONTEXT, raw_message);
    if (about) {
      about = about.trim();
      if (about.length > 0) dialog.addAction(ActionType.RAW, about);
    }
    // make need to search for the second name
    // if(names.length == 1) {
    // ask XXX for intro to YYY about ZZZ
    let target = parsers.extract(lang.ask_intro.TARGET, message, lang.ask_intro.CONTEXT);

    // ask XXX to introduce me to YYY about ZZZ
    const embed = parsers.extract(lang.ask_intro.TARGET, target);
    if (embed.length) target = embed;

    dialog.addAction(ActionType.CMD, target);
    // }
    return true;
  } else {
    if (!isActive(dialog) && !dialog.isMaskedTopic()) {
      dialog.setTopic(Topics.ASK_PROMPT);
      return true;
    }
  }

  if (!dialog.isAuthenticated(AuthLevel.Organizer)) {
    dialog.setTopic(Topics.ASK_INTRO);
    return true;
  }

  return false;
}

function isActive(dialog) {
  return dialog.checkTopic(topics);
}

function runAction(dialog: Dialog) {
  if (!dialog.isAuthenticated(AuthLevel.Organizer)) return;

  const act_people = dialog.actionValues(ActionType.ENTITY).map(e => e.name);
  const note = dialog.actionValues(ActionType.RAW);
  const target = dialog.actionValues(ActionType.CMD);
  const curr_dialog = dialog.currentDialog();

  if (!dialog.context.ask_intro) {
    dialog.context.ask_intro = new AskIntroPluginState();

    dialog.context.ask_intro.names = act_people;
    dialog.context.ask_intro.target = target && target.length ?_.startCase(target[0]) : undefined;
    dialog.context.ask_intro.context = note && note.length ? note[0] : undefined;

    dialog.newDialog();
  }

  if (curr_dialog && curr_dialog.next_topic === Topics.FOUND_PERSON) {
    if (curr_dialog.replies.length) {
      dialog.context.ask_intro.id = curr_dialog.replies[0].id;
      dialog.clearDialog();
    } else {
      // person not found
      logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, 'no one found');
      dialog.addReplies(Dialog.emptyItems(2));
      // dialog.clearPing();
      dialog.clearContext('ask_intro');
      return true;
    }
  }

  if (dialog.context.ask_intro.id) {
    if (!dialog.context.ask_intro.loading) {
      dialog.context.ask_intro.loading = true;
      dialog.runAsync('ask_intro', async () => {
        const person = await dialog.people.byId(dialog.context.ask_intro.id);
        if (person.length) dialog.context.ask_intro.contact = person[0];
        dialog.context.ask_intro.id = null;
      });
    }

    if (!dialog.context.ask_intro.contact) {
      // dialog.quickPing();
      return false;
    }
  } else if (!dialog.context.ask_intro.contact && dialog.context.ask_intro.names.length && !dialog.context.ask_intro.drafting) {
    dialog.clearActions();

    logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `looking for person ${dialog.context.ask_intro.names}`);
    const find_person = dialog.context.ask_intro.names.shift();
    return personInfo.findPersonPrompt(dialog, find_person);
  }

  // get going on a draft message
  if (!dialog.context.ask_intro.draft) {
    if (dialog.context.ask_intro.contact && dialog.context.ask_intro.target && !dialog.context.ask_intro.drafting) {
      dialog.context.ask_intro.drafting = true;
      const message = lang.ask_intro.ASK_EMAIL(dialog.me.nickName, dialog.context.ask_intro.contact, dialog.context.ask_intro.target, dialog.context.ask_intro.context);
      const contact = dialog.context.ask_intro.contact;

      dialog.addReplies(dialog.getPersonInfo(contact));

      dialog.runAsync('ask_intro', async () => {
        const draft: Message = await dialog.messages.create(dialog.me, contact, 'Introduction', message);
        if (dialog.context.ask_intro.draft === null) dialog.context.ask_intro.draft = draft;
        dialog.context.ask_intro.drafting = false;
      }); 

      return false;
    }
  }

  if (dialog.context.ask_intro.drafting && !dialog.context.ask_intro.draft) {
    return false;
  }

  if (curr_dialog.replies.length === 1) {
    if (dialog.context.ask_intro.draft) {
      const message = dialog.context.ask_intro.draft;
      dialog.appendReplies(message);
      dialog.clearContext('ask_intro');
      return true;
    }
  }

  if (dialog.checkTopic(Topics.ASK_PROMPT)) return true;

  logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `got to the end with nothing: ${JSON.stringify(dialog.context.ask_intro)} ${JSON.stringify(curr_dialog)}`);
  dialog.addReplies(Dialog.emptyItems(2));
  dialog.clearContext('ask_intro');
  return true;
}

function setPrompt(dialog) {
  if (!dialog.isAuthenticated(AuthLevel.Organizer)) {
    dialog.addPrompt(lang.init.REG_REQUIRED(dialog.user.provider), true);
    dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
    dialog.setSticky();
    dialog.reset('ask_intro');
  } else {
    const curr_dialog = dialog.currentDialog();

    if (curr_dialog && curr_dialog.replies.length === 2 && curr_dialog.next_topic !== Topics.FOUND_PERSON) {
      const people = dialog.getRepliesType(EntityType.Person);
      const message = dialog.getRepliesType(EntityType.Message);

      if (people.length === 1 && message.length === 1) {
        let url = null;
        if (dialog.user.isAuthenticated(AuthLevel.Email)) url = draftMessageLink(dialog.user.provider, dialog.user.email, message[0]);
        else url = messageLink(message[0].raw);
        const prompt = promptInfo(lang.ask_intro.DRAFT, url, lang.ask_intro.EDIT_SEND, people[0]);
        dialog.addPrompt(prompt, true);
        dialog.setSticky();
      }
      else {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setPrompt', dialog.user.profile, `NOT FOUND at ${curr_dialog.next_topic} with replies ${JSON.stringify(curr_dialog.replies)}`);
        if (dialog.user.refreshing) dialog.addPrompt(lang.ask_intro.NOT_FOUND_LEARNING, true);
        else dialog.addPrompt(lang.ask_intro.NOT_FOUND, true);
      }

      dialog.reset('ask_intro');
    } else if(dialog.checkTopic(Topics.ASK_PROMPT)) {
      dialog.setTopic(Topics.ASK_INTRO);
      dialog.addPrompt(lang.ask_intro.PROMPT, true);
      dialog.addHint(lang.ask_intro.PROMPT_HINT);
    }
  }
}

export default {
  name: INTRO_NAME,
  description: INTRO_DESC,
  examples: [], // lang.ask_intro.INTRO_EG,
  reserved: INTRO_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
