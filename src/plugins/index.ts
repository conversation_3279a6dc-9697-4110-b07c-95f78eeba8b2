import { Plugin } from '../types/plugins';
import about from './about';
import add_reminder from './add_reminder';
import admin from './admin';
import ask_intro from './ask_intro';
import connect from './connect';
import contract from './contract';
import contractor from './contractor';
import create from './create';
import defaultplugin from './default';
import doit from './do';
import feedback from './feedback';
import find from './find';
import help from './help';
import imports from './imports';
import init from './init';
import introduce from './introduce';
import lookup_when from './lookup_when';
import mail from './mail';
import noop from './noop';
import payment from './payment';
import person_info from './person_info';
import ping from './ping';
import privacy from './privacy';
import project from './project';
import project_candidates from './project_candidates';
import project_create from './project_create';
import review from './review';
import settings from './settings';
import slack from './slack';
import stripe from './stripe';
import take_notes from './take_notes';
import tos from './tos';
import welcome from './welcome';
import whats_next from './whats_next';
import wise from './wise';

export let reserved: string[] = [];

export { about, add_reminder, admin, ask_intro, connect, contract, contractor, create, defaultplugin, doit, feedback, find, help, imports, init, introduce, lookup_when, mail, noop, payment, person_info, ping, privacy, project, project_candidates, project_create, review, settings, slack, stripe, take_notes, tos, welcome, whats_next, wise };

const plugins = Array.from(new Set(Object.values(module.exports))) as Plugin[];

for (const p of plugins) {
  const plugin = p as Plugin;
  // plugin.plugins = module.exports;
  if (plugin.reserved) reserved = reserved.concat(plugin.reserved);
}
