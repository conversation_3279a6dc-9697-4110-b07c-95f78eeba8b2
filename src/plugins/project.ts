import _ from 'lodash';
import util from 'util';
import { v4 as uuid } from 'uuid';

import { AuthProvider } from '../auth/auth_provider';
import config from '../config';
import data from '../data';
import lang from '../lang';
import { SERVICE_FEE, onEscrow } from '../routes/gopay';

import Dialog, { TOPIC, Topics } from '../session/dialog';
import ForaUser from '../session/user';
import stripe from '../sources/stripe_controller';
import wise from '../sources/wise_controller';
import { FORA_PROFILE } from '../types/user';

import { ActionType, InternalError, TemplateType, Topic, Transfer, Vanity } from '../types/globals';
import { Person, Project, Tag, Task, projectPerson } from '../types/items';
import { Plugin, ProjectPluginState } from '../types/plugins';
import { AuthContext, AuthLevel, AuthProviders, EntityType, NotificationType, PersonInfo, ProjectCandidateState, ProjectInfo, ProjectRate, TagType, Uid, UndefinedPersonInfo } from '../types/shared';

import { DAYS, MINUTES, SECONDS } from '../utils/datetime';
import { localeDowMonthDay } from '../utils/format';
import { checkState, saveOneTypeValue, slimEntity } from '../utils/funcs';
import { taskInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import personInfo from './person_info';

const DEBUG = (require('debug') as any)('fora:plugins:project');

const LOG_NAME = 'plugins.project';
const PROJECT_NAME = 'Project';
const PROJECT_DESC = 'Updates to a project';

const PROJECT_RESERVED = [
  'jobs',
];

const topics = [
  TOPIC('PROJECT_CLIENT'), // change client
  TOPIC('PROJECT_CMD'), // pull the latest project when user opens or closes it
  TOPIC('PROJECT_VIEW_TEMPLATE'), // view a template
  TOPIC('PROJECT_ACCEPT_PROPOSAL'), // contractor accepts proposal
  TOPIC('PROJECT_SAVING'), // pull the latest project when user opens or closes it
  TOPIC('PROJECT_PROGRESS'), // save progress
  TOPIC('PROJECT_COMPLETE'), // mark complete
  TOPIC('PAY_PROJECT'), // pay out workflow
  TOPIC('PROJECT_MISSING'), // error missing
  TOPIC('PROJECT_INVALID'), // error missing
  TOPIC('PROJECT_TIMEOUT'), // error missing
  TOPIC('PROJECT_EXPIRED'), // error expired
  TOPIC('PROJECT_NDA'), // contractor signs nda
  TOPIC('PROJECT_CANDIDATE'), // candidate side action
  TOPIC('PROJECT_CANDIDATE_UPDATE'), // ping while saving for candidate action
  TOPIC('PROJECT_RECOMMEND'), // candidate recommends someone
  // TOPIC('PROJECT_SHARED_SAVING'), // ping while saving shared project
  TOPIC('PROJECT_SUBMIT_FIRST'), // prompt to submit and complete first
  TOPIC('PROJECT_START'), // start a free job
  TOPIC('PROJECT_AUTH_LEARN'), // auth loop on creating project
  TOPIC('PROJECT_AUTH'), // auth loop on creating project
  TOPIC('PROJECT_AUTH_CANCELLED'), // user didn't want ot auth
];

export const UPDATE_PROJECT_DESC_FIELDS = [
  'confidential',
  'duration',
  'end',
  'network',
  'notes',
  'requirements',
  'deliverables',
  'background',
  'rate',
  'skills',
  'skill_set',
  'start',
  'flex_dates',
  'fee', 
  'title',
  'public',
  'groups',
  'sourcing_type',
  'sourcing_url',
];

export const PROJECT_DATE_FIELDS = [
  'start',
  'end',
];

const UPDATE_PROJECT_PROGRESS_FIELDS = [
  'progress',
]

export const PROJECT_SKILLS_FIELD = ['skill_set', 'skills'];

function keywordMatch(actions, message) {
  return (
    parsers.checkKeyword(message, lang.project.PROJECT_CLIENT, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_SAVE, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_PAY, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_REQUEST_PAY, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_ACCEPT, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_RECOMMEND, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_DECLINE, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_PROGRESS, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_COMPLETE, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_SUBMIT, true) ||
    parsers.checkKeyword(message, lang.project.PROJECT_START, true)
  );
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function setAction(dialog: Dialog, message, raw_message) {
  if (dialog.context.project && !isActive(dialog)) {
    // if (dialog.checkTopic([Topics.PROJECT_NDA, Topics.PROJECT_CANDIDATE_UPDATE])) return true;
    const project = dialog.context.project.project;

    if(project) {
      if (project.contractor){
        // don't load selected projects for non-contractors or clients
        if (project.contractor.askfora_id === dialog.user.profile) dialog.setTopic(Topics.PROJECT_CANDIDATE);
        else if (project.client.askfora_id != dialog.user.profile) dialog.setTopic(Topics.PROJECT_EXPIRED);
      } else if (project.client.askfora_id !== dialog.user.profile) {
        // Candidate mode
        dialog.setTopic(Topics.PROJECT_CANDIDATE);
      }
    } else {
      logging.warnFP(LOG_NAME, 'setAction', dialog.user.profile, `No project`);
      dialog.setTopic(Topics.PROJECT_MISSING);
    }
  }

  // pay out
  if (parsers.checkKeyword(message, lang.project.PROJECT_PAY, true) && (dialog.actionValues(ActionType.PROJECT).length || dialog.actionValues(ActionType.ENTITY).length)) {
    dialog.setTopic(Topics.PAY_PROJECT);
    return true;
  }

  // Project candidate flow
  if (!dialog.checkTopic(Topics.PROJECT_CONFIRM_SEND) &&
      dialog.actionValues(ActionType.PROJECT).length) {
    // Saving and sending drafts
    if (parsers.checkKeyword(message, lang.project.PROJECT_SAVE, true)) {
      if (parsers.checkKeyword(message, lang.project.PROJECT_CLOSE, true)) {
        dialog.addAction(ActionType.CMD, lang.project.CMD_PROJECT_CLOSE);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_OPEN, true)) {
        dialog.addAction(ActionType.CMD, lang.project.CMD_PROJECT_OPEN);
      } else dialog.addAction(ActionType.CMD, lang.project.CMD_PROJECT_SAVE);
      if (!dialog.checkTopic(Topics.PROJECT_STEPS)) dialog.setTopic(Topics.PROJECT_CMD);
    } else if(!dialog.checkTopic(Topics.PROJECT_STEPS)) {
      if (parsers.checkKeyword(message, lang.project.PROJECT_ACCEPT_PROPOSAL, true)) {
        dialog.setTopic(Topics.PROJECT_ACCEPT_PROPOSAL);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_ACCEPT, true)) {
        dialog.addAction(ActionType.CMD, ProjectCandidateState.ACCEPTED);
        dialog.setTopic(Topics.PROJECT_CANDIDATE);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_RECOMMEND, true)) {
        dialog.addAction(ActionType.CMD, ProjectCandidateState.RECOMMENDED);
        dialog.setTopic(Topics.PROJECT_CANDIDATE);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_DECLINE, true)) {
        dialog.addAction(ActionType.CMD, ProjectCandidateState.DECLINED);
        dialog.setTopic(Topics.PROJECT_CANDIDATE);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_REQUEST_PAY, true)) {
        dialog.addAction(ActionType.CMD, ProjectCandidateState.PAYMENT_REQUESTED);
        dialog.setTopic(Topics.PROJECT_CANDIDATE);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_SUBMIT, true)) {
        dialog.addAction(ActionType.CMD, ProjectCandidateState.SUBMITTED);
        dialog.setTopic(Topics.PROJECT_CANDIDATE);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_PROGRESS, true)) {
        dialog.setTopic(Topics.PROJECT_PROGRESS);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_COMPLETE, true)) {
        dialog.setTopic(Topics.PROJECT_COMPLETE);
      } else if (parsers.checkKeyword(message, lang.project.PROJECT_START, true)) {
        dialog.setTopic(Topics.PROJECT_START);
      }
    }
  }

  // refer a candidate
  if (dialog.checkTopic(Topics.PROJECT_RECOMMEND)) {
    // look for name and email in the message
    const rcpt = dialog.actionValues(ActionType.ENTITY) as { id: Uid; name: string; people: Partial<PersonInfo>[]}[];
    const email = parsers.findEmail(message);
    if (email && email.length) {
      if (rcpt && rcpt.length) {
        const comm = {type:'mail', link:`mailto:${email[0]}`};
        if (rcpt[0].people && rcpt[0].people.length) {
          if (!rcpt[0].people[0].comms) rcpt[0].people[0].comms = [comm];
          else rcpt[0].people[0].comms.push(comm);
        } else rcpt[0].people = [{name:rcpt[0].name, comms:[comm]}];
      } else {
        const person = new Person({ comms: email });
        const comms = [{type:'mail', link:`mailto:${email[0]}`}];
        dialog.addAction(ActionType.ENTITY, {
          name: person.displayName,
          people:[{name: person.displayName, comms}]
        });
      }
    }
  }

  // change client
  if (parsers.checkKeyword(message, lang.project.PROJECT_CLIENT)) {
    dialog.setTopic(Topics.PROJECT_CLIENT);
    return true;
  }

  const providers = AuthProvider.clientAuthSet(AuthContext.Project, dialog.group_host);
  if (dialog.checkTopic(Topics.PROJECT_AUTH)) {
    if (parsers.checkKeyword(message, providers.map(p => p.name.toLowerCase()), true)) return false;
    if (parsers.checkKeyword(message, lang.project.LEARN_MORE.toLowerCase(), true)) {
      dialog.setTopic(Topics.PROJECT_AUTH_LEARN);
    }
  }

  if (!isActive(dialog)) dialog.setTopic(Topics.PROJECT_CMD);

  return true;
}

function _projectClient(dialog: Dialog) {
  const wait_for_ensure = _ensureProject(dialog);

  const people: Person[] = dialog.actionValues(ActionType.PERSON);
  if (people && people.length) dialog.context.project.people = people.filter(p => p);

  if (!wait_for_ensure) return;

  if (!dialog.context.project.project) {
    dialog.reset('project');
    return;
  }

  if (dialog.isDoneProcessing()) {
    if (dialog.context.project && dialog.context.project.project) {
      const reply_project: ProjectInfo = peopleUtils.projectInfo(dialog.user.profile, dialog.me, 
          dialog.context.project.project, false, [dialog.group_host.id], dialog.user.locale);
      dialog.addReplies(reply_project);
    } else dialog.reset('project');
    return;
    //
  } else if (dialog.isProcessing()) {
    //
  } else if (dialog.context.project.people && dialog.context.project.people.length === 1 && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
    const new_client_in = dialog.context.project.people[0];
    dialog.context.project.people = undefined;
    if (new_client_in.vanity) {
      dialog.runAsync('project', async () => {
        const new_client_vanity = new_client_in.vanity === dialog.user.vanity ? new Vanity({id: dialog.me.id, profile: dialog.user.profile}) : await data.users.vanity(new_client_in.vanity);
        if (new_client_vanity && new_client_vanity.profile) {
          let new_client;
          let new_client_user;
          let old_client = dialog.context.project.project.client;

          if (new_client_vanity.profile === dialog.user.profile) {
            new_client = projectPerson(dialog.context.project.project, dialog.me);
            new_client.askfora_id = dialog.user.profile;
            new_client_user = dialog.user;
            dialog.context.project.project.client = new_client;
          } else {
            new_client_user = new ForaUser(new_client_vanity.profile);
            await data.users.init(new_client_user, false);
            if (new_client_user.groups && Object.keys(new_client_user.groups).includes(dialog.group_host.id)) {
              const new_client_people = await data.people.byAttributeComms(new_client_user, [new_client_user.email]);
              new_client = new_client_people && new_client_people.length ? new_client_people[0] : undefined;
              if (new_client && dialog.context.project.project) {
                new_client.self = false;
                new_client.askfora_id = new_client_vanity.profile;
                new_client.state = dialog.context.project.project.client.state;
                new_client.groups = dialog.context.project.project.client.groups;

                const my_client_set = await dialog.people.findByComms([new_client_user.email]);
                let my_client = my_client_set && my_client_set.length ? new Person(my_client_set[0]) : undefined;
                if (!my_client) {
                  my_client = new Person(new_client);
                  my_client.tempId();
                  await dialog.people.save(my_client, true);
                }

                dialog.context.project.project.client = projectPerson(dialog.context.project.project, my_client, new_client);
              }
            }
          }

          if (new_client && (!old_client || old_client.askfora_id !== new_client.askfora_id)) {
            dialog.context.project.project.admin = !new_client.self && (!dialog.context.project.project.candidates || 
                !dialog.context.project.project.candidates.filter(c => c.askfora_id).map(c => c.askfora_id).includes(dialog.user.profile));
            dialog.context.project.project.last_update = new Date();
            dialog.context.project.project.groups = [dialog.group_host.id];

            await dialog.projects.update(dialog.context.project.project, new_client.askfora_id === dialog.user.profile);
            if (new_client.askfora_id !== dialog.user.profile) {
              dialog.context.project.project.client = projectPerson(dialog.context.project.project, new_client);
              await dialog.projects.createForUser(new_client_user, new_client, dialog.context.project.project);
              await dialog.projects.updateShared(dialog.context.project.project);
            }

            const updated_project = await dialog.projects.get(dialog.context.project.project);

            dialog.context.project.project = updated_project;

            if (old_client && old_client.askfora_id && old_client.askfora_id !== dialog.user.profile && 
               updated_project.client.askfora_id  !== old_client.askfora_id &&
              (!updated_project.candidates || !updated_project.candidates.filter(c => c.askfora_id).map(c => c.askfora_id).includes(old_client.askfora_id))){
              const old_user = new ForaUser(old_client.askfora_id);
              await data.users.init(old_user);
              await data.projects.delete(old_user, updated_project);
            }

          } else logging.warnFP(LOG_NAME, 'projectClient', dialog.user.profile, `No self person found for client ${new_client_user.profile}`);
        }
      });
    }
    return;
  } 

}

function runAction(dialog: Dialog) {
  const bool = dialog.actionValues(ActionType.BOOL);

  switch (dialog.topic) {
    case Topics.PROJECT_TIMEOUT:
    case Topics.PROJECT_MISSING:
    case Topics.PROJECT_INVALID:
      return;
    case Topics.PROJECT_EXPIRED:
      if (dialog.context.project && dialog.context.project.project &&
        dialog.context.project.project.contractor &&
        dialog.context.project.project.contractor.askfora_id !== dialog.user.profile) {
          if (dialog.cache.projects[dialog.context.project.project.id]) _sendSafeProject(dialog, dialog.context.project.project);
      }
      return;

    case Topics.PROJECT_CMD:
      // Save project changes
      return _projectCmd(dialog);
    
    case Topics.PROJECT_ACCEPT_PROPOSAL:
      return _acceptProposal(dialog);

    case Topics.PROJECT_SAVING:
      return _saveSendProject(dialog);

    case Topics.PROJECT_VIEW_TEMPLATE:
      if (dialog.context.project && dialog.context.project.project) {
        dialog.context.project.saving = true;
        dialog.context.project.saved = true;
        _sendSafeProject(dialog, dialog.context.project.project);
        // dialog.clearPing();
      } else dialog.reset('project');
      return;

    case Topics.PROJECT_NDA:
      // Candidate mode
      if (bool && bool.length) {
        if (bool[0]) {
          dialog.setTopic(Topics.PROJECT_CANDIDATE);
          dialog.context.project.nda = true;
          dialog.context.project.project.last_update = new Date();
          dialog.context.project.project.last_activity = dialog.context.project.project.last_update;
          return _candidateAction(dialog);
        } else if(dialog.context.project.project.me_candidate.state !== ProjectCandidateState.ABANDONED) {
          dialog.context.project.project.me_candidate.state = ProjectCandidateState.ABANDONED;
          dialog.context.project.project.last_update = new Date();
          dialog.context.project.project.last_activity = dialog.context.project.project.last_update;

          dialog.runAsync('project', async () => {
            if (dialog.context.project) {
              // update shared copy
              const save = dialog.projects.updateClient(dialog.context.project.project);
              // remove this user's copy
              const del = dialog.projects.delete(dialog.context.project.project);

              await Promise.all([save, del]);
            }
          });

          // reply
          dialog.setTopic(Topics.PROJECT_CANDIDATE);
          _sendSafeProject(dialog, dialog.context.project.project);
          return; // bail out
        }
      } else return; // bail out


    case Topics.PROJECT_RECOMMEND:
      return _recommendAction(dialog);

    case Topics.PROJECT_CANDIDATE:
      // Updates from candidates
      return _candidateAction(dialog);

    case Topics.PROJECT_CANDIDATE_UPDATE:
      return _updateCandidateProject(dialog);

    // case Topics.PROJECT_SHARED_SAVING:
    //  return _updateSharedProject(dialog);

    case Topics.PAY_PROJECT:
      // Payout processing
      return _processPayment(dialog);

    case Topics.PROJECT_PROGRESS:
      return _projectProgress(dialog);
    case Topics.PROJECT_COMPLETE:
      return _projectComplete(dialog);
    case Topics.PROJECT_START:
      return _projectStart(dialog);
    case Topics.PROJECT_AUTH:
    case Topics.PROJECT_AUTH_CANCELLED:
      dialog.createGuest();
      return;
    case Topics.PROJECT_CLIENT:
      return _projectClient(dialog);
  }
}

function setPrompt(dialog: Dialog) {
  const project_infos: ProjectInfo[] = dialog.getRepliesType(EntityType.Project) as ProjectInfo[];
  let project_info: ProjectInfo = null;
  if (project_infos.length) project_info = project_infos[0];

  const people = dialog.getRepliesType(EntityType.Person);

  switch (dialog.topic) {
    case Topics.PROJECT_PROGRESS:
    case Topics.PROJECT_COMPLETE:
      dialog.reRun();
      break;

    case Topics.PROJECT_CMD:
      if (project_info) {
        let save_prompt = null;
        const answers = null;
        if (!project_info.added) {
          if ( dialog.context.project.added) project_info.added = true;
          else if (dialog.context.project.close) project_info.closed = true;
          else if (dialog.context.project.open) project_info.edit = true;
          else if (dialog.context.project.updated) project_info.updated = true;
          else dialog.setHide();

          if (project_info.client.self) {
            if (project_info.completed && project_info.contractor && dialog.context.project.updated) {
              save_prompt = lang.project.PROCEED_TO_PAY(project_info);
            }
          }
        }

        dialog.addInfo(project_info, true);
        dialog.setOpen(project_info);
        if (answers) dialog.addAnswers(answers);
        dialog.clearSticky();

        if (save_prompt) {
          dialog.addPrompt(save_prompt);
          dialog.setSticky();
        }

        if (!dialog.isGuestAccount() && !dialog.context.project.logging_in) dialog.reset('project');
        else {
          // dialog.clearPing();
          dialog.setTopic(Topics.DEFAULT);
        }
      }
      break;

    case Topics.PROJECT_ACCEPT_PROPOSAL:
      if (project_info) {
        dialog.addInfo(project_info, true);
        dialog.setOpen(project_info);
        dialog.lastPrompt('project', lang.project.ACCEPT_PROPOSAL_PROMPT(project_info));
      }
      break;
    case Topics.PAY_PROJECT:
      if (project_info) {
        // payment is stripe transfer object: https://stripe.com/docs/api#transfer_object
        if (!project_info.payment) {
          if (dialog.context.project && dialog.context.project.payment_error) {
            dialog.addPrompt(lang.project.ERROR_PAYMENT, true);
            dialog.clearContext('project');
            dialog.setTopic(Topics.DEFAULT);
          } else if (project_info.contractor && project_info.contractor.id) {
            const amount = project_info.rate === ProjectRate.fixed ? project_info.fee : project_info.progress * project_info.fee;
            const escrow = project_info.rate === ProjectRate.fixed ? project_info.fee : project_info.duration * project_info.fee;
            dialog.addPrompt(lang.project.PAY_PROMPT(project_info.contractor.name, amount, escrow), true);
            dialog.addAnswers(lang.project.PAY_ANSWERS);
            // dialog.clearPing();
            dialog.setSticky();
          }
        } else if (project_info.payment) {
          dialog.addPrompt(lang.project.CONFIRM_PAYMENT(project_info), true);
          project_info.updated = true;
          dialog.addInfo(project_info);
          dialog.setOpen(project_info);
          dialog.clearSticky();
          dialog.reset('project');
        }
      }
      break;

    case Topics.PROJECT_AUTH_LEARN:
    case Topics.PROJECT_AUTH:
      if (!dialog.message.ping && dialog.context.project) {
        const project = dialog.context.project.project;
        if (project) {
          const auth_context = project.proposal && (!project.contractor || project.contractor.askfora_id !== dialog.user.profile) ?  AuthContext.ProjectCreate : AuthContext.Project;
          const providers = AuthProvider.clientAuthSet(auth_context, dialog.group_host);
          if (dialog.checkTopic(Topics.PROJECT_AUTH_LEARN)) {
            dialog.setTopic(Topics.PROJECT_AUTH);
            dialog.addPrompt(lang.project.LEARN_MORE_PROMPT, true);
            dialog.addAnswers(providers.map(p => p.name).concat([lang.project.CREATE_PROMPT_NEVERMIND]));
          } else {
            dialog.addPrompt(dialog.context.project.project.expert ? 
              lang.project.EXPERT_WELCOME: lang.project.AUTH_WELCOME, true);
            dialog.addAnswers(providers.map(p => p.name).concat([lang.project.LEARN_MORE, lang.project.CREATE_PROMPT_NEVERMIND]));
          }
          dialog.addPrompt(dialog.context.project.project.expert ?
            lang.project.EXPERT_AUTH(providers.map(p => p.name)) :
            lang.project.AUTH(providers.map(p => p.name)));
          for (const provider of providers) {
            const qrs = AuthProvider.contextPermissions(auth_context);
            qrs.push(lang.init.REGISTER_REPLY_LINK(provider));
            dialog.addQuickReply(provider.name, [] /* qrs */, provider.url ? {redirect: provider.url} : { command: provider.command });
          }
          // dialog.clearPing();
          dialog.setSticky();
        } else dialog.reset('project');
        break;
      }
      // fall through
    case Topics.PROJECT_MISSING:
      dialog.lastPrompt('project', lang.project.MISSING, {clear:true});
      break;
 
    case Topics.PROJECT_AUTH_CANCELLED:
      if (!dialog.message.ping) {
        const project = dialog.context.project ? dialog.context.project.project : null;
        const auth_context = project && project.proposal && (!project.contractor || project.contractor.askfora_id !== dialog.user.profile) ? AuthContext.ProjectCreate : AuthContext.Project; 
        dialog.addPrompt(lang.project.AUTH_CANCELLED(AuthProvider.clientAuthInfo(auth_context, dialog.context.init.provider, dialog.group_host)), true);
      }
      break;

    case Topics.PROJECT_NDA:
      // if (dialog.message.ping) dialog.clearPing();
      // else {
      if (!dialog.isProcessing()) {
        if (project_info) {
          dialog.addPrompt(lang.project.NDA(project_info.client.name, project_info.me_candidate.name), true);
          dialog.setClose();
          // dialog.clearPing();
          dialog.setSticky();
        } else dialog.addPrompt(lang.project.REPEAT_NDA, true);
        dialog.addAnswers(lang.project.NDA_ANSWERS);
      }
      break;

    case Topics.PROJECT_VIEW_TEMPLATE:
      if (project_info) {
        project_info.edit = true;
        dialog.addInfo(project_info, true);
        dialog.setOpen(project_info);
      }
      dialog.reset('project');
      break;
   case Topics.PROJECT_TIMEOUT:
      dialog.lastPrompt('project', lang.project.TIMEOUT, {clear:true});
      break;
    case Topics.PROJECT_INVALID:
      dialog.lastPrompt('project', lang.project.INVALID, {clear:true});
      break;
    case Topics.PROJECT_EXPIRED:
      if (project_info) {
        project_info.archived = true;
        project_info.deleted = true;
        dialog.addInfo(project_info, true);
      }
      dialog.lastPrompt('project', [lang.project.MISSING, lang.project.PROJECT_EXPIRED], {clear:project_info === null});
      break;
    case Topics.PROJECT_RECOMMEND:
      if (people && people.length) dialog.lastPrompt('project', lang.project.RECOMMEND_DONE(people[0].displayName.split(' ')[0]), {clear:true});
      // else dialog.quickPing();
      break;
    case Topics.PROJECT_SUBMIT_FIRST:
      dialog.newDialog();
      dialog.lastPrompt('project', lang.project.SUBMIT_FIRST(project_info ? project_info.client.nick : null), { clear: true });
      break;
    case Topics.PROJECT_CANDIDATE:
      if (project_info && project_info.me_candidate && !dialog.message.ping) {
        switch (project_info.me_candidate.state) {
          case ProjectCandidateState.ACCEPTED:
            if (!project_info.added) project_info.updated = true;
            dialog.addInfo(project_info, true);
            dialog.setOpen(project_info);

            if (dialog.context.project.updated) {
              const name = project_info.me_candidate.name.split(' ')[0];
              const client = project_info.client.name.split(' ')[0];
              if (dialog.context.project.project.expert) dialog.addPrompt(lang.project.ANSWER_PROMPT(name, client));
              else if(dialog.context.project.project.rate === ProjectRate.sourcing) dialog.addPrompt(lang.project.APPLY_PROMPT(name, client, dialog.context.project.project.sourcing_url));
              else dialog.addPrompt(lang.project.ACCEPTED_PROMPT(name, client, project_info.me_candidate.ready, !dialog.isAuthenticated(AuthLevel.Organizer), project_info.sourcing_url));

              if (project_info.rate === ProjectRate.sourcing || dialog.context.project.project.expert) {
                //
              } else if (!project_info.me_candidate.ready) {
                dialog.addHint(lang.project.CONNECT_PAYMENT);
                dialog.addAnswers(lang.project.CONNECT_PAYMENT);
              } else if (!dialog.isAuthenticated(AuthLevel.Organizer)) {
                dialog.addHint(lang.project.ACCEPTED_HINT);
                dialog.addSuggestion(lang.project.ACCEPTED_HINT);
                dialog.setHide(10000);
              }
            }

            dialog.setClear();
            dialog.reset('project');
            break;
          case ProjectCandidateState.RECOMMENDED:
            dialog.addPrompt(lang.project.RECOMMENDED_PROMPT(project_info.me_candidate.name.split(' ')[0], project_info.client.name.split(' ')[0], !dialog.isAuthenticated(AuthLevel.Organizer)), true);
            // dialog.clearPing();
            dialog.context.project.saving = false;
            dialog.setTopic(Topics.PROJECT_RECOMMEND);
            break;
          case ProjectCandidateState.ABANDONED:
            // don't send a project if NDA isn't accepted
            dialog.lastPrompt('project', lang.project.DECLINED_NDA, {clear:true});
            dialog.setClose();
            break;
          case ProjectCandidateState.DECLINED:
            dialog.addInfo(project_info, true);
            dialog.setOpen(project_info);
            if (project_info.expert) dialog.addPrompt(lang.project.DECLINED_EXPERT_PROMPT(project_info.me_candidate.name.split(' ')[0], project_info.client.name.split(' ')[0]));
            else dialog.addPrompt(lang.project.DECLINED_PROMPT(project_info.me_candidate.name.split(' ')[0], project_info.client.name.split(' ')[0]));
            dialog.addAnswers(lang.project.DECLINED_ANSWERS);
            // dialog.clearPing();
            dialog.clearContext('project');
            dialog.setTopic(Topics.FEEDBACK);
            break;
          case ProjectCandidateState.SELECTED:
            {
              project_info.updated = true;
              project_info.edit = true;
              const prompt = [];
              let hint;
              let answer;
              dialog.addInfo(project_info);
              dialog.setOpen(project_info);
              if (project_info.contract) {
                if (project_info.payment) {
                  prompt.push(lang.project.SELECTED_CONTRACTOR_PAYMENT);
                } else if (project_info.completed) {
                  prompt.push(lang.project.SELECTED_CONTRACTOR_COMPLETE(project_info.client.nick));
                } else if(project_info.escrow) {
                  prompt.push(lang.project.SELECTED_CONTRACTOR_PROGRESS);
                } else {
                  prompt.push(lang.project.SELECTED_CONTRACTOR(project_info));
                  const contract = dialog.cache.contracts[project_info.contract];
                  if (contract) {
                    if (contract.contractor_signed) {
                      prompt.push(lang.project.SELECTED_CONTRACTOR_SETUP_STRIPE(project_info.client.nick, project_info.contractor.ready));
                      if (!project_info.contractor.ready) {
                        hint = lang.project.CONNECT_PAYMENT;
                        answer = lang.project.CONNECT_PAYMENT;
                      }
                    } else { 
                      const contract_url = contract && !contract.contractor_signed ? dialog.contracts.getSignUrl(contract.id, dialog.user.email) : null;
                      prompt.push(lang.project.SELECTED_CONTRACTOR_SETUP_CONTRACT(project_info, contract_url));
                    }
                  }
                }
              }
              if (prompt.length) dialog.lastPrompt('project', prompt, { sticky: !project_info.contractor.ready });
              if (hint) dialog.addHint(hint);
              if (answer) dialog.addAnswers(answer);
            }
            break;
          case ProjectCandidateState.SUBMITTED:
            dialog.newDialog();
            project_info.updated = true;
            dialog.addInfo(project_info);
            dialog.setOpen(project_info);
            dialog.lastPrompt('project', lang.project.CONFIRM_SUBMITTED(project_info));
            break;
          case ProjectCandidateState.PAYMENT_REQUESTED:
            project_info.updated = true;
            dialog.addInfo(project_info);
            dialog.setOpen(project_info);
            dialog.lastPrompt('project', lang.project.CONFIRM_REQUEST_PAY(project_info));
            break;
          default:
            if (dialog.context.project.nda) {
              project_info.added = true;
              dialog.addInfo(project_info, true);
              dialog.setOpen(project_info);
              if (project_info.expert) dialog.lastPrompt('project', lang.project.EXPERT_VIEW(project_info));
              else dialog.lastPrompt('project', lang.project.INTERESTED(project_info));
              dialog.setClear();
              dialog.clearSticky();
            } else {
              project_info.edit = dialog.context.project.load;
              dialog.addInfo(project_info, true);
              dialog.setOpen(project_info);
              dialog.reset('project');
            }
            break;
        }
      }
      break;
    case Topics.PROJECT_START:
      if (dialog.context.project.updated) {
        if (dialog.context.project.escrow_prompt) {
          dialog.addInfo(dialog.context.project.escrow_prompt.info);
          dialog.setOpen(project_info);
          dialog.lastPrompt('project', dialog.context.project.escrow_prompt.reply);
        }
        else dialog.reset('project');
      } else if(dialog.context.project.loaded && !dialog.context.project.sending) dialog.reset('project');
    case Topics.PROJECT_CLIENT:
      if (project_infos && project_infos.length) {
        project_infos[0].updated = true;
        dialog.addInfo(project_infos, true);
        dialog.setOpen(project_info);
        dialog.reset('project');
      }
      if (dialog.isDoneProcessing() || !dialog.isProcessing(false)) dialog.reset('project');
  }
}

// handle what the candidate does
function _candidateAction(dialog: Dialog) {
  const wait_for_ensure = _ensureProject(dialog, ['progress']);

  const act = dialog.actionValues(ActionType.CMD);
  if (act.length) {
    dialog.context.project.state = act[0];
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `saving state ${dialog.context.project.state}`);
  }

  const projects = dialog.actionValues(ActionType.PROJECT);
  if (projects && projects.length && projects[0].type === EntityType.Project && projects[0].me_candidate && projects[0].me_candidate.answer) {
    dialog.context.project.answer = projects[0].me_candidate.answer;
  }

  if (!wait_for_ensure) return;

  if (!dialog.isAuthenticated()) {
    dialog.createGuest();
    dialog.setTopic(Topics.PROJECT_AUTH);
    return false;
  }

  const project = dialog.context.project.project;

  if (!project || (dialog.context.project.loading && !dialog.context.project.loaded)) {
    if (!dialog.context.project.loading) {
      logging.warnFP(LOG_NAME, '_candidateAction', dialog.user.profile, `Lost project while loading`);
      dialog.setTopic(Topics.PROJECT_MISSING);
    }
    // else dialog.quickPing();
    return;
  }

  // if this was forwarded or logged in with a different email, add self to candidates
  if (!project.me_candidate) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `creating me_candidate for ${project.id}`);
    let comms = [dialog.user.email];
    if (dialog.me) {
      const email = parsers.findEmail(dialog.me.comms);
      if (email) comms = Array.from(new Set([...comms, ...email]));
    }

    project.me_candidate = project.candidates.find(c => c.askfora_id === dialog.user.profile);
    if (!project.me_candidate) project.me_candidate = project.candidates.find(c => !c.askfora_id && _.intersection(c.comms, comms).length > 0);

    if (project.me_candidate) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `matched me while creating for ${project.id}: ${util.format(project.me_candidate)}`);
      project.me_candidate = projectPerson(project, dialog.me, project.me_candidate);
    } else {
      project.me_candidate = projectPerson(project, dialog.me, {
        network: true,
        state: ProjectCandidateState.FOUND,
      });
      const add_candidate = projectPerson(project, project.me_candidate);
      add_candidate.comms = [dialog.user.email]; //add_candidate.comms.filter(c => c !== add_candidate.id);
      add_candidate.id = null;
      project.candidates.push(add_candidate);
      project.last_update = new Date();
      project.last_activity = project.last_update;
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `did not match me while creating for ${project.id}: ${util.format(project.me_candidate)}`);
    }

    project.me_candidate.ready = dialog.user.hasAccount(AuthProviders.Stripe) || dialog.user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment);
    project.me_candidate.comms = [dialog.user.email];

    slimEntity(project.me_candidate);

    // check for a contract and whether contracting as group
    if (dialog.user.loaded_groups) {
      for (const index in dialog.cache.contracts) {
        const contract = dialog.cache.contracts[index];
        if (project.client.comms.includes(contract.client_email)) {
          for (const group of Object.values(dialog.user.loaded_groups)) {
            if (group.name.toLowerCase() === contract.contractor_name.toLowerCase() ||
                group.company_name.toLowerCase() === contract.contractor_name.toLowerCase()) {
                  project.me_candidate.as_group = group.id;
                  break;
            }
          }
          break;
        }
      }
    }
  }

  const proj_candidate = _findCandidate(dialog, project);
  proj_candidate.askfora_id = project.me_candidate.askfora_id;
  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `found candidate ${proj_candidate.askfora_id} ${proj_candidate.state} for ${project.id}`);

  // mark as opened
  if (checkState(project.me_candidate, ProjectCandidateState.OPENED) < 0) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `setting open state for ${project.id}`);
    project.me_candidate.state = ProjectCandidateState.OPENED;
    proj_candidate.state = ProjectCandidateState.OPENED;
    project.last_activity = project.last_update;
    dialog.context.project.state = null;
    dialog.context.project.project.last_update = new Date();
    dialog.context.project.project.last_activity = dialog.context.project.project.last_update;
    dialog.context.project.saving = false;
    dialog.context.project.saved = false;
    return _updateCandidateProject(dialog, false);
  }

  // confirm NDA before marking viewed and showing
  if (checkState(project.me_candidate, ProjectCandidateState.VIEWED) < 0) {
    if (dialog.context.project.nda || project.expert) {
      dialog.context.project.nda = true;
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `setting viewed state for ${project.id}`);
      project.me_candidate.id = dialog.me.id;
      project.me_candidate.state = ProjectCandidateState.VIEWED;
      project.me_candidate.askfora_id = dialog.user.profile;
      project.me_candidate.ready = dialog.user.hasAccount(AuthProviders.Stripe) || dialog.user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment);
      if (project.expert && dialog.context.project.answer) project.me_candidate.answer = dialog.context.project.answer;
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `has stripe ${project.me_candidate.ready} for ${project.id}`);

      proj_candidate.state = project.me_candidate.state;
      proj_candidate.id = project.me_candidate.id;
      proj_candidate.askfora_id = project.me_candidate.askfora_id;
      proj_candidate.ready = project.me_candidate.ready;
      proj_candidate.answer = null;
    
      project.last_activity = project.last_update;

      dialog.context.project.state = null;
      dialog.context.project.answer = null;
      dialog.context.project.project.last_update = new Date();
      dialog.context.project.project.last_activity = dialog.context.project.project.last_update;
      dialog.context.project.saving = false;
      dialog.context.project.saved = false;
      return _updateCandidateProject(dialog, false);
    } else {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `getting nda ${project.id}`);
      // dialog.clearPing();
      dialog.setTopic(Topics.PROJECT_NDA);
      _sendSafeProject(dialog, project);
      return;
    }
  }

  // update state (e.g. accepted)
  if (project.me_candidate && checkState(project.me_candidate, ProjectCandidateState.VIEWED) >= 0) {
    let save = false;
    let do_notify = false;
    if (dialog.context.project.state) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `setting state ${dialog.context.project.state} from ${project.me_candidate.state} for ${project.id}`);
      let can_update = true;

      if (project.me_candidate.state === ProjectCandidateState.SELECTED) {
        can_update = lang.project.STATE_FROM_SELECTED.includes(dialog.context.project.state);
      }

      if (dialog.context.project.state === ProjectCandidateState.PAYMENT_REQUESTED) {
        can_update = project.completed;
        if (!can_update) dialog.setTopic(Topics.PROJECT_SUBMIT_FIRST);
      }

      if (can_update) {
        do_notify = project.me_candidate.state !== dialog.context.project.state || dialog.context.project.updated;
        dialog.context.project.updated = do_notify;
        if (!do_notify) logging.warnFP(LOG_NAME, '_candidateAction', dialog.user.profile, `Not notifying me_candidate state ${project.me_candidate.state} context state ${dialog.context.project.state} updated ${dialog.context.project.updated}`);
      
        if (dialog.context.project.state === ProjectCandidateState.DECLINED) {
          project.archived = true;
          save = true;
        }

        if (project.me_candidate.state !== dialog.context.project.state) {
          project.me_candidate.state = dialog.context.project.state;
          save = true;
        }

        if (project.me_candidate.answer !== dialog.context.project.answer || proj_candidate.answer !== dialog.context.project.answer) {
          project.me_candidate.answer = dialog.context.project.answer;
          proj_candidate.answer = project.me_candidate.answer;
          save = true;
        }

        const ready = dialog.user.hasAccount(AuthProviders.Stripe) || dialog.user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment);
        if (project.me_candidate.ready !== ready || proj_candidate.ready !== ready) {
          project.me_candidate.ready = ready;
          proj_candidate.ready = project.me_candidate.ready;
          save = true;
        }

        if (proj_candidate.state !== dialog.context.project.state) {
          proj_candidate.state = dialog.context.project.state;
          save = true;
        }

        if (proj_candidate.askfora_id !== project.me_candidate.askfora_id) {
          proj_candidate.askfora_id = project.me_candidate.askfora_id;
          save = true;
        }

        if (dialog.context.project.project.contractor && dialog.context.project.project.contractor.askfora_id === project.me_candidate.askfora_id) {
          dialog.context.project.project.contractor.state = dialog.context.project.state;
        }
        if (save) {
          dialog.context.project.project.last_update = new Date();
          dialog.context.project.project.last_activity = dialog.context.project.project.last_update;
        }

        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `me_candidate ${project.me_candidate.state} proj_candidate ${proj_candidate.state} contractor ${dialog.context.project.project.contractor ? dialog.context.project.project.contractor.state : 'no contractor'}`);
      } else if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_candidateAction', dialog.user.profile, `cannot update`);

      dialog.context.project.state = null;
      dialog.context.project.answer = null;
    }

    const replies = dialog.getRepliesType(EntityType.Project);
    if (!replies || !replies.length) {
      dialog.context.project.saving = false;
      dialog.context.project.saved = false;
      return _updateCandidateProject(dialog, do_notify && lang.project.NOTIFY_STATES.includes(proj_candidate.state), save);
    }
  }
}

function _checkNotifyData(dialog: Dialog) {
  const projects: ProjectInfo[] = dialog.actionValues(ActionType.PROJECT);
  if (projects && projects.length) {
    const project_info = projects[0];
    const project = dialog.cache.projects[project_info.id];
    if (project) { 
      dialog.context.project.updated = project.escrow && project.escrow.charges && 
        ((project_info.completed && project.completed !== project_info.completed) ||
        (project_info.progress && project.progress !== project_info.progress));
    } else logging.warnFP(LOG_NAME, 'checkNotifyData', dialog.user.profile, `Project ${project_info.id} not in cache`);
  }
}

// look for names and emails to forward the project to
function _recommendAction(dialog: Dialog) {
  _ensureProject(dialog);
  // const project = dialog.context.project.project;
  let person = null;

  const people = dialog.getRepliesType(EntityType.Person);
  if (people && people.length) person = people[0];
  else if (!dialog.context.project.saving) {
    const rcpt: Partial<UndefinedPersonInfo>[] = dialog.actionValues(ActionType.ENTITY);
    if (rcpt && rcpt.length) {
      if (rcpt[0].people && rcpt[0].people.length) return personInfo.findPersonPrompt(dialog);
      else person = new Person({ displayName: rcpt[0].name, comms: [rcpt[0].email] });
    }
  }

  if (person && !dialog.context.project.saving) {
    if (!person.id) {
      dialog.setTopic(Topics.PROJECT_CANDIDATE_NO_CANDIDATE);
      return;
    }
    dialog.context.project.saving = true;
    dialog.context.project.saved = false;
    dialog.newDialog();
    dialog.runAsync('project', async () => {
      const project = dialog.context.project.project;
      project.last_activity = project.last_update;
      const load_people = await dialog.people.byId(person.id);
      dialog.context.project.people = load_people;
      person = load_people[0];

      const start = localeDowMonthDay(project.start, dialog.user.locale, dialog.user.timeZone);
      const [to_email] = parsers.findEmail(person.comms);
      const email = {
        rcpts: [{ Name: person.displayName, Email: to_email}],
        ccs: [ { Name: dialog.me.displayName, Email: dialog.user.email} ],
        subject: lang.project.REFER_SUBJECT(project, dialog.me),
        message: lang.project.REFER_MESSAGE(project, dialog.me, person, start, dialog.projects.getUrl(project, to_email)),
      };

      const referral = await dialog.projects.projectReferrals(project);
      if (!referral || !referral.length || !referral.find(r => r.candidate && r.candidate.id === person.id)) {
        await dialog.projects.referCandidate(project, person);
      }

      let template;
      if (project.expert) template = TemplateType.Expert;
      else if(project.rate === ProjectRate.sourcing) template = TemplateType.Sourcing;
      else if(project.escrow)  template = TemplateType.NoFee ;
      else if(project.rate === ProjectRate.fixed) template = TemplateType.Fixed;
      else template = null;

      await dialog.projects.notify(project, dialog.user, dialog.context.project.people[0], 
        email, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Recommended, template, referrer: dialog.me });

      dialog.context.project.saved = true;
    });
  } else if(dialog.context.project.saved) {
    dialog.addReplies(dialog.context.project.people);
    return;
  }
}

function _acceptProposal(dialog: Dialog) {
  const wait_for_ensure = _ensureProject(dialog, UPDATE_PROJECT_DESC_FIELDS);
  if (!wait_for_ensure) return;

  const project: Project = dialog.context.project.project;

  if (!project) {
    logging.warnFP(LOG_NAME, 'acceptProposal', dialog.user.profile, `Ensure returned with no project`);
    dialog.setTopic(Topics.PROJECT_TIMEOUT);
    return;
  }

  // show the project
  if (dialog.context.project.saving) {
    if (dialog.context.project.saved) {
      dialog.setTopic(Topics.PROJECT_CONTRACTOR_SELECTED);
      _sendSafeProject(dialog, project);
      dialog.context.project.saving = false;
      dialog.context.project.saved = false;
      return;
    } else {
      return false;
    }
  } else if (!dialog.context.project.saving) {
    dialog.context.project.saved = false;
    dialog.context.project.saving = true;
    dialog.context.project.project.accepted = true;
    dialog.context.project.project.last_update = new Date();
    dialog.context.project.project.last_activity = dialog.context.project.project.last_update;
    dialog.runAsync('project', async () => {
      if (dialog.context.project) {
        let contract = await dialog.projects.setupContract(dialog.context.project.project);
        // handle skip contract
        if (contract) {
          contract = await dialog.contracts.create(contract);
          if (dialog.context.project && dialog.context.project.project) dialog.context.project.project.contract = contract.id;

          if (!contract.client_reviewed || !contract.contractor_reviewed ||
            !contract.client_signed || !contract.contractor_signed) dialog.context.project.new_contract = contract;

          if (dialog.context.project.new_contract) await dialog.projects.notifySelected(dialog.context.project.project, dialog.context.project.new_contract);
        }
      
        if (dialog.context.project) {
          const p = await dialog.projects.update(project, !project.archived);
          dialog.context.project.project = p;
        }

        dialog.context.project.saved = true;
      }
    });
  }
}

// load latest copy of the project and send to user
function _projectCmd(dialog: Dialog) {
  const cmds = dialog.actionValues(ActionType.CMD);
  const save = cmds.includes(lang.project.CMD_PROJECT_SAVE);
  const close = cmds.includes(lang.project.CMD_PROJECT_CLOSE);
  const open = cmds.includes(lang.project.CMD_PROJECT_OPEN);

  let update_fields = [];

  if (save || close) {
    const projects: ProjectInfo[] = dialog.actionValues(ActionType.PROJECT);
    if (projects && projects.length && dialog.cache.projects[projects[0].id] &&
      dialog.cache.projects[projects[0].id].escrow && 
      dialog.cache.projects[projects[0].id].escrow.charges)
      update_fields = UPDATE_PROJECT_PROGRESS_FIELDS;
    else update_fields = UPDATE_PROJECT_DESC_FIELDS;
  }

  const wait_for_ensure = _ensureProject(dialog, update_fields);

  if (cmds && cmds.length) {
    dialog.context.project.close = close;
    dialog.context.project.open = open;
    dialog.context.project.save = save;
  }

  if (!wait_for_ensure) return;

  const project: Project = dialog.context.project.project;

  if (!project) {
    logging.warnFP(LOG_NAME, 'projectCmd', dialog.user.profile, `Ensure returned with no project`);
    dialog.setTopic(Topics.PROJECT_TIMEOUT);
    // dialog.clearPing();
    return;
  }
  
  // show the project
  if (dialog.context.project.saving) {
    if (dialog.context.project.saved || new Date() > new Date(dialog.context.project.timeout)) {
      _sendSafeProject(dialog, project);
      // dialog.clearPing();
      dialog.context.project.saving = false;
      dialog.context.project.saved = false;
      return;
    } else {
      // dialog.quickPing();
      return false;
    }
  }

  if (logging.isDebug(dialog.user.profile)) DEBUG(`Cmd project ${project.id} progress:${project.progress} completed:${project.completed} contractor:${project.contractor ? project.contractor.id : 'NONE'}`);

  // check that project is ready
  if (dialog.isAuthenticatedNonGuest()) {
    try { dialog.projects.checkProject(project, true); } catch (e) {
      logging.warnFP(LOG_NAME, '_projectCmd', dialog.user.profile, 'error checking project', e);
      dialog.setTopic(Topics.PROJECT_INVALID);
      // dialog.clearPing();
    }
  }

  let do_save = dialog.isAuthenticatedNonGuest() && (dialog.context.project.load || (dialog.context.project.save && dialog.context.project.updated));
  // for groups with skip payment, set the payment on completion
  if (project.completed && !project.payment) {
    if (dialog.context.project.project.group_settings && dialog.context.project.project.group_settings.skip_payment) {
      project.payment = lang.project.SKIP_PAYMENT(new Date());
      do_save = true;
    }
  }

  if (do_save) {
    project.last_activity = project.last_update;
    if(project.contractor){
      // don't load selected projects for non-contractors or clients
      if (project.contractor.askfora_id === dialog.user.profile) {
        dialog.setTopic(Topics.PROJECT_CANDIDATE);
        return _candidateAction(dialog);
      } else if (project.client.askfora_id !== dialog.user.profile) {
        if (!project.client.askfora_id && project.client.comms && project.client.comms.includes(dialog.user.email)) {
          dialog.projects.projectRole(project);
        } else {
          dialog.setTopic(Topics.PROJECT_EXPIRED);
          return;
        }
      }
    } else if (project.client.askfora_id !== dialog.user.profile) {
      dialog.setTopic(Topics.PROJECT_CANDIDATE);
      return _candidateAction(dialog);
    }

    // save back project
    if (!dialog.context.project.saving) {
      dialog.context.project.saved = false;
      dialog.context.project.saving = true;
      dialog.context.project.timeout = SECONDS(new Date(), 90);
      dialog.runAsync('project', async () =>  {
        const p = await dialog.projects.update(project, !project.archived);
        if (dialog.context.project) {
          dialog.context.project.project = p;
          dialog.context.project.saved = true;
        }
      });
    }
  } else {
    dialog.context.project.saving = true;
    dialog.context.project.saved = true;
    _sendSafeProject(dialog, project);
    return;
  }
}

function _ensureProject(dialog: Dialog, update_fields: string[] = []): boolean {
  const projects: ProjectInfo[] = dialog.actionValues(ActionType.PROJECT);
  if (!dialog.context.project) {
    dialog.newDialog();
    dialog.context.project = new ProjectPluginState();
    dialog.context.project.timeout = SECONDS(new Date(), 90);
    if (projects && projects.length && projects[0].type === EntityType.Project) {
      _checkNotifyData(dialog);
      const r_project = dialog.projects.receiveProject(projects[0], update_fields);
      if (!r_project || !r_project.project || !r_project.project.id) {
        logging.warnFP(LOG_NAME, 'ensureProject', dialog.user.profile, `Received no project for ${projects[0].id}`);
        dialog.setTopic(Topics.PROJECT_MISSING);
        return false;
      } else {
        dialog.context.project.loading = true;
        dialog.runAsync('project', async () => {
          const p = await dialog.projects.get(r_project.project);
          if (dialog.context.project) {
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'ensureProject', dialog.user.profile, `project loaded ${projects[0].id}`);
            dialog.context.project.project = p;
            dialog.context.project.updated = dialog.context.project.updated || r_project.updated;
            dialog.context.project.loaded = true;
          } else logging.warnFP(LOG_NAME, 'ensureProject', dialog.user.profile, `project context missing while loading ${p.id}`);
        });
        return false;
      }
    }
  } else if(dialog.isGuestAccount() && projects && projects.length && projects[0].type === EntityType.Project) {
    const r_project = dialog.projects.receiveProject(projects[0], update_fields);
    if (r_project && r_project.project) {
      dialog.context.project.project = r_project.project;
      dialog.context.project.updated = dialog.context.project.updated || r_project.updated;
    }
  }

  if (dialog.context.project.loading && new Date(dialog.context.project.timeout) > new Date()) {
    if (!dialog.context.project.loaded) {
      return false;
    } else if (!dialog.context.project.project) {
      if (new Date(dialog.context.project.timeout) < new Date()) {
        logging.warnFP(LOG_NAME, 'ensureProject', dialog.user.profile, 'timeout loading project');
        dialog.setTopic(Topics.PROJECT_TIMEOUT);
      } else {
        logging.warnFP(LOG_NAME, 'ensureProject', dialog.user.profile, 'project missing');
        dialog.setTopic(Topics.PROJECT_MISSING);
      }
      return false;
    }
  }

  if (dialog.context.project.loading && dialog.context.project.loaded) {
    dialog.context.project.loading = false;
    dialog.context.project.loaded = false;
    dialog.resetProcessing();
  }

  return true;
}

function _findCandidate(dialog: Dialog, project: Project) {
  // also map back the candidate in the project
  for (const candidate of project.candidates) {
    for (const comm of project.me_candidate.comms) {
      if (candidate.comms.includes(comm)) return candidate;
    }
  }

  if (project.me_candidate) {
    const have_ids = project.candidates.map(c => c.askfora_id);
    if (!have_ids.includes(project.me_candidate.askfora_id)) {
      const add_candidate = projectPerson(project, project.me_candidate);
      add_candidate.comms = add_candidate.comms.filter(c => c !== add_candidate.id);
      add_candidate.id = null;
      project.candidates.push(add_candidate);
    }
    return project.me_candidate;
  }
}

// Processes escrow and payment by client
function _processPayment(dialog: Dialog) {
  const wait_for_ensure = _ensureProject(dialog);

  const people: Person[] = dialog.getRepliesType(EntityType.Person);
  if (people && people.length) dialog.context.project.people = people.filter(p => p);

  const entities = dialog.actionValues(ActionType.ENTITY).map(e => e.name);
  if (entities && entities.length) dialog.context.project.person_name = entities;

  if (!wait_for_ensure) return;

  let person = dialog.context.project.people && dialog.context.project.people.length ? dialog.context.project.people[0] : null;
  let project: Project = null;

  if (dialog.context.project.project) {
    project = dialog.context.project.project;
    person = project.contractor;
  } else if (dialog.context.project && dialog.context.project.loading) {
    // dialog.quickPing();
    return false;
  } else if (person) {
    for (const index in dialog.cache.projects) {
      if (dialog.cache.projects[index]) {
        if (dialog.cache.projects[index].contractor && dialog.cache.projects[index].contractor.ready) {
          for (const comm of dialog.cache.projects[index].contractor.comms) {
            if (person.comms.includes(comm)) {
              project = dialog.cache.projects[index];
              break;
            }
          }
        }
      }
      if (project) break;
    }
  } else {
    logging.warnFP(LOG_NAME, 'processPayment', dialog.user.profile, `No person to pay for ${dialog.context.project.project.id}`);
    dialog.setTopic(Topics.PROJECT_MISSING);
    return false;
  }

  if (!person) {
    // if there's a project but no person, this is an error
    if (dialog.context.project.project) {
      logging.warnFP(LOG_NAME, 'processPayment', dialog.user.profile, `Missing person to pay for ${dialog.context.project.project.id}`);
      dialog.setTopic(Topics.PROJECT_MISSING);
      return;
    }

    // look for a person and find a project
    dialog.clearActions();
    return personInfo.findPersonPrompt(dialog, dialog.context.project.person_name.pop());
  }

  if (project) {
    if (project.payment === true) {
      // dialog.quickPing();
      return;
    } else if (project.payment) {
      _sendSafeProject(dialog, project);
      // dialog.clearPing();
      return;
    }

    if (project.client.askfora_id !== dialog.user.profile || !project.escrow) {
      // TODO create a topic for "not authorized"
      if (!project.escrow) logging.warnFP(LOG_NAME, 'processPayment', dialog.user.profile, `No escrow trying to pay ${dialog.context.project.project.id}`);
      else logging.warnFP(LOG_NAME, 'processPayment', dialog.user.profile, `Not client trying to pay ${dialog.context.project.project.id}`);
      dialog.setTopic(Topics.PROJECT_MISSING);
      return;
    }

    const bool = dialog.actionValues(ActionType.BOOL);
    const has_charge = project.escrow && project.escrow.object === 'charge' || project.escrow.object === 'askfora' ||
      (project.escrow.object === 'payment_intent' && 
        ( project.escrow.charges && project.escrow.charges.data && project.escrow.charges.data.length) || 
        ( project.escrow.latest_charge &&  project.escrow.latest_charge.length))
    if (bool && bool.length && has_charge) {
      if (bool[0]) {
        let amount = project.fee;
        if (project.rate !== ProjectRate.fixed) amount *= project.progress;
        const escrow = project.escrow.amount / 100;
        const service_fee = amount * (isNaN(project.service_fee) ? SERVICE_FEE : project.service_fee);
        project.payment = true; // placeholder to complete the dialog
        // dialog.quickPing();
        let charge_id = null;
        switch (project.escrow.object) {
          case 'payment_intent': charge_id = project.escrow.charges && project.escrow.charges.data && project.escrow.charges.data.length ? 
                  project.escrow.charges.data[0].id : null;
            break;
          case 'charge':
            charge_id = project.escrow.id;
            break;
        }

        dialog.runAsync('project', async () => {
          try {
            const payment = await stripe.pay(project.skills, charge_id, project.escrow.on_behalf_of, amount);
            if (amount < escrow - service_fee) {
              const refund = await stripe.refund(charge_id, project.escrow.on_behalf_of, escrow - amount - service_fee);
              try { 
                await pay(dialog, project, payment, refund);
              } catch (e) {
                logging.errorFP(LOG_NAME, 'processPayment', dialog.user.profile, 'processing refund', e);
                await pay(dialog, project, payment, null, e);
              }
            } else await pay(dialog, project, payment);

            if (project.escrow.on_behalf_of === config.get('STRIPE_WISE_ACCOUNT')) {
              const contractor = new ForaUser(project.contractor.askfora_id);
              if ((await data.users.init(contractor, false))) {
                const wise_id = contractor.paymentAccount();
                if (wise_id) {
                  const client = wise();
                  const profile = (await client.getProfiles({})).find(p => p.type === 'business');
                  const quote = await client.createQuote({
                    profile: profile.id,
                    sourceCurrency: 'USD',
                    targetCurrency: contractor.getTokens(AuthProviders.Wise, wise_id).currency,
                    sourceAmount: amount,
                  });

                  const customerTransactionId = uuid();
                  const trf = await client.createTransfer({
                    targetAccount: wise_id,
                    quoteUuid: quote.id,
                    customerTransactionId,
                    details: { reference: 'AskFora' }
                  });

                  if (trf) {
                    if (trf.error) {
                      logging.errorFP(LOG_NAME, 'processPayment', dialog.user.profile, `Error transferring wise payment for ${project.id}`,  new InternalError(trf.statusCode, trf.message));
                    } else {
                      await transfer(dialog, project, trf as Transfer);
                    }
                  } else logging.errorFP(LOG_NAME, 'processPayment', dialog.user.profile, `Error transferring wise payment for ${project.id} with no response`,  null);
                }
              } else logging.errorFP(LOG_NAME, 'processPayment', dialog.user.profile, `Error taking payment for project ${project.id}, contractor init failed`, null);
            }
          } catch(e) {
            logging.errorFP(LOG_NAME, 'processPayment', dialog.user.profile, 'processing payment', e);
            project.payment = null;
            project.refund = null;
            project.last_update = new Date();
            project.last_activity = project.last_update;
            if (dialog.context.project) dialog.context.project.payment_error = e;
          }
        })
      } else dialog.reset('project');
    } else {
      _sendSafeProject(dialog, project);
      return;
    }

    // dialog.quickPing();
  }
}

function _projectProgress(dialog: Dialog) {
  let update_fields = []
  const projects: ProjectInfo[] = dialog.actionValues(ActionType.PROJECT);
  if (projects && projects.length && dialog.cache.projects[projects[0].id] &&
    dialog.cache.projects[projects[0].id].escrow && 
    dialog.cache.projects[projects[0].id].escrow.charges) update_fields = UPDATE_PROJECT_PROGRESS_FIELDS;

  if (!_ensureProject(dialog,  update_fields)) return;

  if (dialog.context.project.updated) {
    const project = dialog.context.project.project;

    const notify = new Date(project.last_activity) < MINUTES(project.last_update, -5) || config.isEnvOffline();
    project.last_activity = project.last_update;

    if (!project) {
      logging.warnFP(LOG_NAME, 'projectProgress', dialog.user.profile, `Lost project in topic ${dialog.topic} while sending ${JSON.stringify(dialog.context.project)}`);
      return;
    }

    if (!dialog.context.project.sending) {
      dialog.context.project.sending = true;
      if (project.client.self) return _saveSendProject(dialog);
      else {
        dialog.context.project.saving = false;
        dialog.context.project.saved = false;
        return _updateCandidateProject(dialog, false);
      }
    }

    if (notify) {
      const person = project.client.self ? project.contractor : project.client;
      const emails = parsers.findEmail(person.comms);
      const to_email =  emails && emails.length ? emails[0] : null;

      const email = {
        rcpts: [{ Name: person.displayName, Email: to_email }],
        subject: lang.project.PROGRESS_SUBJECT(project),
        message: lang.project.PROGRESS_MESSAGE(project, dialog.projects.getUrl(project, to_email), person),
      };

      dialog.projects.notify(dialog.context.project.project, new ForaUser(person.askfora_id), person, email, {
        group: dialog.getNotifyGroup(to_email), 
        notification: NotificationType.Project_Progress, 
        template: project.client.self ? TemplateType.Contractor : TemplateType.Client
      }).catch(e => dialog.asyncError(e));
      return;
    }
  } 

  _sendSafeProject(dialog, dialog.context.project.project);

  dialog.setTopic(Topics.PROJECT_CMD);
}

function _projectComplete(dialog: Dialog) {
  let update_fields = []
  const projects: ProjectInfo[] = dialog.actionValues(ActionType.PROJECT);
  if (projects && projects.length && dialog.cache.projects[projects[0].id] &&
    dialog.cache.projects[projects[0].id].escrow) {
      // update the incoming project
      projects[0].completed = true;
      projects[0].update_date = new Date();
      update_fields = [...UPDATE_PROJECT_PROGRESS_FIELDS, 'completed'];
    }

  if (!_ensureProject(dialog,  update_fields)) return;

  if (!dialog.context.project.project) {
    logging.warnFP(LOG_NAME, 'projectComplete', dialog.user.profile, `Lost project in topic ${dialog.topic} while receiving ${JSON.stringify(dialog.context.project)}`);
    dialog.reset('project');
    return;
  }

  if (dialog.context.project.updated && dialog.context.project.project.client.self) {
    const project = dialog.context.project.project;
    project.last_activity = project.last_update;

    if (project.escrow && project.escrow.id === lang.project.SKIP_ESCROW.id && 
      project.escrow.charges && !project.payment) {
      project.payment = lang.project.SKIP_PAYMENT(new Date());
      project.last_update = new Date();
      project.last_activity = project.last_update;
    }

    if (!dialog.context.project.sending) {
      dialog.context.project.sending = true;
      if (!dialog.context.project.project) {
        logging.warnFP(LOG_NAME, 'projectComplete', dialog.user.profile, `Lost project in topic ${dialog.topic} while sending ${JSON.stringify(dialog.context.project)}`);
        return;
      }
      if (dialog.context.project.project.client.self) return _saveSendProject(dialog);
      else {
        dialog.context.project.saving = false;
        dialog.context.project.saved = false;
        return _updateCandidateProject(dialog, false);
      }
    }

    if (dialog.context.project.updated) {
      const person = project.contractor;
      const emails = parsers.findEmail(person.comms);
      const to_email =  emails && emails.length ? emails[0] : null;

      const email = {
        rcpts: [{ Name: person.displayName, Email: to_email }],
        subject: lang.project.COMPLETED_SUBJECT(project),
        message: lang.project.COMPLETED_MESSAGE(project, dialog.projects.getUrl(project, to_email)),
      };

      dialog.projects.notify(project, new ForaUser(person.askfora_id), person, email, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Complete}).catch(e => dialog.asyncError(e));
    }
  } else _sendSafeProject(dialog, dialog.context.project.project);

  dialog.setTopic(Topics.PROJECT_CMD);
}

function _projectStart(dialog: Dialog) {
  if (!_ensureProject(dialog)) return;

  const project = dialog.context.project.project;

  if (!project) {
    logging.warnFP(LOG_NAME, 'projectStart', dialog.user.profile, `Lost project in topic ${dialog.topic} while receiving ${JSON.stringify(dialog.context.project)}`);
    dialog.reset('project');
    return;
  }

  if (project.client.self && project.contractor && project.escrow && project.escrow.id === lang.project.SKIP_ESCROW.id) {
    if (!dialog.context.project.sending) {
      dialog.context.project.sending = true;
      dialog.runAsync('project', async () => {
        if (dialog.context.project) {
          dialog.context.project.updated = true;
          dialog.context.project.escrow_prompt = await escrow(dialog, project, lang.project.SKIP_CHARGE);
        }
      });
    }
  }
}

// save project and get a clean copy to send to user
function _saveSendProject(dialog: Dialog, next_topic: Topic = null) {
  if (!dialog.context.project) {
    dialog.setTopic(Topics.DEFAULT);
    return;
  }

  if (!dialog.context.project.saving) {
    dialog.context.project.saving = true;
    dialog.context.project.saved_topic = next_topic ? next_topic : dialog.topic;
    dialog.context.project.saved = false;
    dialog.setTopic(Topics.PROJECT_SAVING);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'saveSendProject', dialog.user.profile, `saving project with next topic ${dialog.context.project.saved_topic}`);

    if (dialog.context.project.project.client.self || 
      ( dialog.context.project.project.me_candidate && 
        checkState(dialog.context.project.project.me_candidate, ProjectCandidateState.VIEWED) >= 0)) { 
      const project = dialog.context.project.project;
      project.last_activity = project.last_update;
      dialog.runAsync('project', async () => {
        const p = await dialog.projects.update(project);
        if (p.contractor) await dialog.projects.createForUser(new ForaUser(p.contractor.askfora_id), p.contractor, p);
        if (dialog.context.project) {
          dialog.context.project.project = p;
          dialog.context.project.saved = true;
        }
      });
    } else dialog.context.project.saved = true;
  } else if (dialog.context.project.saved) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'saveSendProject', dialog.user.profile, `saved project, next topic ${next_topic}`);
    _sendSafeProject(dialog, dialog.context.project.project);
    dialog.setTopic(dialog.context.project.saved_topic);

    dialog.context.project.saving = false;
    dialog.context.project.saved = false;
    dialog.context.project.saved_topic = null;
    dialog.resetProcessing();
  }
}

// updates project for candidates
function _updateCandidateProject(dialog: Dialog, send_notify = false, save = true) {
  if (!dialog.context.project) {
    dialog.setTopic(Topics.DEFAULT);
    return;
  }

  if (save && !dialog.context.project.saving) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_updateCandidateProject', dialog.user.profile, `saving project ${JSON.stringify(dialog.context.project.project)}`);
    dialog.context.project.saving = true;
    dialog.context.project.saved_topic = dialog.topic;
    dialog.setTopic(Topics.PROJECT_CANDIDATE_UPDATE);
    dialog.context.project.saved = false;

    dialog.context.project.timeout = SECONDS(new Date(), 90);

    dialog.resetProcessing();
    dialog.runAsync('project', async () => {
      const project = dialog.context.project.project;
      project.last_activity = project.last_update;

      if (dialog.isAuthenticatedNonGuest()) await data.users.notificationsClear(dialog.user, NotificationType.Project_Invite);

      // const pl = await dialog.projects.get(project);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_updateCandidateProject', dialog.user.profile, `update project ${JSON.stringify(project)}`);
      let pu;
      if(checkState(project.me_candidate, ProjectCandidateState.VIEWED) >= 0) {
          pu = await dialog.projects.update(project, false);
      } else {
        pu = new Project(project);
      }

      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, '_updateCandidateProject', dialog.user.profile, `${send_notify ? 'notify' : 'updated'} project ${JSON.stringify(pu)}`);

      if (dialog.context.project) dialog.context.project.project = pu;

      if (pu.archived) {
         if (!pu.contractor || pu.contractor.askfora_id !== dialog.user.profile) {
          pu = new Project(pu);
          pu.archived = false;
         }
      }

      // add skills if candidate accepted
      if (dialog.me && pu.me_candidate && pu.me_candidate.state === ProjectCandidateState.ACCEPTED) {
        const index = 150;
        const start = new Date();
        let saved = false;
        for (const value of pu.skill_set) {
          if (!parsers.ignore(value)) {
            const tag = new Tag(TagType.skill, value, index, start, 100);
            saved = saveOneTypeValue(dialog.me.tags, tag) || saved;
          }
        }

        if (saved) {
          logging.warnFP(LOG_NAME, 'updateCandidateProject', dialog.user.profile, `Updating self with new skills`);
          await dialog.people.save(dialog.me);
        }
      }

      if (send_notify) await _candidateNotify(dialog, pu);
      else {
        await dialog.projects.createForUser(dialog.user, dialog.me, pu);
        await dialog.projects.updateClient(pu);
      }

      if (dialog.context.project) dialog.context.project.saved = true;
    });

  } else if (!save || dialog.context.project.saved || new Date() > new Date(dialog.context.project.timeout)) {
    _sendSafeProject(dialog, dialog.context.project.project);
    if (dialog.context.project.saved_topic) dialog.setTopic(dialog.context.project.saved_topic);
    else if (save) dialog.setTopic(Topics.DEFAULT);

    dialog.context.project.saved_topic = null;
    dialog.resetProcessing();
    return runAction(dialog);
  }
}

// remove sensitive info before sending project
function _sendSafeProject(dialog: Dialog, project: Project) {
  // const is_contractor: boolean = project.client && (!dialog.me || project.client.id !== dialog.me.id);
  const is_client = (project.client && project.client.askfora_id == dialog.user.profile) ||
    (project.proposal && project.client.askfora_id === FORA_PROFILE);
  const reply_project: ProjectInfo = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, is_client);
  dialog.addReplies(reply_project);
}

// notify client of candidate action
async function _candidateNotify(dialog: Dialog, project: Project): Promise<void> {
  const to_user = new ForaUser(project.client.askfora_id);
  await data.users.init(to_user, false);
  const to_email = to_user.email ? [to_user.email] : parsers.findEmail(project.client.comms);

  const email = {
    rcpts: [{ Name: project.client.displayName, Email: to_email && to_email.length ? to_email[0] : null }],
    subject: null,
    message: null,
  };

  const url = dialog.projects.getUrl(project, email.rcpts[0].Email);

  let notify_type: NotificationType = null;

  switch (project.me_candidate.state) {
    case ProjectCandidateState.ACCEPTED:
      if (project.expert) {
        email.subject = lang.project.ANSWER_SUBJECT(project, project.me_candidate);
        email.message = lang.project.ANSWER_MESSAGE(project, project.me_candidate, url);
        notify_type = NotificationType.Project_Answer;
      } else {
        email.subject = lang.project.ACCEPTED_SUBJECT(project, project.me_candidate);
        email.message = lang.project.ACCEPTED_MESSAGE(project, project.me_candidate, url);
        notify_type = NotificationType.Project_Accepted;
      }
      break;
    case ProjectCandidateState.DECLINED:
      email.subject = lang.project.DECLINED_SUBJECT(project, project.me_candidate);
      email.message = lang.project.DECLINED_MESSAGE(project, project.me_candidate, url);
      notify_type = NotificationType.Project_Declined;
      break;
    case ProjectCandidateState.SUBMITTED:
      email.subject = lang.project.SUBMITTED_SUBJECT(project);
      email.message = lang.project.SUBMITTED_MESSAGE(project, url);
      notify_type = NotificationType.Project_Submit;
      break;
    case ProjectCandidateState.PAYMENT_REQUESTED:
      email.subject = lang.project.REQUEST_PAY_SUBJECT(project);
      email.message = lang.project.REQUEST_PAY_MESSAGE(project, url);
      notify_type = NotificationType.Project_PayRequested;
      break;
  }

  await dialog.projects.updateClient(project, notify_type !== null);
  if (notify_type) {
    return dialog.projects.notify(project, to_user, project.me_candidate, email.subject ? email : null, { group: dialog.getNotifyGroup(email.rcpts[0].Email), notification: notify_type}).catch(e => dialog.asyncError(e));
  }
}

async function transfer(dialog: Dialog, project: Project, trf: Transfer) {
  project.transfer = trf;
  project.last_update  = new Date();
  project.last_activity = project.last_update;

  // dialog.waitForRun().then(async () => {
  const p = await dialog.projects.update(project);
  const person = p.contractor;
  const contractor_user = new ForaUser(person.askfora_id);
  await dialog.projects.createForUser(contractor_user, person, p);
  await data.plugins.storagePlugin().transferSave(new Transfer(trf));
  // }).catch(err => dialog.asyncError(err));
}

// update project and notify contractor of payout
async function pay(dialog: Dialog, project: Project, payment: any, refund: any = null, error: any = null) {
  project.payment = payment;
  project.refund = refund;
  project.last_update = new Date();
  project.last_activity = project.last_update;
  dialog.context.project.payment_error = error;

  if (payment) {
    project = await dialog.projects.update(project);
    const person = project.contractor;

    const contractor_user = new ForaUser(person.askfora_id);
    await data.users.init(contractor_user, false);
    await dialog.projects.createForUser(contractor_user, person, project);

    const emails = contractor_user.email ? [contractor_user.email] : parsers.findEmail(person.comms);
    const to_email =  emails && emails.length ? emails[0] : null;

    const email = {
      rcpts: [{ Name: person.displayName, Email: to_email }],
      // subject: lang.project.PAID_SUBJECT(p),
      // message: lang.project.PAID_MESSAGE(p),
    };

    // notify contractor
    await dialog.projects.notify(project, new ForaUser(person.askfora_id), person, email, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Paid}).catch(e => dialog.asyncError(e));

    // schedule contractor follow up
    await dialog.projects.notify(project, new ForaUser(person.askfora_id), person, email, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Followup, template: TemplateType.Contractor, when: DAYS(new Date(), 1)}).catch(e => dialog.asyncError(e));

    // email follow up to client
    const client_email = { rcpts: [{ Name: dialog.me.displayName, Email: dialog.user.email }], }
    await dialog.projects.notify(project, dialog.user, null, client_email, { group: dialog.getNotifyGroup(), notification: NotificationType.Project_Followup, template: TemplateType.Client}).catch(e => dialog.asyncError(e));

    if (dialog.context.project) dialog.context.project.project = project;
  }
}

// update project, notify contractor and client, archive for all other candidates
async function escrow(dialog: Dialog, project: Project, charge: any) {
  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'escrow', dialog.user.profile, `Processing escrow for ${project.id}`);

  project.escrow = charge;
  project.last_update = new Date();
  project.last_activity = project.last_update;

  if (project.rate === ProjectRate.fixed) project.progress = project.duration;

  // create a task for the project
  const project_task = new Task({
    id:uuid(),
    people: [project.contractor],
    created: new Date(),
    title: `Notes on ${project.title ? project.title : project.skills}`,
    project: project.id,
  });

  await dialog.tasks.create(project_task);

  const p = await dialog.projects.update(project);

  const task_info = taskInfo(project_task, false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
  task_info.added = true;

  const person = p.contractor;
  // await dialog.projects.createForUser(new ForaUser(person.askfora_id), person, p);

  // email contractor
  const contractor_user = new ForaUser(person.askfora_id);
  await data.users.init(contractor_user, false);
  const emails = contractor_user.email ? [contractor_user.email] : parsers.findEmail(person.comms);
  const to_email = emails  && emails.length ? emails[0] : null;
  const client_email = await dialog.projects.clientEmail(project);

  const email_contractor = {
    rcpts: [{ Name: person.displayName, Email: to_email }],
    // ccs: [{ Name: dialog.me.displayName, Email: client_email }],
    subject: project.select_notice ? 
      project.escrow.id === lang.project.SKIP_ESCROW.id ? 
      lang.project.START_SUBJECT(project) : 
      lang.project.ESCROW_SUBJECT(project) : 
      lang.project.SELECTED_SUBJECT(project),
    message: project.escrow.id === lang.project.SKIP_ESCROW.id ? 
      lang.project.START_MESSAGE(project, dialog.projects.getUrl(project, to_email)) :
      lang.project.ESCROW_MESSAGE(project, dialog.projects.getUrl(project, to_email)),
  };

  await dialog.projects.createForUser(contractor_user, person, p);
  await dialog.projects.notify(p, contractor_user, person, email_contractor, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Deposit}).catch(e => dialog.asyncError(e));

  // email client/
  const email_client = {
    rcpts: [{ Name: dialog.me.displayName, Email: client_email }],
    subject: project.escrow.id === lang.project.SKIP_ESCROW.id ? 
      lang.project.START_RECEIPT_SUBJECT(project) :
      lang.project.ESCROW_RECEIPT_SUBJECT(project),
    message:  project.escrow.id === lang.project.SKIP_ESCROW.id ? 
      lang.project.START_RECEIPT_MESSAGE(project, dialog.projects.getUrl(project, client_email)) :
      lang.project.ESCROW_RECEIPT_MESSAGE(project, dialog.projects.getUrl(project, client_email)),
  };

  // create an archived project and notify every candidate who has looked at the job that isn't the contractor
  await dialog.projects.archiveForCandidates(p).catch(e => dialog.asyncError(e));

  await dialog.projects.notify(p, dialog.user, null, email_client, { group: dialog.getNotifyGroup(client_email), notification: NotificationType.Project_Escrow}).catch(e => dialog.asyncError(e));

  const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, true);
  project_info.updated = true;

  return {
    info: [task_info, project_info],
    reply: lang.project.ESCROW_PROMPT(project),
  };
}

onEscrow(escrow);

export default {
  name: PROJECT_NAME,
  description: PROJECT_DESC,
  examples: [],
  reserved: PROJECT_RESERVED,
  requiresAuth: false,
  keywordMatch,
  isActive,
  runAction,
  setAction,
  setPrompt,
  shortcuts: lang.project.PROJECT_SHORTCUT,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
