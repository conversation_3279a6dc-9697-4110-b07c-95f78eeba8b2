import _ from 'lodash';
import path from 'path';

import { ActionType, TemplateType } from '../types/globals';
import { ContractPluginState, Plugin } from '../types/plugins';
import { AuthContext, AuthProviders, EntityType, NotificationType, TagType, findTypeValues } from '../types/shared';

import { localeDowMonthDay } from '../utils/format';
import { contractInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import { signContract as sign } from '../utils/sign';

import { AuthProvider } from '../auth/auth_provider';
import lang from '../lang';
import Dialog, { TOPIC, Topics } from '../session/dialog';
import ForaUser from '../session/user';
import stripe from '../sources/stripe_controller';

const DEBUG = (require('debug') as any)('fora:plugins:contract');
const LOG_NAME = 'plugins.contract';

const CONTRACT_NAME = 'Contract';
const CONTRACT_DESC = 'Sign a new contract between two people';

const CONTRACT_RESERVED = ['contract', 'myself'];

const topics = [
  // entry points
  TOPIC('CONTRACT_AUTH'), // need to login
  TOPIC('CONTRACT_AUTH_CANCELLED'), // need to login
  TOPIC('CONTRACT_LOAD'), // return the contract
  TOPIC('CONTRACT_SIGN'), // get a contract for signing

  // check name before signing
  TOPIC('CONTRACT_CONFIRM'), // confirm contract details

  // internal
  TOPIC('CONTRACT_LOADING'), // load the contracts
  TOPIC('CONTRACT_SAVING'), // save the contract
  TOPIC('CONTRACT_NOTIFY'), // notifying
  TOPIC('CONTRACT_MISSING'), // couldn't load

  // called from the contract sign prep
  TOPIC('CONTRACT_COMPLETE'), // finish signing (or error or decline)
  TOPIC('CONTRACT_BAD_CMD'),
  TOPIC('CONTRACT_NOT_READY'),
  TOPIC('CONTRACT_CANCELED'),

  // post signing
  TOPIC('CONTRACT_SIGNED'), // signed by at least one party
  TOPIC('CONTRACT_DECLINED'), // declined by at least one party
  TOPIC('CONTRACT_ERROR'), // error in signing
  TOPIC('CONTRACT_MISMATCH'), // wrong user
  TOPIC('CONTRACT_DONE'), // nonthing to do
];

function keywordMatch(actions, message) {
  return parsers.checkKeyword(message, lang.contract.CONTRACT_KWD, true) &&
    Dialog.checkActions(actions, ActionType.CONTRACT);
}

function setAction(dialog: Dialog, message: string, raw_message: string) {
  if (!dialog.context.contract) {
    const contract_info = dialog.actionValues(ActionType.CONTRACT);
    if (contract_info && contract_info.length && contract_info[0].id) {
      const doc = null; // dialog.cache.contracts[contract_info[0].id];
      dialog.context.contract = { 
        contract_id: contract_info[0].id,
        doc,
        was_signed: doc ? 
          (doc.client_id === dialog.user.profile && doc.client_signed) ||
          (doc.contractor_id === dialog.user.profile && doc.contractor_signed)
          : false,
        was_declined: doc ? 
          (doc.client_id === dialog.user.profile && doc.client_declined) ||
          (doc.contractor_id === dialog.user.profile && doc.contractor_declined)
          : false,
      } as ContractPluginState;
    }
  }

  if (!isActive(dialog)) {
    if (parsers.checkKeyword(message, lang.contract.CONTRACT_LOAD, true)) dialog.setTopic(Topics.CONTRACT_LOAD);
    else if (parsers.checkKeyword(message, lang.contract.CONTRACT_COMPLETE, true) && dialog.message.command) {
      dialog.context.contract.cmd = message.split(' ')[1];
      dialog.setTopic(Topics.CONTRACT_COMPLETE);
    }
    else if (parsers.checkKeyword(message, lang.contract.CONTRACT_SIGN, true)) dialog.setTopic(Topics.CONTRACT_SIGN);
  }

  if (dialog.checkTopic(Topics.CONTRACT_CONFIRM) && !dialog.message.command && message.length) {
    if (!parsers.checkKeyword(message, lang.contract.MYSELF, true)) {
      dialog.clearActions();
      dialog.addAction(ActionType.ORG, { type: EntityType.Organization, name: raw_message });
    }
  }

  return isActive(dialog);
}

function isActive(dialog) {
  return dialog.checkTopic(topics);
}

function _loadContract(dialog: Dialog) {
  if (dialog.context.contract.doc) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'loadContract', dialog.user.profile, `Loaded ${dialog.context.contract.doc.id}`);
    if (dialog.context.contract.next_topic) {
      dialog.setTopic(dialog.context.contract.next_topic);
      dialog.context.contract.next_topic = null;
    }
    dialog.resetProcessing();
    return true;
  }

  if (dialog.context.contract.loading) return false;

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'loadContract', dialog.user.profile, `Loading ${dialog.context.contract.contract_id}`);

  dialog.context.contract.loading = true;
  dialog.context.contract.next_topic = dialog.topic;
  dialog.setTopic(Topics.CONTRACT_LOADING);
  const contract_id = dialog.context.contract.contract_id;
  dialog.runAsync('contract', async () => {
    const doc = await dialog.contracts.load(contract_id);
    if (!doc) {
      logging.warnFP(LOG_NAME, 'loadContract', dialog.user.profile, `No contract found ${contract_id}`);
      dialog.setTopic(Topics.CONTRACT_MISSING);
    } else if (dialog.context.contract) {
      dialog.context.contract.doc = doc;
    } else logging.warnFP(LOG_NAME, 'loadContract', dialog.user.profile, `Lost context loading ${contract_id}`);
  });

  return false;
}

function _saveContract(dialog: Dialog) {
  if (dialog.context.contract.saved) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'loadContract', dialog.user.profile, `Saved ${dialog.context.contract.doc.id}`);
    if (dialog.context.contract.next_topic) {
      dialog.setTopic(dialog.context.contract.next_topic);
      dialog.context.contract.next_topic = null;
      return runAction(dialog);
    }
    dialog.resetProcessing();
    return true;
  }

  if (dialog.context.contract.saving) return false;

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'saveContract', dialog.user.profile, `Saving ${dialog.context.contract.doc.id}`);

  dialog.resetProcessing();

  dialog.context.contract.notifying = false;
  dialog.context.contract.notified = false;
  dialog.context.contract.saving = true;
  dialog.context.contract.next_topic = dialog.topic;
  dialog.setTopic(Topics.CONTRACT_SAVING);
  dialog.runAsync('contract', async () => {
    if (dialog.context.contract) {
      await dialog.contracts.save(dialog.context.contract.doc);
      dialog.context.contract.saved = true;
    }
  });

  return false;
}

function _confirmContract(dialog: Dialog) {
  const doc = dialog.context.contract.doc;

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'confirmContract', dialog.user.profile, `Confirming contract ${doc.id} ${doc.client_id == dialog.user.profile ? 'client' : 'contractor'}`);

  const entity = dialog.actionValues(ActionType.ENTITY).filter(e => e.people && e.people.length);
  const org = dialog.actionValues(ActionType.ORG);
  let name = dialog.user.name;

  if (org.length && !parsers.checkKeyword(org[0].name.toLowerCase(), lang.contract.MYSELF, true)) name = org[0].name;
  else if (entity.length) name = entity[0].name;
  else if (dialog.me) name = dialog.me.displayName;

  if (!dialog.message.ping && !dialog.message.command) {
    if (doc.client_id === dialog.user.profile) {
      if (!doc.client_reviewed) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'confirmContract', dialog.user.profile, `Confirming client ${name} for ${doc.id}`);
        doc.client_name = name;
        doc.client_reviewed = true;
      }
    } else {
      if (!doc.contractor_reviewed) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'confirmContract', dialog.user.profile, `Confirming contractor ${name} for ${doc.id}`);
        doc.contractor_name = name;
        doc.contractor_reviewed = true;
      }
    }
  }

  if (doc.client_reviewed && doc.contractor_reviewed &&
    ((doc.client_id === dialog.user.profile && doc.contractor_signed) || 
     (doc.contractor_id === dialog.user.profile) )) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'confirmContract', dialog.user.profile, `Contract ${doc.id} ready to sign`);
    dialog.setTopic(Topics.CONTRACT_SIGN);
    return true;
  } 

  if (dialog.context.contract.saved) {
    if (dialog.context.contract.notified) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'confirmContract', dialog.user.profile, `Contract ${doc.id} notified`);
      return false;
    } /* else if (dialog.context.contract.notifying) {
      dialog.quickPing();
      return false;
    } */

    if (!doc.contractor_reviewed) return _notifyContract(dialog); 
    
    return false;
  }

  if (dialog.message.command || dialog.message.ping) return false;

  _saveContract(dialog);
  return false;
}

function _signContract(dialog: Dialog) {
  const doc = dialog.context.contract.doc;

  if (doc.client_declined || doc.contractor_declined) {
    // dialog.clearPing();
    dialog.setTopic(Topics.CONTRACT_CANCELED);
    return;
  }

  if (!doc.client_reviewed || !doc.contractor_reviewed) {
    // dialog.clearPing();
    dialog.clearCommand();
    dialog.setTopic(Topics.CONTRACT_CONFIRM);
    return;
  }

  if (doc.hash) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'signContract', dialog.user.profile, `Document ${doc.id} already ready for signature`);
    dialog.context.contract.edit = true;
    dialog.setTopic(Topics.CONTRACT_LOAD);
    return;
  } else if(doc.client_id === dialog.user.profile && !doc.contractor_signed) {
    // only generate for contractor to avoid timing
    // dialog.clearPing();
    dialog.clearCommand();
    dialog.setTopic(Topics.CONTRACT_CONFIRM);
  } else if (!dialog.context.contract.signing) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'signContract', dialog.user.profile, `Preparing ${doc.id} for signature`);
    dialog.context.contract.signing = true;
    const url = dialog.contracts.getRedirectUrl(doc.id, dialog.user.email, dialog.group_host);

    dialog.resetProcessing();
    dialog.runAsync('contract', async () => {
      const sign_doc = await sign({
        contract: path.resolve(__dirname, '../files/contract.json'),
        title: 'Independent Contractor Agreement',
        // requester: dialog.context.contract.requester,
        client_id: dialog.context.contract.doc.client_id,
        cvars: {
          CLIENT: doc.client_name,
          CLIENT_EMAIL: doc.client_email,
          CONTRACTOR: doc.contractor_name,
          CONTRACTOR_EMAIL: doc.contractor_email,
          CONTEXT: doc.context,
          DATE: localeDowMonthDay(new Date(), dialog.message.locale, dialog.message.timeZone),
        },
        signers: [
          {
            id: 1,
            name: doc.client_name,
            email: doc.client_email,
          },
          {
            id: 2,
            name: doc.contractor_name,
            email: doc.contractor_email,
          },
        ],
      },   url);

      if (sign_doc) {
        if (dialog.context.contract) {
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'signContract', dialog.user.profile, `Contract ${dialog.context.contract.doc.id} ready for signature`);
          dialog.context.contract.doc.client_url = sign_doc.signers[0].url;
          dialog.context.contract.doc.contractor_url = sign_doc.signers[1].url;
          dialog.context.contract.doc.hash = sign_doc.hash;
          dialog.context.contract.edit = true;
          await dialog.contracts.save(dialog.context.contract.doc);
        } else logging.warnFP(LOG_NAME, 'signContract', dialog.user.profile, `Contract ${doc.id} ready for signing but lost context`);
      } else {
        logging.warnFP(LOG_NAME, 'signContract', dialog.user.profile, `No doc when trying to sign`); 
        dialog.clearContext('contract');
      }
    });
  }

  if (dialog.context.contract.signing) {
    // dialog.quickPing();
    return;
  }
}

function _completeContract(dialog: Dialog) {
  const cmd = dialog.context.contract.cmd;
  const doc = dialog.context.contract.doc;

  if (!['signed', 'decliend', 'error'].includes(cmd)) {
    logging.warnFP(LOG_NAME, 'completeContract', dialog.user.profile, `Invalid contract command ${cmd} for ${doc.id}`);
    dialog.setTopic(Topics.CONTRACT_BAD_CMD);
    return;
  }

  if (!doc.hash) {
    logging.warnFP(LOG_NAME, 'completeContract', dialog.user.profile, `Contract not ready for command ${cmd} for ${doc.id}`);
    dialog.setTopic(Topics.CONTRACT_NOT_READY);
    return;
  }

  let next_topic = null;

  switch(cmd) {
    case 'signed':
      if (doc.client_id === dialog.user.profile) {
        if (doc.client_signed) next_topic = Topics.CONTRACT_LOAD;
        else {
          doc.client_signed = true;
          next_topic = Topics.CONTRACT_SIGNED;
        }
      } else {
        if (doc.contractor_signed) next_topic = Topics.CONTRACT_LOAD;
        else {
          doc.contractor_signed = true;
          next_topic = Topics.CONTRACT_SIGNED;
        }
      }
      break;
    case 'declined':
      if (doc.client_id === dialog.user.profile) doc.client_declined = true;
      else doc.contractor_declined = true;
      next_topic = Topics.CONTRACT_DECLINED;
      break;
    case 'error':
      dialog.setTopic(Topics.CONTRACT_ERROR);
      return;
  }

  // don't repeat sign
  if (dialog.context.contract.was_signed || dialog.context.contract.was_declined) {
    dialog.clearInternalNotifications(NotificationType.Contract_Ready);
    dialog.setTopic(Topics.CONTRACT_DONE);
  } else {
    dialog.setTopic(next_topic);
    return _saveContract(dialog);
  }
}

function _notifyContract(dialog: Dialog) {
  if (!dialog.context.contract || !dialog.context.contract.doc) return true;
  if (dialog.context.contract.notified) {
    if (dialog.context.contract.next_topic) {
      dialog.setTopic(dialog.context.contract.next_topic);
      dialog.context.contract.next_topic = null;
    }
    return true;
  }

  if (dialog.context.contract.notifying) {
    // dialog.quickPing();
    return false;
  }

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'notifyContract', dialog.user.profile, `Notifying ${dialog.context.contract.contract_id}`);

  dialog.resetProcessing();
  dialog.context.contract.notifying = true;
  if (!dialog.context.contract.next_topic) dialog.context.contract.next_topic = dialog.topic;
  dialog.setTopic(Topics.CONTRACT_NOTIFY);
  dialog.runAsync('contract', async () => {
    if (dialog.context.contract) {
      const doc = dialog.context.contract.doc;
      await dialog.contracts.notify(doc);

      // special client side notfications
      if (doc.client_id == dialog.user.profile) {
        const projects = await dialog.projects.byContract([doc.id]);
        if (dialog.context.contract && projects.length) {
          for (const project of projects) {
            if (!project.select_notice) {
              const url = dialog.contracts.getSignUrl(doc.id, doc.contractor_email);
              const subject = lang.project.SELECTED_SUBJECT(project);
              const message = lang.project.SELECTED_MESSAGE(project, project.contractor, url, !doc.contractor_signed);

              project.select_notice = true;
              dialog.context.contract.project_contractor = project.contractor;
              dialog.context.contract.project_ask_deposit = project.escrow === null || project.escrow === undefined;
              dialog.context.contract.project_amount = project.fee;
              if (project.rate !== 'fixed') dialog.context.contract.project_amount *= project.duration;
              dialog.context.contract.project_service_fee = dialog.context.contract.project_amount * project.service_fee;

              project.last_update = new Date();
              project.last_activity = project.last_update;

              await dialog.projects.update(project, true);
              await dialog.projects.createForUser(new ForaUser(project.contractor.askfora_id), project.contractor, project);

              const notify_email = {
                rcpts: [{ Name: doc.contractor_name, Email: doc.contractor_email }],
                subject,
                message,
              };

              const notification = project.escrow || !doc.contractor_signed ? NotificationType.Project_Selected : NotificationType.Contract_Ready;
              let template = doc.contractor_signed ? TemplateType.Ready : null;
              if (project.contractor.ready) template = TemplateType.NoStripe;

              await dialog.projects.notify(project, new ForaUser(project.contractor.askfora_id), project.contractor, notify_email, { group: dialog.getNotifyGroup(doc.contractor_email), notification, template});

            } else if(!project.contractor.ready) {
              const group_email = dialog.getGroup(dialog.groupByEmail(doc.contractor_email));
              const url = stripe.stripeConnectUrl(group_email)
              const subject = lang.project.SELECTED_SUBJECT(project);
              const message = lang.project.SELECTED_MESSAGE(project, project.contractor, url, !doc.contractor_signed);

              project.select_notice = true;
              dialog.context.contract.project_contractor = project.contractor;
              dialog.context.contract.project_ask_deposit = project.escrow === null || project.escrow === undefined;

              project.last_update = new Date();
              project.last_activity = project.last_update;

              await dialog.projects.update(project, true);
              await dialog.projects.createForUser(new ForaUser(project.contractor.askfora_id), project.contractor, project);

              const notify_email = {
                rcpts: [{ Name: doc.contractor_name, Email: doc.contractor_email }],
                subject,
                message,
              };

              await dialog.projects.notify(project, new ForaUser(project.contractor.askfora_id), project.contractor, notify_email, { group: dialog.getNotifyGroup(doc.contractor_email), notification: NotificationType.Contract_Signed, message: lang.stripe.CONNECT_STRIPE});
            } else if(doc.client_signed && doc.contractor_signed && project.contractor.ready && !project.escrow) {
              const person = project.client;
              const emails = parsers.findEmail(person.comms);
              const to_email = emails && emails.length ? emails[0] : null;

              const email = {
                rcpts: [{ Name: person.displayName, Email: to_email }],
                subject: lang.project.DEPOSIT_SUBJECT(project.contractor),
                message: lang.project.DEPOSIT_MESSAGE(project, project.contractor, dialog.projects.getUrl(project, to_email)),
              };

              await dialog.projects.notify(project, dialog.user, null, email, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Ready}).catch(e => dialog.asyncError(e));
            } 
          }
        }
      } else {
        const projects = await dialog.projects.byContract([doc.id]);
        if (dialog.context.contract && projects.length) {
          for (const project of projects) dialog.context.contract.project_client = project.client;
        }
      } 
    }

    if (dialog.context.contract) dialog.context.contract.notified = true;
  });

  return false;
}

function runAction(dialog: Dialog) {
  if (!dialog.context.contract) {
    if (isActive(dialog)) dialog.setTopic(Topics.DEFAULT);
    return;
  }

  const loaded = _loadContract(dialog);
  if (!loaded) return;

  switch (dialog.topic) {
    case Topics.CONTRACT_SAVING: return _saveContract(dialog);
    case Topics.CONTRACT_LOAD:
      if (dialog.context.contract.doc.client_id === dialog.user.profile && 
        !dialog.context.contract.doc.client_reviewed &&
        dialog.context.contract.doc.contractor_reviewed) {
          // catch out of order during load
        dialog.setTopic(Topics.CONTRACT_SIGN);
        // no break
      } else break;
    case Topics.CONTRACT_CONFIRM: 
      if(!_confirmContract(dialog)) break;
      // else no break
    case Topics.CONTRACT_SIGN: return _signContract(dialog);
    case Topics.CONTRACT_COMPLETE: return _completeContract(dialog);
    case Topics.CONTRACT_SIGNED: 
    case Topics.CONTRACT_DECLINED: 
    case Topics.CONTRACT_ERROR: 
      return _notifyContract(dialog);
  }
}

function setPrompt(dialog: Dialog) {
  const doc = dialog.context.contract ? dialog.context.contract.doc : null;

  switch (dialog.topic) {
    case Topics.CONTRACT_AUTH:
      if (!dialog.message.ping) {
        const providers = AuthProvider.clientAuthSet(AuthContext.Contract, dialog.group_host);
        dialog.addPrompt(lang.contract.AUTH(providers.map(p => p.name)), true);
        dialog.addAnswers(providers.map(p => p.name).concat([lang.project.CREATE_PROMPT_NEVERMIND]));
        for (const provider of providers) {
          const qrs = AuthProvider.contextPermissions(AuthContext.Contract);
          qrs.push(lang.init.REGISTER_REPLY_LINK(provider));
          dialog.addQuickReply(provider.name, [] /* qrs */, {redirect: provider.url});
        }
        // dialog.clearPing();
        dialog.setSticky();
      }
      break;
    case Topics.AUTH_CANCELLED:
      if (!dialog.message.ping) dialog.addPrompt(lang.contract.AUTH_CANCELLED(AuthProvider.clientAuthInfo(AuthContext.Contract, dialog.context.init.provider, dialog.group_host)), true);
      break;

    case Topics.CONTRACT_LOAD: 
      if (doc) {
        let url = doc.client_signed && doc.contractor_signed ? dialog.contracts.getDocUrl(doc.id, dialog.user.email) : null;
        let signed = false;
        if (doc.client_id === dialog.user.profile) {
          if (!doc.client_signed && doc.client_reviewed && doc.client_url) url = doc.client_url;
          signed = doc.client_signed;
        } else {
          if (!doc.contractor_signed && doc.contractor_reviewed && doc.contractor_url) url = doc.contractor_url;
          signed = doc.contractor_signed;
        }
        const contract_info = contractInfo(dialog.user.profile, doc);
        contract_info.edit = dialog.context.contract.edit;
        contract_info.url = url;
        dialog.addInfo(contract_info, true);
        if (contract_info.edit || signed) dialog.setHide();
      }
      dialog.setHide();
      dialog.reset('contract');
      break;
    case Topics.CONTRACT_CONFIRM:
      if (doc) {
        if (!dialog.message.ping && !dialog.message.command) {
          let answers = lang.contract.CONTRACT_NAME_ANSWERS.slice();
          if (doc.client_id === dialog.user.profile) {
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setPrompt', dialog.user.profile, `Confirming for client: ${JSON.stringify(doc)}`);
            if (doc.client_reviewed) {
              dialog.lastPrompt('contract', lang.contract.CONTRACT_WAITING( doc.contractor_name.split(' ')[0], doc.context), { clear: true});
              return;
            }
            else dialog.addPrompt(lang.contract.CONTRACT_NAME_PROMPT, true);
          } else {
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setPrompt', dialog.user.profile, `Confirming for contractor: ${JSON.stringify(doc)}`);
            if (doc.contractor_reviewed) {
              dialog.lastPrompt('contract', lang.contract.CONTRACT_WAITING( doc.client_name.split(' ')[0], doc.context), {clear: true});
              return;
            } else dialog.addPrompt(lang.contract.CONTRACT_NAME_PROMPT, true);
          }

          if (dialog.me) {
            const orgs = findTypeValues(dialog.me.tags, TagType.organization);
            answers = answers.concat(orgs);
            if (dialog.user.loaded_groups) {
              const group_names = Object.values(dialog.user.loaded_groups).map(g => g.name);
              const group_co_names = Object.values(dialog.user.loaded_groups).map(g => g.name);
              answers = answers.concat(group_names).concat(group_co_names);
            }
          }
          dialog.addAnswers(_.uniq(answers));
          dialog.setSticky();
          // dialog.noPing();
        } else if(dialog.context.contract.notified) {
          const contract_info = contractInfo(dialog.user.profile, doc);
          dialog.addInfo(contract_info, true);
          dialog.addPrompt(lang.contract.CONTRACT_PREP(doc.contractor_name.split(' ')[0], doc.context));
          dialog.setHide(10000);
          dialog.reset('contract');
        }
      }
      break;
    case Topics.CONTRACT_NOTIFY:
      if(dialog.context.contract.notified) {
        const contract_info = contractInfo(dialog.user.profile, doc);
        dialog.addInfo(contract_info, true);
        dialog.addPrompt(lang.contract.CONTRACT_PREP(doc.contractor_name.split(' ')[0], doc.context));
        dialog.setHide(10000);
        dialog.reset('contract');
      }
      break;
    case Topics.CONTRACT_CANCELED:
      if (!dialog.message.ping) {
        if (doc.client_id === dialog.user.profile) dialog.addPrompt(lang.contract.CONTRACT_CANCELED(doc.contractor_name.split(' ')[0]), true);
        else dialog.addPrompt(lang.contract.CONTRACT_CANCELED(doc.client_name.split(' ')[0]), true);
      }
      dialog.reset('contract')
      break;
    case Topics.CONTRACT_SIGN:
      if (!dialog.message.ping) dialog.addPrompt(lang.contract.CONTRACT_LOADING(true), true);
      break;
    case Topics.CONTRACT_SIGNED:
      if (doc.client_id === dialog.user.profile) {
        dialog.addPrompt(lang.contract.CONTRACT_COMPLETED(doc.contractor_name.split(' ')[0]), true);
        if (dialog.context.contract.project_contractor) {
          if (dialog.context.contract.project_contractor.ready) {
            dialog.addPrompt(lang.project.CLIENT_START_PROMPT(dialog.context.contract.project_ask_deposit, dialog.context.contract.project_amount, dialog.context.contract.project_service_fee));
          } else {
            dialog.addPrompt(lang.project.CLIENT_WAIT_READY(dialog.context.contract.project_contractor.nickName));
          }
        }
      } else {
        const client_name = dialog.context.contract.project_client ? 
          dialog.context.contract.project_client.nickName : doc.client_name.split(' ')[0];
        dialog.addPrompt(lang.contract.CONTRACT_COMPLETED(client_name), true);
        dialog.addPrompt(lang.project.SELECTED_CONTRACTOR_SETUP_STRIPE(client_name, dialog.user.hasAccount(AuthProviders.Stripe) || dialog.user.hasAccount(AuthProviders.Wise)));
      }
      dialog.addInfo(contractInfo(dialog.user.profile, doc));
      dialog.reset('contract');
      break;
    case Topics.CONTRACT_DECLINED:
      if (doc.client_id === dialog.user.profile) dialog.addPrompt(lang.contract.CONTRACT_DECLINED(doc.contractor_name.split(' ')[0]), true);
      else  dialog.addPrompt(lang.contract.CONTRACT_DECLINED(doc.client_name.split(' ')[0]), true);
      dialog.addInfo(contractInfo(dialog.user.profile, doc));
      dialog.reset('contract');
      break;
    case Topics.CONTRACT_ERROR:
      dialog.addPrompt(lang.contract.CONTRACT_SIGNING_ERROR(dialog.contracts.getSignUrl(doc.id, dialog.user.email)), true);
      dialog.addInfo(contractInfo(dialog.user.profile, doc));
      dialog.reset('contract');
      break;
    case Topics.CONTRACT_BAD_CMD:
      dialog.addPrompt(lang.contract.CONTRACT_INTERNAL_ERROR, true);
      dialog.reset('contract');
      break;
    case Topics.CONTRACT_NOT_READY:
      dialog.addPrompt(lang.contract.CONTRACT_NOT_READY, true);
      dialog.reset('contract');
      break;
    case Topics.CONTRACT_LOADING:
      // if (!dialog.context.contract || dialog.context.contract.doc) dialog.reset('contract');
      // else 
      // dialog.quickPing();
      break;
    case Topics.CONTRACT_SAVING:
      if (!dialog.context.contract || dialog.context.contract.saved) dialog.reset('contract');
      // else dialog.quickPing();
      break;
    case Topics.CONTRACT_DONE:
      dialog.reset('contract');
      break;
    case Topics.CONTRACT_MISMATCH:
      dialog.addPrompt(lang.contract.CONTRACT_MISMATCH, true);
      dialog.reset('contract');
      break;
    case Topics.CONTRACT_MISSING:
      dialog.addPrompt(lang.contract.CONTRACT_INTERNAL_ERROR, true);
      dialog.reset('contract');
      break;
  }

}
export default {
  name: CONTRACT_NAME,
  description: CONTRACT_DESC,
  examples: lang.contract.CONTRACT_EG,
  reserved: CONTRACT_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: () => null,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
