import _ from 'lodash';
import lang from '../lang';

import { SERVICE_FEE } from '../routes/gopay';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import wise from '../sources/wise_controller';

import { ActionType, InternalError } from '../types/globals';
import { Location } from '../types/items';
import { Plugin } from '../types/plugins';
import { AuthProviders } from '../types/shared';

import logging from '../utils/logging';
import parsers from '../utils/parsers';

const WISE_NAME = 'Wise';
const WISE_DESC = 'Connect to Wise for payments';
const LOG_NAME = 'plugins.wise';

const topics = [
  TOPIC('WISE_CONNECT'),
  TOPIC('WISE_DISCONNECT'),
  TOPIC('WISE_CANNOT_DISCONNECT'),
  TOPIC('WISE_CURRENCY'),
  TOPIC('WISE_COUNTRY'),
  TOPIC('WISE_ACCOUNT'),
  TOPIC('WISE_REQUIREMENTS'),
  TOPIC('WISE_CONFIRM'),
  TOPIC('WISE_CONNECTING'),
  TOPIC('WISE_CONNECTED'),
  TOPIC('WISE_ERROR'),
];

function keywordMatch(actions, message) {
  return parsers.checkKeyword(message, lang.wise.CONNECT_WISE);
}

function setAction(dialog: Dialog, message, raw_message) {
  if (parsers.checkKeyword(message, lang.wise.CONNECT_WISE, true)) {
    dialog.setTopic(Topics.WISE_CONNECT);
  }

  return isActive(dialog);
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function wiseConnect(dialog: Dialog) {
  if (!dialog.context.wise) dialog.context.wise = {skipped: []};
  else if (!dialog.context.wise.location) {
    dialog.setTopic(Topics.WISE_COUNTRY);
    return;
  }
}

function wiseCurrency(dialog: Dialog) {
  if (!dialog.context.wise.currency) dialog.context.wise.currency = parsers.findCurrency(dialog.message.message);
  if (dialog.context.wise.currency) {
    dialog.setTopic(Topics.WISE_ACCOUNT);
  }
}

function wiseCountry(dialog: Dialog) {
  const location = dialog.actionValues(ActionType.LOCATION) as Partial<Location>[];
  if (location && location.length) {
    dialog.context.wise.location = location[0];
    dialog.setTopic(Topics.WISE_CURRENCY);
  }
}

function wiseAccount(dialog: Dialog) {
  if (!dialog.context.wise.accountHolderName) dialog.context.wise.accountHolderName = dialog.message.message;
  if (dialog.context.wise.accountHolderName) {
    dialog.setTopic(Topics.WISE_REQUIREMENTS);
  }
}

function wiseRequirements(dialog: Dialog) {
  if (!dialog.context.wise) {
    dialog.reset('wise');
    return;
  }

  if (!dialog.context.wise.details) dialog.context.wise.details = {address:{country:dialog.context.wise.location.code}};
  if (!dialog.context.wise.requirements) {
    if (!dialog.context.wise.loading) {
      dialog.context.wise.loading = true;
      dialog.runAsync('wise', async () => {
        if (dialog.context.wise) {
          const client = wise();
          if (!dialog.context.wise.quoteId) {
            const quote = await client.createQuote({sourceCurrency: 'USD', targetCurrency: dialog.context.wise.currency, sourceAmount: 100 });
            if (!quote || quote.error || quote.errors) {
              dialog.context.wise.error = quote.errors ? quote : {errors:[quote.error ? quote.error : {message: 'Unknown error', path: 'currency'}]};
              dialog.setTopic(Topics.WISE_ERROR);
            } else {
              dialog.context.wise.quoteId = quote.id;
            }
          }

          if (dialog.context.wise && dialog.context.wise.quoteId) {
            const req = dialog.context.wise.account_type ?
              await client.getAccountRequirementsForQuote({ quoteId: dialog.context.wise.quoteId, type: dialog.context.wise.account_type, details: dialog.context.wise.details }) : 
              await client.getAccountRequirementsForQuote({ quoteId: dialog.context.wise.quoteId });
            if (req.error || req.errors) {
              logging.errorFP(LOG_NAME, 'wiseRequirements', dialog.user.profile, 'Error getting requirements', req.error);
              dialog.context.wise.error = req.errors ? req : {errors:[req.error ? req.error : { message: 'Unkown error', path: 'requirements'} ]};
              dialog.setTopic(Topics.WISE_ERROR);
            }
            else dialog.context.wise.requirements = req;
          }
        }
      });
    }
    return;
  }

  if (!dialog.context.wise.account_type) {
    if (!dialog.message.ping) {
      const message = dialog.message.message.toLowerCase();
      for (const req of dialog.context.wise.requirements) {
        if (parsers.checkKeyword(message, [req.title.toLowerCase(), req.title.toLowerCase().split(' ')[0]])) {
          dialog.context.wise.account_type = req.type;
          break;
        }
      }
    }
    return;
  }

  if (dialog.context.wise.field) {
    const req = dialog.context.wise.requirements.find(r => r.type === dialog.context.wise.account_type);
    if (req) {
      const field = req.fields.find(f => f.group && f.group.length && f.group[0].key === dialog.context.wise.field);
      if (field) {
        const group = field.group[0];
        const lmessage = dialog.message.message.toLowerCase();

        let did_set = false;
        if (['select', 'radio'].includes(group.type) && group.valuesAllowed && group.valuesAllowed.length) {
          for (const v of group.valuesAllowed) {
            if (v.key && v.key.length) {
              if (parsers.checkFullWord(lmessage, [v.key.toLowerCase()].concat(v.name.toLowerCase().split(' ')))) {
                _.set(dialog.context.wise.details, group.key, v.key);
                did_set = true;
                break;
              }
            }
          }
        } else if(group.validationRegexp) {
          if (parsers.checkKeyword(dialog.message.message.toLowerCase(), lang.wise.SKIP_KWD)) {
            dialog.context.wise.skipped.push(group.key);
            did_set = true;
          } else if ( dialog.message.message.match(new RegExp(group.validationRegexp))) {
            _.set(dialog.context.wise.details, group.key, dialog.message.message);
            did_set = true;
          }
        } else if (group.required) {
          _.set(dialog.context.wise.details, group.key, dialog.message.message);
          did_set = true;
        } else {
          if (dialog.message.message.length && !parsers.checkKeyword(dialog.message.message.toLowerCase(), lang.wise.SKIP_KWD)) {
            _.set(dialog.context.wise.details, group.key, dialog.message.message);
          } else dialog.context.wise.skipped.push(group.key);
          did_set = true;
        }

        dialog.context.wise.field = null;

        if (did_set && group.refreshRequirementsOnChange) {
          dialog.context.wise.loading = false;
          dialog.context.wise.requirements = null;
          // dialog.internalPing();
        }

        return;
      }
    }
  }

  // dialog.setTopic(Topics.WISE_ERROR);
}

function wiseConnecting(dialog: Dialog) {
  if (!dialog.context.wise.connecting) {
    dialog.context.wise.connecting = true;
    dialog.runAsync('wise', async () => {
      const client = wise();
      const account_info = {
        type: dialog.context.wise.account_type,
        accountHolderName: dialog.context.wise.accountHolderName,
        currency: dialog.context.wise.currency,
        profile: dialog.context.wise.profile,
        details: dialog.context.wise.details,
        ownedByCustomer: false,
      };
      const account = await client.createRecipientAccount(account_info);
      if (!account || account.error || account.errors) {
        let error = { code: 500, message: 'Unknown error creating account'};
        if (account) {
          if (account.error) error = account.error;
          else if (account.errors) error = account.errors[0];
        }
        logging.errorFP(LOG_NAME, 'wiseConnecting', dialog.user.profile, 'Error creating account', new InternalError(500, error.message, account_info));
        dialog.context.wise.error = account ? account : {errors:[error]};
        dialog.setTopic(Topics.WISE_ERROR);
      } else {
        if (account.details) delete account.details;
        dialog.user.updateAccount(AuthProviders.Wise, account.id, account);
        await dialog.projects.candidateReady(true, SERVICE_FEE);
        dialog.context.wise.connected = true;
      }
    });
  } 

  if (dialog.context.wise.connected) {
    dialog.setTopic(Topics.WISE_CONNECTED);
    return;
  } else if (dialog.context.wise.error) {
    dialog.setTopic(Topics.WISE_ERROR);
    return;
  }
}

function wiseDisconnect(dialog: Dialog) {
  if (dialog.message.command) return;
  if (!dialog.context.wise) dialog.context.wise = { skipped: []};
  const bool = dialog.actionValues(ActionType.BOOL);
  if (bool && bool.length) {
    if (bool[0] && !dialog.context.wise.deleting) {
      dialog.context.wise.deleting = true;
      dialog.runAsync('wise', async () => {
        dialog.user.deleteAccount(AuthProviders.Wise);
        await dialog.projects.candidateReady(false);
        dialog.context.wise.deleted = true;
      });
    }
  } else if(dialog.context.wise.deleting) {
    // NOOP
  } else {
    // check for projects as contractor
    if (dialog.cache.projects) {
      for (const project of Object.values(dialog.cache.projects)) {
        if (project.contractor && project.contractor.askfora_id === dialog.user.profile) {
          if (project.escrow && !project.payment) {
            dialog.setTopic(Topics.WISE_CANNOT_DISCONNECT);
          }
        }
      }
    }
  }
}

function runAction(dialog: Dialog) {
  const wise_client = wise();
  const bool = dialog.actionValues(ActionType.BOOL);

  switch(dialog.topic) {
    case Topics.WISE_CONNECT: return wiseConnect(dialog);
    case Topics.WISE_CURRENCY: return wiseCurrency(dialog);
    case Topics.WISE_COUNTRY: return wiseCountry(dialog);
    case Topics.WISE_ACCOUNT: return wiseAccount(dialog);
    case Topics.WISE_REQUIREMENTS: return wiseRequirements(dialog);
    case Topics.WISE_CONFIRM: 
      if (bool && bool.length) {
        if (bool[0]) dialog.setTopic(Topics.WISE_CONNECTING);
        else {
          dialog.context.wise = {skipped: []};
          dialog.setTopic(Topics.WISE_COUNTRY);
        }
      }
      break;
    case Topics.WISE_CONNECTING: return wiseConnecting(dialog);
    // case Topics.WISE_CONNECTED: return wiseConnected(dialog);
    // case Topics.WISE_ERROR: return wiseError(dialog);
    case Topics.WISE_DISCONNECT: return wiseDisconnect(dialog);
  }
}

function setPrompt(dialog: Dialog) {
  if (!dialog.context.wise) {
    dialog.reset();
    return;
  }

  switch(dialog.topic) {
    case Topics.WISE_CONNECT:
      dialog.addPrompt(lang.wise.WISE_PROMPT, true);
      dialog.setTopic(Topics.WISE_COUNTRY);
    case Topics.WISE_COUNTRY:
      dialog.addPrompt(lang.wise.WISE_COUNTRY);
      if (dialog.user.location) dialog.addAnswers(dialog.user.location.name);
      // dialog.clearPing();
      dialog.setSticky();
      break;
    case Topics.WISE_CURRENCY:
      dialog.addPrompt(lang.wise.WISE_CURRENCY, true);
      dialog.addAnswers(dialog.context.wise.location.currency);
      dialog.setSticky();
      break;
    case Topics.WISE_ACCOUNT:
      dialog.addPrompt(lang.wise.WISE_CONFIRM('Account Name'), true);
      dialog.addAnswers(dialog.me.displayName);
      dialog.setSticky();
      break;
    case Topics.WISE_REQUIREMENTS:
      if(dialog.context.wise.requirements && dialog.context.wise.details) {
        if (!dialog.context.wise.account_type) {
          dialog.addPrompt(lang.wise.WISE_ACCOUNT_TYPE, true);
          dialog.addAnswers(dialog.context.wise.requirements.map(r => r.title));
          dialog.setSticky();
          return;
        }
        else { 
          const req = dialog.context.wise.requirements.find(r => r.type === dialog.context.wise.account_type);
          if (req.fields) {
            for (const field of req.fields) {
              if (field.group && field.group.length) {
                const group = field.group[0];
                if (!_.get(dialog.context.wise.details, group.key) && !dialog.context.wise.skipped.includes(group.key)) {
                  dialog.context.wise.field = group.key;
                  dialog.addPrompt(lang.wise.WISE_FIELD_PROMPT(field.name), true);
                  if (['select', 'radio'].includes(group.type) && group.valuesAllowed) dialog.addAnswers(group.valuesAllowed.filter(v => v.key && v.key.length).map(v => v.name));
                  if (group.example && group.example.length) dialog.addHint(`e.g. ${group.example}`);
                  if (!group.required) dialog.addAnswers(lang.wise.SKIP);
                  dialog.setSticky();
                  return;
                }
              }
            }
          }
        }
      } else {
        // dialog.quickPing();
        break;
      }
      // no break
    case Topics.WISE_CONFIRM:
      dialog.setTopic(Topics.WISE_CONFIRM);
      dialog.addPrompt(lang.wise.WISE_FULL_CONFIRM(dialog.context.wise), true);
      dialog.addAnswers(['Yes', 'No, start over']);
      dialog.setSticky();
      break;
    case Topics.WISE_CONNECTING:
      if (!dialog.context.wise.connecting) dialog.addPrompt(lang.wise.WISE_CONNECTING, true);
      // dialog.quickPing();
      break;
    case Topics.WISE_CONNECTED:
      dialog.newDialog();
      dialog.lastPrompt('wise', lang.wise.WISE_CONNECTED, { clear: true });
      break;
    case Topics.WISE_ERROR:
      dialog.lastPrompt('wise', lang.wise.WISE_ERROR(dialog.context.wise.error), { clear: true });
      break;
    case Topics.WISE_DISCONNECT:
      if (!dialog.context.wise || !dialog.context.wise.deleting) {
        dialog.addPrompt(lang.wise.CONFIRM_DISCONNECT, true);
        dialog.setSticky();
        // dialog.clearPing();
      } else if(dialog.context.wise.deleted) {
        dialog.lastPrompt('wise', lang.wise.DISCONNECTED, {clear: true});
      }
      break;
    case Topics.WISE_CANNOT_DISCONNECT:
      if (!dialog.message.ping) dialog.lastPrompt('wise', lang.wise.CANNOT_DISCONNECT, {clear:true});
      break;

  }
}

export default {
  name: WISE_NAME,
  description: WISE_DESC,
  examples: [],
  reserved: [],
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: a => { return null; },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default);