import _ from 'lodash';

import lang from '../lang';

import { Action, ActionType } from '../types/globals';
import { Event, GlobalType, IEntity, Message, Person, SavedFilter, Task, dehydratePerson } from '../types/items';
import { Plugin, WhatsNextPluginState } from '../types/plugins';
import { AuthLevel, EntityType, PersonInfo, Relation, TagType, TimeVal, Uid, UnresolvedPerson } from '../types/shared';

import { DAYS, HOURS, MIDNIGHT, MINUTES, activeEvent, allDay, compareTimeVals, finishedEvent, future, sameDay, sortTasks, startingEvent, upcomingEvent } from '../utils/datetime';
import { checkFilter } from '../utils/filter';
import { localeDate } from '../utils/format';
import { arrayContains, flatten, randomPhrase, } from '../utils/funcs';
import { dayInfo, promptInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import { lookupSkill } from '../skills';

import Dialog, { TOPIC, Topics } from '../session/dialog';

const LOG_NAME = 'plugins.whats_next';
const WHAT_NAME = "What's Next";
const WHAT_DESC = 'Tell the user about what to do next.';

const topics = [TOPIC('WHATS_NEXT'), TOPIC('WHAT_ELSE'), TOPIC('SUGGEST')];

function keywordMatch(actions: Action[], message: string, _raw_message: string) {
  if (parsers.checkKeyword(message, lang.whats_next.WHAT_KWD, true)) return true; // &&
  // (parsers.checkKeyword(message, lang.whats_next.WHATS_NEXT) || Dialog.getActionValues(actions, ActionType.ENTITY_TYPE).length)) return true;
  if (parsers.checkKeyword(message, lang.whats_next.INQUERY_START, true) && (parsers.checkKeyword(message, lang.whats_next.INQUERY_KEY) || Dialog.getActionValues(actions, ActionType.ENTITY_TYPE).length)) {
    const act_people = Dialog.getActionValues(actions, ActionType.ENTITY).filter(e => e.people && e.people.length);
    return act_people.length === 0;
  }
  if (parsers.checkKeyword(message, lang.whats_next.WHAT_ELSE, true) && !parsers.checkKeyword(message, lang.whats_next.NOT_ELSE)) return true;

  const act_time = Dialog.getActionValues(actions, ActionType.DATE);
  if (act_time && act_time.length) {
    const strip_msg = message.replace(/[.?!]$/, '');
    const ltime = act_time[0].text.toLowerCase();
    if (strip_msg === ltime) return true;
    // || (parts.length > 1 && parsers.checkKeyword(message, lang.whats_next.WHAT_KWD, true) &&
    // (parts[1] === ltime || (parts.length > 2 && parts[2] === ltime)))) return true;
  }

  return false;
}

function setAction(dialog: Dialog, message, raw_message) {
  // TODO check for skills, e.g. What designers do I know?
  if (parsers.checkKeyword(message, lang.whats_next.DUE)) dialog.addAction(ActionType.CMD, 'due');
  if (isActive(dialog)) {
    if (dialog.checkTopic(Topics.SUGGEST)) return true;

    if (parsers.checkKeyword(message, lang.whats_next.START_OVER, true)) {
      dialog.setTopic(Topics.WHATS_NEXT);
      return true;
    }

    if (dialog.message.ping ||
      (parsers.checkKeyword(message, lang.whats_next.WHAT_ELSE, true) && !parsers.checkKeyword(message, lang.whats_next.NOT_ELSE))) {
      dialog.setTopic(Topics.WHAT_ELSE);
      return true;
    }

    const kwd = keywordMatch(dialog.actions, message, raw_message);
    if (!kwd) {
      const curr_dialog = dialog.currentDialog();
      if (!curr_dialog || !curr_dialog.answers || !parsers.checkKeyword(message, curr_dialog.answers.map(a => a.toLowerCase()), true)) {
        dialog.clearContext('whats_next');
        dialog.newDialog();
      }
    }
    return kwd;
  } else if(parsers.checkKeyword(message, lang.whats_next.SUGGEST_KWD)) {
    dialog.setTopic(Topics.SUGGEST);
    return true;
  } else {
    const entity = dialog.actionValues(ActionType.ENTITY).filter(e => e.people && e.people.length).map(e => e.name);
    if (entity && entity.length) {
      for (const e of entity) {
        if (parsers.checkKeyword(raw_message, e)) {
          if(!parsers.checkKeyword(message, lang.whats_next.WHAT_KWD)) return false;
          const filters = dialog.filters && dialog.filters.filter(f =>
            f.conditions.filter(c =>
              c.att as any as ActionType === ActionType.ENTITY &&
              c.value && c.value.length && (c.value[0] as any as UnresolvedPerson).name === e)
            .length)
          filters.forEach(f => dialog.removeFilter(f.id));
        }
      }
    }

    dialog.setTopic(Topics.WHATS_NEXT);
    return true;
  }
  // return false;
}

function isActive(dialog) {
  return dialog.checkTopic([Topics.WHATS_NEXT, Topics.WHAT_ELSE, Topics.FULL_DAY, Topics.SUGGEST]);
}

// Figure out what event, task, or email should be worked on
function runAction(dialog: Dialog) {
  const now = new Date();
  let next_types: (EntityType|GlobalType)[] = [];
  let event_time = null;
  let active_time = null;
  let full_day = false;
  let last_date = now;

  const check_last = dialog.checkTopic(Topics.WHAT_ELSE);

  const due = dialog.actionValues(ActionType.CMD).includes('due');

  let user_time = null;
  const act_time = dialog.actionValues(ActionType.DATE);
  if (act_time !== null && act_time.length > 0) user_time = act_time[0];

  let type_names = [
    ...lang.find.FIND_KWD,
    ...lang.find.FIND_WORDS,
    ...lang.whats_next.WHAT_KWD,
    ...lang.whats_next.WHAT_ELSE,
    ...lang.whats_next.INQUERY_KEY,
    ...lang.whats_next.WITH,
    ...lang.whats_next.NOT_ELSE,
    ...lang.whats_next.DUE,
    ...lang.whats_next.INQUERY_START,
  ];
  const user_types = dialog.getActions(ActionType.ENTITY_TYPE);
  if (user_types && user_types.length) {
    type_names = [...type_names, ...user_types.map(t => t.context).filter(c => c)];
    next_types = user_types.map(t => t.value as EntityType|GlobalType);
    /*for (const ut of user_types) {
      switch(ut) {
        case EntityType.Event: type_names = type_names.concat(KeywordTypes.EVENT); break;
        case EntityType.Task: type_names = type_names.concat(KeywordTypes.TASK); break;
        case EntityType.Message: type_names = type_names.concat(KeywordTypes.MESSAGE); break;
      }
    }*/
  }

  const skills = dialog.actionValues(ActionType.SKILL);

  const filters = dialog.filters; // peopleUtils.makeFilters(dialog.actions);

  // on a ping, check for important information
  if (dialog.message.ping) {
    const curr_dialog = dialog.currentDialog();
    if (curr_dialog) last_date = new Date(curr_dialog.date);

    // report on the day if it's been quiet for 4 hours
    if (HOURS(last_date, 8) <= now) {
      full_day = true;
      // event time is midnight of the offset data, offset from GMT
      // e.g. if it's 2/20 6pm GMT -8, we want 2/20 12am GMT -8 not 2/21 12am Z
      event_time = MIDNIGHT(MINUTES(now, -1 * dialog.user.offset), dialog.user.offset);
    }
  } else {
    // use the time frame in actions if there is one
    if (user_time) {
      event_time = user_time.start;
      if (act_time[0].day) {
        full_day = true;
        dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Event);

        // adjust event_time to start at midnight (offset) for today (offset) local time
        event_time = MIDNIGHT(MINUTES(event_time, -1 * dialog.user.offset), dialog.user.offset);
        // event_time = MIDNIGHT(event_time, dialog.user.offset);
      }
    }

    // use right now for active time if event_time is today, otherwise use event_time
    active_time = now;
    if (event_time && !(full_day && sameDay(event_time, now, dialog.user.offset))) active_time = event_time;

    // if check_last, just skip to the next reply from the existing dialog uniless the entity or time types have changed
    if (check_last && dialog.context.whats_next) {
      if ((next_types.length === 0 || arrayContains(dialog.context.whats_next.next_types, next_types)) && (event_time === null || dialog.context.whats_next.event_time === event_time)) {
        dialog.context.whats_next.next_types = next_types;
        dialog.addReplies(nextReply(dialog.context.whats_next.next, next_types));

        const curr_dialog = dialog.currentDialog();
        const count = curr_dialog ? curr_dialog.replies.length : 0;
        if (count === 0 && (!dialog.filters || !dialog.filters.length)) dialog.context.whats_next.quip = randomPhrase(lang.whats_next.NEXT_WITTY);
        else dialog.context.whats_next.quip = null;
        return count > 0;
      }
    }
  }

  // default to all item types
  if (next_types.length === 0) next_types = [EntityType.Event, EntityType.Task, EntityType.Message];

  let events = next_types.includes(EntityType.Event) ? dialog.cache.events : [];
  let messages = next_types.includes(EntityType.Message) && dialog.isAuthenticated(AuthLevel.Email) ? dialog.cache.messages : [];
  let tasks = next_types.includes(EntityType.Task) ?
    sortTasks(Object.values(dialog.cache.tasks).filter(t => {
      const tdue = t.due ? new Date(t.due) : null;
      const ntdue = tdue ? tdue.getTime() : 0;
      const tcompleted = t.completed ? new Date(t.completed).getTime() : null;
      return (
          (due && (tdue && !isNaN(ntdue) && ntdue > 0)) ||
          (!due && (!tdue || (tdue && !isNaN(ntdue) && ntdue > 0 && DAYS(tdue, -1) > now )))
        ) && (!t.completed || isNaN(tcompleted) || tcompleted === 0)
    })) : [];

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Next types ${JSON.stringify(next_types)} ${events.length}/${dialog.cache.events.length} events ${messages.length}/${dialog.cache.messages.length} messages ${tasks.length}/${Object.keys(dialog.cache.tasks).length} tasks`);

  const hour_offset = 0 - dialog.user.offset / 60;
  if (event_time === null) event_time = new Date();

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `What's next at ${localeDate(event_time, dialog.message.locale, dialog.message.timeZone)} GMT ${hour_offset}`);

  const ids = flatten(dialog
    .actionValues(ActionType.ENTITY)
    .filter(e => e.people && !type_names.includes(e.name) && (!user_time || !user_time.text.includes(e.name)))
    .map(p => p.people), 'id');

  if (ids.length) {
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `Limiting to events for ${JSON.stringify(ids)}`);
    events = events.filter(e => e.people && e.people.map(p => p.id).some(i => ids.includes(i)));
    tasks = tasks.filter(t => t.people && t.people.map(p => p.id).some(i => ids.includes(i)));
    messages = messages.filter(m => ids.includes(m.sender.id) || (m.recipient && m.recipient.map(p => p.id).some(i => ids.includes(i))));
  }

  let scope: 'day' | 'week' | 'month' | 'year' = 'day';

  if (filters && filters.length) {
    let w_events: Event[] = [];
    let w_tasks: Task[] = [];
    let w_messages: Message[] = [];

    let no_events = false;
    let no_tasks = false;
    let no_messages = false;

    for (const filter of filters) {
      for (const condition of filter.conditions.filter(c => [TagType.event, EntityType.Event, EntityType.Task, EntityType.Message].includes(c.att))) {
        if (condition.att === TagType.event && condition.value && condition.value.length) {
          if (scope === 'day') {
            if ((condition.value[0] as TimeVal).week) scope = 'week';
            if ((condition.value[0] as TimeVal).month) scope = 'month';
            if ((condition.value[0] as TimeVal).year) scope = 'year';
          } else if(scope === 'week') {
            if ((condition.value[0] as TimeVal).month) scope = 'month';
            if ((condition.value[0] as TimeVal).year) scope = 'year';
          } else if(scope === 'month') {
            if ((condition.value[0] as TimeVal).year) scope = 'year';
          }
        }

        switch(condition.rel) {
          case Relation['=']:
          case Relation['$']:
            w_events = w_events.concat(events.filter(event =>
              condition.att === EntityType.Event || (condition.att === TagType.event && (
                compareTimeVals({start: new Date(event.start), text: undefined}, condition.value[0] as TimeVal, dialog.cache.user.offset) ||
                compareTimeVals({start: new Date(event.end), text: undefined}, condition.value[0] as TimeVal, dialog.cache.user.offset)
              ))
            ));
            w_tasks = w_tasks.concat(tasks.filter(task =>
              condition.att === EntityType.Task || (condition.att === TagType.event && (
                compareTimeVals({start: new Date(task.created), text: undefined}, condition.value[0] as TimeVal,  dialog.cache.user.offset) ||
                compareTimeVals({start: new Date(task.due), text: undefined}, condition.value[0] as TimeVal,  dialog.cache.user.offset)
              ))
            ));
            w_messages = w_messages.concat(messages.filter(message =>
              condition.att === EntityType.Message || (condition.att === TagType.event && (
                compareTimeVals({start: new Date(message.received), text: undefined}, condition.value[0] as TimeVal,  dialog.cache.user.offset)
              ))
            ));
            break;
          case Relation['~']:
          case Relation['<']:
            if (condition.att === TagType.event) {
              w_events = w_events.concat(events.filter(event => new Date(event.start) < (condition.value[0] as TimeVal).start));
              w_tasks = w_tasks.concat(tasks.filter(task => new Date(task.created) < (condition.value[0] as TimeVal).start));
              w_messages = w_messages.concat(messages.filter(message => new Date(message.received) < (condition.value[0] as TimeVal).start));
            }
            break;
          case Relation['!']:
          case Relation['>']:
            if (condition.att === TagType.event) {
              w_events = w_events.concat(events.filter(event => new Date(event.end) > (condition.value[0] as TimeVal).start));
              w_tasks = w_tasks.concat(tasks.filter(task => new Date(task.created) > (condition.value[0] as TimeVal).start));
              w_messages = w_messages.concat(messages.filter(message => new Date(message.received) > (condition.value[0] as TimeVal).start));
            }
            break;
          case Relation['<>']:

            if (condition.att === EntityType.Event) no_events = true;
            else if(!no_events && condition.att === TagType.event) w_events = w_events.concat(events.filter(event => !compareTimeVals({start: new Date(event.start), text: undefined}, condition.value[0] as TimeVal, dialog.cache.user.offset)));

            if (condition.att === EntityType.Task) no_tasks = true;
            else if(!no_tasks && condition.att === TagType.event) {
              w_tasks = w_tasks.concat(tasks.filter(task =>
                !compareTimeVals({start: new Date(task.created), text: undefined}, condition.value[0] as TimeVal,  dialog.cache.user.offset) ||
                !compareTimeVals({start: new Date(task.due), text: undefined}, condition.value[0] as TimeVal,  dialog.cache.user.offset)
              ));
            }

            if (condition.att === EntityType.Message) no_messages = true;
            else if(!no_tasks && condition.att === TagType.event) {
              w_messages = w_messages.concat(messages.filter(message => !compareTimeVals({start: new Date(message.received), text: undefined}, condition.value[0] as TimeVal,  dialog.cache.user.offset)));
            }
            break;
        }
      }
      if (no_events) w_events = [];
      if (no_tasks) w_tasks = [];
      if (no_messages) w_messages = [];
    }

    events = _.uniqBy(w_events, 'id');
    tasks = _.uniqBy(w_tasks, 'id');
    messages = _.uniqBy(w_messages, 'id');
  }

  if (!dialog.context.whats_next) {
    // save context, note that event_time is null if not user specified
    dialog.context.whats_next = dialog.activitySummary(next_types, events, tasks, messages, event_time, active_time, full_day, scope) as WhatsNextPluginState;
    dialog.context.whats_next.filters = filters.map(f => new SavedFilter(f));

    // on automated pings only report full day or starting events
    if (dialog.message.ping && dialog.context.whats_next.next.length) {
      if (!full_day && !startingEvent(dialog.context.whats_next.next[0] as Event, event_time)) {
        // every couple hours, add a quip
        dialog.context.whats_next.next = [];
      }
    }

    const filter_set = [...Object.values(ActionType), TagType.event, EntityType.Event, EntityType.Task, EntityType.Person];

    if (skills?.length ||
        dialog.context.whats_next?.filters?.filter(f =>
          f.conditions.filter(c => !filter_set.includes(c.att)).length).length) {

      if (!dialog.isProcessing()) {
        logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Processing filter ${JSON.stringify(dialog.context.whats_next.filters)} and searching skills ${skills}`);
        dialog.runAsync('whats_next', async () => {
          let skill_people: Partial<Person>[] = [];
          if (skills && skills.length ) {
            const search_skills = [];
            const found_skills = await lookupSkill(skills, dialog.user.locale);

            for (const skill of found_skills) {
              search_skills.push(skill.skill);
              if (skill.synonyms) for (const synonym of skill.synonyms) search_skills.push(synonym);
              if (skill.initialisms) for (const initials of skill.initialisms) search_skills.push(initials);
            }

            const group_search_ids = dialog.user.search_groups;
            const mandatory_tags = dialog.user.mandatory_tags;

            skill_people = await dialog.search.searchSkills([...skills, ...search_skills], {
              network: 'include', require_email: false, mandatory_tags, group_search_ids});

            for (let i = 0; i < skill_people.length; i++) {
              const person = skill_people[i];
              if (!person.id) skill_people[i] = await dialog.people.temp(person, true);
            }

            skill_people = skill_people.filter(p => !p.self && (!filters || !filters.length || checkFilter(p, filters, dialog.cache, null, [TagType.skill]))).map(p => dehydratePerson(p, skills));
          }

          if (dialog.context.whats_next) {
            const event_people = (await dialog.people.byId(dialog.context.whats_next.event_people_ids)).filter(p => checkFilter(p, dialog.context.whats_next.filters, dialog.cache));
            const people = (await dialog.people.byId(dialog.context.whats_next.people_ids)).filter(p => checkFilter(p, dialog.context.whats_next.filters, dialog.cache));

            dialog.context.whats_next.event_people_ids = event_people.map(p => p.id);
            dialog.context.whats_next.people_ids = people.map(p => p.id);

            dialog.context.whats_next.next = dialog.context.whats_next.next.concat(skill_people);
          }
        });
      }
    } else if((next_types.includes(GlobalType.Skill) || dialog.checkTopic(Topics.SUGGEST) && !dialog.isProcessing())) {
      logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Looking for recent skills`);
      dialog.runAsync('whats_next', async () => {
        const people_events = dialog.cache.events.filter(e =>
            e.people?.filter(p => !p.self && p.id !== dialog.me.id).length
          ).sort((a,b) => new Date(b.start).getTime() - new Date(a.start).getTime()).slice(0,10);
        let people_messages = dialog.isAuthenticated(AuthLevel.Email) ? dialog.cache.messages.slice(0,10) : [];
        let people_tasks = Object.values(dialog.cache.tasks).filter(t =>
          t.people?.filter(p => !p.self && p.id !== dialog.me.id).length
        ).sort((a,b) => new Date(b.created).getTime() - new Date(a.created).getTime()).slice(0,10);

        const people = _.uniqBy([...flatten(people_events.map(e => e.people)),
            ...flatten(people_tasks.map(t => t.people)),
            ...flatten(people_messages.map(m => [m.sender, ...m.recipient]))].filter(p => !p.self && p.id !== dialog.me.id)
            , 'id').slice(0,10);
        // const people_ids = people.map(p => p.vanity ? `profile/${p.vanity}` : p.id);
        if (people.length) {
          const pmap = await dialog.skills.peopleCategoryMap(people);

          if (dialog.checkTopic(Topics.SUGGEST)) {
            if (dialog.context.whats_next) {
              const matches:{score: number, skill: string, skills: string[]}[] = _.sortBy(
                Object.keys(pmap).map(pid => {
                  const cat = pmap[pid].sort((a,b) => b.score - a.score)[0];
                  return {
                    score: cat.score,
                    skill: cat.label,
                    skills: cat.skills,
                  }
                })
                , 'score', 'desc').slice(0,3);

              const people_cats = await Promise.all(matches.map(async cat => {
                const people = await dialog.search.searchSkills(cat.skills, {network: 'include', group_search_ids: dialog.group_host ? [dialog.group_host.id] : undefined});
                return {
                  score: cat.score,
                  skill: cat.skill,
                  people,
                }
              }));

              const suggest_fit = people_cats.sort((a,b) => b.score - a.score);
              const suggest_match: {skill: string, displayName: string, pid: Uid}[] = [];
              const has_ids: Uid[] = [];
              const loaded_people: Partial<Person>[] = [];

              for(const sf of suggest_fit) {
                for(const p of sf.people) {
                  if (!has_ids.includes(p.id)) {
                    has_ids.push(p.id);
                    loaded_people.push(p);
                    suggest_match.push({skill: sf.skill, displayName: p.displayName, pid: p.id});
                    break;
                  }
                }
              }

              dialog.context.whats_next.people_ids = has_ids;
              dialog.context.whats_next.people = loaded_people;

              const people_info_list = suggest_match.map(sm =>
                `[${sm.displayName}](${sm.pid}) about ${sm.skill}`
              );

              dialog.context.whats_next.quip = people_info_list.length ?
                lang.whats_next.SUGGEST_PEOPLE(people_info_list) :
                lang.whats_next.SUGGEST_NO_SUGGESTIONS;
            }
          } else {
            const suggested_skills = _.uniqBy(Object.values(pmap).reduce((a,b) => a.concat(b), []), 'id').map(s => s.label).slice(0,3);
            logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Suggested skills ${suggested_skills} from ${people.length} people`);
            if (dialog.context.whats_next) dialog.context.whats_next.quip = lang.whats_next.LEARN_SKILLS(suggested_skills);
          }
        } else {
          if (dialog.checkTopic(Topics.SUGGEST)) dialog.context.whats_next.quip = lang.whats_next.SUGGEST_NO_SUGGESTIONS;
          else {
            const skill_cats = await dialog.skills.peopleCategories([dialog.me]);
            const suggested_skills = skill_cats.map(s => s.label).slice(0,3);
            logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Suggested skills ${suggested_skills}`);
            if (dialog.context.whats_next) dialog.context.whats_next.quip = lang.whats_next.LEARN_SKILLS(suggested_skills);
          }
        }
      });
    }
  }

  if (dialog.isProcessing(false)) {
    // dialog.quickPing();
    return;
  }

  const curr_dialog = dialog.currentDialog();
  if (full_day) {
    dialog.addReplies(dialog.context.whats_next.next);
    if (dialog.context.whats_next.filters) dialog.appendReplies(dialog.context.whats_next.filters, curr_dialog ? curr_dialog.next_topic : null);
  } else {
    const next = nextReply(dialog.context.whats_next.next);
    if (next) {
      dialog.addReplies(next);
      if (dialog.context.whats_next.filters) dialog.appendReplies(dialog.context.whats_next.filters, curr_dialog ? curr_dialog.next_topic : null);
    }
  }

  if (dialog.context.whats_next.next.length) {
    if (full_day && future(dialog.context.whats_next.event_time, dialog.user.offset, true)) dialog.addPage();
    return true;
  } else {
    /*if (dialog.message.ping && HOURS(last_date, 2) <= now) {
      dialog.context.whats_next.quip = randomPhrase(lang.defaultanswers.SAY_HELLO(dialog.user.name, dialog.user.offset));
    }*/
  }

  return false;
}

function nextReply(replies: Partial<IEntity>[], filter_types: (EntityType|GlobalType)[] = []): IEntity {
  const now = new Date();
  for (let index = 0; index < replies.length; index++) {
    const reply = replies[index] as IEntity;
    if (!filter_types.length || filter_types.includes(reply.type)) {
      switch (reply.type) {
        case EntityType.Event:
          // check if event just finished
          if (finishedEvent(reply as Event, now)) {
            replies.splice(index, 1);
            return reply;
          }

          // check if an event is underway
          if (activeEvent(reply as Event, now)) {
            replies.splice(index, 1);
            return reply;
          }

          // check if an event is yet to start
          if (upcomingEvent(reply as Event, now)) {
            replies.splice(index, 1);
            return reply;
          }

          break;
        case EntityType.Task:
          replies.splice(index, 1);
          return reply;
        case EntityType.Message:
          replies.splice(index, 1);
          return reply;
        case EntityType.Person:
          replies.splice(index, 1);
          return reply;
      }
    }
  }

  return null;
}

// take the list of things to work on and format a response
function setPrompt(dialog) {
  if (dialog.message.message) dialog.logMessage();

  if (!dialog.isAuthenticated(AuthLevel.Organizer)) {
    if (!dialog.message.ping) {
      dialog.addPrompt(lang.init.REG_REQUIRED(dialog.user.provider), true);
      dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
    }
    dialog.reset('whats_next');
    return;
  }

  if (dialog.isProcessing(false)) return;

  // dialog.clearPing();

  const full_day = dialog.context.whats_next.full_day;
  const all_day = [];
  const now = new Date();
  const offset = dialog.user.offset;
  const locale = dialog.user.locale;
  const timeZone = dialog.user.timeZone;

  let curr_dialog = dialog.currentDialog();

  let people: Partial<PersonInfo>[] = dialog.context.whats_next.people ? dialog.context.whats_next.people.map(person => dialog.getPersonInfo(person)) : [];
  let prompt_people = people.map(p => dialog.getPersonInfo({id: p.id, name: p.name, image: p.image, vanity: p.vanity } ));

  if (curr_dialog === null || curr_dialog.replies.length === 0) {
    if (dialog.context.whats_next.quip) dialog.addPrompt(promptInfo(dialog.context.whats_next.quip, null, null, prompt_people), true);
    if (people.length) dialog.addInfo(people);
    dialog.setHide(lang.defaultanswers.HIDE_QUIP);
    if (dialog.filters && dialog.filters.length) dialog.setRefresh();
    dialog.reset('whats_next');
    dialog.setTopic(Topics.DEFAULT);
  } else {
    if (full_day) {
      dialog.addPrompt([], true);
      dialog.addInfo(dayInfo(dialog.context.whats_next));
    }

    if (dialog.context.whats_next.quip) dialog.addPrompt(promptInfo(dialog.context.whats_next.quip, null, null, prompt_people), !full_day);

    if (people.length) dialog.addInfo(people);
    else if (dialog.filters && dialog.filters.length) dialog.setRefresh();

    let found_event = false;

    for (const reply of curr_dialog.replies) {
      if (!full_day && found_event) break;

      if (!found_event || (reply.type === EntityType.Event && full_day)) {
        // check if an event is all day (starts or ends not today)
        if (activeEvent(reply, now) && allDay(reply, dialog.context.whats_next.event_time)) {
          all_day.push(dialog.ALLDAY_EVENT(reply, full_day, offset, locale, timeZone));
          found_event = true;
        } else {
          const format = dialog.formatNext(reply, now, full_day, offset, locale, timeZone);
          if (format) {
            if (format.prompt) dialog.addPrompt(format.prompt);
            if (format.info) dialog.addInfo(format.info);
            if (format.answers) dialog.addAnswers(format.answers);
            found_event = true;
          }
        }
      }
    }

    if (!found_event) dialog.setHide(lang.defaultanswers.HIDE_QUIP);
    // else dialog.setClear();

    if (full_day) {
      dialog.addPrompt(all_day);
      dialog.clearContext('whats_next');
    } else if (all_day.length) dialog.addPrompt(all_day[0]);

    curr_dialog = dialog.currentDialog();

    if (!curr_dialog.answers || !curr_dialog.answers.length) {
      dialog.clearContext('whats_next');
      dialog.setTopic(Topics.DEFAULT);
    }
  }
}

export default {
  name: WHAT_NAME,
  description: WHAT_DESC,
  examples: [], // lang.whats_next.WHAT_EG,
  reserved: lang.whats_next.WHAT_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: () => { return null; },
  reset_context: ['whats_next'],
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true); 
