import config from '../config';
import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import stripe from '../sources/stripe_controller';

import { Action, ActionType } from '../types/globals';
import { Location } from '../types/items';
import { Plugin, StripePluginState } from '../types/plugins';
import { AuthProviders, EntityType, TagType, findTypeValues } from '../types/shared';

import { promptInfo } from '../utils/info';
import parsers from '../utils/parsers';

const STRIPE_NAME = 'Stripe';
const STRIPE_DESC = 'Connect to Stripe for payments';

const topics = [
  TOPIC('STRIPE_CONNECT'),
  TOPIC('STRIPE_COUNTRY'),
  TOPIC('STRIPE_DISCONNECT'),
  TOPIC('STRIPE_CANNOT_DISCONNECT'),
  TOPIC('STRIPE_ACCOUNT'),
  TOPIC('STRIPE_DETAILS'),
  TOPIC('STRIPE_CONFIRM'),
  TOPIC('STRIPE_JOB_DESC'),
  TOPIC('STRIPE_COMPANY'),
  TOPIC('STRIPE_COMPANY_NAME'),
  TOPIC('STRIPE_CATEGORY'),
  TOPIC('STRIPE_AUTH'),
  TOPIC('STRIPE_AUTH_SUCCESS'),
  TOPIC('STRIPE_AUTH_PROJECT'),
  TOPIC('STRIPE_REG_ERROR'),
  TOPIC('STRIPE_DASHBOARD'),
];

function keywordMatch(_actions: Action[], message: string) {
  return parsers.checkKeyword(message, lang.stripe.STRIPE_KWD);
}

function setAction(dialog: Dialog, message: string, raw_message: string) {
  if (dialog.checkTopic(Topics.STRIPE_DETAILS)) {
    const emails = parsers.findEmail(raw_message);
    if (emails && emails.length) dialog.addAction(ActionType.COMMS, emails);

    const phones = parsers.findPhone(raw_message, dialog.user.locale);
    if (phones && phones.length) dialog.addAction(ActionType.COMMS, phones);

    //const url = parsers.findUrl(raw_message);
    //if (url) dialog.addAction(ActionType.URL, url);
  }

  if (dialog.checkTopic(Topics.STRIPE_COMPANY)) {
    if (parsers.checkKeyword(message, lang.stripe.COMPANY_BUSINESS)) {
      dialog.addAction(ActionType.ORG, { type: EntityType.Organization, name: raw_message });
    } else dialog.clearActions(ActionType.ORG);
  }

  if (dialog.checkTopic(Topics.STRIPE_COMPANY_NAME)) {
    dialog.addAction(ActionType.ORG, { type: EntityType.Organization, name: raw_message });
  }

  if (dialog.checkTopic(Topics.STRIPE_CATEGORY)) {
    for (const index in stripe.SERVICE_CATEGORIES) {
      const cat = stripe.SERVICE_CATEGORIES[index];
      if (parsers.checkKeyword(cat, message.split(' '))) {
        dialog.addAction(ActionType.CMD, cat);
        break;
      }
    }
  }

  if (parsers.checkKeyword(message, lang.stripe.STRIPE_KWD)) {
    const account = parsers.extract(lang.stripe.STRIPE_KWD, raw_message);
    if (dialog.user.paymentAccount(account && account.length ? account : null)) {
      if (!dialog.context.stripe) dialog.context.stripe = new StripePluginState();
      dialog.context.stripe.account_id = account && account.length ? account : null;
      dialog.setTopic(Topics.STRIPE_DASHBOARD);
    } else dialog.setTopic(Topics.STRIPE_CONNECT); // kick it over to admin
  }

  return true;
}

function isActive(dialog: Dialog) {
  return dialog.checkTopic(topics);
}

function runAction(dialog: Dialog) {
  // lookup a contract for context
  if (!dialog.context.stripe || dialog.checkTopic(Topics.STRIPE_CONNECT)) {
    dialog.context.stripe = stripe.setupContext(dialog.user, dialog.me, dialog.user.locale, dialog.cache);
    dialog.setTopic(Topics.STRIPE_ACCOUNT);
    // dialog.setTopic(Topics.STRIPE_DETAILS);
    return;
  }

  const bool = dialog.actionValues(ActionType.BOOL);
  const org = dialog.actionValues(ActionType.ORG);
  const cmd = dialog.actionValues(ActionType.CMD);
  const location = dialog.actionValues(ActionType.LOCATION) as Partial<Location>[];

  switch (dialog.topic) {
    case Topics.STRIPE_CONFIRM:
      if (dialog.message.command) return;
      if (bool && bool.length && bool[0]) {
        if (dialog.context.stripe.desc) {
          // if (dialog.context.stripe.company)
          dialog.setTopic(Topics.STRIPE_COMPANY);
          // else dialog.setTopic(Topics.STRIPE_CATEGORY);
        } else dialog.setTopic(Topics.STRIPE_JOB_DESC);
      } 
      break;
    case Topics.STRIPE_ACCOUNT:
      if (bool && bool.length && bool[0]) {
        dialog.context.stripe.name = null;
        dialog.setTopic(Topics.STRIPE_AUTH);
      }
      else dialog.setTopic(Topics.STRIPE_DETAILS);
      break;
    case Topics.STRIPE_DETAILS:
      if (dialog.message.command) return;
      else {
        const entity = dialog.actionValues(ActionType.ENTITY);
        if (entity && entity.length) dialog.context.stripe.name = entity.map(e => e.name).join(' ').trim();

        // if (org && org.length) dialog.context.stripe.company = org[0].name;

        const comms = dialog.actionValues(ActionType.COMMS);

        let email; 
        let phone;
        for (const comm of comms) {
          const emails = parsers.findEmail(comm);
          if (emails && emails.length) {
            email = emails[0];
            continue;
          }

          const phones = parsers.findPhone(comm, dialog.user.locale);
          if (phones && phones.length) {
            phone = phones[0];
            /*.slice(-10);*/

            continue;
          }
        }

        dialog.context.stripe.email = email;
        dialog.context.stripe.phone = phone;

        const url = dialog.actionValues(ActionType.URL);
        if (url && url.length) dialog.context.stripe.url = url[0];
        else dialog.context.stripe.url = null;

        const name_pos = dialog.message.message.indexOf(dialog.context.stripe.name);
        const email_pos = dialog.message.message.toLowerCase().indexOf(dialog.context.stripe.email);
        const phone_pos = dialog.message.message.search(/\d/);
        const url_pos = dialog.message.message.indexOf(dialog.context.stripe.url);

        const pos_index = [name_pos, email_pos, phone_pos, url_pos].sort();
        const name_index = pos_index.indexOf(name_pos);
        const next_index = pos_index.slice(name_index).find(p => p !== name_pos);

        if (name_index === 0) dialog.context.stripe.name = dialog.message.message.slice(0, next_index).trim();
        else if(name_index === pos_index.length - 1) dialog.context.stripe.name = dialog.message.message.slice(pos_index[name_index]).trim();
        else dialog.context.stripe.name = dialog.message.message.slice(pos_index[name_index], next_index).trim();

        dialog.context.stripe.detail_step = (dialog.context.stripe.detail_step + 1) % lang.stripe.DETAILS_PROMPT.length;
      }
      break;

    case Topics.STRIPE_JOB_DESC:
      if (dialog.message.command) return;
      if (bool && bool.length) {
        if (bool[0]) {
          // if (dialog.context.stripe.company) 
          dialog.setTopic(Topics.STRIPE_COMPANY);
          // else dialog.setTopic(Topics.STRIPE_CATEGORY);
        }
      } else {
        if (dialog.message.message.length) {
          dialog.context.stripe.desc = dialog.message.message;
          // if (dialog.context.stripe.company) 
          dialog.setTopic(Topics.STRIPE_COMPANY);
          // else dialog.setTopic(Topics.STRIPE_CATEGORY);
        }
      }
      break;

    case Topics.STRIPE_COMPANY:
      if (dialog.message.command) return;
      if (org && org.length) dialog.setTopic(Topics.STRIPE_COMPANY_NAME);
      else {
        dialog.context.stripe.company = null;
        dialog.setTopic(Topics.STRIPE_CATEGORY);
      }
      break;

    case Topics.STRIPE_COMPANY_NAME:
      if (dialog.message.command) return;
      if (org && org.length) {
        dialog.context.stripe.company = org[0].name;
        dialog.setTopic(Topics.STRIPE_CATEGORY);
      }
      break;

    case Topics.STRIPE_CATEGORY:
      if (dialog.message.command) return;
      if (cmd && cmd.length) dialog.context.stripe.category = cmd[0];
      if (dialog.context.stripe.country_confirmed) dialog.setTopic(Topics.STRIPE_AUTH);
      else dialog.setTopic(Topics.STRIPE_COUNTRY);
      break;
    case Topics.STRIPE_COUNTRY:
      if (dialog.message.command && !dialog.message.ping) return;
      if (!dialog.context.stripe.countries) {
        dialog.runAsync('payment', async () => {
          dialog.context.stripe.countries = await stripe.countries();
        });
      }

      if (!dialog.context.stripe.countries) {
        // dialog.quickPing();
        return true;
      }

      // dialog.clearPing();

      if (location && location.length) {
        if ((location[0].name && dialog.context.stripe.countries.includes(location[0].name)) ||
          (location[0].native && dialog.context.stripe.countries.includes(location[0].native)) ||
          (location[0].code && dialog.context.stripe.countries.includes(location[0].code))
          ) {
          dialog.context.stripe.locale = location[0].code;
          dialog.context.stripe.country_confirmed = true;
          dialog.setTopic(Topics.STRIPE_AUTH);
        }
      } else if (bool && bool.length) {
        dialog.context.stripe.country_confirmed = true;
        dialog.setTopic(Topics.STRIPE_AUTH);
      }
      break;
    case Topics.STRIPE_DASHBOARD:
      if (dialog.context.stripe && dialog.user.paymentAccount(dialog.context.stripe.account_id)) {
        if (!dialog.context.stripe.dashboard_loading) {
          dialog.context.stripe.dashboard_loading = true;
          dialog.runAsync('stripe', async () => {
            const url = await stripe.login(dialog.user.getTokens(AuthProviders.Stripe, dialog.context.stripe.account_id).stripe_user_id);
            if (dialog.context.stripe) dialog.context.stripe.dashboard_link = url;
          });
        }

        // if (!dialog.context.stripe.dashboard_link) { dialog.quickPing(); }
      } else dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.STRIPE_DISCONNECT:
      if (dialog.message.command) return;
      if (bool && bool.length) {
        if (bool[0] && !dialog.context.stripe.deleting) {
          dialog.context.stripe.deleting = true;
          dialog.runAsync('stripe', async () => {
            dialog.user.deleteAccount(AuthProviders.Stripe, dialog.context.stripe.account_id);
            // await data.users.save(dialog.user).catch(err => dialog.asyncError(err));
            await dialog.projects.candidateReady(false);
            dialog.context.stripe.deleted = true;
          });
        }

        // if (dialog.context.stripe.deleting && !dialog.context.stripe.deleted) dialog.quickPing();
      } else if(dialog.context.stripe.deleting) {
        // if (!dialog.context.stripe.deleted) dialog.quickPing();
        // NOOP
      } else {
        // check for projects as contractor
        if (dialog.cache.projects) {
          for (const project of Object.values(dialog.cache.projects)) {
            if (project.contractor && project.contractor.askfora_id === dialog.user.profile) {
              if (project.escrow && !project.payment) {
                dialog.setTopic(Topics.STRIPE_CANNOT_DISCONNECT);
              }
            }
          }
        }
      }
      break;
  }
}

function setPrompt(dialog: Dialog) {
  let phone = dialog.context.stripe.phone;
  if (!phone) phone = '';
  let url = dialog.context.stripe.url;
  if (!url) url = '';

  switch (dialog.topic) {
    case Topics.STRIPE_DETAILS:
      if (!dialog.message.ping) { // && (!dialog.message.command || dialog.message.link)) {
        dialog.addPrompt([lang.stripe.DETAILS_PROMPT[dialog.context.stripe.detail_step], `${dialog.context.stripe.name} ${dialog.context.stripe.email} ${phone} ${url}`], true);
        dialog.addAnswers(lang.stripe.DETAILS_ANSWERS);
        dialog.setTopic(Topics.STRIPE_CONFIRM);
        // dialog.clearPing();
        dialog.setSticky();
      }
      break;
    case Topics.STRIPE_CONFIRM:
      if (!dialog.message.ping && !dialog.message.command) {
        dialog.addPrompt(lang.stripe.DETAILS_CHANGE, true);
        dialog.addSuggestion(`${dialog.context.stripe.name} ${dialog.context.stripe.email} ${phone} ${url}`);
        // dialog.clearPing();
        dialog.setSticky();
        dialog.setTopic(Topics.STRIPE_DETAILS);
      }
      break;
    case Topics.STRIPE_JOB_DESC:
      if (!dialog.message.ping && !dialog.message.command) {
        dialog.addPrompt([lang.stripe.DETAILS_CONFIRM, `${dialog.context.stripe.name} ${dialog.context.stripe.email} ${phone} ${url}`], true);
        if (dialog.context.stripe.desc) {
          dialog.addPrompt([promptInfo(lang.stripe.JOB_DESC_CONFIRM), promptInfo(dialog.context.stripe.desc)]);
          dialog.addAnswers('All set');
        } else dialog.addPrompt(lang.stripe.JOB_DESC_PROMPT);
        dialog.setSticky();
      }
      break;
    case Topics.STRIPE_COMPANY:
      if (!dialog.message.ping && !dialog.message.command) {
        dialog.addPrompt(lang.stripe.COMPANY_PROMPT, true);
        dialog.addAnswers(lang.stripe.COMPANY_ANSWERS);
        dialog.setSticky();
      }
      break;
    case Topics.STRIPE_COMPANY_NAME:
      if (!dialog.message.ping && !dialog.message.command) {
        dialog.addPrompt(lang.stripe.COMPANY_NAME_PROMPT, true);
        let answers = dialog.context.stripe.company ? [dialog.context.stripe.company] : [];
        if (dialog.me) {
          const orgs = findTypeValues(dialog.me.tags, TagType.organization);
          answers = answers.concat(orgs);
        }
        if (answers.length) dialog.addAnswers(answers);
        dialog.setSticky();
      }
      break;
    case Topics.STRIPE_CATEGORY:
      if (!dialog.message.ping && !dialog.message.command) {
        dialog.addPrompt(lang.stripe.CATEGORY_PROMPT, true);
        dialog.addAnswers(lang.stripe.STRIPE_CATEGORIES);
        dialog.setSticky();
      }
      break;
    case Topics.STRIPE_COUNTRY:
      if (!dialog.message.ping && !dialog.message.command && dialog.context.stripe.countries) {
        dialog.addPrompt(lang.stripe.COUNTRY_PROMPT, true);
        const ci = parsers.countryInfo(dialog.context.stripe.locale.slice(-2).toUpperCase());
        if (ci) dialog.addAnswers([ci.native]);
        dialog.setSticky();
      } // else dialog.quickPing();
      break;
    case Topics.STRIPE_ACCOUNT:
      dialog.newDialog();
      dialog.addPrompt(lang.stripe.STRIPE_ACCOUNT);
      dialog.addAnswers(['Yes', 'No']);
      break;
    case Topics.STRIPE_AUTH:
      if (!dialog.message.ping && !dialog.message.command) {
        const sc = dialog.context.stripe;

        let url = null;
        if (config.isEnvOffline()) url = lang.stripe.AUTH_STUB(sc.context);
        else url = stripe.stripeAuthUrl(sc.context, sc.name, sc.email, sc.phone, sc.url, sc.category, sc.desc, sc.company, sc.locale);
        
        // dialog.setRedirect(url);
        dialog.addPrompt(lang.stripe.STRIPE_URL(url), true);
        // dialog.noPing();
        dialog.setSticky();
        dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.STRIPE_AUTH_SUCCESS:
      dialog.lastPrompt('stripe', lang.stripe.CONNECTED, {clear:true});
      break;
    case Topics.STRIPE_AUTH_PROJECT:
      dialog.lastPrompt('stripe', lang.stripe.CONNECTED_PROJECT(dialog.context.stripe ? dialog.context.stripe.project : null), {clear:true});
      break;
    case Topics.STRIPE_REG_ERROR:
      if (!dialog.message.ping) {
        dialog.lastPrompt('stripe', lang.stripe.ERROR, {clear:true});
      }
      break;
    case Topics.STRIPE_DASHBOARD:
      if (!dialog.context.stripe || !dialog.context.stripe.dashboard_loading) dialog.lastPrompt('stripe', lang.stripe.ERROR, { clear:true});
      else {
        if (dialog.context.stripe.dashboard_link) {
          dialog.lastPrompt('stripe', lang.stripe.DASHBOARD(dialog.context.stripe.dashboard_link), {clear:true});
        }
      }
      break;
    case Topics.STRIPE_DISCONNECT:
      if (!dialog.context.stripe || !dialog.context.stripe.deleting) {
        dialog.addPrompt(lang.stripe.CONFIRM_DISCONNECT, true);
        dialog.setSticky();
        // dialog.clearPing();
      } else if(dialog.context.stripe.deleted) {
        dialog.lastPrompt('stripe', lang.stripe.DISCONNECTED, {clear: true});
      }
      break;
    case Topics.STRIPE_CANNOT_DISCONNECT:
      if (!dialog.message.ping) dialog.lastPrompt('stripe', lang.stripe.CANNOT_DISCONNECT, {clear:true});
      break;
  }
}

export default {
  name: STRIPE_NAME,
  description: STRIPE_DESC,
  examples: [],
  reserved: [],
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default);
