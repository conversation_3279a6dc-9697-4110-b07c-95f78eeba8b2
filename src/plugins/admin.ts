import { AuthProvider } from '../auth/auth_provider';
import config from '../config';
import data from '../data';
import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import * as itemTypes from '../types/items';
import { ImportsPluginState, InitPluginState, Plugin, StripePluginState, WisePluginState } from '../types/plugins';
import { AuthClientNameInfo, AuthContext, AuthLevel, AuthProviders, EntityType } from '../types/shared';

import { flatten } from '../utils/funcs';
import logging from '../utils/logging';
import mail from '../utils/mail';
import parsers from '../utils/parsers';

const LOG_NAME = 'plugins.admin';
const ADMIN_RESERVED = [
  ...lang.admin.REFRESH,
  ...lang.admin.UPDATE,
  // ...lang.admin.CLEAR,
  ...lang.admin.RELOAD,
  ...lang.admin.REAUTH,
  ...lang.admin.FLUSH,
  ...lang.admin.RECONNECT,
  ...lang.admin.HARD_RESET,
  ...lang.admin.RESET,
  ...lang.admin.LAST_LOAD,
  ...lang.admin.EXPORT,
  ...lang.admin.STRIPE,
  ...lang.admin.PAYMENT, 
  ...lang.admin.WISE,
  ...lang.admin.CONTRACTOR,
  ...lang.admin.LOGOUT,
  ...lang.admin.DELETE_ACCOUNT,
  ...lang.admin.DISCONNECT,
  ...lang.admin.CONNECT_POSITIVE,
];

const CONNECT_CMDS = [...lang.admin.DISCONNECT, ...lang.admin.CONNECT_POSITIVE, ...lang.admin.RECONNECT];

const topics = [
  TOPIC('ADMIN'),
  TOPIC('REFRESH'), // force refresh data
  TOPIC('UPDATE'), // update data if appropriate
  // TOPIC('CLEAR'), // delete data (for testing only)
  TOPIC('RELOAD'), // reload cache
  //TOPIC('REAUTH'), // authenticate or reauth
  TOPIC('UNAUTH'), // remove account
  TOPIC('FLUSH'), // flush the cache
  TOPIC('HARD_RESET'), // reset dialog to to the conversation
  TOPIC('RESET'), // reset dialog but don't ack
  TOPIC('LAST_LOAD'), // show cache and refresh times
  TOPIC('EXPORT'), // export people to bigquery
  TOPIC('LOGOUT'), // delete session and go back home
  TOPIC('DELETE_ACCOUNT'), // delete all account data
];

const ADMIN_KWD = ADMIN_RESERVED;
const ADMIN_MASKED = [
  ...lang.admin.REFRESH,
  ...lang.admin.UPDATE,
  // ...lang.admin.CLEAR,
  ...lang.admin.RELOAD,
  ...lang.admin.REAUTH,
  ...lang.admin.FLUSH,
  ...lang.admin.RECONNECT,
  ...lang.admin.LAST_LOAD,
  ...lang.admin.EXPORT,
  ...lang.admin.STRIPE,
  ...lang.admin.PAYMENT,
  ...lang.admin.WISE,
  ...lang.admin.HARD_RESET,
  ...lang.admin.RESET,
  ...lang.admin.CONTRACTOR,
  ...lang.admin.LOGOUT,
  ...lang.admin.DELETE_ACCOUNT,
  ...lang.admin.DISCONNECT,
  ...lang.admin.CONNECT_POSITIVE,

];

function keywordMatch(actions, message, _raw_message, group_host) {
  const skills = Dialog.getActionValues(actions, ActionType.SKILL);
  const bool = Dialog.getActionValues(actions, ActionType.BOOL);
  const ignore_entity_acts = actions.filter(a => a.type !== ActionType.ENTITY || (a.people && a.people.length));

  const providers: AuthClientNameInfo[] = AuthProvider.clientAuthNameSet(null, group_host);

  const kwd = parsers.checkKeyword(message, ADMIN_KWD, true);
  const provider_match = parsers.checkKeyword(message, providers.map(a => a.name.toLowerCase()), true) || parsers.checkKeyword(message, providers.map(a => a.provider.toLowerCase()), true);
  const reset = parsers.checkKeyword(message, [...lang.admin.RESET, ...lang.admin.HARD_RESET], true);
  if (logging.isDebug()) logging.debugF(LOG_NAME, 'keywordMatch', `kwd:${kwd} provider:${provider_match} reset:${reset} ignore:${ignore_entity_acts.length} skills:${skills.length} bool:${bool.length}`);
  return (kwd || provider_match) && (!reset || ignore_entity_acts.length === skills.length + bool.length);
}

function authAction(dialog: Dialog, message: string, raw_message: string) {
  const bool = dialog.actionValues(ActionType.BOOL);

  if (parsers.checkKeyword(message, lang.admin.REFRESH, true)) {
    if (dialog.message.link || (dialog.isMaskedTopic() && dialog.message.command)) return false;
    dialog.setTopic(Topics.REFRESH);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'refresh');
    return true;
  }
  if (parsers.checkKeyword(message, lang.admin.UPDATE, true)) {
    if (dialog.message.link) return false;
    dialog.setTopic(Topics.UPDATE);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'update');
    return true;
  }
  if (parsers.checkKeyword(message,lang.admin. FLUSH, true)) {
    if (dialog.message.link) return false;
    if (!dialog.context.init) dialog.context.init = new InitPluginState();
    dialog.setTopic(Topics.FLUSH);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'flush');
    return true;
  }
  if (parsers.checkKeyword(message, lang.admin.RELOAD, true)) {
    if (dialog.message.link) return false;
    dialog.setTopic(Topics.RELOAD);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'reload');
    return true;
  }
  if (parsers.checkKeyword(message, lang.admin.REAUTH, true)) {
    if (dialog.message.link) return false;
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `${JSON.stringify(dialog.context)} ${dialog.user.provider}`);
    if (!dialog.context.init) dialog.context.init = { provider: dialog.user.provider } as InitPluginState;
    dialog.setTopic(Topics.REGISTER_REAUTH);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'register_reauth');
    return true;
  }
  if (parsers.checkKeyword(message, lang.admin.LAST_LOAD, true)) {
    if (dialog.message.link) return false;
    dialog.setTopic(Topics.LAST_LOAD);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'last_load');
    return true;
  }
  if (parsers.checkKeyword(message, lang.admin.EXPORT, true)) {
    if (dialog.message.link) return false;
    dialog.setTopic(Topics.EXPORT);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'export');
    return true;
  }
  if (parsers.checkKeyword(message, lang.admin.DELETE_ACCOUNT, true)) {
    if (dialog.message.link) return false;
    dialog.setTopic(Topics.DELETE_ACCOUNT);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'delete account');
    return true;
  }

  if (parsers.checkKeyword(message, CONNECT_CMDS, true) && !dialog.isMaskedTopic()) {
    // group or default providers
    const providers: AuthClientNameInfo[] = AuthProvider.clientAuthNameSet(null /*dialog.user.provider*/, dialog.group_host, dialog.group_host && dialog.user.isAdmin(dialog.group_host.id));
    const user_providers: AuthClientNameInfo[] = dialog.user.accounts ? flatten(Object.keys(dialog.user.accounts).filter(p => Object.values(dialog.user.accounts[p]).length).map(p => AuthProvider.clientAuthNameSet(p as AuthProviders))) : [];
    const group_providers: AuthClientNameInfo[] = dialog.group_host && dialog.group_host.accounts ? flatten(Object.keys(dialog.group_host.accounts).filter(p => Object.values(dialog.user.accounts[p]).length).map(p => AuthProvider.clientAuthNameSet(p as AuthProviders))) : [];

    const set_provider = parsers.checkKeyword(message, [...providers.map(a => a.provider.toLowerCase()), ...providers.map(a => a.name.toLowerCase())]);
    const user_provider = parsers.checkKeyword(message, [...user_providers.map(a => a.name.toLowerCase()), ...user_providers.map(a => a.name.toLowerCase())]);
    const group_provider = parsers.checkKeyword(message, [...group_providers.map(a => a.name.toLowerCase()), ...group_providers.map(a => a.name.toLowerCase())]);

    if (parsers.checkKeyword(message, lang.admin.STRIPE)) {
      if (parsers.checkKeyword(message, lang.admin.DISCONNECT, true)) {
        if (dialog.message.link) return false;
        dialog.context.stripe = new StripePluginState();
        const account = parsers.extract(lang.stripe.STRIPE_KWD, raw_message);
        if(account && account.length) dialog.context.stripe.account_id = account;
        dialog.setTopic(Topics.STRIPE_DISCONNECT);
      } else dialog.setTopic(Topics.STRIPE_CONNECT);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'stripe');
      return true;
    } else if (parsers.checkKeyword(message, lang.admin.WISE)) {
      if (parsers.checkKeyword(message, lang.admin.DISCONNECT, true)) {
        if (dialog.message.link) return false;
        dialog.context.wise = new WisePluginState();
        dialog.setTopic(Topics.WISE_DISCONNECT);
      } else dialog.setTopic(Topics.WISE_CONNECT);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'wise');
      return true;
    } else if (parsers.checkKeyword(message, lang.admin.PAYMENT)) {
      dialog.setTopic(Topics.CONNECT_PAYMENT);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'payment');
      return true;
    } else if (set_provider || user_provider || group_provider) {
      if (dialog.message.link) return false;

      if (parsers.checkKeyword(message, lang.admin.DISCONNECT, true)) {
        // if not in a group with a default provider, disconnecting the main account with no other accounts is the same as delete account
        if ((!dialog.group_host || !dialog.group_host.provider) && 
          parsers.checkKeyword(message, dialog.user.provider) && user_providers.length === 1 && Object.values(dialog.user.accounts[user_providers[0].provider]).length === 1) {
          dialog.setTopic(Topics.DELETE_ACCOUNT);
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'delete account');
          return true;
        } else {
          // disconnect the provider
          const provider = [...providers, ...user_providers, ...group_providers].find(p => message.includes(p.name.toLowerCase()) || message.includes(p.provider));
          if (provider) {
            let account = parsers.extract(provider.name.toLowerCase(), message);
            if (!account || !account.length) account = parsers.extract(provider.provider, message);
            if (!dialog.context.init) {
              dialog.context.init = { 
                provider: provider.provider,
                account,
              } as InitPluginState;
            } else {
              dialog.context.init.provider = provider.provider;
              dialog.context.init.account = account;
              if (dialog.context.init.topic) {
                dialog.setTopic(dialog.context.init.topic);
                return true;
              }
            }
            dialog.setTopic(Topics.UNAUTH);
            return true;
          }
        }
      } else {
        // reconnect with the same scope
        let provider = [...providers, ...user_providers, ...group_providers].find(p => message.includes(p.name.toLowerCase()));
        if (provider) {
          // upgrade microsoft to msal
          if (provider.provider === AuthProviders.Microsoft) [provider] = AuthProvider.clientAuthNameSet(AuthProviders.Msal, dialog.group_host, dialog.group_host && dialog.user.isAdmin(dialog.group_host.id));
          const account = parsers.extract(provider.name.toLowerCase(), message);
          let auth_context = AuthProvider.getAuthContext(dialog.user, provider.provider, account, dialog.group_host);
          const auth_permissions = AuthProvider.clientAuthPermissions(dialog.user, provider.provider, dialog.group_host);
          if (parsers.checkKeyword(message, lang.admin.SYNC)) auth_context = AuthContext.AuthSyncOrganizer;
          if (parsers.checkKeyword(message, lang.admin.DIRECTORY)) auth_context = AuthContext.AuthConnect;

          if (!dialog.context.init) {
            dialog.context.init = { 
              provider: provider.provider,
              auth_context,
              account,
            } as InitPluginState;
          } else {
            dialog.context.init.provider = provider.provider;
            dialog.context.init.auth_context = auth_context;
            dialog.context.init.account = account;
            if (dialog.context.init.topic) {
              dialog.setTopic(dialog.context.init.topic);
              return true;
            }
          }

          dialog.setTopic(Topics.REGISTER);
          dialog.session.redirect = '/app';

          if (dialog.checkActionValues(ActionType.ENTITY_TYPE, EntityType.Message) ) {
            if (bool.length && !bool[0] && auth_permissions === AuthLevel.Organizer) dialog.setTopic(Topics.REGISTER_NO_EMAIL);
          } else if (dialog.checkActionValues(ActionType.ENTITY_TYPE, EntityType.Person)) {
            if (bool.length && !bool[0]) dialog.setTopic(Topics.REGISTER_NO_ORGANIZER);
          } else if (dialog.checkActionValues(ActionType.CMD, lang.admin.NETWORK)) {
            dialog.setTopic(Topics.REGISTER_NETWORK);
          } 
          return true;
        }
      }
    } else if (parsers.checkKeyword(message, lang.admin.RECONNECT)) {
      // no provider specified, used default
      if (dialog.message.link) return false;
      const auth_context = AuthProvider.getAuthContext(dialog.user, dialog.user.provider, dialog.user.profile, dialog.group_host);
      dialog.context.init = { provider: dialog.user.provider, auth_context, account: dialog.user.profile } as InitPluginState;
      dialog.setTopic(Topics.REGISTER);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'reconnect');
      return true;
    } else if (message.split(' ').length <= 4) {
      if (dialog.message.link) return false;
      if (parsers.checkKeyword(message, Object.keys(lang.imports.IMPORTS_TYPES))) {
        if (!dialog.context.imports) dialog.context.imports = new ImportsPluginState();
        for (const import_type in lang.imports.IMPORTS_TYPES) {
          if (parsers.checkKeyword(message, [import_type])) {
            dialog.context.imports.import_type = lang.imports.IMPORTS_TYPES[import_type];
            break;
          }
        }
        if (parsers.checkKeyword(message, lang.imports.ACCOUNT_KWD)) dialog.setTopic(Topics.IMPORTS_ACCOUNT);          
        else dialog.setTopic(Topics.IMPORTS);
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'import');
        return true;
      } else {
        let auth_context = AuthContext.AuthNoEmail;

        const entities = dialog.actionValues(ActionType.ENTITY_TYPE);

        if ((entities.length === 0 || dialog.checkActionValues(ActionType.ENTITY_TYPE, EntityType.Message)) && 
          dialog.actionValues((itemTypes as any).ENTITY).length === 0 && !dialog.actionValues(ActionType.BOOL).length && !parsers.checkKeyword(message, lang.admin.DISCONNECT, true)) {

          dialog.addAction(ActionType.BOOL, false);
          auth_context = AuthContext.AuthChat;
        } else if(dialog.checkActionValues(ActionType.ENTITY_TYPE, EntityType.Message)) {
          auth_context = AuthContext.AuthChat;
        } else if(dialog.checkActionValues(ActionType.ENTITY_TYPE, EntityType.Person) || dialog.checkActionValues(ActionType.ENTITY_TYPE, EntityType.Event)) {
          if (parsers.checkKeyword(message, 'sync')) auth_context = AuthContext.AuthSyncOrganizer;
          else auth_context = AuthContext.AuthNoEmail;
        }

        const auth_providers = AuthProvider.clientAuthSet(auth_context, dialog.group_host);
        const provider_set = auth_providers.find(p => message.includes(p.name.toLowerCase()));
        let provider = provider_set ? provider_set.provider : null;
        if (provider && provider === AuthProviders.Microsoft) provider = AuthProviders.Msal;

        if (parsers.checkKeyword(message, lang.admin.NETWORK)) dialog.addAction(ActionType.CMD, lang.admin.NETWORK);
        // if (dialog.context.init) delete dialog.context.init;
        dialog.context.init = { provider, auth_context };

        dialog.setTopic(Topics.REGISTER);
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'reauth');
        return true;
      }
    }
  }

  return isActive(dialog);
}

function guestAction(dialog: Dialog, message: string, raw_message: string) {
  // Not authenticated
  const providers: AuthClientNameInfo[] = AuthProvider.clientAuthNameSet(null, dialog.group_host);
  if ((!dialog.isMaskedTopic() || dialog.checkTopic(Topics.INIT) || dialog.checkTopic(Topics.PROJECT_AUTH)) && 
    (parsers.checkKeyword(message, providers.map(a => a.name.toLowerCase()), true) ||
    parsers.checkKeyword(message, providers.map(a => a.provider.toLowerCase()), true)) ) {
    for (const provider of providers) {
      if (message.includes(provider.name.toLowerCase()) || message.includes(provider.provider.toLowerCase())) {
        if (dialog.group_host && dialog.group_host.provider && dialog.group_host.provider !== provider.provider 
            && (!dialog.group_host.accounts || !Object.keys(dialog.group_host.accounts).includes(provider.provider))) {
          dialog.setTopic(Topics.REG_ERROR_CANCELLED);
        } else if (!dialog.context.init) {
          dialog.context.init = { provider: provider.provider } as InitPluginState;
          dialog.setTopic(Topics.REGISTER);
        } else {
          dialog.context.init.provider = provider.provider;
          if (dialog.context.init.topic) dialog.setTopic(dialog.context.init.topic);
          else dialog.setTopic(Topics.REGISTER);
        }
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `auth ${provider.provider}`);
        return true;
      }
    }
  } else if (parsers.checkKeyword(message, CONNECT_CMDS, true) && (!dialog.isMaskedTopic() || dialog.checkTopic(Topics.INIT) || dialog.checkTopic(Topics.PROJECT_AUTH))) {
    if (parsers.checkKeyword(message, lang.admin.MOBILE)) {
      dialog.setTopic(Topics.REGISTER_MOBILE);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'mobile');
      return true;
    } else if (parsers.checkKeyword(message, providers.map(a => a.name.toLowerCase()))) {
      let auth_context: AuthContext;
      const connect_context = parsers.extractLast(CONNECT_CMDS, message);
      for (const ac of Object.values(AuthContext)) {
        if (parsers.checkKeyword(connect_context, ac)) {
          auth_context = ac;
          break;
        }
      }

      for (const provider of providers) {
        if (message.includes(provider.name.toLowerCase()) || message.includes(provider.provider.toLowerCase())) {
          if (dialog.group_host && dialog.group_host.provider && dialog.group_host.provider !== provider.provider
            && (!dialog.group_host.accounts || !dialog.group_host.accounts[provider.provider]) ) {
            dialog.setTopic(Topics.REG_ERROR_CANCELLED);
          } else if (!dialog.context.init) {
            dialog.context.init = { provider: provider.provider, auth_context } as InitPluginState;
            dialog.setTopic(Topics.REGISTER);
          } else {
            dialog.context.init.provider = provider.provider;
            if (auth_context) dialog.context.init.auth_context = auth_context;
            //if (dialog.context.init.topic) dialog.setTopic(dialog.context.init.topic);
            //else 
            dialog.setTopic(Topics.REGISTER);
          }
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, `connect ${provider.provider}`);
          return true;
        }
      }
    } else if (parsers.checkKeyword(message, lang.admin.CONTRACTOR)) {
      if (!dialog.checkTopic(Topics.CONTRACTOR_REGISTER)) dialog.setTopic(Topics.INIT_CONTRACTOR);
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'contractor');
      return true;
    } else {
      const parts = message.split(' ');
      if ( parts.length <= 3 &&
      (dialog.actionValues(ActionType.ENTITY_TYPE).length === 0 || dialog.checkActionValues(ActionType.ENTITY_TYPE, EntityType.Message)) &&
      dialog.actionValues((itemTypes as any).ENTITY).length === 0) {
        if (!dialog.actionValues(ActionType.BOOL).length && parsers.checkKeyword(message, lang.admin.DISCONNECT, true)) {
          dialog.addAction(ActionType.BOOL, false);
        }

        if (parsers.checkKeyword(message, lang.admin.NETWORK)) dialog.addAction(ActionType.CMD, lang.admin.NETWORK);

        // group or default providers
        const providers: AuthClientNameInfo[] = AuthProvider.clientAuthNameSet(null /*dialog.user.provider*/, dialog.group_host, dialog.group_host && dialog.user.isAdmin(dialog.group_host.id));
        const group_providers: AuthClientNameInfo[] = dialog.group_host && dialog.group_host.accounts ? flatten(Object.keys(dialog.group_host.accounts).filter(p => Object.values(dialog.user.accounts[p]).length).map(p => AuthProvider.clientAuthNameSet(p as AuthProviders))) : [];

        if (parts.length > 1) {
          const lpart = parts[1];
          let provider = group_providers.find(a => a.provider.toLowerCase() === lpart || a.name.toLowerCase() === lpart);
          if (!provider) provider = providers.find(a => a.provider.toLowerCase() === lpart || a.name.toLowerCase() === lpart);
          if (provider) {
            if (dialog.context.init) dialog.context.init.provider = provider.provider;
            else dialog.context.init = { provider: provider.provider }
          } else if(Object.values(AuthContext).includes(lpart as AuthContext)) {
            if (dialog.context.init) dialog.context.init.auth_context = lpart as AuthContext
            else dialog.context.init = { auth_context: lpart as AuthContext}
          }
        }

        if (parts.length > 2) {
          const lpart = parts[2];
          let provider = group_providers.find(a => a.provider.toLowerCase() === lpart || a.name.toLowerCase() === lpart);
          if (!provider) provider = providers.find(a => a.provider.toLowerCase() === lpart || a.name.toLowerCase() === lpart);
          if (provider) {
            if (dialog.context.init) dialog.context.init.provider = provider.provider;
            else dialog.context.init = { provider: provider.provider }
          } else if(Object.values(AuthContext).includes(lpart as AuthContext)) {
            if (dialog.context.init) dialog.context.init.auth_context = lpart as AuthContext
            else dialog.context.init = { auth_context: lpart as AuthContext}
          }
        }

        dialog.setTopic(Topics.REGISTER);
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'auth');
        return true;
      }
    }
  }

  return isActive(dialog);
}

function setAction(dialog: Dialog, message: string, raw_message: string) {
  if (dialog.isMaskedTopic() && !parsers.checkKeyword(message, ADMIN_MASKED, true)) return false;

  if (parsers.checkKeyword(message, lang.admin.RESET, true)) {
    if (dialog.message.link) return false;
    if (dialog.message.command && dialog.checkTopic([Topics.PROJECT_EXPERT, Topics.PROJECT_EXPERT_SKILLS, Topics.PROJECT_EXPERT_GOAL])) dialog.addAction(ActionType.CMD, 'expert');
    if (dialog.checkTopic([Topics.PROJECT, Topics.PROJECT_SKILLS, Topics.PROJECT_GOAL])) dialog.addAction(ActionType.CMD, 'project');
    dialog.setTopic(Topics.RESET);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'reset');
    return true;
  }

  if (parsers.checkKeyword(message, lang.admin.HARD_RESET, true)) {
    if (dialog.message.link) return false;
    dialog.setTopic(Topics.HARD_RESET);
    if (parsers.checkKeyword(message, lang.admin.FORCE)) dialog.addAction(ActionType.CMD, true);
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'hard reset');
    return true;
  }

  if (parsers.checkKeyword(message, lang.admin.LOGOUT, true)) {
    if (dialog.message.link) return false;
    dialog.setTopic(Topics.LOGOUT);
    dialog.context.init = null;
    if (parsers.checkKeyword(message, 'network')) dialog.addAction(ActionType.CMD, 'network');
    else if (parsers.checkKeyword(message, 'expert')) dialog.addAction(ActionType.CMD, 'expert');
    else if (parsers.checkKeyword(message, 'app')) dialog.addAction(ActionType.CMD, 'app');
    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'setAction', dialog.user.profile, 'logout');
    return true;
  }

  if (dialog.isAuthenticated()) return authAction(dialog, message, raw_message);  
  else return guestAction(dialog, message, raw_message);
}

function isActive(dialog: Dialog): boolean {
  return dialog.checkTopic([Topics.DELETE_ACCOUNT, Topics.FLUSH, Topics.RELOAD]);
}

function runAction(dialog: Dialog) {
  let types = [EntityType.Event, EntityType.Task, EntityType.Message, EntityType.Note, EntityType.Person, EntityType.Contract, EntityType.Project];

  const act_types = dialog.actionValues(ActionType.ENTITY_TYPE);
  if (act_types.length > 0) types = act_types;

  const bool = dialog.actionValues(ActionType.BOOL);

  let prompt_topic = dialog.isAuthenticated() ? Topics.DEFAULT : Topics.INIT;

  switch (dialog.topic) {
    /*case Topics.REAUTH:
      if (!config.isEnvOffline() && dialog.isAuthenticated() && !dialog.isGuestAccount()) {
        // ******** - Joe and Omer decided that re-authentication doesn't remove tokens, it will ask for re-consent from the end user
        Promise.all([data.users.save(dialog.user), data.users.save(dialog.user, true)]).catch(err => dialog.asyncError(err));
      }

      prompt_topic = Topics.REGISTER;

      dialog.newDialog({ next_topic:prompt_topic });
      break;*/
    case Topics.UNAUTH:
      prompt_topic = Topics.DEREGISTER;
      break;
    case Topics.REFRESH:
      dialog.updateSession(true, types);
      dialog.newDialog({ next_topic:prompt_topic });
      break;
    case Topics.UPDATE:
      dialog.updateSession();
      dialog.newDialog({ next_topic:prompt_topic });
      break;
    case Topics.FLUSH:
      if (dialog.context.init) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `${JSON.stringify(dialog.context)}`);
        prompt_topic = Topics.FLUSH;
        dialog.flushCaches(types)
        if (dialog.context.init) dialog.context.init.completed = true;
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `flush processing ${prompt_topic} ${JSON.stringify(dialog.context)}`);

        if (dialog.context.init.completed) {
          prompt_topic = Topics.DEFAULT;
          dialog.clearContext('init');
          // dialog.clearPing();
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `flush completed ${prompt_topic} ${JSON.stringify(dialog.context)}`);
        } else {
          prompt_topic = Topics.FLUSH;
          // dialog.quickPing();
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `flush ping ${prompt_topic} ${JSON.stringify(dialog.context)}`);
        }
      }
      dialog.newDialog({ next_topic:prompt_topic });
      break;
    /* case Topics.CLEAR:
      dialog.clearData(types).catch(err => dialog.asyncError(err));
      dialog.newDialog({ next_topic:prompt_topic });
      break;*/
    case Topics.RELOAD:
      if (!dialog.context.init) dialog.context.init = new InitPluginState();
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `${JSON.stringify(dialog.context)}`);
      if (!dialog.context.init.processing) prompt_topic = Topics.RELOAD;
      dialog.runAsync('init', async () => {
        await dialog.reloadData(types, true);
        if (dialog.context.init) dialog.context.init.completed = true;
      });
      dialog.newDialog({ next_topic: Topics.RELOAD });

      if (dialog.isDoneProcessing()) {
        dialog.reset('init');
        dialog.newDialog({ next_topic: Topics.DEFAULT });
        prompt_topic = Topics.RELOAD;
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `reload completed ${prompt_topic} ${JSON.stringify(dialog.context)}`);
      } else {
        prompt_topic = Topics.RELOAD;
        dialog.newDialog({ next_topic: Topics.RELOAD });
        // dialog.quickPing();
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'runAction', dialog.user.profile, `reload ping ${prompt_topic} ${JSON.stringify(dialog.context)}`);
      }
      // dialog.newDialog({ next_topic:prompt_topic });
      break;
    case Topics.LAST_LOAD:
      dialog.newDialog({ next_topic:prompt_topic });
      prompt_topic = Topics.LAST_LOAD;
      break;
    case Topics.EXPORT:
      dialog.people.export();
      dialog.newDialog({ next_topic:prompt_topic });
      break;
    case Topics.RESET:
    case Topics.HARD_RESET:
      {
        const cmd = dialog.actionValues(ActionType.CMD);
        const next_topic = dialog.isAuthenticated() ? Topics.DEFAULT : 
          cmd && cmd.includes('expert') ? Topics.PROJECT_EXPERT : Topics.INIT;
        prompt_topic = /* dialog.checkTopic(Topics.RESET) ? next_topic :*/ Topics.HARD_RESET;
        if (!dialog.isGuestAccount() || (cmd && cmd[0])) dialog.clearContext();
        else dialog.resetContext(dialog.isGuestAccount() ? ['init'] : []);
        dialog.clearCommand();
        // dialog.clearSessionWait();
        dialog.newDialog({ next_topic });
      }
      break;
    case Topics.DELETE_ACCOUNT:
      if (bool && bool.length) {
        if (bool[0]) {
          const provider = dialog.context.init ? dialog.context.init.provider : null; 
          const account = dialog.context.init ? dialog.context.init.account : null;
          if (!config.isEnvOffline()) dialog.user.revokeTokens(provider, account).catch(err => dialog.asyncError(err));
          data.users.deleteAccount(dialog.user.profile, provider, account).catch(err => dialog.asyncError(err));
          
          if (!provider || !account) {
            dialog.newDialog({ next_topic: Topics.LOGOUT });
            prompt_topic = Topics.LOGOUT;
          }

          const user_info = `${dialog.user.name} <${dialog.user.email}> ${dialog.user.profile} deleted account ${provider ? provider : ''}.${account ? account : ''} ${dialog.user.groups ? JSON.stringify(dialog.user.groups) : ''}`;
          mail([{
            Email: '<EMAIL>',
            Name: 'Fora',
          }], [], `${dialog.user.name} deleted ${account ? 'an' : 'their'} account!`, user_info);

          // don't break, go to logout
        } else {
          dialog.reset('init');
          /*prompt_topic = Topics.DEFAULT;
          dialog.newDialog({ next_topic:prompt_topic });*/
          return;
        }
      } else if (dialog.isAuthenticated() && !dialog.isGuestAccount()) {
        dialog.newDialog({ next_topic: Topics.DELETE_ACCOUNT });
        return;
      }
    // don't break
    case Topics.LOGOUT:
      if (!dialog.context.init) {
        dialog.context.init = new InitPluginState();
        const [redirect] = dialog.actionValues(ActionType.CMD);
        if (redirect && ['network', 'expert', 'app'].includes(redirect)) dialog.context.init.redirect = `/${redirect}`;
        else dialog.context.init.redirect = '/';
      }

      dialog.runAsync('init', async () => {
        const init = dialog.context.init;
        await dialog.deleteSession(false).catch(e => dialog.asyncError(e));
        dialog.context.init = init;
        if (dialog.context.init) dialog.context.init.completed = true;
        dialog.setTopic(Topics.LOGOUT);
      });

      // dialog.internalPing();
      // dialog.reRun();
      // dialog.newDialog({ next_topic: Topics.LOGOUT });
      prompt_topic = Topics.LOGOUT;
      break;
  }

  dialog.setTopic(prompt_topic);

  return true;
}

function setPrompt(dialog: Dialog) {
  let curr_dialog = dialog.currentDialog();

  switch (dialog.topic) {
    case Topics.HARD_RESET:
      dialog.clearSticky();
      dialog.setHide();
      // dialog.defaultPing();
      break;
    case Topics.LAST_LOAD:
      dialog.addPrompt(`Loaded cache: ${dialog.loaded}, Last refresh: ${dialog.user.last}`);
      dialog.reset();
      break;
    case Topics.LOGOUT:
      if (dialog.isDoneProcessing()) {
        curr_dialog = dialog.newDialog({ next_topic: Topics.INIT });
        dialog.setRedirect(dialog.context.init.redirect);
        dialog.lastPrompt('init', lang.init.GOODBYE(), {clear: true});
        dialog.setLogout();
      }
      break;
    case Topics.RELOAD:
      if (!dialog.context.init) {
        dialog.addInfo(dialog.getPersonInfo(), true);
        dialog.addPrompt(lang.defaultanswers.ACKNOWLEDGE());
        // dialog.defaultPing();
        dialog.setHide(10000);
      }
      break;
    case Topics.DELETE_ACCOUNT:
      {
        const auth = AuthProvider.clientAuthNameSet(dialog.user.provider);
        const revoke_link = AuthProvider.clientAuthRevokeUrl(dialog.user, dialog.group_host);
        dialog.addPrompt(lang.init.CONFIRM_DELETE(auth && auth.length ? auth[0].name : dialog.user.provider, revoke_link), true);
        // dialog.clearPing();
        dialog.setSticky();
      }
      break;
  }

  if (curr_dialog?.next_topic) dialog.setTopic(curr_dialog.next_topic);
}

export default {
  name: lang.admin.ADMIN_NAME,
  description: lang.admin.ADMIN_DESC,
  examples: lang.help.HELP_EG,
  reserved: ADMIN_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: () => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default);
