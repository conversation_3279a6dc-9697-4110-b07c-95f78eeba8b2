import config from '../config';
import data from '../data';
import lang from '../lang';

import { notifyStep } from '../routes/notify';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType, TemplateType } from '../types/globals';
import { Plugin } from '../types/plugins';
import { NotificationType } from '../types/shared';

import * as funcs from '../utils/funcs';
import { getMailTemplate, mailTemplate } from '../utils/mail';
import parsers from '../utils/parsers';

const LOG_NAME = 'plugins.welcome';
const WELCOME_NAME = ' Welcome';
const WELCOME_DESC = 'Debug welcome email';

TOPIC('WELCOME');

const WELCOME_KWD = ['welcome'];

const TEMPLATES = Object.values(TemplateType);

function keywordMatch(actions, message: string) {
  return config.isEnvDevelopment() && parsers.checkKeyword(message, WELCOME_KWD, true);
}

function setAction(dialog: Dialog, message: string) {
  const run = config.isEnvDevelopment();

  if (run) {
    dialog.addAction(ActionType.CMD, message.split(' ')[1]);
    dialog.setTopic(Topics.WELCOME);
  }

  return run;
}

function isActive(dialog: Dialog) {
  return config.isEnvDevelopment() && dialog.checkTopic(Topics.WELCOME);
}

function runAction(dialog: Dialog) {
  return;
}

function setPrompt(dialog: Dialog) {
  const cmd = dialog.actionValues(ActionType.CMD);
  if (cmd.length) {
    dialog.runAsync('welcome', async () => {
      if (TEMPLATES.includes(cmd[0])) await data.users.onboardingSet(dialog.user, cmd[0] as TemplateType);
      dialog.context.welcome = await notifyStep(dialog.user);
      if (dialog.context.welcome && dialog.context.welcome.template) {
        await data.users.onboardingSet(dialog.user, dialog.context.welcome.next_step);
        if(dialog.context.welcome.template) {
          const profile = funcs.mapURL(`/profile/${dialog.user.vanity}`, dialog.getGroup());

          const project = Object.values(dialog.cache.projects)[0];
          const job_name = project ? project.title : null;
          const job = project ? dialog.projects.getUrl(project, dialog.user.email) : null;

          const params = {
            firstname: dialog.user.name.split(' ')[0],
            profile,
            signature: lang.about.ABOUT_EMAIL_SIGNATURE,
            job_name,
            job,
          }

          dialog.context.welcome.text = await getMailTemplate(NotificationType.Onboarding, dialog.context.welcome.template, params);
          await mailTemplate([{ Email: dialog.user.email, Name: dialog.me ? dialog.me.displayName : dialog.user.name, }], [],
            NotificationType.Onboarding,
            dialog.context.welcome.template,
            params
          );
        }
      } else dialog.context.welcome = {text: `Templates: next, ${TEMPLATES.join(', ')}`};
    });
  } else if (!dialog.message.ping) dialog.context.welcome = {text: `Templates: next, ${TEMPLATES.join(', ')}`};

  if (dialog.context.welcome) {
    dialog.addPrompt(dialog.context.welcome.text, true); 
    dialog.reset('welcome');
    return;
  }
}

export default {
  name: WELCOME_NAME,
  description: WELCOME_DESC,
  examples: [],
  reserved: WELCOME_KWD,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: _a => { return null; },
} as Plugin;

Dialog.registerPlugin(Topics.WELCOME, module.exports.default, true);