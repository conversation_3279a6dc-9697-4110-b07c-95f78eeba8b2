import _ from 'lodash';

import lang from '../lang';

import Learn from '../skills/learn';

import { Action, ActionType } from '../types/globals';
import { Person } from '../types/items';
import { Plugin } from '../types/plugins';
import { EntityType, Relation, TagType, Uid, UndefinedPersonInfo } from '../types/shared';

import { cleanFilter, filterFilter } from '../utils/filter';
import { flatten } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import Dialog, { TOPIC, Topics } from '../session/dialog';

const LOG_NAME = 'plugins.find';

const topics = [
  TOPIC('FIND_PERSON'),
  TOPIC('DISCOVER'),
  TOPIC('DISCOVER_URL'),
]

function keywordMatch(actions: Action[], message: string, raw_message: string): boolean {
  const entities = actions.filter(a => a.type === ActionType.ENTITY);
  const context = entities.map(e => e.context);
  if (context.length && parsers.checkKeyword(message, context, true)) return true;
  return parsers.checkKeyword(message, lang.find.FIND_KWD, true);
}

function isActive(dialog): boolean {
  return dialog.checkTopic(topics);
}

function setAction(dialog: Dialog, message: string): boolean {
  if (dialog.checkTopic(Topics.DISCOVER_URL)) {
    // TODO: get urls from the user, add empty array if there aren't any
    dialog.setTopic(Topics.DISCOVER);
    const urls = dialog.actionValues(ActionType.URL);
    if (urls && dialog.context.find) {
      if (dialog.context.find.urls) dialog.context.find.urls = _.uniq([...dialog.context.find.urls, ...urls]);
      else dialog.context.find.urls = urls;
    } else dialog.context.find.urls = [];
  }

  if (dialog.filters && dialog.filters.find(f => f.conditions.find(c => c.att === EntityType.Person)) ||
    dialog.filters.find(f => f.conditions.find(c => c.att === TagType.skill)) ||
    dialog.actionValues([ActionType.PERSON, ActionType.ENTITY]).length  || dialog.actionValues(ActionType.ENTITY_TYPE).includes(EntityType.Person)) {

    // remove keywords from skill
    dialog.filters.forEach(filter => {
      filter.name = parsers.skillExtract(filter.name);
      filter.conditions.forEach(c => {
        if(c.att === TagType.skill && c.value.length && typeof c.value[0] === 'string') {
          c.value = (c.value as string[]).map(parsers.skillExtract).filter(v => v.length);
        }
      });
    });

    const events = dialog.filters.filter(f => f.conditions.map(c => c.att).includes(TagType.event));
    const people = dialog.actionValues(ActionType.PERSON);
    const skills = flatten(dialog.filters.map(f => flatten(f.conditions.filter(c => c.att === TagType.skill).map(c => c.value as string[]))));
    
    if (!events.length && !people.length && !skills.length && parsers.checkKeyword(message, lang.whats_next.SUGGEST_KWD)) 
      dialog.setTopic(Topics.SUGGEST);
    else if(!isActive(dialog)) dialog.setTopic(Topics.FIND_PERSON);

    dialog.removeActions(ActionType.ENTITY, [...lang.find.FIND_KWD, ...lang.find.FIND_WORDS].map(name => { return {name}}));

    return true;
  }

  return isActive(dialog);
}

function runAction(dialog: Dialog) {
  if (!dialog.context.find) {
    const init_urls = dialog.actionValues(ActionType.URL);
    dialog.context.find = { 
      // match_people: [],
      urls: init_urls && init_urls.length ? init_urls : null,
    };
  }

  if (dialog.checkTopic(Topics.DISCOVER)) {
    // learn about the fitler person or match_people
    const filter_people = flatten(dialog.filters.map(f => flatten(f.conditions.filter(c => c.att === EntityType.Person).map(c => c.value as string[]))).filter(v => v));
    if (filter_people.includes('self'))  {
      dialog.clearFilters();
      dialog.resetProcessing();
      if (!dialog.context.find.discover_people || !dialog.context.find.discover_people.find(p => p.self)) {
        dialog.context.find.discover_people = [new Person(dialog.me)];
      }
    }

    if(dialog.context.find.discover_people.length) {
      if (dialog.context.find.discover_people.find(p => p.self)) {
        // learn self
        if (!dialog.context.find.urls) {
          // prompt for urls
          dialog.setTopic(Topics.DISCOVER_URL);
          return;
        }

        dialog.runAsync('find', async () => {
          // add urls then learn
          if (dialog.context.find.urls?.length) {
            const me = new Person(dialog.me);
            const urls = dialog.context.find.urls.map(parsers.findUrl).filter(u => u);
            if (urls.length) {
              me.urls = _.uniq([...me.urls, ...urls]);
              await dialog.setSelf(me);
            }
          }

          const learn = new Learn(dialog.user);
          let person = await learn.learnPerson(new Person(dialog.me), false, true);
          person = await dialog.people.save(person);
          if (dialog.context.find) dialog.context.find.learned_people = [person];
        });
      } else  {
        // learn match people 
        dialog.runAsync('find', async () => {
          const people = await Learn.learnPeople(dialog.user, dialog.context.find.discover_people.map(p => new Person(p)), true);
          if (dialog.context.find) dialog.context.find.learned_people = people;
        });
      }
    } // else break and go match people

    if (dialog.context.find && dialog.isDoneProcessing()) {
      const curr_dialog = dialog.currentDialog();
      const last_topic = curr_dialog ? curr_dialog.next_topic : undefined;
      dialog.addReplies(dialog.context.find.learned_people);
      if (last_topic) {
        dialog.clearContext('find');
        dialog.setTopic(last_topic);
        dialog.resetProcessing();
      }
      return;
    } else if(dialog.isProcessing()) return;
  }

  dialog.runAsync('find', async () => {
    const context = dialog.actions.map(a => a.context ? a.context.toLowerCase() : '');
    const check_people = flatten<UndefinedPersonInfo>(dialog.actionValues(ActionType.ENTITY).map(a => a.people));
    let check_names  = dialog.actionValues(ActionType.ENTITY).filter(a => !a.people || !a.people.length).map(a => a.name);

    // dialog.makeFilters()
    logging.infoFP(LOG_NAME, 'runAction', dialog.user.profile, `Updating filters ${JSON.stringify(dialog.filters)} message: "${dialog.message.message}" context: "${context}" actions: ${JSON.stringify(dialog.actions)} check_people: ${JSON.stringify(check_people)} check_names: ${JSON.stringify(check_names)}`);

    await dialog.search.updateSearchFilters(dialog.filters, dialog.message.message, context, dialog.actions, check_people, check_names, true);

    // grap person ids
    const people_filters = dialog.filters.filter(f => f.conditions?.find(c => c.att === TagType.person && c.rel === Relation['=']));
    const pfi = people_filters.map(f => f.id);
    dialog.filters = dialog.filters.filter(f => !pfi.includes(f.id));

    if(dialog.context.find) {
      const ids = flatten<Uid>( people_filters.map(f => 
            flatten<Uid>(f.conditions.filter(c => c.att === TagType.person && c.rel === Relation['=']).map(c => c.value as Uid[]))));
      const people = await dialog.people.byId(ids);
      dialog.context.find.match_people = people;
    }
  });

  if (dialog.context.find && dialog.isDoneProcessing()) {
    if(dialog.checkTopic(Topics.FIND_PERSON)) {
      // NOOP
      if (dialog.context.find.match_people) dialog.addReplies(dialog.context.find.match_people);
    } else dialog.resetProcessing();
  }
}

function setPrompt(dialog: Dialog) {
  if (dialog.checkTopic(Topics.DISCOVER_URL)) {
    dialog.addPrompt(lang.find.DISCOVER_URL, true, true, true);
  } else if(dialog.checkTopic(Topics.DISCOVER)) {
    if (!dialog.isProcessing(false) || dialog.isDoneProcessing()) {
      const replies = dialog.getLastRepliesType(EntityType.Person);
      if (replies && replies.length) {
        dialog.lastPrompt('find', lang.find.DISCOVER_SUCCESS);
        dialog.addInfo(replies.map(p => dialog.getPersonInfo(p)));
      }
    }
  } else if (!dialog.isProcessing(false) || dialog.isDoneProcessing()) {
    const people = dialog.getRepliesType(EntityType.Person);
    if (people.length) {
      dialog.lastPrompt('find', lang.find.FIND_SUCCESS(people.length), { clear: true });
      dialog.addInfo(people.map(p => dialog.getPersonInfo(p)));
    } else {
      let filters = dialog.filters.slice();
      filters.forEach(cleanFilter);
      filters = filters.filter(filterFilter);
      if(filters.length) dialog.lastPrompt('find', lang.find.SEARCHING, { clear: true });
      else  dialog.lastPrompt('find', lang.find.FIND_NONE, { clear: true });
    }
   
    dialog.reset();
  }
}

export default {
  name: lang.find.FIND_PERSON,
  description: lang.find.FIND_DESC,
  examples: lang.find.FIND_EG,
  reserved: lang.find.FIND_RESERVED,
  requiresAuth: true,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: () => { return null; },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);

