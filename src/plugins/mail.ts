import Dialog, { TOPIC, Topics } from '../session/dialog';
import { Plugin } from '../types/plugins';

import lang from '../lang';

import { ActionType } from '../types/globals';

import * as format from '../utils/format';
import mail from '../utils/mail';
import parsers from '../utils/parsers';

const MAIL_NAME = 'Mail';
const LOG_NAME = 'plugins.mail';

const topics = [
  TOPIC('MAIL'),
  TOPIC('MEET'),
]

function setAction(dialog: Dialog, message: string, raw_message: string) {
  if (parsers.checkKeyword(message, lang.mail.MAIL_KWD, true)) {
    const act_people = dialog.actionValues(ActionType.PERSON);
    if (dialog.message.data  && dialog.message.data && dialog.message.data.message.length && act_people && act_people.length) {
      dialog.setTopic(Topics.MAIL);
      return true;
    }
  }

if (parsers.checkKeyword(message, lang.mail.MEET_KWD, true)) {
    const act_people = dialog.actionValues(ActionType.PERSON);
    if (dialog.message.data  && dialog.message.data && dialog.message.data.message.length &&
        dialog.message.data.dates && dialog.message.data.dates.length
        && act_people && act_people.length) {
      dialog.setTopic(Topics.MEET);
      return true;
    }
  }

  return dialog.checkTopic(topics);
}

function runAction(dialog: Dialog) {
  const act_people = dialog.actionValues(ActionType.PERSON);
  const data = dialog.message.data;

  switch(dialog.topic) {
    case Topics.MAIL:
      dialog.runAsync('mail', async () => {
        const people = await dialog.people.byId(act_people.map(p => p.id).filter(id => id));
        await Promise.all(people.map(async p => {
          const emails = parsers.findEmail(p.comms);
          const recipient = {
            Email: emails && emails.length ? emails[0] : undefined,
            Name: p.displayName
          }

          const cc = p.network ? undefined : [{Name: dialog.me.displayName, Email: dialog.user.email }];
          const replyto = {Name: dialog.me.displayName, Email: dialog.user.email };

          const message = `${dialog.user.name} asked me to share the following message:

  ${data.message}
          
  ${lang.about.ABOUT_EMAIL_SIGNATURE}`

          if (recipient.Email && recipient.Email.length) await mail([recipient], cc, lang.mail.MAIL_SUBJECT(dialog.me.displayName), message, replyto);
        }));
      });
      break;
    case Topics.MEET:
      dialog.runAsync('mail', async () => {
        const times = data.dates ? data.dates.map(d => `${format.localeDowMonthDay(d.start, dialog.user.locale, dialog.user.timeZone)} between ${format.localeHour(d.start, dialog.user.locale, dialog.user.timeZone)} and ${format.localeHour(d.end, dialog.user.locale, dialog.user.timeZone)}`).join('\n') : '';

        const people = await dialog.people.byId(act_people.map(p => p.id).filter(id => id));
        await Promise.all(people.map(async p => {
          const emails = parsers.findEmail(p.comms);
          const recipient = {
            Email: emails && emails.length ? emails[0] : undefined,
            Name: p.displayName
          }

          const cc = p.network ? undefined : [{Name: dialog.me.displayName, Email: dialog.user.email }];
          const replyto = {Name: dialog.me.displayName, Email: dialog.user.email };

          const message = `${dialog.user.name} asked me to help find some time to meet.

  ${data.message}
  
  Are you available any of these times?

  ${times}
          
  ${lang.about.ABOUT_EMAIL_SIGNATURE}`

          if (recipient.Email && recipient.Email.length) await mail([recipient], cc, lang.mail.MEET_SUBJECT(dialog.me.displayName), message, replyto);
        }));
      });
      break;
  }
}

function setPrompt(dialog: Dialog) {
  if (dialog.isDoneProcessing()) {
    dialog.lastPrompt('mail', lang.mail.SENT, { clear: true });
  }
}

export default {
  name: MAIL_NAME,
  description: lang.mail.MAIL_DESC,
  examples: [],
  reserved: [],
  requiresAuth: false,
  keywordMatch: (a, m, r) => {
    return parsers.checkKeyword(m, lang.mail.MAIL_KWD, true) || parsers.checkKeyword(m, lang.mail.MEET_KWD, true);
  },
  setAction,
  isActive: (dialog: Dialog) => {
    return dialog.checkTopic(topics);
  },
  runAction,
  setPrompt,
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default);
