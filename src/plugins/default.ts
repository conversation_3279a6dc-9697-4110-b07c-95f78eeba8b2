import config from '../config';
import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { ActionType } from '../types/globals';
import { Plugin } from '../types/plugins';
import { AuthClientNameInfo } from '../types/shared';

import { MINUTES } from '../utils/datetime';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import { AuthProvider } from '../auth/auth_provider';

const DEFAULT_NAME = 'Default';
const DEFAULT_DESC = 'The default action - say hello';

const DEFAULT_RESERVED = lang.defaultanswers.HELLO_KWD;

const LOG_NAME = 'plugins.default';

const topics = [TOPIC('DEFAULT'), TOPIC('HELLO')];

function keywordMatch(actions, message, raw_message) {
  // ignore skills
  const bool = Dialog.getActionValues(actions, ActionType.BOOL);
  const skills = Dialog.getActionValues(actions, ActionType.SKILL);
  const dates = Dialog.getActionValues(actions, ActionType.DATE);
  const cmd = Dialog.getActionValues(actions, ActionType.CMD);
  const entity_type = Dialog.getActionValues(actions, ActionType.ENTITY_TYPE);
  const ignore_entity_acts = actions.filter(a => a.type !== ActionType.ENTITY || (a.people && a.people.length));
  if (ignore_entity_acts.length === skills.length + dates.length + cmd.length + entity_type.length + bool.length) return true;
  else logging.warnF(LOG_NAME, 'keywordMatch', `DEFAULT plugin set no topic for "${raw_message}" because of ${JSON.stringify(actions)}`);
}

function setAction(dialog: Dialog, message: string, raw_message: string): boolean {
  const bool = dialog.actionValues(ActionType.BOOL);
  const skills = dialog.actionValues(ActionType.SKILL);
  const dates = dialog.actionValues(ActionType.DATE);
  const cmd = dialog.actionValues(ActionType.CMD);
  const entity_type = dialog.actionValues(ActionType.ENTITY_TYPE);
  const entity = dialog.actionValues(ActionType.ENTITY);
  const ignore_actions_count = skills.length + dates.length + cmd.length + entity_type.length + entity.length + bool.length;

  // handle returning from FAQ
  /*if (dialog.checkTopic([Topics.DEFAULT, Topics.FAQ]) && (!dialog.isAuthenticated() || dialog.isGuestAccount()) && !dialog.isMaskedTopic()) {
    const start_project = Dialog.getTopicPlugin(Topics.PROJECT).setAction(dialog, message, raw_message);
    logging.infoFP(LOG_NAME, 'setAction', dialog.user.profile, `Should start project for ${message}: ${start_project}`);
    if (start_project) {
      if(!dialog.isGuestAccount()) dialog.createGuest();
      return true;
    }
  }*/

  if (dialog.actions.length === ignore_actions_count) {
    if (parsers.checkKeyword(message, lang.defaultanswers.HELLO_KWD, true)) dialog.setTopic(Topics.HELLO);
    else dialog.setTopic(dialog.isAuthenticated() ? Topics.DEFAULT : Topics.INIT);
  }

  return true;
}

function setPrompt(dialog: Dialog) {
  if (dialog.message && dialog.message.message && dialog.message.message.length && !dialog.message.command && dialog.checkTopic(Topics.DEFAULT)) {
    if (dialog.isAuthenticated()) {
      // authenticated
      if (dialog.filters && dialog.filters.length) {
        dialog.setRefresh();
      } else if (parsers.isQuestion(dialog.message.message)) {
        const lastd = dialog.lastDialog();
        if (lastd && lastd.topic === Topics.DEFAULT) dialog.addPrompt(lang.defaultanswers.ANSWER_QUESTION(dialog.user.name, dialog.message.offset), true);
        else {
          dialog.addPrompt(lang.defaultanswers.ACKNOWLEDGE_QUESTION(), true);
          dialog.addAnswers(lang.defaultanswers.QUESTION_ANSWERS);
        }
      }
      else if(dialog.message.ping !== 0) dialog.addPrompt(lang.defaultanswers.ACKNOWLEDGE(), true);
      else {
        dialog.addPrompt(lang.defaultanswers.DEFAULT_HELP, true);
        dialog.addAnswers(lang.defaultanswers.DEFAULT_HELP_ANSWERS);
      }
      dialog.logMessage();
      dialog.clearSticky();
      dialog.setHide(lang.defaultanswers.HIDE_QUIP);
    } else if (config.get('BETA') && (!dialog.user || !config.productionEnabled(dialog.user.email))) {
      dialog.addPrompt(lang.init.BETA_LINK(config.get('BETA')), true);
    } else if (parsers.checkKeyword(dialog.message.message, lang.admin.RESET, true)) {
      dialog.addPrompt(lang.defaultanswers.ACKNOWLEDGE(), true);
      dialog.clearSticky();
    } else {
      dialog.logMessage();
      // not authenticated
      const providers: AuthClientNameInfo[] = AuthProvider.clientAuthNameSet(null, dialog.group_host);
      // dialog.addPrompt(lang.defaultanswers.ACKNOWLEDGE(), true);
      dialog.addPrompt(lang.defaultanswers.PLEASE_CONNECT(providers.map(p => p.name)), true);
      dialog.addAnswers(providers.map(p => p.name)); //.concat([lang.defaultanswers.PRIVACY]));
      dialog.clearSticky();
    }
    // dialog.defaultPing();
  } else if (dialog.isAuthenticated()) {
    const int_msg = dialog.getInternalMessages();
    if (int_msg && int_msg.length) {
      dialog.lastPrompt(undefined, int_msg, {clear: true});
      // dialog.defaultPing();
    } else {
      /* const int_notify = dialog.getInternalNotifications();
      if (int_notify && int_notify.length) {
        dialog.newDialog();
        for (const notify of int_notify) {
          let prompt = null;
          if (notify.prompt) prompt = notify.prompt;
          else if(notify.webpush && notify.webpush.notification) prompt = funcs.promptInfo(`${notify.webpush.notification.body}`, notify.webpush.notification.click_action, 'Click here to see it.', null, true);
          if (prompt) dialog.addPrompt(prompt, false, true);
        }
        dialog.defaultPing();
        return;
      } */
    }

    // if we haven't pinged in 30 min...
    const friendly = parseInt(config.get('FRIENDLY', 30), 10);
    if (!dialog.message.ping || MINUTES(dialog.lastChat(), friendly && !isNaN(friendly) ? friendly : 30) < new Date()) {
      if (dialog.isGuestAccount()) {
        // not authenticated
        const providers = AuthProvider.clientAuthNameSet(null, dialog.group_host);
        dialog.addPrompt(lang.defaultanswers.FIRST_PROJECT(providers.map(p => p.name)), true);
        dialog.addHint(lang.defaultanswers.HELP_HINT);
        const answers = lang.defaultanswers.HELP_ANSWERS.slice().concat(providers.map(p => p.name));
        dialog.addAnswers(answers);
      } else {
        // authenciate
        dialog.addPrompt(lang.defaultanswers.SAY_HELLO(dialog.user.name, dialog.message.offset), true);
        dialog.addAnswers('Tip');
        dialog.setHide(lang.defaultanswers.HIDE_QUIP);
      }
    }
  } else if (!dialog.message.ping) {
    if (config.get('BETA') && (!dialog.user || !config.productionEnabled(dialog.user.email))) dialog.addPrompt(lang.defaultanswers.ACKNOWLEDGE_NEGATIVE(), true);
    else {
      // not authenticated
      const providers = AuthProvider.clientAuthNameSet(null, dialog.group_host);
      // dialog.addPrompt(lang.defaultanswers.ACKNOWLEDGE(), true);
      dialog.addPrompt(lang.defaultanswers.PLEASE_CONNECT(providers.map(p => p.name)), true); 
      dialog.addAnswers(providers.map(p => p.name)); //.concat([lang.defaultanswers.PRIVACY]));
    }
  }

  // dialog.defaultPing();
}

export default {
  name: DEFAULT_NAME,
  description: DEFAULT_DESC,
  examples: [],
  reserved: DEFAULT_RESERVED,
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive: d => {
    return false;
  },
  runAction: d => {
    return false;
  },
  setPrompt,
  shortcuts: a => {
    return null;
  },
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default);
