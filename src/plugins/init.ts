import { AuthProvider } from '../auth/auth_provider';
import config from '../config';
import lang from '../lang';

import Dialog, { TOPIC, Topics } from '../session/dialog';

import { NormalizedProviderSettings } from '../types/auth';
import { ActionType } from '../types/globals';
import { InitPluginState, Plugin } from '../types/plugins';
import { AuthContext, AuthProviders, EntityType } from '../types/shared';

import logging from '../utils/logging';
import parsers from '../utils/parsers';

const INIT_NAME = 'Init';
const INIT_DESC = 'Initialize and authenticate';
const LOG_NAME = 'plugins.init';

function INIT_SHORTCUT(a) {
  if (!a) return { name: 'Connect with Fora', value: 'Connect with Fora' };
  else return null;
}

const topics = [
  TOPIC('INIT'),
  // TOPIC('INIT_BETA'),
  TOPIC('INIT_PROFILE'),
  TOPIC('INIT_CONTRACTOR'),
  TOPIC('INIT_PROJECT'),
  TOPIC('INIT_CONNECT'),
  TOPIC('DEREGISTER'),
  TOPIC('REGISTER'),
  TOPIC('REGISTER_CONNECT'),
  TOPIC('REGISTER_REAUTH'),
  TOPIC('REGISTER_EMAIL'),
  TOPIC('REGISTER_EMAIL_FAIL'),
  TOPIC('REGISTER_CODE'),
  TOPIC('REGISTER_EMAIL_REDIRECT'),
  // TOPIC('REGISTER_MOBILE'),
  TOPIC('REGISTER_NETWORK'),
  TOPIC('REGISTER_NO_EMAIL'),
  TOPIC('REGISTER_NO_ORGANIZER'),
  TOPIC('REG_ERROR'),
  TOPIC('REG_ERROR_NO_PROVIDER'),
  TOPIC('REG_ERROR_CANCELLED'),
  TOPIC('REG_ERROR_NO_EMAIL'),
  TOPIC('REG_ERROR_NO_EMAIL_CANCELLED'),
  TOPIC('REG_ERROR_NO_ORGANIZER'),
  TOPIC('REG_ERROR_NO_ORGANIZER_CANCELLED'),
];

function keywordMatch(actions, message, _raw_message) {
  return !message.length || parsers.checkKeyword(message, lang.help.FAQ_START, true) || parsers.checkKeyword(message, lang.help.HELP_CONNECT, true, true) || message === lang.help.HELP_CONNECT_ME;
}

function setAction(dialog: Dialog, message, raw_message) {
  if (parsers.checkKeyword(message, lang.help.FAQ_START, true)) {
    dialog.setTopic(Topics.FAQ);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.LEARN_MORE, true)) {
    dialog.setTopic(Topics.CLICK_TUTORIAL);
    return true;
  }

    /* if (config.get('BETA') && (!dialog.user || !config.productionEnabled(dialog.user.email))) {
    const bool = dialog.actionValues(ActionType.BOOL);
    if (bool && bool.length) {
      if (bool[0]) dialog.setTopic(Topics.INIT_BETA);
      else dialog.setTopic(Topics.FAQ);
      return true;
    }
  } else */ 
  if (parsers.checkKeyword(message, lang.help.HELP_CONNECT, true, true) || message === lang.help.HELP_CONNECT_ME) {
    dialog.setTopic(Topics.HELP_CONNECT);
    return true;
  }

  if (parsers.checkKeyword(message, lang.help.HELP_PROFILE, true)) {
    dialog.addAction(ActionType.BOOL, true);
    dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Settings);
    dialog.setTopic(Topics.SETTINGS);
    dialog.setTopic(Topics.SETTINGS);
    return true;
  }

  // only process if not authenticated or masked
  if (!dialog.isAuthenticated() && (isActive(dialog) || !dialog.isMaskedTopic()) &&
      (!dialog.group_host || dialog.group_host.provider !== AuthProviders.Saml)) {
    // don't set to init if it's in the Init flow already
    if (keywordMatch([], message, raw_message) && !isActive(dialog)) {
      dialog.setTopic(Topics.INIT);
      return true;
    } else if ( // !config.get('BETA') && 
      !dialog.checkTopic(Topics.REGISTER_CONTRACTOR) &&
      (!dialog.checkTopic(Topics.INIT_PROFILE) || dialog.actionValues(ActionType.ENTITY).length || dialog.actionValues(ActionType.PROJECT).length) && 
      (!dialog.user || !config.productionEnabled(dialog.user.email))) {

      if(dialog.checkTopic(Topics.INIT_PROFILE) &&
          (dialog.actionValues(ActionType.ENTITY).length || dialog.actionValues(ActionType.PROJECT).length)) {
        const start_project = Dialog.getTopicPlugin(Topics.PROJECT).setAction(dialog, message, raw_message);
        if (start_project) return;
      }

      if (parsers.checkKeyword(message, lang.init.INIT_PROJECT)) {
        dialog.clearContext('project');
        dialog.clearActions(ActionType.SKILL);
        dialog.setTopic(Topics.PROJECT);
        return true;
      } else if(parsers.checkKeyword(message, lang.init.INIT_EXPERT)) {
        dialog.clearContext('project');
        dialog.clearActions(ActionType.SKILL);
        dialog.setTopic(Topics.PROJECT_EXPERT);
        return true;
      } else if (parsers.checkKeyword(message, lang.init.INIT_CONTRACTOR)) {
        dialog.clearContext('project');
        dialog.setTopic(Topics.INIT_CONTRACTOR);
        return true;
      } else if(parsers.checkKeyword(message, lang.project.PROJECT_KWD)) {
        dialog.clearContext('project');
        return false;
      }
    }
  }

 if (dialog.checkTopic([Topics.INIT]) && (!dialog.isAuthenticated() || dialog.isGuestAccount())) {
    const help = Dialog.getTopicPlugin(Topics.HELP).setAction(dialog, message, raw_message);
    if (help) return true;
  }

  return isActive(dialog);
}

function isActive(dialog: Dialog): boolean {
  return dialog.checkTopic(topics);
}

function runAction(dialog: Dialog) {
  const bool = dialog.actionValues(ActionType.BOOL);

  switch (dialog.topic) {
    case Topics.INIT:
      if (!dialog.message.message.length && (!dialog.context.init || !dialog.context.init.provider)) {
        dialog.addPrompt(lang.init.WELCOME, true);
        // dialog.addAnswers(lang.init.WELCOME_ANSWERS);
        // dialog.addHint(lang.init.WELCOME_HINT);
        dialog.setTopic(Topics.REGISTER);
      }
      // dialog.clearPing();
      break;
    case Topics.INIT_CONTRACTOR:
      if (dialog.context.contractor && dialog.context.contractor.profile) dialog.setTopic(Topics.REGISTER_CONTRACTOR);
      else dialog.createGuest();
      break;
    // case Topics.REGISTER_NO_EMAIL:
    case Topics.REGISTER_CONNECT:
      // dialog.clearPing();
      dialog.setTopic(Topics.REGISTER);
      break;
    case Topics.REG_ERROR_NO_EMAIL:
      if (dialog.isAuthenticated() && !dialog.isGuestAccount()) dialog.setTopic(Topics.TUTORIAL);
      else dialog.setTopic(Topics.REG_ERROR_NO_EMAIL);
      break;
    // case Topics.REGISTER_NO_ORGANIZER:
    case Topics.REG_ERROR_NO_ORGANIZER:
      if (dialog.isAuthenticated() && !dialog.isGuestAccount()) dialog.setTopic(Topics.TUTORIAL);
      else dialog.setTopic(Topics.REG_ERROR_NO_ORGANIZER);
      break;
    case Topics.REGISTER_EMAIL:
      if (dialog.context.email_login) {
        if(dialog.context.email_login.sent) {
          // dialog.clearPing();
          dialog.setTopic(Topics.REGISTER_CODE);
        } else if(dialog.context.email_login.failed) {
          // dialog.clearPing();
          delete dialog.context.email_login;
          dialog.setTopic(Topics.REGISTER_EMAIL_FAIL);
        } // else dialog.quickPing();
      } else {
        dialog.context.email_login = {};
        dialog.runAsync('email_login', async () => {
          await dialog.createGuest(true);
          const emails = parsers.findEmail(dialog.message.message.toLowerCase());
          if (emails && emails.length) {
            try {
              await AuthProvider.email.generateCode(dialog, emails[0], dialog.context.email_login);
              dialog.context.email_login.sent = true;
            } catch(e) {
              if (dialog.context.email_login) dialog.context.email_login.failed = true;
              else dialog.asyncError(e);
            }
          } else dialog.context.email_login.failed = true;
        });
      }
      break;
    case Topics.REGISTER_CODE:
      if (dialog.context.email_login && dialog.actionValues(ActionType.NUMBER).length &&
        dialog.context.email_login.seed && dialog.context.email_login.factor) {
        const code_acts = dialog.actionValues(ActionType.NUMBER);
        const tokens = AuthProvider.email.tokensFromCode(code_acts[0], 
          dialog.user.email,
          dialog.context.email_login.seed,
          dialog.context.email_login.factor,
          dialog.group_host);

        if (tokens) dialog.setTopic(Topics.REGISTER_EMAIL_REDIRECT);
        else {
          delete dialog.context.email_login.seed;
          delete dialog.context.email_login.factor;
          delete dialog.context.email_login;
          dialog.setTopic(Topics.REGISTER_EMAIL_FAIL);
        }
      }
      break;
    case Topics.REG_ERROR:
      {
        const provider = dialog.context.init && dialog.context.init.provider ? dialog.context.init.provider : dialog.user.provider;
        const account = dialog.context.init && dialog.context.init.account ? dialog.context.init.account : dialog.user.profile;
        const context = dialog.context.init && dialog.context.init.auth_context ? dialog.context.init.auth_context : AuthProvider.getAuthContext(dialog.user, provider, account,  dialog.group_host);
        const topic = AuthProvider.connectedTopic(provider, context, dialog.group_host, dialog.user);
        dialog.setTopic(topic);
        // if (dialog.isAuthenticated() && !dialog.isGuestAccount()) dialog.setTopic(Topics.TUTORIAL);
        // else dialog.setTopic(Topics.REG_ERROR);
      }
      break;
    case Topics.DEREGISTER:
      if (bool && bool[0]) {
        if (dialog.context.init && dialog.context.init.provider) {
          const defaults = AuthProvider.getDefaultProviderSettings(dialog.context.init.provider);
          let provider_settings = dialog.group_host ? dialog.group_host.providerSettings(dialog.context.init.provider) : defaults;
          if (!provider_settings) provider_settings = defaults;
          const tokens = dialog.user.getTokens(provider_settings.provider, dialog.context.init.account);
          dialog.context.init.processing = true;
          dialog.user.deleteAccount(provider_settings.provider, dialog.context.init.account);
          if (!dialog.user.hasAccount(provider_settings.provider, dialog.context.init.account)) dialog.user.removeAccountSetting(provider_settings.provider);

          if (tokens) 
            dialog.runAsync('init', async () => {
              try { 
                await AuthProvider.revokeToken(provider_settings.provider, dialog.user.profile, tokens, provider_settings);
              } catch(e) {
                logging.warnFP(LOG_NAME, 'runAction', dialog.user.profile, `Error revoking tokens for ${provider_settings.provider}`, e);
              }
              if (dialog.context.init) dialog.context.init.completed = true;
            });
          else if (dialog.context.init) dialog.context.init.completed = true;
          break;
        }
      } else if(dialog.context.init && dialog.context.init.processing) {
        if (!dialog.context.init.completed) break;
      }
      dialog.clearContext('init');
      break;
  }
  return true;
}

function setPrompt(dialog: Dialog) {
  dialog.clearCommand();
  let provider_settings: NormalizedProviderSettings = null;
  let auth_context = AuthProvider.getAuthContext(dialog.user, dialog.user.provider, dialog.user.profile, dialog.group_host); // AuthContext.AuthChat;
  if (dialog.context.init && dialog.context.init.provider) {
    const default_settings = AuthProvider.getDefaultProviderSettings(dialog.context.init.provider);
    provider_settings = dialog.group_host ? dialog.group_host.providerSettings(dialog.context.init.provider) : default_settings;
    if (!provider_settings) provider_settings = default_settings;
    if (dialog.context.init.auth_context) auth_context = dialog.context.init.auth_context;
    else auth_context = AuthProvider.getAuthContext(dialog.user, provider_settings.provider, dialog.context.init.account, dialog.group_host); 
  } else if (dialog.context.init) {
    dialog.context.init.topic = dialog.topic;
    if (dialog.context.init.auth_context) auth_context = dialog.context.init.auth_context;
  }
  else dialog.context.init = { topic: dialog.topic } as InitPluginState;

  if (dialog.message.ping) {
    dialog.clearDialog(true); // don't need to keep sending messages but keep the topic
    return;
  }

  const provider_names = provider_settings ? AuthProvider.clientAuthNameSet(provider_settings.provider, dialog.group_host) :
    AuthProvider.clientAuthSet(auth_context, dialog.group_host);
  const names = provider_names.map(p => p.name);

  const account = dialog.context.init && dialog.context.init.account ? dialog.context.init.account : dialog.user.profile;
  let current_context = dialog.isAuthenticatedNonGuest() ? AuthProvider.getAuthContext(dialog.user, dialog.context.init.provider, account, dialog.group_host) : null;

  switch (dialog.topic) {
    case Topics.INIT: {
      if (parsers.checkKeyword(dialog.message.message, lang.init.HELP) ||
        (dialog.group_host && dialog.group_host.provider === AuthProviders.Saml)) {
        dialog.addPrompt(lang.init.WELCOME_HELP(names), true);
        dialog.addAnswers(lang.help.HOW_TO_ANSWERS);
        dialog.setTopic(Topics.FAQ);
      } else if (!dialog.message.message.length) {
        if (dialog.context.init.provider) {
          const auth_context = AuthProvider.clientAuthContext(current_context ? current_context : AuthContext.App, dialog.context.init.provider, dialog.group_host, dialog.isAuthenticatedNonGuest() ? dialog.user.email : null);
          dialog.addPrompt(lang.init.REG_REQUIRED(auth_context.name), true);
          dialog.addPrompt(lang.init.PERMISSIONS_BASIC());
          dialog.addAnswers(lang.init.REG_ANSWERS);
          if (dialog.isAuthenticatedNonGuest()) dialog.addQuickReply(lang.init.REG_CONNECT, [], {redirect: auth_context.url});
          else dialog.addQuickReply(lang.init.REG_CONNECT, lang.init.REGISTER_REPLY_REDIRECT(auth_context), { redirect: auth_context.url });
          dialog.setSticky();
        } else {
          dialog.addPrompt(lang.init.WELCOME, true);
          dialog.addAnswers(lang.init.WELCOME_ANSWERS);
          dialog.addHint(lang.init.WELCOME_HINT);
          dialog.setTopic(Topics.REGISTER);
        }

        // dialog.clearPing();
      } else {
        dialog.addPrompt(lang.defaultanswers.PLEASE_CONNECT(names), true);
        dialog.addAnswers(names);
        dialog.clearSticky();
        const auth_set = AuthProvider.clientAuthSet(current_context ? current_context : AuthContext.App, dialog.group_host);
        for (const auth of Object.values(auth_set)) {
          dialog.addQuickReply(auth.name, 'Just a sec...', { redirect: auth.url });
        }
      }
      break;
    }
    case Topics.INIT_CONTRACTOR:
      dialog.setTopic(Topics.CONTRACTOR_REGISTER);
      dialog.addPrompt(lang.contractor.INTRO, true);
      dialog.addAnswers(lang.project.SUGGEST_SKILLS);
      // dialog.noPing();
      // dialog.internalPing();
      break;
    /* case Topics.INIT_BETA:
      dialog.addPrompt(lang.init.BETA_LINK(config.get('BETA')));
      break; */
    case Topics.INIT_PROFILE:
      if (dialog.context.init && dialog.context.init.profile) {
        const profile = dialog.getPersonInfo(dialog.context.init.profile);
        profile.vanity = dialog.context.init.vanity;
        profile.card = dialog.context.init.card;
        profile.size = dialog.context.init.size;
        dialog.addInfo(profile, true);
      } 
      dialog.setHide();
      dialog.reset('init');
      break
    case Topics.INIT_CONNECT:
      dialog.lastPrompt('init', lang.init.CONFIRM_CONNECT, {clear: true, hide: 10000});
      break;
    case Topics.REGISTER_EMAIL_REDIRECT:
      if (dialog.actionValues(ActionType.NUMBER).length) {
        dialog.session.seed = dialog.context.email_login.seed;
        dialog.session.factor = dialog.context.email_login.factor;
        const code_acts = dialog.actionValues(ActionType.NUMBER);
        dialog.addPrompt('Just a sec...', true);
        dialog.setRedirect(`/goauth?code=${code_acts[0]}&state=${dialog.context.init.auth_context ? dialog.context.init.auth_context : AuthContext.App} ${AuthProviders.Email} ${dialog.group_host ? dialog.group_host.id : ''}`);
        if (dialog.context.init.topic) dialog.setTopic(dialog.context.init.topic);
        else dialog.setTopic(Topics.REGISTER);
      }
      break;
    case Topics.REGISTER_EMAIL:
      // dialog.quickPing();
      break;
    case Topics.REGISTER_EMAIL_FAIL:
      if (!dialog.isProcessing() || dialog.isDoneProcessing()) { // hasPing()) {
        dialog.addPrompt(lang.init.REGISTER_EMAIL_FAIL, true);
        dialog.setTopic(Topics.REGISTER_EMAIL);
      }
      break;
    case Topics.REGISTER_CODE:
      dialog.addPrompt(lang.init.REGISTER_CODE, true);
      break;
    case Topics.REGISTER:
      if (dialog.context.init && dialog.context.init.provider === AuthProviders.Email) {
        dialog.addPrompt(lang.init.REGISTER_EMAIL, true);
        dialog.setTopic(Topics.REGISTER_EMAIL);
      } else if (provider_settings) {
        // dialog.addPrompt(AuthProvider.promptRegister(AuthContext.AuthChat, dialog.group_host, provider_settings, dialog.user), true);
        dialog.setRedirect(AuthProvider.urlRedirect(auth_context, dialog.group_host, provider_settings, dialog.user));
        dialog.setTopic(Topics.DEFAULT);
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
        const auth_set = AuthProvider.clientAuthSet(auth_context, dialog.group_host);
        for (const auth of Object.values(auth_set)) {
          dialog.addQuickReply(auth.provider, 'Just a sec...', auth.url ? { redirect: auth.url } : { command: auth.command } );
          if (auth.provider !== auth.name.toLowerCase()) {
            dialog.addQuickReply(auth.name.toLowerCase(), 'Just a sec...', auth.url ? { redirect: auth.url } : { command: auth.command } );
          }
        }
        dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.DEREGISTER:
      if (dialog.context.init && dialog.context.init.processing && !dialog.context.init.completed) {
        // dialog.quickPing();
      } else if (provider_settings) {
        dialog.addPrompt(lang.init.CONFIRM_DISCONNECT(names[0]), true);
        // dialog.noPing();
      } else dialog.lastPrompt('init', lang.init.DISCONNECTED, { clear: true });
      break;
    /*case Topics.REGISTER_MOBILE:
      if (provider_settings) {
        // dialog.addPrompt(AuthProvider.promptRegister(AuthContext.AuthMobile, dialog.group_host, provider_settings, dialog.user), true);
        dialog.setRedirect(AuthProvider.urlRedirect(AuthContext.AuthMobile, dialog.group_host, provider_settings, dialog.user));
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
        const auth_set = AuthProvider.clientAuthSet(AuthContext.AuthMobile, dialog.group_host);
        for (const auth of Object.values(auth_set)) {
          dialog.addQuickReply(auth.provider, 'Just a sec...', { redirect: auth.url });
        }
      }
      dialog.setTopic(Topics.DEFAULT);
      break;*/
    case Topics.REGISTER_NO_EMAIL:
      if (provider_settings) {
        // dialog.addPrompt(AuthProvider.promptRegister(AuthContext.App, dialog.group_host, provider_settings, dialog.user), true);
        dialog.setRedirect(AuthProvider.urlRedirect(AuthContext.App, dialog.group_host, provider_settings, dialog.user));
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
        const auth_set = AuthProvider.clientAuthSet(AuthContext.App, dialog.group_host);
        for (const auth of Object.values(auth_set)) {
          dialog.addQuickReply(auth.provider, 'Just a sec...', { redirect: auth.url });
        }
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REGISTER_NETWORK:
      if (provider_settings) {
        // dialog.addPrompt(AuthProvider.promptRegister(AuthContext.App, dialog.group_host, provider_settings, dialog.user), true);
        dialog.setRedirect(AuthProvider.urlRedirect(AuthContext.Network, dialog.group_host, provider_settings, dialog.user));
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
        const auth_set = AuthProvider.clientAuthSet(AuthContext.Network, dialog.group_host);
        for (const auth of Object.values(auth_set)) {
          dialog.addQuickReply(auth.provider, 'Just a sec...', { redirect: auth.url });
        }
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REGISTER_NO_ORGANIZER:
      if (provider_settings) {
        // dialog.addPrompt(AuthProvider.promptRegister(AuthContext.App, dialog.group_host, provider_settings, dialog.user), true);
        dialog.setRedirect(AuthProvider.urlRedirect(AuthContext.App, dialog.group_host, provider_settings, dialog.user));
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
        const auth_set = AuthProvider.clientAuthSet(AuthContext.App, dialog.group_host);
        for (const auth of Object.values(auth_set)) {
          dialog.addQuickReply(auth.provider, 'Just a sec...', { redirect: auth.url });
        }
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REGISTER_REAUTH:
      if (current_context) { if (logging.isDebug(dialog.user.profile)) logging.debugF(LOG_NAME, 'setPrompt', `Current context = '${current_context}'`); }
      else {
        current_context = AuthContext.App;
        if (logging.isDebug(dialog.user.profile)) logging.debugF(LOG_NAME, 'setPrompt', `Current context not set, defaulting to '${current_context}'`);
      }

      if (provider_settings) {
        // dialog.addPrompt(AuthProvider.contextPermissions(current_context), true);
        // dialog.addPrompt(AuthProvider.promptRegister(current_context, dialog.group_host, provider_settings, dialog.user), true);
        dialog.setRedirect(AuthProvider.urlRedirect(current_context, dialog.group_host, provider_settings, dialog.user));
        // dialog.addAnswers(lang.defaultanswers.PRIVACY);
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);

        const auth_set = AuthProvider.clientAuthSet(current_context, dialog.group_host);
        for (const auth of Object.values(auth_set)) {
          dialog.addQuickReply(auth.provider, 'Just a sec...', { redirect: auth.url });
        }
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REG_ERROR:
      if (provider_settings) {
        const email = dialog.user ? dialog.user.email : null;
        const url = AuthProvider.clientAuthUrl(AuthContext.AuthChat, dialog.group_host, provider_settings, email, true);
        dialog.addPrompt(lang.init.REG_ERROR(url, names[0]), true);
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REG_ERROR_CANCELLED:
      if (provider_settings) {
        const email = dialog.user ? dialog.user.email : null;
        const url = AuthProvider.clientAuthUrl(AuthContext.AuthChat, dialog.group_host, provider_settings, email, true);
        dialog.addPrompt(lang.init.REG_ERROR_CANCELLED(url, provider_settings.provider), true);
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REG_ERROR_NO_EMAIL:
      if (provider_settings) {
        const email = dialog.user ? dialog.user.email : null;
        const url = AuthProvider.clientAuthUrl(AuthContext.App, dialog.group_host, provider_settings, email, true);
        dialog.addPrompt(lang.init.REG_ERROR(url, names[0]), true);
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REG_ERROR_NO_EMAIL_CANCELLED: {
        const email = dialog.user ? dialog.user.email : null;
        const url = AuthProvider.clientAuthUrl(AuthContext.AuthChat, dialog.group_host, provider_settings, email, true);
        dialog.addPrompt(lang.init.REG_ERROR(url, names[0]), true);

        if (provider_settings) {
          const email = dialog.user ? dialog.user.email : null;
          const url = AuthProvider.clientAuthUrl(AuthContext.App, dialog.group_host, provider_settings, email, true);
          dialog.addPrompt(lang.init.REG_ERROR_CANCELLED(url, provider_settings.provider), true);
        } else {
          if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
          else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
          dialog.addAnswers(names);
        }
        dialog.setTopic(Topics.DEFAULT);
      }
      break;
    case Topics.REG_ERROR_NO_ORGANIZER:
      if (provider_settings) {
        const email = dialog.user ? dialog.user.email : null;
        const url = AuthProvider.clientAuthUrl(AuthContext.App, dialog.group_host, provider_settings, email, true);
        dialog.addPrompt(lang.init.REG_ERROR(url, names[0]), true);
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REG_ERROR_NO_ORGANIZER_CANCELLED:
      if (provider_settings) {
        const email = dialog.user ? dialog.user.email : null;
        const url = AuthProvider.clientAuthUrl(AuthContext.App, dialog.group_host, provider_settings, email, true);
        dialog.addPrompt(lang.init.REG_ERROR_CANCELLED(url, provider_settings.provider), true);
      } else {
        if (dialog.isAuthenticatedNonGuest()) dialog.addPrompt(lang.init.CONNECT_PICK(names), true);
        else dialog.addPrompt(lang.init.REGISTER_PICK(names), true);
        dialog.addAnswers(names);
      }
      dialog.setTopic(Topics.DEFAULT);
      break;
    case Topics.REG_ERROR_NO_PROVIDER:
      if (dialog.context.init) dialog.lastPrompt('init', lang.init.REG_WRONG_PROVIDER(dialog.context.init.provider, dialog.context.init.existing));
      else dialog.lastPrompt('init', lang.init.REG_WRONG_PROVIDER());
      dialog.setTopic(Topics.DEFAULT);
  }

  if (dialog.checkTopic(Topics.DEFAULT)) dialog.clearContext('init');
}

export default {
  name: INIT_NAME,
  description: INIT_DESC,
  examples: [],
  reserved: [],
  requiresAuth: false,
  keywordMatch,
  setAction,
  isActive,
  runAction,
  setPrompt,
  shortcuts: INIT_SHORTCUT,
} as Plugin;

Dialog.registerPlugin(topics, module.exports.default, true);
