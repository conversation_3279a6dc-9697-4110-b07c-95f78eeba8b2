/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';

import config from '../../config';

import lang from '../../lang';

import Dialog from '../../session/dialog';
import ForaUser from '../../session/user';

import { InternalError, TemplateType } from '../../types/globals';
import { Project } from '../../types/items';
import { AuthProviders, Info, NotificationType, Reply } from '../../types/shared';

import { getContractUrl, hash } from '../../utils/funcs';
import { contractInfo } from '../../utils/info';
import logging from '../../utils/logging';
import parsers from '../../utils/parsers';
import peopleUtils from '../../utils/people';
import { getDocument } from '../../utils/sign';

import stripe from '../../sources/stripe_controller';

import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { AbstractController } from './a_controller';

/**
 * Notes:
 * - https://stackoverflow.com/questions/26982996/trying-to-proxy-an-image-in-node-js-express
 */
class ContractsController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:contracts');
  log_name = 'rest.controllers.ContratsController';

  constructor() {
    super();
    this.contractAck = this.contractAck.bind(this);
    this.contractsGet = this.contractsGet.bind(this); // Express oddity, make sure this methods you call from Express know what "this" is
    this.contractPost = this.contractPost.bind(this);
    this.contractLoad = this.contractLoad.bind(this);
    this.contractSigned = this.contractSigned.bind(this); 
    this.contractDeclined = this.contractDeclined.bind(this); 
    this.contractError = this.contractError.bind(this); 
  }

  contractAck(req: express.Request, res: express.Response, next: express.NextFunction) {
    res.status(200);
    res.send('');
    res.end();
  }

  async contractsGet(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      if(dialog.isAuthenticatedNonGuest()) {
        let contract = dialog.cache.contracts[req.params.contract_id];
        if (!contract) contract = await dialog.contracts.load(req.params.contract_id);
        if (contract && contract.hash) {
          const doc = await getDocument(contract.hash);
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'contractsGet', dialog.user.profile, `Loaded contract size ${doc.length} for ${contract.hash}`);
          if (doc) {
            const name = contract.client_id === dialog.user.profile ? contract.contractor_name : contract.client_name;

            res.status(200);
            res.contentType('application/pdf');
            res.setHeader('Content-Disposition', `attachment; filename=AskFora_Service_Agreement_${name.replace(' ', '_')}.pdf`);
            res.setHeader('Content-Length', doc.length);
            res.send(doc);
            res.end();

            return;
          }
        }
      }
    }
    res.sendStatus(404);
  }

  async contractPost(req: express.Request, res: express.Response): Promise<void> {
    return this.contractLoad(req, res);
  }

  async contractLoad(req: express.Request, res: express.Response, next?: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'create', dialog ? dialog.user.profile : 'UNKNOWN_PROFILE', 'Missing one of group, user or session_id', null);
      res.status(500).json({ code: 500, error: 'Internal Server Error' }).end();
      return;
    }

    // Get our Dialog (created by UserAuthValidationHandler)
    if (!dialog) {
      logging.errorFP(this.log_name, 'create', dialog.user.profile, `Unable to find session ${session_id}`, null);
      res.status(500).json({ code: 500, error: 'Internal Server Error' }).end();
      return;
    }

    let contract;

    try {
      contract = dialog.cache.contracts[req.params.contract_id];
      if (!contract) contract = await dialog.contracts.load(req.params.contract_id);
      if (contract && contract.hash) req['contract'] = contract;
    } catch(err) {
      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
      if (err instanceof InternalError) {
        const fe = err as InternalError;
        res.status(fe.code).json({ error: err.message }).end();
        return;
      } else {
        res
          .status(500)
          .json({ error: config.isEnvDevelopment() ? err.message : 'Internal Server Error' })
          .end();
        return;
      }

    }

    if (next) next();
    else if(contract) {
     res.json(contractInfo(dialog.user.profile, contract, getContractUrl(dialog.getGroup(), contract.id, false))).end();
    } else res.status(404).end();
  }

  async contractSigned(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const contract = req['contract'];
    if (dialog && contract) {
      if (dialog.user.profile === contract.client_id) {
        if (contract.client_signed) {
          await dialog.clearInternalNotifications(NotificationType.Contract_Ready);
          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.json({}).end();
        } else { 
          contract.client_signed = true;
          await dialog.contracts.save(contract);
          await dialog.contracts.notify(contract);

          let project_contractor;
          let ask_deposit;
          let project_amount;
          let project_service_fee;
          let project_ask_deposit;

          const projects = (Object.values(dialog.cache.projects) as Project[]).filter(p => p.contract === contract.id);
          if (contract && projects.length) {
            for (const project of projects) {
              project_contractor = project.contractor;
              if (!project.select_notice) {
                const url = dialog.contracts.getSignUrl(contract.id, contract.contractor_email);
                const subject = lang.project.SELECTED_SUBJECT(project);
                const message = lang.project.SELECTED_MESSAGE(project, project_contractor, url, !contract.contractor_signed);

                project.select_notice = true;
                project_ask_deposit = project.escrow === null || project.escrow === undefined;
                project_amount = project.fee;
                if (project.rate !== 'fixed') project_amount *= project.duration;
                project.last_update = new Date();
                project.last_activity = project.last_update;
                project_service_fee = project_amount * project.service_fee;

                await dialog.projects.update(project, true);

                const notify_email = {
                  rcpts: [{ Name: contract.contractor_name, Email: contract.contractor_email }],
                  subject,
                  message,
                };

                const notification = project.escrow || !contract.contractor_signed ? NotificationType.Project_Selected : NotificationType.Contract_Ready;
                let template = contract.contractor_signed ? TemplateType.Ready : null;
                if (project_contractor.ready) template = TemplateType.NoStripe;

                await dialog.projects.notify(project, new ForaUser(project_contractor.askfora_id), project_contractor, notify_email, { group: dialog.getNotifyGroup(contract.contractor_email), notification, template});

              } else if(!project_contractor.ready) {
                const group_email = dialog.getGroup(dialog.groupByEmail(contract.contractor_email));
                const url = stripe.stripeConnectUrl(group_email)
                const subject = lang.project.SELECTED_SUBJECT(project);
                const message = lang.project.SELECTED_MESSAGE(project, project_contractor, url, !contract.contractor_signed);

                project.select_notice = true;
                project_ask_deposit = project.escrow === null || project.escrow === undefined;
                project.last_update = new Date();
                project.last_activity = project.last_update;

                await dialog.projects.update(project, true);

                const notify_email = {
                  rcpts: [{ Name: contract.contractor_name, Email: contract.contractor_email }],
                  subject,
                  message,
                };

                await dialog.projects.notify(project, new ForaUser(project_contractor.askfora_id), project_contractor, notify_email, { group: dialog.getNotifyGroup(contract.contractor_email), notification: NotificationType.Contract_Signed, message: lang.stripe.CONNECT_STRIPE});
              } else if(contract.client_signed && contract.contractor_signed && project_contractor.ready && !project.escrow) {
                const person = project.client;
                const emails = parsers.findEmail(person.comms);
                const to_email = emails && emails.length ? emails[0] : null;

                const email = {
                  rcpts: [{ Name: person.displayName, Email: to_email }],
                  subject: lang.project.DEPOSIT_SUBJECT(project_contractor),
                  message: lang.project.DEPOSIT_MESSAGE(project, project_contractor, dialog.projects.getUrl(project, to_email)),
                };

                await dialog.projects.notify(project, new ForaUser(person.askfora_id), null, email, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Ready}).catch(e => dialog.asyncError(e));
              } 
            }
          }

          // TODO: fix
          let reply: Reply[] = [lang.contract.CONTRACT_COMPLETED(contract.contractor_name.split(' ')[0])];
          if (project_contractor) {
            if (project_contractor.ready) {
              reply = reply.concat(lang.project.CLIENT_START_PROMPT(project_ask_deposit, project_amount, project_service_fee));
            } else {
              reply.push(lang.project.CLIENT_WAIT_READY(project_contractor.nickName));
            }
          }

          const pi = projects.map(p => peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, p.client && p.client.askfora_id === dialog.user.profile))
          for (const p of pi) p.updated = true;

          const curr_dialog = dialog.currentDialog();
          const response = {
            id: curr_dialog  && (curr_dialog.info?.length || curr_dialog.replies?.length) ? curr_dialog.id : hash(new Date().getTime().toString()),
            reply, 
            info:([contractInfo(dialog.user.profile, contract, getContractUrl(dialog.getGroup(), contract.id, false))] as Info[]).concat(pi),
            answers: [],
            page: 0,
          };

          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.json(response).end();
        }
      } else if (dialog.user.profile === contract.contractor_id) {
        if (contract.contractor_signed) {
          await dialog.clearInternalNotifications(NotificationType.Contract_Ready);
          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.json({}).end();
        } else {
          contract.contractor_signed = true;
          await dialog.contracts.save(contract);

          let project_client;
          let project;
          const projects = (Object.values(dialog.cache.projects) as Project[]).filter(p => p.contract === contract.id);
          if (contract && projects.length) {
            project = projects[0];
            project_client = project.client;
          }

          await dialog.contracts.notify(contract, project);
           
          // TODO: fix
          const client_name = project_client ? 
          project_client.nickName : contract.client_name.split(' ')[0];
          const ready = dialog.user.hasAccount(AuthProviders.Stripe);
          const answers: string[] = ready ? [] : [lang.project.CONNECT_PAYMENT];
          const hint = ready ? undefined : lang.project.CONNECT_PAYMENT;
          const reply: Reply[] = [
            lang.contract.CONTRACT_COMPLETED(client_name),
            lang.project.SELECTED_CONTRACTOR_SETUP_STRIPE(client_name, ready),
          ];

          const curr_dialog = dialog.currentDialog();
          const response = {
            id: curr_dialog  && (curr_dialog.info?.length || curr_dialog.replies?.length) ? curr_dialog.id : hash(new Date().getTime().toString()),
            reply, 
            info:([contractInfo(dialog.user.profile, contract, getContractUrl(dialog.getGroup(), contract.id, false))] as Info[]).concat(projects.map(p => peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, p.client && p.client.askfora_id === dialog.user.profile))),
            hint,
            answers,
            page: 0,
          };

          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.json(response).end();
        }
      } else res.sendStatus(500).json({error: 'Invalid contract'});
    } else res.sendStatus(404).json({error: 'Contract not found'});
  }

  async contractDeclined(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const contract = req['contract'];
    if (dialog && contract) {
      if (dialog.user.profile === contract.client_id) {
        if (contract.client_declined) {
          await dialog.clearInternalNotifications(NotificationType.Contract_Ready);
          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.json({}).end();
        } else {
          contract.client_declined = true;
          await dialog.contracts.save(contract);
          await dialog.contracts.notify(contract);

          const reply: Reply[] = [lang.contract.CONTRACT_DECLINED(contract.contractor_name.split(' ')[0])];
  
          const curr_dialog = dialog.currentDialog();
          const response = {
            id: curr_dialog  && (curr_dialog.info.length || curr_dialog.replies.length) ? curr_dialog.id : hash(new Date().getTime().toString()),
            reply, 
            info: [contractInfo(dialog.user.profile, contract, getContractUrl(dialog.getGroup(), contract.id, true))],
            answers: [],
            page: 0,
          };

          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.json(response).end();
        } 
      } else if (dialog.user.profile === contract.contractor_id) {
        if (contract.contractor_declined) {
          await dialog.clearInternalNotifications(NotificationType.Contract_Ready);
          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.json({}).end();
        } else {
          contract.contractor_declined = true;
          await dialog.contracts.save(contract);
          await dialog.contracts.notify(contract);

          const reply: Reply[] = [lang.contract.CONTRACT_DECLINED(contract.client_name.split(' ')[0])];
          const curr_dialog = dialog.currentDialog();
          const response = {
            id: curr_dialog  && (curr_dialog.info.length || curr_dialog.replies.length) ? curr_dialog.id : hash(new Date().getTime().toString()),
            reply, 
            info: [contractInfo(dialog.user.profile, contract, getContractUrl(dialog.getGroup(), contract.id, true))],
            answers: [],
            page: 0,
          };

          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.json(response).end();
        }
      } else res.sendStatus(500).json({error: 'Invalid contract'});
    } else res.sendStatus(404).json({error: 'Contract not found'});
  }

  async contractError(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const contract = req['contract'];
    if (dialog && contract) {
      if (dialog.user.profile === contract.client_id || dialog.user.profile === contract.contractor_id) {
        const reply: Reply[] = [lang.contract.CONTRACT_SIGNING_ERROR(dialog.contracts.getSignUrl(contract.id, dialog.user.email))];

        const curr_dialog = dialog.currentDialog();
        const response = {
          id: curr_dialog  && (curr_dialog.info.length || curr_dialog.replies.length) ? curr_dialog.id : hash(new Date().getTime().toString()),
          reply, 
          info: [contractInfo(dialog.user.profile, contract, getContractUrl(dialog.getGroup(), contract.id, true))],
          answers: [],
          page: 0,
        };

        await dialog.saveSession(true).catch(e => dialog.asyncError(e));
        res.json(response).end();
      } else res.sendStatus(500).json({error: 'Invalid contract'});
    } else res.sendStatus(404).json({error: 'Contract not found'});
  }
}

// Build our router/controller and map them
const router = express.Router();
const controller = new ContractsController();

router.route('/signed').get(controller.contractAck);
router.route('/declined').get(controller.contractAck);

router.route('/:contract_id')
  .get(new GroupAuthValidation().route, new UserAuthValidation({read_only: true}).route, controller.contractsGet);
router.route('/:contract_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({read_only: true}).route, controller.contractPost);
router.route('/sign/:contract_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({read_only: false}).route, controller.contractLoad, controller.contractSigned);
router.route('/signed/:contract_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({read_only: false}).route, controller.contractLoad, controller.contractSigned);
router.route('/declined/:contract_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({read_only: false}).route, controller.contractLoad, controller.contractDeclined);
router.route('/error/:contract_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({read_only: false}).route, controller.contractLoad, controller.contractError);

export default router;