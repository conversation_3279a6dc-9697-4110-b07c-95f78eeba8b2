/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import data from '../../data';
import Dialog from '../../session/dialog';
import { StartInfo } from '../../types/shared';
import logging from '../../utils/logging';
import UserAuthValidation from '../auth/user_auth_validation';
import { AbstractController } from './a_controller';

class ContentController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:contacts');
  log_name = 'rest.controllers.ContentController';

  constructor() {
    super();
    this.content = this.content.bind(this);
  }

  async content(req: express.Request, res: express.Response): Promise<void> {
    let start;
    if (req.params.path) {
      start = await data.content.contentByPath(req.params.path);
    } else if(req.query.id) {
      start = await data.content.contentById(req.query.id as string);
    }

    if (start) {
      if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

      let dialog: Dialog = req['dialog'];
      try {
        if (dialog) {
          if(dialog.isAnonymousAccount()) {
            await dialog.saveSession();
            dialog = await Dialog.loadSession('content', req.session, req.cookies, {offset:req.body.offset, read_only: false, no_cache: true, create_new:true, check_tokens:false, stack:Error().stack});
            await dialog.createGuest(true);
            req['dialog'] = dialog;
          }
          await dialog.saveSession();
        }
      } catch(err) {
        logging.errorFP(this.log_name, 'post', dialog && dialog.user ? dialog.user.profile : null, 'Content error', err);
        if (dialog) await dialog.saveSession();
      }
 
      res.json({
        path: start.path,
        title: start.title,
        subtitle: start.subtitle,
        content: start.content,
        skills: start.skills,
        project: start.project,
        link_to: start.link_to,
        link_text: start.link_text,
      } as StartInfo).end();
    } else res.status(404).end();
  }
}

const router = express.Router();
const controller = new ContentController();

router.route('/:path').get(new UserAuthValidation({read_only: true}).route, controller.content);

export default router;