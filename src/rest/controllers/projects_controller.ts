/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { ValidationError, Validator } from 'class-validator';
import express from 'express';
import { JsonConvert, OperationMode, ValueCheckingMode } from 'json2typescript';
import _ from 'lodash';
import util from 'util';
import { v4 as uuid } from 'uuid';

import config from '../../config';
import data from '../../data';
import lang from '../../lang';

import Dialog from '../../session/dialog';
import ForaUser from '../../session/user';
import { FORA_PROFILE } from '../../types/user';

import { InternalError, Jab, TemplateType } from '../../types/globals';
import { Group } from '../../types/group';
import { Candidate, Person, Project, Tag, projectPerson } from '../../types/items';
import { ANONYMOUS_ID, AuthProviders, EntityType, NotificationType, ProjectCandidateState, ProjectInfo, ProjectRate, ProjectSourcingType, TagType, Uid } from '../../types/shared';

import { HOURS, MINUTES, getEndDate } from '../../utils/datetime';
import { arraysIntersect, checkState, flatten, hash, saveOneTypeValue } from '../../utils/funcs';
import logging from '../../utils/logging';
import parsers from '../../utils/parsers';
import peopleUtils from '../../utils/people';

import { AuthProvider } from '../../auth/auth_provider';
import { PROJECT_DATE_FIELDS, PROJECT_SKILLS_FIELD, UPDATE_PROJECT_DESC_FIELDS } from '../../plugins/project';
import { SERVICE_FEE } from '../../routes/gopay';
import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { CandidateDto, ProjectDto, TemplateDto } from '../models/project';
import { AbstractController } from './a_controller';

class ProjectController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:projects');
  log_name = 'rest.controllers.ProjectController';

  constructor() {
    super();

    this.findCandidate = this.findCandidate.bind(this);

    this.create = this.create.bind(this); // Express oddity, make sure this methods you call from Express know what "this" is
    this.get = this.get.bind(this);
    this.search = this.search.bind(this);
    this.share = this.share.bind(this);
    this.select = this.select.bind(this);
    this.unselect = this.unselect.bind(this);
    this.propose = this.propose.bind(this);
    this.update = this.update.bind(this);
    this.network = this.network.bind(this);
    this.template = this.template.bind(this);
    this.repeat = this.repeat.bind(this);
    this.delete = this.delete.bind(this);

    this.accept = this.accept.bind(this);
    this.decline = this.decline.bind(this);

    this.complete = this.complete.bind(this);
    this.progress = this.progress.bind(this);
    this.rename = this.rename.bind(this);

    this.answer = this.answer.bind(this);
    this.shared = this.shared.bind(this);
    this.group = this.group.bind(this);

    this.getJab = this.getJab.bind(this);
    this.createJab = this.createJab.bind(this);
    this.deleteJab = this.deleteJab.bind(this);
  }

  findCandidate(dialog: Dialog, project: Project, add = true): Partial<Candidate> {
    for (const candidate of project.candidates) {
      if (candidate.askfora_id === dialog.user.profile) return candidate;
      if (candidate?.comms.includes(dialog.user.email)) return candidate;
      if (_.intersection(candidate.comms, project.me_candidate.comms).length > 0) return candidate;
    }

    if (!add) return null;

    const add_candidate = projectPerson(project, dialog.me, project.me_candidate);
    add_candidate.comms = [dialog.user.email];
    add_candidate.id = null;

    project.candidates.push(add_candidate);
    return add_candidate;
  }

  async answer(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      // check req.params.project_id
      if (req.params.project_id) {
        const project = await dialog.projects.get(new Project({ id: req.params.project_id }));
        if (project && project.candidates) {
          const me_candidate: Partial<Candidate> = project.candidates.find(c => c.askfora_id === dialog.user.profile);
          // user must have answered (ACCEPTED)
          if (me_candidate && me_candidate.state === ProjectCandidateState.ACCEPTED) {
            const now = new Date();
            project.last_update = now;
            project.last_activity = project.last_update;
            // expecting { answer?: string, is_public?: boolean, groups?: Uid[], remove? : boolean }
            const { answer, is_public, groups, remove }: { answer?: string, is_public?: boolean, groups?: Uid[], remove?: boolean } = req.body;

            let notification;

            let updated = false;

            if (remove === true) {
              me_candidate.answer = null;
              me_candidate.public = false;
              me_candidate.state = ProjectCandidateState.VIEWED;
              me_candidate.groups = null;
              updated = true;
            } else {
              if (answer && answer.length && answer !== me_candidate.answer) {
                me_candidate.answer = answer;
                notification = { group: dialog.getNotifyGroup(), notification: NotificationType.Project_Update, template: TemplateType.Answer}
                updated = true;
              }

              if (is_public === true) {
                if (!me_candidate.public) {
                  me_candidate.public = true;
                  notification = { group: dialog.getGroup(), notification: NotificationType.Project_Update, template: TemplateType.Public}
                  updated = true;
                }
              }
              else if (is_public === false) {
                if (me_candidate.public) {
                  me_candidate.public = false;
                  notification = { group: dialog.getGroup(), notification: NotificationType.Project_Update, template: TemplateType.Private}
                  updated = true;
                }
              }

              if (groups) {
                const new_groups = groups.filter(g => g in dialog.user.groups);
                if (((!me_candidate.groups || !me_candidate.groups.length ) && new_groups.length) ||
                  (me_candidate.groups && me_candidate.groups.length && new_groups.length !== me_candidate.groups.length) || 
                  (me_candidate.groups && _.intersection(me_candidate.groups, new_groups).length !== new_groups.length)) {
                  me_candidate.groups = new_groups;
                  notification = { group: dialog.getGroup(), notification: NotificationType.Project_Update, template: new_groups.length ? TemplateType.Shared : TemplateType.Private}
                  updated = true;
                }
              }
            }

            const updated_project = await dialog.projects.update(project, false);
            await dialog.projects.updateShared(project);
            await dialog.projects.updateClient(updated_project);

            if (notification) {
              const to_user = new ForaUser(project.client.askfora_id);
              await data.users.init(to_user, false);
              const to_email = to_user.email ? [to_user.email] : parsers.findEmail(project.client.comms);

              const email = {
                rcpts: [{ Name: project.client.displayName, Email: to_email && to_email.length ? to_email[0] : null }],
                subject: 'AskFora Request Update',
                message: 'AskFora Request Update',
              };

              notification.group = dialog.getNotifyGroup(email.rcpts[0].Email);

              await dialog.projects.notify(project, to_user, project.me_candidate, email, notification).catch(e => dialog.asyncError(e));
            }
          
            await dialog.saveSession();
            res.status(204).end();
            return;
          }
        }
      }
      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async search(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      if (dialog.isAuthenticated() && !dialog.isGuestAccount()) {
        if (req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);
          if (project && project.skill_set && project.skill_set) {
            project.searched_skills = null;
            project.suggested = null;
            project.searching = true;
            project.last_update = new Date();
            project.last_activity = project.last_update;

            const people = await dialog.projects.searchCandidates(project, project.client && project.client.askfora_id !== dialog.user.profile ? [project.client.askfora_id] : undefined).catch(err => {
              logging.errorFP(this.log_name, 'search', dialog.user.profile, `Error searching for candidates for ${project.id}`, err);
              dialog.asyncError(err);
            })

            if (people) logging.infoFP(this.log_name, 'search', dialog.user.profile, `${project.id} Finished searching for candidates ${people.length}`);
 
            if (!project.expert) {
              const connections = await dialog.people.findIntroductions(project.searched_skills ? project.searched_skills : project.skills.split(' '), 20);
              const candidate_comms = [
                  ...project.candidates ? flatten(project.candidates.map(c => c.comms)) : [],
                  ...people ? flatten(people.map(c => c.comms)) : []];
              project.suggested = connections.filter(c => !arraysIntersect(candidate_comms, c.comms)).map(c => `profile/${c.vanity}`);
            }

            if (people) {
              logging.infoFP(this.log_name, 'search', dialog.user.profile, `${project.id} Found introductions for second candidates ${project.suggested ? project.suggested.length : 0}`);
              project = await dialog.projects.saveSearchCandidates(project, people);
            } else {
              project.last_update = new Date();
              project.searching = false;
              if(project.suggested && project.suggested.length) await dialog.projects.update(project);
            }
          } 

          logging.infoFP(this.log_name, 'search', dialog.user.profile, `${project.id} Done searching`);
          await dialog.saveSession();
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
          res.json(project_info).end();
          return;
        }
      }

      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async share(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      if (dialog.isAuthenticated() && !dialog.isGuestAccount()) {
        if (req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);

          let draft_note = req.body.note as string;

          const json_convert = new JsonConvert();
          let candidates;
          try {
            candidates = await dialog.people.loadPeople((req.body.candidates as CandidateDto[]).map(c => json_convert.deserializeObject(c, CandidateDto)));
          } catch(e) {
            logging.warnFP(this.log_name, 'share', dialog.user.profile, 'parse error', e);
            await dialog.saveSession().catch(e => dialog.asyncError(e));
            res.status(422).json({code: 422, error: 'Unable to parse incoming candidates'}).end();
            return;
          }

          const has_candidates = [...project.candidates.map(c => c.id), ...project.candidates.map(c => c.vanity)].filter(x => x);
          const missing_candidates = candidates.filter(c => !has_candidates.includes(c.id) && (!c.vanity || !has_candidates.includes(c.vanity)));
          project.candidates = [...project.candidates, ...missing_candidates.map(c => projectPerson(project, c))];

          const send_ids = [...candidates.map(c => c.id), ...candidates.map(c => c.vanity)].filter(x => x);

          const send_candidates = project.candidates.filter(c => send_ids.includes(c.id) || (c.vanity && send_ids.includes(c.vanity)));

          const notification_type = project.expert ? NotificationType.Project_Expert : NotificationType.Project_Invite;
          let template;
          if (project.expert) template = null;
          else if(project.rate === ProjectRate.sourcing) template = TemplateType.Sourcing;
          else if (draft_note?.length) template = TemplateType.Custom
          else if(project.escrow) template = TemplateType.NoFee;
          else if(project.rate === ProjectRate.fixed) template = TemplateType.Fixed;
          else template = null;

          await Promise.all(send_candidates.map(async person => {
            const user = person.askfora_id ? new ForaUser(person.askfora_id) : null;
            if (user) await data.users.init(user, false);
            const to_email = user && user.email ? [user.email] : parsers.findEmail(person.comms);
            if (to_email && to_email.length) {
              if (person.refer_by && person.refer_by.length) {
                for (const referrer of person.refer_by) {
                  const refer_user = referrer.askfora_id ? new ForaUser(referrer.askfora_id) : null;
                  if (refer_user) await data.users.init(refer_user, false);

                  const referrals = await data.projects.findReferral(refer_user, project.id);
                  const referral = referrals ? referrals.find(referral => referral.referrer === referrer.askfora_id && referral.candidate && referral.candidate.id === person.id) : null;
                  if (referral) {
                    logging.infoFP(this.log_name, 'share', dialog.user.profile, `Skipping repeat referral from ${referrer.askfora_id} for ${person.id} project ${project.id} because of referral ${referral.id}`);
                    continue;
                  }

                  if (refer_user) await data.projects.saveReferral(refer_user, project.id, person);

                  const remail = refer_user && refer_user.email ? [refer_user.email] : parsers.findEmail(referrer.comms);
                  const email = {
                    rcpts: [{ Name: referrer.displayName, Email: remail[0] }],
                    ccs: [{ Name: dialog.me.displayName, Email: dialog.user.email }],
                    subject: project.expert ? lang.project.EXPERT_SUBJECT(project) : lang.project.INVITE_SUBJECT(project),
                    message: draft_note ?
                      draft_note.replace(/\(candidate\)/g, person.displayName) : null,
                  }

                  await dialog.projects.notify(project, refer_user, person, email, { group: dialog.getNotifyGroup(remail[0]), notification: notification_type, template, referrer }).catch(e => dialog.asyncError(e));
                }
              } else if (!person.network || person.vanity || person.askfora_id) {
                const email = {
                  rcpts: [{ Name: person.displayName, Email: to_email[0] }],
                  ccs: person.network ? [] : [{ Name: dialog.me.displayName, Email: dialog.user.email }],
                  subject: project.expert ? lang.project.EXPERT_SUBJECT(project) : lang.project.INVITE_SUBJECT(project),
                  message: draft_note,
                }
                await dialog.projects.notify(project, new ForaUser(person.askfora_id), person, email, { group: dialog.getNotifyGroup(to_email[0]), notification: notification_type, template }).catch(e => dialog.asyncError(e));
              } else logging.warnFP(this.log_name, 'share', dialog.user.profile, `Not sending invitation to ${person.id} for project ${project.id}`);
            }
          }));

          await dialog.saveSession();
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
          res.json(project_info).end();
          return;
        }

      }

      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async propose(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      if (dialog.isAuthenticated() && !dialog.isGuestAccount()) {
        if (req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);

          // let draft_note = req.body.note as string;
        
          await dialog.saveSession();
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
          res.json(project_info).end();
          return;
        }

      }

      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async accept(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      try {
        if (dialog.isAuthenticatedNonGuest() && req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);
          if (project?.client?.self && project.candidates) {
            // proposal
            project.accepted = true;
            project.last_update = new Date();
            project.last_activity = project.last_update;
 
            let contract = await dialog.projects.setupContract(project);
            // handle skip contract
            if (contract) {
              contract = await dialog.contracts.create(contract);
              project.contract = contract.id;

              if (!contract.client_reviewed || !contract.contractor_reviewed ||
                !contract.client_signed || !contract.contractor_signed) {
                await dialog.projects.notifySelected(project, contract);
              }
            }
          
            const p = await dialog.projects.update(project, !project.archived);

            await dialog.saveSession();
            const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
            res.json(project_info).end();
            return;
          } else if(project.me_candidate && checkState(project.me_candidate, ProjectCandidateState.ACCEPTED) <= 0 ) {
            // accept as candidates
            project.me_candidate = this.findCandidate(dialog, project);
            project.me_candidate.ready = dialog.user.hasAccount(AuthProviders.Stripe) || dialog.user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment);
            project.me_candidate.state = ProjectCandidateState.ACCEPTED;
            
            if (dialog.user.loaded_groups) {
              const contracts = await data.contracts.load(dialog.user);
              for (const contract of contracts) {
                if (project.client.comms.includes(contract.client_email)) {
                  for (const group of Object.values(dialog.user.loaded_groups)) {
                    if (group.name.toLowerCase() === contract.contractor_name.toLowerCase() ||
                      group.company_name.toLowerCase() === contract.contractor_name.toLowerCase()) {
                      project.me_candidate.as_group = group.id;
                      break;
                    }
                  }
                  break;
                }
              }
            }

            project.last_update = new Date();
            project.last_activity = project.last_update;

            // add skills
            const index = 150;
            const start = new Date();
            let saved = false;
            const me = new Person(dialog.me);
            for (const value of project.skill_set) {
              if (!parsers.ignore(value)) {
                const tag = new Tag(TagType.skill, value, index, start, 100);
                saved = saveOneTypeValue(dialog.me.tags, tag) || saved;
              }
            }

            if (saved) {
              logging.warnFP(this.log_name, 'accept', dialog.user.profile, `Updating self with new skills`);
              await dialog.setSelf(me);
            }

            await data.users.notificationsClear(dialog.user, NotificationType.Project_Invite);

            const p = await dialog.projects.update(project, false);
            await dialog.projects.updateShared(p);
            await dialog.projects.updateClient(p, true);

            const to_user = new ForaUser(project.client.askfora_id);
            await data.users.init(to_user, false);
            const to_email = to_user.email ? [to_user.email] : parsers.findEmail(project.client.comms);
            const url = dialog.projects.getUrl(project, to_email && to_email.length ? to_email[0] : null);

            const email = {
              rcpts: [{ Name: project.client.displayName, Email: to_email && to_email.length ? to_email[0] : null }],
              subject: lang.project.ACCEPTED_SUBJECT(project, project.me_candidate),
              message: lang.project.ACCEPTED_MESSAGE(project, project.me_candidate, url),
            };

            dialog.projects.notify(p, to_user, p.me_candidate, email.subject ? email : null, { group: dialog.getNotifyGroup(email.rcpts[0].Email), notification: NotificationType.Project_Accepted}).catch(e => dialog.asyncError(e));

            await dialog.saveSession();
            const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
            res.json(project_info).end();
            return;
 
          }
        }
      } catch(e) {
        logging.errorFP(this.log_name, 'accept', dialog.user.profile, `Error accepting project`, e);
      }

      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async decline(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      try {
        if (dialog.isAuthenticatedNonGuest() && req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);
          if (project?.client?.self && project.candidates) {
            // proposal
            project.accepted = true;
            project.last_update = new Date();
            project.last_activity = project.last_update;
            const p = await dialog.projects.update(project, !project.archived);

            await dialog.saveSession();
            const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
            res.json(project_info).end();
            return;
          } else if(project.me_candidate && !project.payment &&
            checkState(project.me_candidate, ProjectCandidateState.SUBMITTED) < 0 ) {
            // decline as candidate

            project.me_candidate = this.findCandidate(dialog, project, false);
            if (project.me_candidate) {
              project.me_candidate.state = ProjectCandidateState.DECLINED;
              project.last_update = new Date();
              project.last_activity = project.last_update;
              await data.users.notificationsClear(dialog.user, NotificationType.Project_Invite);

              const p = await dialog.projects.update(project, !project.archived);
              await dialog.projects.updateClient(project, true);

              const to_user = new ForaUser(project.client.askfora_id);
              await data.users.init(to_user, false);
              const to_email = to_user.email ? [to_user.email] : parsers.findEmail(project.client.comms);
              const url = dialog.projects.getUrl(project, to_email && to_email.length ? to_email[0] : null);

              const email = {
                rcpts: [{ Name: project.client.displayName, Email: to_email && to_email.length ? to_email[0] : null }],
                subject: lang.project.DECLINED_SUBJECT(project, project.me_candidate),
                message: lang.project.DECLINED_MESSAGE(project, project.me_candidate, url),
              };

              dialog.projects.notify(project, to_user, project.me_candidate, email.subject ? email : null, { group: dialog.getNotifyGroup(email.rcpts[0].Email), notification: NotificationType.Project_Declined}).catch(e => dialog.asyncError(e));

              await dialog.saveSession();
              const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
              res.json(project_info).end();
              return;
  
            }
          }
        }
      } catch(e) {
        logging.errorFP(this.log_name, 'decline', dialog.user.profile, `Error decilning project`, e);
      }

      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async complete(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      try {
        if (dialog.isAuthenticatedNonGuest() && req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);
          project.completed = true;
          project.last_update = new Date();
          project.last_activity = project.last_update;

          const p = await dialog.projects.update(project, !project.archived);

          await dialog.saveSession();
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
          res.json(project_info).end();
          return;
        }
      } catch(e) {
        logging.errorFP(this.log_name, 'complete', dialog.user.profile, `Error completing project`, e);
      }
  
      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async progress(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      try {
        if (dialog.isAuthenticatedNonGuest() && req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);

          const project_dto: ProjectDto = this.parseJson(dialog, req.body);
          project.progress = project_dto.progress;
          project.last_update = new Date();
          project.last_activity = project.last_update;

          const p = await dialog.projects.update(project, project.client.self);
          if (!project.client.self) await dialog.projects.updateShared(project);

          await dialog.saveSession();
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
          res.json(project_info).end();
          return;
        }
      } catch(e) {
        logging.errorFP(this.log_name, 'progress', dialog.user.profile, `Error updating project progress`, e);
      }
  
      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async rename(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      try {
        if (dialog.isAuthenticatedNonGuest() && req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);
          if (dialog.isAuthenticatedNonGuest() && req.params.project_id) {
            const project_dto: ProjectDto = this.parseJson(dialog, req.body);
            project.title = project_dto.title;
            project.last_update = new Date();
            project.last_activity = project.last_update;

            const p = await dialog.projects.update(project, !project.archived);

            await dialog.saveSession();
            const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
            res.json(project_info).end();
            return;
          } else {
            // candidate
          }
        }
      } catch(e) {
        logging.errorFP(this.log_name, 'rename', dialog.user.profile, `Error renaminig project`, e);
      }
  
      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async select(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      try {
        if (dialog.isAuthenticatedNonGuest() && req.params.project_id && req.body.id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);
          if (project?.client?.self && project.candidates) {
            const candidate = project.candidates.find(c => c.id === req.body.id);
            if (candidate) {
              candidate.state = ProjectCandidateState.SELECTED;
              project.contractor = projectPerson(project, candidate);
            }

            let contract = await dialog.projects.setupContract(project);
            if (contract) {
              if (!contract.id) contract = await dialog.contracts.create(contract);
              else {
                if ((!project.contractor.ready ||
                  (contract.client_reviewed && !contract.contractor_signed)) ||
                  (!project.escrow && project.proposal)) {
                  project.select_notice = true;
                  project.last_update = new Date();
                  project.last_activity = project.last_update;
                  
                  await dialog.projects.notifySelected(project, contract);
                }
              }
              project.contract = contract.id;
            }

            project = await dialog.projects.update(project, !project.archived);
            const contractor_person = projectPerson(project, project.contractor);
            contractor_person.id = null;
            await dialog.projects.createForUser(new ForaUser(project.contractor.askfora_id), contractor_person, project, false);

            await dialog.saveSession();
            const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
            res.json(project_info).end();
            return;
          }
        }
      } catch(e) {
        logging.errorFP(this.log_name, 'select', dialog.user.profile, `Error selecting contractor`, e);
      }

      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async unselect(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      try {
        if (dialog.isAuthenticatedNonGuest() && req.params.project_id) {
          let project = await dialog.projects.get(new Project({ id: req.params.project_id }), true);
          if (project?.client?.self && project.contractor) {
            project.last_update = new Date();
            project.last_activity = project.last_update;
            const person = project.contractor;
            if (!project.escrow || (project.escrow.id === lang.project.SKIP_ESCROW.id && !project.escrow.charges /*progress && !project.completed*/)) {
              project.select_notice = null;

              if (project.group_settings && 'service_fee' in project.group_settings) project.service_fee = project.group_settings.service_fee;
              else project.service_fee = SERVICE_FEE;

              project.progress = 0;

              let delete_contract;
              if (project.contract) {
                const contract = dialog.cache.contracts[project.contract];
                if (contract && !(contract.client_signed && contract.contractor_signed)) delete_contract = contract;
                project.contract = null;
              }

              if (project.candidates) {
                for (const candidate of project.candidates) {
                  if (candidate.state === ProjectCandidateState.SELECTED) {
                    candidate.state = ProjectCandidateState.ACCEPTED;
                    break;
                  }
                }
              }

              project.contractor = null;

              project = await dialog.projects.update(project, !project.archived);

              if (delete_contract) await dialog.contracts.delete(delete_contract);
              if (person) {
                await dialog.projects.notify(project, new ForaUser(person.askfora_id), person);
                const contractor_person = projectPerson(project, person);
                contractor_person.id = null;
                await dialog.projects.createForUser(new ForaUser(person.askfora_id), contractor_person, project, false);
              }

              await dialog.saveSession();
              const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, true, dialog.user.groups ? Object.keys(dialog.user.groups) : [])
              res.json(project_info).end();
              return;
            }
          }
        }
      } catch(e) {
        logging.errorFP(this.log_name, 'unselect', dialog.user.profile, `Error unselecting contractor`, e);
      }

      await dialog.saveSession();
    }
    res.status(404).end();
  }

  async shared(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let project_info = [];
      const groups = req.params.group_id ? [req.params.group_id] : null;
      const projects = await dialog.projects.sharedProjects(groups);
      if (projects) project_info = projects.map(p => peopleUtils.projectInfo(dialog.user.profile, dialog.me, p, false, groups, dialog.user.locale));

      await dialog.saveSession();
      res.json(project_info).end();
      return;
    }

    // find all public or in the req.params.group_id
    res.status(404);
  }

  async group(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'group', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      if (dialog) await dialog.saveSession();
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    // Get our Dialog (created by UserAuthValidationHandler)
    if (!dialog) {
      logging.errorFP(this.log_name, 'group', dialog.user.profile, `Unable to find session ${session_id}`, null);
      if (dialog) await dialog.saveSession();
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    const projects = await dialog.projects.load(req.params.group && req.params.group.length ? [req.params.group as Uid] : undefined);

    if (dialog) await dialog.saveSession();

    // fix runaway search
    const expired = MINUTES(new Date(), -5);
    projects.forEach(p => {
      if (!p.client || !p.client.self || p.last_update < expired) p.searching = false;
    });

    const info: ProjectInfo[] = peopleUtils.getProjectsInfo(dialog.user.profile, dialog.me, dialog.me, projects, dialog.group_host, dialog.user.locale);

    res.json(info).end();
    return;


  }

  async create(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    this.debug('create: Headers = %o', req.headers);
    this.debug('create: Body = %o', req.body);
    this.debug('create: Group = %o', req['group']);
    this.debug('create: User = %o', req['user']);
    this.debug('create: Session ID = %o', req['session_id']);

    // GroupAuthValidationHandler sets these variables if authentication passes
    // const group: Group = req['group'];
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'create', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      if (dialog) await dialog.saveSession();
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    try {
      // Convert the incoming JSON to our subset object
      const project_dto: ProjectDto = this.parseJson(dialog, req.body);

      // Validate that we got what we need
      const errors = await this.validateProject(dialog, project_dto);
      if (errors && errors.length) {
        logging.errorFP(this.log_name, 'create', dialog.user.profile, `Incoming project did not pass validation :: ${util.format(errors)}`, null);
        throw new InternalError(422, 'Unable to parse incoming project', { fields: errors });
      }

      // Create a Project in the underlying data source
      const project = await this.createProject(dialog, project_dto);
      logging.infoFP(this.log_name, 'create', dialog.user.profile, `Project created ${project.id}`);
      this.debug('create: project after create = %o', project);

      //
      // Convert the newly created project to a ProjectInfo
      let project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, true);
      this.debug('create: project_info after create = %o', project_info);

      if (dialog.user.provider === AuthProviders.Saml || AuthProvider.isAccessAndProviderOnly(dialog.user.tokens)) {
        logging.warnF(this.log_name, 'create', 'expiring tokens because they are access tokens only');
        dialog.user.tokens.expires_at = HOURS(new Date(), -1);
        await data.users.save(dialog.user, false).catch(err => logging.errorFP(this.log_name, 'create', dialog.user.profile, 'Error saving user after candidate search', err));
      }

      //
      // Return the result to the caller
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      if (!project_info) throw new InternalError(500, 'Unexpected error returning job success');
      // res.json(ProjectController.projectInfoToProjectDto(project_info)).end();
      res.json(project_info).end();
      return;
    } catch (err) {
      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
      if (err instanceof InternalError) {
        const fe = err as InternalError;
        const rich_error = fe.error ? fe.error : null;
        if (rich_error) {
          rich_error.code = err.code;
          rich_error.error = err.message;
        }

        res.status(fe.code).json(rich_error ? rich_error : { error: err.message }).end();
      } else {
        res.status(500).json({ error: config.isEnvDevelopment() ? err.message : 'Internal Server Error' }).end();
      }

      logging.errorFP(this.log_name, 'create', dialog.user.profile, 'Error creating project', err);
    }
  }

  async delete(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (!dialog) {
      logging.errorFP(this.log_name, 'delete', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    const project_id = req.params.project_id;

    if (project_id) {
      try {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'delete', dialog.user.profile, `Looking for project ${project_id}`);
        // let project = dialog.cache.projects[project_id];

        // if (project) {
          // get latest
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'delete', dialog.user.profile, `Reloading shared project ${project_id}`);
        let project = await dialog.projects.get(new Project({id: project_id}), true);
        // } 

        let deleted = false;
        if (project) {
          // create a new copy 
          let deleted_project = await dialog.projects.delete(project, dialog.group_host);
          if (deleted_project) project = deleted_project;
          else deleted = true;
        }

        if (project) {
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self || project.admin, 'group' in req ? [(req['group'] as Group).id] : undefined, dialog.user.locale);
          if (dialog) await dialog.safeSaveSession();
          if (deleted) project_info.deleted = true;
          res.json(project_info).end();
          return;
        }
      } catch(e) {
        logging.errorFP(this.log_name, 'delete', dialog?.user.profile, `${project_id} error deleting project`, e);
        if (dialog) await dialog.safeSaveSession();
        res.sendStatus(404);
        return
      }
    }
    
    if (dialog) await dialog.safeSaveSession();
    res.sendStatus(404);
  }


  async repeat(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (!dialog) {
      logging.errorFP(this.log_name, 'repeat', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    const project_id = req.params.project_id;

    if (project_id) {
      try {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'repeat', dialog.user.profile, `Looking for project ${project_id}`);
        // let project = dialog.cache.projects[project_id];

        // if (project) {
          // get latest
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'repeat', dialog.user.profile, `Reloading shared project ${project_id}`);
        let project = await dialog.projects.get(new Project({id: project_id}), true);
        // } 

        if (project) {
          // create a new copy 
          project = await dialog.projects.repeat(project, dialog.group_host);
        }

        if (project) {
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self || project.admin, 'group' in req ? [(req['group'] as Group).id] : undefined, dialog.user.locale);
          if (dialog) await dialog.safeSaveSession();
          res.json(project_info).end();
          return;
        }
      } catch(e) {
        if (dialog) await dialog.safeSaveSession();
        res.sendStatus(404);
        return
      }
    }
    
    if (dialog) await dialog.safeSaveSession();
    res.sendStatus(404);
  }

  async network(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'network', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      if (dialog) await dialog.saveSession();
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    if (!dialog) {
      logging.errorFP(this.log_name, 'network', dialog.user.profile, `Unable to find session ${session_id}`, null);
      if (dialog) await dialog.saveSession();
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    if (!dialog.isAuthenticatedNonGuest()) {
      if (dialog) await dialog.saveSession();
      res.json([]);
      return;
    }

    const project_id = req.params.project_id;

    if (project_id) {
      try {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'network', dialog.user.profile, `Looking for project ${project_id}`);
        // let project = dialog.cache.projects[project_id];

        // if (project) {
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'network', dialog.user.profile, `Reloading shared project ${project_id}`);
          let project = await dialog.projects.get(new Project({id: project_id}), true);

          if (project) {
            // look for Fora users who match this project or have contacts who match
            const connections = await dialog.people.findIntroductions(project.searched_skills ? project.searched_skills : project.skills.split(' '), 20);
            const candidate_comms = project.candidates ? flatten(project.candidates.map(c => c.comms)) : [];
      
            if (dialog) await dialog.saveSession();
            res.json(connections.filter(c => !arraysIntersect(candidate_comms, c.comms)).map(c => dialog.getPersonInfo(c)));
            return;
          }
        // }
      } catch (err) {
        if (dialog) await dialog.saveSession();
        logging.errorFP(this.log_name, 'network', dialog ? dialog.user.profile : undefined, `Error getting project ${project_id}`, err);
      }
    }

    res.sendStatus(404);
  }

  async template(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'template', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      if (dialog) await dialog.saveSession();
      res.status(401).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    if (!dialog) {
      logging.errorFP(this.log_name, 'template', dialog.user.profile, `Unable to find session ${session_id}`, null);
      if (dialog) await dialog.saveSession();
      res.status(401).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    let templates = null;

    try {
      // look for body
      if (req.method === 'POST' && dialog.isAuthenticatedNonGuest()) {
        const template_dto: TemplateDto = this.parseTemplate(dialog, req.body);
        if (template_dto && template_dto.templates) {
          await dialog.people.saveTemplates(template_dto.templates);
        }
      }

      // return public templates
      templates = await dialog.projects.getTemplates(req.params.vanity);

      // return template details
      if (req.query.full === 'true' && templates) {
        const projects = [];
        for (const id of templates) {
          const shared = await data.projects.projectById(id);
          if (shared) projects.push(peopleUtils.projectInfo(dialog.user.profile, dialog.me, shared, true, Object.keys(dialog.user.groups), dialog.user.locale));
        }
        res.status(200).json({ projects }).end();
        return;
      }
    } catch (err) {
      logging.errorFP(this.log_name, 'template', dialog.user.profile, `Error procesing template`, err);
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      res.status(500).end();
      return;
    }

    await dialog.saveSession(true).catch(e => dialog.asyncError(e));

    res.status(200).json({ templates }).end();
  }

  async get(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    // GroupAuthValidationHandler sets these variables if authentication passes
    // const group: Group = req['group'];
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'get', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      if (dialog) await dialog.saveSession();
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    // Get our Dialog (created by UserAuthValidationHandler)
    if (!dialog) {
      logging.errorFP(this.log_name, 'get', dialog.user.profile, `Unable to find session ${session_id}`, null);
      if (dialog) await dialog.saveSession();
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    const project_id = req.params.project_id;

    if (project_id) {
      try {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'get', dialog.user.profile, `Looking for project ${project_id}`);
        // let project = dialog.cache.projects[project_id];

        /*if (project) {
          // get latest
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'get', dialog.user.profile, `Reloading shared project ${project_id}`);
          project = await dialog.projects.get(project, true);
        } else {*/
          // check for shared project
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'get', dialog.user.profile, `Loading new shared project ${project_id}`);
          let project = await dialog.projects.get(new Project({ id: project_id }), true);

          if (!project) { if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'get', dialog.user.profile, `No shared project ${project_id}`); }
          else if (project.archived || project.escrow || project.progress) {
            const flags = `${project.archived ? ' archived' : ''}${project.escrow ? ' escrow' : ''}${project.progress ? ' progress' : ''}`;
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'get', dialog.user.profile, `Shared project in progress ${project_id}${flags}`);
          }
        // }

        if (project) {
          // const archived_for_me = project.archived && project.client && dialog.user.profile !== project.client.askfora_id
          //  && project.contractor && project.contractor.askfora_id !== dialog.user.profile;
          // if (dialog) await dialog.saveSession();

          // if (!archived_for_me) {
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self || project.admin, 'group' in req ? [(req['group'] as Group).id] : undefined, dialog.user.locale);
          res.json(project_info).end();
          return;
          // }
        }
      } catch (err) {
        logging.errorFP(this.log_name, 'get', dialog ? dialog.user.profile : 'UNKNOWN_PROFILE', `Error getting project ${project_id}`, err);
      }
    } else {
      const projects = await dialog.projects.load(req.query.group && req.query.group.length ? [req.query.group as Uid] : undefined);

      if (dialog) await dialog.saveSession();

      // fix runaway search
      const expired = MINUTES(new Date(), -5);
      projects.forEach(p => {
        if (!p.client || !p.client.self || p.last_update < expired) p.searching = false;
      });

      const info: ProjectInfo[] = peopleUtils.getProjectsInfo(dialog.user.profile, dialog.me, dialog.me, projects, dialog.group_host, dialog.user.locale);

      res.json(info).end();
      return;
    }

    res.sendStatus(404);
  }

  async update(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    // GroupAuthValidationHandler sets these variables if authentication passes
    // const group: Group = req['group'];
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'update', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      if (dialog) await dialog.saveSession();
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    // Get our Dialog (created by UserAuthValidationHandler)
    if (!dialog) {
      logging.errorFP(this.log_name, 'update', dialog.user.profile, `Unable to find session ${session_id}`, null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    let project_dto: ProjectDto = null;

    try {
      project_dto = this.parseJson(dialog, req.body);

      if (project_dto && project_dto.id) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'update', dialog.user.profile, `Looking for project ${project_dto.id}`);
        let project = dialog.cache.projects[project_dto.id];

        if (dialog.isAuthenticatedNonGuest()) {
          /*if (project) {
            // get latest
            logging.infoFP(this.log_name, 'update', dialog.user.profile, `Reloading shared project ${project_dto.id}`);
            project = await dialog.projects.get(project);
            // dialog.projects.checkProject(project, true);
          } else {*/
            // check for shared project
            logging.infoFP(this.log_name, 'update', dialog.user.profile, `Loading new shared project ${project_dto.id}`);
            project = await dialog.projects.get(new Project({ id: project_dto.id }), true);
          // }
        }

        if (!project) {
          logging.infoFP(this.log_name, 'update', dialog.user.profile, `No shared project ${project_dto.id}`);
        } else if (!project.archived && !project.completed && (!project.payment || project.rate === ProjectRate.sourcing)) {
          let update = false;
          if (!project.escrow || (project.escrow.id === lang.project.SKIP_ESCROW.id && !project.escrow.charges)) {
            if (project.client.self || project.admin || project.proposal || project.client.id === ANONYMOUS_ID) {
              update = await this.updateProject(dialog, project, project_dto);

              if (update) {
                project.last_update = project_dto.update_date ? new Date(project_dto.update_date) : new Date();
                project.last_activity = project.last_update;
                if (project.client.self || project.client.id === ANONYMOUS_ID) {
                  if (!dialog.user.isGuestAccount()) await dialog.projects.update(project);
                } else {
                  await dialog.projects.update(project, !project.client || project.client.askfora_id === FORA_PROFILE);
                  if (!project.proposal || project.accepted) await dialog.projects.updateClient(project);
                  await dialog.projects.updateShared(project);
                }
              }
            }
          } else {
            // only update progress
            if (project_dto.progress !== undefined && project_dto.progress !== null && project.progress !== project_dto.progress) {
              project.progress = project_dto.progress <= project.duration ? project_dto.progress : project.duration;
              project.last_update = project_dto.update_date ? new Date(project_dto.update_date) : new Date();
              project.last_activity = project.last_update;
              if (project.progress < 0) project.progress = 0;
              const updated_project = await dialog.projects.update(project, project.client.self ? true : false);
              if (project.client.self) await dialog.projects.createForUser(new ForaUser(project.contractor.askfora_id), project.contractor, updated_project);
              else await dialog.projects.updateClient(updated_project);

              const person = project.client.self ? project.contractor : project.client;
              const user = new ForaUser(person.askfora_id);
              await data.users.init(user, false);
              const emails = user.email ? [user.email] : parsers.findEmail(person.comms);
              const to_email = emails && emails.length ? emails[0] : null;

              const email = {
                rcpts: [{ Name: person.displayName, Email: to_email }],
                subject: lang.project.PROGRESS_SUBJECT(project),
                message: lang.project.PROGRESS_MESSAGE(project, dialog.projects.getUrl(project, to_email), person),
              };

              await dialog.projects.notify(project, new ForaUser(person.askfora_id), project.client.self ? person : null, email, { group: dialog.getNotifyGroup(to_email), notification: NotificationType.Project_Progress, template: project.client.self ? TemplateType.Contractor : TemplateType.Client }).catch(e => dialog.asyncError(e));
            }
          }

          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self || project.admin);
          project_info.updated = update;
          await dialog.saveSession();
          res.json(project_info).end();
          return;
        } else {
          const project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self || project.admin);
          await dialog.saveSession();
          res.json(project_info).end();
          return;
        }
      }
    } catch (err) {
      logging.errorFP(this.log_name, 'update', dialog ? dialog.user.profile : null, `Error updating project ${project_dto ? project_dto.id : ''}`, err);
    }

    await dialog.saveSession();
    res.status(404).end();
  }

  async updateProject(dialog: Dialog, project: Project, project_dto: ProjectDto): Promise<boolean> {
    let update = false;
    // make sure dates are objects
    project.start = new Date(project.start);
    project.end = new Date(project.end);
    if (!project.duration || isNaN(project.duration) || project.duration > 100000) project.duration = 1;
    if ('start' in project_dto) project_dto.start = new Date(project_dto.start);
    if ('end' in project_dto) project_dto.end = new Date(project_dto.end);
    if ('duration' in project_dto && isNaN(project_dto['duration'])) project_dto.duration = project.duration;

    // ok to update details, not progress
    for (const field of UPDATE_PROJECT_DESC_FIELDS) {
      if (field in project_dto &&
        (PROJECT_DATE_FIELDS.includes(field) && !isNaN(project_dto[field].getTime()) && project_dto[field].getTime() !== project[field].getTime()) ||
        (!PROJECT_DATE_FIELDS.includes(field) && project_dto[field] !== project[field])) {
        if (field === 'skills' && 'skill_set' in project_dto) continue;
        update = true;
        project[field] = project_dto[field];
        if (PROJECT_SKILLS_FIELD.includes(field)) {
          project.searched_skills = null;
          project.searching = false;
          await dialog.projects.resolveSkills(project);
        }
      }
    }
    return update;
  }

  async getJab(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'getJab', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    // Get our Dialog (created by UserAuthValidationHandler)
    if (!dialog) {
      logging.errorFP(this.log_name, 'getJab', dialog.user.profile, `Unable to find session ${session_id}`, null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    try {
      let projects: { public: boolean, project: Project }[] = [];
      if (req.params.project_id) {
        const jab = await data.projects.getJab({ id: req.params.project_id }, dialog.group_host);
        if (jab) {
          if (jab.public || (dialog.user.groups && jab.group in dialog.user.groups)) {
            const project = await data.projects.projectById(jab.project);
            if (project) projects.push({ public: jab.public, project });
          }
        }
      } else {
        projects = await data.projects.jabProjects(
          dialog.group_host ? [dialog.group_host] :
          dialog.user.loaded_groups ? Object.values(dialog.user.loaded_groups) : null);
      }
      await dialog.saveSession();
      res.status(200).json(projects.map(j => { return { public: j.public, project: peopleUtils.projectInfo(dialog.user.profile, dialog.me, j.project, false, Object.keys(dialog.user.groups), dialog.user.locale) } }));
      return;
    } catch (err) {
      logging.errorFP(this.log_name, 'getJab', dialog ? dialog.user.profile : null, `Error getting Jab ${req.params.project_id}`, err);
    }
    await dialog.saveSession();
    res.status(404).end();
  }

  async createJab(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    this.debug('create: Headers = %o', req.headers);
    this.debug('create: Body = %o', req.body);
    this.debug('create: Group = %o', req['group']);
    this.debug('create: User = %o', req['user']);
    this.debug('create: Session ID = %o', req['session_id']);

    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'createJab', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    // Get our Dialog (created by UserAuthValidationHandler)
    if (!dialog) {
      logging.errorFP(this.log_name, 'createJab', dialog.user.profile, `Unable to find session ${session_id}`, null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    try {
      const group = dialog.group_host; // getGroup();
      let project;
      let jab;
      // Convert the incoming JSON to our subset object
      const project_dto: ProjectDto = req.body.project ? this.parseJson(dialog, req.body.project) : null;
      if (req.params.project_id) {
        const project_id = req.params.project_id;
        project = await dialog.projects.get(new Project({ id: project_id }), true);
        if (project) {
          jab = await data.projects.getJab(project, group);
          if (project_dto) {
            const update = await this.updateProject(dialog, project, project_dto);
            if (update) await data.projects.saveShared(project);
          }
        }
      } else {

        // Validate that we got what we need
        const errors = await this.validateProject(dialog, project_dto);
        if (errors && errors.length) {
          logging.errorFP(this.log_name, 'createJab', dialog.user.profile, `Incoming project did not pass validation :: ${util.format(errors)}`, null);
          throw new InternalError(422, 'Unable to parse incoming project', { fields: errors });
        }

        // Create a Project in the underlying data source
        project = await this.createJabProject(dialog, project_dto);
        logging.infoFP(this.log_name, 'createJab', dialog.user.profile, `Project created ${project.id}`);
        this.debug('create: project after create = %o', project);
      }

      if (project) {
        if (jab) {
          if ('public' in req.body) jab.public = lang.bool.AFFIRMATIVES.includes(req.body.public.toString());
        } else {
          jab = new Jab({
            id: uuid(),
            project: project.id,
            group: group ? group.id : undefined,
            public: 'public' in req.body && lang.bool.AFFIRMATIVES.includes(req.body.public.toString()),
          });

        }

        await data.projects.saveJab(jab);

        //
        // Convert the newly created project to a ProjectInfo
        let project_info = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, true);
        this.debug('createJab: project_info after create = %o', project_info);
        await dialog.saveSession();
        res.status(200).json({ project: project_info, group: jab.group, public: jab.public });
        return;
      }
    } catch (err) {
      logging.errorFP(this.log_name, 'createJab', dialog ? dialog.user.profile : null, `Error creating Jab ${req.params.project_id}`, err);
    }
    await dialog.saveSession();
    res.status(404).end();
  }

  async deleteJab(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!dialog || !session_id) {
      logging.errorFP(this.log_name, 'deleteJab', dialog ? dialog.user.profile : undefined, 'Missing one of group, user or session_id', null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    // Get our Dialog (created by UserAuthValidationHandler)
    if (!dialog) {
      logging.errorFP(this.log_name, 'deleteJab', dialog.user.profile, `Unable to find session ${session_id}`, null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    try {
      const group = dialog.getGroup();
      if (group) {
        if (req.params.project_id) {
          const project_id = req.params.project_id;
          const project = await dialog.projects.get(new Project({ id: project_id }), true);
          if (project) await data.projects.deleteJab(project, group);
          await dialog.saveSession();
          res.sendStatus(204).end();
          return;
        }
      }
    } catch (err) {
      logging.errorFP(this.log_name, 'deleteJab', dialog ? dialog.user.profile : null, `Error getting Jab ${req.params.project_id}`, err);
    }
    await dialog.saveSession();
    res.status(404).end();
  }

  private async createJabProject(dialog: Dialog, project_dto: ProjectDto): Promise<Project> {
    const people = await dialog.people.loadPeople([project_dto.client, project_dto.contractor, ...project_dto.candidates ? project_dto.candidates : []].filter(c => c));
    const client = project_dto.client ? projectPerson(null, people.find(p => p.id === project_dto.client.id), project_dto.client) : projectPerson(null, dialog.me, {askfora_id: dialog.user.profile});
    const contractor = project_dto.contractor ? projectPerson(null, people.find(p => p.id === project_dto.contractor.id), project_dto.contractor) : undefined;
    const candidates = project_dto.candidates ? project_dto.candidates.map(c => { return {c, p: people.find(x => x.id === c.id)}}).map(({c,p}) => projectPerson(null, p, c)) : [];
    let project = ProjectController.projectDtoToProject(dialog.user.profile, project_dto, client, contractor, candidates);
    if (logging.isDebug(dialog.user.profile)) this.debug('create jab: project = %o', project);

    dialog.mapGroupProjectSettings(project);

    const service_fee = project.group_settings && 'service_fee' in project.group_settings ? project.group_settings.service_fee : SERVICE_FEE;

    project.service_fee = service_fee;

    if (!project.id) project.id = hash(JSON.stringify(project) + new Date());

    project.client = projectPerson(project, lang.about.ABOUT_INFO[0]);
    project.client.askfora_id = FORA_PROFILE;
    project.contractor = projectPerson(project, lang.init.FORA_INFO());
    project.contractor.state = ProjectCandidateState.SELECTED;
    project.candidates = [project.contractor]

    await data.projects.saveShared(project);

    return project;
  }


  private async createProject(dialog: Dialog, project_dto: ProjectDto): Promise<Project> {
    const people = await dialog.people.loadPeople([project_dto.client, project_dto.contractor, ...project_dto.candidates ? project_dto.candidates : []].filter(c => c));
    const client = project_dto.client ? projectPerson(null, people.find(p => p.id === project_dto.client.id), project_dto.client) : projectPerson(null, dialog.me, {askfora_id: dialog.user.profile});
    const contractor = project_dto.contractor ? projectPerson(null, people.find(p => p.id === project_dto.contractor.id), project_dto.contractor) : undefined;
    const candidates = project_dto.candidates ? project_dto.candidates.map(c => { return {c, p: people.find(x => x.id === c.id)}}).map(({c,p}) => projectPerson(null, p, c)) : [];
    let project = ProjectController.projectDtoToProject(dialog.user.profile, project_dto, client, contractor, candidates);
    if (logging.isDebug(dialog.user.profile)) this.debug('create: project = %o', project);

    dialog.mapGroupProjectSettings(project);

    const service_fee = project.group_settings && 'service_fee' in project.group_settings ? project.group_settings.service_fee : SERVICE_FEE;

    project.service_fee = service_fee;

    // place holder for skills if using a url
    let placeholder_skills = false;
    if ((!project.skills || !project.skills.length) && project.rate === ProjectRate.sourcing && project.sourcing_url && project.sourcing_url.length) {
      placeholder_skills = true;
      project.skills = 'sourcing';
    }

    // Manually create the project so it exists in the data store
    project = await dialog.projects.create(project);

    if (placeholder_skills) {
      project.skills = '';
    }

    await dialog.projects.resolveSkills(project);

    if (!project.skill_set || !project.skill_set.length) {
      project.skill_set = project.searched_skills.slice(0, 5);
      project.skills = project.skill_set.join(' ');
    }

    if (!project.notes || !project.notes.length) {
      project.notes = `${_.startCase(project.rate)} job for ${project.skill_set.join(', ')}`;
    }

    project = await dialog.projects.update(project);

    await dialog.reloadData([EntityType.Project], true).catch(err => {
      logging.errorFP(this.log_name, 'reloadData', dialog.user.profile, 'Error reloading project cache', err);
    });

    /*if (!dialog.cache.projects[project.id]) {
      logging.warnFP(this.log_name, 'create', dialog.user.profile, `new project ${project.id} not in cache`)
        ;
    }*/

    await dialog.reset();

    return project;
  }

  private parseJson(dialog: Dialog, body: string): ProjectDto {
    try {
      const jsonConvert = new JsonConvert();
      if (config.isSilly()) jsonConvert.operationMode = OperationMode.LOGGING;
      jsonConvert.ignorePrimitiveChecks = true; // allow assigning number to string etc. Validation will catch them
      jsonConvert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL;

      const project_dto = jsonConvert.deserializeObject(body, ProjectDto);
      this.debug('create: project_dto = %o', project_dto);
      return project_dto;
    } catch (err) {
      logging.errorFP(this.log_name, 'projectCreate', dialog.user.profile, 'Unable to parse incoming project', err);
      throw new InternalError(422, 'Unable to parse incoming project');
    }
  }

  private parseTemplate(dialog: Dialog, body: string): TemplateDto {
    try {
      const jsonConvert = new JsonConvert();
      if (config.isSilly()) jsonConvert.operationMode = OperationMode.LOGGING;
      jsonConvert.ignorePrimitiveChecks = true; // allow assigning number to string etc. Validation will catch them
      jsonConvert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL;

      const template_dto = jsonConvert.deserializeObject(body, TemplateDto);
      this.debug('parse: template_dto = %o', template_dto);
      return template_dto;

    } catch (err) {
      logging.errorFP(this.log_name, 'parseTemplate', dialog.user.profile, 'Unable to parse incoming template', err);
      throw new InternalError(422, 'Unable to parse incoming template');
    }
  }

  private async validateProject(dialog: Dialog, project: ProjectDto): Promise<ValidationError[]> {
    try {
      const validator = new Validator();
      return await validator.validate(project, { validationError: { target: false } });
    } catch (err) {
      logging.errorFP(this.log_name, 'projectCreate', dialog.user.profile, 'Unable to validate incoming project', err);
      throw new InternalError(422, 'Unable to parse incoming project');
    }
  }

  private static getMaxDuration(rate: ProjectRate) {
    switch (rate) {
      case ProjectRate.hourly: return 100000; // hours
      case ProjectRate.daily: return 4000; // days
      case ProjectRate.fixed: return 1; // hours
      case ProjectRate.sourcing: return 1; // placeholder
    }
  }

  private static projectDtoToProject(profile: Uid, in_project: ProjectDto, client: Partial<Candidate>, contractor: Partial<Candidate>, candidates: Partial<Candidate>[]): Project {
    const rate = ProjectRate[in_project.rate];
    const duration = Math.min(in_project.duration, ProjectController.getMaxDuration(rate));
    const start = in_project.start ? in_project.start : new Date();
    const end = in_project.end ? in_project.end : getEndDate(duration, rate, start);
    const sourcing_type = ProjectSourcingType[in_project.sourcing_type];
    const expert = !!in_project.expert;

    return new Project({
      client,
      contractor,
      candidates,
      confidential: in_project.confidential,
      duration,
      fee: in_project.fee,
      network: in_project.network,
      notes: in_project.notes ? in_project.notes : '',
      requirements: in_project.requirements,
      deliverables: in_project.deliverables,
      background: in_project.background,
      rate,
      skills: in_project.skills ? in_project.skills : '',
      sourcing_url: in_project.sourcing_url,
      sourcing_type,
      start,
      end,
      expert,
      last_update: new Date(),
    });
  }

  /*private static projectInfoToProjectDto(in_project: ProjectInfo): ProjectDto {
    const out_project = new ProjectDto();

    out_project.confidential = in_project.confidential;
    out_project.duration = in_project.duration;
    out_project.progress = in_project.progress;
    out_project.fee = in_project.fee;
    out_project.id = in_project.id;
    out_project.network = in_project.network;
    out_project.public = in_project.public;
    out_project.groups = in_project.groups;
    out_project.flex_dates = in_project.flex_dates;
    out_project.notes = in_project.notes;
    out_project.requirements = in_project.requirements;
    out_project.deliverables = in_project.deliverables;
    out_project.background = in_project.background;
    out_project.rate = in_project.rate;
    out_project.skills = in_project.skills;
    out_project.title = in_project.title;
    out_project.start = in_project.start;
    out_project.end = in_project.end;
    out_project.update_date = in_project.update_date;
    out_project.sourcing_type = in_project.sourcing_type;
    out_project.sourcing_url = in_project.sourcing_url;

    return out_project;
  }*/
}

// const projectGet = function (req: express.Request, res: express.Response, next: express.NextFunction) {
//   logging.warnF(this.log_name, 'post', `Headers = ${util.format(req.headers)}`);
//   logging.warnF(this.log_name, 'post', `Body = ${util.format(req.body)}`);
// };

// Build our router/controller and map them

const router = express.Router();
const controller = new ProjectController();

router.route('/search/:project_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.search);

router.route('/share/:project_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.share);

router.route('/propose/:project_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.propose);

router.route('/accept/:project_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.accept);

router.route('/decline/:project_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.decline);

router.route('/answer/:project_id')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.answer);

router.route('/complete/:project_id?')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.complete)

router.route('/select/:project_id?')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.select)

router.route('/unselect/:project_id?')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.unselect)

router.route('/progress/:project_id?')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.progress)

router.route('/rename/:project_id?')
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.rename)

router.route('/repeat/:project_id?')
  .get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.repeat)

router.route('/group/:group_id')
  .get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true, no_cache: true }).route, controller.group);

router.route('/shared/:group_id?')
  .get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true, no_cache: true }).route, controller.shared);

router.route('/network/:project_id')
  .get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).route, controller.network);

router.route('/templates/:vanity?')
  .get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).route, controller.template)
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.template);

router.route('/jab/:project_id?')
  .get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).route, controller.getJab)
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false, require_admin: true }).handle, controller.createJab)
  .delete(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false, require_admin: true }).handle, controller.deleteJab);


router.route('/:project_id?')
  .get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).route, controller.get)
  .post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.create)
  .put(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.update)
  .delete(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.delete);

export default router;
