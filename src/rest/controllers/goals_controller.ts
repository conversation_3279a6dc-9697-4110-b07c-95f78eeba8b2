/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { JsonConvert } from 'json2typescript';
import _ from 'lodash';

import { filterRelated, scoreCandidateCategories, SKILL_WORDS, skillGroup } from '../../skills';

import data from '../../data';
import Dialog from '../../session/dialog';
import ForaUser from '../../session/user';

import { Category, ExtendedCategory, Notification, TemplateType } from '../../types/globals';
import { Goal, Person, Tag } from '../../types/items';
import { GoalInfo, NotificationType, TagType } from '../../types/shared';

import { mapCategory } from '../../utils/filter';
import { combineTagValues, flatten, mapURL } from '../../utils/funcs';
import { categoryInfo, goalInfo } from '../../utils/info';
import logging from '../../utils/logging';
import notify, { NotifyType } from '../../utils/notify';

import { createAssessment } from '../../sources/vertex_controller';
import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { CategoryDto } from '../models/category';

import { AbstractController } from './a_controller';

class GoalsController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:goals');
  log_name = 'rest.controllers.GoalsController';

  constructor() {
    super();

    this.goalsGet = this.goalsGet.bind(this);
    this.goalGet = this.goalGet.bind(this);
    this.goalUpdate = this.goalUpdate.bind(this);
    this.goalDelete = this.goalDelete.bind(this);
    this.goalsReset = this.goalsReset.bind(this);
    this.recommendGoals = this.recommendGoals.bind(this);
    this.assignCourses = this.assignCourses.bind(this);
    this.assessSkills = this.assessSkills.bind(this);
    this.createGoal = this.createGoal.bind(this);
  }

  async goalGet(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let goal: GoalInfo = undefined;

      const user_goal: Goal = await dialog.goals.get(req.params.id);
      await dialog.saveSession();

      if (user_goal && !user_goal.assessed && (!user_goal.courses || !user_goal.courses.find(c => c.completed))) {
        const ecat = await scoreCandidateCategories( [user_goal.category], dialog.me);
        if (ecat.length && ecat[0].score !== user_goal.category.score) {
          user_goal.category.score = ecat[0].score;
          user_goal.category.weights = ecat[0].weights;
          await dialog.goals.save(user_goal);
        }
      }

      goal = user_goal && !user_goal.deleted ? goalInfo(user_goal) : null;
      if (goal) goal.experts = undefined;

      if (goal) {
        res.json(goal).end();
        return;
      }
    }

    res.sendStatus(404).end();
  }

  async goalsGet(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let goals: GoalInfo[] = [];
      let user_goals: Goal[];

      if (req.params.vanity && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        const goal_user = await data.users.globalByVanity(req.params.vanity);
        if (goal_user?.groups?.includes(dialog.group_host.id)) {
          const full_user = new ForaUser(goal_user.profile);
          user_goals = await data.goals.load(full_user);
        }
      } else user_goals = await dialog.goals.load();

      await dialog.saveSession();

      goals = user_goals?.filter(g => !g.deleted).map(goalInfo);
      goals.forEach(g => g.experts = undefined);

      res.json(goals).end();
      return;
    }

    res.sendStatus(404).end();
  }

  async goalUpdate(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      logging.infoFP(this.log_name, 'goalUpdate', dialog.user.profile, `${req.params.id}${req.body.completed ? ' completed: ' : ''}${req.body.completed ? JSON.stringify(req.body.completed) : ''}${req.body.uncompleted ? ' uncompleted: ' :''}${req.body.uncompleted ? JSON.stringify(req.body.uncompleted) : ''}`);
      let goal: Goal = await dialog.goals.get(req.params.id);

      if (goal) {
        if(goal.courses) {
          if (req.body.completed) {
            const course = goal.courses.find(c => c.id === req.body.completed);
            if (course) {
              course.completed = new Date();
              // update person skills and re-score the goal
              const me = new Person(dialog.me);
              const now = new Date();
              if (!course.assign_weight) course.assign_weight = data.courses.assignWeight(course);
              if (!course.assign_skills?.length) {
                course.assign_skills = [
                  ...await data.courses.assignSkills(course, goal.category.skills), 
                  ...await data.courses.assignSkills(course, goal.category.skills, true), 
                ];
              }

              // include skills from goal
              for(const skill of course.assign_skills) {
                combineTagValues(me.tags, new Tag(TagType.skill, skill, 0, now, course.assign_weight));
              }

              let completed = false;
              if (course.score) {
                completed = goal.category.score < 1 && goal.category.score + course.score >= 1 
                goal.category.score += course.score;
              }

              const ecat = await scoreCandidateCategories([goal.category], me);
              if (ecat && ecat.length) {
                if (!course.score) {
                  completed = goal.category.score < 1 && ecat[0].score >= 1 
                  course.score = ecat[0].score - goal.category.score;
                  goal.category.score = ecat[0].score;
                }
                goal.category.weights = ecat[0].weights;
              }
  
              await dialog.setSelf(me);

              const global_user = await data.users.globalById(dialog.user.profile);

              const url = mapURL(`/app/learning/${goal.id}`, dialog.group_host, dialog.user.profile);

              const notification: Notification = {
                email: { rcpts: [{Name: dialog.me.displayName, Email: global_user.email}] },
                type: NotificationType.Goal,
                template: completed ?
                  ( goal.assigned ? TemplateType.AssignedGoalCompleted :  // 5933680
                    TemplateType.GoalCompleted ) // 5934175
                  : goal.assigned ? TemplateType.AssignedCompleted :  // 5933566
                    TemplateType.Completed, // 5934163
                variables: {
                  firstname: global_user.name,
                  goalname: goal.title,
                  learninggoal: url,
                }
              }

              if (goal.assigner) {
                const assigner = await data.users.globalById(goal.assigner);
                if (assigner) {
                  notification.variables.admin_firstname = assigner.name;
                  if (completed) notification.email.ccs = [{Name: assigner.name, Email: assigner.email}]
                }
              }

              await notify(dialog.user, notification, NotifyType.EmailOnly, dialog.group_host, false);
            } else logging.warnFP(this.log_name, 'goalUpdate', dialog.user.profile, `${goal.id} completed course not found ${req.body.completed}`);
          } else if(req.body.uncompleted) {
            const course = goal.courses.find(c => c.id === req.body.uncompleted);
            if (course &&  course.completed) {
              delete course.completed;
              const me = new Person(dialog.me);
              const now = new Date();

              if (!course.assign_weight) course.assign_weight = data.courses.assignWeight(course);
              if (!course.assign_skills) {
                course.assign_skills = [
                  ...await data.courses.assignSkills(course, goal.category.skills), 
                  ...await data.courses.assignSkills(course, goal.category.skills, true), 
                ];
              }

              // include skills from goal
              const weight = -1 * course.assign_weight;
              for(const skill of course.assign_skills) {
                combineTagValues(me.tags, new Tag(TagType.skill, skill, 0, now, weight), true, weight);
              }

              me.tags = me.tags.filter(t => t.type !== TagType.skill || t.index !== weight);

              if (course.score) goal.category.score -= course.score;
              const ecat = await scoreCandidateCategories([goal.category], me);
              if (ecat && ecat.length) {
                if (!course.score) {
                  course.score = goal.category.score - ecat[0].score;
                  goal.category.score = ecat[0].score;
                }
                goal.category.weights = ecat[0].weights;
              }
  
              await dialog.setSelf(me);
            } else logging.warnFP(this.log_name, 'goalUpdate', dialog.user.profile, `${goal.id} uncompleted course not found ${req.body.uncompleted}`);
          } else if(req.body.ignore) {
            const course = goal.courses.find(c => c.id === req.body.ignore);
            if (course) course.ignore = true;
          } else if(req.body.restore) {
            const course = goal.courses.find(c => c.id === req.body.restore);
            if (course) course.ignore = false;
          }

          goal = await dialog.goals.save(goal);
        } else if (req.body.skills && Array.isArray(req.body.skills) && 
          req.body.skills.length && typeof req.body.skills[0] === 'string') {
          await dialog.goals.save(goal);
          goal.category.skills = req.body.skills as string[];

          const me = new Person(dialog.me);
          const ecat = await scoreCandidateCategories([goal.category], me);
          if (ecat && ecat.length) {
            goal.category.score = ecat[0].score;
            goal.category.weights = ecat[0].weights;
          }

          goal = await dialog.goals.save(goal);
        } else if(req.body.assessed) {
          goal.assessed = true;
          const ecat = await scoreCandidateCategories( [goal.category], dialog.me);
          if (ecat.length && ecat[0].score !== goal.category.score) {
            goal.category.score = ecat[0].score;
            goal.category.weights = ecat[0].weights;
          }
 
          goal = await dialog.goals.save(goal);
        }

        await dialog.saveSession();
        res.json(goalInfo(goal)).end();
        return;
      }
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async goalDelete(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      const id = req.params.id;
      try {
        if (id) {
          if (req.params.permanent) await dialog.goals.delete(new Goal({id}));
          else {
            const goal = await dialog.goals.get(id);
            if (goal) {
              goal.deleted = new Date();
              await dialog.goals.save(goal);
            }
          }
        }
      } catch(e) {
        logging.warnFP(this.log_name, 'goalDelete', dialog.user.profile, 'delete error', e);
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.status(422).json({code: 422, error: 'Unable to find incoming goal'}).end();
        return;
      }

      await dialog.saveSession();
      res.sendStatus(204).end();
      return;
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }

  async goalsReset(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if(dialog && dialog.isAuthenticatedNonGuest()) {
      const goals: Goal[] = await dialog.goals.load();
      if (goals && goals.length) {
        const me = new Person(dialog.me);
        const now = new Date();

        for (const goal of goals) {
          if (goal.courses) {
            for (const course of goal.courses.filter(c => c.completed)) {
              if (!course.assign_skills?.length) {
                course.assign_skills = [
                  ...await data.courses.assignSkills(course, goal.category.skills), 
                  ...await data.courses.assignSkills(course, goal.category.skills, true), 
                ];
              }

              const weight = -1 * course.assign_weight;
              for(const skill of course.assign_skills) {
                combineTagValues(me.tags, new Tag(TagType.skill, skill, 0, now, weight), true, weight);
              }
              me.tags = me.tags.filter(t => t.type !== TagType.skill || t.index !== weight);
            }
          }
        }

        await dialog.goals.deleteAll(goals);
        
        await dialog.setSelf(me);
      }

      await dialog.saveSession();
      res.sendStatus(204).end();
      return;
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }

  async recommendGoals(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if(dialog && dialog.isAuthenticatedNonGuest()) {

      let goals: Goal[] = await dialog.goals.load();

      let cat_ids = goals ? goals.map(g => g.category.id) : [];

      let global = true;
      const groups = dialog.user.categoryGroups();
      const group_ids = groups ? groups.map(g => g.id) : [];

      global = groups?.length ? groups.reduce((a,b) => a || b.global_categories, false) : true;

      let skills: string[] = [];
      const ts: string[] = dialog.me.lowestSkills;
      const inv = dialog.me.skillsInv;
      const lset = _.uniq([...ts, ...inv]);
      let max = 10;
      while(skills.length < 10) {
        const me_skills = ts.length < max ? lset.slice(0,max) : ts.slice(0,max);
        skills = await filterRelated(SKILL_WORDS, me_skills, 0.99, true);

        logging.infoFP(this.log_name, 'recommendGoals', dialog.user.profile, `Recommending skills ${skills}`);

        if (skills.length < 10 && max > lset.length) {
          const add_skills = (await filterRelated(SKILL_WORDS, lset, 0.99)).slice(0,20);
          logging.infoFP(this.log_name, 'recommendGoals', dialog.user.profile, `Adding skills ${skills}`);
          skills = _.uniq([...skills, ...add_skills]).slice(0,10);
          break;
        }

        max += 10;
      }

      const related_skills =  _.uniqBy([...skills, ...await skillGroup(group_ids, global, skills, dialog.user.locale, 20)], s => s.toLowerCase());

      logging.infoFP(this.log_name, 'recommendGoals', dialog.user.profile, `Generating categories for ${skills} `);
      const categories = await dialog.skills.categories(skills, related_skills, Math.min(skills.length + cat_ids.length, 10), cat_ids, false, true);
      logging.infoFP(this.log_name, 'recommendGoals', dialog.user.profile, `Found categoryes ${categories.map(c => c.label)}`);
      const matches = await scoreCandidateCategories(categories, dialog.me);
      logging.infoFP(this.log_name, 'recommendGoals', dialog.user.profile, `Mapped goals ${matches ? matches.map(m => m.label) : 0}`);

      const filtered = matches.filter(m => m.score < 1);
      logging.infoFP(this.log_name, 'recommendGoals', dialog.user.profile, `Filtered goals < 1 ${filtered.map(f => f.label)} `);

      // rename matches that duplicate with goals
      const goal_labels = [...goals.map(g => g.title.toLowerCase())];
      const dupes = filtered.filter(m => goal_labels.includes(m.label.toLowerCase()));
      const passed = filtered.map(m => m.label.toLowerCase()).filter(m => !goal_labels.includes(m));

      dupes.forEach(d => {
        for(const s of d.skills) {
          if (![...goal_labels, ...passed].includes(s.toLowerCase())) {
            logging.infoFP(this.log_name, 'recommendGoals', dialog.user.profile, `Relabeling ${d.label} to ${s}`);
            d.label = _.startCase(s);
            passed.push(d.label);
            break;
          }
        }
      });

      const curated = filtered.filter(g => SKILL_WORDS.includes(g.label.toLocaleString())).sort((a,b) => b.score = a.score);
      const generated = filtered.filter(g => !SKILL_WORDS.includes(g.label.toLocaleString())).sort((a,b) => b.score = a.score);

      logging.infoFP(this.log_name, 'recommendGoals', dialog.user.profile, `Curated goals: ${curated.map(c => c.label)} generated goals: ${generated.map(g => g.label)}`);

      res.json([...curated, ...generated].slice(0,3).map(categoryInfo)).end();
      return;
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async assessSkills(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if(dialog && dialog.isAuthenticatedNonGuest()) {
      logging.infoFP(this.log_name, 'assessSkills', dialog.user.profile, `${req.params.id}`);
      const goal: Goal = await dialog.goals.get(req.params.id);

      if (goal?.category?.skills?.length) {
        const assessment = await createAssessment(goal.category.skills);
        await dialog.saveSession();
        res.json(assessment).end();
        return;
      }
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async createGoal(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if(dialog && dialog.isAuthenticatedNonGuest()) {
      let in_category: CategoryDto = null;
      const json_convert = new JsonConvert();

      try {
        in_category = json_convert.deserializeObject(req.body, CategoryDto); 
      } catch (e) {
        logging.warnFP(this.log_name, 'createGoal', dialog.user.profile, 'parse error', e);
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.status(422).json({code: 422, error: 'Unable to parse incoming ask'}).end();
        return;
      }

      const category: Category = mapCategory(in_category);

      logging.infoFP(this.log_name, 'createGoal', dialog.user.profile, `Creating goal for ${category.id} ${category.label} ${category.skills}`);

      const matches: ExtendedCategory[] = in_category.score ? [category as ExtendedCategory] : await scoreCandidateCategories( [category], dialog.me);
      logging.infoFP(this.log_name, 'createGoal', dialog.user.profile, `Mapped ${matches ? matches.length : 0} goals`);

      let goal = data.goals.goalFromCategory(matches[0]);
      goal = await dialog.goals.save(goal); 

      await dialog.saveSession();

      res.json(goalInfo(goal)).end();
      return;
    } 

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async assignCourses(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if(dialog && dialog.isAuthenticatedNonGuest()) {
      let goal: Goal;

      const goals: Goal[] = await dialog.goals.load();

      const valid_ids = goals ? flatten(goals.filter(g => g.deleted && g.courses)
        .map(g => g.courses.filter(c => !c.ignore && !c.completed).map(c => c.id))) : [];

      const course_ids = goals ? flatten(goals.map(g => g.courses ? g.courses.map(c => c.id) : []))
        .filter(id => !valid_ids.includes(id)) : [];

      if (req.params.id) goal = await dialog.goals.get(req.params.id);
      else {
        let in_category: CategoryDto = null;
        const json_convert = new JsonConvert();

        try {
          in_category = json_convert.deserializeObject(req.body, CategoryDto); 
        } catch (e) {
          logging.warnFP(this.log_name, 'assignCourses', dialog.user.profile, 'parse error', e);
          await dialog.saveSession().catch(e => dialog.asyncError(e));
          res.status(422).json({code: 422, error: 'Unable to parse incoming ask'}).end();
          return;
        }

       const category: Category = mapCategory(in_category);

        const matches: ExtendedCategory[] = in_category.score ? [category as ExtendedCategory] : await scoreCandidateCategories( [category], dialog.me);
        logging.infoFP(this.log_name, 'assignCourses', dialog.user.profile, `Mapped ${matches ? matches.length : 0} goals`);

        goal = data.goals.goalFromCategory(matches[0]);
      }

      await dialog.goals.recommend([goal], course_ids, dialog.group_host?.courses);
      logging.infoFP(this.log_name, 'assignCourses', dialog.user.profile, `Assigned ${goal.courses.length} courses to goal`);

      goal = await dialog.goals.save(goal); 

      await dialog.saveSession();

      res.json(goalInfo(goal)).end();
      return;
 
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }
}

const router = express.Router();
const controller = new GoalsController();

router.route('/recommend').get(new UserAuthValidation({read_only: true}).route, controller.recommendGoals);
router.route('/assign').post(new UserAuthValidation({read_only: true}).route, controller.assignCourses);
router.route('/assign/:id').get(new UserAuthValidation({read_only: true}).route, controller.assignCourses);
router.route('/assess/:id').get(new UserAuthValidation({read_only: true}).route, controller.assessSkills);
router.route('/create').post(new UserAuthValidation({read_only: true}).route, controller.createGoal);

router.route('/profile/:vanity?').get(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.goalsGet);
router.route('/reset').get(new UserAuthValidation({read_only: true}).handle, controller.goalsReset);

router.route('/:id').get(new GroupAuthValidation().route, new UserAuthValidation({read_only: true}).handle, controller.goalGet);
router.route('/:id').post(new GroupAuthValidation().route, new UserAuthValidation({read_only: false}).handle, controller.goalUpdate);
router.route('/:id').delete(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.goalDelete);


export default router;
