/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { JsonConvert } from 'json2typescript';
import _ from 'lodash';
import { v4 as uuid } from 'uuid';
import { cosineSimilarity } from 'vector-cosine-similarity';

import lang from '../../lang';

import Dialog from '../../session/dialog';
import ForaUser from '../../session/user';
import { ExtendedCategory, Plan, TemplateType } from '../../types/globals';
import { Goal, Person } from '../../types/items';
import { GoalInfo, NotificationType } from '../../types/shared';

import { flatten, mapURL } from '../../utils/funcs';
import { goalInfo, planInfo } from '../../utils/info';
import logging from '../../utils/logging';
import notify, { NotifyType } from '../../utils/notify';
import peopleUtils from '../../utils/people';

import data from '../../data';
import { scoreCandidateCategories } from '../../skills';
import { skillVector } from '../../skills/model';
import { PlanDto } from '../models/plan';

import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';

import { AbstractController } from './a_controller';

class PlansController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:plans');
  log_name = 'rest.controllers.PlansController';

  constructor() {
    super();

    this.planGet = this.planGet.bind(this);
    this.plansGet = this.plansGet.bind(this);
    this.planCreate = this.planCreate.bind(this);
    this.planUpdate = this.planUpdate.bind(this);
    this.planDelete = this.planDelete.bind(this);
    this.assignedGoals = this.assignedGoals.bind(this);
    this.assignGoal = this.assignGoal.bind(this);
    this.unassignGoal = this.unassignGoal.bind(this);
  }

  async planGet(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let plan: Plan;
      if (req.params.id && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        plan = await data.plans.get(dialog.group_host.id, req.params.id);
      }

      const people_set = [...plan.assigned ? plan.assigned : [], ...plan.unassigned ? plan.unassigned : []];
      const people = people_set.length ? await data.people.vanityByIds(people_set) : [];
      const people_map: {[key:string]: Partial<Person>} = {};
      people.forEach(p => {
        people_map[p.profile] = peopleUtils.personFromVanity(p);
      });

      await dialog.saveSession();
      if (plan) {
        res.json(planInfo(plan, people_map)).end();
        return;
      }
    } 
    res.sendStatus(404).end();
  }

  async plansGet(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let plans: Plan[] = [];
      if(dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        plans = await data.plans.load(dialog.group_host.id);
      }

      const people_set = flatten(_.uniq([...plans.map(p => p.assigned ? p.assigned : []), ...plans.map(p => p.unassigned ? p.unassigned : [])]));
      const people = await data.people.vanityByIds(people_set);
      const people_map: {[key:string]: Partial<Person>} = {};
      people.forEach(p => {
        people_map[p.profile] = peopleUtils.personFromVanity(p);
      });

      await dialog.saveSession();
      res.json(plans.map(p => planInfo(p, people_map))).end();
      return;
    } 
    res.sendStatus(404).end();
  }

  async planCreate(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let plan: Plan;
      if(dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        let in_plan: PlanDto = null;
        const json_convert = new JsonConvert();

        try {
          in_plan = json_convert.deserializeObject(req.body, PlanDto); 
        } catch (e) {
          logging.warnFP(this.log_name, 'planCreate', dialog.user.profile, 'parse error', e);
          await dialog.saveSession().catch(e => dialog.asyncError(e));
          res.status(422).json({code: 422, error: 'Unable to parse incoming ask'}).end();
          return;
        }
  
        plan = new Plan({
          title: in_plan.title ? in_plan.title : `New Development Plan`,
          skills: in_plan.skills ? in_plan.skills : [],
          course_filter: in_plan.course_filter ? in_plan.course_filter : [],
          assigned: [],
          unassigned: [],
        });

        plan.vector = await skillVector(plan.skills);
        const tsv = await skillVector([plan.title]);
        const wsv = await Promise.all(plan.skills.map(s => skillVector([s])));
        plan.weights = wsv.map(w => cosineSimilarity(tsv, w));

        plan = await data.plans.create(dialog.group_host.id, plan);
      }
      await dialog.saveSession();
      if (plan) {
        res.json(planInfo(plan, {})).end();
        return;
      }
    } 
    res.sendStatus(404).end();
  }

  async planUpdate(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let plan: Plan;
      if(dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        let in_plan: PlanDto = null;
        const json_convert = new JsonConvert();

        try {
          in_plan = json_convert.deserializeObject(req.body, PlanDto); 
        } catch (e) {
          logging.warnFP(this.log_name, 'planUpdate', dialog.user.profile, 'parse error', e);
          await dialog.saveSession().catch(e => dialog.asyncError(e));
          res.status(422).json({code: 422, error: 'Unable to parse incoming ask'}).end();
          return;
        }

        plan = await data.plans.get(dialog.group_host.id, in_plan.id);
        if (plan) {
          let update = false;
          if (in_plan.title && plan.title !== in_plan.title) {
            plan.title = in_plan.title;
            update = true;
          }

          const int_skills = in_plan.skills ? _.intersection(plan.skills, in_plan.skills) : plan.skills;
          if (in_plan.skills && (int_skills.length !== plan.skills.length || int_skills.length !== in_plan.skills.length)) {
            plan.skills = _.uniqBy(in_plan.skills.filter(x => x && x.length), x => x.toLowerCase());
            plan.vector = await skillVector(plan.skills);
            const tsv = await skillVector([plan.title]);
            const wsv = await Promise.all(plan.skills.map(s => skillVector([s])));
            plan.weights = wsv.map(w => cosineSimilarity(tsv, w));
            update = true;
          }

          if (in_plan.course_filter && _.intersection(plan.course_filter, in_plan.course_filter).length !== plan.course_filter.length) {
            plan.course_filter = in_plan.course_filter;
            update = true;
          }

          if (update) plan = await data.plans.update(dialog.group_host.id, plan);
        }
      }
      await dialog.saveSession();
      if (plan) {
        const people_set = [...plan.assigned ? plan.assigned : [], ...plan.unassigned ? plan.unassigned : []];
        const people = people_set.length ? await data.people.vanityByIds(people_set) : [];
        const people_map: {[key:string]: Partial<Person>} = {};
        people.forEach(p => {
          people_map[p.profile] = peopleUtils.personFromVanity(p);
        });

        res.json(planInfo(plan, people_map)).end();
        return;
      }
    } 
    res.sendStatus(404).end();
  }

  async planDelete(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      if(req.params.id && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        const plan = await data.plans.get(dialog.group_host.id, req.params.id);
        if (plan && (!plan.assigned || !plan.assigned.length)) {
          await data.plans.delete(dialog.group_host.id, plan);
        }
      }
      await dialog.saveSession();
      res.status(204).end();
      return;
    } 
    res.sendStatus(404).end();
  }

  async assignedGoals(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let plan: Plan;
      let goals: {[key:string]: GoalInfo} = {};
      if (req.params.id && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        plan = await data.plans.get(dialog.group_host.id, req.params.id);
      }

      if (plan && plan.assigned && plan.assigned.length) {
        const global_users = await data.users.globalByIds(plan.assigned);
        await Promise.all(global_users.map(async user => {
          const fora_user = new ForaUser(user.profile);
          const goal = await data.goals.get(fora_user, plan.id);
          if (goal) goals[user.vanity] = goalInfo(goal);
          else goals[user.vanity] = goalInfo(new Goal({
            id: plan.id, 
            title: plan.title, 
            last_update: new Date(),
            category: {
              id: plan.id,
              score: 0,
              skills: plan.skills,
            },
            assigned: new Date(),
          }));
        }));
      }
 
      await dialog.saveSession();
      res.json(goals).end();
      return;
    } 
    res.sendStatus(404).end();
  }

  async assignGoal(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let plan: Plan;
      if (req.params.id && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        plan = await data.plans.get(dialog.group_host.id, req.params.id);
      }

      const vanities = req.body as string[];
      const global_users = await data.users.globalsByVanities(vanities);
      const profiles = global_users.map(u => u.id);

      const reassign_users = plan.unassigned ? plan.unassigned.filter(a => profiles.includes(a)) : [];

      const people_map: {[key:string]: Partial<Person>} = {}

      const other_users = [...plan.assigned ? plan.assigned : [], ...plan.unassigned ? plan.unassigned : []].filter(p => !profiles.includes(p));

      if (plan && global_users && global_users.length) {
        const category: ExtendedCategory = {
          id: plan.id,
          label: plan.title,
          manual: true,
          skills: plan.skills,
          weights: plan.weights,
          vector: plan.vector,
        }

        if(!plan.assigned) plan.assigned = [];

        await Promise.all(global_users.map(async assign_global => {
          const assign_user: ForaUser = new ForaUser(assign_global.profile);
          const assign_self = await data.people.getUserPerson(assign_user, assign_user.profile, dialog.group_host);
          const matches = await scoreCandidateCategories([category], assign_self, true);
          let new_goal = data.goals.goalFromCategory(matches[0]);

          people_map[assign_global.profile] = new Person(assign_self);
          people_map[assign_global.profile].self = false;

          if (plan.assigned.includes(assign_global.profile)) {
            // make sure it's not unassigned
            if (plan.unassigned) plan.unassigned = plan.unassigned.filter(u => u !== assign_global.profile);
            const has_goal = await data.goals.get(assign_user, plan.id);
            if (has_goal) {
              has_goal.assigned = new Date()
              has_goal.assigner = dialog.user.profile;
              has_goal.category = new_goal.category;
              new_goal = has_goal;
            }
          } else {
            if (reassign_users.includes(assign_global.id)) {
              if (plan.unassigned) plan.unassigned = plan.unassigned.filter(u => u !== assign_global.profile);
              const has_goal = await data.goals.get(assign_user, plan.id);
              if (has_goal) {
                has_goal.assigned = new Date()
                has_goal.assigner = dialog.user.profile;
                has_goal.category = new_goal.category;
                new_goal = has_goal;
              }
            }
          }

          if (!new_goal.assigned) {
            new_goal.assigned = new Date();
            new_goal.assigner = dialog.user.profile;
          }

          new_goal.id = plan.id;

          await data.goals.save(assign_user, new_goal);
          plan.assigned.push(assign_global.profile);

          const url = mapURL(`/app/learning/${new_goal.id}`, dialog.group_host, assign_global.profile);

          const rcpts = [{Email: assign_global.email, Name: assign_self.displayName}];
          const subject = lang.plans.ASSIGN_SUBJECT;
          const message = lang.plans.ASSIGN_MESSAGE(dialog.user.name, assign_global.name, url);

          await notify(assign_user, {
            type: NotificationType.Goal,
            email: {rcpts, message, subject}, 
            webpush: {
              data: { message, url },
              fcm_options: { link: url },
              notification: {
                tag: uuid(),
                title: subject,
                body: message,
                click_action: url,
              }
            },
            prompt: [lang.plans.ASSIGN_PROMPT(url)],
            info: [goalInfo(new_goal)],
            // when: new Date(),
            template: TemplateType.Assigned,
            variables: {
              firstname: assign_self.nickName,
              admin_firstname: dialog.user.name, 
              learninggoal: url,
            }
          }, NotifyType.PushAndEmail, dialog.group_host, false, false);
        
        }));

        await Promise.all(other_users.map(async profile => {
          const assign_user: ForaUser = new ForaUser(profile);
          const assign_self = await data.people.getUserPerson(assign_user, assign_user.profile, dialog.group_host);

          people_map[profile] = new Person(assign_self);
          people_map[profile].self = false;
        }));

        plan = await data.plans.update(dialog.group_host.id, plan);
      }
  
      await dialog.saveSession();
      if (plan) {
        res.json(planInfo(plan, people_map)).end();
        return;
      }
    } 
    res.sendStatus(404).end();
  }

  async unassignGoal(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      let plan: Plan;
      if (req.params.id && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        plan = await data.plans.get(dialog.group_host.id, req.params.id);
      }

      const vanities = req.body as string[];
      const global_users = await data.users.globalsByVanities(vanities);
      const profiles = global_users.map(g => g.profile);

      const people_map: {[key:string]: Partial<Person>} = {}

      const other_users = [...plan.assigned ? plan.assigned : [], ...plan.unassigned ? plan.unassigned : []].filter(p => !profiles.includes(p));

      if (plan && global_users && global_users.length) {
        if(!plan.unassigned) plan.unassigned = [];
        if (plan.assigned) plan.assigned = plan.assigned.filter(p => !profiles.includes(p));

        await Promise.all(global_users.map(async assign_global => {
          const assign_user: ForaUser = new ForaUser(assign_global.profile);
          const assign_self = await data.people.getUserPerson(assign_user, assign_user.profile, dialog.group_host);

          people_map[assign_global.profile] = new Person(assign_self);
          people_map[assign_global.profile].self = false;

          if (plan.assigned) plan.assigned.filter(p => p !== assign_global.profile);
          if (!plan.unassigned.includes(assign_global.profile)) {
            plan.unassigned.push(assign_global.profile);

            const goal = await data.goals.get(assign_user, plan.id);
            if (goal && !goal.deleted && (!goal.courses || !goal.courses.find(c => c.completed))) {
              goal.deleted = new Date();
              await data.goals.save(assign_user, goal);

              const url = mapURL(`/app/learning/${goal.id}`, dialog.group_host, assign_global.profile);

              const rcpts = [{Email: assign_global.email, Name: assign_self.displayName}];
              const subject = lang.plans.UNASSIGN_SUBJECT;
              const message = lang.plans.UNASSIGN_MESSAGE(dialog.user.name, assign_global.name);

              await notify(assign_user, {
                type: NotificationType.Goal,
                email: {rcpts, message, subject}, 
                webpush: {
                  data: { message, url },
                  fcm_options: { link: url },
                  notification: {
                    tag: uuid(),
                    title: subject,
                    body: message,
                    click_action: url,
                  }
                },
                prompt: [lang.plans.UNASSIGN_PROMPT],
                info: [goalInfo(goal)],
                when: new Date(),
                template: TemplateType.Unassigned,
                variables: {
                  firstname: assign_self.nickName,
                  admin_firstname: dialog.user.name, 
                  learninggoal: url,
                }
              }, NotifyType.PushAndEmail, dialog.group_host, false, false);
            }
          }
        }));

        await Promise.all(other_users.map(async profile => {
          const assign_user: ForaUser = new ForaUser(profile);
          const assign_self = await data.people.getUserPerson(assign_user, assign_user.profile, dialog.group_host);

          people_map[profile] = new Person(assign_self);
          people_map[profile].self = false;
        }));

        plan = await data.plans.update(dialog.group_host.id, plan);
      }

      await dialog.saveSession();
      if (plan) {
        res.json(planInfo(plan, people_map)).end();
        return;
      }
    } 
    res.sendStatus(404).end();
  }
}

const router = express.Router();
const controller = new PlansController();

router.route('/goals/:id').get(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).route, controller.assignedGoals);
router.route('/assign/:id').post(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).route, controller.assignGoal);
router.route('/unassign/:id').post(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).route, controller.unassignGoal);

router.route('/').get(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.plansGet);

router.route('/:id').get(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.planGet);
router.route('/').post(new GroupAuthValidation().route, new UserAuthValidation({read_only: false, no_cache: true}).handle, controller.planCreate);
router.route('/').put(new GroupAuthValidation().route, new UserAuthValidation({read_only: false, no_cache: true}).handle, controller.planUpdate);
router.route('/:id').delete(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.planDelete);

export default router;

