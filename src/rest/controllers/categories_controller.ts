/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import _ from 'lodash';
import { v4 as uuid } from 'uuid';
import { cosineSimilarity } from 'vector-cosine-similarity';

import Dialog from '../../session/dialog';

import { SkillCategory } from '../../types/globals';
import { skillsetInfo } from '../../utils/info';

import { allSkillCategories, loadSkillCategories, removeSkillCategory, skillCategoriesLoaded, skillCategoryById, updateSkillCategory } from '../../skills';
import { skillVector } from '../../skills/model';

import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';

import { AbstractController } from './a_controller';

class CategoriesController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:categories');
  log_name = 'rest.controllers.CategoriesController';

  constructor() {
    super();

    this.categoryGet = this.categoryGet.bind(this);
    this.categoryPost = this.categoryPost.bind(this);
    this.categoryPut = this.categoryPut.bind(this);
    this.categoryDelete = this.categoryDelete.bind(this);
  }

  async categoryGet(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      dialog.saveSession();

      if (req['group'] && dialog.user.isAdmin(req['group'].id)) {
        if(!skillCategoriesLoaded(req['group'].id)) await loadSkillCategories(req['group'].id);
        const scs = allSkillCategories(req['group'].id);
        res.json(scs.map(skillsetInfo)).end();
        return;
      }
    }

    res.sendStatus(404).end();
  }

  async categoryPost(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      dialog.saveSession();
      const in_skills = req.body as string[];

      if (req['group'] && dialog.user.isAdmin(req['group'].id) && in_skills && in_skills.length) {
        const label = in_skills[0];
        const skills = [label, ..._.uniqBy(in_skills.filter(s => s.toLowerCase() !== label.toLowerCase()), x => x.toLowerCase())];
        const vector = await skillVector(skills);
        const wsv = await Promise.all(skills.map(s => skillVector([s])));
        const weights = [0.9999999999999999, ...wsv.slice(1).map(w => cosineSimilarity(wsv[0], w))];

        const sc = new SkillCategory({
          id: uuid(),
          version: new Date().getTime(),
          weights,
          vector,
          skills,
        });

        updateSkillCategory(req['group'].id, sc);
        await dialog.skills.saveCategory(req['group'], sc);

        res.json(skillsetInfo(sc)).end();
        return;
      }
    }

    res.sendStatus(400).end();
  }

  async categoryPut(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      dialog.saveSession();
      const in_skills = req.body as string[];

      if (req['group'] && dialog.user.isAdmin(req['group'].id) && in_skills && in_skills.length) {
        const label = in_skills[0];
        const skills = [label, ..._.uniqBy(in_skills.filter(s => s.toLowerCase() !== label.toLowerCase()), x => x.toLowerCase())];
        const vector = await skillVector(skills);
        const wsv = await Promise.all(skills.map(s => skillVector([s])));
        const weights = [0.9999999999999999, ...wsv.slice(1).map(w => cosineSimilarity(wsv[0], w))];

        const sc = new SkillCategory({
          id: req.params.id,
          version: new Date().getTime(),
          weights,
          vector,
          skills,
        });

        updateSkillCategory(req['group'].id, sc);
        await dialog.skills.saveCategory(req['group'], sc);

        res.json(skillsetInfo(sc)).end();
        return;
      } 
    }

    res.sendStatus(400).end();
  }

  async categoryDelete(req: express.Request, res: express.Response) {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      dialog.saveSession();

      if (req['group'] && dialog.user.isAdmin(req['group'].id)) {
        if(!skillCategoriesLoaded(req['group'].id)) await loadSkillCategories(req['group'].id);
        const sc = skillCategoryById(req['group'].id, req.params.id);
        if (sc) {
          removeSkillCategory(req['group'].id, sc);
          await dialog.skills.deleteCategory(req['group'], sc);
        }

        res.sendStatus(204).end();
        return;
      }
    }

    res.sendStatus(400).end();
  }
}


// Build our router/controller and map them
const router = express.Router();
const controller = new CategoriesController();

router.route('/').get(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.categoryGet);
router.route('/').post(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.categoryPost);
router.route('/:id').put(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.categoryPut);
router.route('/:id').delete(new GroupAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.categoryDelete);

export default router;