/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */


import express from 'express';
import { v4 as uuid } from 'uuid';

import data from '../../data';

import Dialog from '../../session/dialog';
import { CourseType } from '../../types/globals';
import { Document } from '../../types/items';

import { internalCourse } from '../../routes/courses';

import logging from '../../utils/logging';

import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { AbstractController } from './a_controller';

class CourseController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:projects');
  log_name = 'rest.controllers.ProjectController';

  constructor() {
    super();

    this.create = this.create.bind(this);
  } 

  async create(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];

    if (!dialog ){
      logging.errorFP(this.log_name, 'create', dialog ? dialog.user.profile : null, 'Missing one of group, user or session_id', null);
      res.status(403).json({ code: 403, error: 'Internal Server Error' }).end();
      return;
    }

    try {
      await dialog.saveSession();
      if (dialog.isAuthenticatedNonGuest() && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        if (req.body) {
          const body = req.body.data;
          const course_type = req.body.type as CourseType;

          const id = uuid();
          let mime = 'text/html';

          try {
            JSON.parse(body);
            mime = 'application/json';
          } catch(e) {
            // ignore
          }

          const doc = new Document({
            body,
            created: new Date(),
            file_id: id,
            id,
            props: { course_type },
            mime,
          });

          await data.courses.storeCourse(doc);
          await internalCourse(doc.id);
        }
      }

    } catch(e) {
      logging.errorFP(this.log_name, 'create', dialog.user.profile, `Error loading course`, e);
      await dialog.safeSaveSession();
    }
  }
}

const router = express.Router();
const controller = new CourseController();

router.route('/')
  .post(new GroupAuthValidation().handle, new UserAuthValidation({ read_only: false }).handle, controller.create);

export default router;