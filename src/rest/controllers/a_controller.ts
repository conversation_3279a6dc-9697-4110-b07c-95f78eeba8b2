/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import 'express-session';
import fs from 'fs';
import { Jimp } from 'jimp';
import path from 'path';
import { Readable } from 'stream';

import Dialog from '../../session/dialog';
import MicrosoftGraphPhotoHelper from '../../sources/helpers/microsoft/microsoft_graph_photo_helper';

import { AuthProviders } from '../../types/shared';
import logging from '../../utils/logging';

export abstract class AbstractController {
  protected abstract debug;
  protected abstract log_name: string;

  protected async loadDialog(req: express.Request, read_only = true): Promise<Dialog> {
    // Load our dialog so we can get to the authenticated user
    const dialog = await Dialog.loadSession(req.originalUrl, req.session, req.cookies, {read_only, check_tokens:true, stack:Error().stack});
    if (dialog) return dialog;
    else {
      logging.errorFP(this.log_name, 'loadDialog', null, `Unable to find session ${req.session}`, null);
      return null;
    }
  }

  protected async photoGetStream(dialog: Dialog, api_path: string, res?: string): Promise<{mimeType: string; stream: NodeJS.ReadableStream}> {
    try { 
      let msft_account = dialog.user.getAccountIds(AuthProviders.Msal);
      if (!msft_account) msft_account = dialog.user.getAccountIds(AuthProviders.Microsoft);
      if (!msft_account) {
        logging.warnFP(this.log_name, 'photoGetStream', dialog ? dialog.user.profile : null, `No Microsoft account to get image from ${api_path}`);
        return null;
      }

      let stream = await MicrosoftGraphPhotoHelper.photoStream(dialog.user, dialog.user.profile, api_path);
      if (stream) {
        if (res) {
          const bufs: Buffer[] = [];
          let buf: Buffer;
          stream.on('data', function(d){ bufs.push(d); });
          stream.on('end', function(){
            buf = Buffer.concat(bufs);
          });

          const image = await Jimp.fromBuffer(buf);
          const [x,y] = res.split('x').map(n => parseInt(n, 10));
          if(y) image.resize({w:x, h:y});
          else image.resize({w:x});
          const buff = await image.getBuffer('image/png');
          const resized = new Readable();
          resized._read = () => { /* */ };
          resized.push(buff);
          resized.push(null);
          return { mimeType: 'image/png', stream: resized }

        } else return { mimeType: 'image/png', stream };
      }
    } catch(e) {
      logging.warnFP(this.log_name, 'photoGetStream', dialog ? dialog.user.profile : null, `Could not get image from ${api_path}`, e);
    }

    return null;
  }

  protected async photoGet(req: express.Request, res: express.Response, api_path: string): Promise<void> {
    this.debug('photoGet: Headers = %o', req.headers);
    this.debug('photoGet: Body = %o', req.body);

    let stream;
    const dialog: Dialog = req['dialog']; //await this.loadDialog(req);
    if (dialog) {
      const initials = req.query.fallback ? req.query.fallback as string : 'default';
      const photo = await this.photoGetStream(dialog, api_path);
      if (photo) stream = photo.stream;
      if (!stream) {
        logging.warnFP(this.log_name, 'photoGet', dialog.user.profile, 'Issue reading photo, falling back to initials');
        stream = await this.streamInitialsAvatar(initials);
      }
    }

    if (stream) {
      res.status(200).contentType('png');
      stream.pipe(res);
    } else {
      logging.warnFP(this.log_name, 'photoGet', dialog ? dialog.user.profile : null, 'No photo found');
      res.status(500).json({ code: 500, error: 'Internal Server Error' }).end();
    }

    return;
  }

  protected async streamInitialsAvatar(initials: string, res?: string): Promise<NodeJS.ReadableStream> {
    // Notes:
    let initials_file = `../../files/initials/${initials.toLowerCase()}.png`;

    try {
      if (!fs.existsSync(path.resolve(__dirname, initials_file))) initials_file = '../../files/initials/default.png';
      if (res) {
        const image = await Jimp.fromBuffer(fs.readFileSync(path.resolve(__dirname, initials_file)));
        const [x,y] = res.split('x').map(n => parseInt(n, 10));
        if(y) image.resize({w:x, h:y});
        else image.resize({w:x});
        const buff = await image.getBuffer('image/png');
        const stream = new Readable();
        stream._read = () => { /* */ };
        stream.push(buff);
        stream.push(null);
        return stream;
      } else return fs.createReadStream(path.resolve(__dirname, initials_file));
    } catch (err) {
      logging.errorF(this.log_name, 'streamInitialsAvatar', `Error reading initials file ${initials_file}`, err);
      return null;
    }
  }
}
