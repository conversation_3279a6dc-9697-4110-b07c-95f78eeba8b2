/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { JsonConvert } from 'json2typescript';

import { Dialog } from '../../session/dialog';
import { Ask } from '../../types/items';
import { AskInfo } from '../../types/shared';

import { filterFilter, mapCategory, mapFilter } from '../../utils/filter';
import { askInfo } from '../../utils/info';
import logging from '../../utils/logging';

import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { AskDto } from '../models/ask';
import { AbstractController } from './a_controller';

class AskController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:ask');
  log_name = 'rest.controllers.AskController';

  constructor() {
    super();

    this.askGet = this.askGet.bind(this);
    this.asksGet = this.asksGet.bind(this);
    this.askSave = this.askSave.bind(this);
    this.askDelete = this.askDelete.bind(this);

    this.askHide = this.askHide.bind(this);
    this.askShare = this.askShare.bind(this);
    this.askShared = this.askShared.bind(this);
  }

  async askGet(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try { 
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        const id = req.params.id;
        let ask: Ask;
        if (id) ask = await dialog.asks.get(id);
        //await dialog.saveSession();
        if (ask) {
          const ask_info: AskInfo = askInfo(ask);
          res.json(ask_info).end();
        } else res.sendStatus(404).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'asksGet', dialog ? dialog.user.profile: undefined, `Error getting asks`, err);
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }


  async asksGet(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try { 
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        const asks = await dialog.asks.load();
        await dialog.saveSession();
        if (asks) {
          const ask_info: AskInfo[] = asks.map(askInfo).sort((a,b) => a.update_date && b.update_date ? b.update_date.getTime() - a.update_date.getTime() : -1);
          res.json(ask_info).end();
        } else res.sendStatus(404).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'asksGet', dialog ? dialog.user.profile: undefined, `Error getting asks`, err);
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }


  async askSave(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      const json_convert = new JsonConvert();
      let in_ask: AskDto = null;
      try { 
        in_ask = json_convert.deserializeObject(req.body, AskDto); 
      } catch (e) {
        logging.warnFP(this.log_name, 'askSave', dialog.user.profile, 'parse error', e);
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.status(422).json({code: 422, error: 'Unable to parse incoming ask'}).end();
        return;
      }

      if (in_ask) {
        try {
          let ask: Ask;
          if (in_ask.id && in_ask.id.length) {
            ask = await dialog.asks.get(in_ask.id);
            if (ask) {
              if (in_ask.title) ask.title = in_ask.title;
              if (in_ask.shared) ask.shared = in_ask.shared;
              if (in_ask.projects) ask.projects = in_ask.projects;
              if (in_ask.chat) ask.chat = in_ask.chat;
              if (in_ask.filters) ask.filters = in_ask.filters.map(mapFilter).filter(filterFilter);
              if (in_ask.categories) ask.categories = in_ask.categories.map(mapCategory);
              if (in_ask.candidates) ask.candidates = await dialog.people.loadEntityPeople(in_ask.candidates);

              ask = await dialog.asks.update(ask);
            } else {
              await dialog.saveSession();
              res.sendStatus(404).end();
              return;
            }
          } else {
            ask = new Ask({
              title: in_ask.title,
              public: in_ask.public,
              shared: in_ask.shared,
              projects: in_ask.projects,
              chat: in_ask.chat,
              filters: in_ask.filters.map(mapFilter).filter(filterFilter),
              categories: in_ask.categories.map(mapCategory),
              candidates: in_ask.candidates ? await dialog.people.loadEntityPeople(in_ask.candidates) : [],
            });

            ask = await dialog.asks.create(ask);
          }

          await dialog.saveSession();
          const ask_info: AskInfo = askInfo(ask);
          res.json(ask_info).end();
          return;
        } catch(e) {
          logging.errorFP(this.log_name, dialog ? dialog.user.profile : undefined, 'askSave', `Error saving ask`, e);
        }
 
      }

      await dialog.saveSession();
      res.sendStatus(404).end(); 
      return;
    }

    if(dialog) await dialog.saveSession();
    res.sendStatus(404).end(); 
  }

  async askDelete(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      const id = req.params.id;
      try {
        if (id) await dialog.asks.delete({id});
      } catch(e) {
        logging.warnFP(this.log_name, 'askDelete', dialog.user.profile, 'delete error', e);
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.status(422).json({code: 422, error: 'Unable to find incoming ask'}).end();
        return;
      }

      await dialog.saveSession();
      res.sendStatus(204).end();
      return;
    }

    if (dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }

  async askHide(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try { 
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        const id = req.params.id;
        let ask: Ask;
        if (id) {
          ask = await dialog.asks.get(id);
          await dialog.asks.share(ask, false, undefined);
        }
        await dialog.saveSession();
        if (ask) {
          const ask_info: AskInfo = askInfo(ask);
          res.json(ask_info).end();
        } else res.sendStatus(404).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'asksGet', dialog ? dialog.user.profile: undefined, `Error getting asks`, err);
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }

  async askShare(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try { 
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        const id = req.params.id;
        let ask: Ask;
        if (id) {
          ask = await dialog.asks.get(id);
          await dialog.asks.share(ask, dialog.group_host ? false : true, dialog.group_host ? [dialog.group_host.id] : undefined);
        }
        await dialog.saveSession();
        if (ask) {
          const ask_info: AskInfo = askInfo(ask);
          res.json(ask_info).end();
        } else res.sendStatus(404).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'asksGet', dialog ? dialog.user.profile: undefined, `Error getting asks`, err);
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }

  async askShared(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try { 
      if (dialog) {
        const id = req.params.id;
        let ask: Ask;
        if (id) {
          ask = await dialog.asks.getShared(id);
        }
        await dialog.saveSession();
        if (ask) {
          const ask_info: AskInfo = askInfo(ask);
          res.json(ask_info).end();
        } else res.sendStatus(404).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'asksGet', dialog ? dialog.user.profile: undefined, `Error getting asks`, err);
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404).end();

  }
}


// Build our router/controller and map them
const router = express.Router();
const controller = new AskController();

router.route('/:id').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true, no_cache: true }).handle, controller.askGet);
router.route('/').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true, no_cache: true }).handle, controller.asksGet);
router.route('/').post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.askSave);
router.route('/:id').delete(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).handle, controller.askDelete);

router.route('/hide/:id').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).handle, controller.askHide);
router.route('/share/:id').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).handle, controller.askShare);
router.route('/shared/:id').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).route, controller.askShared);

export default router;
