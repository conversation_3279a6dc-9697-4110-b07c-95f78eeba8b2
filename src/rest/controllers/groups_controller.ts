/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { Jim<PERSON> } from 'jimp';
import data from '../../data';
import logging from '../../utils/logging';
import parsers from '../../utils/parsers';
import { AbstractController } from './a_controller';

/**
 * Notes:
 * - https://stackoverflow.com/questions/26982996/trying-to-proxy-an-image-in-node-js-express
 */
class GroupsController extends AbstractController {
  debug = (require('debug') as any)('fora:routes:rest:groups');
  log_name = 'routes.rest.controllers.GroupsController';

  constructor() {
    super();
    this.logoGet = this.logoGet.bind(this);
    this.infoGet = this.infoGet.bind(this);
  }

  async logoGet(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    if (req.params.id) {
      const group = await data.groups.byId(req.params.id);
      if (group) {
        if (group.logo) {
          let buff = Buffer.from(group.logo, 'base64');
          try {
            if (req.params.res) {
              const [x,y] = req.params.res.split('x').map(i => parseInt(i, 10));
              const background = new Jimp({width: x, height: y});
              const logo = await Jimp.fromBuffer(buff);
              background.composite(logo, 
                (x - logo.bitmap.width)/2, (y - logo.bitmap.height)/2);

              buff = await background.getBuffer('image/png');
            }
            res.status(200).contentType('image/png').send(buff);
            return;
          } catch(e) {
            logging.warnF(this.log_name, 'logoGet', 'Error getting sized logo');
          }
        } else {
          const stream = await this.streamInitialsAvatar(group.name && group.name.length ? (group.name.length < 3 ? group.name : parsers.findInitials(group.name)) : 'default');
          res.status(200).contentType('png');
          stream.pipe(res);
          return;
        }
      }
    }
    res.sendStatus(404);
  }

  async infoGet(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    if (req.params.id) {
      const group = await data.groups.byId(req.params.id);
      if (group) {
        res.json({
          name: group.name,
          company:group.company_name,
          provider:group.provider,
          notifications:group.notifications,
          imports: group.import_maps && Object.keys(group.import_maps).length ? true : false,
          accounts: group.accounts ? Object.keys(group.accounts) : [],
        });
        return;
      }
    }
    res.sendStatus(404);
  }
}

// Build our router/controller and map them
const router = express.Router();
const controller = new GroupsController();

router.route('/:id/logo').get(controller.logoGet);
router.route('/:id/logo/:res?').get(controller.logoGet);
router.route('/:id/info').get(controller.infoGet);

export default router;
