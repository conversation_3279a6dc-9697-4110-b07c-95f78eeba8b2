/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { JsonConvert } from 'json2typescript';
import _ from 'lodash';
import { cosineSimilarity } from 'vector-cosine-similarity';

import { ActionType, Category, Message } from '../../types/globals';
import { GlobalType } from '../../types/items';
import { EducationLevel, EntityType, findTypeValues, Fit, JobTags, PersonInfo, SkillStat, TagType, Uid, UndefinedPersonInfo } from '../../types/shared';

import { cleanFilter, filterFilter, mapCategory } from '../../utils/filter';
import { flatten } from '../../utils/funcs';
import { categoryInfo } from '../../utils/info';
import logging from '../../utils/logging';
import parsers from '../../utils/parsers';

import procBool from '../../proc/bool';
import procEntity from '../../proc/entity';
import procEvents from '../../proc/events';
import procLocations from '../../proc/locations';
import procNumber from '../../proc/number';
import procPlaces from '../../proc/places';
import procRange from '../../proc/range';
import procSkills from '../../proc/skills';
import procTime from '../../proc/time';
import procTypes from '../../proc/types';
import procUrls from '../../proc/urls';

import { CategoryDto } from '../models/category';
import { PersonDto } from '../models/person';

import Dialog from '../../session/dialog';
import { fitPeople, mapCandidateCategories, scoreCandidateCategories } from '../../skills';
import { skillVector } from '../../skills/model';
import { SourceController } from '../../sources/source_controller';
import SignedAuthValidation from '../auth/signed_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { CandidateDto } from '../models/project';
import { AbstractController } from './a_controller';

/**
 * Notes:
 * - https://stackoverflow.com/questions/26982996/trying-to-proxy-an-image-in-node-js-express
 */
class SkillsController extends AbstractController {
  debug = (require('debug') as any)('fora:routes:rest:skills');
  log_name = 'routes.rest.controllers.SkillsController';

  constructor() {
    super();
    this.skillsGet = this.skillsGet.bind(this);
    this.skillsPost = this.skillsPost.bind(this);
    this.skillsFilter = this.skillsFilter.bind(this);
    this.skillsCreate = this.skillsCreate.bind(this);
    this.skillCategories = this.skillCategories.bind(this);
    this.relatedSkills = this.relatedSkills.bind(this);
    // this.relatedSkillsMap = this.relatedSkillsMap.bind(this);
    this.mapCandidateCategories = this.mapCandidateCategories.bind(this);
    this.skillsMatch = this.skillsMatch.bind(this);
    this.skillsBatch = this.skillsBatch.bind(this);
    this.skillsAnalyze = this.skillsAnalyze.bind(this);
    this.skillsScore = this.skillsScore.bind(this);
  }

  async skillsGet(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
      const network = req.query.network !== undefined && req.query.network !== 'false'; 
      const groups = req.query.groups !== undefined && req.query.groups !== 'false';
      const global = req.query.global !== undefined && req.query.global !== 'false';
      const user = req.query.user !== undefined && req.query.user !== 'false';

      let all_tags = [];

      // system wide skills table - all contacts from everyone
      if (global) all_tags = await dialog.people.getGlobalTags(limit);

      // just users
      else if (user) all_tags = await dialog.people.getGlobalUserTags(limit);

      // this user, network connections, group connections
      else if(dialog.isAuthenticatedNonGuest() && SourceController.firstRunDone(dialog.user, EntityType.Person) && dialog.user.exported) {
        all_tags = await dialog.people.getTags(limit, network, groups);
      }

      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));

      const lower_names = dialog.me && dialog.me.names ? dialog.me.names.map(n => n.toLowerCase()) : dialog.user.name.toLowerCase().split(' ');
      const jobs = findTypeValues(dialog.me.tags, JobTags).map(j => j.toLowerCase());
      const tag_set = _.uniq(all_tags.filter(t => t.value && !parsers.mask(t.value) &&
        !parsers.ignore(t.value, true, true, [...lower_names, ...jobs, ...EducationLevel])).map(t => {
          return { 
            num: t.num,
            weight: t.weight * parsers.boost(t.value),
            value: parsers.remap(t.value)
          }
        }));

      const tag_map: {[key:string]: SkillStat} = {};
      tag_set.forEach(t => {
        if (tag_map[t.value]) {
          tag_map[t.value].weight += t.weight;
          tag_map[t.value].num += t.num;
        } else tag_map[t.value] = t;
      });

      const tags = Object.values(tag_map).sort((a,b) => b.weight - a.weight);
      res.json(tags).end();
      return;
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async skillsPost(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      // const bio = req.body.bio;
      const skills = req.body.skills;
      const lnew_skills = skills ? [...skills, 
        ...flatten(skills.map(s => s.split(' ').map(s => s.toLowerCase()).filter(s => s.length > 1)))
      ] : [];

      const json_convert = new JsonConvert();

      const candidates = req.body.candidates as CandidateDto[];

      const people = candidates && Array.isArray(candidates) ? await dialog.people.loadPeople(candidates.map(c => json_convert.deserializeObject(c, CandidateDto))) : undefined;
      // const people = candidates && candidates.length ? await dialog.people.loadPeople(candidates) : undefined;

      const categories = people ? await dialog.skills.peopleCategories(people) : await dialog.skills.categories(lnew_skills, dialog.me.topSkills, 20);

      const has_skills = dialog.me.allSkills.map(s => s.toLowerCase());

      const lower_names = dialog.me && dialog.me.names ? dialog.me.names.map(n => n.toLowerCase()) : dialog.user.name.toLowerCase().split(' ');
      const jobs = dialog.me ? dialog.me.jobNames.map(j => j.toLowerCase()) : [];

      const new_skills = _.uniq(flatten<string>(categories.map(c => c.skills))
        .filter(s => !lnew_skills.includes(s) && !has_skills.includes(s) && !parsers.name(s) && !parsers.mask(s) && !parsers.ignore(s, true, true, [...lower_names, ...jobs, ...EducationLevel])));

      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.json(new_skills).end();
      return;
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404);
  }

  async skillsFilter(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog && dialog.isAuthenticatedNonGuest() && 
      req.body.people && req.body.skills &&
      Array.isArray(req.body.people) && Array.isArray(req.body.skills)) {

      const people: Partial<PersonInfo>[] = req.body.people as Partial<PersonInfo>[];
      const skills:string[] = req.body.skills as string[];

      const lskills = skills.map(s => s.toLowerCase());

      const loaded_people = await dialog.people.loadPeople(people.map(p => { return {id: p.id, vanity: p.vanity, self: p.self}}));
      const filter_people = loaded_people.filter(p => p.tags && _.intersection(findTypeValues(p.tags, TagType.skill).map(s => s.toLowerCase()), lskills).length);

      res.json(filter_people.map(p => dialog.getPersonInfo(p)));
      await dialog.saveSession().catch(e => dialog.asyncError(e));
      return;
    }
    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async skillsCreate(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if(dialog && dialog.isAuthenticatedNonGuest()) {
      try { 
        const message = req.body.message;
        const network = req.body.network;

        dialog.message = {
          type: GlobalType.Message,
          message,
          entities: [],
          ping: 0,
          offset: dialog.user.offset,
          locale: dialog.user.locale,
          timeZone: dialog.user.timeZone,
          command: true,
          link: false,
          time: new Date(),
          data: undefined,
          max: null,
        } as Message;

        procRange(dialog);
        procBool(dialog);
        procNumber(dialog);
        procTime(dialog);
        procTypes(dialog);
        procEvents(dialog);
        procPlaces(dialog);
        procLocations(dialog);
        procUrls(dialog);
        procSkills(dialog);
        await procEntity(dialog);

        dialog.makeFilters();

        const context = dialog.actions.map(a => a.context ? a.context.toLowerCase() : '');
        const check_people = flatten<UndefinedPersonInfo>(dialog.actionValues(ActionType.ENTITY).map(a => a.people));
        let check_names  = dialog.actionValues(ActionType.ENTITY).filter(a => !a.people || !a.people.length).map(a => a.name);

        await dialog.search.updateSearchFilters(dialog.filters, message, context, dialog.actions, check_people, check_names, network);

        let filters = dialog.filters.slice();
        filters.forEach(cleanFilter);
        filters = filters.filter(filterFilter);
  
        // check for people
        if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.json(filters).end();
        return;
      } catch(e) {
        if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
        logging.errorFP(this.log_name, 'skillsCreate', dialog.user.profile, `Error creating filter`, e);
      }
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async skillCategories(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if(dialog && dialog.isAuthenticatedNonGuest()) {
      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));

      const json_convert = new JsonConvert();
      let candidates: PersonDto[] = null;
      let related: string[] = undefined;

      try { 
        candidates = req.body.candidates ? req.body.candidates.map(c => json_convert.deserializeObject(c, PersonDto)) : undefined; 
      } catch (e) {
        logging.warnFP(this.log_name, 'skillCategories', dialog.user.profile, 'parse error', e);
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.status(422).json({code: 422, error: 'Unable to parse incoming candidates'}).end();
        return;
      }

      if (req.body.related && Array.isArray(req.body.related) && req.body.related.length && typeof req.body.related[0] === 'string') related = req.body.related;

      const people = await dialog.people.loadPeople(candidates);

      const categories = await dialog.skills.peopleCategories(people, related);

      res.json(Object.values(categories.slice(0,5).map(categoryInfo))).end();
      return;
    } 
    res.sendStatus(404).end();
  }

  async relatedSkills(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try {
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        await dialog.saveSession().catch(e => dialog.asyncError(e));

        const skills: string[] = req.body.skills;

        const related_skills = await dialog.skills.group(skills);

        res.json(related_skills).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'relatedSkills', dialog ? dialog.user.profile : null, `Error`, err);
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  /*async relatedSkillsMap(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try {
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        await dialog.saveSession().catch(e => dialog.asyncError(e));

        const related_skills: {[key:string]: string[]} = {};
        const skills: string[] = req.body.skills;

        await Promise.all<void>(skills.filter(s => s && s.length).map(async skill => {
          const skill_group = await dialog.skills.expand(skill);
          related_skills[skill] = skill_group;
        }));

        res.json(related_skills).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'relatedSkills', dialog ? dialog.user.profile : null, `Error`, err);
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }*/


  async skillsMatch(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];
    let a;
    let b;
    let av;
    let bv;
    let fit = 0;
    try {
      if ((dialog && dialog.isAuthenticatedNonGuest()) || req.body.demo) {
        if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));

        a = req.body.a as string[];
        b = req.body.b as string[];


        if (a && a.length && b && b.length) {
          av = await skillVector(a);
          bv = await skillVector(b);

          fit = cosineSimilarity(av, bv);
        }

        res.json({fit}).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'skillsMatch', dialog ? dialog.user.profile : null, `Error`, err);
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async skillsBatch(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];

    try {
      if ((dialog && dialog.isAuthenticatedNonGuest()) || req.body.demo) {
        if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));

        const skill_sets = req.body.skillsets as {[key:string]:  {a:string[], b:string[]}}; 

        const fits = await Promise.all(Object.keys(skill_sets).map(async (id) => {
          const {a,b} = skill_sets[id];
          if (a && a.length && b && b.length) {
            const av = await skillVector(a);
            const bv = await skillVector(b);

            const fit = cosineSimilarity(av, bv);
            return { id, fit };
          } 
          return { id, fit: 0 };
        }));

        const fit_map: {[key:string]: number} = {};
        for (const ft of fits) fit_map[ft.id] = ft.fit;

        res.json(fit_map).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'skillsBatch', dialog ? dialog.user.profile : null, `Error`, err);
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async mapCandidateCategories(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try {
      if ((dialog && dialog.isAuthenticatedNonGuest()) || req.body.demo) {

        let categories: Category[];
        let in_categories: CategoryDto[] = null;
        const json_convert = new JsonConvert();

        try {
          in_categories = req.body.categories.map(cat => json_convert.deserializeObject(cat, CategoryDto));
          categories = in_categories.map(mapCategory);
        } catch (e) {
          logging.warnFP(this.log_name, 'mapCandidateCategories', dialog.user.profile, 'parse error', e);
          await dialog.saveSession().catch(e => dialog.asyncError(e));
          res.status(422).json({code: 422, error: 'Unable to parse incoming ask'}).end();
          return;
        }

        const candidates = req.body.candidates as {skills:string[], id: Uid, vanity: string}[];

        const people = await dialog.people.loadPeople(candidates);

        // if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
        
        const mapped_candidates = categories ?  await mapCandidateCategories(dialog.user, categories, people) : [];

        res.json(mapped_candidates).end();
        return;
      }
    } catch(err) {
      logging.errorFP(this.log_name, 'mapCandidateCategories', dialog ? dialog.user.profile : null, `Error`, err);
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async skillsAnalyze(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try {
      let fit: Fit = {};
      // let related: {[key:string]: {[key:string]: string[]}} = {};

      if (dialog && dialog.isAuthenticatedNonGuest()) {
        let categories: Category[];
        let in_categories: CategoryDto[] = null;
        const json_convert = new JsonConvert();

        try {
          in_categories = req.body.categories.map(cat => json_convert.deserializeObject(cat, CategoryDto));
          categories = in_categories.map(mapCategory);
        } catch (e) {
          logging.warnFP(this.log_name, 'mapCandidateCategories', dialog.user.profile, 'parse error', e);
          await dialog.saveSession().catch(e => dialog.asyncError(e));
          res.status(422).json({code: 422, error: 'Unable to parse incoming ask'}).end();
          return;
        }

        const candidates = req.body.candidates as {id: Uid, vanity: string}[];

        const people = candidates && candidates.length ? await dialog.people.loadPeople(candidates) : undefined;

        if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));

        fit = await fitPeople(dialog.user, categories, people);
      } else if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));

      res.send(fit).end();
      return;
    } catch(err) {
      logging.errorFP(this.log_name, 'skillsAnalyze', dialog ? dialog.user.profile : null, `Error`, err);
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }

  async skillsScore(req: express.Request, res: express.Response, _next: express.NextFunction ): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try {
      if(dialog && dialog.isAuthenticatedNonGuest()) {
        if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));

        const categories = await dialog.skills.peopleCategories([dialog.me]);

        const matches = await scoreCandidateCategories(categories, dialog.me);

        matches.forEach(m => {
          delete m.vector;
        });

        res.json(matches.map(categoryInfo)).end();
        return;
      } 
    } catch(err) {
      logging.errorFP(this.log_name, 'skillsScore', dialog ? dialog.user.profile : null, `Error`, err);
    }

    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(404).end();
  }
}

// Build our router/controller and map them
const router = express.Router();
const controller = new SkillsController();

router.route('/').get(new UserAuthValidation({read_only: true}).handle, controller.skillsGet);
router.route('/suggest').post(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.skillsPost);
router.route('/filter').post(new UserAuthValidation({read_only: true}).handle, controller.skillsFilter);
router.route('/create').post(new SignedAuthValidation().route, new UserAuthValidation({read_only: true}).handle, controller.skillsCreate);
router.route('/categories').post(new UserAuthValidation({read_only: true}).handle, controller.skillCategories);
router.route('/related').post(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.relatedSkills);
// router.route('/relatedmap').post(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.relatedSkillsMap);
router.route('/map').post(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.mapCandidateCategories);
router.route('/match').post(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.skillsMatch);
router.route('/batch').post(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.skillsBatch);
router.route('/analyze').post(new UserAuthValidation({read_only: true}).handle, controller.skillsAnalyze);
router.route('/score').post(new UserAuthValidation({read_only: true}).handle, controller.skillsScore);

export default router;
