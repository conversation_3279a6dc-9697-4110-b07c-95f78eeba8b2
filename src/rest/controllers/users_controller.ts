/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { Stats } from '../../types/shared';
import UserAuthValidation from '../auth/user_auth_validation';
import { AbstractController } from './a_controller';

import Dialog from '../../session/dialog';

/**
 * Notes:
 * - https://stackoverflow.com/questions/26982996/trying-to-proxy-an-image-in-node-js-express
 */
class UsersController extends AbstractController {
  debug = (require('debug') as any)('fora:routes:rest:users');
  log_name = 'routes.rest.controllers.UsersController';

  constructor() {
    super();
    this.profilePhotoGet = this.profilePhotoGet.bind(this); // Express oddity, make sure this methods you call from Express know what "this" is
    this.profileStats = this.profileStats.bind(this); // Express oddity, make sure this methods you call from Express know what "this" is
  }

  async profilePhotoGet(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const photo = await this.photoGet(req, res, `/users/${req.params.user_id}/photo/$value`);
    const dialog: Dialog = req['dialog']
    if (dialog) await dialog.saveSession();
    return photo;
  }

  async profileStats(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog']
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      const stats = await dialog.people.getStats(req.params.which === 'network', req.params.which === 'group');
      if (stats) res.send(stats).end();
      else res.send({first_count: 0, second_count: 0, group_count: 0, user_count: 0, fora_count: 0} as Stats).end();
      await dialog.saveSession();
      return;
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404);
  }

}

// Build our router/controller and map them
const router = express.Router();
const controller = new UsersController();

router.route('/:user_id/photo').get(new UserAuthValidation({read_only: true}).handle, controller.profilePhotoGet);
router.route('/stats/:which?').get(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.profileStats);

export default router;
