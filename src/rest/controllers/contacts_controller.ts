/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import axios from 'axios';
import { Valida<PERSON> } from 'class-validator';
import DataURIParser from 'datauri/parser';
import express from 'express';
import 'express-session';
import fs from 'fs';
import { buildSchema } from 'graphql';
import { createHand<PERSON> } from 'graphql-http/lib/use/express';
import { Jimp } from 'jimp';
import { JsonConvert } from 'json2typescript';
import _ from 'lodash';
import multer from 'multer';
import path from 'path';
import { Readable } from 'stream';

import { Recommendation, Vanity } from '../../types/globals';
import { Group } from '../../types/group';
import { Person, Tag } from '../../types/items';
import { ANONYMOUS_ID, Filter, IndustryTags, JobTags, NotificationType, OrgTags, RecommendationInfo, RoleTags, TagType, Uid, findTypeTag, findTypeValues, formatJob, tagParts } from '../../types/shared';

import { arrayIncludes, arraysMatch, flatten, mapURL, permuteName, saveOneTypeValue } from '../../utils/funcs';
import logging from '../../utils/logging';
import notify, { NotifyType } from '../../utils/notify';
import parsers from '../../utils/parsers';
import peopleUtils from '../../utils/people';

import config from '../../config';
import data from '../../data';
import lang from '../../lang';
import { internalLearn } from '../../routes/learn';
import { Dialog } from '../../session/dialog';
import ForaUser from '../../session/user';
import { lookupSkill } from '../../skills';
import Learn from '../../skills/learn';
import { FORA_PROFILE } from '../../types/user';

import GroupAuthValidation from '../auth/group_auth_validation';
import SignedAuthValidation from '../auth/signed_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { PersonDto } from '../models/person';
import { AbstractController } from './a_controller';

/**
 * Notes:
 * - https://stackoverflow.com/questions/26982996/trying-to-proxy-an-image-in-node-js-express
 */
class ContactsController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:contacts');
  log_name = 'rest.controllers.ContactsController';

  constructor() {
    super();
    // Express oddity, make sure this methods you call from Express know what "this" is
    this.initialsPhotoGet = this.initialsPhotoGet.bind(this);
    this.contactPhotoGet = this.contactPhotoGet.bind(this); 
    this.contactPhotosGet = this.contactPhotosGet.bind(this); 
    this.contactPhotoDelete = this.contactPhotoDelete.bind(this);
    this.contactPhotoPost = this.contactPhotoPost.bind(this);
    this.contactConnections = this.contactConnections.bind(this);
    this.contactRecommendations = this.contactRecommendations.bind(this);
    this.contactRecommendationUpdate = this.contactRecommendationUpdate.bind(this);
    this.contactUpdate = this.contactUpdate.bind(this);
    this.contactFind = this.contactFind.bind(this);
    this.contactLearn = this.contactLearn.bind(this);
    this.contactSearch = this.contactSearch.bind(this);
    this.getPhotoData = this.getPhotoData.bind(this);
    this.orgsGet = this.orgsGet.bind(this);

    this.findPeople = this.findPeople.bind(this);
  }

  async initialsPhotoGet(req: express.Request, res: express.Response): Promise<void> {
    let stream = null;
    if (req.params.initials) stream = await this.streamInitialsAvatar(req.params.initials);
    if (!stream) stream = await this.streamInitialsAvatar('default');
    res.status(200).contentType('png');
    stream.pipe(res);
  }

  async getPhotoData(dialog: Dialog, url: string, name, fallback = true, res?: string): Promise<{mimeType: string; stream: NodeJS.ReadableStream}> {
    let stream;
    let mimeType = 'image/png';
    let initials = parsers.findInitials(name);
    if (dialog && url.startsWith(`askfora://${dialog.user.profile}`)) {
      const photo = await dialog.people.getPhoto(url);
      if (photo && photo.body) {
        let buff = Buffer.from(photo.body['data'] ? photo.body['data'] : photo.body, 'binary')
        if (res) {
          const image = await Jimp.fromBuffer(Buffer.from(photo.body['data'] ? photo.body['data'] : photo.body, 'binary'));
          const [x,y] = res.split('x').map(n => parseInt(n, 10));
          if(y) image.resize({w:x, h:y});
          else image.resize({w:x});
          buff = await image.getBuffer('image/png');
        }
 
        stream = new Readable();
        stream._read = () => { /* */ };
        stream.push(buff);
        stream.push(null);
        if (photo.mime && photo.mime.startsWith('image')) mimeType = photo.mime;
      }
    } else if(url.startsWith(`askfora://`)) {
      const [a,b,profile,id] = url.split('/');
      const user = new ForaUser(profile);
      const photo = await data.people.photoById(user, id);
      if (photo && photo.body) {
        let buff = Buffer.from(photo.body['data'] ? photo.body['data'] : photo.body, 'binary')
        if (res) {
          const image = await Jimp.fromBuffer(Buffer.from(photo.body['data'] ? photo.body['data'] : photo.body, 'binary'));
          const [x,y] = res.split('x').map(n => parseInt(n, 10));
          if(y) image.resize({w:x, h:y});
          else image.resize({w:x});
          buff = await image.getBuffer('image/png');
        }
 
        stream = new Readable();
        stream._read = () => { /* */ };
        stream.push(buff);
        stream.push(null);
        if (photo.mime && photo.mime.startsWith('image')) mimeType = photo.mime;
      }
    } else if(url.startsWith('/')) {
      if (url === `/api/contacts/${ANONYMOUS_ID}/photo`) {
        const favicon = config.isRunningOnGoogle() ?
          path.resolve(__dirname, '..', '..', 'public', 'icons', 'icon-120.png')
          : path.resolve(__dirname, '..', '..', '..', 'lib', 'public', 'icons', 'icon-120.png');
        if (res) {
          const image = await Jimp.fromBuffer(fs.readFileSync(favicon));
          const [x,y] = res.split('x').map(n => parseInt(n, 10));
          if(y) image.resize({w:x, h:y});
          else image.resize({w:x});
          const buff = await image.getBuffer('image/png');
          stream = new Readable();
          stream._read = () => { /* */ };
          stream.push(buff);
          stream.push(null);
        } else stream = fs.createReadStream(favicon);
        mimeType = 'image/png';
      } else if(url.startsWith('/images/about')) {
        const image_path = url.split('/').slice(3).join('/');
        const favicon = config.isRunningOnGoogle() ?
          path.resolve(__dirname, '..', '..', 'public', 'images', 'about', image_path)
          : path.resolve(__dirname, '..', '..', '..', 'lib', 'public', 'images', 'about', image_path);
        if (res) {
          const image = await Jimp.fromBuffer(fs.readFileSync(favicon));
          const [x,y] = res.split('x').map(n => parseInt(n, 10));
          if(y) image.resize({w:x, h:y});
          else image.resize({w:x});
          const buff = await image.getBuffer('image/png');
          stream = new Readable();
          stream._read = () => { /* */ };
          stream.push(buff);
          stream.push(null);
        } else stream = fs.createReadStream(favicon);
        mimeType = 'image/png';
      } else {
        const u = new URL(`http://localhost${url}`);
        const fname = u.searchParams.get('fallback');
        if (fname) {
          if (fname.length < 3) initials = fname;
          else initials = parsers.findInitials(fname);
        }
        const path = u.pathname.split('/');
        const cpath = path.indexOf('contacts');
        const upath = path.indexOf('users');
        if (cpath !== -1 && cpath < path.length - 1) {
          const id = path[cpath + 1];
          const photo = await this.photoGetStream(dialog, `/me/contacts/${id}/photo/$value`, res);
          if (photo) {
            stream = photo.stream;
            mimeType = photo.mimeType;
          }
        } else if (upath !== -1 && upath < path.length - 1) {
          const id = path[upath + 1];
          const photo = await this.photoGetStream(dialog, `/users/${id}/photo/$value`, res);
          if (photo) {
            stream = photo.stream;
            mimeType = photo.mimeType;
          }
        }
      }
    } else {
      try { 
        logging.infoFP(this.log_name, 'getPhotoData', dialog ? dialog.user.profile : null, `Fetching ${url}`);
        if (res) {
          const photo = await axios({ method: 'GET', url, timeout: 5000});
          const image = await Jimp.fromBuffer(Buffer.from(photo.data, 'binary'));
          const [x,y] = res.split('x').map(n => parseInt(n, 10));
          if(y) image.resize({w:x, h:y});
          else image.resize({w:x});
          const buff = await image.getBuffer('image/png');
          stream = new Readable();
          stream._read = () => { /* */ };
          stream.push(buff);
          stream.push(null);
          if (photo.headers && photo.headers['content-type']) mimeType = photo.headers['content-type'];
        } else {
          const photo = await axios({ method: 'GET', url, responseType: 'stream', timeout: 5000});
          stream = photo.data;
          if (photo.headers && photo.headers['content-type']) mimeType = photo.headers['content-type'];
        }
      } catch(e) {
        logging.warnFP(this.log_name, 'getPhotoData', dialog ? dialog.user.profile : null, `Issue reading photo for ${name}, falling back to initials`);
      }
    }
    if (!stream && fallback) stream = await this.streamInitialsAvatar(initials, res);
    return {mimeType, stream};
  }

  async loadContactPhoto(person: Partial<Person>): Promise<string> {
    if ((!person.photos || person.photos.length === 0) && person.displayName) {
      const initials = parsers.findInitials(person.displayName)
      let initials_file = `../../files/initials/${initials.toLowerCase()}.png`;
      if (!fs.existsSync(path.resolve(__dirname, initials_file))) initials_file = '../../files/initials/default.png';
      const image = await Jimp.fromBuffer(fs.readFileSync(path.resolve(__dirname, initials_file)));
      image.resize({w:34, h:34});
      const buff = await image.getBuffer('image/png');
      return `data:image/png;base64,${buff.toString('base64')}`;
    }
  }

  async contactPhotoGet(req: express.Request, res: express.Response): Promise<void> {
    if (req.params.contact_id) return this.photoGet(req, res, `/me/contacts/${req.params.contact_id}/photo/$value`);
    else {
      const dialog: Dialog = req['dialog'];
      let person: Partial<Person>;
      if(req.params.vanity) {
        person = lang.about.ABOUT_INFO.find(a => a.vanity === req.params.vanity);
        if (!person) {
          let vanity = await data.users.vanity(req.params.vanity);
          if (!vanity) vanity = await data.users.vanity(encodeURI(req.params.vanity));
          if (vanity) person = peopleUtils.personFromVanity(vanity);
        }
      }
      else if (req.params.person_id) {
        if (req.params.profile_id && req.params.profile_id.startsWith('n')) {
          person = await data.people.byId(new ForaUser(req.params.profile_id.slice(1)), `people/${req.params.person_id}`);
        }
        else if(req['hash'] && req['profile']) {
          const signed_user = new ForaUser(req['profile']);
          [person] = await data.people.byAttributeId(signed_user, [`people/${req.params.person_id}`]);
        }
        else if(req.params.person_id !== FORA_PROFILE && dialog) [person] = await dialog.people.byId(`people/${req.params.person_id}`);
      }

      if (person) {
        let stream = null;
        let mimeType = null;

        if ((!person.photos || person.photos.length === 0) && person.displayName) {
          stream = await this.streamInitialsAvatar(parsers.findInitials(person.displayName), req.params.res);
          mimeType = 'image/png';
        } else {
          for (let i = 0; i < person.photos.length; i++) {
            const photo = person.photos[i];
            const photo_stream = await this.getPhotoData(dialog, photo, person.displayName, i === person.photos.length - 1, req.params.res);
            if (photo_stream && photo_stream.stream) {
              mimeType = photo_stream.mimeType;
              stream = photo_stream.stream;
              break;
            }
          }
        }

        if (stream) {
          res.status(200).contentType(mimeType);
          stream.pipe(res);
          return;
        }
      }
    }
    const favicon = config.isRunningOnGoogle() ?
      path.resolve(__dirname, '..', '..', 'public', 'icons', 'icon-120.png')
      : path.resolve(__dirname, '..', '..', '..', 'lib', 'public', 'icons', 'icon-120.png');
    res.status(200).sendFile(favicon);
    // res.status(404).end();
  }

  async contactPhotosGet(req: express.Request, res: express.Response): Promise<void> {
    if (req.params.person_id || req.params.vanity_id) {
      const dialog: Dialog = req['dialog'];
      let person: Person | Vanity;
      if (req.params.vanity_id) person = await data.users.vanity(req.params.vanity_id);
      else [person] = await dialog.people.byId(`people/${req.params.person_id}`);

      let photos;
      if (person) {
        photos = person.photos.map(id => { return { id: Buffer.from(id).toString('base64'), src: null }});
        await Promise.all(person.photos.map(async photo => {
          const parser = new DataURIParser();
          const { mimeType, stream } = await this.getPhotoData(dialog, photo, person.displayName);
          const bufs = [];
          stream.on('data', d => bufs.push(d));

          await new Promise<void>(c => {
            stream.on('end', () => {
              photos.find(p => p.id === Buffer.from(photo).toString('base64')).src = parser.format(mimeType, Buffer.concat(bufs)).content;
              c();
            });
          });
        }));
      }

      await dialog.saveSession();

      if (photos) {
        res.json(photos).end();
        return;
      }
    }

    res.sendStatus(404);
  }

  async contactPhotoPost(req: express.Request, res: express.Response): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (req.params.person_id) {
      let [person] = await dialog.people.byId(`people/${req.params.person_id}`);
      if (person) {
        const file = (req as any).file;
        if (file) {
          const parser = new DataURIParser();
          const id = await dialog.people.addPhoto(person, file);
          if (person.self) {
            person = new Person(person);
            await dialog.setSelf(person).catch(e => dialog.asyncError(e));
          }
          await dialog.saveSession();
          res.json({id: Buffer.from(id).toString('base64'), src: parser.format(file.fieldname, file.buffer).content}).end();
          return;
        }
      }
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404);
  }

  async contactPhotoDelete(req: express.Request, res: express.Response): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (req.params.person_id && req.params.photo_id) {
      const [person] = await dialog.people.byId(`people/${req.params.person_id}`);
      if (person) {
        const id = decodeURI(req.params.photo_id.replace(/%2F/g, '/'));
        await dialog.people.deletePhoto(person, Buffer.from(id, 'base64').toString()).catch(e => dialog.asyncError(e));
        // await dialog.saveSession();
        return this.contactPhotosGet(req, res);
      }
    }
    if (dialog) await dialog.saveSession();
    res.sendStatus(404);
  }
  
  async contactConnections(req: express.Request, res: express.Response): Promise<void> {
    const dialog: Dialog = req['dialog'];
    let person;
    let id;
    if (req.params.vanity) {
      person = lang.about.ABOUT_INFO.find(a => a.vanity === req.params.vanity);
      if (person) {
        res.json([]).end();
        return;
      }
      let vanity = req.params.vanity;
      person = await data.users.vanity(vanity);
      if (!person) {
        vanity = encodeURI(vanity);
        person = await data.users.vanity(vanity);
      }
      id = `profile/${vanity}`;
    }
    if (req.params.person_id) {
      if (req.params.profile_id) {
        const profile_id = req.params.profile_id && req.params.profile_id.length && req.params.profile_id[0] === 'n' ? 
          req.params.profile_id.slice(1) : req.params.profile_id;
        const profile_user = new ForaUser(profile_id);
        person = await data.people.byId(profile_user, `people/${req.params.person_id}`);
      } else [person] = await dialog.people.byId(`people/${req.params.person_id}`);
    }
    if (person && dialog) {
      const email = parsers.findEmail(person.comms);

      let profile_user = dialog.user;
      if (req.params.profile_id) {
        const profile_id = req.params.profile_id && req.params.profile_id.length && req.params.profile_id[0] === 'n' ? 
          req.params.profile_id.slice(1) : req.params.profile_id;
        profile_user = new ForaUser(profile_id);
        await data.users.init(profile_user, false);

        if (!person.network) {
          const people = await data.users.globalVanities(profile_user, profile_user.vanity);
          res.json(people.map(p => dialog.getPersonInfo(p))).end();
          return;
        }
      }

      let connections = email && email.length ? await data.people.connectionsByAttributeComms(profile_user, email) : null;
      if ((!connections || !connections.length) && profile_user.profile !== dialog.user.profile) {
        connections = email && email.length ? await data.people.connectionsByAttributeComms(dialog.user, email) : null;
      }

      if (email && email.length && (!connections || !connections.length) && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
        const conn_profiles = await data.people.connectedUserProfiles(profile_user, email);
        const gusers = await data.users.globalByIds(conn_profiles);
        const filter_users = gusers.filter(g => g.groups && g.groups.includes(dialog.group_host.id));
        connections = (await data.users.globalVanities(profile_user, filter_users.map(u => u.vanity))).map(p => new Person(p));
      }

      if (connections && connections.length) {
        const people = _.uniqBy(connections.filter(p => p && p.id !== person.id && p.vanity !== person.vanity), p => p.vanity ? p.vanity : p.id).map(p => dialog.getPersonInfo(p))
        res.json(people).end();
        return;
      }
    } 
    res.json([]).end();
  }

  async contactRecommendations(req: express.Request, res: express.Response): Promise<void> {
    const dialog: Dialog = req['dialog'];
    let person;
    let id;
    if (req.params.vanity) {
      person = lang.about.ABOUT_INFO.find(a => a.vanity === req.params.vanity);
      if (person) {
        res.json([]).end();
        return;
      }
      let vanity = req.params.vanity;
      person = await data.users.vanity(vanity);
      if (!person) {
        vanity = encodeURI(vanity);
        person = await data.users.vanity(vanity);
      }
      id = `profile/${vanity}`;
    }
    if (req.params.person_id) [person] = await dialog.people.byId(`people/${req.params.person_id}`);
    if (person) {
      if (!id) id = person.id;
      const is_self = dialog && dialog.me && (id === dialog.me.id || (dialog.me.vanity && dialog.me.vanity == person.vanity)) ? true : false;
      const email = is_self ? [dialog.user.email] : parsers.findEmail(person.comms);
      const recs_info: RecommendationInfo[] = []; 
      if (email && email.length) {
        const person_info = dialog.getPersonInfo(person);
        person_info.id = id;

        if (!dialog.user.isAuthenticated()) delete person_info.comms;

        const recs_to = await data.recommendations.find(null, email); 
        const rec_ids = [];
        
        for (const rec of recs_to.sort((a,b) => new Date(b.when).getTime() - new Date(a.when).getTime())) {
          if (!rec.public && !is_self && rec.from !== dialog.user.profile) continue;

          const from_me = rec.from === dialog.user.profile;
          const rec_info = await data.recommendations.format(rec, dialog.user.locale);

          if (from_me) {
            rec_info.from = dialog.getPersonInfo();
          } else {
            const from_email = rec_info.from.comms ? rec_info.from.comms.filter(c => c.type === 'mail').map(c => c.link.split(':')[1]) : null;
            if (from_email && from_email.length) {
              let load_vanity = true;
              if (dialog.user.isAuthenticated()) {
                const from = await dialog.people.findByComms(from_email);
                if (from && from.length) {
                  rec_info.from = dialog.getPersonInfo(from[0]);
                  load_vanity = false;
                }
              }

              const global = await data.users.globalByEmail(from_email);
              if (global && global.length) {
                rec_info.from.vanity = global[0].vanity;
                if (load_vanity) {
                  const vanity = await data.users.vanity(global[0].vanity);
                  if (vanity) {
                    const person = peopleUtils.personFromVanity(vanity);
                    rec_info.from = dialog.getPersonInfo(person);
                  }
                }
              }
            }

            if (!dialog.user.isAuthenticated()) delete rec_info.from.comms;
          }

          rec_info.to = person_info;

          const rec_id = `${rec_info.from.id}_${rec_info.to.id}`;
          if (!rec_ids.includes(rec_id)) {
            rec_ids.push(rec_id);
            recs_info.push(rec_info);
          } else {
            logging.warnFP(this.log_name, 'contacRecommendations', dialog.user.profile, `Skipping duplicate reccommendation ${rec_id}`);
          }
        }

        let from_id;
        if (is_self) from_id = dialog.user.profile;
        else if (email && email.length) {
          const global_person = await data.users.globalByEmail(email);
          if (global_person && global_person.length) {
            person_info.vanity = global_person[0].vanity;
            from_id = global_person[0].id;
          }
        }

        if (from_id) {
          let recs_from = await data.recommendations.find(from_id, null);

          for (const rec of recs_from.sort((a,b) => new Date(b.when).getTime() - new Date(a.when).getTime())) {
            if (!rec.public && !is_self) continue;
            const rec_info = await data.recommendations.format(rec, dialog.user.locale);
            const to_email = rec_info.to.comms ? rec_info.to.comms.filter(c => c.type === 'mail').map(c => c.link.split(':')[1]) : null;
            if (to_email && to_email.length) {
              let load_vanity = true;
              if (dialog.user.isAuthenticated()) {
                const to = await dialog.people.findByComms(to_email);
                if (to && to.length) {
                  rec_info.to = dialog.getPersonInfo(to[0]);
                  load_vanity = false;
                }
              }

              const global = await data.users.globalByEmail(to_email);
              if (global && global.length) {
                rec_info.to.vanity = global[0].vanity;
                if (load_vanity) {
                  const vanity = await data.users.vanity(global[0].vanity);
                  if (vanity) {
                    const person = peopleUtils.personFromVanity(vanity);
                    rec_info.from = dialog.getPersonInfo(person);
                  }
                }
              }
              if (!dialog.user.isAuthenticated()) delete rec_info.to.comms;
            }

            rec_info.from = person_info;

            const rec_id = `${rec_info.from.id}_${rec_info.to.id}`;
            if (!rec_ids.includes(rec_id)) {
              rec_ids.push(rec_id);
              recs_info.push(rec_info);
            } else {
              logging.warnFP(this.log_name, 'contacRecommendations', dialog.user.profile, `Skipping duplicate reccommendation ${rec_id}`);
            }
          }
        }

      } 

      res.json(recs_info).end();
    } else res.sendStatus(404);
  }

  async contactRecommendationUpdate(req: express.Request, res: express.Response): Promise<void> {
    const dialog: Dialog = req['dialog']; //await this.loadDialog(req, false);
    if (!dialog) {
      res.sendStatus(401);
      return;
    }
    const rec_id = req.params.rec_id;
    const action = req.params.action;

    try {
      const recommendation = await data.recommendations.get(rec_id);    
      if (recommendation) {
        switch(action) {
          case 'public':
            if (!recommendation.public) {
              recommendation.public = true;
              await data.recommendations.update(recommendation);
            }
            break;
          case 'private':
            if (recommendation.public) {
              recommendation.public = false;
              await data.recommendations.update(recommendation);
            }
            break;
        }

        const rec_info = await data.recommendations.format(recommendation, dialog.user.locale);
        rec_info.to = dialog.getPersonInfo();
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.json(rec_info).end();
        return;
      }
    } catch (e) {
      logging.errorFP(this.log_name, 'contactRecommendationUpdate', dialog.user.profile, `Error updating recommendation for ${rec_id} action ${action}`, e);
      await dialog.saveSession().catch(e => dialog.asyncError(e));
    }

    res.sendStatus(404);
  }

  async contactUpdate(req: express.Request, res: express.Response): Promise<void> {
    const dialog: Dialog = req['dialog']; //await this.loadDialog(req, false);

    if (!dialog) {
      res.sendStatus(401);
      return;
    }

    const json_convert = new JsonConvert();
    let in_person: PersonDto = null;
    try { 
      in_person = json_convert.deserializeObject(req.body, PersonDto); 
    } catch (e) {
      logging.warnFP(this.log_name, 'contactUpdate', dialog.user.profile, 'parse error', e);
      await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.status(422).json({code: 422, error: 'Unable to parse incoming person'}).end();
      return;
    }

    const validator = new Validator();
    const err = await validator.validate(in_person, { validationError: {target: false}});
    if (err && err.length) {
      await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.status(422).json({ code: 422, error: 'Unable to validate incoming person', fields: err, }).end();
      return;
    }

    const people = await dialog.people.byId(in_person.id);
    if (!people || !people.length) {
      await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.status(404).json({ code: 401, error: 'Person not found', }).end(); 
      return;
    }

    const person = new Person(people[0]);
    const raw_person = req.body;

    if (logging.isDebug(dialog.user.profile)) {
      logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `In person: ${JSON.stringify(in_person)}`);
      logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `Raw person: ${JSON.stringify(raw_person)}`);
    }

    try {
      if (in_person.name) {
        const [displayName, names, parens] = permuteName(in_person.name);
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `Updating displayName ${in_person.name} to ${displayName} and names ${names}`);
        if (displayName) {
          person.displayName = displayName;
          person.nickName = displayName.split(' ')[0];
        }
        if (names.length) person.names = names;
      }

      if (in_person.image) {
        const image = Buffer.from(in_person.image, 'base64').toString();
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `Updating image to ${image}`);
        if (person.photos.includes(image)) {
          person.photos = [image, ...person.photos.filter(p => p !== image)];
        } else person.photos.splice(0,0,image);
      }

      let update_rec_email = false;
      let remove_rec_email = [];
      if (in_person.comms) {
        let email = parsers.findEmail(in_person.comms);
        let phones = parsers.findPhone(in_person.comms);
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `Updating comms to ${JSON.stringify([...email, ...phones])}`);

        const has_email = parsers.findEmail(person.comms);
        update_rec_email = !arraysMatch(email, has_email);
        if (update_rec_email && has_email) remove_rec_email = has_email.filter(e => !email.includes(e));

        if (!email) email = [];
        if (!phones) phones = [];

        person.comms = [person.id].concat(email).concat(phones);
      }

      if (in_person.links) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `Updating links from ${person.urls} to ${in_person.links}`);
        person.urls = in_person.links;
      } 

      if (!person.tags) person.tags = [];

      if (in_person.tags) {
        const skills = person.tags.filter(t => t.type === TagType.skill);
        const non_job_tags = person.tags.filter(t => !JobTags.includes(t.type));

        /* const in_tags = in_person.tags.map(t => { 
          let type: TagType = t.type === TagType.default ? t.tag : t.type;
          if (type === TagType.title) type = TagType.jobTitle;
          return new Tag(type, t.value, t.index, t.start);
        });*/

        // resolve changes to extra and sub tags
        for (const extra_tag of in_person.tags) {
          const parts = extra_tag.value ? tagParts(extra_tag.value) : [];
          if (extra_tag.tags) {
            const render_tag = formatJob(extra_tag.tags);
            if (render_tag && render_tag.value !== extra_tag.value) {
              // re-create tags to match parts
              const range = findTypeTag(extra_tag.tags, TagType.range);
              extra_tag.tags = [new Tag(TagType.symbol, extra_tag.type, extra_tag.index, extra_tag.start)].concat(parts.map(p => new Tag(p.type, p.value, extra_tag.index, extra_tag.start)));
              if (range) extra_tag.tags.push(range);
            }

            // update indexes to match
            for (const tag of extra_tag.tags) tag.index = extra_tag.index;

            // check for icon
            const symbol = findTypeTag(extra_tag.tags, TagType.symbol);
            if (symbol) symbol.value = extra_tag.type;
            else if(extra_tag.tags.filter(t => t.type !== TagType.range).length > 1) {
              extra_tag.tags.push(new Tag(TagType.symbol, extra_tag.type, extra_tag.index, extra_tag.start));
            }
          } else {
            // create tags to match parts
            extra_tag.tags = [new Tag(TagType.symbol, extra_tag.type, extra_tag.index, extra_tag.start)].concat(parts.map(p => new Tag(p.type, p.value, extra_tag.index, extra_tag.start)));
          }
        }

        const in_tags = flatten(in_person.tags.map(t => t.tags).filter(t => t)).map(t => { return new Tag(t.type, t.value, t.index, t.start) } );
        person.tags = skills.concat(non_job_tags).concat(in_tags);
        const orgs = person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
        parsers.cleanTags(person.tags, person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
      }

      if (in_person.skills && in_person.skills.length) {
        const old_skills = person.tags.filter(t  => t.type  === TagType.skill);
        const non_skills = person.tags.filter(t => t.type !== TagType.skill);

        const in_skills = in_person.skills.map(s => s.toLowerCase());
        // const keep_skills = old_skills.filter(s => s.value && in_skills.includes(s.value.toLowerCase()));
        const rem_skills = old_skills.filter(s => s.value && !in_skills.includes(s.value.toLowerCase()));
        rem_skills.forEach(s => s.index = -1);
        person.tags = non_skills.concat(old_skills);
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `Updated skills to ${JSON.stringify(old_skills)}`);

        const index = 200;
        const start = new Date();

        for (const value of in_person.skills) {
          const tag = new Tag(TagType.skill, value, index, start, 100);
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `Adding skill ${JSON.stringify(tag)}`);
          saveOneTypeValue(person.tags, tag);
        }
      }

      if (raw_person.meta !== undefined) {
        person.tags = person.tags.filter(t => t.type !== TagType.meta);
        if (in_person.meta && in_person.meta.length) {
          const meta = new Tag(TagType.meta, in_person.meta);
          person.tags.push(meta);
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(this.log_name, 'contactUpdate', dialog.user.profile, `Setting meta ${JSON.stringify(meta)}`);
        }
      }

      if (raw_person.recommendation !== undefined || update_rec_email) {
        if (raw_person.recommendation === null || raw_person.recommendation === undefined || !raw_person.recommendation.length) {
          if (person.recommendation) {
            const email = parsers.findEmail(person.comms);
            const recommendations = await data.recommendations.find(dialog.user.profile, remove_rec_email.concat(email));
            if (recommendations && recommendations.length) {
              for (const rec of recommendations) data.recommendations.delete(rec);
            }
          }
          delete person.recommendation;
        } else {
          person.recommendation = in_person.recommendation;

          const email = parsers.findEmail(person.comms);
          const recommendations = await data.recommendations.find(dialog.user.profile, remove_rec_email.concat(email));
          if (recommendations && recommendations.length) {
            if (email && email.length) {
              for (const rec of recommendations) {
                rec.text = person.recommendation;
                rec.to = email;
                data.recommendations.update(rec);
              } 
            } else {
              for (const rec of recommendations) data.recommendations.delete(rec);
            }
          } else {
            if (email && email.length) {
              const rec = await data.recommendations.create(new Recommendation({from: dialog.user.profile, to: email, text: person.recommendation, public: false}));
              const global_user = await data.users.globalByEmail(email);
              if (global_user && global_user.length) {
                const guser = new ForaUser(global_user[0].profile);
                const url = mapURL('/app/settings/profile');
                await notify(guser, {
                  type: NotificationType.Recommendation,
                  webpush: {
                    notification: {
                      tag: rec.id,
                      title: 'You have a new recommendation',
                      body: `Check out your recommendation from ${dialog.me.displayName}`,
                      click_action: url,
                    },
                  },
                  email: {
                    rcpts: [{Name: global_user[0].name, Email: global_user[0].email}],
                    subject: 'You have a new recommendation',
                    message: `Check out your recommendation from ${dialog.me.displayName}: ${url}`,
                  },
                  variables: {firstname: global_user[0].name, url},
                }, NotifyType.PushOrEmail, dialog.group_host, false)
              }
            }
          }
        }
      }

      if (person.self && raw_person.bio !== undefined) {
        person.bio = in_person.bio;
        const bio_skills = parsers.findMeaning(person.bio);
        const now =  new Date();
        bio_skills.forEach(skill => {
          saveOneTypeValue(person.tags, new Tag(TagType.skill, skill, 200, now, 50));
        });
      }

    } catch(e) {
      logging.errorFP(this.log_name, 'contactUpdate', dialog.user.profile, `Error updating person ${person.id}`, e);
    }

    if (person.self) person.learned = [new Date(Number.MAX_SAFE_INTEGER / 1000)];

    try {
      let save_person;
      if (person.self) save_person = await dialog.setSelf(person);
      else save_person = await dialog.people.save(person);

      const person_info = dialog.getPersonInfo(save_person);
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      res.status(200).json(person_info).end();
    } catch(e) {
      if (dialog) dialog.asyncError(e);
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      res.sendStatus(501);
    }
  }

  async contactFind(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    const group: Group = req['group'];
    const dialog: Dialog = req['dialog'];
    const session_id: string = req['session_id'];

    if (!group && !dialog && !session_id) {
      logging.errorFP(this.log_name, 'contactFind', dialog ? dialog.user.profile : 'UNKNOWN_PROFILE', 'Missing one of group, user or session_id', null);
      res.status(500).json({ code: 500, error: 'Internal Server Error' }).end();
      return;
    } else if (dialog || session_id) {
      // process user
    } else if (group) {
      // process group
    }

    return createHandler({ schema, rootValue: { people: controller.findPeople }, context: {dialog: req['dialog']}})(req, res, next);
  }

  async contactLearn(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    const group: Group = req['group'];
    const session_id: string = req['session_id'];
    let dialog: Dialog = req['dialog'];

    if (!group && !dialog && !session_id) {
      logging.errorFP(this.log_name, 'contactLearn', dialog ? dialog.user.profile : 'UNKNOWN_PROFILE', 'Missing one of group, user or session_id', null);
      res.status(500).json({ code: 500, error: 'Internal Server Error' }).end();
      return;
    }

    if (!dialog || !dialog.isAuthenticated()) {
      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.sendStatus(404).end();
      return;
    }

    const json_convert = new JsonConvert();
    let in_person: PersonDto = null;
    try { 
      in_person = json_convert.deserializeObject(req.body, PersonDto); 
    } catch (e) {
      logging.errorFP(this.log_name, 'contactLearn', dialog.user.profile, 'parse error', e);
      await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.status(422).json({code: 422,error: 'Unable to parse incoming person',}).end();
      return;
    }

    const validator = new Validator();
    const err = await validator.validate(in_person, { validationError: {target: false}});
    if (err && err.length) {
      await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.status(422).json({ code: 422, error: 'Unable to validate incoming person', fields: err, }).end();
      return;
    }

    let person: Person;
    let internal_learn = false;
    if (in_person.id && in_person.id !== 'people/learn') {
      await dialog.saveSession();
      dialog = await Dialog.loadSession('learn', req.session, req.cookies, {offset:req.body.offset, read_only: false, no_cache: true, create_new:true, check_tokens:false, stack:Error().stack});
      if (dialog.me && in_person.id === dialog.me.id) person = new Person(dialog.me);
      else [person] = await dialog.people.byId(in_person.id);

      if (person) person.learned = [new Date(0)];
      internal_learn = true;
    } else { 
      const [displayName, _names, _parens] = permuteName(in_person.name);
      const tags = in_person.tags ? in_person.tags.map(t => { 
        //let type: TagType = t.type === TagType.default ? t.tag : t.type;
        let type = t.type;
        if (type === TagType.title) type = TagType.jobTitle;
        return new Tag(type, t.value, t.index, t.start);
      }) : [];

      person = new Person({
        displayName: displayName ? displayName : 'NO NAME',
        tags
      });

      person.tempId();
    }

    if (person) {
      try {
        if (internal_learn) {
          if (person.self) person = await dialog.setSelf(person);
          else person = await dialog.people.save(person);
          await internalLearn({profile: dialog.user.profile, person: person.id, force_local: true, no_export: true});
          // refretch after learn
          [person] = await dialog.people.byId(in_person.id);

          // testing
          // saveOneTypeValue(person.tags, new Tag(TagType.skill, 'test learn skills', 500, new Date(), 500));

          // if (config.isEnvOffline()) person = await dialog.people.byId(in_person.id);
        }
        else {
          const learn = new Learn(dialog.user);
          person = await learn.learnPerson(person);
          person = await dialog.people.save(person);
        }

        // TODO: update big query
        // await data.people.bigQueryUpdate(dialog.user.profile, person).catch(e => logging.errorFP(this.log_name, 'contactLearn', dialog.user.profile, `Error updating big query`, e));

        const person_info = dialog.getPersonInfo(person);
        await dialog.saveSession();
        res.json(person_info).end(); 
      } catch(e) {
        logging.errorFP(this.log_name, 'learnPerson', dialog.user.profile, `Error learning ${JSON.stringify(in_person)}`, e);
        await dialog.safeSaveSession();
        res.sendStatus(500);
      }
    } else {
      await dialog.safeSaveSession();
      res.sendStatus(404).end();
    }
  }

  async contactSearch(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];

    if (dialog && dialog.isAuthenticatedNonGuest()) {
      const params = req.body as { filters: Filter[]; group?: Uid };

      let people: Partial<Person>[] = [];

      // const searches: Promise<Person[]>[] = [];
      
      if (params.filters) {
        params.filters.forEach(f => {
          f.conditions?.forEach(c => {
            c?.value?.forEach(v => {
              if (v?.start) v.start = new Date(v.start); 
            });
          });
        });
        const group = params.group && dialog.user.loaded_groups ? dialog.user.loaded_groups[params.group] : dialog.group_host;
        people = await dialog.search.searchByFilter(params.filters, group);
      }

      await dialog.safeSaveSession();
      logging.infoFP(this.log_name, 'contactSearch', dialog.user.profile, `${people.length} matched filters ${JSON.stringify(params.filters)}`);

      const info = people.map(r => dialog.getPersonInfo(r));

      res.json(info).end();
      return;
    }

    if (dialog) await dialog.safeSaveSession();
    res.sendStatus(404).end();
  }

  async findPeople({name, email, id, imports, skills, tags, orgs, roles, industry, location, first, network, recommended, groups, all_groups, users, guest}: { 
      name?: string[]; 
      email?: string[]; 
      id?: string[]; 
      imports?: string[];
      skills?: string[]; 
      tags?: string[]; 
      orgs?: string[];
      roles?: string[];
      industry?: string[];
      location?: string[];
      first?: boolean;
      network?: boolean;
      recommended?: boolean;
      groups?: string[];
      all_groups?: boolean;
      users?: boolean;
      guest?: boolean;
    }, 
    context) {
    const dialog: Dialog = context.dialog; //req['dialog'];
 
    if (dialog) {
      let people: Partial<Person>[] = [];
      let reload = false;
      if (id && id.length) {
        // logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Finding ${id}`);
        let ids = Array.isArray(id) ? id : [id];
        if (dialog.me) {
          if (ids.includes(dialog.me.id) || (dialog.user.vanity && ids.includes(`profile/${dialog.user.vanity}`))) {
            ids = ids.filter(i => i !== dialog.me.id && (!dialog.user.vanity || i !== `profile/${dialog.user.vanity}`));
            people.push(dialog.me);
          }
        }

        const user_profiles = id.filter(i => i.split('/').length === 2);
        
        const profiles = user_profiles.filter(i => i && i.startsWith('profile'));
        const about_profiles = lang.about.ABOUT_INFO.map(p => p.id);
        const about_vanities = lang.about.ABOUT_INFO.map(p => p.vanity);
        if (profiles.length) {
          const vanities = profiles.map(p => p.split('/')[1]).filter(v => v.length);
          people = people.concat(lang.about.ABOUT_INFO.filter(a => vanities.includes(a.vanity)));
        }

        people = people.concat(lang.about.ABOUT_INFO.filter(a => user_profiles.includes(a.id)));

        people = [...people, ...(await dialog.people.loadPeople(user_profiles.filter(id => id && id.length && 
            !about_profiles.includes(id) && 
            (!id.startsWith('profile') || !about_vanities.includes(id.split('/')[0])))
          .map(id => {
          return {
            id,
            vanity: id.startsWith('profile/') ? id.split('/')[1] : undefined
          } as Partial<Person>
        })))];

        const network_profiles = id.filter(i => i.split('/').length === 4);
        if (network_profiles.length && dialog.group_host && dialog.user.isAdmin(dialog.group_host.id)) {
          const users: {[key:string]: {user: ForaUser, ids: Uid[]}} = {};
          const profiles = _.uniq(network_profiles.map(n => n.split('/')[1]));
          for (const nprofile of profiles) {
            const profile = nprofile.length && nprofile[0] === 'n' ? nprofile.slice(1) : nprofile;
            if (!users[profile]) users[profile] = {user: new ForaUser(profile), ids:[]};
          }

          await Promise.all(Object.values(users).map(u => data.users.init(u.user)));

          for (const np of network_profiles) {
            const parts = np.split('/');
            const id = `people/${parts[3]}`;
            const nprofile = parts[1];
            const profile = nprofile.length && nprofile[0] === 'n' ? nprofile.slice(1) : nprofile;

            users[profile].ids.push(id);
          }

          const net_people: Person[] = flatten(await Promise.all(Object.values(users).map(async (u) => {
            const p = await data.people.byAttributeId(u.user, u.ids);
            if (p) {
              const pl = p.map(x => new Person(x));
              pl.forEach(x => {
                x.id = `people/n${u.user.profile}/${x.id}`
              });
              return pl;
            }
          })));

          people = people.concat(net_people);
        }

        await data.people.updatePeopleWithProfiles(dialog.user, people);

        const unique_people = _.uniqWith(people, (a,b) => {
          if(a.id === b.id) return true;
          if (a.vanity && b.vanity && a.vanity === b.vanity) return true;
          return false;
        });

        return dialog.queryPeople(unique_people);
      } else if(network || groups || all_groups || (skills && skills.length) || (tags && tags.length) || (imports && imports.length)) {
        const mandatory_tags: Tag[][] = [];
        const group_search_ids: Uid[] = []; 
        if ((groups || all_groups) && dialog.user.loaded_groups) {
          const lgroups = groups ? flatten(groups.map(g => g.toLowerCase().split(' '))) : [];
          for (const group of Object.values(dialog.user.loaded_groups) as Group[]) {
            if (all_groups || arrayIncludes([
              ...group.company_name.toLowerCase().split(' '),
              ...group.email_domain.map(ed => ed.toLowerCase().split('@')[1]),
              ...group.name.toLowerCase().split(' ')], lgroups)) {
                group_search_ids.push(group.id);
                mandatory_tags.push(group.search_mandatory_tags);
              }
          }
        }

        if (email && email.length) {
          logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Finding network email ${email}`);
          people = await dialog.search.searchByEmail(email, network, mandatory_tags, group_search_ids);
        } else if(name && name.length) {
          logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Finding network name ${name}`);
          people = await dialog.search.searchByName(name, network, mandatory_tags, group_search_ids);
        } else {
          logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Finding network skills ${skills}`);
          const found_skills = skills && skills.length ? await lookupSkill(skills, dialog.user.locale) : [];
          const search_skills = [];

          for (const skill of found_skills) {
            search_skills.push(skill.skill);
            if (skill.synonyms) for (const synonym of skill.synonyms) search_skills.push(synonym);
            if (skill.initialisms) for (const initials of skill.initialisms) search_skills.push(initials);
          }

          skills = skills ? skills.map(s => s.toLowerCase()) : [];

          // for now orgs, roles, industry, location are skill searches
          if (roles) skills = skills ? skills.concat(roles) : roles;
          if (industry) skills = skills ? skills.concat(industry) : industry;
          if (location) skills = skills ? skills.concat(location) : location;
          if (orgs) skills = skills ? skills.concat(orgs) : orgs;
          if (tags) skills = skills ? skills.concat(tags) : tags;
          if (imports) skills = skills ? skills.concat(imports) : imports;

          let net_state: 'include' | 'exclude' | 'only'  = 'exclude';
          if (network) {
            if (first) net_state = 'include';
            else net_state = 'only'
          } else if(first !== undefined && !first) net_state = 'only';

          if (users) people = await dialog.people.userPeople(skills, [TagType.skill], guest);
          else people = await dialog.search.searchSkills([...skills, ...search_skills], {
            network: net_state, 
            require_email: false, 
            mandatory_tags, 
            group_search_ids
          });

          if (people) {
            people = people.filter(p => (!p.comms || !p.comms.includes(dialog.user.email)) && 
              (!p.vanity || p.vanity !== dialog.user.vanity));
          }
        }
      } else if(email && email.length) {
        logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Finding ${email}`);
        people = await dialog.people.findByComms(email, false, false);
      } else if(name && name.length) {
        logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Finding ${name}`);
        await dialog.cache.loadCache();
        if (dialog.cache.people_loaded) await dialog.cache.people_loaded;
        people = await dialog.people.findByName(name);
        if (!people || !people.length) people = await dialog.search.searchByName(name, false);
        reload = true;
      } else if(orgs && orgs.length) {
        logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Finding ${orgs}`);

        let net_state: 'include' | 'exclude' | 'only'  = 'exclude';
        if (network) {
          if (first) net_state = 'include';
          else net_state = 'only'
        } else if(first !== undefined && !first) net_state = 'only';


        people = await dialog.people.findByOrg(orgs);
        if (!people || !people.length) people = await dialog.search.searchSkills(orgs, {network: net_state });
        reload = true;
      } 

      if (people && people.length) {
        const filter_people = async (people: Partial<Person>[]) => {
          if (id && id.length) return people.filter(p => id.includes(p.id));

          if (email && email.length) {
            const lemail = email.map(e => e.toLowerCase());
            return people.filter(p => arrayIncludes(p.comms.map(c => c.toLowerCase()), lemail));
          }

          if (network || (groups && groups.length) || all_groups || users) {
            const needs_temp = people.filter(p => p.network);
            const first_connections = people.filter(p => !p.network);
            const second_connections: Person[] = [];
            logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Connecting ${needs_temp.length} people`);
            for (const person of needs_temp) {
              const temp_person = await dialog.people.temp(person, true)
              second_connections.push(temp_person);
            }
            people = first_connections.concat(second_connections);
          } else people = people.filter(p => !p.network);

          if (name && name.length) {
            const lnames = flatten(name.map(n => n.toLowerCase().split(' ')));
            people = people.filter(p => p.names && arrayIncludes(p.names.map(n => n.toLowerCase()), lnames));
          }

          if (orgs && orgs.length) {
            const lorgs = flatten(orgs.map(o => o.toLowerCase().split(' ')));
            people = people.filter(p => p.tags && arrayIncludes( findTypeValues(p.tags, OrgTags).map(t => t.toLowerCase()), lorgs));
          }

          if (roles && roles.length) {
            const lroles = flatten(roles.map(o => o.toLowerCase().split(' ')));
            people = people.filter(p => p.tags && arrayIncludes(findTypeValues(p.tags, RoleTags).map(t => t.toLowerCase()), lroles));
          }

          if (industry && industry.length) {
            const lindustry = flatten(industry.map(o => o.toLowerCase().split(' ')));
            people = people.filter(p => p.tags && arrayIncludes(findTypeValues(p.tags, IndustryTags).map(t => t.toLowerCase()), lindustry));
          }
  
          if (location && location.length) {
            const llocation = flatten(location.map(o => o.toLowerCase().split(' ')));
            people = people.filter(p => p.tags && arrayIncludes(p.tags.filter(t => t.value).map(t => t.value.toLowerCase()), llocation));
          }

          if (recommended) people = people.filter(p => p.recommendation !== null && p.recommendation !== undefined);

          return people;
        }

        if (reload) logging.infoFP(this.log_name, 'findPeople', dialog.user.profile, `Reloading ${people.filter(p => !p.network).length} people`);
        const loaded_people = await filter_people( !reload ? people :
          (await dialog.people.byId(people.filter(p => !p.network).map(p => p.id))));

        return dialog.queryPeople(loaded_people.filter(p => p.id).sort((a,b) => a.displayName < b.displayName ? -1 : 1)) /* {
          if (a.network) {
             if(b.network) return -1;
             else return 1;
          }
          if (b.network) return -1;
        }));*/
      }
    }

    return [];
  }

  async orgsGet(req: express.Request, res: express.Response, next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const limit = req.query.limit ? parseInt(req.query.limit as string, 10) : 10;
      const network = req.query.network !== undefined && req.query.network !== 'false';
      const groups = req.query.groups !== undefined && req.query.group !== 'false';
      const orgs = await dialog.people.getOrgStats(limit, network, groups);
      res.json(orgs).end();
      return;
    }
    res.sendStatus(404);
  }
}

// Build our router/controller and map them
const router = express.Router();
const controller = new ContactsController();

const schema = buildSchema (/* GraphQL */ `
  type Query {
    people(
      name: [String], 
      email: [String], 
      id: [ID], 
      imports: [String],
      skills: [String], 
      tags: [String], 
      orgs: [String],
      roles: [String],
      industry: [String],
      location: [String],
      network: Boolean,
      first: Boolean,
      recommended: Boolean,
      groups: [String],
      all_groups: Boolean,
      users: Boolean,
      guest: Boolean,
      ): [Person]
  }

  """
  Person represents a contact 
  """
  type Person {
    comms: [Link],
    contracts: [ContractInfo],
    description(date: String): [BasicInfo],
    extra(type: String): [Extra],
    id: ID,
    askfora_id: String,
    image: String,
    learned: Boolean,
    links(type: String): [Link],
    meta: String,
    name: String,
    network: Boolean,
    groups: [String],
    nick: String,
    orgs: [String],
    recommendation: String,
    skills: [Skill],
    projects: [ProjectInfo],
    related(date: String): [Person],
    related_ids(date: String): [ID],
    self: Boolean,
    tasks(due: String, completed: Boolean): [TaskInfo],
    vanity: String,
    bio: String,
    state: String,
    status: String,
    ready: Boolean,
    type: String,
  }

  """
  Candidate represents a candidate 
  """
  type Candidate {
    comms: [Link],
    contracts: [ContractInfo],
    description(date: String): [BasicInfo],
    extra(type: String): [Extra],
    id: ID,
    askfora_id: String,
    answer: String,
    public: Boolean,
    groups: [String],
    image: String,
    learned: Boolean,
    links(type: String): [Link]
    meta: String,
    name: String,
    network: Boolean,
    nick: String,
    orgs: [String],
    recommendation: String,
    skills: [Skill],
    projects: [ProjectInfo],
    related(date: String): [Person],
    related_ids(date: String): [ID],
    state: String,
    ready: Boolean,
    self: Boolean,
    tasks(due: String, completed: Boolean): [TaskInfo],
    vanity: String,
    bio: String,
    type: String,
    refer_by: [Person],
  }


  """
  Call or mail links
  """
  type Link {
    link: String, 
    type: String,
  }

  """
  Weighted skill
  """
  type Skill {
    value: String,
    weight: Int,
  }

  """
  Tagged contact information about a contact
  """
  type Extra {
    type: String,
    start: String,
    tags: [Extra],
    value: String,
    index: Int
  }

  """
  Descriptive information about a contact such as emails and events
  """
  type BasicInfo {
    "Link to the native calendar entry"
    calendar: String,
    "True if this is a message"
    message: Boolean,
    id: ID,
    label: String,
    link: String,
    people: [Person],
    read: Boolean,
    text: String,
    date: String,
    image: String
  }

  """
  Contracts signed with the contact
  """
  type ContractInfo {
    id: ID,
    canceled: Boolean,
    client_email: String,
    client_name: String,
    client_signed: Boolean,
    contractor_email: String,
    contractor_name: String,
    contractor_signed: Boolean,
    url: String
    type: String,
  }

  """
  Notes and Tasks referencing a contact
  """
  type TaskInfo {
    due: String,
    completed: Boolean,
    created: String,
    id: ID,
    title: String,
    notes: String,
    project: String,
    people: [Person]
    type: String,
  }

  """
  Jobs with the contact
  """
  type ProjectInfo {
    id: ID,
    admin: Boolean,
    archived: Boolean,
    candidates: [Candidate],
    suggested: [String],
    client: Person,
    completed: Boolean,
    confidential: Boolean,
    contract: String,
    contractor: Candidate,
    duration: Int,
    end: String,
    escrow: Boolean,
    fee: Int,
    flex_dates: Boolean,
    me_candidate: Candidate,
    network: Boolean,
    notes: String,
    requirements: String,
    deliverables: String,
    background: String,
    payment: Payment,
    profile: Boolean,
    progress: Int,
    proposal: Boolean,
    template: Boolean,
    expert: Boolean,
    accepted: Boolean,
    viewed: Boolean,
    declined: Boolean,
    rate: String,
    refund: Payment,
    service_fee: Float,
    skills: String,
    skill_set: [String],
    searched_skills: [String],
    searching: Boolean,
    start: String,
    title: String,
    type: String,
    update_date: String,
    activity_date: String,
    simple_invite: Boolean,
    no_fee: Boolean,
    no_referrals: Boolean,
    public: Boolean,
    groups: [String],
    sourcing_url: String,
    sourcing_type: String
  }

  type Payment {
    created: String,
    amount: Int,
  }
`);

const storage = multer.memoryStorage();
const upload = multer({storage});
const photo_upload = upload.single('photo');

router.route('/default/:initials/photo').get(controller.initialsPhotoGet);
router.route('/people/:person_id/photo').post(new UserAuthValidation({read_only: false}).handle, photo_upload, controller.contactPhotoPost);
router.route('/people/:person_id/photo/:photo_id').delete(new UserAuthValidation({read_only: false}).handle, controller.contactPhotoDelete);
router.route('/people/:person_id/photos').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactPhotosGet);
router.route('/profile/:vanity_id/photos').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactPhotosGet);

router.route('/people/:person_id/connections').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactConnections);
router.route('/people/n:profile_id/:person_id/connections').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactConnections);
router.route('/people/:profile_id/people/:person_id/connections').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactConnections);

router.route('/people/n:profile_id/profile/:vanity/connections').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactConnections);
router.route('/people/:profile_id/profile/:vanity/connections').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactConnections);
router.route('/profile/:vanity/connections').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactConnections);

router.route('/people/:person_id/recommendations').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactRecommendations);
router.route('/profile/:vanity/recommendations').get(new UserAuthValidation({read_only: true, no_cache: true }).handle, controller.contactRecommendations);
router.route('/recommendations/:rec_id/:action').put(new UserAuthValidation({no_cache: true}).handle, controller.contactRecommendationUpdate);

router.route('/people/:person_id/photo/:res?').get(new SignedAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true }).route, controller.contactPhotoGet);
router.route('/people/:profile_id/people/:person_id/photo/:res?').get(new SignedAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true }).route, controller.contactPhotoGet);
router.route('/profile/:vanity/photo/:res?').get(new SignedAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).route, controller.contactPhotoGet);
router.route('/:contact_id/photo/:res?').get(new SignedAuthValidation().route, new UserAuthValidation({read_only: true, no_cache: true}).route, controller.contactPhotoGet);

router.route('/update').post(new UserAuthValidation({read_only: false}).handle, controller.contactUpdate);
router.route('/orgs').get(new UserAuthValidation({read_only: true, no_cache: true}).handle, controller.orgsGet);
router.route('/find').get(
  new GroupAuthValidation().route, 
  new UserAuthValidation({ read_only: true }).route, 
  controller.contactFind, 
);
router.route('/find').post(
  new GroupAuthValidation().route, 
  new UserAuthValidation({ read_only: true } ).route, 
  controller.contactFind, 
);
router.route('/learn').post(
  new GroupAuthValidation().route, 
  new UserAuthValidation({ read_only: true, no_cache: true }).route, 
  controller.contactLearn
);

router.route('/search').post(
  new GroupAuthValidation().route, 
  new UserAuthValidation({ read_only: true }).route, 
  controller.contactSearch
);

export default router;
