/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { JsonConvert } from 'json2typescript';

import Dialog from '../../session/dialog';

import { Task } from '../../types/items';

import { DAYS, HOURS } from '../../utils/datetime';
import logging from '../../utils/logging';
import peopleUtils from '../../utils/people';

import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { TaskDto } from '../models/task';
import { AbstractController } from './a_controller';

class NotesController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:notes');
  log_name = 'rest.controllers.NotesController';

  constructor() {
    super();

    this.noteGet = this.noteGet.bind(this);
    this.notesGet = this.notesGet.bind(this);
    this.noteComplete = this.noteComplete.bind(this);
    this.noteUncomplete = this.noteUncomplete.bind(this);
    this.noteClear = this.noteClear.bind(this);
    this.noteLater = this.noteLater.bind(this);
    this.noteSave = this.noteSave.bind(this);
    this.noteDelete = this.noteDelete.bind(this);
  }

  async noteGet(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const id = req.params.id;
      let task: Task;
      if (id) task = dialog.cache.tasks[id];
      await dialog.saveSession();
      if (task) {
        const task_info = peopleUtils.personInfoTasks([task], dialog.me);
        res.json(task_info[0]).end();
      } else res.sendStatus(404).end();
      return;
    }
    res.sendStatus(404).end();
  }

  async notesGet(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const tasks: Task[] = Object.values(dialog.cache.tasks);
      await dialog.saveSession();
      const task_info = peopleUtils.personInfoTasks(tasks, dialog.me);
      res.json(task_info).end();
      return;
    }
    res.sendStatus(404).end();
  }

  async noteComplete(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const id = req.params.id;
      let task: Task;
      if (id) task = dialog.cache.tasks[id];

      if(task) {
        task.completed = new Date();
        task = await dialog.tasks.create(task);
      }

      await dialog.saveSession();
      if (task) {
        const task_info = peopleUtils.personInfoTasks([task], dialog.me);
        res.json(task_info[0]).end();
      } else res.sendStatus(404).end();
      return;
    }
    res.sendStatus(404).end(); 
  }

  async noteUncomplete(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const id = req.params.id;
      let task: Task;
      if (id) task = dialog.cache.tasks[id];

      if(task) {
        delete task.completed;
        task = await dialog.tasks.create(task);
      }

      await dialog.saveSession();
      if (task) {
        const task_info = peopleUtils.personInfoTasks([task], dialog.me);
        res.json(task_info[0]).end();
      } else res.sendStatus(404).end();
      return;
    }
    res.sendStatus(404).end();
  }

  async noteClear(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const id = req.params.id;
      let task: Task;
      if (id) task = dialog.cache.tasks[id];

      if(task) {
        delete task.due;
        task = await dialog.tasks.create(task);
      }

      await dialog.saveSession();
      if (task) {
        const task_info = peopleUtils.personInfoTasks([task], dialog.me);
        res.json(task_info[0]).end();
      } else res.sendStatus(404).end();
      return;
    }
    res.sendStatus(404).end(); 
  }

  async noteLater(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const id = req.params.id;
      let task: Task;
      if (id) task = dialog.cache.tasks[id];

      if(task) {
        let due = new Date(task.due);
        let tdue = due.getTime();
        if (!task.due || isNaN(tdue) || tdue === 0) task.due = new Date();
        switch(req.body.when) {
          case 'today':
            if (due < HOURS(new Date(), 4)) task.due = HOURS(new Date(), 4);
            else task.due = HOURS(due, 1);
            break;
          case 'tomorrow':
            task.due = DAYS(due, 1);
            break;
          case 'next week':
            task.due = DAYS(due, 4);
          default:
            break;
        }
        task = await dialog.tasks.create(task);
      }

      await dialog.saveSession();
      if (task) {
        const task_info = peopleUtils.personInfoTasks([task], dialog.me);
        res.json(task_info[0]).end();
      } else res.sendStatus(404).end();
      return;
    }
    res.sendStatus(404).end();
  }

  async noteSave(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      const json_convert = new JsonConvert();
      let in_task: TaskDto = null;
      try { 
        in_task = json_convert.deserializeObject(req.body, TaskDto); 
      } catch (e) {
        logging.warnFP(this.log_name, 'noteSave', dialog.user.profile, 'parse error', e);
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.status(422).json({code: 422, error: 'Unable to parse incoming task'}).end();
        return;
      }

      if (in_task) {
        let task;
        try {
          if (in_task.id) {
            task = await dialog.cache.tasks[in_task.id];
            if (task) {
              if (in_task.title) task.title = in_task.title;
              if (in_task.notes) task.notes = in_task.notes;
              if (in_task.due) task.due = new Date(in_task.due);
              if (in_task.created) task.created = new Date(in_task.created);
              if (in_task.completed && !task.completed) task.completed = new Date();
              if (in_task.project) task.project = in_task.project;
              if (in_task.people) {
                const people_ids = in_task.people.map(p => p.id).filter(id => id);
                const people = await dialog.people.byId(people_ids);
                task.people = people.map(p => {
                  return { 
                    id: p.id,
                    displayName: p.displayName,
                    nickName: p.nickName,
                    network: p.network,
                    photos: p.photos,
                  }
                });
              }

              task = await dialog.tasks.update(task);
            } else {
              await dialog.saveSession();
              res.sendStatus(404).end();
              return;
            }
          } else {
            let people;

            if (in_task.people) {
              const people_ids = in_task.people.map(p => p.id).filter(id => id);
              const found_people = await dialog.people.byId(people_ids);
              people = found_people ? found_people.map(p => {
                return { 
                  id: p.id,
                  displayName: p.displayName,
                  nickName: p.nickName,
                  network: p.network,
                  photos: p.photos,
                }
              }) : undefined;
            }

            task = new Task({
              title: in_task.title,
              notes: in_task.notes,
              due: new Date(in_task.due),
              created: new Date(),
              completed: in_task.completed ? new Date() : undefined,
              project: in_task.project,
              people,
            });
            task = await dialog.tasks.create(task);
          }
        } catch(e) {
          logging.errorFP(this.log_name, 'noteSave', dialog.user.profile, `Error saving note ${task ? task.id : 'no id'} from ${JSON.stringify(in_task)}`, e); 
          await dialog.saveSession();
          res.sendStatus(500).end();
          return;
        }
        await dialog.saveSession();
        const task_info = peopleUtils.personInfoTasks([task], dialog.me);
        res.json(task_info[0]).end();
        return;
      }

      await dialog.saveSession();
      res.sendStatus(404).end();
    }
  }

  async noteDelete(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog) {
      if (req.params.id) await dialog.tasks.delete({id: req.params.id});
      await dialog.saveSession();
    }

    res.sendStatus(204).end();
  }
}

// Build our router/controller and map them
const router = express.Router();
const controller = new NotesController();

router.route('/:id').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).route, controller.noteGet);

router.route('/:id/complete').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.noteComplete);
router.route('/:id/uncomplete').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.noteUncomplete);
router.route('/:id/clear').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.noteClear);
router.route('/:id/later').post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.noteLater);

router.route('/').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true }).route, controller.notesGet);
router.route('/').post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.noteSave);
router.route('/:id').delete(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.noteDelete);

export default router;