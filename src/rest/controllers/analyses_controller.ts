/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { JsonConvert } from 'json2typescript';
import _ from 'lodash';

import Dialog from '../../session/dialog';
import { Analysis } from '../../types/items';
import { AnalysisInfo } from '../../types/shared';

import { filterFilter, mapCategory, mapFilter } from '../../utils/filter';
import { analysisInfo } from '../../utils/info';
import logging from '../../utils/logging';

import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';
import { AnalysisDto } from '../models/analysis';
import { AbstractController } from './a_controller';

class AnalysisController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:analysis');
  log_name = 'rest.controllers.AnalysisController';

  constructor() {
    super();

    this.analysisGet = this.analysisGet.bind(this);
    this.analysesGet = this.analysesGet.bind(this);
    this.analysisSave = this.analysisSave.bind(this);
    this.analysisDelete = this.analysisDelete.bind(this);
  }

  async analysisGet(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    const id = req.params.id;
    try {
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        let analysis: Analysis;
        if (id) analysis = await dialog.analyses.get(id);
        await dialog.saveSession();
        if (analysis) {
          const analysis_info = analysisInfo(analysis);
          res.json(analysis_info).end();
        } else res.sendStatus(404).end();
        return;
      }
    } catch(e) {
      logging.errorFP(this.log_name, 'analysisGet', dialog ? dialog.user.profile : null, `Error getting analysis ${id}`, e);
    }
    if(dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }

  async analysesGet(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    try { 
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        const analyses = await dialog.analyses.load();
        await dialog.saveSession();
        if (analyses) {
          const analysis_info: AnalysisInfo[] = analyses.map(analysisInfo).sort((a,b) => a.update_date && b.update_date ? b.update_date.getTime() - a.update_date.getTime() : -1);
          res.json(analysis_info).end();
        } else res.sendStatus(404).end();
        return;
      }
    } catch(e) {
      logging.errorFP(this.log_name, 'analysesGet', dialog ? dialog.user.profile : null, `Error getting analyses`, e);
    }
    if(dialog) await dialog.saveSession();
    res.sendStatus(404).end();
  }

  async analysisSave(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      const json_convert = new JsonConvert();
      let in_analysis: AnalysisDto = null;
      try { 
        in_analysis = json_convert.deserializeObject(req.body, AnalysisDto); 
      } catch (e) {
        logging.warnFP(this.log_name, 'analysisSave', dialog.user.profile, 'parse error', e);
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.status(422).json({code: 422, error: 'Unable to parse incoming analysis'}).end();
        return;
      }

      if (in_analysis) {
        try {
          let analysis: Analysis;
          if (in_analysis.id) {
            analysis = await dialog.analyses.get(in_analysis.id);
            if (analysis) {
              if (in_analysis.title) analysis.title = in_analysis.title;
              if (in_analysis.x) analysis.x = in_analysis.x;
              if (in_analysis.y) analysis.y = in_analysis.y;
              if (in_analysis.filters) analysis.filters = in_analysis.filters.map(mapFilter).filter(filterFilter);
              if (in_analysis.categories) analysis.categories = in_analysis.categories.map(mapCategory);
              if (in_analysis.target_skills) analysis.target_skills = in_analysis.target_skills.map(mapCategory);
              if (in_analysis.focus_skill) analysis.focus_skill = mapCategory(in_analysis.focus_skill);
              if (in_analysis.candidates) {
                const candidate_vanities = analysis.candidates.map(c => c.vanity).filter(v => v);
                const candidate_ids = analysis.candidates.map(c => c.id).filter(id => id).concat(candidate_vanities.map(v => `profile/${v}`));
                const in_vanities = in_analysis.candidates.map(c => c.vanity).filter(v => v);
                const in_ids = in_analysis.candidates.map(c => c.id).filter(id => id).concat(in_vanities.map(v => `profile/${v}`));
                  
                const new_candidates = in_analysis.candidates.filter(c => !candidate_ids.includes(c.id) && (!c.vanity || !candidate_vanities.includes(c.vanity)));
                const lookup_candidates = new_candidates && new_candidates.length ? await dialog.people.loadEntityPeople(new_candidates) : [];

                const keep_candidates = analysis.candidates.filter(c => in_ids.includes(c.id) || (c.vanity && (in_vanities.includes(c.vanity) || in_ids.includes(`profile/${c.vanity}`))));

                analysis.candidates = _.uniqWith([...keep_candidates, ...lookup_candidates], (a,b) => a.id === b.id || (a.vanity && b.vanity && a.vanity === b.vanity));
              }

              analysis = await dialog.analyses.update(analysis);
            } else {
              await dialog.saveSession();
              res.sendStatus(404).end();
              return;
            }
          } else {
            analysis = new Analysis({
              title: in_analysis.title,
              filters: in_analysis.filters.map(mapFilter).filter(filterFilter),
              categories: in_analysis.categories.map(mapCategory),
              target_skills: in_analysis.target_skills.map(mapCategory),
              focus_skill: mapCategory(in_analysis.focus_skill),
              candidates: in_analysis.candidates ? await dialog.people.loadEntityPeople(in_analysis.candidates) : [],
            });
            analysis = await dialog.analyses.create(analysis);
          }

          await dialog.saveSession();
          const analysis_info = analysisInfo(analysis);

          res.json(analysis_info).end();
          return;
        } catch(e) {
          logging.errorFP(this.log_name, dialog ? dialog.user.profile : undefined, 'analysisSave', `Error saving analysis`, e);
        }
      }
    }
    
    if(dialog) await dialog.saveSession();
    res.sendStatus(404).end(); 
  }

  async analysisDelete(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = req['dialog'];
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      const id = req.params.id;
      try {
        if (id) await dialog.analyses.delete({id});
      } catch(e) {
        logging.warnFP(this.log_name, 'AnalysisDelete', dialog.user.profile, 'parse error', e);
        await dialog.saveSession().catch(e => dialog.asyncError(e));
        res.status(422).json({code: 422, error: 'Unable to parse incoming analysis'}).end();
        return;
      }

      await dialog.saveSession();
      res.sendStatus(204).end();
      return;
    }
    res.sendStatus(404).end();
  }
}

// Build our router/controller and map them
const router = express.Router();
const controller = new AnalysisController();

router.route('/:id').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true, no_cache: true }).route, controller.analysisGet);
router.route('/').get(new GroupAuthValidation().route, new UserAuthValidation({ read_only: true, no_cache: true }).route, controller.analysesGet);
router.route('/').post(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.analysisSave);
router.route('/:id').delete(new GroupAuthValidation().route, new UserAuthValidation({ read_only: false }).route, controller.analysisDelete);

export default router;