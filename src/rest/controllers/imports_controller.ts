/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import axios from 'axios';
import express from 'express';
import multer from 'multer';

import Dialog from '../../session/dialog';
import ForaUser from '../../session/user';

import { FileType } from '../../types/globals';
import { Document, ImportType } from '../../types/items';
import { Imports } from '../../types/shared';

import { fileType } from '../../utils/imports';
import logging from '../../utils/logging';

import GroupAuthValidation from '../auth/group_auth_validation';
import UserAuthValidation from '../auth/user_auth_validation';

import { internalImport } from '../../routes/imports';

import { LinkedInPeopleImportPlugin } from '../../sources/people/linkedin/linkedin_people_import_plugin';
import { SourceController } from '../../sources/source_controller';
import { Abstract<PERSON>ontroller } from './a_controller';

import data from '../../data';

async function tempUrl(user: ForaUser, file): Promise<Document> {
  return data.imports.upload(user, new Document({
    mime: file.mimetype,
    created: new Date(),
    props: {
      originalname: file.originalname,
      encoding: file.encoding,
      size: file.size ? file.size.toString() : 0,
    }
  }));
}

class ImportsController extends AbstractController {
  debug = (require('debug') as any)('fora:rest:imports');
  log_name = 'rest.controllers.ImportsController';

  constructor() {
    super();

    this.importStart = this.importStart.bind(this);
    this.importUpload = this.importUpload.bind(this);
    this.importFinish = this.importFinish.bind(this);
  }

  async importStart(req: express.Request, res: express.Response): Promise<void> {
    if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

    const dialog: Dialog = req['dialog'];
    try {
      if (dialog) {
        if (dialog.isAuthenticatedNonGuest()) {
          const doc = await tempUrl(dialog.user, {
                mimetype: req.query.mimetype,
                originalname: req.query.originalname,
                encoding: req.query.encoding,
                size: req.query.size,
              });
          await dialog.saveSession();
          res.json({
            id: doc.id,
            url: doc.url,
            mimetype: doc.mime,
            size: req.query.size,
            created: doc.created,
          }).end();
          return;
        } else {
          await dialog.saveSession();
        }
      }
      res.status(401).end();
    } catch(e) {
      logging.errorFP(this.log_name, 'start', dialog && dialog.user ? dialog.user.profile : null, `Error starting import`, e);
      if (dialog) await dialog.saveSession();
      res.status(500).end();
    }
  }

  async importUpload(req: express.Request, res: express.Response): Promise<void> {
    if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
    const dialog: Dialog = req['dialog'];

    try {
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        const files = (req as any).files;
        if (files && files['import'] && files['import'].length && 
          req.body && req.body.url && req.body.url.length) {
          const file = files['import'][0];
          const url = req.body.url;
          const start = req.body.start;
          const end = req.body.end;
          const total = req.body.total;
          logging.infoFP(this.log_name, 'upload', dialog.user.profile, `Appending to file at ${url}'}`);
          const data = file.buffer;
          const r = await axios.put(url, data, {
            headers: {
              "content-length": data.length,
              "content-range": `bytes ${start}-${end}/${total}`
            },
            validateStatus: s => { return s < 400 },
          });
          logging.infoFP(this.log_name, 'upload', dialog.user.profile, `Imported ${data.length} bytes to ${url}`);
        }
      }

      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.sendStatus(204).end();
    } catch(e) {
      logging.errorFP(this.log_name, 'upload', dialog && dialog.user ? dialog.user.profile : null, `Error uploading import`, e);
      if (dialog) await dialog.saveSession();
      res.status(500).end();
    } 
  }

  async importFinish(req: express.Request, res: express.Response): Promise<void> {
    if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

    if (req.body && req.body._method && req.body._method.toLowerCase() === 'put') return this.importUpload(req, res);

    const dialog: Dialog = req['dialog'];

    try {
      if (dialog && dialog.isAuthenticatedNonGuest()) {
        let doc;
        let import_type: ImportType = null;
        if (req.body) {
          if(req.body.id) {
            doc = req.body as Document;
            doc = await data.imports.byFileId(dialog.user, req.body.id);
          }

          import_type = req.body.type;
        }

        if (doc) {
          // try to figure out the file type
          // tslint:disable-next-line:prefer-const
          let [file_type, file_data] = await fileType(doc, null, false);

          logging.infoFP(this.log_name, 'finish', dialog.user.profile, `Processing file ${file_type}`);

          doc.props['file_type'] = file_type;

          switch(file_type) {
            case FileType.HTML:
              break;
            case FileType.VCF:
              import_type = Imports.Facebook;
              break;
            case FileType.CSV:
              if ((new LinkedInPeopleImportPlugin(dialog.user, doc)).checkImport()) import_type = Imports.LinkedIn;
            case FileType.ZIP:
              if (file_data && Array.isArray(file_data) && file_data.length) {
                const check_import_type = SourceController.importTypeFromZipData(file_data);
                if (check_import_type) import_type = check_import_type;
              }
            }

          if (import_type) internalImport(dialog.user, dialog.me, doc.file_id, import_type);
          else logging.warnFP(this.log_name, 'finish', dialog && dialog.user ? dialog.user.profile : null, `Missing import type for ${doc.file_id} ${file_type}`);
        }

      }
      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
      res.sendStatus(204).end();
    } catch(e) {
      logging.errorFP(this.log_name, 'finish', dialog && dialog.user ? dialog.user.profile : null, `Error finishing import`, e);
      if (dialog) await dialog.saveSession();
      res.status(500).end();
    }
  }
}

const router = express.Router();
const controller = new ImportsController();

const storage = multer.memoryStorage();
const upload = multer({storage});
const import_upload = upload.fields([ { name: 'import' } ]);

router.route('/').get(
  new GroupAuthValidation().handle, 
  new UserAuthValidation({ read_only: true, no_cache: true }).handle, 
  controller.importStart
);

router.route('/').put(
  import_upload,
  new GroupAuthValidation().handle, 
  new UserAuthValidation({ read_only: true, no_cache: true }).handle, 
  controller.importUpload
);

router.route('/').post(
  import_upload,
  new GroupAuthValidation().handle, 
  new UserAuthValidation({ read_only: true, no_cache: true }).handle, 
  controller.importFinish
);

export default router;