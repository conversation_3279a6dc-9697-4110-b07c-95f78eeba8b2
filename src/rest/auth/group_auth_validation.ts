/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import 'express-session';
// import { AuthProvider } from '../../auth/auth_provider';
import config from '../../config';
import data from '../../data';
import { NormalizedProviderToken, SAML_AUTHENTICATED } from '../../types/auth';
import { Group } from '../../types/group';
import { AuthLevel, AuthProviders, Uid } from '../../types/shared';
import logging from '../../utils/logging';
import AuthValidation, { AuthValidationError } from './auth_validation';

import { StatusCodes } from 'http-status-codes';

const BEARER_PREFIX = 'Bearer ';
const DEBUG = (require('debug') as any)('fora:rest:auth:group');
const LOG_NAME = 'rest.auth.GroupAuthValidation';

export default class GroupAuthValidation extends AuthValidation {
  constructor() {
    super({});
    this.route = this.route.bind(this);
    this.resolve = this.resolve.bind(this);
    this.handle = this.handle.bind(this);
  }

  public async route(req: express.Request, res: express.Response, next: express.NextFunction = null) {
    await this.resolve(req, res, next);
  }

  // resolves groups and returns errors
  public async resolve(req: express.Request, res: express.Response, next: express.NextFunction = null): Promise<AuthValidationError> {
    if (!config.isLoaded()) await config.loadConfig();

    let token: NormalizedProviderToken;
    let is_saml = false;

    try {
      const header_group = req.header('x-group-for');
      const header_host = req.session['hostname'];
      let header_auth = req.header('authorization');
      if (!header_auth) header_auth = req.header('x-forwarded-authorization');

      if (logging.isDebug()) DEBUG('handle: Host = %s', header_host);

      //
      // Get the Group, 401 for not found or no matches
      //
      if (!header_host || ['askfora.com', 'www.askfora.com', 'askfora.ngrok.io', 'test.askfora.com'].includes(header_host)) {
        logging.warnF(LOG_NAME, 'resolve', `Skipping group ${header_group} ${header_host}`);
        if (next) next();
        return AuthValidationError.NoEntity;
      }

      const group =  await this.getGroup(header_group, header_host);
      if (group) req['group'] = group;
      else {
        logging.warnF(LOG_NAME, 'resolve', `Unknown group ${header_group} ${header_host}`);
        if (next) next();
        return AuthValidationError.NoEntity;
      }

      // Fail fast if we don't have our headers
      if (!group && (!header_auth || !header_group)) {
        if (next) next();
        else logging.warnF(LOG_NAME, 'resolve', 'missing group and/or auth');
        return AuthValidationError.MissingAuth;
      }

      if (logging.isDebug()) DEBUG('handle: group = %o', group);
      is_saml = group.provider === AuthProviders.Saml;
      if (logging.isDebug()) DEBUG('handle: is_saml = %s, %s', is_saml, group.provider);

      //
      // Validate the SAML shared key
      //
      if (is_saml && header_auth !== `Bearer ${group.shared_key}`) {
        logging.warnF(LOG_NAME, 'resolve', `group shared key mismatch [IN=${header_auth}][G=${group.shared_key}]`);
        if (next) next();
        return AuthValidationError.InvalidSharedKey;
      }

      //
      // Get the token, 401 for missing or no match on email
      //
      if (is_saml) token = new NormalizedProviderToken(AuthProviders.Saml, SAML_AUTHENTICATED, AuthLevel.Basic);
      else if (header_auth) token = await this.getOAuthToken(group, header_auth);

      req['token'] = token;
      if (logging.isDebug()) DEBUG('handle: access token = %o', token);

      // For completeness for the future
      /*if (!token || !AuthProvider.isValid(token, false)) {
        logging.warnF(LOG_NAME, 'resolve', `Token restrictions. Token: ${JSON.stringify(token)} Headers: ${JSON.stringify(req.headers)}`);
        if (next) next();
        return AuthValidationError.InvalidToken;
      }*/
    } catch(err) {
      logging.errorF(LOG_NAME, 'resolve', 'Unexpected exception', err);
      if (next) next();
      return AuthValidationError.UnexpectedException;
    } 

    if (next) next();
  }

  // resolves groups and sends errors back to the client
  public async handle(req: express.Request, res: express.Response, next: express.NextFunction) {
    const error = (await this.resolve(req, res));
    let error_status = null;
    let error_msg = null;
    switch(error) {
      case AuthValidationError.MissingAuth:
        error_status = StatusCodes.UNAUTHORIZED;
        error_msg = { code: 401, message: 'Unauthorized', internal: config.isEnvOffline() ? 'headers' : undefined };
        break;
      case AuthValidationError.NoEntity:
        error_status = StatusCodes.UNAUTHORIZED;
        error_msg = { code: 401, message: 'Unauthorized', internal: config.isEnvOffline() ? 'group' : undefined };
        break;
      case AuthValidationError.InvalidSharedKey:
        error_status = StatusCodes.UNAUTHORIZED;
        error_msg ={ code: 401, message: 'Unauthorized', internal: config.isEnvOffline() ? 'group_shared_key' : undefined };
        break;
      case AuthValidationError.InvalidToken:
        error_status = StatusCodes.UNAUTHORIZED;
        error_msg = { code: 401, message: 'Unauthorized', internal: config.isEnvOffline() ? 'token' : undefined };
        break;
      case AuthValidationError.UnexpectedException:
        error_status = StatusCodes.INTERNAL_SERVER_ERROR;
        error_msg = { code: 500, message: 'Internal server error', internal: config.isEnvOffline() ? 'exception' : undefined };
        break;
    }

    if (error_status) {
      logging.warnF(LOG_NAME, 'handle', `${error_status} ${error}`);
      res.status(error_status);
      if (error_msg) res.json(error_msg);
      res.end();
    } else next();
  }

  private async getGroup(group_id: Uid, host: string): Promise<Group> {
    const group: Group = group_id ? await data.groups.byId(group_id) : await data.groups.byHost(host);

    if (group && host === group.host) return group;
    else if(group) {
      logging.warnF(LOG_NAME, 'getGroup', `Incoming host does not match group host [IN=${host}][G=${group.host}]`);
      return undefined;
    } else {
      logging.warnF(LOG_NAME, 'getGroup', `Incoming group not found [IN=${host}][G=${group_id}]`);
      return undefined;
    }
  }
}
