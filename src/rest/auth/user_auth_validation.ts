/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import 'express-session';
import { StatusCodes } from 'http-status-codes';
import { AuthProvider } from '../../auth/auth_provider';
import { SamlAuthenticationProvider } from '../../auth/saml_authentication_provider';
import config from '../../config';
import data from '../../data';
import Dialog from '../../session/dialog';
import ForaUser from '../../session/user';
import { NormalizedProviderToken, NormalizedProviderUser } from '../../types/auth';
import { Group } from '../../types/group';
import { AuthProviders } from '../../types/shared';
import * as funcs from '../../utils/funcs';
import logging from '../../utils/logging';
import AuthValidation, { AuthValidationError } from './auth_validation';

const BEARER_PREFIX = 'Bearer ';
const DEBUG = (require('debug') as any)('fora:rest:auth:user');
const LOG_NAME = 'rest.auth.UserAuthValidation';

export default class UserAuthValidation extends AuthValidation {

  constructor(options: { read_only?: boolean; no_cache? : boolean, require_admin?: boolean } = {
      read_only: true, no_cache: false, require_admin: false}) {
    super(options);
    this.route = this.route.bind(this);
    this.resolve = this.resolve.bind(this);
    this.handle = this.handle.bind(this);
  }

  public async route(req: express.Request, res: express.Response, next: express.NextFunction = null) {
    await this.resolve(req, res, next);
  }

  /**
   * Handle the incoming request to authenticate and authorize for any handlers in the chain.
   *
   * @param req - Incoming request
   * @param res - Outgoing response
   * @param next - Next function
   */
  public async resolve(req: express.Request, res: express.Response, next: express.NextFunction = null): Promise<AuthValidationError> {
    let user: NormalizedProviderUser;
    let token = req['token'] as NormalizedProviderToken;
    let group = req['group'] as Group;
    let is_saml = group ? group.provider === AuthProviders.Saml : false;

    let dialog: Dialog = null;

    try {
      const cookie_name = config.get('COOKIE');
      const header_cookie = req.cookies ? req.cookies[cookie_name ? cookie_name : 'Fora'] : null;

      const header_uid = req.header('x-userid-for');
      const header_email = req.header('x-email-for');
      const header_name = req.header('x-name-for');

      DEBUG('handle: Headers = %o', req.headers);

      const saml_authentication_provider = is_saml ? new SamlAuthenticationProvider() : null;
      const saml_request: express.Request = {user:null} as express.Request;
      if(is_saml) {
        Object.assign(saml_request, req);
        saml_request.user = {
            identifier: header_uid,
            email: header_email,
            displayname: header_name,
        };
      }

      //
      // Get the user
      //
      if (is_saml && header_uid && header_email) {
        user = await saml_authentication_provider.getProviderUserNormalized(saml_request, group);
      } else if(group && token) {
        if(req.query.hash && req.query.profile && funcs.verifyURL(req.protocol, req.session['hostname'], `${req.baseUrl}${req.path}`, req.query, group.signing_secret)) {
          req.session['profile'] = req.query.profile;
          req['session_id'] = req.session.id;
          const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
          const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
          dialog = await Dialog.loadSession(req.originalUrl, req.session, req.cookies, {offset, read_only: this.options.read_only, no_cache: this.options.no_cache, create_new:true, check_tokens:false, ephemeral: true, stack:Error().stack});
          req['dialog'] = dialog;
        } else {
          logging.infoF(LOG_NAME, 'resolve', `No hash or signature doesn't match, checking user token`);
          user = await this.getOAuthUser(req, group, token);
        }
      } else if(header_cookie) {
        req['session_id'] = req.session.id;
        const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
        const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
        dialog = await Dialog.loadSession(req.originalUrl, req.session, req.cookies, {offset, read_only: this.options.read_only, no_cache: this.options.no_cache, create_new:true, check_tokens:true, ephemeral: true, stack:Error().stack});
        if (!req.query.profile && !req.query.hash || req.query.profile === dialog.user.profile) req['dialog'] = dialog;
      } else {
        if (next) {
          req['session_id'] = req.session.id;
          const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
          const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
          dialog = await Dialog.loadSession(req.originalUrl, req.session, req.cookies, {offset, read_only: this.options.read_only, no_cache: this.options.no_cache, create_new:true, check_tokens:false, ephemeral: true, stack:Error().stack});
          req['dialog'] = dialog;
          next();
        }
        return AuthValidationError.MissingAuth;
      }

      if (logging.isDebug()) DEBUG('handle: provider user = %o', user);

      if (!dialog) {
        if (!user) {
          logging.warnF(LOG_NAME, 'resolve', 'returning 401 due to user restrictions');
          if (next) next();
          return AuthValidationError.NoEntity;
        }

        const guser = await data.users.globalById(user.id);

        if (group && guser && (!guser.groups || !guser.groups.includes(group.id))) {
          if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
          return AuthValidationError.Unauthorized;
        }

        // Create and initialize our Dialog
        const session_id = `api:${user.id}`;
        dialog = await Dialog.loadSession(req.originalUrl, { id: session_id, profile: user.id, hostname: req.session['hostname'], save:(c) => { if (c) c(); } }, {}, {create_new:true, stack:Error().stack, group, ephemeral: true });
        req['session_id'] = session_id;
        req['dialog'] = dialog;

        let self = dialog ? dialog.me : null;
        if (!self) {
          if (is_saml) self = saml_authentication_provider.loadSelfPerson(saml_request, user);
          else if (config.isEnvOffline()) self = await data.people.byId(new ForaUser(user.id, is_saml ? AuthProviders.Saml : group.provider), `people/t${user.id}`);
          else {
            let provider_settings = group.defaultProviderSettings;
            if (!provider_settings) provider_settings = AuthProvider.getDefaultProviderSettings(group.provider);
            self = await AuthProvider.getProviderUserSelf(token, user, provider_settings, req);
          }
        }

        dialog.user = new ForaUser(user.id, group.provider);
        dialog.user.name = user.given_name;
        dialog.user.email = user.email;
        if (!dialog.user.tokens) dialog.user.setTokens(token, group.provider, user.id);
        if (!(await data.users.init(dialog.user, false))) throw new Error('Unable to initialize user');

        // Initialize all the user related items we need
        await dialog.initUser(self, is_saml ? AuthProviders.Saml : group.provider, 'api');
      }

      if (dialog) {
        if (this.options.require_admin && ((group && !dialog.user.isAdmin(group.id)) || (dialog.group_host && !dialog.user.isAdmin(dialog.group_host.id)))) {
          await dialog.saveSession().catch(e => dialog.asyncError(e));
          return AuthValidationError.Unauthorized;
        }
      }
    } catch (err) {
      logging.errorFP(LOG_NAME, 'resolve', user ? user.id : 'UNKNOWN_PROFILE', 'Unexpected exception', err);
      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
      if (next) next();
      return AuthValidationError.UnexpectedException;
    }

    if (next) next();
  }

  // resolves groups and sends errors back to the client
  public async handle(req: express.Request, res: express.Response, next: express.NextFunction) {
    const error = (await this.resolve(req, res));
    let error_status = null;
    let error_msg = null;
    switch(error) {
      case AuthValidationError.MissingAuth:
        error_status = StatusCodes.UNAUTHORIZED;
        error_msg = { code: 401, message: 'Unauthorized', internal: config.isEnvOffline() ? 'headers' : undefined };
        break;
      case AuthValidationError.NoEntity:
        error_status = StatusCodes.UNAUTHORIZED;
        error_msg = { code: 401, message: 'Unauthorized', internal: config.isEnvOffline() ? 'user' : undefined };
        break;
      case AuthValidationError.UnexpectedException:
        error_status = StatusCodes.INTERNAL_SERVER_ERROR;
        error_msg = { code: 500, message: 'Internal server error', internal: config.isEnvOffline() ? 'exception' : undefined };
        break;
      case AuthValidationError.Unauthorized:
        error_status = StatusCodes.UNAUTHORIZED;
        error_msg = { code: 401, message: 'Unauthorized', internal: config.isEnvOffline() ? 'user' : undefined };
    }

    if (error_status) {
      res.status(error_status);
      if (error_msg) res.json(error_msg);
      res.end();
    } else next();
  }


  private async getOAuthUser(req: express.Request, group: Group, token: NormalizedProviderToken): Promise<NormalizedProviderUser> {
    try {
      const defaults = AuthProvider.getDefaultProviderSettings(group.provider);
      const provider_settings = group.defaultProviderSettings;
      const user = await AuthProvider.getProviderUser(token, provider_settings ? provider_settings : defaults, group, req);

      if (!group.email_domain || (user && user.email && group.email_domain.find(ed => user.email.endsWith(ed)))) return user;
      else {
        logging.warnF(LOG_NAME, 'handle', `Incoming user email not in domain [IN=${user.email}][G=${group.email_domain}]`);
        return undefined;
      }
    } catch (err) {
      logging.errorF(LOG_NAME, 'getOAuthUser', `Unable to retrieve user for token [${err.code}]`, err);
    }
  }

}
