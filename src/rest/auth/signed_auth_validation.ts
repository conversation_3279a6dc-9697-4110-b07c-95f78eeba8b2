/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import 'express-session';
import config from '../../config';
import data from '../../data';
import { Group } from '../../types/group';
import { Uid } from '../../types/shared';
import * as funcs from '../../utils/funcs';
import logging from '../../utils/logging';
import AuthValidation, { AuthValidationError } from './auth_validation';

import { StatusCodes } from 'http-status-codes';

const LOG_NAME = 'rest.auth.SignedAuthValidation';

export default class SignedAuthValidation extends AuthValidation {
  constructor() {
    super({});
    this.route = this.route.bind(this);
    this.resolve = this.resolve.bind(this);
    this.handle = this.handle.bind(this);
  }

  public async route(req: express.Request, res: express.Response, next: express.NextFunction = null) {
    await this.resolve(req, res, next);
  }

  // resolves groups in signed urls 
  public async resolve(req: express.Request, res: express.Response, next: express.NextFunction = null): Promise<AuthValidationError> {
    try {
      if(req.query.hash && req.query.profile && req.query.group) {
        const header_host = req.session['hostname'];
        const group = await this.getGroup(req.query.group as string, header_host);
        if (group && funcs.verifyURL(req.protocol, header_host, `${req.baseUrl}${req.path}`, req.query, group.signing_secret)) {
          req['group'] = group;
          req['profile'] = req.query.profile;
          req['hash'] = req.query.hash;

          let header_auth = req.header('authorization');
          if (!header_auth) header_auth = req.header('x-forwarded-authorization');

          if (header_auth) {
            req['token'] = await this.getOAuthToken(group, header_auth);
          }
        }
      }

      if (!req['group']) {
        if (next) next();
        return AuthValidationError.NoEntity;
      }

    } catch(err) {
      logging.errorF(LOG_NAME, 'resolve', 'Unexpected exception', err);
      if (next) next();
      return AuthValidationError.UnexpectedException;
    }

    if (next) next();
  }

  // resolves groups and sends errors
  public async handle(req: express.Request, res: express.Response, next: express.NextFunction) {
    const error = (await this.resolve(req, res));
    let error_status = null;
    let error_msg = null;
    switch(error) {
      case AuthValidationError.NoEntity:
        error_status = StatusCodes.UNAUTHORIZED;
        error_msg = { code: 401, message: 'Unauthorized', internal: config.isEnvOffline() ? 'group' : undefined };
        break;
      case AuthValidationError.UnexpectedException:
        error_status = StatusCodes.INTERNAL_SERVER_ERROR;
        error_msg = { code: 500, message: 'Internal server error', internal: config.isEnvOffline() ? 'exception' : undefined };
        break;
    }

    if (error_status) {
      logging.warnF(LOG_NAME, 'handle', `${error_status} ${error}`);
      res.status(error_status);
      if (error_msg) res.json(error_msg);
      res.end();
    } else next();

  }

  private async getGroup(group_id: Uid, host: string): Promise<Group> {
    const group: Group = await data.groups.byId(group_id);

    if (group && host === group.host) return group;
    else if(group) {
      logging.warnF(LOG_NAME, 'getGroup', `Incoming host does not match group host [IN=${host}][G=${group.host}]`);
      return undefined;
    } else {
      logging.warnF(LOG_NAME, 'getGroup', `Incoming group not found [IN=${host}][G=${group_id}]`);
      return undefined;
    }
  }
}