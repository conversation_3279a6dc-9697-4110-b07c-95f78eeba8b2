import express from 'express';

import { NormalizedProviderToken } from '../../types/auth';
import { Group } from '../../types/group';
import { AuthLevel } from '../../types/shared';

const BEARER_PREFIX = 'Bearer ';

export enum AuthValidationError {
  MissingAuth = "MissingAuth",
  NoEntity = "NoEntity",
  InvalidSharedKey = "InvalidSharedKey",
  InvalidToken = "InvalidToken",
  UnexpectedException = "UnexpectedException",
  Unauthorized = 'Unauthorized',
}

export default abstract class AuthValidation {
  options: any = null;

  constructor(options: any = {}) {
    this.options = options;
  }

  // resovles and returns error
  public async route(req: express.Request, res: express.Response, next: express.NextFunction = null) {
    throw new Error('Not implemeted');
  }

  // resovles and returns error
  public async resolve(req: express.Request, res: express.Response, next: express.NextFunction = null): Promise<AuthValidationError> {
    throw new Error('Not implemeted');
  }

  // resolves and returns errors to client
  public async handle(req: express.Request, res: express.Response, next: express.NextFunction) {
    throw new Error('Not implemeted');
  }

  protected async getOAuthToken(group: Group, access_token: string): Promise<NormalizedProviderToken> {
    const tokens = new NormalizedProviderToken(group.provider, access_token, AuthLevel.Basic);

    if (access_token && access_token.startsWith(BEARER_PREFIX)) tokens.access_token = tokens.access_token.slice(BEARER_PREFIX.length);
    return tokens;
  }
}
