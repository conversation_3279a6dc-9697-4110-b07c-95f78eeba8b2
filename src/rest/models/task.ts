/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsArray, IsOptional, IsString, ValidateNested } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';
import { DateConverter } from '../converters/date_converter';
import { PersonDto } from './person';

@JsonObject('Task')
export class TaskDto {
  @IsOptional() @IsString() @JsonProperty('id', String, true) id: string = undefined;
  @IsOptional() @IsString() @JsonProperty('title', String, true) title: string = undefined;
  @IsOptional() @IsString() @JsonProperty('notes', String, true) notes: string = undefined;

  @IsOptional() @JsonProperty('due', DateConverter, true) due: Date = new Date();
  @IsOptional() @JsonProperty('created', DateConverter, true) created: Date = new Date();

  @IsOptional() @JsonProperty('completed', Boolean, true) completed = false;

  @IsOptional() @JsonProperty('project', String, true) project: string = undefined;

  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('people', [PersonDto],  true) people?: PersonDto[] = undefined;
}