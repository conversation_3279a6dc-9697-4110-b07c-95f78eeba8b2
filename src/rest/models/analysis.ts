/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsArray, IsDefined, IsOptional, IsString, Length, ValidateNested } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';
import { TagType } from '../../types/shared';
import { CategoryDto } from './category';
import { FilterDto } from './filter';
import { PersonDto } from './person';

@JsonObject('Analysis')
export class AnalysisDto {
  @IsOptional() @JsonProperty('id', String, true) id: string = undefined;
  @IsDefined() @IsString() @Length(2, 1500) @JsonProperty('title', String, true) title: string = undefined;
  @IsOptional() @IsString() @Length(2, 1500) @JsonProperty('x', String, true) x?: TagType = undefined;
  @IsOptional() @IsString() @Length(2, 1500) @JsonProperty('y', String, true) y?: TagType = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('categories', [CategoryDto],  true) categories?: CategoryDto[] = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('target_skills', [CategoryDto],  true) target_skills?: CategoryDto[] = undefined;
  @IsOptional() @ValidateNested({each:true}) @JsonProperty('focus_skill', CategoryDto,  true) focus_skill?: CategoryDto = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('filters', [FilterDto],  true) filters?: FilterDto[] = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('candidates', [PersonDto],  true) candidates?: PersonDto[] = undefined;
}
 