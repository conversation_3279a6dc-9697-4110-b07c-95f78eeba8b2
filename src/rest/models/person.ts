/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsArray, IsBoolean, IsDefined, IsOptional, IsString, ValidateNested } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';
import { TagType } from '../../types/shared';
import { DateConverter } from '../converters/date_converter';

@JsonObject('NestedTag')
export class NestedTagDto {
  @IsDefined() @IsString() @JsonProperty('type', String, false) type: TagType = undefined;
  @IsOptional() @IsString() @JsonProperty('value', String, true) value: string = undefined;
  @IsOptional() @JsonProperty('index', Number, true) index: number = undefined;
  @IsOptional() @JsonProperty('start', DateConverter, true) start: Date = undefined;
}

@JsonObject('Tag')
export class TagDto extends NestedTagDto {
  @IsOptional() @IsString() @JsonProperty('link', String, true) link?: string = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('tags', [NestedTagDto],  true) tags?: NestedTagDto[] = undefined;
}

@JsonObject('Person')
export class PersonDto {
  @IsDefined() @IsString() @JsonProperty('id', String, false) id: string = undefined;
  @IsOptional() @IsString() @JsonProperty('name', String, true) name: string = undefined;
  @IsOptional() @IsString() @JsonProperty('meta', String, true) meta: string = undefined;
  @IsOptional() @JsonProperty('image', String, true) image: string = undefined;
  @IsOptional() @IsArray() @JsonProperty('comms', [String], true) comms: string[] = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('tags', [TagDto],  true) tags: TagDto[] = undefined;
  @IsOptional() @IsArray() @JsonProperty('links', [String], true) links: string[] = undefined;
  @IsOptional() @IsArray() @JsonProperty('skills', [String], true) skills: string[] = undefined;
  @IsOptional() @IsString() @JsonProperty('recommendation', String, true) recommendation: string = undefined;
  @IsOptional() @IsString() @JsonProperty('bio', String, true) bio: string = undefined;
  @IsOptional() @IsString() @JsonProperty('vanity', String, true) vanity: string = undefined;
  @IsOptional() @IsBoolean() @JsonProperty('self', Boolean, true) self: boolean = undefined;
}
