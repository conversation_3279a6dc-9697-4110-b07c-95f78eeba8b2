/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsArray, IsDefined, IsIn, IsNumber, IsOptional, IsPositive, IsString, Length, Min, ValidateNested } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';
import { ProjectCandidateState, ProjectRate } from '../../types/shared';
import { DateConverter } from '../converters/date_converter';
import { PersonDto } from './person';

@JsonObject('Candidate')
export class CandidateDto extends PersonDto {
  @IsOptional() @JsonProperty('askfora_id', String, true) askfora_id: string = undefined;
  @IsOptional() @JsonProperty('answer', String, true) answer: string = undefined;
  @IsOptional() @JsonProperty('public', Boolean, true) public = false;
  @IsOptional() @JsonProperty('ready', Boolean, true) ready = false;
  @IsOptional() @IsArray() @JsonProperty('groups', [String], true) groups: string[] = undefined;
  @IsDefined() @IsIn(Object.values(ProjectCandidateState)) @JsonProperty('state', String, true) state: ProjectCandidateState = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('refer_by', [PersonDto],  true) refer_by: PersonDto[] = undefined;
}

@JsonObject('Project')
export class ProjectDto {
  @IsOptional() @JsonProperty('confidential', Boolean, true) confidential = false;
  @IsDefined() @IsNumber() @IsPositive() @JsonProperty('duration', Number, true) duration: number = undefined;
  @IsOptional() @IsNumber() @IsPositive() @JsonProperty('progress', Number, true) progress: number = undefined;
  @IsDefined() @IsNumber() @Min(0) @JsonProperty('fee', Number, true) fee: number = undefined;
  @IsOptional() @JsonProperty('id', String, true) id: string = undefined;
  @IsOptional() @JsonProperty('network', Boolean, true) network = false;
  @IsOptional() @JsonProperty('public', Boolean, true) public = undefined;
  @IsOptional() @IsArray() @JsonProperty('groups', [String], true) groups: string[] = undefined;
  @IsOptional() @JsonProperty('flex_dates', Boolean, true) flex_dates = false;
  @IsOptional() @IsString() @Length(0, 1500) @JsonProperty('notes', String, true) notes: string = undefined;
  @IsOptional() @Length(2, 1500) @JsonProperty('requirements', String, true) requirements: string = undefined;
  @IsOptional() @Length(2, 1500) @JsonProperty('deliverables', String, true) deliverables: string = undefined;
  @IsOptional() @Length(2, 1500) @JsonProperty('background', String, true) background: string = undefined;
  @IsDefined() @IsIn(Object.keys(ProjectRate)) @JsonProperty('rate', String, true) rate: ProjectRate = undefined;
  @IsOptional() @IsString() @Length(0, 1500) @JsonProperty('skills', String, true) skills: string = undefined;
  @IsOptional() @IsArray() @JsonProperty('skill_set', [String], true) skill_set: string[] = undefined;
  @IsOptional() @IsString() @Length(2, 1500) @JsonProperty('title', String, true) title: string = undefined;
  @IsOptional() @JsonProperty('start', DateConverter, true) start: Date = new Date();
  @IsOptional() @JsonProperty('end', DateConverter, true) end: Date = undefined;
  @IsOptional() @JsonProperty('update_date', DateConverter, true) update_date: Date = undefined;
  @IsOptional() @JsonProperty('sourcing_type', String, true) sourcing_type: string = undefined;
  @IsOptional() @JsonProperty('sourcing_url', String, true) sourcing_url: string = undefined;
  @IsOptional() @JsonProperty('expert', Boolean, true) expert: boolean = undefined;
  @IsOptional() @JsonProperty('accepted', Boolean, true) accepted: boolean = undefined;
  @IsOptional() @JsonProperty('declined', Boolean, true) declined: boolean = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('candidates', [CandidateDto],  true) candidates: Partial<CandidateDto>[] = undefined;
  @IsOptional() @ValidateNested({each:true}) @JsonProperty('contractor', CandidateDto,  true) contractor: Partial<CandidateDto> = undefined;
  @IsOptional() @ValidateNested({each:true}) @JsonProperty('client', PersonDto,  true) client: Partial<PersonDto> = undefined;

}

@JsonObject('Template')
export class TemplateDto {
  @JsonProperty('templates', [String]) templates: string[] = undefined;
}