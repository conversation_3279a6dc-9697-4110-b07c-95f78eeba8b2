/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsArray, IsDefined, IsOptional, IsString, Length, ValidateNested } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';
import { FilterDto } from './filter';
import { PersonDto } from './person';

@JsonObject('List')
export class ListDto {
  @IsOptional() @JsonProperty('id', String, true) id: string = undefined;
  @IsDefined() @IsString() @Length(2, 1500) @JsonProperty('title', String, true) title: string = undefined;
  @IsOptional() @JsonProperty('public', Boolean, true) public: boolean = undefined;
  @IsOptional() @JsonProperty('shared', Boolean, true) shared: boolean = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('filters', [FilterDto],  true) filters?: FilterDto[] = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('candidates', [PersonDto],  true) candidates?: PersonDto[] = undefined;
}
  