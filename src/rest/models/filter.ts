/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsArray, IsDefined, IsIn, IsOptional, ValidateNested } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';
import { EntityType, Relation, TagType } from '../../types/shared';
import { DateConverter } from '../converters/date_converter';


@JsonObject('TimeVal')
export class TimeValDto {
  @IsOptional() @JsonProperty('week', Boolean, true) week?: boolean = undefined;
  @IsOptional() @JsonProperty('day', Boolean, true) day?: boolean = undefined;
  @IsOptional() @JsonProperty('end', DateConverter, true) end?: Date = undefined;
  @IsDefined() @JsonProperty('start', DateConverter, true) start: Date = undefined;
  @IsDefined() @JsonProperty('text', String, true) text: string = undefined;
}

@JsonObject('Condition')
export class ConditionDto {
  @IsDefined() @JsonProperty('att', String, true) att: TagType|EntityType = undefined;
  @IsDefined() @IsIn(Object.keys(Relation)) @JsonProperty('rel', String, true) rel: Relation = undefined;
  @IsDefined() @IsArray() @JsonProperty('value') value: string[]|TimeValDto[] = undefined;
}

@JsonObject('Filter')
export class FilterDto {
  @IsOptional() @JsonProperty('id', String, true) id?: string = undefined;
  @IsOptional() @JsonProperty('name', String, true) name?: string = undefined;
  @IsOptional() @JsonProperty('network', Boolean, true) network?: boolean = undefined;
  @IsDefined() @IsArray() @ValidateNested({each:true}) @JsonProperty('conditions', [ConditionDto],  true) conditions: ConditionDto[] = undefined;
}

