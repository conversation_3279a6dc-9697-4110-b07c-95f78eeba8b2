/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsDefined, IsOptional, IsString, Length } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';

import { Uid } from '../../types/shared';


@JsonObject('Plan')
export class PlanDto {
  @IsOptional() @JsonProperty('id', String, true) id: string = undefined;
  @IsDefined() @IsString() @Length(2, 1500) @JsonProperty('title', String, true) title: string = undefined;
  @IsDefined() @JsonProperty('skills', [String], true) skills: string[] = undefined;
  @IsOptional() @JsonProperty('course_filter', [String], true) course_filter: Uid[] = undefined;
}