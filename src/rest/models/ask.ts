/*
id: string = undefined;
  title?: string = undefined;
  public?: boolean = undefined;
  shared?: {type: 'user'|'group', id: Uid}[] = undefined;
  network?: boolean = undefined;
  projects?: Uid[] = undefined;
  chat?: ServerChat[] = undefined;
  x?: string = undefined;
  y?: string = undefined;
  categories: Category[] = [];
  candidates: Partial<Candidate>[] = [];
  filters: Filter[] = [];

*/

/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsArray, IsDefined, IsOptional, IsString, Length, ValidateNested } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';
import { ServerChat, Uid } from '../../types/shared';
import { CategoryDto } from './category';
import { FilterDto } from './filter';
import { PersonDto } from './person';

@JsonObject('Share')
export class ShareDto {
  @IsOptional() @JsonProperty('id', String, true) id?: string = undefined;
  @IsOptional() @JsonProperty('type', String, true) type?: 'user'|'group' = undefined;
}


@JsonObject('Ask')
export class AskDto {
  @IsOptional() @JsonProperty('id', String, true) id: string = undefined;
  @IsDefined() @IsString() @Length(2, 1500) @JsonProperty('title', String, true) title: string = undefined;
  @IsOptional() @JsonProperty('public', Boolean, true) public: boolean = undefined;
  @IsOptional() @IsArray() @JsonProperty('shared', [ShareDto], true) shared: {type: 'user'| 'group', id: Uid}[] = undefined;
  @IsOptional() @JsonProperty('network', Boolean, true) network: boolean = undefined;
  @IsOptional() @IsString() @Length(2, 1500) @JsonProperty('x', String, true) x?: string = undefined;
  @IsOptional() @IsString() @Length(2, 1500) @JsonProperty('y', String, true) y?: string = undefined;
  @IsOptional() @IsArray() @JsonProperty('projects', [String], true) projects: string[] = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('categories', [CategoryDto],  true) categories?: CategoryDto[] = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('filters', [FilterDto],  true) filters?: FilterDto[] = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('candidates', [PersonDto],  true) candidates?: PersonDto[] = undefined;
  @IsOptional() @IsArray() @JsonProperty('chat') chat?: ServerChat[] = undefined;
}
 