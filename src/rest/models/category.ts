/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { IsArray, IsDefined, IsIn, IsNumber, IsOptional, IsPositive, ValidateNested } from 'class-validator';
import { JsonObject, JsonProperty } from 'json2typescript';
import { Color } from '../../types/shared';

@JsonObject('WeightedSkill')
export class WeightedSkillDto {
  @IsDefined() @JsonProperty('value', String, true) value: string = undefined;
  @IsOptional() @JsonProperty('weight', Number, true) weight: number = undefined;
}

@JsonObject('Category')
export class CategoryDto {
  @IsDefined() @JsonProperty('id', String, true) id: string = undefined;
  @IsDefined() @JsonProperty('label', String, true) label: string = undefined;
  @IsOptional() @IsArray() @ValidateNested({each:true}) @JsonProperty('skills', [WeightedSkillDto],  true) skills: WeightedSkillDto[] = undefined;
  @IsOptional() @IsIn(Object.keys(Color)) @JsonProperty('color', String, true) color: Color = undefined;
  @IsOptional() @IsNumber() @IsPositive() @JsonProperty('count', Number, true) count: number = undefined;
  @IsOptional() @IsNumber() @IsPositive() @JsonProperty('score', Number, true) score: number = undefined;
  @IsOptional() @JsonProperty('manual', Boolean, true) manual: boolean = undefined;
}