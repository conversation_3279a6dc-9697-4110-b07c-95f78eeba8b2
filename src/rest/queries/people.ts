// related(date: $date) { self, id, name, network, image, type },
export const PERSON_QUERY = /* GraphQL*/ `
  query People($id: ID!, $date: String) {
    people(id: [$id]) {
      comms { link, type },
      contracts { id, client_email, client_name, client_signed, contractor_email, contractor_name, contractor_signed, url },
      description(date: $date) { 
        calendar, message, id, label, link, 
        people { self, id, name, network, image, type }, 
        read, text, date, image 
      },
      extra { type, start, tags { type, start, value, index } , value, index },
      id,
      image,
      learned,
      links { type, link },
      meta,
      name,
      nick, 
      network,
      groups,
      orgs,
      skills { value, weight },
      learned,
      projects { 
        id, 
        admin,
        candidates { self, id, image, network, askfora_id, name, nick, state, answer, learned, public, groups, ready, network, skills { value, weight }, type, related_ids, links { type, link }, comms { type, link }, refer_by { id, vanity } },
        client { self, id, image, network, askfora_id, name, nick, state, ready, learned, skills { value, weight }, type, comms { type, link } },
        suggested,
        accepted,
        declined,
        viewed,
        archived,
        completed,
        confidential,
        contract,
        contractor { self, id, image, network, askfora_id, name, answer, learned, public, groups, nick, state, ready, skills { value, weight }, type, comms { type, link }, refer_by { id, vanity }  },
        suggested,
        duration,
        end,
        escrow,
        fee,
        flex_dates,
        me_candidate { self, id, image, network, askfora_id, answer, learned, public, groups, name, nick, state, ready, skills { value, weight }, type , comms { type, link }},
        network,
        notes,
        requirements,
        deliverables,
        background,
        payment { created, amount },
        progress,
        proposal,
        profile,
        template,
        expert,
        rate,
        refund { created, amount },
        service_fee,
        skills,
        skill_set,
        searched_skills,
        searching,
        start,
        title,
        type,
        update_date,
        activity_date,
        public,
        groups,
        sourcing_url,
        sourcing_type
      },
      related_ids,
      self,
      tasks(due: $date) { due, completed, created, id, title, notes, project, people { self, id, name, network, image, type }, type },
      type,
      status,
      vanity,
      bio,
    }
  }`;

export const PEOPLE_ID_QUERY = /* GraphQL*/ `
  query People($ids: [ID]!){
    people(id: $ids) {
      comms { link, type },
      extra { type, start, tags { type, start, value, index }, value , index},
      description { 
        calendar, message, id, label, link, 
        people { self, id, name, network, image, type }, 
        read, text, date, image 
      },
      id,
      image,
      learned,
      links { type, link },
      meta,
      name,
      network,
      groups,
      nick,
      orgs,
      skills { value, weight },
      self,
      vanity,
      type,
    }
  }`;
 
export const PEOPLE_EMAIL_QUERY = /* GraphQL*/ `
  query People($email: String!, $network: Boolean){
    people(email: [$email], network: $network) {
      comms { link, type },
      contracts { id, client_email, client_name, client_signed, contractor_email, contractor_name, contractor_signed, url, type },
      extra { type, start, tags { type, start, value, index }, value, index },
      description { 
        calendar, message, id, label, link, 
        people { self, id, name, network, image, type }, 
        read, text, date, image 
      },
      id,
      image,
      learned,
      links { type, link },
      meta,
      name,
      nick,
      network,
      groups,
      orgs,
      skills { value, weight },
      self,
      type,
    }
  }`;

export const PEOPLE_NAME_QUERY = /* GraphQL*/ `
  query People($name: String!, $network: Boolean) {
    people(name: [$name], network: $network) {
      comms { link, type },
      contracts { id, client_email, client_name, client_signed, contractor_email, contractor_name, contractor_signed, url, type },
      extra { type, start, tags { type, start, value, index }, value, index },
      description { 
        calendar, message, id, label, link, 
        people { self, id, name, network, image, type }, 
        read, text, date, image 
      },
      id,
      image,
      learned,
      links { type, link },
      meta,
      name,
      nick,
      network,
      groups,
      orgs,
      skills { value, weight },
      self,
      type,
    }
  }`;

export const PEOPLE_ADVANCED_QUERY = /* GraphQL*/ `
  query People(
    $name: [String], 
    $email: [String], 
    $imports: [String], 
    $skills: [String], 
    $tags: [String], 
    $orgs: [String], 
    $roles: [String], 
    $industry: [String], 
    $location: [String],
    $groups: [String], 
    $network: Boolean,
    $first: Boolean,
    $all_groups: Boolean,
    $guest: Boolean,
    $users: Boolean
  ){
    people(
      name: $name, 
      email: $email,
      imports: $imports, 
      skills: $skills, 
      tags: $tags, 
      orgs: $orgs, 
      roles: $roles, 
      industry: $industry, 
      location: $location, 
      groups: $groups, 
      network: $network,
      first: $first,
      all_groups: $all_groups,
      guest: $guest,
      users: $users
    ) {
      comms { link, type },
      contracts { id, client_email, client_name, client_signed, contractor_email, contractor_name, contractor_signed, url, type },
      extra { type, start, tags { type, start, value, index }, value, index },
      description { 
        calendar, message, id, label, link, 
        people { self, id, name, network, image, type }, 
        read, text, date, image 
      },
      id,
      image,
      learned,
      links { type, link },
      meta,
      name,
      nick,
      network,
      groups,
      orgs,
      skills { value, weight },
      status,
      self,
      type,
    }
  }`;

///////////////////
// for unit tests
export const PEOPLE_QUERY = /* GraphQL*/ `
  query People($ids: [ID]!) {
    people(id: $ids) {
      comms { link, type },
      contracts { id, client_email, client_name, client_signed, contractor_email, contractor_name, contractor_signed, url },
      description { 
        calendar, message, id, label, link, 
        people { id, name, network, self, image, type }, 
        read, text, date, image 
      },
      extra { type, start, tags { type, start, value, index }, value, index },
      description { 
        calendar, message, id, label, link, 
        people { self, id, name, network, image, type }, 
        read, text, date, image 
      },
      id,
      image
      name,
      learned,
      links { type, link },
      meta,
      network,
      groups,
      self,
      nick,
      orgs,
      skills { value, weight },
      learned,
      projects { 
        id, 
        admin,
        candidates { self, id, image, askfora_id, name, nick, state, answer, public, groups, learned, ready, network, skills { value, weight }, type, related_ids, links { type, link }, comms { type, link }, refer_by { id, vanity } },
        client { self, id, image, network, askfora_id, name, nick, type, learned, comms { type, link }, skills { value, weight } },
        suggested,
        accepted,
        declined,
        viewed,
        archived,
        completed,
        confidential,
        contract,
        contractor { self, id, image, network, askfora_id, name, nick, state, answer, learned, public, groups, ready, skills { value , weight }, type, comms { type, link }, refer_by { id, vanity }  },
        duration,
        end,
        escrow,
        fee,
        flex_dates,
        me_candidate { self, id, image, network, askfora_id, name, answer, public, groups, nick, state, ready, skills { value, weight }, type, comms { type, link } },
        network,
        notes,
        requirements,
        deliverables,
        background,
        payment { created, amount },
        progress,
        proposal,
        profile,
        template,
        expert,
        rate,
        refund { created, amount },
        service_fee,
        skills,
        skill_set,
        searched_skills,
        searching,
        start,
        title,
        type,
        update_date,
        activity_date,
        simple_invite,
        no_fee,
        no_referrals,
        public,
        groups,
        sourcing_url,
        sourcing_type
      },
      status,
      related_ids,
      tasks { due, completed, created, id, title, notes, project, people { id, self, network, name, image, type }, type },
      type,
      vanity,
      bio,
    }
  }`;