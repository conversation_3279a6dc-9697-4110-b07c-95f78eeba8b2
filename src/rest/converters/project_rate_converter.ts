/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { JsonConverter, JsonCustomConvert } from 'json2typescript';
import { ProjectRate } from '../../types/shared';

@JsonConverter
export class ProjectRateConverter implements JsonCustomConvert<ProjectRate> {
  deserialize(rate: any): ProjectRate {
    return ProjectRate[rate] as ProjectRate;
  }

  serialize(rate: ProjectRate): any {
    return rate.toString();
  }
}
