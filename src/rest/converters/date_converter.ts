/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { JsonConverter, JsonCustomConvert } from 'json2typescript';

@JsonConverter
export class DateConverter implements JsonCustomConvert<Date> {
  deserialize(date: any): Date {
    return new Date(date);
  }

  serialize(date: Date): any {
    return date.toISOString();
  }
}
