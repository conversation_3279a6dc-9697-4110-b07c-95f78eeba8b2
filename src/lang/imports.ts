import { Imports } from '../types/shared';

import { promptInfo } from '../utils/info';

import about from './about';

function accountLabel(t) {
  switch(t) {
    case Imports.Facebook: return 'Facebook ';
    case Imports.iCloud: return 'Apple or iCloud ' ;
    case Imports.LinkedIn: return 'LinkedIn ';
  }
  return '';
}

export default {
  IMPORTS_KWD: ['import', 'upload', 'how to import', 'how do i import', 'how do you import', 'how to upload', 'how do i upload', 'how do you upload'],

  ACCOUNT_KWD: ['account', 'profile'],

  IMPORTS_CMD: 'Import',

  IMPORTS_LIST: ['list imports', 'show imports', 'list files', 'show files', 'list uploads', 'show uploads', 'imports', 'uploads'],

  EXAMPLES: ['Ask me to "import" to add your private network. I never contact anyone without your permission'],

  IMPORTS_TYPES: {
    facebook: Imports.Facebook,
    fb: Imports.Facebook,
    apple: Imports.iCloud,
    icloud: Imports.iCloud,
    'i-cloud': Imports.iCloud,
    cloud: Imports.iCloud,
    ios: Imports.iCloud,
    mac: Imports.iCloud,
    macos: Imports.iCloud,
    macosx: Imports.iCloud,
    'mac-os': Imports.iCloud,
    'mac-osx': Imports.iCloud,
    'mac-os-x': Imports.iCloud,
    linkedin: Imports.LinkedIn,
    'linked-in': Imports.LinkedIn,
  },

  IMPORTS_LIST_DOC: doc => `${doc.id} ${doc.file_id} ${doc.mime} ${doc.created}`,

  IMPORTS_LIST_NONE: "You don't have any uploads pending",

  IMPORTS_PROGRESS: "I'm working on importing your data. Give me a few minutes and I'll email you when I'm done.",

  IMPORTS_PROMPT_TYPE: 'What kind of data is this?',

  IMPORTS_PROMPT_ERROR: "I wasn't able to import your data.",

  IMPORTS_TUTORIAL_TYPE: 'What kind of data do you want to import?',

  IMPORTS_TUTORIAL_TYPE_ANSWERS: ['Facebook', 'LinkedIn', 'iCloud'],

  IMPORTS_ACCOUNT_TYPE: 'Which account do you want to import?',

  IMPORTS_ACCOUNT_TYPE_ANSWERS: ['Facebook', 'LinkedIn'],

  IMPORTS_ACCOUNT: t => [`What's your ${accountLabel(t)}account?`, `Make sure your ${accountLabel(t)} email matches an email on your AskFora profile`],

  IMPORTS_FACEBOOK_FILENAMES: [
    'your_address_books',
    'received_friend',
    'sent_friend',
    'your_friends',
    'profile_information',
  ],

  IMPORTS_ACCOUNT_HINT: t => {
    switch (t) {
      case Imports.Facebook: return 'https://facebook.com/askfora';
      case Imports.LinkedIn: return 'https://www.linkedin.com/in/askfora';
      case Imports.iCloud: return '<EMAIL>';
    }
  },

  IMPORTS_ACCOUNT_IMPORTING: t => `I'm adding information from your ${accountLabel(t)}account and will email you when it's ready.`,

  IMPORTS_TUTORIAL_FACEBOOK: [
    // promptInfo('Start by opening your Facebook privacy settings from', 'https://www.facebook.com/settings?tab=your_facebook_information', 'here'),
    // promptInfo("Facebook doesn't support automatic syncing at this time, so there are a few steps to follow."),

    promptInfo("Happy to help import your Facebook contacts in a few simple steps. I've emailed these to you to follow."),
    // promptInfo("I've sent you detailed instructions to download your contacts, and you can also see them", '/settings/accounts/import/facebook', 'here'),
    promptInfo('Start by opening your Facebook settings from', 'https://www.facebook.com/settings?tab=your_facebook_information', 'here'),
    promptInfo("Once you have your file, tap 'Upload' from account settings and I'll let you know when I've finished adding your contacts."),
    // 'You can find more specific, detailed instructions in Account Settings.',
  ],

  IMPORTS_TUTORIAL_ICLOUD: [
    // promptInfo('Start by signing in to your iCloud contacts from', 'https://www.icloud.com/#contacts', 'here'),
    // promptInfo("Choose your first contact and then press ctrl+a on your keyboard to select them all, then tap 'Export vCard' from the gear icon menu on the bottom left"),
    // promptInfo('A vcf file will automatically download to your computer.'),
    // promptInfo("From the Accounts menu on the Settings page, upload the vcf file using the iCloud 'Upload' button", '/settings/accounts', 'here'),
    //promptInfo("Apple doesn't support automatic syncing at this time, so there are a few steps to follow."),
    promptInfo("Happy to help import your Apple iCloud contacts in a few simple steps. I've emailed these to you to follow."),
    // promptInfo("I've sent you detailed instructions to download your contacts and you can also see them", '/settings/accounts/import/icloud', 'here'),
    promptInfo('Start by signing in to your iCloud contacts from', 'https://www.icloud.com/contacts', 'here'),
    promptInfo("Once you have your file, tap 'Upload' from account settings and I'll let you know when I've finished adding your contacts"),
  ],

  IMPORTS_TUTORIAL_LINKEDIN: [
    // promptInfo('Start by opening LinkedIn privacy settings from', 'https://www.linkedin.com/psettings/member-data', 'here'),
    // promptInfo('LinkedIn will let you know when your data is ready for download.'),
    // promptInfo("Download the file to your computer, then from the Accounts menu on the Settings page upload the zip file using the Linkedin 'Upload' button", '/settings/accounts', 'here'),
    // promptInfo("LinkedIn doesn't support automatic syncing at this time, so there are a few steps to follow."),
    promptInfo("Happy to help import your LinkedIn contacts in a few simple steps. I've emailed these to you to follow."),
    // promptInfo("I've sent you detailed instructions to download your contacts and you can also see them", '/settings/accounts/import/linkedin', 'here'),
    promptInfo('Start by opening LinkedIn settings from', 'https://www.linkedin.com/psettings/member-data', 'here'),
    promptInfo("Once you have your file, tap 'Upload' from account settings and I'll let you know when I've finished adding your contacts."),
  ],

  IMPORT_EMPTY: (name, label) => `${name},
  
  I wasn't able to upload anything your ${label} data. Double check the file or send me a note and I can help.
  
 ${about.ABOUT_EMAIL_SIGNATURE}`,

  IMPORT_MESSAGE: (name, label, url) => `${name},
  
  I've finished uploading your ${label} data. Click here to see it: ${url}
  
 ${about.ABOUT_EMAIL_SIGNATURE}`,
};
