export default {
  LO<PERSON><PERSON>_NAME: 'Lookup When',

  <PERSON>O<PERSON><PERSON>_DESC: 'Get information on when someone e-mailed or you or when you met someone',

  LOOKUP_RESERVED: ['when [did I meet|email someone?]', "when's [my meeting with someone?]"],

  LO<PERSON>UP_EG: ["Forgot when you last met someone? You can ask me 'When did I last meet with <PERSON>?'"],

  LOOKUP_KWD: [
    'when',
    "when's",
    'when did',
    'tell me when',
    'find out when',
    'find when'
  ],

  LOOKUP_RECENT: ['last', 'recenty', 'recently', 'just', 'met', 'emailed'],

  LOOKUP_UPCOMING: ['next', 'coming', 'meeting'],

  LOOKUP_START: ['next', 'coming', 'meeting'],

  LOOKUP_END: ['done', 'end', 'until'],

  LOOKUP_FIRST: ['first'],
  
  LOOKUP_LAST: ['last'],
  
  LOOKUP_RECONNECT: ['reconnect'],

  FINISHED_ANSWERS: [/*'Next', */'Take Notes', 'Reminder', 'Follow-up', 'Done'],

  EVENT_ANSWERS: ['Done', 'Cancel'],

  MEETING_ANSWERS: ['Info', 'Running late'],
};
