import { stringList } from '../utils/format';
import { promptInfo } from '../utils/info';
import about from './about';

export default {
  CONTRACT_EG: [/*'Contract with <PERSON><PERSON> about the Design Project'*/],

  MYSELF: ['myself'],

  CONTRACT_KWD: [
    'contract',
    /* 
    'send contract',
    'send a contract',
    'start contract',
    'start a contract',
    'create contract',
    'create a contract',
    'new contract',
    */
  ],

  // SHOW_CONTRACT_KWD: ['contracts'],

  // CLOSE_CONTRACT_KWD: ['close', 'save'],

  // CONTRACT_ACTION: ['signed', 'declined', 'error'],

  // CONTEXT: [' about ', ' regarding ', ' re ', ' for ', ' for project', ' for job'],

  CONTRACT_LOAD: 'contract load',

  CONTRACT_SIGN: 'contract sign',

  CONTRACT_COMPLETE: ['contract signed', 'contract declined', 'contract error'],

  CONTRACT_PREP: (name, context) => `I'll reach out to ${name} and let you know when your agreement for ${context} is ready to sign.`,

  CONTRACT_WAITING: (name, context) => `I'll let you know when your agreement with ${name} for ${context} is ready to sign.`,

  // CONTRACT_PARTY_PROMPT: 'Are you the client (paying for services?)',

  // CONTRACT_PARTY_ANSWERS: ["Yes, I'm paying", "No, I'm getting paid"],

  CONTRACT_NAME_PROMPT: "In order to prepare the agreement, I need to know if you're contracting under your own name. Or if you represent an organization, tell me what it is now.",

  CONTRACT_NAME_ANSWERS: ['Myself'],

  // CONTRACT_EMAIL: name => `Please confirm or update the email for ${name}`,

  // CONTRACT_CONTEXT: 'Please briefly explain the scope of services (e.g. legal services)',

  CONTRACT_SUBJECT: company => `Service Agreement from ${company}`,

  CONTRACT_NOTIFICATION: 'You have a service agreement to sign.',

  CONTRACT_MAIL_INITIAL: (as_client: boolean, to: string, from: string, context: string, url: string) => {
    let intro = `I'm helping ${from}, who is an expert in ${context} and interested in working for you.`;
    if (as_client) intro = `I'm helping ${from} find an expert in ${context} for a job.`;
    return `${to},

I'm Fora, a service for doing better work with people you know. ${intro}

${from} asked me to send you a service agreement to review and sign: ${url}

 ${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONTRACT_MAIL: (as_client: boolean, to: string, from: string, context: string, url: string) => {
    return `${to},

Here's the standard services agreement for your ${context} job with ${from}. Follow the link to review and sign: ${url}

 ${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONTRACT_LOGIN: p => `In order to prepare your agreement, I need to confirm your email. You can do that by logging in with your ${stringList(p, 'or')} account. Which do you want to use?`,

  CONTRACT_READY_SUBJECT: n => `Sign your agreement with ${n}`,

  CONTRACT_NOTIFY_TITLE: 'AskFora Service Agreement',

  CONTRACTOR_NEXT_STEPS: n => `AskFora ${n} next steps`,  

  CONTRACT_READY_MAIL: (from, url) => {
    return `${from} received your agreement and it is ready for your signature: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONTRACT_COMPLETED: name => {
    return promptInfo(`Thank you for using AskFora to complete your service agreement.`);
  },

  // CONTRACT_REGISTER: "If you'd like Fora to help you find people you know for work you need done, you can say 'Connect'",

  AUTH: p => {
    return promptInfo(`For details about this service agreement, you will need to sign in with a ${stringList(p, 'or')} account.${p.length > 1 ? ' Which do you want to use?' : ''}`);
  },

  CONTRACT_CONNECT: 'Connect',

  CONTRACT_LOADING: s => { 
    if (s) return 'Loading your agreement for signature...';
    else return 'Downloading your signed agreement...';
  },

  CONTRACT_DECLINED: name => {
    return promptInfo(`Got it. I've let ${name} know and canceled the agreement.`);
  },

  CONTRACT_CANCELED: name => {
    return `This agreement has been declined. You will need to start a new job to sign an agreement with ${name}.`;
  },

  CONTRACT_ERROR: url => {
    return promptInfo('There was an error loading the agreement. Please try again by clicking', `${url}`, 'here', [], true);
  },

  CONTRACT_SIGNING_ERROR: url => {
    return promptInfo('There was an error signing the agreement. Please try again by clicking', `${url}`, 'here', [], true);
  },

  // CONTRACT_MISSING: 'I need a valid contact and email to create a service agreement.',

  CONTRACT_MISMATCH: 'This does not appear to be a valid service agreement for you.',

  CONTRACT_NOT_READY: 'This agreement is not ready for signature.',

  CONTRACT_INTERNAL_ERROR: 'I ran into an error with this agreement',

  CONTRACT_LOAD_ERROR: 'Your agreement has expired. You will need to create a new one.',

  AUTH_CANCELLED: auth => {
    return [
      promptInfo(`AskFora uses ${auth.name} accounts for authentication.`),
      promptInfo(`I wasn't able to authenticate to ${auth.name} because you didn't approve my permissions.`),
      promptInfo('For details about this service agreement please sign in and approve permissions with your', auth.url, `${auth.name} account`, [], true),
    ];
  },

  NO_CONTRACTS: "I couldn't find any service agreements",
};
