import ForaUser from '../session/user';
import { FORA_GUEST_ID, FORA_PROFILE } from '../types/user';

import { Event, Note, Person, Project, Tag, Task } from '../types/items';
import { AuthClientContext, AuthPermissions, AuthProviders, BasicInfo, ForaUserSettings, ProjectRate, TagType } from '../types/shared';

import { DAYS, ROUND_HOUR } from '../utils/datetime';
import { localeDate, stringList } from '../utils/format';
import { randomPhrase } from '../utils/funcs';
import { promptInfo } from '../utils/info';

function createForaInfoUser(): Person {
  return new Person({
    id: `people/${FORA_PROFILE}`,
    self: true,
    displayName: 'Guest User',
    comms: ['<EMAIL>'],
    tags: [new Tag(TagType.jobTitle, 'Guest',  0, new Date() ), new Tag(TagType.organization,  'AskFora', 0, new Date() )],
    photos: ['/icons/icon-196.png'],
    urls: ['https://askfora.com', 'https://facebook.com/askfora', 'https://twitter.com/askfora'],
  });
}

function createForaUser(): ForaUser {
  const fora_user = new ForaUser(FORA_PROFILE);
  fora_user.provider = AuthProviders.Email;
  fora_user.permissions = { email: AuthPermissions.None, organizer: AuthPermissions.None, demo: AuthPermissions.Full };
  fora_user.settings = {
    authenticated: false,
    email: undefined,
    beta: false,
    push: 'ask',
    active: null,
    calendars: { enabled: false, disallowed: false, sources: { fora_internal: { name: 'Calendar', selected: true, sync: true } } },
    imports: { enabled: false, disallowed: false },
    info: { enabled: false },
    messages: { enabled: false, disallowed: false },
    notes: { enabled: false, disallowed: false },
    people: { enabled: false, disallowed: false },
    lists: { enabled: false, disallowed: false },
    plans: { enabled: false, disallowed: false },
    analyses: { enabled: false, disallowed: false },
    development: { enabled: false, disallowed: false },
    learning: { enabled: false, disallowed: false },
    projects: { archived: false, open: true },
    providers:[],
    accounts: [],
    notifications: [],
    templates: [],
    tasks: { enabled: false, disallowed: false },
    widgets: [],
    tutorial: { id: undefined, lesson: undefined, practiced: undefined, assessed: undefined },
  } as ForaUserSettings;

  return fora_user;
}

export default {
  HELP: ['help'],

  FORA_USER: createForaUser,

  FORA_PROFILE,

  FORA_GUEST_ID,

  // FORA_TOKENS: () => { return {expiry_date: DAYS(new Date(), 30)}; },

  // FORA_PERSON: {id: 'people/guest', focus: 'com.askfora.Fora', type: 'com.askfora.Person'},

  FORA_INFO: createForaInfoUser,

  FORA_MEET: () => {
    return [
      new Event({
        id: 'meet_fora',
        calendarId: 'fora_internal',
        title: 'How Fora can Connect you with Trusted Professionals',
        start: ROUND_HOUR(new Date()),
        end: ROUND_HOUR(new Date(), 1),
        link: '/',
      }),
      new Event({
        id: 'use_fora',
        calendarId: 'fora_internal',
        title: 'New Project kick off',
        start: ROUND_HOUR(new Date(), 1),
        end: ROUND_HOUR(new Date(), 2),
        link: '/',
      }),
    ];
  },

  FORA_CONNECT: () => {
    return new Task({
      id: 'connect_fora',
      taskListId: 'fora_internal',
      title: 'Reach out to Omer about AskFora',
      due: ROUND_HOUR(new Date(), 1),
      people: [{ id: 'people/f1', displayName: 'Omer' }],
    });
  },

  FORA_NOTE: () => {
    return new Note({
      id: 'note_fora',
      file_id: 'fora_internal',
      created: new Date(),
      notes: [
        `Notes from your meeting on ${localeDate(new Date(), 'en-US', 'America/New_York')} about How Fora can Connect you with Trusted Professionals`,
        'Fora can find trusted professionals to assist with your projects by searching within your network and in Fora’s vast extended network to help you connect with more people.',
        "You can use AskFora chat to search for people by name or by asking for a needed skill like 'find Stacey Jacobs,' 'look for a web designer' or 'I need an expert in trademark law'.",
        'Once you have defined the skill set, project description, rate of pay, and estimated number of hours for the work, Fora will begin the search and present to you viable candidates who ' +
          'have already expressed interest in the project. Even better, Fora will help execute a contract between you and the expert, and escrow funds, making the process of hiring and payment ' +
          'seamless and easy for everyone involved.',
      ],
    });
  },

  FORA_PROJECT: () => {
    return new Project({
      id: 'fora_internal_0',
      skills: 'UX design',
      notes: 'Looking for a UX Designer to help with initial design and implementation of a new project that will help busy professionals connect with others and keep track of their interactions.',
      client: createForaInfoUser(),
      contractor: null,
      contract: 'demo_contract',
      start: DAYS(new Date(), -1),
      end: DAYS(new Date(), 3),
      rate: ProjectRate.fixed,
      fee: 0,
      duration: 5,
      progress: 1,
      completed: false,
      confidential: false,
      candidates: [],
    });
  },

  WELCOME: [
    // "Hi I'm Fora. I connect you with people in your network who have the skills and experience you need. I handle contracting and payment, you get better work with people you know.", 
    // 'What skills are you looking for?'
    // "Hi I'm Fora. I connect you with people in your network who have the skills and experience you need. If you're a contractor, I can help you get hired by people you know and trust.", 
    // "Are you hiring for a job, have a question for an expert, or freelancing?",
    // "Hi, I'm Fora! I help you make better use of your professional network, and manage your team based on their skills.",
    `Hi, I'm Fora. I can help you make better use of your professional network and develop your skills through learning goals.`
  ],

  WELCOME_EXPERT: "Hi I'm Fora. I connect you with people in your network who have the skills and experience you need.", 

  WELCOME_ANSWERS: ["Hiring", "Expert", "Freelancing", "Learn more", "Login"],

  INIT_PROJECT: ['hiring', 'hire', 'find', 'client', 'manager', 'managing', 'finding', 'searching'],
  INIT_EXPERT: ['question', 'answer', 'ask', 'inquiry', 'expert', 'expertise'],
  INIT_CONTRACTOR: ['contract', 'contracting', 'contractor', 'freelancer', 'freelance', 'freelancing'], 
  // WELCOME_BETA: ["Hi I'm Fora. I find people you know for short term work and handle contracting and payment.", 'I just launched my beta. Do you want to give it a shot?'],

  BETA_LINK: l => {
    return promptInfo('You can check out my beta at', l, l, [], true);
  },

  WELCOME_HINT: 'How can you help me, Fora?',

  WELCOME_HELP: providers => { return [
    // 'I thought you’d never ask!',
    // 'I help you develop and maintain the relationships you already have and I can connect you to people you’d like to know or work with.',
    'I automatically create a private, skills-based, talent marketplace to help you do better work with people you know.',
    `You can get started by logging in to your ${stringList(providers, 'or')} account.`,
    // 'You\'re logged into a demo and seeing what our founder, Omer, would see when he uses AskFora. Find out more by exploring the menu below. You can chat with me at any time by clicking my picture in the middle.',
    "If you want to learn more, go ahead and ask. I'm happy to answer your questions about how AskFora works.",
  ]; },

  CONFIRM_CONNECT: "All set. I'll automatically synchronize your users and contacts",

  DONE_LEARNING: 'I\'m done with my initial learning. Ask me for \'help\' anytime if you want to learn more about what I can do',

  FAQ_ANSWER: 'Tell me more',

  CONNECT_PICK: providers => {
    return `AskFora uses ${stringList(providers, 'or')} to read your calendars and contacts. Which do want to use?`;
  },

  REGISTER_PICK: providers => {
    return `AskFora uses ${stringList(providers, 'or')} for logins. Which do want to use?`;
  },

  REGISTER_EMAIL: `What's your email address?`,

  REGISTER_CODE: 'Check your email for a code and let me know what the code is.',

  REGISTER_EMAIL_FAIL: "Sorry, I didn't understand. Can you try providing your email again?",

  REGISTER_REPLY_LINK: (auth: AuthClientContext) => {
    return {
      label: 'Follow this link and sign in with your',
      text: `${auth.name} account`,
      link: auth.url,
      redirect: true,
    } as BasicInfo;
  },

  REGISTER_REPLY_REDIRECT: (auth: AuthClientContext) => {
    return {
      label: null,
      link:auth.url,
      redirect: true,
    }
  },

  REGISTER: (url, provider) => {
    return promptInfo(`Make sure you select the correct ${provider} account by`, url, 'clicking here', [], true);
  },

  REG_ERROR: (url, provider) => {
    return promptInfo(`I wasn't able to connect to ${provider}.`, url, 'Try again?', [], true);
  },

  REG_ERROR_CANCELLED: (url, provider) => {
    return [
      promptInfo(`I wasn't able to connect to ${provider} because you didn't approve my permissions.`),
      promptInfo('In order to help I will need you to approve them for me.', url, 'Try again?', [], true),
    ];
  },

  REG_REQUIRED: provider => {
    return `I'll need your permission to access information at ${provider}. Say 'Link' to securely link your contacts and calendar.`;
  },

  REG_WRONG_PROVIDER: (provider?, existing?) => {
    if (provider && existing) return `I wasn't able to log you in using ${provider}. Tryin using ${existing}?`;
    else return `I wasn't able to log you in. Try a different account type?`;
  },

  REG_ANSWERS: ['Link', 'Nevermind'],

  REG_CONNECT: 'Link',
  
  REG_CONNECT_CONTACTS: 'Link Contacts',

  PERMISSIONS_BASIC: () => [
    promptInfo("After you login, you'll be asked to give me access to some information including your profile info to verify your identity."),
    // 'I also need your permissions to store and share notes and docs, but only when you ask me to.',
    // 'I never change or delete information unless you ask me.',
    promptInfo(`I'm conscientious about how I use any data from your account. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
  ],

  PERMISSIONS_CHAT_API: () => [
    promptInfo("After you connect, you'll be asked to give me access to chat with team members. Each member will need to choose connect and opt-in before I will chat with them."),
    promptInfo(`I'm conscientious about how I use any data from your team. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
  ],

  PERMISSIONS_CONNECT: () => [
    promptInfo("After you connect, you'll be asked to give me access to your directory. Each member or employee will need to choose connect their accounts when they log in."),
    promptInfo(`I'm conscientious about how I use any data from your team. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
  ],

  PERMISSIONS_PROJECT: () => [
    promptInfo("I'll need to confirm your account and get your permissions to look at calendars, and contacts to figure out who could help you with this job"), //store and share notes, docs, and tasks when you ask me to.',
    promptInfo(`I'm conscientious about how I use your data. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
  ],

  PERMISSIONS_ORGANIZER: () => [
    promptInfo("I'll need to confirm your account and get your permissions to look at calendars and contacts to get started."), //  figure out who could help you with this job"), //store and share notes, docs, and tasks when you ask me to.',
    // promptInfo('I check your contacts to figure out who you know and learn what they can help with'), // and who else they know.',
    // 'I never change or delete information or contact anyone unless you ask me.',
    promptInfo(`I'm conscientious about how I use your data. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
  ],

  PERMISSIONS_ORGANIZER_SYNC: () => [
    promptInfo("I'll need to confirm your account and get your permissions to manage your calendars, contacts, and tasks."),
    promptInfo(`I'm conscientious about how I use your data. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
  ],

  PERMISSIONS_FULL: () => [
    promptInfo(`To help find people you know, I can take a look at who you email. I only look at names and addresses, not the contents of your email. If you ask me to, I can help draft emails and contact people.`), // That way I can see who you know, learn what they can help with, and who they can refer for you.",
    // promptInfo('I only look at who you email, not the contents, and I only create email drafts or contact people when you ask me.'),
    promptInfo(`I'm conscientious about how I use any data from your account. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
  ],

  CONFIRM_DISCONNECT: provider => `Are you sure you want to disconnect your ${provider} account? You will not lose any data but you will need to reconnect if you want to contine to use AskFora with ${provider}.`,

  DISCONNECTED: 'I have disconneted your account.',

  GOODBYE: () => {
    return randomPhrase(['ttyl', 'ttfn', 'cya', 'come back soon', 'k', 'bye!']);
  },

  CONFIRM_DELETE: (provider, link) => [
      link ? promptInfo(`This will irrecoverably delete all your AskFora data. Your ${provider} data will not be touched and you can manually remove access`, link, 'here') : 
      promptInfo(`This will irrecoverably delete all your AskFora data. Your ${provider} data will not be touched.`),
      promptInfo('Are you sure you want to delete your AskFora account?'),
  ],

  LOGIN_CODE_SUBJECT: 'AskFora Login Code',
  LOGIN_CODE_MESSAGE: code => `Your AskFora login code is ${code}`,
};
