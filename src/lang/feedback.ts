export default {
  FEEDBACK_EG: ['I\'m always trying to improve. Tell me when I mess up by starting your chat with "Feedback:"'],

  FEEDBACK_RESERVED: ['feedback', 'send', 'share'],

  FEEDBACK_SHORTCUTS: a => {
    if (a) return { name: 'Send Feedback', value: 'Feedback' };
    else return null;
  },

  FEEDBACK_KWDS: ['feedback', 'send feedback', 'share feedback', "isn't working", 'not working', 'problem', 'error', 'stuck'],

  //FEEDBACK_SENSITIVE: 'May I include the recent exchanges between us (say NO if anything is sensitive)?',
  FEEDBA<PERSON>K_SENSITIVE: 'May I use this feedback and our recent conversation to learn more about you?',

  FEEDBACK_ANSWERS: ['Yes, include our conversation', "No, it's sensitive"],

  FEEDBACK_PROMPT: "Send me your feedback and I'll let my team know about it. Tell me if you want a response and we'll be in touch soon.",

  FEEDBACK_CONFIRM: "I've shared your feedback. Thank you!",

  HELP_FEEDBACK_CONFIRM: "Thanks, I'll let my team know and they will email you directly.",

  HELP_FEEDBACK: 'Okay, what else can I help you with?',
};
