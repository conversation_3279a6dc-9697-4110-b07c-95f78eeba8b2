export default {
  CONNECT_EG: ['You can quickly remember people you meet. Just chat "Add <PERSON>, CEO at <NAME_EMAIL>"'],

  CONFIRM: n => [
    `Should I add ${n} to your contacts?`, 
    'You can also tell me more contact info or notes you want to remember.'
  ],

  RECONFIRM: n => `Got it. Ready to update ${n} in your contacts or do you have additional details?`,

  CONNECT_PROMPT: 'Who do you want to add and what is their contact info?',

  CONFIRM_ANSWERS: ['Ok', 'Nevermind'],

  CONFIRM_CONNECTION: p => `Do you want to connect with ${p.displayName}?`,

  CONNECT_HINT: 'Name, email, phone...',

  CONFIRM_MATCH: 'I found a possible match. Is this correct?',

  CONNECTING_WAIT: "Sit tight, I'll be just a minute...",

  MATCH_ANSWERS: ['Yes!', 'No, add a new contact'],

  PROMPT_EMAIL: n =>  { return `What's ${n}'s email address?`; },

  SELECT_MATCH: 'I found a few possible matches. Are any of them correct (pick the right one)?',

  SELECT_ANSWERS: ['No, add a new contact'],

  MEET_SUBJECT: 'Nice meeting you',

  MEET_EMAIL: (name: string, meet: string, a: string) => {
    const about = a && a.length ? `I'm looking forward to continuing our conversation regarding ${a}` :
      'I look forward to staying in touch';

    return `${meet},

It was a pleasure meeting you. ${about}.

-${name}

---
Connected with help from AskFora (https://askfora.com). Better work with people you know.`;
  },

  DRAFT: "I've drafted a follow up that you can",

  EDIT_SEND: 'edit and send',

  DRAFT_PROMPT: 'Should I draft a follow up message?',

  DRAFT_ANSWERS: ['Yes, draft a message', 'No, just add a contact'],

  ACK: 'No worries.',

  CONNECTING: n => {
    return `I updated ${n} in your contacts.`;
  },

  INTRO: [
    'add',
    'add contact',
    // 'meet ',
    // 'meeting ',
    'met',
    'i met',
    'learn',
    'discover', 
    'research',
  ],

  PREFIX: [
    'Add',
    'Add Contact',
    'Met',
    'I Met',
    'Learn',
    'Discover',
    'Research',
  ],

  CONTEXT: ['about ', 'regarding ', ' at ', ' re ', ' to ', ' on ', ' from '],
};
