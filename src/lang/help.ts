import { stringList } from '../utils/format';
import { promptInfo } from '../utils/info';

export default {
  HELP_EG : ['If you want to change what you\'re doing or I ever seem stuck, just tell me to "Nevermind"'],

  HELP_RESERVED: ['help', 'how do', 'tell', 'show', 'examples', 'example', 'features', 'feature', 'tutorial'],

  HELP_KWDS: ['help', 'get help', 'tutorial', 'tips', 'learn more'],

  HELP_TIP: ['tip'],

  HELP_FREELANCING: ['find work', 'freelance', 'freelancing', 'contracting', 'profile', 'edit profile', 'share profile', 'share my profile', 'edit my profile', 'complete profile', 'complete my profile', 'my profile', 'show profile', 'show my profile'],
  
  HELP_PROFILE: ['profile', 'edit profile', 'share profile', 'share my profile', 'edit my profile', 'complete profile', 'complete my profile', 'my profile', 'show profile', 'show my profile'],

  HELP_ELSE: ['something else'],

  TUTORIAL_FREELANCING: [
    promptInfo('AskFora helps you get hired by people in your network. Start by completing your profile then add a Freelance Service Offering.'), 
    promptInfo('Just tap "Profile" in the top right, add your experience and skills, create a freelance service offering, then share your personal link with your professional network.'),
    promptInfo('Get started by editing your', '/settings/profile', 'profile'),
  ],

  HELP_MENU: ['help', 'what can you do', 'what else can you do'],

  HELP_ALL: ['examples', 'example', 'features', 'feature', 'how'],

  HELP_SEARCH: ['help find people', 'help me find people', 'find people', 'find people by skill', 'find people by name'],

  HELP_TERMS_KWD: ['data', 'privacy', 'terms', 'security', 'about', 'blog'],

  HELP_TERMS_DATA: ['data'],

  HELP_TERMS_PRIVACY: ['privacy'],

  HELP_TERMS_SECURITY: ['security'],

  HELP_TERMS_ABOUT: ['about'],

  HELP_TERMS_BLOG: ['blog'],

  HELP_TERMS_TERMS: ['terms'],

  HELP_TERMS_PROMPT: 'Tap to read about my',
  HELP_TERMS: 'terms of services',
  HELP_TERMS_DATA_PROMPT: 'Tap to learn more about what I do with your',
  HELP_DATA: 'data',
  HELP_TERMS_PRIVACY_PROMPT: 'You can tap to see my',
  HELP_PRIVACY: 'privacy policy',
  HELP_TERMS_SECURITY_PROMPT: 'Tap to see my',
  HELP_SECURITY: 'security process',
  HELP_TERMS_ABOUT_PROMPT: 'Learn more about AskFora',
  HELP_ABOUT: 'here',
  HELP_TERMS_BLOG_PROMPT: 'Read up the latest on my',
  HELP_BLOG: 'blog',
  // HELP_TERMS_TERMS_PROMPT: '',

  HELP_SEARCH_PROMPT: 'Tell me who you want to find. You can search by name, skill, experience, or anything else.',

  FAQ_START: ['faq', 'frequently asked questions', 'show me the faq', 'what is the faq', 'what are the faq', 'tell me more', 'how can you help', 'what are you', 'what do you do'],
  
  LEARN_MORE: ['learn more'],

  HELP_CONNECT: ['connect', 'link'],
  HELP_CONNECT_ME: 'connect with fora',

  CLICK_TUTORIAL: "Select either AskFora for Clients or AskFora for Freelancers above to see how I can help.",

  IGNORE: ['with', 'about', 'on', 'of', 'including', 'around', 'do', 'to'],

  HELP_MERGE: "I can't log you in with that provider. Maybe you used a different account? After you login you can connect additional accounts to use for logging in.",

  SHARE: 'Click the icons above to share your profile with people you know by email, Facebook, LinkedIn or Twitter.',

  TUTORIAL: name => [
    `It's nice to meet you${name}. I'm gathering information about people you know and remembering it securely.`,
    // promptInfo('Check the information above and make sure it looks correct. You can update it via', 'https://contacts.google.com', 'Google Contacts'),
    // "To get started, see what I know how to do by asking me 'what can you do?'",
    "Are you hiring for a job, have a question for an expert, or freelancing?",
  ],

  TUTORIAL_ANSWERS: ["Hire for a Job", "Find an Expert", "Freelancing", "Learn more"],

  CONNECT_TUTORIAL: "I'm gathering information about people you know and remember it securely so you can search your network.",

  HELP_EXAMPLES: 'Some examples of requests I understand:',
  HELP_FEEDBACK: 'You can say "Feedback" to share bugs or comments.',
  HELP_ANSWER: 'What can you do?',

  HOW_TO: p => [
    // "I show timely information about who you meet, remember notes about people, and find anyone you ask for.",
    "To start, I'll learn about your contacts and meetings, keeping everything secure. I always ask and confirm before doing things.",
    `If you'd like my help, say 'Link.' You'll need a ${stringList(
      p,
      'or',
    )} account to use AskFora, and if you use multiple accounts, be sure to connect me with the account where you sync your contacts`,
  ],

  EMAIL_HOW_TO: [
    "For more relevant information, I can also analyze the people you email (but not the email contents), and draft replies for you. You can ask me to 'connect email' or 'disconnect email' anytime.",
  ],

  FAQ: [
    "I'm always here to chat with you about how I can help build value in your network.",
    'Need to find an expert to help with a project? I can connect you with the right person.',
    "Let's see what I can do!",
  ],

  FAQ_HINTS: [
    'Fora, can you find me a UX designer?',
    'Will you send my boss flowers on her birthday?',
    "Will you watch my dog while I'm out of town?",
    'Can you read my emails and respond for me?',
    'Can I link my contacts now?',
  ],

  FAQ_KWD: ['find', 'send', 'watch', 'read', 'link'],

  FAQ_ANSWERS: [
    [
      "Happy to! I ask for details about your specific needs and search your contacts first, because it's great to work with people you already know.",
      'I can also look in my extended network, and facilitate introductions to trusted professionals you may not know directly yet.',
      // "You can click the 'Connect Me' button in the top right corner at any time to have me sync with your Google contacts.",
      'Want help growing and staying in touch with your network? See all the ways I can support you.',
    ],
    "I never do anything without your permission.  But I can remind you that her birthday is coming up, and recommend a florist that you've used in the past.",
    "I'm allergic to dogs myself but I'll remind you that you have a trip coming up and can connect you with a dog sitter in your network.",
    'I never read your messages but if you connect your email,  I can learn names and dates to better understand your network, make more relevant suggestions, and create drafts when you ask.',
    // "Absolutely.  Select 'Connect with Fora' from the bottom left menu.  I can't wait to help you manage your projects and strengthen your network!"
  ],

  HOW_TO_ANSWERS: ['Link', 'Tell me more'],

  HELP_DESC: 'Get help on working with Fora',

  HELP_MENU_TOP: [
    'What do you need help with? Here are a few things you can ask me to help with...',
    'Upload my contacts',
    'Find people based on skills',
    // 'Unlock AskFora professional',
    // 'Find freelance work',
    // 'Earn my Ready to Hire badge or become a Premier freelancer',
    // 'Find experts in my professional network',
    'Hire for a job', //' or find an expert to answer a question',
    'Complete and share my profile',
    'Something else?',
  ],

  HELP_MENU_ANSWERS: [
    'Upload contacts',
    'Find people',
    // 'Find work',
    // 'AskFora Professional',
    // 'Earn a badge',
    // 'Ask an expert',
    'Hire freelancer',
    'Edit profile',
    // 'Tutorial', 
    'Something else',
  ],

  HELP_MENU_OPTIONS: [
    {
      keywords: ['find work', 'find freelance work', 'get hired', 'get job', 'get a job'],
      prompt: ['Congrats! Freelancers enjoy a high level of flexibility and satisfaction with their work.', 'First, you want to make sure your profile is completed and up to date so clients can see that you’re an AskFora Professional.', 'Next, add a Service Offering to your profile to give clients an easy way to hire you for a specific job at a set rate.', `Don't forget to upload your contacts and share your profile link by Facebook, LinkedIn, Twitter, or by email. The more people who can see your profile and what you’re good at, the better your chances of being hired.`],
      answers: [
        // 'Get Ready to Hire',
        'AskFora Professional',
        'Offer serivces',
        'Upload Contacts',
        'Share my profile',
      ],
    },
    {
      keywords: ['find people', 'search', 'search people'],
      prompt: ['I can help you quickly find people in your professional network.', `Make sure you've uploaded your contacts.`, `Then just ask me to find the type of skills you're looking for.`],
      answers: [
        'Upload Contacts',
      ],
    },
    {
      keywords: ['earn a badge', 'earn my badge', 'earn', 'become', 'earn my ready', 
        'earn my ready hire', 'earn my ready hire badge', 'earn my ready to hire badge',
        'get ready to hire', 'get ready', 'get ready hire', 'professional', 'askfora professional',
      ],
      prompt: [ 'You can step up to AskFora Professional by filling in your profile with information about yourself, your work experience and education, and keywords that describe the skills you have.', 'Clients will see the AskFora Professional badge on your profile and will be confident that your information is up to date and you are available for work.', ],
      answers: [
        'Edit my profile',
        // 'Create Service Offering',
        'Offer services',
      ],
    },
    {
      keywords: [ 'become premier', 'become premier freelancer', 'become a premier freelancer', 'create service offering', 'create service', 'offer services', 'services', 'offer', 'offering', 'create offering', 'create an offering'],
      prompt: ['Premier freelancers find work on their own terms by creating Freelance Service Offerings to show clients what work they can do, how much time it will take, and what the pay rate is.', 'Only freelancers who have earned their AskFora Professional badge can take the next step to achieve Premier status.'],
      answers: [
        'Edit my profile',
        'AskFora Professional',
      ],
    },
    {
      keywords: ['hire freelancer', 'hire', 'hire a freelancer'],
    },
    {
      keywords: ['ask an expert', 'ask expert', 'expert request', 'expert search', 'expert question', 'expert help'],
    },
    {
      keywords: ['edit profile', 'profile', 'show profile', 'my profile', 'share profile', 'edit my profile', 'share my profile', 'show my profile'],
      help_topic: true,
    },
    {
      keywords: ['upload', 'upload contacts'],
    },
    {
      keywords: ['something else'],
      help_topic: true,
    }
  ],
};
