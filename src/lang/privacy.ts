import fs from 'fs';
import path from 'path';
import * as funcs from '../utils/funcs';

export default {
  PRIVACY_RESERVED: ['privacy'],

  PRIVACY_SHORTCUTS: a => {
    if (!a) return { name: 'Privacy', value: 'Privacy' };
    else return null;
  },

  PRIVACY_KWDS: ['show me the privacy', "what's your privacy", 'what is your privacy', 'privacy & security'],

  PRIVACY_POLICY: fs
    .readFileSync(path.resolve(__dirname, '..', 'files', 'privacy.txt'))
    .toString()
    .split('\n')
    .filter(l => l.length),
};
