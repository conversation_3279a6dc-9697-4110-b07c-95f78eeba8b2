import fs from 'fs';
import path from 'path';

import { TemplateType } from '../types/globals';
import { Person, Project } from '../types/items';
import { NotificationType, PersonInfo, ProjectCandidateState, ProjectInfo, ProjectRate, Reply } from '../types/shared';

import { stringList } from '../utils/format';
import { promptInfo } from '../utils/info';

import about from './about';

function projectEstimate(proj: Project, as_fee = false) {
  let rate = '';
  let amount = proj.fee ? proj.fee : null;
  let comp = !proj.group_settings || ! proj.group_settings.skip_payment ? ' and there is no compensation' : '';
  let estimate = null;
  const compensation = as_fee ? 'fee' : 'compensation';
  switch (proj.rate) {
    case ProjectRate.hourly:
      rate = 'hours';
      amount *= proj.duration;
      if (amount) comp = ` and the ${compensation} is up to $${amount.toLocaleString()}`;
      estimate = `It will take about ${proj.duration} ${rate}${comp}`;
      break;
    case ProjectRate.daily:
      rate = 'days';
      amount *= proj.duration;
      if (amount) comp = ` and the ${compensation} is up to $${amount.toLocaleString()}`;
      estimate = `It will take about ${proj.duration} ${rate}${comp}`;
      break;
    case ProjectRate.fixed:
      rate = 'hours';
      if (amount) estimate = `It will be a fixed fee of $${amount.toLocaleString()}`;
      else estimate = !proj.group_settings || ! proj.group_settings.skip_payment ? `There is no ${compensation} for this job` : '';
    case ProjectRate.sourcing:
      estimate = ''
      break;
  }

  return estimate;
}


function  depositAmount(proj: Project | ProjectInfo, progress = false) {
  let amount = proj.fee;
  switch (proj.rate) {
    case ProjectRate.hourly:
      amount *= progress ? proj.progress : proj.duration;
      break;
    case ProjectRate.daily:
      amount *= progress ? proj.progress : proj.duration;
      break;
    case ProjectRate.fixed:
    case ProjectRate.sourcing:
      break;
  }

  return amount;
}

function serviceFee(proj: Project | ProjectInfo) {
  const amount = depositAmount(proj);
  return amount * proj.service_fee;
}

const SETUP_STRIPE_PROMPT = (n, r) => promptInfo(`I've asked ${n} to deposit the job fee with me, \
which I'll hold on to until you're finished working. \
${r ? 'I\'ll let you know when your job is ready to start.' : 'In order for me to take a deposit, you will need to set up a payment account, \
which you can do by asking to \'Link Payment\''}`);

export default {
  PROJECT_NAME: 'Project Create',

  PROJECT_DESC: 'Create a job with someone your know',


  PROJECT_EG:  ['Need some help with a job? Ask me to "Create a job for UX design"'],

  PROJECT_RESERVED: [
    'project [with someone]',
    'create project [with someone]',
    'start project [with someone]',
    'send project [to someone]',
    'job [with someone]',
    'create job [with someone]',
    'start job [with someone]',
    'send job [to someone]',
    'jobs',
    'new project',
    'new job',
    'repeat job',
    'repeat project',
    'expert request',
    'new freelancing search',
    'new job from template',
    'start project profile',
    'create a sourcing job',
    'create a new template',
    'hiring for a new job',
    'create a new project',
    'start a new project',
    'new sourcing search',
    'starting a new job',
    'send a new project',
    'hire a freelancer',
    'freelancer search',
    'create a template',
    'create a sourcing',
    'freelance service',
    'hiring for a job',
    'freelance search',
    'create a project',
    'create a new job',
    'start a project',
    'start a new job',
    'hire freelancer',
    'create template',
    'create sourcing',
    'create proposal',
    'starting a job',
    'send a project',
    'send a new job',
    'post a new job',
    'hire for a job',
    'create project',
    'start project',
    'send project',
    'new sourcing',
    'hiring a job',
    'hire for job',
    'create a job',
    'start a job',
    'new project',
    'send a job',
    'post a job',
    'hire a job',
    'freelancer',
    'create job',
    'freelance',
    'start job',
    'post job', 
    'send job',
    'new search',
    'new job',
    //'search',
    'hiring',
    'hire',
    'job',
  ],

  PROJECT_KWD: [
    'new freelancing search',
    'new job from template',
    'start project profile',
    'create a sourcing job',
    'create a new template',
    'hiring for a new job',
    'create a new project',
    'start a new project',
    'new sourcing search',
    'starting a new job',
    'send a new project',
    'hire a freelancer',
    'freelancer search',
    'create a template',
    'create a sourcing',
    'freelance service',
    'hiring for a job',
    'freelance search',
    'create a project',
    'create a new job',
    'start a project',
    'start a new job',
    'hire freelancer',
    'create template',
    'create sourcing',
    'create proposal',
    'starting a job',
    'send a project',
    'send a new job',
    'post a new job',
    'hire for a job',
    'create project',
    'start project',
    'send project',
    'new sourcing',
    'hiring a job',
    'hire for job',
    'create a job',
    'start a job',
    'new project',
    'new search',
    'send a job',
    'post a job',
    'hire a job',
    'freelancer',
    'create job',
    'freelance',
    'start job',
    'post job', 
    'send job',
    'new job',
    //'search',
    'hiring',
    'hire',
    'job',
  ],

  PROJECT_CLIENT: 'project client',

  PROJECT_REPEAT: [
    'repeat job',
    'repeat project',
  ],

  PROJECT_PROPOSE_TEMPLATE: ['propose template'],

  FROM_PROFILE: 'profile',

  PROJECT_SHORTCUT: a => {
    if (a) return { name: 'Hire for a job', value: 'Hire for a job' };
    else return null;
  },

  EXPERT_SHORTCUT: a => {
    if (a) return { name: 'Ask an expert', value: 'Ask an expert' };
    else return null;
  },

  PROJECT_CONTRACTOR: ['contractor project'],

  PROJECT_CONTRACTOR_PROMPT: 'Do you want to create a job proposal or a service offering?',

  PROJECT_CREATE: ['create project', 'new freelancing search'],

  PROJECT_SOURCING_CREATE: ['create sourcing', 'new sourcing', 'new sourcing search'],

  PROJECT_PROPOSE: [ 
    'propose project', 
    'get hired', 
    'propose job', 
    'new propoal', 
    'new job proposal', 
    'propose new job', 
    'propose new project', 
    'job proposal', 
    'proposal', 
    'create proposal' ,
    'propose a job',
  ],

  PROPOSE_CONTEXT: [
    'propose',
  ],

  PROJECT_TEMPLATE: ['create template', 'service template', 'create offering', 
    'create freelance offering', 'create freelance service offering', 'new service offering', 
    'service offering', 'offering', 'freelance service', 'freelance', 'service'],

  PROJECT_CONTRACTOR_HINTS: ['Job Proposal', 'Service Offering'],

  PROJECT_SHARE_PROPOSAL: 'share proposal',

  PROJECT_SHARE_QUESTION: 'share question',

  PROJECT_ACCEPT_PROPOSAL: ['accept proposal'],

  PROJECT_EXPERT: [
    'find expert', 
    'find an expert', 
    'find a expert', 
    'expert question',
    'expert request', 
    'expert advice',
    'expert search', 
    'expert help',
    'look for an expert',
    'looking for an expert',
    'ask an expert',
    'ask expert',
    'question for an expert',
    'question an expert',
    'question expert',
    'send question',
    'send a question',
    'ask question',
    'ask a question',
  ],

  PROJECT_IGNORE_FOUND_ENTITIES:[
    'expert', 
    'request', 
    'find', 
    'job', 
    'project', 
    'start', 
    'starting',
    'look', 
    'looking', 
    'search', 
    'freelance',
    'freelancer',
    'freelancing',
    'advice', 
    'question', 
    'create', 
    'new',
    'hire', 
    'hiring', 
    'searching',
    'post',
    'send',
    'template',
  ],

  PROJECT_JOB_EXPERT_PROMPT: 'Are you hiring a freelancer or looking for expert advice?',

  PROJECT_JOB_EXPERT_ANSWERS: ['Freelancer search', 'Expert advice'],

  PROJECT_EXPERT_CHOICE: ['expert', 'help', 'advice'],

  PROJECT_JOB_CHOICE: ['freelance', 'freelancer', 'job', 'hiring', 'search'],

  PROJECT_ACCEPT: ['accept'],

  PROJECT_RECOMMEND: ['recommend'],

  PROJECT_DECLINE: ['decline'],

  PROJECT_PAY: ['pay ', 'send payment'],

  PROJECT_REQUEST_PAY: ['request payment'],

  PROJECT_PROGRESS: ['project progress'],

  PROJECT_COMPLETE: ['project complete'],

  PROJECT_SUBMIT: ['submit project'],

  PROJECT_START: ['project start'],

  PROJECT_SUFFIX: ['create', 'project', 'job', 'fixed', 'fee'],

  // add/select candidate actions
  PROJECT_SEARCH: 'search candidates',
  PROJECT_ADD: 'candidate add',
  PROJECT_SELECT: 'select candidate',
  PROJECT_UNSELECT: 'unselect contractor',
  PROJECT_REJECT: 'reject candidate',

  CONTEXT: ['for', 'about', 'on', 'profile'],

  PROJECT_SAVE: ['save project', 'close project', 'open project'],

  PROJECT_CLOSE: ['close'],

  CMD_PROJECT_CLOSE: 'close',

  PROJECT_OPEN: ['open'],

  CMD_PROJECT_OPEN: 'open',

  CMD_PROJECT_SAVE: 'save',

  PROJECT_SEND: 'project send',

  PROJECT_DRAFT: 'project draft',

  CANDIDATES_SEND: 'send',

  CANDIDATES_DRAFT: 'draft',

  // SUGGEST_SKILLS: ['Migrate a blog', 'UX design', 'Crypto expert', 'Cyber consultant'],
  // ['Content marketing', 'UX design', 'Social media strategy', 'SEO growth'], 
  // SUGGEST_SKILLS: ['Tutoring', 'UX design', 'Social media', 'Editing', 'Something else'],
  SUGGEST_SKILLS: ['Consulting services', 'Lead generation', 'Coaching', 'Social media', 'Design', 'Development', 'Tutoring', 'Something else'],

  SUGGEST_SKILLS_ELSE: ['something', 'else', 'something else'],

  SKILLS_ELSE_PROMPT: 'Okay, tell me what job you need help with',

  SUGGEST_EXPERT: ['Project Manager', 'Statistician', 'International Relations'],

  DRAFT_PROMPT: u => {
    return [
      promptInfo("Success! I've drafted an e-mail that you can", u, 'edit and send'),
      promptInfo("I'll e-mail you as candidates express interest and you can view the current status by going to the Select step. "),
    ];
  },

  SEND_PROMPT: (network: boolean) => [
    `Success! I've sent an e-mail invitation to your job${network ? '' : ' and cc\'d you'}.`,
    "I'll e-mail you as candidates express interest and you can view the current status by going to the Select step.",
  ],

  EXPERT_PROMPT: (network: boolean) => [
    `Success! I've sent your question${network ? '' : ' and cc\'d you'}.`,
    "I'll email you when you get an answer."
  ],

  SEND_PROPOSAL_PROMPT: (client: Partial<PersonInfo>) => [
    `All set. I've sent an e-mail with a link to your job proposal${client.network ? '' : ' and cc\'d you'}.`,
    `I'll e-mail you about next steps once ${client.nick} has reviewed the job`,
  ],

  PROPOSAL_REPLIED_PROMPT: `Your job proposal has been replied to.`,
  PROPOSAL_ACCEPTED_PROMPT: (client: string) => `${client} has accepted your proposal. I'll let you know when your job is ready to start.`,
  PROPOSAL_DECLINED_PROMPT: (client: string) => `${client} has declined your proposal.`,

  REQUIRES_EMAIL: u => {
    return promptInfo('I can prepare your drafts, I just need email support enabled. You can do that', u, 'here');
  },

  NDA: (client, contractor) =>
    [
      `${contractor.split(' ')[0]}, thank you for your interest in this job. I'll show you the details if you agree to keep them confidential. Specifically...`,
      // 'To see the job details, you must agree to keep it confidential. Specifically...',
      `${client} ("Client") is sharing a proposal (the "Job") with you, ${contractor} ("Contractor"), for certain services. While evaluating the Job, you may both exchange information, including the nature of the Job. This information is shared under the following terms:`,
    ].concat(
      fs
        .readFileSync(path.resolve(__dirname, '..', 'files', 'nda.txt'))
        .toString()
        .split('\n')
        .filter(l => l.length),
    ),

  REPEAT_NDA: "I'm sorry, I didn't understand. Do you agree to these terms to keep the job details confidential?",

  NDA_ANSWERS: ['Agree', 'No thank you'],

  AUTH_WELCOME: 'Welcome to AskFora, a way to do better work with people you know. You got here from a link to an AskFora job.',

  EXPERT_WELCOME: 'Welcome to AskFora, a way to do better work with people you know. You got here from a link to an AskFora question.',

  AUTH: p => promptInfo(`To see the job details, you need to sign in with a ${stringList(p, 'or')} account and agree to keep the details confidential.${p.length > 1 ? ' Which do you want to use?' : ''}`),

  EXPERT_AUTH: p => promptInfo(`To answer this question, please sign in with a ${stringList(p, 'or')} account.${p.length > 1 ? ' Which do you want to use?' : ''}`),

  AUTH_CANCELLED: auth => {
    return [
      promptInfo(`AskFora uses ${auth.name} accounts for authentication.`),
      promptInfo(`I wasn't able to authenticate to ${auth.name} because you didn't approve my permissions.`),
      promptInfo('For details about this job please sign in and approve permissions with your', auth.url, `${auth.name} account`, [], true),
    ];
  },

  MISSING: "I couldn't find the job you're looking for. Try the link in your email invitation again.",

  TIMEOUT: "Whoops, I lost track of time! You can keep working, I'm back with you now.",

  INVALID: "There's a problem with this job. I'm going to need to ask someone to help me fix it. I'll let you know when it's sorted.",

  PROJECT_EXPIRED: 'Someone else may have been selected for this job, or it has been removed by the client.',

  PROPOSE_CLIENT: 'Who will you be working with for this job?',

  PROPOSE_CLIENT_NOT_FOUND: c => [`Sorry, I can't find ${c.split(' ')[0]} in your contacts. I can only send job proposals to your first connections.`, 'Who will you be working with?'],

  SKILLS_PROMPT: ['I can find people in your network to help with anything  you need.', 'What job do you need help with?'],

  SKILLS_HINT: "You can ask for any number of skills, like 'UX design' or a task like 'drafting  whitepapers'",

  EXPERT_SKILLS_PROMPT: 'What type of expert are you looking for?',

  EXPERT_SKILLS_HINT: "You can ask for a skill like 'design' or a profession or role like 'designer'",

  PERSON_SKILLS_PROMPT: (p: Partial<Person>) => `Happy to help you start a job with ${p.nickName}. What should I call the job?`,

  TEMPLATE_SKILLS_PROMPT: `Happy to help you create a new freelance service offering. What service is this for?`,

  PROPOSAL_SKILLS_PROMPT: (p: Partial<Person>) => `Happy to help you create a proposal for ${p.nickName}. What should I call the job?`,

  CREATE_PROMPT_NEVERMIND: 'Nevermind',

  LEARN_MORE: 'Learn More',

  LEARN_MORE_PROMPT: [
    promptInfo(`AskFora helps you find people in your professional network for short term jobs. It's like having your own personal talent marketplace. You create a job description and I search your contacts for candidates.`),
    promptInfo(`One of your connections sent you this link because they think you'd be a good fit or might know someone who can help. You can learn more about AskFora`, '/about', 'here'),
  ],

    // "Okay, let's write a job description and I'll find candidates from your network.",
    // "You choose who you want to work with. I'll handle the contract and let you pay them by credit card.",
  PROPOSAL_GOALS_PROMPT: 'Can you briefly describe your service?',

  GOALS_PROMPT: s => `Can you tell me briefly about your ${s} job?`,

  SOURCING_GOALS_PROMPT: s => `Can you briefly describe the ${s} position?`,

  SOURCING_GOALS_HINT: 'Describe the key responsibilities. You can edit this later.',

  PROPOSAL_GOALS_HINT: "Describe the proficiencies, tools, or experience you're offering...",

  GOALS_HINT: "Describe your goals and any proficiencies, tools, or experience you're looking for...",

  EXPERT_GOALS_PROMPT: ['What advice are you looking for or what question do you need answered?', "You'll be able to edit this later"],

  EXPERT_GOALS_HINT: "You can go into as much detail as you'd like",

  GOALS_TEMPLATE: n => `${n}

Additional information you may want to add:
Requirements: Skills, tools, experience, education
Deliverable: When is this job considered complete?
Addtional Notes: Details about you, your organizations, the job
`,

  RATE_PROMPT: (p, s) => [ `Will you ${p ? 'charge' : 'pay' } an hourly or daily rate, ${s ? 'a fixed amount, or is this a full time job' : 'or a fixed amount'}? If you're not sure, go with hourly for now.`,
    `For hourly and daily rates, you can set a maximum number and ${p ? 'charge' : 'pay' } only for the time worked.`] , 
  
  RATE_NO_PAY: s => [
    `Do you want to track your job by hour, day, ${s ? 'for a fixed time, or full or part time': 'or is it a fixed time'}? If you're not sure, go with hourly for now.`,
  ],

  RATE_ANSWERS: s => ['Hourly', 'Daily', 'Fixed Fee'].concat(s ? ['Full Time', 'Part Time', 'Contract'] : []), // , 'Not sure yet'],

  RATE_FIXED_TIME: s => ['Hourly', 'Daily', 'Fixed Time'].concat(s ? ['Full Time', 'Part Time', 'Contract'] : []), // , 'Not sure yet'],

  RATE_HINT: "If you're not sure, say 'hourly'",

  // PROJECT_RATES: ['hourly', 'daily', 'fixed', 'fixed fee', 'fixed amount', 'fixed time', 'full', 'full time', 'sourcing'],

  PROJECT_RATE_MAP: {
    '/hour': ProjectRate.hourly,
    '/hours': ProjectRate.hourly,
    '/hrs': ProjectRate.hourly,
    '/hr': ProjectRate.hourly,
    hour: ProjectRate.hourly,
    hours: ProjectRate.hourly,
    hr: ProjectRate.hourly,
    'hrs ': ProjectRate.hourly,
    hourly: ProjectRate.hourly,
    days: ProjectRate.daily,
    day: ProjectRate.daily,
    '/day': ProjectRate.daily,
    '/days': ProjectRate.daily,
    daily: ProjectRate.daily,
    fixed: ProjectRate.fixed,
    'fixed fee': ProjectRate.fixed,
    'fixed amount': ProjectRate.fixed,
    'fixed time': ProjectRate.fixed,
    total: ProjectRate.fixed,
    contract: ProjectRate.sourcing,
    contracting: ProjectRate.sourcing,
    full: ProjectRate.sourcing,
    part: ProjectRate.sourcing,
    'full time': ProjectRate.sourcing,
    'part time': ProjectRate.sourcing,
    'sourcing': ProjectRate.sourcing,
  },

  PROJECT_RATE_KWDS: {
    'hourly': ['hour', 'hours', 'hourly', 'hr', 'hrs'],
    'daily': ['day', 'daily', 'days'],
    'fixed': ['hour', 'hours', 'hourly', 'hr', 'hrs', 'day', 'daily', 'days', 'week', 'weeks', 'weekly', 'month', 'months', 'monthy', 'year', 'years', 'yearly', 'fixed', 'per'],
    'sourcing': ['sourcing'],
  },

  PROPOSAL_FEE_PROMPT:  r => {
    return `What is your ${r} fee?`;
  },

  FEE_PROMPT: r => {
    return `What is the ${r} fee?`;
  },

  // FEE_HINT: 'You can change this later',
  FEE_HINT: '$ xx',

  FEE_UNKNOWN: r => {
    return `Okay, you can always update it later. For now we will use $${r}.`;
  },

  DURATION_PROMPT: r => {
    switch (r) {
      default:
      case 'hourly':
        return 'And how many hours do you expect this to take?';
      case 'daily':
        return 'And how many days do you expect this to take?';
    }
  },

  DURATION_HINT: 'Enter a number, you can change this later',

  DURATION_ANSWERS: ['Skip'],

  DURATION_UNKNOWN: (r, d) => {
    let p = '';
    switch(r) {
      case 'hourly': p = ' hour'; break;
      case 'daily': p = ' day'; break;
    }
    return `Okay, you can always update the duration later. For now we will use ${d}${p}.`;
  },

  CONFIRM: s => {
    return `Hold on, here's what I've got for your ${s} needs`;
  },

  DESCRIPTION_PROMPT: [
    // 'Does this look right? You can add or delete keywords under Skills to refine my search.',
    "Adjust details like rate of pay and duration, then include the description or other information below before tapping 'Find Candidates.'",
  ],

  SEARCH_STEPS: [
    'Does this look right? You can add or delete keywords under Skills to refine my search.',
    "Go ahead and edit the job description and add any requirements, deliverables, or background for the job. I'll start looking when you tap 'Find Candidates.'",
    // 'Okay, here\'s what I heard. Does this look right? You can make changes directly.',
    // 'The more information you share, the greater your chances of finding someone.',
  ],

  EXPERT_STEPS: [
    // 'Does this look right? You can add or delete keywords under Expertise to refine my search.',
    // "Go ahead and edit your expertise or question. I'll start looking when you tap 'Find Experts.'",
    `Here's your question. Tap Send Question and I'll share it with the experts from your search.`,
    `You can also add or remove experts, and post or share a link to this question.`,
  ],

  EXPERT_ASK: [
    ""
  ],

  SEARCH_ADDED: [
    `I've added some candidates to your job.`,
    'You can go back and edit the description before sending invitations',
  ],

  TEMPLATE_LINK: promptInfo(`You can always find your service offerings under the`, '/app/jobs', 'freelance menu'),

  TEMPLATE_STEPS: [
    promptInfo(`Here's a service offering you can edit. When you set it to public, people in your network will be able to send you job requests based on this offering.`),
    promptInfo('If your service has specific requirements or deliverables, add those to this offering or provide guidance for your clients.'),
    promptInfo(`You can always find your service offerings under the`, '/app/jobs', 'freelance menu')
  ],

  PROPOSAL_STEPS: c => [
    `I've prepared your proposal for ${c.name} and I'm ready to send a link.`,
    'If your job has specific requirements or deliverables, add those to this proposal or provide guidance for your clients.',
  ],

  SEARCH_INVITE: c => [
    `I've added ${c.name} to your job and I'm ready to send an invite.`,
    'You can edit the description anytime before starting the job.',
  ],

  SEARCH_INVITE_ANSWERS: ['Send Invite', 'Not yet'],

  SEND_LATER: ['No worries. You can send an invite from the Find Candidates page or send a reminder on the Select Candidate page at any time.', 'You can also share this job from the share link on the top right.'],

  SEND_LATER_PROPOSAL: ['No worries. You can send an invite from the Review Client at any time.', 'You can also share this job from the share link on the top right.'],

  SEARCH_CONNECT_CONTACTS: (p, l) => {
    return promptInfo(`Before I start my search, to make sure I can find you the best candidates, link your ${p} contacts`, l, 'here', null, true);
  },

  SEARCH_CONNECT_CONTACTS_EMAIL: 'Before I start my search, to make sure I can find you the best candidates, link an account or import your contacts',

  CANDIDATE_ADDED: (p: Partial<Person>[]) => {
    switch (p.length) {
      case 0: return `Ok, I didn't add anyone to your job`;
      case 1: return `Ok, I've added ${p[0].displayName} to the candidates for your job.`;
      case 2: return `Ok, I've added ${p[0].displayName} and ${p[1].displayName} to the candidates for your job.`;
      default: return `Ok, I've added ${p.length} candidates for your job.`;
    }
  }, 

  FIRST_PROJECT: p => {
    return [
      // promptInfo("Check that I got everything right and edit the Skills to help me refine my search."), // then I'll start looking for people you know."),
      // promptInfo('You can add or delete keywords in the Skills section to help me refine my search.'),
      promptInfo(`I'll start looking for people you know as soon as you sign in with ${stringList(p, 'or')}.${p.length > 1 ? ' Which do you want to use?' : ''}`),
      promptInfo('I need access to your contacts, calendar, and tasks to search your network and manage your job. I never send emails or delete data without your express permission.'),
    ];
  },

  FIRST_PROJECT_PROFILE: (p, n) => {
    return [
      promptInfo(`I can start your job with ${n} as soon as you sign in with ${stringList(p, 'or')}.${p.length > 1 ? ' Which do you want to use?' : ''}`),
      promptInfo('I need access to your contacts, calendar, and tasks to search your network and manage your job. I never send emails or delete data without your express permission.'),
    ]
  },

  FIRST_PROJECT_EXPERT: p => {
    return [
      promptInfo(`I'll help you find an expert after you sign in with ${stringList(p, 'or')}.${p.length > 1 ? ' Which do you want to use?' : ''}`),
      promptInfo('I need access to your contacts, calendar, and tasks to search your network and manage your request. I never send emails or delete data without your express permission.'),
    ]
  },

  FIRST_SEARCH: [
    'Congratulations on your first job request and welcome on board!',
    "I'm gathering information now and then I'll start searching for candidates. This may take a few minutes and I'll email you when I'm done. Feel free to continue filling out the job description.",
    "You can also add people by selecting 'Add Candidate', ask me to 'Import' from LinkedIn, Facebook, or iCloud, or share the job link directly.",
    // 'With more details, I\'ll be able to find a better match. When you\'re ready, tap \'Find Candidates\' and I\'ll start my search.',
    // 'I\'m searching people you know and I\'ll tell you when I\'ve found folks who may be a fit or who could refer someone. This could take a few minutes.',
    // 'You can also add people directly from the \'Find Candidates\' step.',
    // 'You may want to turn on notifications. You can manage them from the Settings menu.',
    // 'While, I\'m searching, if you want to know what else I can do, just ask \'what can you do?\'',
  ],

  SEARCHING: ["I'm searching people you know and I'll email you when I've found folks who may be a fit or who could refer someone.", "You can keep using AskFora or close this page. I'll include a link in my follow up email."], // "You can also add people by selecting 'Add Candidate' or ask me to 'Import' from LinkedIn, Facebook, or iCloud."],

  EXPERT_SEARCHING: ["I'm searching people you know and I'll email you when I've found folks who may be able to answer your question.", "You can keep using AskFora or close this page. I'll include a link in my follow up email."], // "You can also ask me to 'Import' contacts ffrom LinkedIn, Facebook, or iCloud and expand your search."],

  SEARCH_FOUND: [
    'I found some people who might be a fit or who could refer someone.',
    // 'Double check the job description then pick one or more candidates to contact about this job.',
    'Pick who you want to contact about this job. You can invite candidates or ask them for a referral.',
    "You can also add someone by selecting 'Add Candidate' or expand your search by asking me to 'Import' from LinkedIn, Facebook, or iCloud, or share the job link directly.",
  ],

  EXPERT_FOUND: [
    'I found some experts who might be able to answer your question.',
    "Pick who you'd like to share your question with and I'll send them the request.",
    "You can expand your search by asking me to 'Import' from LinkedIn, Facebook, or iCloud.",
  ],

  SEARCH_NONE: c => [
    "Sorry I didn't find anyone in your network who was a good fit.",
    `You can ${c ? 'Link your contacts or' : ''} add LinkedIn or Facebook contacts to expand your search or share the job link directly.`,
    'Try refining your job description by adding or deleting keywords in the Skills section and searching again.',
    // 'You can try different keywords in the Skills section and I\'ll search again',
    // 'You can also manually add someone to contact about this job or to ask for a referral.',
  ],

  EXPERT_NONE: c => [
    "Sorry I didn't find anyone in your contacts who was a good fit.",
    `You can ${c ? 'Link your contacts or' : ''} add LinkedIn or Facebook contacts to expand your search or share the question link directly.`,
    'Try refining your question by adding or deleting keywords in the Expertise section and searching again.',
  ],

  FOUND_SUBJECT: (proj: Project) => {
    return `AskFora ${proj.title} job candidates`;
  },

  FOUND_MESSAGE: (proj: Project, new_count, url: string, iurl: string) => {
    return `${proj.client.nickName},

I found ${new_count} candidate${new_count === 1 ? '' : 's'} for your ${proj.title} job. \
Let me know who you want to contact and I can draft an e-mail for you or contact them on your behalf. \
Pick who you want to invite to your job here: ${url}

You can also add LinkedIn or Facebook contacts to expand your search here: ${iurl}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  NOT_FOUND_SUBJECT: (proj: Project) => {
    return `AskFora ${proj.title} job update`;
  },

  NOT_FOUND_MESSAGE: (proj: Project, url: string, iurl: string) => {
    return `${proj.client.nickName},

I wasn't able to find any candidates for your ${proj.title} job. \
You can add specific contacts or refine the skills you're looking for and ask me to search again. When you find the candidates you want to contact, let me know and I can draft an e-mail for your or contact them on your behalf. \
Go ahead and update your job here: ${url}

You can also add LinkedIn or Facebook contacts to expand your search here: ${iurl}

${about.ABOUT_EMAIL_SIGNATURE}`;

  },

  INVITE_ERROR_STARTED: 'I can\'t invite new candidates to a job that you already started',

  INVITE_SUBJECT: (proj: Project) => {
    return `AskFora ${proj.title} job from ${proj.client.displayName}`;
  },

  INVITE_MESSAGE: (proj: Project, person: Partial<Person>, message: string, start: string, auths, url: string) => {
    const estimate = projectEstimate(proj);
    const referral = !proj.group_settings || !proj.group_settings.no_referrals ? ' or could you recommend someone' : '';

    if (!message) {
      message = `I'm helping ${proj.client.displayName} find someone for a short term job who has these skills and experience: ${proj.skills}. \
${estimate}. ${proj.client.displayName} would like to find someone by ${start}. Are you interested${referral}?`;
    }

    return `${person.nickName},

${message}

You can see details here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  REFER_SUBJECT: (proj: Project, person: Partial<Person>) => {
    return `AskFora ${proj.title} job recommendation from ${person.displayName}`;
  },

  REFER_MESSAGE: (proj: Project, rec_from: Partial<Person>, person: Partial<Person>, start: string, url: string) => {
    return `${person.displayName}, 

Your connection, ${rec_from.displayName}, thinks you'd be a good fit for a short term ${proj.title} job.

${projectEstimate(proj)}

The client offering this job would like to find someone by ${start}.

You can see details here: ${url}

Are you new to AskFora? Create a profile with a personal link [https://askfora.com] to share with clients so you can do better work with people you know.

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  EXPERT_SUBJECT: (proj: Project) => {
    return `AskFora ${proj.title} question from ${proj.client.displayName}`;
  },

  EXPERT_MESSAGE:  (proj: Project, person: Partial<Person>, message: string, start: string, auths, url: string) => {
    return `${person.nickName},

I'm helping ${proj.client.displayName} find someone knowledgable about ${proj.skills} to answer this question: 

"${proj.notes}"

If you can help or know someone who can, click here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;

  },

  INVITE_PROPOSAL_SUBJECT: (proj: Project) => {
    return `AskFora job proposal for ${proj.skills} from ${proj.contractor.displayName}`;
  },

  INVITE_PROPOSAL_MESSAGE: (proj: Project, person: Partial<Person>, message: string, start: string, auths, url: string) => {
    const estimate = projectEstimate(proj, true);
    if (!message) {
      message = `I'm helping ${proj.contractor.displayName} prepare a job proposal that requires these skills and experience: ${proj.skills}. \
${estimate}. ${proj.contractor.displayName} has proposed a start date of ${start}.`;
    }

    return `${person.nickName},

${message}

You can see details here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  DRAFT_MESSAGE: (proj: Project, person: Partial<Person>, message: string, start: string, auths, url: string) => {
    const estimate = projectEstimate(proj);
    const referral = !proj.group_settings || !proj.group_settings.no_referrals ? ' or could you recommend someone' : '';

    if (!message) {
      message = `I'm working with Fora (cc'd) to find someone for a short term job who has these skills and experience: ${proj.skills}. \
${estimate}. I would like to find someone by ${start}. Are you interested${referral}?`;
    }

    return `${person.nickName},

${message}

You can see details here: ${url}

-${proj.client.nickName}

Learn more about AskFora: https://askfora.com/about`;
  },

  NOTIFY_STATES: [
    // ProjectCandidateState.VIEWED, // don't notify until we show in the UI
    ProjectCandidateState.ACCEPTED,
    ProjectCandidateState.SELECTED,
    ProjectCandidateState.SUBMITTED,
    ProjectCandidateState.PAYMENT_REQUESTED,
  ],

  STATE_FROM_SELECTED: [
    ProjectCandidateState.SELECTED,
    ProjectCandidateState.SUBMITTED,
    ProjectCandidateState.PAYMENT_REQUESTED,
  ],

  INTERESTED: (proj: ProjectInfo) => {
    return [`Thanks ${proj.me_candidate.nick} for taking a look at this job.`, `You can review the job details, and let ${proj.client.nick} know if you're interested.`];
  },

  EXPERT_VIEW: (proj: ProjectInfo) => {
    return [`Thanks ${proj.me_candidate.nick} for taking a look at this question.`, `You can answer ${proj.client.nick} directly.`];
  },

  MISSING_EMAIL: `I can't add people without an e-mail address.`,

  MISSING_CANDIDATE: `I couldn't find anyone among your first connections. Make sure you capitalize names or include a valid email address.`,

  CONFIRM_SELECT: c => {
    return `Do you want to select ${c.nickName} for this job?`;
  },

  ERROR_SELECT: c => {
    if (c) return `You can't select two people for the same job. You'll need to unselect ${c.nickName} first.`;
    else return "I can't select this candidate. It looks like there may be a problem with this job.";
  },

  SELECT_ANSWERS: ['Yes', 'No'],

  SELECTED_PROMPT: (project: ProjectInfo, new_contract) => {
    const prompt = []; // funcs.promptInfo(`${project.contractor.nick} will be excited to hear it. I'll send a note with instructions for next steps.`)];

    if (new_contract) {
      prompt.push(promptInfo(`Great! I'll email ${project.contractor.nick} with the standard service agreement, which you'll countersign. You can see a copy`, '/contract.pdf', 'here'));
    } else if (project.fee === 0 || project.contractor.ready) {
      const service_fee = serviceFee(project);
      const fee = service_fee > 0 ? ` and a fee of $${service_fee.toLocaleString()}` : '';
      let deposit = `deposit $${depositAmount(project).toLocaleString()}${fee} with a credit card`;
      if (project.escrow) deposit = 'get started';

      prompt.push(promptInfo(`To get started, go to 'Track' and tap 'Start' to ${deposit}`));
      if (!project.escrow) prompt.push(promptInfo("I'll hold on to your payment until the job is complete and you release the funds."));
    } else {
      prompt.push(
        promptInfo(
          `Great! I'll let you know when ${project.contractor.nick} has set up a payment account, then to get started you can go to 'Track' and tap 'Start' to deposit funds with a credit card.`,
        ),
      );
    }

    return prompt;
  },

  CLIENT_START_PROMPT: (ask_deposit: boolean, amount: number, service_fee: number) => {
    const fee = service_fee > 0 ? ` and a fee of $${service_fee.toLocaleString()}` : '';
    let deposit = `deposit $${amount} and a fee of $${fee} with a credit card`;
    if (!ask_deposit) deposit = 'get started';

    const prompt: Reply[] = [];
    prompt.push(promptInfo(`Great! To get started, go to 'Track' and tap 'Start' to ${deposit}`));
    if (ask_deposit) prompt.push(promptInfo("I'll hold on to your payment until the job is complete and you release the funds."));
    return prompt;
  },

  CLIENT_WAIT_READY: n => {
    return promptInfo(`${n} needs to set up a payment account before you can deposit funds and start the job. I sent an email reminder and will let you know when I’m ready to accept your deposit.`);
  },

  UNSELECT_SUCCESS: 'All set. Go ahead and select someone else for your job.',

  UNSELECT_ERROR: c => `You can't change contractors once the job is started. You should let ${c} know and then finish the job with the agreed upon progress.`,

  REJECT_SUCCESS: "Ok, I've removed this candidate and made a note.",

  SELECTED_CONTRACTOR_PROGRESS: 'Track your progress and submit your job when completed.',

  SELECTED_CONTRACTOR_COMPLETE: u => `Tap to request your payment. I'll check with ${u} to confirm that everything looks good and to release your payment.`,

  SELECTED_CONTRACTOR_PAYMENT: `I've released your funds. If you haven't yet, you will receive payment in the next 3 to 5 business days. Don't forget, I don't charge freelancers a fee for using AskFora. Client cover all the transaction costs.`,

  SELECTED_CONTRACTOR: (project: ProjectInfo) => promptInfo(`Congratulations! ${project.client.nick} wants you to help with this ${project.title} job.`),

  SELECTED_CONTRACTOR_SETUP_STRIPE: (n, r) => SETUP_STRIPE_PROMPT(n, r),

  SELECTED_CONTRACTOR_SETUP_CONTRACT: (project: ProjectInfo, contract_url: string) => {
    return promptInfo("Here's the standard service agreement for you to", contract_url, 'sign', [], true);
  }, 

  /* SELECTED_CONTRACTOR_PREPARE_CONTRACT: (project: ProjectInfo, contract_url: string) => {
    return promptInfo("Here's the standard service agreement for you to", contract_url, 'sign', [], true);
    // prompt.push(promptInfo('You should be getting an email with a link to a contract to sign, which will protect you and your work. You can see a copy', '/contract.pdf', 'here'));
  }, */

  /* SELECTED_CONTRACTOR_PROMPT: (project: ProjectInfo, contract: Contract, contract_url: string) => {
    const prompt = [promptInfo(`Congratulations! ${project.client.nick} wants you to help with this ${project.title} job.`)];
    if (contract && (contract.contractor_reviewed || contract.contractor_signed)) {
      if (project.contractor.ready) {
        if (project.escrow) {
          prompt.push(promptInfo(`I have a deposit from ${project.client.nick}, which will get paid out when the job is finished. You can track your progress from the 'Track Work' screen.`));
        } else {
          prompt.push(promptInfo(`I'm waiting for a deposit from ${project.client.nick}, which will get paid out when the job is finished. I'll let you know once you can start tracking your job progress`));
        }
    } else if (project.contract) {
      if (contract_url) prompt.push(promptInfo("Here's the standard service agreement for you to", contract_url, 'sign', [], true));
      else prompt.push(SETUP_STRIPE_PROMPT(project.client.nick, false));
    } else {
      prompt.push(promptInfo('You should be getting an email with a link to a contract to sign, which will protect you and your work. You can see a copy', '/contract.pdf', 'here'));
    }
    return prompt;
  }, */

  SELECTED_SUBJECT: (proj: Project) => {
    if (proj.select_notice) return `Next steps on AskFora ${proj.title} job from ${proj.client.displayName}`;
    return `Selected for AskFora ${proj.title} job from ${proj.client.displayName}`;
  },

  SELECTED_MESSAGE: (proj: Project, person: Partial<Person>, url: string, sign_contract: boolean) => {
    let contract = '';
    let payment = '';
    let get_started = '';
    let intro = proj.select_notice ? `Your ${proj.title} job with ${proj.client.nickName} is almost ready.`
      :`Congratulations! ${proj.client.nickName} has selected you for the ${proj.title} job.`;

    if (sign_contract) {
      contract = `Click here to sign a standard service agreement which protects your interests in your work: ${url}`;
      if (!person.ready) payment = ' You will also need to set up your account to receive payment.';
    } else if (!person.ready) {
      payment = 'You will need to set up your account to receive payment.';
      get_started = `Click here so you can receive payment: ${url}`;
    } else if (proj.escrow) {
      get_started = `Click here to track your progress: ${url}`;
    }

    return `${person.nickName},

${intro} ${contract}${payment}

${get_started}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  PAY_PROMPT: (c, a, t) => {
    const prompt = [`Please confirm that I can release $${a.toLocaleString()} from deposit to ${c}.`];
    if (a < t) prompt.push(`You'll receive a refund of $${(t - a).toLocaleString()} plus fees.`);
    return prompt;
  },

  PAY_ANSWERS: ['Confirmed', 'Hold on, not yet'],

  ACCEPTED_PROMPT: (n, c, s, t, u) => {
    let p = null;
    if (!s) {
      p = [
        `Excellent ${n}! I'll let ${c} know you're interested. \
      If ${c} selects you, you will need a payment account to get paid.`,
        "Ask me to 'Link Payment' and I'll help you set up a new account or link an existing one.",
      ];
    } else if (u) {
      p = promptInfo(`Thank you ${n}. I'll let ${c} know. You can go ahead and submit your application at the link above or by selecting`, u, 'here.');
    } else {
      p = [`Thank you ${n}. I'll let ${c} know and will get back to you on next steps. In the mean time, let me know if there's anything I can help with.`];
      if (t) p.push("To learn more about what I can do, say 'Show me the FAQ'");
    }
    return p;
  },

  ANSWER_PROMPT: (n, c) => {
    return [`Thank you ${n}. I'll let ${c} know you sent an answer and will email you if ${c} requests additional help.`]; //, `In the meantime, ask me to "Import" and you can add contacts from LinkedIn, Facebook, and iCloud.`, `The more people in your network, the easier it is to get expert help and connect with people for freelancing jobs.`];
  },

  APPLY_PROMPT: (n, c, u) => {
    return u ? [promptInfo(`Thank you ${n}. You can submit your detailed application`, u, 'here')] : [`Thank you ${n}. We'll reach out with details about applying.`];
  },

  ACCEPT_PROPOSAL_SUBJECT: (project: Project) => {
    return `AskFora ${project.title} job proposal accepted`;
  },

  ACCEPT_PROPOSAL_MESSAGE: (project: Project, person: Partial<Person>, url: string) => {
    return `${person.nickName},

${project.client.nickName} has accepted your ${project.title} job proposal. You can track next steps here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  ACCEPT_PROPOSAL_PROMPT: (project: ProjectInfo) => {
    return `Excellent! I'll let ${project.contractor.nick} know.`;
  },

  ACCEPTED_HINT: 'Show me the FAQ',

  CONNECT_PAYMENT: 'Link Payment',

  SKIP_CONTRACT: 'no_contract',

  SKIP_ESCROW: { id: 'no_escrow', amount: 0, charges: null },

  SKIP_CHARGE: { id: 'no_escrow', amount: 0, charges: { data: [ { id: 'no_charge' } ] } },

  SKIP_PAYMENT: d => { return { id: 'no_payment', amount: 0, created: Math.floor(d.getTime()/1000) } },

  ACCEPTED_SUBJECT: (proj, person) => {
    return `${person.displayName} is interested in your AskFora ${proj.title} job`;
  },

  ACCEPTED_MESSAGE: (project: Project, person: Partial<Person>, url: string) => {
    if (!project.escrow && (!project.group_settings || !project.group_settings.skip_payment)) {
      const contract = project.contract ? '' : 'sign a contract and ';
      const service_fee = serviceFee(project);
      const fee = service_fee > 0 ? ` and a fee of $${service_fee.toLocaleString()}` : '';
      const deposit = `deposit $${depositAmount(project).toLocaleString()}${fee} with a credit card`;
      return `${project.client.nickName},

${person.displayName} is interested in your ${project.title} job. Click this link to view your job and select one of the interested candidates: ${url}

You will need to ${contract}${deposit} for ${person.nickName} to start the job. You retain full control of the funds on deposit so long as you abide by the AskFora Terms of Service (https://askfora.com/terms).

${about.ABOUT_EMAIL_SIGNATURE}`;
    } else {
      const contract = project.contract ? '' : `
You will need to sign a contract with ${person.nickName} to start the job.`;
      return `${project.client.nickName},

${person.displayName} is interested in your ${project.title} job. Click this link to view your job and select one of the interested candidates: ${url}
${contract}

${about.ABOUT_EMAIL_SIGNATURE}`;
    }
  },

  ANSWER_SUBJECT: (proj, person) => {
    return `${person.displayName} has answered your ${proj.title} question`;
  },

  ANSWER_MESSAGE: (project: Project, person: Partial<Person>, url: string) => {
    return `${project.client.nickName},

${person.displayName} has answered your ${project.title} question. Click this link to view the answer: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  DECLINED_SUBJECT: (proj, person) => {
    return `${person.displayName} is not interested in your AskFora ${proj.title} job`;
  },

  DECLINED_MESSAGE: (proj: Project, person: Partial<Person>, url: string) => {
    return `${proj.client.nickName},

${person.displayName} has reviewed your job and is not interested at this time. \
You may want to update your job description or fee. You can also select other candidates by going to \
'Find Candidates' and sending an invitation to consider your job: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONTRACTOR_NEXT_STEPS: (name, client, pay_url = null) => {
    const do_pay = pay_url ? `You'll need to set up a payment account so I know where to deposit your money when the job is complete. You can do that here: ${pay_url}` : '';
    return `${name},

Thank you for using AskFora to complete your service agreement. We’re almost ready to start this job. ${do_pay}
Once ${client} has deposited funds with AskFora, I’ll send you an email that the job is ready to start.

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONTRACT_FILLED_SUBJECT: (proj: Project) => {
    return `${proj.client.displayName} AskFora ${proj.title} job closed`;
  },

  CONTRACT_FILLED_MESSAGE: (proj: Project, person: Partial<Person>) =>  {
    return `${person.nickName},

Thank you for your interest in ${proj.client.displayName}'s ${proj.title} job. Either ${proj.client.displayName} selected someone else or no longer needs to fill the job so I have removed it from your job list.
If you have any questions or have feedback specific to this job, please reach out to ${proj.client.displayName} directly.

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONTRACTOR_CANCEL_SUBJECT: (proj: Project) => {
    return `${proj.contractor.nickName} canceled your AskFora ${proj.title} job`;
  },

  CONTRACTOR_CANCEL_MESSAGE: (proj: Project, url: string) => {
    return `${proj.client.nickName},

Your contractor ${proj.contractor.nickName} for your ${proj.title} job has canceled the job. I've refunded your deposit back to your card. You can view the job details here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  DEPOSIT_SUBJECT: (person) => {
    return `Start your job with ${person.displayName}`;
  },

  DEPOSIT_MESSAGE: (project: Project, person: Partial<Person>, url: string) => {
    let deposit = `deposit $${depositAmount(project).toLocaleString()} and a fee of $${serviceFee(project).toLocaleString()} with a credit card`;
    if (project.escrow) deposit = 'get started';

    return `${project.client.nickName},

${person.displayName} has set up a payment account. You can start your ${project.title} job by selecting 'Start' from the 'Track' screen to ${deposit}: ${url} \

You can change your selected contractor at any time until you deposit funds. After you have submitted a deposit to AskFora, you won’t be able to change your contractor for this job.

As always, you retain full control of the funds on deposit so long as you abide by the AskFora Terms of Service (https://askfora.com/terms).

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  DECLINED_NDA: "No worries. If you're interested in looking later, just tap the link in my email.",

  DECLINED_PROMPT: (n, c) => {
    return [`Thank you ${n}. Sorry this job isn't a fit. I'll let ${c} know and remove it from your queue.`, 'Did I miss on skill set or are you not available or interested?'];
  },

  DECLINED_EXPERT_PROMPT: (n, c) => {
    return [`Thank you ${n}. I'll let ${c} know you can't answer this question.`, 'Did I miss on skill set or are you not available or interested?'];
  },

  DECLINED_ANSWERS: ['Wrong skill set', 'Not available', 'Not interested'],

  RECOMMENDED_SUBJECT: (proj, person) => {
    return `${person.displayName} has some recommendations for your AskFora ${proj.title} job`;
  },

  RECOMMENDED_MESSAGE: (proj: Project, person: Partial<Person>, url: string) => {
    return `${proj.client.nickName},

${person.displayName} has reviewed your job and may have some recommendations. \
They'll send the job details to folks who they think would be a fit and I'll let you know if any recommended candidate is interested. \
As with every job, candidates will have to accept a short form non-disclosure before viewing the job details, \
which you can update at any time: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  RECOMMEND_MISSING: "Sorry I can't help you. You can forward the job invitation if you want to refer someone you know",

  RECOMMEND_DONE: n => {
    return `I'll send ${n} a link to the job details`;
  },

  FORWARD_SUBJECT: (proj, person) => {
    return `Job recommendation from ${person.displayName}`;
  },

  FORWARD_MESSAGE: (proj, person, rcpt_name, auths, url: string) => {
    const estimate = projectEstimate(proj);

    return `${rcpt_name},

I'm helping ${proj.client.displayName} find someone for a short term job who has these skills and experience: ${proj.skills}. \
${estimate}. ${person.nickName} thought you might be interested.

You can see details here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  RECOMMENDED_PROMPT: (n, c, t) => {
    const p = [
      `Thank you ${n}. Personal recommendations are the best source of trusted talent. \
You can forward the job link or ask me to reach out directly to any recommendations you have. \
I won't share their information with ${c} unless your referral considers the job.`,
    ];

    if (t) p.push("You can tell me names and email addresses or link your contacts by saying 'Link'");
    else p.push('You can tell me names of people in your contacts or any additional names and email addresses');

    return p;
  },

  PROPOSAL_DECLINED_SUBJECT: (proj: Project) => {
    return `${proj.client.displayName} AskFora ${proj.title} job proposal declined`;
  },

  PROPOSAL_DECLINED_MESSAGE: (proj: Project) =>  {
    return `${proj.contractor.nickName},

${proj.client.displayName} has reviewed and declined your ${proj.title} job proposal. If you have any questions, please reach out to ${proj.client.displayName} directly.

${about.ABOUT_EMAIL_SIGNATURE}`;
  },


  ESCROW_SUBJECT: (proj: Project) => {
    return `AskFora deposit received for ${proj.title} job from ${proj.client.displayName}`;
  },

  ESCROW_MESSAGE: (proj: Project, url: string) => {
    const amount = depositAmount(proj);

    const congrats = proj.select_notice ? '' : `Congratulations! ${proj.client.displayName} has selected you for the ${proj.title} job.
    
`;

    return `${proj.contractor.nickName},

${congrats}I've received $${amount.toLocaleString()} on deposit for your job with ${proj.client.displayName}. You can record your progress and request payment via ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  START_SUBJECT: (proj: Project) => {
    return `AskFora ${proj.title} job from ${proj.client.displayName} ready to go`;
  },

  START_MESSAGE: (proj: Project, url: string) => {
    const congrats = proj.select_notice ? '' : `Congratulations! ${proj.client.displayName} has selected you for the ${proj.title} job.
    
`;

    return `${proj.contractor.nickName},

${congrats}I've heard from ${proj.client.displayName} that your job is ready to start. You can record your progress via ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  COMPLETED_SUBJECT: (proj: Project) => {
    return `AskFora completed ${proj.title} job from ${proj.client.displayName}`;
  },

  COMPLETED_MESSAGE: (proj: Project, url: string) => {
    const amount = depositAmount(proj, true);

    const congrats = proj.select_notice ? '' : `Congratulations! ${proj.client.displayName} let me know your ${proj.title} job is complete`;
    const request = proj.escrow && proj.escrow.id !== 'no_escrow' ? `. I've already received $${amount.toLocaleString()} on deposit and you can request payment here` : '';

    return `${proj.contractor.nickName},

${congrats}${request}: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  ESCROW_RECEIPT_SUBJECT: (proj: Project) => {
    return `AskFora deposit receipt for ${proj.title} job with ${proj.contractor.displayName}`;
  },


  ESCROW_RECEIPT_MESSAGE: (proj: Project, url: string) => {
    

    return `${proj.client.nickName},

I've received $${depositAmount(proj).toLocaleString()} on deposit from you for your job with ${proj.contractor.displayName}. You can record your progress and approve payment via ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  START_RECEIPT_SUBJECT: (proj: Project) => {
    return `AskFora ${proj.title} job with ${proj.contractor.displayName} started`;
  },

  START_RECEIPT_MESSAGE: (proj: Project, url: string) => {
    return `${proj.client.nickName},

I'm confirming that your job with ${proj.contractor.displayName} is ready to go. You can record your progress via ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  ESCROW_PROMPT: (project: Project) => {
    return [
      promptInfo(`Thank you! ${project.escrow && project.escrow.id !== 'no_escrow' ? 'Your deposit was successfully processed. I\'ve sent you a receipt and' : 'I\'ve'} emailed ${project.contractor.nickName} to begin work.`),
      promptInfo('Check back here at any time to track progress, take notes, or share documents.'),
    ];
  },

  UPDATE_SUBJECT: (proj:  Project) => {
    return `AskFora ${proj.title} job update`;
  },

  UPDATE_MESSAGE: (proj: Project, url: string, person: Partial<Person>) => {
    return `${person.nickName},

You have an update for your ${proj.title} job in progress. Take a look  here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  PROGRESS_SUBJECT: (proj:  Project) => {
    return `AskFora progress on ${proj.title} job`;
  },

  PROGRESS_MESSAGE: (proj: Project, url: string, person: Partial<Person>) => {
    return `${person.nickName},

You have progress on your ${proj.title} job. Take a look  here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  SUBMIT_FIRST: (nick: string) => {
    const client = nick ? nick : 'the client';
    return `You must submit the job for approval and wait for ${client} to confirm before requesting payment.`;
  },

  SUBMITTED_SUBJECT: (project: Project) => {
    return `AskFora ${project.title} job submitted`;
  },

  SUBMITTED_MESSAGE: (project: Project, url: string) => {
    let amount: number = project.fee;
    let complete = '';
    switch (project.rate) {
      case ProjectRate.hourly:
        complete = ` ${project.progress} of ${project.duration} hours completed`
        amount *= project.progress;
        break;
      case ProjectRate.daily:
        complete = ` ${project.progress} of ${project.duration} days completed`
        amount *= project.progress;
        break;
      case ProjectRate.fixed:
        break;
    }

    return `${project.client.nickName},

${project.contractor.nickName} has submitted your job${complete} for a total of $${amount.toLocaleString()}. Please review, finish and release payment for the job here ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;

  },

  REQUEST_PAY_SUBJECT: (project: Project) => {
    return `AskFora ${project.title} job payment request`;
  },

  REQUEST_PAY_MESSAGE: (project: Project, url: string) => {
    const amount = depositAmount(project, true);

    return `${project.client.nickName},

${project.contractor.nickName} has requested payment for $${amount.toLocaleString()}, which I am holding on deposit. \
Please confirm that I can release payment by selecting "Release Payment" here ${url}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONFIRM_SUBMITTED: (proj: ProjectInfo) => {
    return `I'll let ${proj.client.nick} know that you finished the job.`;
  },

  CONFIRM_REQUEST_PAY: (proj: ProjectInfo) => {
    const amount = depositAmount(proj, true);

    return `Congratulations on completing this job. I'll ask ${proj.client.nick} to approve releasing your payment for $${amount.toLocaleString()}`;
  },

  PROCEED_TO_PAY: (proj: ProjectInfo) => {
    const pp = [`Congratulations on having your ${proj.title} job completed!`]
    if (proj.fee) pp.push(`If you are satisfied with the work and have received all of your deliverables, click 'Approve Payment' so I can release funds to ${proj.contractor.nick}.`);
    return pp;
  },

  CONFIRM_PAYMENT: (proj: ProjectInfo) => {
    return `Great, I've released the funds and have let ${proj.contractor.nick} know. ${proj.contractor.nick} will receive payment in the next 3 to 5 business days. Don't forget, when you repeat a job or start a new one with the same contractor, my job fee is only 5%.`;
  },

  ERROR_PAYMENT: "I ran into an error releasing funds. Please try again or send feedback and I'll look into it.",

  PAID_SUBJECT: (proj: Project) => {
    return `AskFora ${proj.title} job payment from ${proj.client.displayName}`;
  },

  PAID_MESSAGE: (proj: Project) => {
    const amount = depositAmount(proj, true);
    let comp = 'let me know that this pro bono job is complete';

    if (amount) comp = `released payment for $${amount.toLocaleString()}, which is headed your way`;

    return `${proj.contractor.nickName},

Job well done. ${proj.client.nickName} has ${comp}. You should receive it in 3 to 5 business days. Let me know if there's anything I can do to improve this service.

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  NOTIFY_UPDATE_TITLE: 'AskFora Job',

  NOTIFY_UPDATE_EXPERT_TITLE: 'AskFora Request',

  NOTIFY_UPDATE_MESSAGE: (n, t, s, d, client, contractor) => {
    switch(n) {
      case NotificationType.Project_Invite: return `You might be interested in a ${s} job from ${client}.`;
      case NotificationType.Project_Proposal: 
        if(t === TemplateType.Accept) return `${client} has accepted your ${s} job proposal.`;
        else if (t === TemplateType.Decline) return `${client} has declined your ${s} job proposal.`;
        return `You have a job proposal for ${s} from ${contractor}.`;
      case NotificationType.Project_Found: 
        if (t === TemplateType.None) return `I couldn't find any candidates for your ${s} job.`;
        else if (t === TemplateType.NoExpert) return `I couldn't find any experts for your ${s} question.`;
        else if(t === TemplateType.Expert) return `You have new experts for your ${s} question.`;
        return `You have new candidates for your ${s} job.`;
      case NotificationType.Project_Accepted: return `${contractor} accepted the invitation for your ${s} job.`;
      case NotificationType.Project_Answer: return `${contractor} has answered your ${s} question.`;
      case NotificationType.Project_Recommended: return `You have a recommendation for your ${s} job.`;
      case NotificationType.Project_Declined: return `${contractor} declined the invitation for your ${s} job.`;
      case NotificationType.Project_Selected: return `${client} has selected you for a ${s} job.`;
      case NotificationType.Project_SetupPayment: return `${client} has countersigned the agreement for the ${s} job. You will need to set up a payment account to get started.`;
      case NotificationType.Project_Ready: return `Your ${s} job is ready to start. Tap 'Track' and then 'Start' to ${d ? '' : 'deposit payment '}and get started.`;
      case NotificationType.Project_Deposit: return `${client} ${d ? 'told me to start' : 'sent me payment for'} your ${s} job. You're good to go.`;
      case NotificationType.Project_Escrow: return `I'm holding on to payment for your ${s} job and let ${contractor} know to get started.`;
      case NotificationType.Project_Progress: return `You have a progress update to your ${s} job.`;
      case NotificationType.Project_Closed: return  `Your ${s} job has been closed.`;
      case NotificationType.Project_Submit: 
        if(contractor) return `${contractor} wants you to know that your ${s} job is done.`;
        else return `Your ${s} job is done.`;
      case NotificationType.Project_Complete: return  `Your ${s} job is complete. You can ask ${client} to release your payment.`;

      case NotificationType.Project_Paid: return  `Your ${s} job has been paid.`;
      case NotificationType.Project_PayRequested: 
        if (contractor) return  `${contractor} has requested payment for your ${s} job.`;
        else return  `Your ${s} job has a payment request.`;
      case NotificationType.Project_Update:
        if (t === TemplateType.Answer) return `${contractor} updated their answer to your ${s} question`;
        else if (t === TemplateType.Public) return `${contractor} marked their answer to your ${s} question as public.`;
        else if (t === TemplateType.Shared) return `${contractor} marked their answer to your ${s} question as shared.`;
        else if (t === TemplateType.Private) return `${contractor} marked their answer to your ${s} question as private.`;
      default:
        return `You have an update to your ${s} job.`;
    }
  },
};
