import fs from 'fs';
import path from 'path';

export default {
  TOS_RESERVED: ['terms', 'license', 'tos', 'show'],

  TOS_SHORTCUT: a => {
    if (!a) return { name: 'Terms', value: 'Terms of service' };
    else return null;
  },

  TOS_KWDS: ['tos', 'terms', 'terms of service', 'license', 'show terms', 'show me the terms', 'show me the license', 'what are your terms', 'what is your license', "what's your license"],

  TERMS: fs
    .readFileSync(path.resolve(__dirname, '..', 'files', 'tos.txt'))
    .toString()
    .split('\n')
    .filter(l => l.length),
};
