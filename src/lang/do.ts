import { Person } from '../types/items';

import { localeDowMonthDay } from '../utils/format';
import { promptInfo } from '../utils/info';

export default {
  DO_EG : [],

  DO_RESERVED : ['complete', 'clear', 'ignore', 'later', 'late', 'cancel', 'read', 'mark', 'archive', 'draft', 'delete', 'remove', 'not done', 'undo', 'uncomplete'],

  DONE_KWD: ['complete', 'cancel'],
  IGNORE_KWD: ['ignore', 'later', 'do later', 'postpone'],
  CLEAR_KWD: ['clear'],
  NOT_DONE_KWD: ['not done', 'undo', 'uncomplete'],

  READ_KWD: ['done', 'read', 'mark'],
  ARCHIVE_KWD: ['archive'],

  LATE_KWD: ['running late', 'late'],
  CANCEL_KWD: ['cancel'],

  DRAFT_KWD: ['draft'],

  DELETE_KWD: ['delete', 'remove'],

  CONFIRM_DELETE_NOTE: (t, d, p) => {
    const prompt = `Should I delete your '${t}' note`;
    const from = d ? ` from ${d}` : '';
    if (!p || p.length === 0) return promptInfo(`${prompt}${from}?`);
    if (p.length === 1) return promptInfo(`${prompt} linked to ${p[0].displayName}${from}?`);
    if (p.length === 2) return promptInfo(`${prompt} linked to ${p[0].displayName} and ${p[1].displayName}${from}?`);
    return promptInfo(`${prompt} linked to ${p.length} folks${from}?`);
  },

  CONFIRM_DELETE_PROJECT: (a, s, j, d, l, t) => {
    return promptInfo(`Should I ${a} your ${s} ${j} from ${localeDowMonthDay(d, l, t)}?`);
  },

  CONFIRM_DECLINE_PROJECT: (t, c) => {
    return promptInfo(`Are you sure you want to decline the ${t} job proposal from ${c}?`);
  },

  CONFIRM_DELETE_ANSWERS: (a, plural = null) => {
    return [`Yes, ${a} ${plural ? 'them' : 'it'}`, `No, keep ${plural ? 'them' : 'it'}`];
  },

  CONFIRM_DELETE_PERSON: (p: Partial<Person>) => {
    let remote_del = '';
    if (p.id.startsWith('people/c')) {
      remote_del = ' This will also delete the record in your Google account.';
    } else if (p.id.startsWith('people/m')) {
      remote_del = ' This will also delete the record in your Microsoft account.';
    }
    return promptInfo(`Should I delete your contact record for ${p.displayName}?${remote_del}`);
  },

  CONFIRM_DELETE_CONTRACT: (p: Partial<Person>) => {
    return promptInfo(`Should I delete all contracts wtih ${p.displayName}? This may invalidate some active projects.`);
  },

  CONFIRM_COMPLETE: "Okay I’ll mark the note as complete and add it to your archives. You can view your archived notes in Settings.",

  CONFIRM_IGNORE: "Got it, I'll remind you later",

  CONFIRM_CLEAR: "No worries, I won't bug you about it",

  REFUND: a => {
    return promptInfo(`I'll refund you a total of $${a} for escrow and fees.`);
  },

  CONTRACTOR_CANCEL: u => {
    return promptInfo(`I'll let ${u} know that you are not able to complete this job.`);
  },

  CLIENT_DECLINE: u => {
    return promptInfo(`I'll let ${u} know that you did not accept this job and archive it for you.`);
  },

  DRAFT_PROMPT: u => {
    return promptInfo("I've drafted email that you can", u, 'edit and send');
  },

  IGNORE: 'ignore',
  CLEAR: 'clear',
  DONE: 'done',
  UNDO:'undo',
  DRAFT: 'draft',
  READ: 'read',
  ARCHIVE: 'archive',
  LATE: 'late',
  CANCEL: 'cancel',
  DELETE: 'delete',

  RUNNING_LATE: (name, event) => {
    let to: string;
    if (event.people.length === 1) return ''; // just the user
    if (event.people.length <= 4) {
      const names = [];
      for (const person of event.people) {
        if (!person.self) names.push(person.nickName);
      }
      const last = names.pop();
      if (names.length) to = `${names.join(', ')}, and ${last}`;
      else to = last;
    } else to = 'Folks';

    return `${to},

My apologies, I'm running few minutes late.

-${name}`;
  },

  DRAFT_FOLLOW_UP: (name: string, recipient: Partial<Person>[], message = null) => {
    let to = '';
    const nl_message = message ? `
${message}
` : '';
    const r_not_me = recipient.filter(p => !p.self);
    if (r_not_me.length <= 4) {
      const names = [];
      for (const person of recipient) {
        if (!person.self) names.push(person.nickName);
      }
      const last = names.pop();
      if (names.length) to = `${names.join(', ')}, and ${last},
`;
      else if (last) to = `${last},
`;
    } else to = 'Folks';

    return `${to}${nl_message} 
-${name}`;
  },
};
