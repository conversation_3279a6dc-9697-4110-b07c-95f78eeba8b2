import { Person } from '../types/items';

export default {
  INTRO_EG: ["Get an introduction just by asking me: 'Ask <PERSON><PERSON> for an introduction to <PERSON><PERSON>'"],

  ASK: ['ask'],

  INTRO: ['eintro', 'intro', 'introduction', 'introduce', 'introduced', 'connect', 'connected', 'meet', 'meeting', 'met'],

  CONTEXT: [
    'about ',
    'regarding ',
    ' re ',
    // ' to ',
    ' on ',
    ' from ',
  ],

  TARGET: [' intro to ', ' introduction to ', ' connect to ', ' eintro to ', ' me to ', ' me with ', ' me and ', ' with '],

  PROMPT: 'Who would you like to ask to introduce you to whom?',

  PROMPT_HINT: 'Ask First Connection to introduce me to Second Connection',

  DRAFT: "I've drafted an email that you can",

  EDIT_SEND: 'edit and send',

  NOT_FOUND_LEARNING: ["I wasn't able to find the contact info.", "I'm still learning so please try again in a few minutes."],

  NOT_FOUND: "I wasn't able to find the contact info.",

  ASK_EMAIL: (name: string, contact: Partial<Person>, target: string, context: string) => {
    const about = context && context.length ? ` regarding ${context}` : '';
    return `${contact.nickName},

I'm looking to connect with ${target}${about}. Could you make an introduction?

-${name}`;
  },
};
