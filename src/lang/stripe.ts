import { promptInfo } from '../utils/info';

function fakeCharge(i,a) {
  return {
    id: 'ch_test_offline',
    object: 'charge',
    amount: a,
    captured: true,
    created: new Date().getTime(),
    currency: 'usd',
    customer: 'cus_test_offline',
    livemode: false,
    on_behalf_of: i,
    outcome: { network_status: 'approved_by_network', reason: null, risk_level: 'normal', seller_message: 'Complete', type: 'authorized' },
    paid: false,
    source: {
      id: 'card_offline',
      object: 'card',
      address_zip: '00000',
      address_zip_check: 'pass',
      brand: 'Visa',
      country: 'US',
      customer: 'cus_test_offline',
      exp_month: new Date().getMonth(),
      exp_year: new Date().getFullYear(),
      fingerprint: 'offline',
      funding: 'credit',
      last4: '4242',
      metadata: {},
    },
    status: 'succeeded',
  }
}

export default {
  STRIPE_KWD: [
    'stripe dashboard',
    'payments dashboard',
  ],

  CONNECT_STRIPE: 'connect stripe',

  CHANGE_KWD: 'change',

  STRIPE_ACCOUNT: 'Do you want to connect an existing Stripe account?',

  DETAILS_PROMPT: [
    "Please confirm details for your pay out account. If you already have a Stripe account you'll be able to connect it in a minute.",
    "Here's what I have. Does this look right?",
    'Make sure your name starts with upper case letters and email and phone numbers are correctly formatted',
    "Here's my info as an example: '<NAME_EMAIL>'",
  ],
  
  DETAILS_ANSWERS: [`That's Right`, 'Make Changes', 'Nevermind'],

  DETAILS_CHANGE: 'No problem, go ahead and edit your details',

  DETAILS_CONFIRM: "Ok, I'll send this information to Stripe to set up your payment account:",

  CONTRACTOR_ERROR: n => `${n} needs to complete setting up their payment account before starting this job.`,

  JOB_DESC_PROMPT: 'In a few words, how would you describe the services (e.g. facilitating payments)?',

  JOB_DESC_CONFIRM: 'Confirm or change this description of services:',

  COMPANY_PROMPT: 'Are you working as a business or an individual?',

  COMPANY_ANSWERS: ['Business', 'Individual'],

  COMPANY_SELF: ['me', 'myself', 'individual'],

  COMPANY_BUSINESS: ['partnership', 'partner', 'org', 'organization', 'business', 'biz', 'corp', 'llc'],

  COMPANY_NAME_PROMPT: 'What is your business name?',

  CATEGORY_PROMPT: 'What category best describes the services you generally provide?',

  COUNTRY_PROMPT: 'Please confirm the country for your pay out account.',

  ACCOUNT_DEBIT_ERROR: "I can't use this account for payment. Please choose a different option to start your job.",

  ERROR: 'Error connecting to Stripe',

  STRIPE_CATEGORIES: ['Design', 'Consulting', 'Prof. Services', 'Web Dev', 'Other'],

  STRIPE_URL: u => {
    return promptInfo("You're all set to connect to Stripe for payment. Click to", u, 'setup or login.', [], true);
  },

  CONNECTED_PROJECT: (p = null) => {
    const from = p && p.client && p.client.nickName ? ` from ${p.client.nickName}` : '';
    return `I'm waiting for a deposit${from}, which will get paid out when the job is finished. I'll let you know once you can start tracking your job progress`;
  },

  CONNECTED: "Congratulations, you're ready to get paid for jobs!",

  CONFIRM_DISCONNECT: 'Are you sure you want to disconnect your pay out account?',

  CANNOT_DISCONNECT: 'You need to keep a pay out account connected while you have a job in progress.',
  
  DISCONNECTED: 'Your account has been disconnected. You will need to connect a pay out account in order to be paid for jobs.',

  ESCROW_ERROR: "I'm having trouble processing your deposit.",

  AUTH_STUB: c => {
    return `https://connect.stripe.com/express/oauth/authorize?redirect_uri=https://localhost:8443/gopay&client_id=ca_offline&state=${c}`;
  },

  DASHBOARD: u => promptInfo('You can view your payment account', u, 'here'),

  TOKEN_STUB: {
    stripe_publishable_key: 'pk_test_offline',
    refresh_token: 'rt_offline',
    livemode: false,
    access_token: 'sk_test_offline',
    scope: 'express',
    stripe_user_id: 'acct_offline',
    token_type: 'bearer',
  },

  ACCOUNT_STUB: id => { return {
    id,
    object: 'account',
    business_profile: {},
    capabilities: { card_payments: 'active', tax_reporting_us_1099_misc: 'active', transfers: 'active' },
    charges_enabled: true,
    country: 'US',
    created: **********,
    default_currency: 'usd',
    details_submitted: true,
    email: '<EMAIL>',
    external_accounts: { object: 'list', data: [], has_more: false, total_count: 1, url: `/v1/accounts/${id}/external_accounts` },
    future_requirements: {},
    login_links: { object: 'list', data: [], has_more: false, total_count: 0, url: `/v1/accounts/${id}/login_links` },
    metadata: {},
    payouts_enabled: true,
    requirements: { alternatives: [], current_deadline: **********, currently_due: [ 'individual.address.line1' ], disabled_reason: null, errors: [], eventually_due: [ 'individual.address.line1' ], past_due: [], pending_verification: []},
    settings: {},
    tos_acceptance: { date: ********** },
    type: 'express'
  }},

  PAYINTENT_STUB: (s, r, i, a) => {
    return { 
      transfer_data: null,
      id: 'pi_offline_payment',
      currency: 'usd',
      charges:
        { data: [ fakeCharge(i, a) ],
          total_count: '1',
          has_more: false,
          url: '/v1/charges?payment_intent=pi_offline_payment',
          object: 'list' },
      client_secret:
        'pi_offline_payment',
      review: null,
      payment_method_types: [ 'card' ],
      description:s, 
      on_behalf_of: i,
      receipt_email: r,
      amount: a,
      amount_received: a,
      customer: null,
      object: 'payment_intent',
      livemode: false,
      next_action: null,
      metadata: {},
      capture_method: 'automatic',
      cancellation_reason: null,
      status: 'succeeded',
      last_payment_error: null,
      source: null,
      created: `${Math.round(new Date().getTime()/1000)}`,
      application: null,
      application_fee_amount: null,
      canceled_at: null,
      statement_descriptor_suffix: null,
      setup_future_usage: null,
      payment_method: 'pm_offline_payment',
      confirmation_method: 'manual',
      statement_descriptor: null,
      transfer_group: null,
      invoice: null,
      shipping: null,
      amount_capturable: '0',
      payment_method_options: { card: { request_three_d_secure: 'automatic', installments: null } } 
    }
  },

  CHARGE_STUB: (i, a) => {
    return fakeCharge(i,a);
  },

  CHARGE_FAIL_RETRY_PROMPT: "I wasn't able to process your escrow with Stripe. In a few minutes, you should try again.",

  PAY_STUB: (i, a, e) => {
    return {
      id: 'tr_test_offline',
      object: 'transfer',
      amount: a,
      balance_transaction: 'txn_test_offline',
      created: new Date().getTime(),
      currency: 'usd',
      description: null,
      destination: i,
      destination_payment: 'py_test_offline',
      livemode: false,
      metadata: {
        order_id: '6735',
        something_else: 'blah',
      },
      source_transaction: e,
      source_type: 'card',
      transfer_group: `group_${e}`,
    };
  },

  REFUND_STUB: (c, a) => {
    return {
      id: 're_test_offline',
      object: 'refund',
      amount: a,
      balance_transaction: 'txn_test_offline',
      charge: c,
      created: new Date().getTime(),
      currency: 'usd',
      status: 'succeeded',
    };
  },
};
