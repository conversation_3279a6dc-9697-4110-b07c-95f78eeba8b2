import { Person } from '../types/items';
import { EntityType } from '../types/shared';

import { promptInfo } from '../utils/info';

import about from './about';

export default {
  SHARE_NAME: 'Share',
  SHARE_DESCRIPTION: 'Share searches, development plans, jobs, sourcing',
  RESERVED: ['share', 'share list', 'share plan', 'share a plan', 'share search', 'share ask', 'share a list', 'share engagement', 'share an engagement', 'share anlalysis', 'share an analysis', 'share job', 'share a job'],
  SHARE_KWD: ['share', 'share list', 'share plan', 'share a plan', 'share search', 'share ask', 'share a list', 'share engagement', 'share an engagement', 'share anlalysis', 'share an analysis', 'share job', 'share a job'],
  SHARE_PROMPT: 'I can draft an email for you. Who would you like to share this with?',

  SHARE_SUBJECT: (t: EntityType) => {
    switch(t) {
      case EntityType.Contract: return `Here's an AskFora Contract`;
      case EntityType.Project: return `Here's an AskFora Job`;
      case EntityType.Expert: return `Here's an AskFora Question`;
      case EntityType.Analysis: return `Here's an AskFora Analysis`;
      case EntityType.Ask: return `Here's an AskFora Search`;
      default: return 'Shared from AskFora';
    }
  },

  SHARE_MESSAGE: (t: EntityType, u:string, me: Partial<Person>) => {
    let item = '';
    switch(t) {
      case EntityType.Contract: return `Here's an AskFora Contract`;
      case EntityType.Project: return `Here's an AskFora Job`;
      case EntityType.Expert: return `Here's an AskFora Question`;
      case EntityType.Analysis: return `Here's an AskFora Analysis`;
      case EntityType.Ask: return `Here's an AskFora Search`;
    }

    return `Take a look at this${item && item.length ? ' ' : ''}${item}: ${u}
    
-${me.nickName}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  DRAFT: l => promptInfo('You can edit and send this message', l, 'draft'),
}