import { localeDate } from '../utils/format';
import { promptInfo } from '../utils/info';

import about from './about';

export default {
  REMINDER_NAME: 'Add Reminder',
  REMINDER_DESC: 'Add a new task with optional due date and people associated',

  REMINDER_EG : ['Create reminders by chatting to me "Remind me to call <PERSON> Monday at 9am"'],

  REMINDER_RESERVED: [
    'remember [to do something at time]',
    'remind me [to call someone at time]',
    "don't forget [to do something at time]",
    'todo [something at time]',
    'to-do [something at time]',
    'follow [with someone about something at time]',
    'followup [with someone about something at time]',
    'follow-up [with someone about something at time]',
    'next step [with something is...]',
    'next steps [with something is...]',
  ],

  REMINDER_SHORTCUT: a => {
    if (a) return { name: 'Remind me...', value: 'Remind me ' };
    else return null;
  },

  ADD_REMINDER_KWDS: [
    'remember',
    'remind',
    'reminder',
    'forget',
    'todo',
    'to-do',
    'next steps',
    'next step',
    'follow',
    'followup',
    'followups',
    'follow-up',
    'follow-ups',
    // 'meet',
    // 'set a meeting',
    // 'ask to meet',
    // 'schedule a meeting',
    // 'call',
    // 'meet ' TODO: workout language for remember to meet/call
  ],

  FOLLOWUP_KWD: [
    'follow',
    'followup',
    'followups',
    'follow-up',
    'follow-ups',
  ],

  /* MEET_KWD: [
    'meet',
    'set a meeting',
    'ask to meet',
    'schedule a meeting',
  ],*/

  ADD_REMINDER_IGNORE: ['what', 'tell', 'list', 'who', 'when'],

  CONTEXT: [
    'about ',
    'regarding ',
    ' re ',
    'remember to ',
    'forget to ',
    'follow to ',
    'follow-up to ',
    'followup to ',
    'follow up to ',
    ' on ',
    ' from ',
    ' me to ',
    ' step with ',
    ' steps with ',
    ' step ',
    ' steps ',
  ],

  REMINDER_PROMPT: 'Who, when, and what can I remind you about?',

  REMINDER_CONTEXT: 'Do you want to include any additional information?',

  REMINDER_MEET_CONTEXT: 'What would you like to meet abouut?',

  REMINDER_LISTEN: "Go heading, I'm listening...",

  FOLLOWUP_DRAFT: (e, c, p, u) => {
    return {
      subject: `Following up${e ? ' on ' : ''}${e ? e.title : ''}`,
      raw: `${p.map(x => x.displayName).join(', ')},

I'm following up on our ${e ? localeDate(e.start, u.locale, u.timeZone) : ''}${e ? ' ' : ''}meeting. ${c ? c : ''}
      
-${u.name}

${about.ABOUT_EMAIL_SIGNATURE}`,
    }
  },

  MEET_DRAFT: (c, p, u) => {
    return {
      subject: `Meet ${c && c.length ? 'about ' : 'up'}${c ? c : ''}`,
      raw: `${p.map(x => x.displayName).join(', ')},

I'd like meet${c && c.length ? ' to discuss ' : ''}${c ? c : ''}. What's the best way to find some time?

- ${u.name}

${about.ABOUT_EMAIL_SIGNATURE}`,
    }
  },

  DRAFT: l => promptInfo('You can edit and send this message', l, 'draft'),
};
