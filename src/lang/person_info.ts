import { stringList } from '../utils/format';

import add_reminder from './add_reminder';
import project from './project';
import whats_next from './whats_next';

export default {
  PERSON_EG: ['I can find people you know based on a skill. You can ask me to "Find a UX designer"'],

  PERSON_RESERVED: [
    'who [do I know at AskFora]',
    "who's [<PERSON>]",
    'tell [me about someone]',
    'details [about a person]',
    'get [a designer]',
    'find [a developer]',
    'whois [someone]',
    'person',
    'people',
    'more',
    'more info',
    'info about [someone]',
    'list',
    'lookup [someone at company]',
    'i',
    'im',
    "i'm",
    'seeking',
    'looking',
    'searching',
    'want',
    'need',
  ],

  PERSON_INFO: [
    'who',
    'find',
    'who is',
    'know',
    "what's",
    'what is',
    "who's",
    'lookup',
    'i need',
    'need',
    'searching for',
    'i searching for',
    'im searching for',
    "i'm searching for",
    'looking for',
    'i looking for',
    'im looking for',
    "i'm looking for",
    'seeking',
    'i seeking',
    'im seeking',
    "i'm seeking",
    'i want',
    'want',
  ],

  INQUIRY_START: ['tell', 'details', 'get', 'more', 'list', 'search', 'find'],

  SEARCH: ['search'],

  FIND: ['find'],

  LIST: ['list'],

  INQUIRY_KEY: ['about', 'info', 'more', 'skill'],

  NOT_INQUIRY_START: add_reminder.ADD_REMINDER_KWDS.concat(whats_next.WHAT_KWD).concat(project.PROJECT_EXPERT),

  NOT_INQUIRY: ['what', 'todo'],

  REFINE_HINT: 'Filter by name',

  NOT_FOUND: [ "Sorry. I couldn't find anyone." ],

  NOT_FOUND_LEARNING: "Sorry, I couldn't find anyone among your first connections but I'm still learning your network. Please give me a few minutes.",

  NOT_FOUND_REG_REQUIRED: p => [
    `Sorry, I couldn't find anyone among your first connections. I can't help you until you link a ${stringList(p, 'or')} account.`,
    // "Say 'Link' to sync contacts, calendar, tasks, and email. If you prefer not to sync email, say 'Link without email'",
    "Say 'Link' to link contacts, and calendar.",
  ],

  FOUND: "I found a few people and added them to your list",

  FOUND_NETWORK: "I found a few people in your network and added them to your list",

  FOUND_EVENT: "I found a few people on your calendar and added them to your list",
  
  FOUND_SKILL: "I found several people among your first connections who might be a match.",

  REFINE: p => `Did you mean [${p.displayName}](${p.id})?`,

  REFINE_NETWORK: p => `I found [${p.displayName}](${p.id}) in your extended network.`,

  REFINE_EVENT: p => `I found [${p.displayName}](${p.id}) on your calendar.`,

  REFINE_SKILL: p => `I found [${p.displayName}](${p.id}) who matches your search.`,

  REFIND_ANSWERS: ['Yes', 'No', 'List'],
};
