import { Person } from '../types/items';
import { findTypeIndex, findTypesAtIndex, formatJob, PersonInfo, TagType } from '../types/shared';
import { stringList } from '../utils/format';
import { promptInfo } from '../utils/info';
import parsers from '../utils/parsers';
import about from './about';

function introAbout(person: Partial<Person>) {
  let info = null;

  const tindex = findTypeIndex(person.tags, TagType.organization, true);
  if (tindex != null) {
    const tag_set = findTypesAtIndex(person.tags, tindex);
    info = formatJob(tag_set).value;
  }

  const link = parsers.findLink(person);

  if (info && link) return `${person.nickName} ${parsers.findLink(person)} is ${info}.`;
  return '';
}


const conjunction = [' and ', ' with ', ' to ', ' between '];

export default {

  INTRO: ['intro', 'introduce', 'connect', 'make an intro', 'make an introduction', 'make a connection', 'ask for a connection'],

  CONNECTION_INTRO: 'ask for a connection',

  INTRO_EG: ['Draft an introduction by chatting "Introduce <PERSON> to <PERSON> about the four color theorem"'],

  INTRO_RESERVED: [
    'intro [someone and someone else about something]',
    'introduce [someone and someone else about something]',
    'connect [someone with someone else about something]',
    'make an intro between [someone and someone else about something]',
  ],

  NOT_INTRO: [' me '],

  CONNECTION_SUBJECT: 'AskFora Intro',

  CONNECTION_EMAIL: (p: Partial<Person>[], c: string[], i: string) => {
    const from_p = p[0];
    const to_p = p[1];
    return `${to_p.displayName},

I'd like to introduce you to another AskFora user, ${from_p.displayName}. I thought you may be able to help find someone who can help${c && c.length ? ' with ':''}${c ? c[0] : ''}.

If you're open to connecting, click here: ${i}

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONFIRM_CONNECTION: (p: Partial<Person>) => {
    return `Would you like an introduction to ${p.displayName}?`;
  },

  CONFIRM_ACCEPT_CONNECTION: (p: Partial<Person>) => {
    return `Would you like to accept ${p.nickName}'s request for an introduction? If so, I'll connect you both by email.`;
  },

  CONFIRM_ACCEPT_WAIT: `Great! Expanding your network is the best way to find people and get better work. I'll be just a minute...`,

  CONFIRM_ANSWERS: ['Yes please', 'Nevermind'],

  CONFIRM_ANSWERS_VIEW_PROFILE: ['Yes please', 'No thank you', 'View profile'],

  CONFIRM_ANSWERS_PROFILE: ['Yes please', 'No thank you'],

  VIEW_PROFILE: ['view', 'profile', 'view profile'],

  CONNECTION_ACCEPTED_SUBJECT: 'AskFora Intro',

  CONNECTION_ACCEPTED_EMAIL: (p: Partial<Person>[]) => {
    const from_p = p[0];
    const to_p = p[1];
    return `${from_p.displayName} and ${to_p.displayName},
    
Happy to connect you both here. I hope this intro is helpful.

${about.ABOUT_EMAIL_SIGNATURE}`;
  },

  CONNECTION_REQUESTED: (p: Partial<Person>) => { return promptInfo(`I've reached out to ${p.displayName} and will let you know if they'd like to connect.`); },

  CONNECTION_SUCCESS: (p: Partial<PersonInfo>) => // { return promptInfo(`I've sent an email to you both to make the introduction. You are now first connections on AskFora and can refer candidates for work`, null, null, [p], true); },
  [ `I've sent an email to you both to make the introduction. In a few minutes you'll have ${p.nick} as a first connection in AskFora.`, `You'll then be able to start a job with ${p.nick} or refer ${p.nick} for jobs with other people in your network.`],

  NOTIFY_CONNECT_REQUEST: 'Connection request',

  NOTIFY_CONNECT_ACCEPT: 'Connection accepted',

  INVALID_INTRO: "I'm sorry, this isn't a valid introduction.",

  AUTH_INTRO: (providers: string[]) => {
    return promptInfo(`To review the introduction, you will need to sign in with a ${stringList(providers, 'or')} account.${providers.length > 1 ? ' Which do you want to use?' : ''}`);
  },

  INVALID_CONNECTION: "I'm sorry, I couldn't make this introduction.",

  AUTH_CONNECTION: (person: Partial<Person>, providers: string[]) => {
    return promptInfo(`To ask for an introduction${person ? ' to ': ''}${person ? person.displayName : ''}, you will need to sign in with a ${stringList(providers, 'or')} account.${providers.length > 1 ? ' Which do you want to use?' : ''}`);
  },


  CONJUNCTION: conjunction,

  CONJ_REG: `[${conjunction.join('|')}]`,

  CONTEXT: ['about ', 'regarding ', ' re ', ' to ', ' on ', ' from ', 'vis a vis', 'pertaining'],

  PROMPT: c => `Who would you like to introduce ${c} to?`,

  PROMPT_BOTH: 'Which two people would you like to introduce?',

  DRAFT: "I've drafted an introduction email that you can",

  EDIT_SEND: 'edit and send',

  MISSING: 'I need the names of two people you know to make an introduction',

  INTRO_SUBJECT: 'Introduction',

  INTRO_EMAIL: (name: string, people: Partial<Person>[], context: string[]): string => {
    let context_str = 'here.';
    if (context && context.length) {
      context_str = `regarding ${context}`;
      if ('.!?,'.indexOf(context_str[context_str.length - 1]) === -1) context_str += '.';
    }

    return `${people[0].nickName} and ${people[1].nickName},

Introducing you both ${context_str}

${introAbout(people[0])}
${introAbout(people[1])}

I'll leave you to connect directly.

-${name}

${about.DRAFT_EMAIL_SIGNATURE}`
  },
};
