import { stringList } from '../utils/format';
import { promptInfo } from '../utils/info';

export default {
  SETTINGS_RESERVED: [
    'set [setting] [true|false]',
    // 'show [setting]',
    'hide [setting]',
    'enable [setting]',
    'disable [setting]',
  ],

  SETTINGS_KWD: [
    'set',
    // 'show',
    'hide',
    'enable',
    'disable',
    'sync',
  ],

  SETTINGS_LOGIN: p => {
    return `Please login using ${stringList(p, 'or')} to view, edit, and share your profile.`;
  },

  SETTINGS_PROFILE: ['I can help you get hired by people in your network. Start by completing your profile then add a Freelance Service Offering.'], 

  /*PLEASE_CONNECT: p => {
    // return [promptInfo(a), promptInfo(`I'm happy to chat more, I just need to learn a bit about you. To get started, you can connect with ${stringList(p, 'or')}`)];
    return [
      promptInfo(`I'll find people who can help you by searching your first and second connections or help you make a public profile you can share with your network.`), 
      promptInfo(`To get started, you can connect with ${stringList(p, 'or')}, wherever you keep your contacts.`),
      promptInfo(`I'm conscientious about how I use any data from your account. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
    ];
  },*/


  SETTINGS_PROMPT_EMAIL: url => promptInfo('I can learn about who you email but I never read the contents of messages. You can give me permission by', url, 'clicking here', [], true),

  SETTINGS_PROMPT_PROJECT_EMAIL: url => [
    promptInfo('If you want the email to come from you, please give me access to your email account.'),
    promptInfo('I will create drafts for you and I can learn people you email with. I never read your messages or make any changes, delete email, or send email unless you ask.'),
    promptInfo('You can give me permission by', url, 'clicking here', [], true),
  ],

  SETTINGS_PROMPT_ORGANIZER: url => promptInfo('I can learn about who you meet and help with reminders and notes about people. You can give me permission by', url, 'clicking here', [], true),

  SETTINGS_PROMPT_CALENDAR: cal => `Do you want to synchronize all events and attendees from ${cal}?`,

  SETTINGS_ANSWERS: ['Yes, please', 'Not right now'],

  INFO: 'info',
  PEOPLE: 'people',
  TASKS: 'tasks',
  NOTES: 'notes',
  MESSAGES: 'messages',
  PROJECTS: 'projects',
};
