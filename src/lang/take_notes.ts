export default {
  NOTES_EG: ["I'll start a new note for you if you tell me 'note' or 'take a note'"],

  NOTES_RESERVED: [
    'take',
    'note',
    'notes',
    'save',
    'done',
    'record',
    // 'rec',
  ],

  NOTES_SHORTCUT: a => {
    if (a) return { name: 'Take notes', value: 'Take notes' };
    else return null;
  },

  NOTES: [
    'take note',
    'take a note',
    'take notes',
    'note',
    'notes',
    'record',
    // 'rec ',
  ],

  SAVE: ['save note', 'save notes', 'done'],

  ADD_REMINDER: ['remember ', 'remind ', ' forget', 'todo', 'to-do', 'next step', 'follow ', 'followup', 'follow-up'],

  CONTEXT: ['about ', 'regarding ', ' re ', ' to ', ' on ', ' from '],

  LISTEN_WITTY: ["Go ahead and 'done' when complete...", "I'm listening, just 'done' when finished...", "Recording, say 'done' when finished...", "Go for it. Remember, say 'done' at the end..."],

  PEOPLE_HINT: 'Find someone to link to your note',

  EVENT_PROMPT: e => {
    return `Are you working on ${e}?`;
  },

  EVENT_ANSWERS: ['Yes', 'Next', 'No, create a blank note'],

  EVENT_ANSWERS_LAST: ['Yes', 'No, create a blank note'],
};
