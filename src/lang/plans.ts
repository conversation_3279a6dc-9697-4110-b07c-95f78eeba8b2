import { promptInfo } from '../utils/info';
import about from './about';

export default {
  ASSIGN_SUBJECT: `You've been assigned a new learning goal`,

  ASSIGN_MESSAGE: (from, to, url) => `${to},

${from} assigned you a new learning goal. Check it out here: ${url}

${about.ABOUT_EMAIL_SIGNATURE}`,

  ASSIGN_PROMPT: url => promptInfo(`You've been assign a new `, url, `learning goal`),

  UNASSIGN_SUBJECT: `You've been unassigned a learning goal`,

  UNASSIGN_MESSAGE: (from, to) => `${to},

${from} unassigned a new learning goal.

${about.ABOUT_EMAIL_SIGNATURE}`,

  UNASSIGN_PROMPT: promptInfo(`You've been unassign a learning goal`),

}