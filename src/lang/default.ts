import { MINUTES } from '../utils/datetime';
import { stringList } from '../utils/format';
import { randomPhrase } from '../utils/funcs';
import { promptInfo } from '../utils/info';

const ACK_LIST = [
  // 'Aye-aye',
  '<PERSON>',
  'On it!',
  'You got it!',
  'Will do',
  'k',
  'Yup',
  'Sure thing',
  'As you wish',
  'It would be my pleasure',
  'Certainly',
  'Absolutely',
];

const ACK_QUESTION_LIST = [
  "I can't help with that yet",
  'Still working on that',
  "I'm still learning how to help with that",
  "I'm not sure how to help with that",
];

function HELLO_EARLY(name) {
  return [
    "Look who's getting a worm today!",
    `Rise and shine ${name}`,
    'Excuse my bed-head',
    `I hope you brought the coffee ${name}`,
    "Isn't it still yesterday?",
    `Ok ${name}, let's do this`,
    "I don't do pep talks this early",
  ];
}

function HELLO_MORNING(name) {
  return [
    `Good morning ${name}!`,
    `How are you today ${name}?`,
    `Any exciting plans for the day ${name}?`,
    'Feels like a take over the world kind of day.',
    "Let's go get this day started.",
    'Ready to rock and roll.',
  ];
}

function HELLO_AFTERNOON(name) {
  return [
    'No siesta today?', 
    `Good afternoon ${name}.`, 
    "How's your day been so far?", 
    "I've got a few more hours in me.", 
    "What's up?", 
    'How can I help?'
  ];
}

function HELLO_EVENING(name) {
  return [
    'Tell me you took a break for dinner.',
    'Ok, just one more e-mail...',
    `${name} why are you online?`,
    "Wouldn't you rather be reading a good book?",
    'Even AIs need a break at the end of the day.',
    `What're you drinking ${name}?`,
  ];
}

function HELLO_NIGHT(name) {
  return [
    `Burning the midnight oil ${name}?`, 
    `${name} I believe it's past your bedtime.`, 
    `${name} It can wait for tomorrow.`, 
    `Is something on your mind ${name}?`];
}

function ANSWER_EARLY(name) {
  return [
    '<Yawn>! It\'s a bit early but I\'m ready to go!'
  ];
}

function ANSWER_MORNING(name) {
  return [
    'Can\'t way to get this day started!',
  ];
}

function ANSWER_AFTERNOON(name) {
  return [
    'Might need some coffee to perk me up.'
  ];
}

function ANSWER_EVENING(name) {
  return [
    'I\'ve got a few more hours in me'
  ];
}

function ANSWER_NIGHT(name) {
  return [
    'It\'s getting pretty late!'
  ];
}

export default {
  HELLO_KWD: [
    'hello',
    'hi',
    'hey',
    'hiya',
    'howdy',
    'morning',
    "mornin'",
    "g'morning",
    "g'mornin",
    "g'mornin'",
    'goodmorning',
    'good morning',
    'good day',
    'good afternoon',
    'afternoon',
    'good evening',
    'evneing',
    'good night',
    'night',
    "g'night",
    "'night",
  ],

  HELLO_NIGHT: name => {
    return [`Burning the midnight oil ${name}?`, `${name} I believe it's past your bedtime.`, `${name} It can wait for tomorrow.`, `Is something on your mind ${name}?`];
  },

  ACKNOWLEDGE_QUESTION: () => {
    return randomPhrase(ACK_QUESTION_LIST);
  },

  ACKNOWLEDGE_QUESTION_LIST: ACK_QUESTION_LIST,

  ANSWER_QUESTION: (name: string, offset = 0): string[] => {
    // use the offset to make UTC appear like local (for the user, not this machine)
    const local_now = MINUTES(new Date(), -1 * offset);
    const hour = local_now.getUTCHours();

    let hello = null;
    if (!name) name = '';

    if (hour > 22) hello = ANSWER_NIGHT(name);
    else if (hour > 18) hello = ANSWER_EVENING(name);
    else if (hour > 12) hello = ANSWER_AFTERNOON(name);
    else if (hour > 6) hello = ANSWER_MORNING(name);
    else if (hour > 4) hello = ANSWER_EARLY(name);
    else hello = ANSWER_NIGHT(name);

    return [randomPhrase(hello)];
  }, 

  ACKNOWLEDGE: () => randomPhrase(ACK_LIST),

  ACKNOWLEDGE_PHRASES: ACK_LIST,

  ACKNOWLEDGE_NEGATIVE: () => {
    return randomPhrase(['No worries', 'Ok', 'Sure']);
  },

  HIDE_QUIP: 30000,

  PLEASE_CONNECT: p => {
    // return [funcs.promptInfo(a), funcs.promptInfo(`I'm happy to chat more, I just need to learn a bit about you. To get started, you can connect with ${stringList(p, 'or')}`)];
    return [
      // promptInfo(`I'll find people who can help you by searching your first and second connections or help you make a public profile you can share with your network.`), 
      promptInfo(`Hi, I'm Fora. I can help you make better use of your professional network and develop your skills through learning goals.`),
      promptInfo(`I'm conscientious about how I use your data. If you want to know more, here is my`, '/privacy', 'privacy policy'), 
      promptInfo(`To get started, you can connect with ${stringList(p, 'or')}`),
    ];
  },

  PRIVACY: 'Privacy & Security',

  FIRST_PROJECT: p => [
    promptInfo(`I'm ready to start searching once you login with ${stringList(p, 'or')} account`),
    promptInfo("If you have questions, I'm happy to share more about myself"),
  ],

  HELP_HINT: 'How can you help?',

  HELP_ANSWERS: ['Tell me more'],

  DEFAULT_HELP: 'Do you need help with something?',

  DEFAULT_HELP_ANSWERS: ['FAQ', 'Feedback', 'What can you do?'],

  QUESTION_ANSWERS: ['What can you do?'],

  CONNECT_HINT: 'Link',

  NEVERMIND: 'Nevermind',

  ALL_HELLO: (name) => {
    return [
      ...HELLO_EARLY(name),
      ...HELLO_MORNING(name),
      ...HELLO_AFTERNOON(name),
      ...HELLO_EVENING(name),
      ...HELLO_NIGHT(name),
    ]
  },

  SAY_HELLO: (name: string, offset = 0): string[] => {
    // use the offset to make UTC appear like local (for the user, not this machine)
    const local_now = MINUTES(new Date(), -1 * offset);
    const hour = local_now.getUTCHours();

    let hello = null;
    if (!name) name = '';

    if (hour > 22) hello = HELLO_NIGHT(name);
    else if (hour > 18) hello = HELLO_EVENING(name);
    else if (hour > 12) hello = HELLO_AFTERNOON(name);
    else if (hour > 6) hello = HELLO_MORNING(name);
    else if (hour > 4) hello = HELLO_EARLY(name);
    else hello = HELLO_NIGHT(name);

    return [randomPhrase(hello)];
  },
};
