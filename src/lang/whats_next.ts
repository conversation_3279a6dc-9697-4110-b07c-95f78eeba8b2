import { stringList } from '../utils/format';
import { promptInfo } from '../utils/info';

export default {
  WHAT_EG: [`Tip: Check what's coming up. Just ask me "What's next?"`],

  WHAT_RESERVED: [
    'what [do I have tomorrow]',
    "what's [my next meeting?]",
    // 'who',
    // "who's",
    'check [my schedule]',
    "tell [me what's on my calendar]",
    'get [my schedule]',
    'triage [my email]',
    'schedule [today]',
    'calendar [on Monday]',
    'agenda [next week]',
    'events [today]',
    'tasks',
    'todos',
    'what next',
    'suggest',
  ],

  WHAT_SHORCUTS: a => {
    if (a) return { name: "What's next?", value: "What's next?" };
    else return null;
  },

  WHAT_KWD: [
    'what',
    "what's",
    'what is',
    'schedule',
    'calendar',
    'agenda',
    'triage',
    'email',
    'messages',
    'tasks',
    // 'todo',
    'todos',
    'events',
    'check',
    'mail',
    'to-do',
    'to-dos',
    'suggest',
  ],

  SUGGEST_KWD: [ 'suggest', 'about', 'should' ],

  SUGGEST_NO_SUGGESTIONS: "I'm still learning who might be interesting for you to meet.",

  // starts with what
  /*WHATS_NEXT : [
    'next', //what's next
    ' up', //what's up today?
    ' do', //what should I do
    ' doing', //what am I doing
    'was ', //what was...
  ],*/

  // starts with this list
  WHAT_ELSE: [
    'next',
    'skip it',
    'skip to',
    'more', // more
    'after', // after that?
    'what else',
    'also', // also?
    'other', // other than that?
    'then', // then what?
    // 'page' //page
  ],

  START_OVER: ['start over', 'restart', 'begin again', 'again', 'over'],

  // combo of these two
  INQUERY_START: [
    'tell', // tell me what's on my calendar
    'get', // get my schedule
  ],

  INQUERY_KEY: [
    'about',
    // ' info',
    'what',
    'todo',
    'todos',
    'to-do',
    'to-dos',
    'to do',
    'to dos',
    // ' more'
  ],

  WITH: ['with'],

  NOT_ELSE: ['info'],

  DUE: ['due'],

  LEARN_SKILLS: s => `Try learning about ${stringList(s, 'or')}`,

  SUGGEST_PEOPLE: p => `You might be interested in chatting with ${stringList(p, 'or')}`,

  NEXT_WITTY: [
    'Done for the day. Go Fishing!',
    'You finally have time to crack a good book.',
    'Call your family, they miss you.',
    'Take a hike...seriously, get up and stretch your legs.',
    'Clean out your spam folder.',
    'Time for inbox zero? j/k.',
  ],

  NEXT_WITTY_EMPTY: ['Looks like you have a light day. ', 'Time to GSD! ', 'Everyone needs a recovery day. ', "People who don't appreciate free time call this slacking. "],

  NEXT_WITTY_MEDIUM: [
    'Remember to get up and stretch. ',
    'A healthy balance makes for a productive day. ',
    "Scheduling Zen, you've achieved balance",
    'You have some breathing room.',
    'Keep some free time to refocus.',
  ],

  NEXT_WITTY_FULL: [
    'Your schedule is packed. Remember to eat. ',
    'Busy day! You forgot to add bathroom breaks. ',
    'Brace yourself...I hope you got a good nights sleep. ',
    'Tell me one of these meetings is really nap time. ',
  ],

  ALL_DAY: e => {
    return promptInfo('All day', e.link, e.title);
  },

  ALSO_DAY: e => {
    return promptInfo('You also have', e.link, e.title);
  },

  NEXT_EVENT: (t, e, p) => {
    return promptInfo(`On ${t} you have`, e.link, e.title, p);
  },

  ACTIVE_EVENT: (t, e, p) => {
    return promptInfo(`You're scheduled until ${t} for`, e.link, e.title, p);
  },

  NOTES: promptInfo('Send me notes to record'),

  LAST_EVENT: (t, e) => {
    return promptInfo(`Ended at ${t}`, e.link, e.title);
  },

  NO_EVENT: e => {
    return promptInfo('Nothing is coming up. You can send me additional notes or follow up on', e.link, e.title);
  },

  STARTED_DRAFT: p =>  `You started a draft ${p}`,

  LAST_EMAIL: t => {
    return `You last emailed on ${t} about`;
  },

  FOLLOW_UP: () => {
    return 'Follow up on';
  },

  NEXT_TASK: (i, t, d = null, f = false) => {
    const url = `/note/${i}`;
    if (!d) return promptInfo('Time to work on', url, t);
    if (f) return promptInfo(`You've got a due date coming up at ${d}. Get going on`, url, t);
    return promptInfo(`You've been overdue since ${d}. Time to get to`, url, t);
  },

  MAIL_LINK: id => {
    return `https://mail.google.com/mail/u/0/#all/${id}`;
  },

  FINISHED_ANSWERS: ['Next', 'Notes', 'Done', 'Follow up'],

  MESSAGE_ANSWERS: message => {
    if (message.draft) return ['Next', 'Delete'];
    return ['Next', 'Read', 'Archive'];
  },

  ATTENDEE_ANSWERS: event => {
    let answers = ['Next', 'Notes'];
    if (event.people && event.people.length === 2) {
      for (const item in event.people) {
        const person = event.people[item];
        if (!person.self) answers.push(person.displayName);
      }
      answers.push('Late');
    } else if (event.people && event.people.length === 1) answers = answers.concat(['Done', 'Cancel']);
    else answers = answers.concat(['Running late']);

    return answers;
  },

  TASK_ANSWERS: task => {
    let answers = []; //'Next'];
    // if (task.people && task.people.length) answers.push('Draft');
    answers = answers.concat(['Complete', 'Do later', 'Nevermind']);
    return answers;
  },

  DAY_INFO_TITLE: (ec, pc) => {
    if (!ec) return 'You have no events scheduled today';
    const you_have = `You have ${ec} events`;
    if (pc) return `${you_have} with ${pc} people`;
    return `${you_have} today`;
  },
};
