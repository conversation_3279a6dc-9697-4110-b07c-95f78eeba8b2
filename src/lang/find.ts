function genFind(): string[] {
  const ACT = [
    'can you find me ',
    'can you find ',
    'could you find me ',
    'could you find ',
    'please find me ',
    'please find ',
    'find me ',
    'find ',
    'i need ',
    'i want ',
    `i'm looking for `,
    `i'm searching for `,
    'who do i know ',
    'do I know ',
    'show me ',
    'show ',
    'need ',
    '',
  ];

  const NOUN = [
    'someone ',
    'people ',
    'a person ',
    'an expert ',
    'a contact ',
    'an ',
    'a ',
    '',
  ];

  const INT = [
    'who works here and ',
    'who works here ',
    'who works ',
    '',
  ];

  const QUAL = [
    'that knows about',
    'that knows',
    'that is from',
    'that is at',
    'that is a',
    'that is',
    `that's from`,
    `that's at`,
    `that's a`,
    `that's`,
    'who knows about',
    'who knows',
    `who's from`,
    `who's at`,
    `who's a`,
    'who is',
    'and knows about',
    'and knows',
    'from',
    'that',
    'who',
    'at',
    'on',
    '',
  ];

  const ETC = [
    'learn',
    'tell',
    'can',
    'will',
    'i',
  ];

  const kwds: string [] = [];
  for (const a of ACT) {
    for(const n of NOUN) {
      for(const i of INT) {
        for(const q of QUAL) {
          kwds.push(`${a}${n}${i}${q}`.trim())
        }
      }
    }
  }

  return [...kwds, ...ETC].filter(x => x?.length);
}

export default {
  FIND_PERSON: 'Find Person',
  FIND_DESC: 'Find people based on search filters',
  FIND_EG: ['I can find people you know based on a skill, name, or other criteria. You can ask me to "Find a when I last met an AI expert "'],
  FIND_RESERVED: [
    'who did I meet last month',
    'who should I meet',
    'who am I meeting today?',
    'who [do I know at AskFora]',
    "who's [Arthur Cayley]",
    'tell [me about someone]',
    'details [about a person]',
    'get [a designer]',
    'find [a developer]',
    'whois [someone]',
    'person',
    'people',
    'more',
    'more info',
    'info about [someone]',
    'list',
    'lookup [someone at company]',
    'i',
    'im',
    "i'm",
    'seeking',
    'looking',
    'searching',
    'want',
    'need',
  ],

  FIND_KWD: genFind(),

  _FIND_KWD: [
    'find someone that knows about',
    'find someone that knows',
    'find someone that is at',
    'find someone that is from',
    'find someone that is',
    'find someone that\'s at',
    'find someone that\'s from',
    'find someone that\'s',
    'find someone who knows about',
    'find someone who knows',
    'find someone who is at',
    'find someone who is from',
    'find someone who is',
    'find someone who\'s at',
    'find someone who\'s from',
    'find someone who\'s',
    'find someone from',
    'find someone at',
    'find someone with',
    'find someone',
    'find people that know about',
    'find people that know',
    'find people that are',
    'find people who know about',
    'find people who know',
    'find people who are',
    'find people from',
    'find people at',
    'find people',
    'find some people',
    'find a person who',
    'find a person',
    'find a contact',
    'find an expert that knows about',
    'find an expert that knows',
    'find an expert that can',
    'find an expert that',
    'find an expert who knows about',
    'find an expert who knows',
    'find an expert who',
    'find an expert from',
    'find an expert at',
    'find an expert',
    'find experts that know about',
    'find experts that know',
    'find experts that',
    'find experts who know about',
    'find experts who know',
    'find experts who',
    'find experts from',
    'find experts at',
    'find experts',
    'find me a',
    'find me',
    'find the',
    'find an',
    'find a',
    'find',
    'i need someone that knows about',
    'i need someone that knows',
    'i need someone that can',
    'i need someone that is from',
    'i need someone that is at',
    'i need someone that is',
    'i need someone that',
    'i need someone who knows about',
    'i need someone who knows',
    'i need someone who can',
    'i need someone who is from',
    'i need someone who is at',
    'i need someone who is',
    'i need someone who',
    'i need someone from',
    'i need someone with',
    'i need someone at',
    'i need someone',
    'i need',
    'i want',
    `i'm looking for someone who knows about`,
    `i'm looking for someone that knows about`,
    `i'm looking for someone who knows`,
    `i'm looking for someone that knows`,
    `i'm looking for someone who`,
    `i'm looking for someone that`,
    `i'm looking for someone from`,
    `i'm looking for someone at`,
    `i'm looking for a`,
    `i'm looking for`,
    `i'm searching for someone who knows about`,
    `i'm searching for someone that knows about`,
    `i'm searching for someone who knows`,
    `i'm searching for someone that knows`,
    `i'm searching for someone who can`,
    `i'm searching for someone that can`,
    `i'm searching for someone who`,
    `i'm searching for someone that`,
    `i'm searching for someone from`,
    `i'm searching for someone at`,
    `i'm searching for a`,
    `i'm searching for`,
    'need someone who',
    'need someone from',
    'need someone that',
    'need someone at',
    'need someone',
    'need a',
    'who do I know that knows about',
    'who do I know that knows',
    'who do I know that is from',
    'who do I know that is at',
    'who do I know that is',
    'who do I know that\'s from',
    'who do I know that\'s at',
    'who do I know that\'s',
    'who do I know that has',
    'who do I know who knows about',
    'who do I know who knows',
    'who do I know who is from',
    'who do I know who is at',
    'who do I know who is',
    'who do I know who\'s from',
    'who do I know who\'s at',
    'who do I know who\'s',
    'who do I know who has',
    'who do I know from',
    'who do I know that',
    'who do I know who',
    'who do I know at',
    'who do I know',
    'who knows about',
    'who knows',
    'who know',
    'who is',
    'who does',
    'who do i know',
    'who can',
    'who',
    'do I know who',
    'do I know that',
    'do I know is',
    'do I know from',
    'do I know',
    'learn',
    'tell',
    'can', 
    'will',
    'i',
    'need',
    'show',
],

  FIND_WORDS: [
    'am',
    'at',
    'when',
    'meeting',
    'meet',
    'today',
    'yesterday',
    'tomorrow',
    'week',
    'month',
    'year',
    'this',
    'next',
    'know',
  ],

  FIND_SUCCESS: c => `I found some${c === 1 ? 'one': ' folks'} who match${c === 1 ? 'es': ''} your search`,

  FIND_NONE: `I couldn't find anyone for you.`,

  DISCOVER_URL: `Are there any social profiles you want me to look at?`,

  DISCOVER_SUCCESS: `I've updated your contacts with what I learned`,

  SEARCHING: `I'm searching for people.`,
}