import _ from 'lodash';

import { WisePluginState } from '../types/plugins';

import { DAYS } from '../utils/datetime';
import { formatKeyValues } from '../utils/format';
import { promptInfo } from '../utils/info';

export default {

  CONNECT_WISE: 'connect wise',

  WISE_PROMPT: [
    promptInfo('I have a few questions in order to link your pay out account. AskFora securely stores your bank information at Wise. You can learn more about Wise ', 'https://wise.com/terms-and-conditions', 'here'),
    promptInfo(`You'll need your banking information handy. I don't keep any of your information at AskFora, these questions come from and get transmitted directly to <PERSON> to create your account.`),
  ],

  WISE_CURRENCY: 'What is your local currency?',

  WISE_COUNTRY: 'In which country is your bank account?',

  WISE_ACCOUNT_TYPE: 'What type of account do you use?',

  SKIP: 'Skip',

  SKIP_KWD: 'skip',

  WISE_FIELD_PROMPT: (p: string) => `What is your ${p}`,

  WISE_CONFIRM: (p: string) => `Please confirm your ${p}`,

  WISE_FULL_CONFIRM: (c: WisePluginState) => {
    const name = `Name: ${c.accountHolderName}`;
    const currency = `Currency: ${c.currency}`;
    const name_map = {};
    const order = [];
    const req = c.requirements.find(r => r.type === c.account_type);
    if (req && req.fields) {
      for (const field of req.fields) {
        if (field.group && field.group.length) {
          const key = field.group[0].key;
          name_map[key] = field.name;
          if (key.includes('.')) {
            const kp = key.split('.').slice(0, -1);
            for (const k of kp) {
              if (!order.includes(k)) order.push(k);
            }
          }
          order.push(key);
        }
      }
    }

    const details = formatKeyValues(c.details, order, (a,v) => [name_map[a] ? name_map[a] : _.capitalize(a), v]);

    return ['Does this look right?', name, currency].concat(details.split('\n'));
  },

  WISE_CONNECTING: `One second while I set up your account...`,

  WISE_CONNECTED: "Congratulations, you're ready to get paid for jobs!",

  CONFIRM_DISCONNECT: 'Are you sure you want to disconnect your pay out account?',

  CANNOT_DISCONNECT: 'You need to keep a pay out account connected while you have a job in progress.',

  DISCONNECTED: 'Your account has been disconnected. You will need to connect a pay out account in order to be paid for jobs.',

  WISE_ERROR: e => {
    const errs = [];
    if (e) {
      if (e.errors) for (const err of e.errors) errs.push(`${err.message}: ${err.path}`)
      else errs.push(JSON.stringify(e));
    }
    return errs.length ? errs : [`I ran into an error, please check your pay out information and try again.`];
  },

  ERROR: {
    errors: [
      {
        code: 'NOT_VALID',
        message: 'Unknown routing number. Please check the number and try again.',
        path: 'accountNumber',
        arguments: ['currency', 'XXX']
      }
    ],
  },

  FAKE_PROFILE: {
      id: ********,
      type: 'business',
      details: {
        name: 'AskFora, Co',
        registrationNumber: '82-4735247',
        acn: null,
        abn: null,
        arbn: null,
        companyType: 'FOR_PROFIT_CORPORATION',
        companyRole: null,
        descriptionOfBusiness: 'FREELANCE_PLATFORMS',
        primaryAddress: ********,
        webpage: 'https://askfora.com',
        businessCategory: 'CONSULTING_IT_BUSINESS_SERVICES',
        businessSubCategory: 'FREELANCE_PLATFORMS'
      }
    },

  FAKE_QUOTE: (a,c) => { return {
    "sourceAmount": a,
    "guaranteedTargetAmountAllowed": false,
    "targetAmountAllowed": true,
    "paymentOptions": [
      {
        "formattedEstimatedDelivery": "by January 25th, 2022",
        "estimatedDeliveryDelays": [],
        "allowedProfileTypes": [
          "PERSONAL",
          "BUSINESS"
        ],
        "feePercentage": 0.0144,
        "estimatedDelivery": DAYS(new Date(), 1).toISOString(), 
        "sourceCurrency": "USD",
        "targetCurrency": c,
        "sourceAmount": a,
        "targetAmount": 98.56,
        "payOut": "BANK_TRANSFER",
        "fee": {
          "transferwise": 1.13,
          "payIn": 0.31,
          "discount": 0,
          "total": 1.44,
          "priceSetId": 132,
          "partner": 0
        },
        "price": {
          "priceSetId": 132,
          "total": {
            "type": "TOTAL",
            "label": "Total fees",
            "value": {
              "amount": 1.44,
              "currency": "USD",
              "label": "1.44 USD"
            }
          },
          "items": [
            {
              "type": "PAYIN",
              "label": "fee",
              "value": {
                "amount": 0.31,
                "currency": "USD",
                "label": "0.31 USD"
              }
            },
            {
              "type": "TRANSFERWISE",
              "label": "Our fee",
              "value": {
                "amount": 1.13,
                "currency": "USD",
                "label": "1.13 USD"
              }
            }
          ]
        },
        "payIn": "DIRECT_DEBIT",
        "disabled": false
      },
    ],
    "notices": [],
    "transferFlowConfig": {
      "highAmount": {
        "showFeePercentage": false,
        "trackAsHighAmountSender": false,
        "showEducationStep": false,
        "offerPrefundingOption": false,
        "overLimitThroughCs": false
      }
    },
    "rateTimestamp": new Date().toISOString(),
    "clientId": "transferwise-personal-tokens",
    "id": "************************************",
    "type": "REGULAR",
    "status": "PENDING",
    "profile": ********,
    "rate": 1,
    "sourceCurrency": "USD",
    "targetCurrency": c,
    "createdTime": new Date().toISOString(),
    "user": 5858965,
    "rateType": "FIXED",
    "rateExpirationTime": DAYS(new Date(), 1).toISOString(),
    "payOut": "BANK_TRANSFER",
    "guaranteedTargetAmount": false,
    "providedAmountType": "SOURCE",
    "expirationTime": DAYS(new Date(), 1).toISOString(),
    "payInCountry": "US",
    "funding": "POST"
  } },
 
  FAKE_TRANSFER_REQUIREMENTS: [
      {
        "type": "transfer",
        "usageInfo": null,
        "fields": [
          {
            "name": "Transfer reference",
            "group": [
              {
                "key": "reference",
                "name": "Transfer reference",
                "type": "text",
                "refreshRequirementsOnChange": false,
                "required": false,
                "displayFormat": null,
                "example": null,
                "minLength": null,
                "maxLength": 50,
                "validationRegexp": null,
                "validationAsync": null,
                "valuesAllowed": null
              }
            ]
          }
        ]
      }
    ],

  FAKE_ACCOUNT_REQUIREMENTS: [
    {
      "type": "ghana_local",
      "title": "Local bank account",
      "usageInfo": null,
      "fields": [
        {
          "name": "Recipient type",
          "group": [
            {
              "key": "legalType",
              "name": "Recipient type",
              "type": "select",
              "refreshRequirementsOnChange": true,
              "required": true,
              "displayFormat": null,
              "example": "",
              "minLength": null,
              "maxLength": null,
              "validationRegexp": null,
              "validationAsync": null,
              "valuesAllowed": [
                {
                  "key": "PRIVATE",
                  "name": "Person"
                },
                {
                  "key": "BUSINESS",
                  "name": "Business"
                }
              ]
            }
          ]
        },
        {
          "name": "Bank name",
          "group": [
            {
              "key": "bankCode",
              "name": "Bank name",
              "type": "select",
              "refreshRequirementsOnChange": true,
              "required": true,
              "displayFormat": null,
              "example": "Please choose recipient's bank",
              "minLength": null,
              "maxLength": null,
              "validationRegexp": null,
              "validationAsync": null,
              "valuesAllowed": [
                {
                  "key": "",
                  "name": "Please choose recipient's bank"
                },
                {
                  "key": "070100",
                  "name": "ARB Apex and Rural Bank"
                },
                {
                  "key": "030100",
                  "name": "Absa Bank Ghana Limited"
                },
                {
                  "key": "280100",
                  "name": "Access Bank"
                },
                {
                  "key": "080100",
                  "name": "Agricultural Development Bank"
                },
                {
                  "key": "990001",
                  "name": "Bank of Africa"
                },
                {
                  "key": "990002",
                  "name": "Bank of Ghana"
                },
                {
                  "key": "140100",
                  "name": "Cal Bank"
                },
                {
                  "key": "340100",
                  "name": "Consolidated Bank Ghana"
                },
                {
                  "key": "130100",
                  "name": "Ecobank"
                },
                {
                  "key": "200100",
                  "name": "FBN Bank"
                },
                {
                  "key": "240100",
                  "name": "Fidelity Bank"
                },
                {
                  "key": "170100",
                  "name": "First Atlantic Bank"
                },
                {
                  "key": "330100",
                  "name": "First National Bank"
                },
                {
                  "key": "390100",
                  "name": "GHL BANK LTD"
                },
                {
                  "key": "040100",
                  "name": "Ghana Commercial Bank Limited"
                },
                {
                  "key": "230100",
                  "name": "Guaranty Trust Bank"
                },
                {
                  "key": "050100",
                  "name": "National Investment Bank"
                },
                {
                  "key": "990007",
                  "name": "Omnibank Ghana Ltd"
                },
                {
                  "key": "180100",
                  "name": "Prudential Bank"
                },
                {
                  "key": "110100",
                  "name": "Republic Bank"
                },
                {
                  "key": "090100",
                  "name": "Societe General"
                },
                {
                  "key": "190100",
                  "name": "Stanbic Bank"
                },
                {
                  "key": "020100",
                  "name": "Standard Chartered Bank"
                },
                {
                  "key": "060100",
                  "name": "United Bank for Africa"
                },
                {
                  "key": "100100",
                  "name": "Universal Merchant Bank"
                },
                {
                  "key": "120100",
                  "name": "Zenith Bank"
                }
              ]
            }
          ]
        },
        {
          "name": "Branch name",
          "group": [
            {
              "key": "branchCode",
              "name": "Branch name",
              "type": "select",
              "refreshRequirementsOnChange": false,
              "required": true,
              "displayFormat": null,
              "example": "Please choose recipient's branch",
              "minLength": null,
              "maxLength": null,
              "validationRegexp": null,
              "validationAsync": null,
              "valuesAllowed": [
                {
                  "key": "GH010101",
                  "name": "Accra [GH010101]"
                },
                {
                  "key": "GH010103",
                  "name": "Agona Swedru [GH010103]"
                },
                {
                  "key": "GH010401",
                  "name": "Takoradi [GH010401]"
                },
                {
                  "key": "GH010402",
                  "name": "Sefwi Boako [GH010402]"
                },
                {
                  "key": "GH010601",
                  "name": "Kumasi [GH010601]"
                },
                {
                  "key": "GH010701",
                  "name": "Sunyani [GH010701]"
                },
                {
                  "key": "GH010801",
                  "name": "Tamale [GH010801]"
                },
                {
                  "key": "GH011101",
                  "name": "Hohoe [GH011101]"
                }
              ]
            }
          ]
        },
        {
          "name": "Account number",
          "group": [
            {
              "key": "accountNumber",
              "name": "Account number",
              "type": "text",
              "refreshRequirementsOnChange": false,
              "required": true,
              "displayFormat": null,
              "example": "*************",
              "minLength": 8,
              "maxLength": 20,
              "validationRegexp": "^\\d{8,20}$",
              "validationAsync": null,
              "valuesAllowed": null
            }
          ]
        },
        {
          "name": "Country",
          "group": [
            {
              "key": "address.country",
              "name": "Country",
              "type": "select",
              "refreshRequirementsOnChange": true,
              "required": true,
              "displayFormat": null,
              "example": "Choose a country",
              "minLength": null,
              "maxLength": null,
              "validationRegexp": null,
              "validationAsync": null,
              "valuesAllowed": [
                {
                  "key": "AL",
                  "name": "Albania"
                },
                {
                  "key": "DZ",
                  "name": "Algeria"
                },
                {
                  "key": "AS",
                  "name": "American Samoa"
                },
                {
                  "key": "AD",
                  "name": "Andorra"
                },
                {
                  "key": "AO",
                  "name": "Angola"
                },
                {
                  "key": "AI",
                  "name": "Anguilla"
                },
                {
                  "key": "AQ",
                  "name": "Antarctica"
                },
                {
                  "key": "AG",
                  "name": "Antigua and Barbuda"
                },
                {
                  "key": "AR",
                  "name": "Argentina"
                },
                {
                  "key": "AM",
                  "name": "Armenia"
                },
                {
                  "key": "AW",
                  "name": "Aruba"
                },
                {
                  "key": "AU",
                  "name": "Australia"
                },
                {
                  "key": "AT",
                  "name": "Austria"
                },
                {
                  "key": "AZ",
                  "name": "Azerbaijan"
                },
                {
                  "key": "BS",
                  "name": "Bahamas"
                },
                {
                  "key": "BH",
                  "name": "Bahrain"
                },
                {
                  "key": "BD",
                  "name": "Bangladesh"
                },
                {
                  "key": "BB",
                  "name": "Barbados"
                },
                {
                  "key": "BY",
                  "name": "Belarus"
                },
                {
                  "key": "BE",
                  "name": "Belgium"
                },
                {
                  "key": "BZ",
                  "name": "Belize"
                },
                {
                  "key": "BJ",
                  "name": "Benin"
                },
                {
                  "key": "BM",
                  "name": "Bermuda"
                },
                {
                  "key": "BT",
                  "name": "Bhutan"
                },
                {
                  "key": "BO",
                  "name": "Bolivia"
                },
                {
                  "key": "BQ",
                  "name": "Bonaire"
                },
                {
                  "key": "BA",
                  "name": "Bosnia and Herzegovina"
                },
                {
                  "key": "BW",
                  "name": "Botswana"
                },
                {
                  "key": "BV",
                  "name": "Bouvet Island"
                },
                {
                  "key": "BR",
                  "name": "Brazil"
                },
                {
                  "key": "IO",
                  "name": "British Indian Ocean Territory"
                },
                {
                  "key": "VG",
                  "name": "British Virgin Islands"
                },
                {
                  "key": "BN",
                  "name": "Brunei Darussalam"
                },
                {
                  "key": "BG",
                  "name": "Bulgaria"
                },
                {
                  "key": "BF",
                  "name": "Burkina Faso"
                },
                {
                  "key": "BI",
                  "name": "Burundi"
                },
                {
                  "key": "KH",
                  "name": "Cambodia"
                },
                {
                  "key": "CM",
                  "name": "Cameroon"
                },
                {
                  "key": "CA",
                  "name": "Canada"
                },
                {
                  "key": "CV",
                  "name": "Cape Verde"
                },
                {
                  "key": "KY",
                  "name": "Cayman Islands"
                },
                {
                  "key": "CF",
                  "name": "Central African Republic"
                },
                {
                  "key": "TD",
                  "name": "Chad"
                },
                {
                  "key": "CL",
                  "name": "Chile"
                },
                {
                  "key": "CN",
                  "name": "China"
                },
                {
                  "key": "CX",
                  "name": "Christmas Island"
                },
                {
                  "key": "CC",
                  "name": "Cocos (Keeling) Islands"
                },
                {
                  "key": "CO",
                  "name": "Colombia"
                },
                {
                  "key": "KM",
                  "name": "Comoros"
                },
                {
                  "key": "CG",
                  "name": "Congo"
                },
                {
                  "key": "CD",
                  "name": "Congo, the Democratic Republic of the"
                },
                {
                  "key": "CK",
                  "name": "Cook Islands"
                },
                {
                  "key": "CR",
                  "name": "Costa Rica"
                },
                {
                  "key": "HR",
                  "name": "Croatia"
                },
                {
                  "key": "CW",
                  "name": "Curaçao"
                },
                {
                  "key": "CY",
                  "name": "Cyprus"
                },
                {
                  "key": "CZ",
                  "name": "Czech Republic"
                },
                {
                  "key": "CI",
                  "name": "Côte d''''Ivoire"
                },
                {
                  "key": "DK",
                  "name": "Denmark"
                },
                {
                  "key": "DJ",
                  "name": "Djibouti"
                },
                {
                  "key": "DM",
                  "name": "Dominica"
                },
                {
                  "key": "DO",
                  "name": "Dominican Republic"
                },
                {
                  "key": "EC",
                  "name": "Ecuador"
                },
                {
                  "key": "EG",
                  "name": "Egypt"
                },
                {
                  "key": "SV",
                  "name": "El Salvador"
                },
                {
                  "key": "GQ",
                  "name": "Equatorial Guinea"
                },
                {
                  "key": "EE",
                  "name": "Estonia"
                },
                {
                  "key": "ET",
                  "name": "Ethiopia"
                },
                {
                  "key": "FK",
                  "name": "Falkland Islands"
                },
                {
                  "key": "FO",
                  "name": "Faroe Islands"
                },
                {
                  "key": "FJ",
                  "name": "Fiji"
                },
                {
                  "key": "FI",
                  "name": "Finland"
                },
                {
                  "key": "FR",
                  "name": "France"
                },
                {
                  "key": "GF",
                  "name": "French Guiana"
                },
                {
                  "key": "PF",
                  "name": "French Polynesia"
                },
                {
                  "key": "TF",
                  "name": "French Southern Territories"
                },
                {
                  "key": "GA",
                  "name": "Gabon"
                },
                {
                  "key": "GM",
                  "name": "Gambia"
                },
                {
                  "key": "GE",
                  "name": "Georgia"
                },
                {
                  "key": "DE",
                  "name": "Germany"
                },
                {
                  "key": "GH",
                  "name": "Ghana"
                },
                {
                  "key": "GI",
                  "name": "Gibraltar"
                },
                {
                  "key": "GR",
                  "name": "Greece"
                },
                {
                  "key": "GL",
                  "name": "Greenland"
                },
                {
                  "key": "GD",
                  "name": "Grenada"
                },
                {
                  "key": "GP",
                  "name": "Guadeloupe"
                },
                {
                  "key": "GU",
                  "name": "Guam"
                },
                {
                  "key": "GT",
                  "name": "Guatemala"
                },
                {
                  "key": "GG",
                  "name": "Guernsey"
                },
                {
                  "key": "GN",
                  "name": "Guinea"
                },
                {
                  "key": "GW",
                  "name": "Guinea-Bissau"
                },
                {
                  "key": "GY",
                  "name": "Guyana"
                },
                {
                  "key": "HT",
                  "name": "Haiti"
                },
                {
                  "key": "HM",
                  "name": "Heard Island and McDonald Islands"
                },
                {
                  "key": "HN",
                  "name": "Honduras"
                },
                {
                  "key": "HK",
                  "name": "Hong Kong"
                },
                {
                  "key": "HU",
                  "name": "Hungary"
                },
                {
                  "key": "IS",
                  "name": "Iceland"
                },
                {
                  "key": "IN",
                  "name": "India"
                },
                {
                  "key": "ID",
                  "name": "Indonesia"
                },
                {
                  "key": "IE",
                  "name": "Ireland"
                },
                {
                  "key": "IM",
                  "name": "Isle of Man"
                },
                {
                  "key": "IL",
                  "name": "Israel"
                },
                {
                  "key": "IT",
                  "name": "Italy"
                },
                {
                  "key": "JM",
                  "name": "Jamaica"
                },
                {
                  "key": "JP",
                  "name": "Japan"
                },
                {
                  "key": "JE",
                  "name": "Jersey"
                },
                {
                  "key": "JO",
                  "name": "Jordan"
                },
                {
                  "key": "KZ",
                  "name": "Kazakhstan"
                },
                {
                  "key": "KE",
                  "name": "Kenya"
                },
                {
                  "key": "KI",
                  "name": "Kiribati"
                },
                {
                  "key": "XK",
                  "name": "Kosovo"
                },
                {
                  "key": "KW",
                  "name": "Kuwait"
                },
                {
                  "key": "KG",
                  "name": "Kyrgyzstan"
                },
                {
                  "key": "LA",
                  "name": "Laos"
                },
                {
                  "key": "LV",
                  "name": "Latvia"
                },
                {
                  "key": "LB",
                  "name": "Lebanon"
                },
                {
                  "key": "LS",
                  "name": "Lesotho"
                },
                {
                  "key": "LR",
                  "name": "Liberia"
                },
                {
                  "key": "LI",
                  "name": "Liechtenstein"
                },
                {
                  "key": "LT",
                  "name": "Lithuania"
                },
                {
                  "key": "LU",
                  "name": "Luxembourg"
                },
                {
                  "key": "MO",
                  "name": "Macao"
                },
                {
                  "key": "MK",
                  "name": "Macedonia, Former Yugoslav Republic of"
                },
                {
                  "key": "MG",
                  "name": "Madagascar"
                },
                {
                  "key": "MW",
                  "name": "Malawi"
                },
                {
                  "key": "MY",
                  "name": "Malaysia"
                },
                {
                  "key": "MV",
                  "name": "Maldives"
                },
                {
                  "key": "ML",
                  "name": "Mali"
                },
                {
                  "key": "MT",
                  "name": "Malta"
                },
                {
                  "key": "MH",
                  "name": "Marshall Islands"
                },
                {
                  "key": "MQ",
                  "name": "Martinique"
                },
                {
                  "key": "MR",
                  "name": "Mauritania"
                },
                {
                  "key": "MU",
                  "name": "Mauritius"
                },
                {
                  "key": "YT",
                  "name": "Mayotte"
                },
                {
                  "key": "MX",
                  "name": "Mexico"
                },
                {
                  "key": "FM",
                  "name": "Micronesia, Federated States of"
                },
                {
                  "key": "MD",
                  "name": "Moldova"
                },
                {
                  "key": "MC",
                  "name": "Monaco"
                },
                {
                  "key": "MN",
                  "name": "Mongolia"
                },
                {
                  "key": "ME",
                  "name": "Montenegro"
                },
                {
                  "key": "MS",
                  "name": "Montserrat"
                },
                {
                  "key": "MA",
                  "name": "Morocco"
                },
                {
                  "key": "MZ",
                  "name": "Mozambique"
                },
                {
                  "key": "MM",
                  "name": "Myanmar"
                },
                {
                  "key": "NA",
                  "name": "Namibia"
                },
                {
                  "key": "NR",
                  "name": "Nauru"
                },
                {
                  "key": "NP",
                  "name": "Nepal"
                },
                {
                  "key": "NL",
                  "name": "Netherlands"
                },
                {
                  "key": "AN",
                  "name": "Netherlands Antilles"
                },
                {
                  "key": "NC",
                  "name": "New Caledonia"
                },
                {
                  "key": "NZ",
                  "name": "New Zealand"
                },
                {
                  "key": "NI",
                  "name": "Nicaragua"
                },
                {
                  "key": "NE",
                  "name": "Niger"
                },
                {
                  "key": "NG",
                  "name": "Nigeria"
                },
                {
                  "key": "NU",
                  "name": "Niue"
                },
                {
                  "key": "NF",
                  "name": "Norfolk Island"
                },
                {
                  "key": "MP",
                  "name": "Northern Mariana Islands"
                },
                {
                  "key": "NO",
                  "name": "Norway"
                },
                {
                  "key": "OM",
                  "name": "Oman"
                },
                {
                  "key": "PK",
                  "name": "Pakistan"
                },
                {
                  "key": "PW",
                  "name": "Palau"
                },
                {
                  "key": "PS",
                  "name": "Palestine"
                },
                {
                  "key": "PA",
                  "name": "Panama"
                },
                {
                  "key": "PG",
                  "name": "Papua New Guinea"
                },
                {
                  "key": "PY",
                  "name": "Paraguay"
                },
                {
                  "key": "PE",
                  "name": "Peru"
                },
                {
                  "key": "PH",
                  "name": "Philippines"
                },
                {
                  "key": "PN",
                  "name": "Pitcairn Islands"
                },
                {
                  "key": "PL",
                  "name": "Poland"
                },
                {
                  "key": "PT",
                  "name": "Portugal"
                },
                {
                  "key": "PR",
                  "name": "Puerto Rico"
                },
                {
                  "key": "QA",
                  "name": "Qatar"
                },
                {
                  "key": "RO",
                  "name": "Romania"
                },
                {
                  "key": "RU",
                  "name": "Russian Federation"
                },
                {
                  "key": "RW",
                  "name": "Rwanda"
                },
                {
                  "key": "RE",
                  "name": "Réunion"
                },
                {
                  "key": "BL",
                  "name": "Saint Barthélemy"
                },
                {
                  "key": "SH",
                  "name": "Saint Helena"
                },
                {
                  "key": "KN",
                  "name": "Saint Kitts and Nevis"
                },
                {
                  "key": "LC",
                  "name": "Saint Lucia"
                },
                {
                  "key": "MF",
                  "name": "Saint Martin (French part)"
                },
                {
                  "key": "PM",
                  "name": "Saint Pierre and Miquelon"
                },
                {
                  "key": "VC",
                  "name": "Saint Vincent and the Grenadines"
                },
                {
                  "key": "WS",
                  "name": "Samoa"
                },
                {
                  "key": "SM",
                  "name": "San Marino"
                },
                {
                  "key": "ST",
                  "name": "Sao Tome and Principe"
                },
                {
                  "key": "SA",
                  "name": "Saudi Arabia"
                },
                {
                  "key": "SN",
                  "name": "Senegal"
                },
                {
                  "key": "RS",
                  "name": "Serbia"
                },
                {
                  "key": "SC",
                  "name": "Seychelles"
                },
                {
                  "key": "SL",
                  "name": "Sierra Leone"
                },
                {
                  "key": "SG",
                  "name": "Singapore"
                },
                {
                  "key": "SX",
                  "name": "Sint Maarten (Dutch part)"
                },
                {
                  "key": "SK",
                  "name": "Slovakia"
                },
                {
                  "key": "SI",
                  "name": "Slovenia"
                },
                {
                  "key": "SB",
                  "name": "Solomon Islands"
                },
                {
                  "key": "SO",
                  "name": "Somalia"
                },
                {
                  "key": "ZA",
                  "name": "South Africa"
                },
                {
                  "key": "GS",
                  "name": "South Georgia and the South Sandwich Islands"
                },
                {
                  "key": "KR",
                  "name": "South Korea"
                },
                {
                  "key": "SS",
                  "name": "South Sudan"
                },
                {
                  "key": "ES",
                  "name": "Spain"
                },
                {
                  "key": "LK",
                  "name": "Sri Lanka"
                },
                {
                  "key": "SD",
                  "name": "Sudan"
                },
                {
                  "key": "SR",
                  "name": "Suriname"
                },
                {
                  "key": "SJ",
                  "name": "Svalbard and Jan Mayen"
                },
                {
                  "key": "SZ",
                  "name": "Swaziland"
                },
                {
                  "key": "SE",
                  "name": "Sweden"
                },
                {
                  "key": "CH",
                  "name": "Switzerland"
                },
                {
                  "key": "TW",
                  "name": "Taiwan"
                },
                {
                  "key": "TJ",
                  "name": "Tajikistan"
                },
                {
                  "key": "TZ",
                  "name": "Tanzania"
                },
                {
                  "key": "TH",
                  "name": "Thailand"
                },
                {
                  "key": "TL",
                  "name": "Timor-Leste"
                },
                {
                  "key": "TG",
                  "name": "Togo"
                },
                {
                  "key": "TK",
                  "name": "Tokelau"
                },
                {
                  "key": "TO",
                  "name": "Tonga"
                },
                {
                  "key": "TT",
                  "name": "Trinidad and Tobago"
                },
                {
                  "key": "TN",
                  "name": "Tunisia"
                },
                {
                  "key": "TR",
                  "name": "Turkey"
                },
                {
                  "key": "TM",
                  "name": "Turkmenistan"
                },
                {
                  "key": "TC",
                  "name": "Turks and Caicos Islands"
                },
                {
                  "key": "TV",
                  "name": "Tuvalu"
                },
                {
                  "key": "UG",
                  "name": "Uganda"
                },
                {
                  "key": "UA",
                  "name": "Ukraine"
                },
                {
                  "key": "AE",
                  "name": "United Arab Emirates"
                },
                {
                  "key": "GB",
                  "name": "United Kingdom"
                },
                {
                  "key": "US",
                  "name": "United States"
                },
                {
                  "key": "UM",
                  "name": "United States Minor Outlying Islands"
                },
                {
                  "key": "VI",
                  "name": "United States Virgin Islands"
                },
                {
                  "key": "UY",
                  "name": "Uruguay"
                },
                {
                  "key": "UZ",
                  "name": "Uzbekistan"
                },
                {
                  "key": "VU",
                  "name": "Vanuatu"
                },
                {
                  "key": "VA",
                  "name": "Vatican City"
                },
                {
                  "key": "VN",
                  "name": "Vietnam"
                },
                {
                  "key": "WF",
                  "name": "Wallis and Futuna"
                },
                {
                  "key": "EH",
                  "name": "Western Sahara"
                },
                {
                  "key": "ZM",
                  "name": "Zambia"
                },
                {
                  "key": "ZW",
                  "name": "Zimbabwe"
                },
                {
                  "key": "AX",
                  "name": "Åland Islands"
                }
              ]
            }
          ]
        },
        {
          "name": "City",
          "group": [
            {
              "key": "address.city",
              "name": "City",
              "type": "text",
              "refreshRequirementsOnChange": false,
              "required": true,
              "displayFormat": null,
              "example": "",
              "minLength": 1,
              "maxLength": 255,
              "validationRegexp": "^.{1,255}$",
              "validationAsync": null,
              "valuesAllowed": null
            }
          ]
        },
        {
          "name": "Address",
          "group": [
            {
              "key": "address.firstLine",
              "name": "Address",
              "type": "text",
              "refreshRequirementsOnChange": false,
              "required": true,
              "displayFormat": null,
              "example": "",
              "minLength": 1,
              "maxLength": 255,
              "validationRegexp": "^.{1,255}$",
              "validationAsync": null,
              "valuesAllowed": null
            }
          ]
        },
        {
          "name": "Address",
          "group": [
            {
              "key": "address.secondLine",
              "name": "Address",
              "type": "text",
              "refreshRequirementsOnChange": false,
              "required": false,
              "displayFormat": null,
              "example": "",
              "minLength": 1,
              "maxLength": 255,
              "validationRegexp": "^.{1,255}$",
              "validationAsync": null,
              "valuesAllowed": null
            }
          ]
        }
      ]
    },
  ],

  FAKE_ACCOUNT: c => { return {
    id: *********,
    creatorId: 5858965,
    name: {
      fullName: 'Fora Frederic',
      givenName: null,
      familyName: null,
      middleName: null,
      patronymicName: null,
      cannotHavePatronymicName: null
    },
    address: {
      country: 'US',
      firstLine: '236 Main St.',
      postCode: '05055',
      city: 'Norwich',
      state: 'VT'
    },
    currency: c,
    country: 'US',
    type: 'Aba',
    legalEntityType: 'PERSON',
    active: true,
    details: {
      abartn: '*********',
      accountType: 'CHECKING',
      accountNumber: '659573',
      hashedByLooseHashAlgorithm: '3a01128cc2d6dc93c12ae8e02ceca91f1f776f85faacccfe38217fd0e697b06b'
    },
    commonFieldMap: { accountNumberField: 'accountNumber', bankCodeField: 'abartn' },
    isDefaultAccount: false,
    hash: 'c51ed037748e6f872dc466a358ce9bff8af2a7d402b156200d4eed5725470f41',
    accountSummary: '(*********) 6****3',
    longAccountSummary: 'USD account ending in 9573',
    displayFields: [
      { label: 'ACH routing number', value: '*********' },
      { label: 'Account number', value: '659573' }
    ],
    ownedByCustomer: false
  } },

  FAKE_BALANCE: [
      {
        "id": 36252,
        "profileId": ********,
        "recipientId": *********,
        "creationTime": "2021-12-23T16:59:38.032Z",
        "modificationTime": "2021-12-23T16:59:38.032Z",
        "active": true,
        "eligible": true,
        "balances": [
          {
            "id": 93170,
            "balanceType": "AVAILABLE",
            "currency": "USD",
            "amount": {
              "value": Number.MAX_VALUE,
              "currency": "USD"
            },
            "reservedAmount": {
              "value": 0,
              "currency": "USD"
            },
            "bankDetails": null
          }
        ]
      }
    ],

  FAKE_TRANSFER: c => { return {
      "id": ********,
      "user": 5858965,
      "targetAccount": *********,
      "sourceAccount": *********,
      "quote": null,
      "quoteUuid": "ba8dc075-0484-4078-ba3a-07e006d45d91",
      "status": "incoming_payment_waiting",
      "reference": "AskFora Payment",
      "rate": 6.14524,
      "created": "2021-12-24 16:03:54",
      "business": ********,
      "transferRequest": null,
      "details": {
        "reference": "AskFora Payment"
      },
      "hasActiveIssues": false,
      "sourceCurrency": "USD",
      "sourceValue": 6.82,
      "targetCurrency": c,
      "targetValue": 41.91,
      "customerTransactionId": "d4058de1-2bca-4b47-b19a-a1f43a1890fb"
    } },

  FAKE_FUND: {
    "type": "BALANCE",
    "status": "COMPLETED",
    "errorCode": null,
    "errorMessage": null,
    "balanceTransactionId": 2375084
  },

}
