import { promptInfo } from '../utils/info';

export default {
  INTRO: ["Let's get started. Could you tell me a bit about yourself, like what are you good at?", "You can choose from a few options or type in your own."],
  ADD_SKILLS: [
    "Looking good. List as many skills as you want to promote. You can say you're done at any time.",
    `Excellent, keep going! Tell me "I'm done" when you’re finished. You can add or edit later as well.`,
    "You've got a great set of skills. When you're ready to create your profile, tell me you're 'done'",
  ],
  DONE: 'done',
  BIO_KWD: 'set bio',
  LOGIN: ["Thanks, we're almost done. Log in to get your unique link to share with your network. It's easy for clients to view your skills and hire you in just a few clicks. I handle the contracts and invoices, you get paid fast.", "Oh yeah, it’s free for you."],
  ANSWERS: ["Done", "Nevermind"],
  WELCOME: n => `Welcome ${n}! Here's your AskFora profile. Go ahead and add any public information you want to share.`,
  <PERSON><PERSON><PERSON>: "You can share your personal link with people you know. They can see your skills and hire you directly from your profile.",

  BIO_PROMPT: n => [
    `We<PERSON>lome ${n}. I'd like to learn more about you. I'll save whatever you share in the "About Me" part of your profile.`,
    `I'm a connector extraordinaire. I help you do better work with people you know. I can find people in your network to hire and to help them find you.`,
    `What kind of work do you specialize in?`,
  ],

  AUTH_CANCELLED: auth => {
    return [
      promptInfo(`AskFora uses ${auth.name} accounts for authentication.`),
      promptInfo(`I wasn't able to authenticate to ${auth.name} because you didn't approve my permissions.`),
      promptInfo('To create an account  please sign in and approve permissions with your', auth.url, `${auth.name} account`, [], true),
    ];
  },

  CONTRACTOR_SHORTCUT: a => {
    if (a) return { name: 'Find work', value: 'Find work' };
    else return null;
  },


}
