import { EntityType } from '../types/shared';

const type_label: {[key:string] : string} = {};
type_label[EntityType.Analysis] = 'plan';

export default {
  CREATE_NAME: 'Create',
  CREATE_DESCRIPTION: 'Create new search or development plans with filters',
  RESERVED: [
    'plan', 'analyze', 'analyze plan', 'analysis', 'analysis plan', 'develop', 'develop plan', 'develop a plan', 'network', 'network plan',
    'create plan', 'create a plan',
    'create engagement', 'create an engagement', 'create engagement plan', 'create an engagement plan',
    'create network', 'create network plan', 
    'create a network', 'create a network plan', 'network', 'network plan', 
    'create anlalysis', 'create an analysis', 'new talent plan', 'new ask', 'new people search',
    'create develop', 'create development', 'create a development', 'create a development plan', 'create develop plan', 'create development plan',
    'new learning goal', 'new goal', 'new learning',
    'talent analysis', 'create talent analysis', 'new talent analysis',
    'create engagement', 'create an engagement', 'create analysis', 'create an analysis', 'new ask', 'new people search',
    'create learning goal', 'create goal', 'create learning',
    'create new learning goal', 'create new goal', 'create new learning',
    'create a new learning goal', 'create a new goal', 'create a new learning',
  ],
  CREATE_KWD: [
    'create', 'plan', 'analyze', 'analyze plan', 'analysis', 'analysis plan', 'develop', 'develop plan', 'develop a plan', 'development plan', 'network', 'network plan',
    'create plan', 'create a plan', 'engage', 'analyize', 'analysis', 'engagement', 'new talent plan', 'new development', 'new development plan',
    'talent analysis', 'create talent analysis', 'new talent analysis',
    'create engagement', 'create an engagement', 'create analysis', 'create an analysis', 'new ask', 'new people search',
    'create learning goal', 'create goal', 'create learning',
    'create new learning goal', 'create new goal', 'create new learning',
    'create a new learning goal', 'create a new goal', 'create a new learning',
    'new learning goal', 'new goal', 'new learning', 'learning goal', 'learning goal goal', 'learning goal goal goal',
  ],
  CONTEXT: ['for', 'about', 'on', 'profile'],
  CREATE_TYPES: ['analysis', 'development', 'develop', 'analyze', 'talent', 'plan', 'ask', 'people', 'search', 'learning', 'goal'],
  GOAL: 'goal',
  LEARNING: 'learning',
  PEOPLE: 'people',
  SEARCH: 'search',
  ANALYSIS: 'analysis',
  ANALYZE: 'analyze',
  DEVELOP: 'develop',
  PLAN: 'plan',
  TALENT: 'talent',
  DEVELOPMENT: 'development',
  ANALYSIS_HINT: 'List multiple skills and be as specific as possible',
  PLAN_HINT: 'List multiple skills and be as specific as possible',
  GOAL_HINT: `List multiple skills and be as specific as possible`,
  CREATE_EDIT: [`Here is a plan that aligns with your future skillsets. You can add or delete skills that you're looking for.`, `When you’re finished, tap the save button in the top right and I’ll find and categorize team members who match these skills.`],

  CREATE_COPY: 'Do you want to use this search to create a list of people, an engagement plan to reach out, or an analysis to dive into how skills are mapped?',
  CREATE_COPY_HINT: ['Develop', 'Nevermind'],

  CREATE_PROMPT_ANALYSIS: [
    `Talent Analysis helps you analyze existing skillsets on your team and map them to future skillsets that you need.`,
    `Let me know what future skillsets you are planning for and I'll identify people who may be a fit.`, 
    `You can list as many skills as you like and will be able to make changes later on as well.`
  ],
  CREATE_PROMPT_GOALS: ['What learning goal are you working towards? The more specific your learning goal, the better I can recommend matching courses.'],
  CREATE_PROMPT_PLAN: [`What future skillset do you need to plan for?`, `You can list as many skills as you like and will be able to make changes later on as well.`],
  GOALS_MORE_SKILLS: [`I need to know a bit more about you before I can help find courses for your learning goals. What are some things you're good at?`],
  GOAL_SKILLS_HINT: `.e.g Design, Development, Operations, Coaching`,

  NEW_GOAL: `I've added your learning goal. Add or remove skills and be as specific as possible. Then take an assessment to check what skills you're already good at.`,
  NEW_GOAL_ACHIEVED: `Looks like you’re already proficient in that skill. Would you like to choose something else?`,
  DUPLICATE_GOAL: 'You already have this learning goal. Can you be more specific about what you want to learn?',
  EMPTY_GOAL: `I couldn't find any new learning goals that match your request. Can you be more specific in what you want to learn?`,
  EMPTY_COURSES: `I couldn't find any learning goals with matching courses. In order to find you the best course recommendations, could you be more specific?`,

  NEW_PLAN: [`I’ve added some skills that belong with this skillset. Feel free to add or delete any.`,`When you’re finished, you can assign this development plan to team members and then track their progress.`],

  GOALS_PENDING: [
    `I'm looking up learning goals that match.`,
    `Checking the latest the internet has to offer.`,
    `Digging through the wealth of knowledge that can help you achieve your learning goals.`,
    `Refining my course recommendations.`,
    `Crossing my "t"s and dotting my "i"s.`,
    `Still working on an exhaustive search.`,
  ],
}
