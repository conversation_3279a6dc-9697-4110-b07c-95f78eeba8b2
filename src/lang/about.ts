import { Person, Tag } from '../types/items';
import { TagType } from '../types/shared';
import { FORA_PROFILE } from '../types/user';

export default {
  ABOUT_EG: [],

  ABOUT_KWDS: ['about'],

  ABOUT_INFO: [
    new Person({
      askfora_id: FORA_PROFILE,
      id: 'people/f0',
      displayName: 'Fora',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.jobTitle,  'Professional Connector',  0, new Date('2018-02-23')),
        new Tag(TagType.organization, 'AskFora', 0, new Date('2018-02-23')),
        ...['Discovering trusted people', 'Simple and fair contracting', 'Fast and efficient payments'].map(v => new Tag(TagType.skill, v, 0, new Date('2018-02-23' ), 100)),
      ],
      photos: ['/images/about/fora.jpg'],
      urls: ['https://askfora.com/privacy', 'https://askfora.com/terms', 'https://twitter.com/askfora'],
      vanity: 'about_fora',
      network: true,
    } as Partial<Person>),
    new Person({
      id: 'people/f1',
      displayName: '<PERSON><PERSON> Trajman',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.jobTitle,  'Founder',  0, new Date('2018-02-23')),
        new Tag(TagType.organization, 'AskFora', 0, new Date('2018-02-23') ),

        new Tag(TagType.jobTitle,  'CEO, co-Founder',  1, new Date('2014-02-20')),
        new Tag(TagType.organization, 'Rocana', 1, new Date('2014-02-20') ),
        new Tag(TagType.range, 'Rocana', 1, new Date('2017-10-31') ),

        new Tag(TagType.jobTitle,  'VP Field Ops',  2, new Date('2012-11-04')),
        new Tag(TagType.organization, 'WibiData', 2, new Date('2012-11-04') ),
        new Tag(TagType.range, 'WibiData', 2, new Date('2013-12-31') ),

        new Tag(TagType.jobTitle,  'VP Solutions',  3, new Date('2010-01-15')),
        new Tag(TagType.organization, 'Cloudera', 3, new Date('2010-01-15') ),
        new Tag(TagType.range, 'Cloudera', 3, new Date('2012-11-04') ),

        ...['startups', 'investing', 'entrepreneurship', 'analytics', 'technology', 'data'].map(v => new Tag(TagType.skill, v, 0, new Date('2018-02-23' ), 100)),
      ],
      photos: ['/images/about/omer.jpg'],
      urls: ['https://linkedin.com/in/omert', 'https://facebook.com/otrajman', 'https://twitter.com/otrajman'],
      vanity: 'about_omer',
      network: true,
      bio: 'Omer is a entrepreneur, investor, and advisor. He founded AskFora in 2018 to help folks do better work with people they know.',
    }),
    new Person({
      id: 'people/f2',
      displayName: 'Theresa Wylie',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.jobTitle,  'Product Design',  0, new Date('2018-04-10')),
        new Tag(TagType.organization, 'AskFora', 0, new Date('2018-04-10') ),

        new Tag(TagType.jobTitle, 'Operations Manager', 1, new Date('2015-03-01')),
        new Tag(TagType.organization, 'Rocana', 1, new Date('2015-03-01')),
        new Tag(TagType.range, 'Rocana', 1, new Date('2017-10-31')),

        new Tag(TagType.jobTitle, 'EA', 2, new Date('2014-09-01')),
        new Tag(TagType.organization, 'Teneo Holdings', 2, new Date('2014-09-01')),
        new Tag(TagType.range, 'Teneo Holdings', 2, new Date('2015-03-31')),

        new Tag(TagType.jobTitle, 'Facilities Manager', 3, new Date('2009-04-01')),
        new Tag(TagType.organization, 'Green Mountain Energy Company', 3, new Date('2009-04-01')),
        new Tag(TagType.range, 'Green Mountain Energy Company', 3, new Date('2014-05-31')),

        ...['project management', 'continuous improvement', 'use case design', 'event coordination'].map(v => new Tag(TagType.skill, v, 0, new Date('2018-04-10' ), 100)),
      ],
      photos: ['/images/about/theresa.jpg'],
      urls: ['https://www.linkedin.com/in/theresa-wylie-capm-32780b22', 'https://angel.co/theresa-wylie'],
      vanity: 'about_theresa',
      network: true,
      bio: 'Theresa is adept at turning ideas and amorphous guidelines into measurable productivity. She works on AskFora UX and CX.'
    }),
    new Person({
      id: 'people/f3',
      displayName: 'Joe Cavanaugh',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.jobTitle,  'Product Development',  0, new Date('2018-06-01')),
        new Tag(TagType.organization, 'AskFora', 0, new Date('2018-06-01') ),

        new Tag(TagType.jobTitle, 'Chief Architect', 1, new Date('2016-03-01')),
        new Tag(TagType.organization, 'Three Wire Systems', 1, new Date('2016-03-01')),
        new Tag(TagType.range, 'Three Wire Systems', 1, new Date('2017-09-31')),

        ...['software development', 'software architecture', 'software design'].map(v => new Tag(TagType.skill, v, 0, new Date('2018-06-01' ), 100)),
      ],
      photos: ['/images/about/joe.jpg'],
      urls: ['https://www.linkedin.com/in/joecavanaugh', 'https://github.com/juniorfoo'],
      vanity: 'about_joe',
      network: true,
      bio: 'Joe worked on the initial back end and synchronization systems at AskFora',
    }),
    new Person({
      id: 'people/f4',
      displayName: 'Sam Purcell',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.jobTitle,  'Product Development',  0, new Date('2018-05-09')),
        new Tag(TagType.organization, 'AskFora', 0, new Date('2018-05-09') ),

        new Tag(TagType.jobTitle, 'Lead Engineer', 1, new Date('2015-08-01')),
        new Tag(TagType.organization, 'Cymbal', 1, new Date('2015-08-01')),
        new Tag(TagType.range, 'Cymbal', 1, new Date('2017-07-31')),

        ...['mobile development', 'web development', 'typescript', 'javascript', 'react'].map(v => new Tag(TagType.skill, v, 0, new Date('2018-05-09' ), 100)),
      ],
      photos: ['/images/about/sam.jpg'],
      urls: ['https://www.linkedin.com/in/sam-purcell-98331b72', 'https://github.com/sampurcell93'],
      vanity: 'about_sam',
      network: true,
      bio: 'Sam Experienced engineer, still hungry to explore new domains and work on big, interesting, <adjective>, meaty technical and organizational problems. He worked on the initial front end and mobile interface at AskFora',
    }),
    new Person({
      id: 'people/f5',
      displayName: 'ExVentures',
      comms: [],
      tags: [
        new Tag(TagType.occupation,  'Investor',  0, new Date('2018-05-31')),
        new Tag(TagType.organization, 'ExVentures', 0, new Date('2018-05-31') ),
        new Tag(TagType.skill, 'early stage investor', 0, new Date('2018-05-31' ), 100),
      ],
      photos: ['/images/about/exventures.jpg'],
      urls: ['https://exventures.com/', 'https://www.linkedin.com/in/richminer/'],
      vanity: 'about_exventures',
      network: true,
      bio: 'ExVentures helped incubate and was the first investor in AskFora',
    }),
    new Person({
      id: 'people/f6',
      displayName: 'Glasswing',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.occupation,  'Investor',  0, new Date('2018-06-13')),
        new Tag(TagType.organization, 'Glasswing', 0, new Date('2018-06-13') ),
        new Tag(TagType.skill, 'seed investor', 0, new Date('2018-06-13' ), 100),
      ],
      photos: ['/images/about/glasswing.jpg'],
      urls: ['https://glasswing.vc/', 'https://www.linkedin.com/company/glasswing-ventures/'],
      vanity: 'about_glaswing',
      network: true,
      bio: 'Glasswing funds visionary entrepreneurs harnessing the power of AI and frontier technologies to transform markets and revolutionize industries.',
    }),
    new Person({
      id: 'people/f7',
      displayName: 'FreshTracks',
      comms: [],
      tags: [
        new Tag(TagType.occupation,  'Investor',  0, new Date('2018-06-15')),
        new Tag(TagType.organization, 'FreshTracks', 0, new Date('2018-06-15') ),
        new Tag(TagType.skill, 'multistage investor', 0, new Date('2018-06-15' ), 100),
      ],
      photos: ['/images/about/freshtracks.jpg'],
      urls: ['https://www.freshtrackscap.com/', 'https://www.linkedin.com/company/freshtracks-capital/'],
      vanity: 'about_freshtracks',
      network: true,
      bio: 'FreshTracks is an early-stage venture capital firm focused on financing businesses in Vermont and surrounding regions.',
    }),
    new Person({
      id: 'people/f8',
      displayName: 'Koa Labs',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.occupation,  'Investor',  0, new Date('2021-10-19')),
        new Tag(TagType.organization, 'Koa Labs', 0, new Date('2021-10-19')),
        new Tag(TagType.skill, 'seed investor', 0, new Date('2021-10-19' ), 100),
      ],
      photos: ['/images/about/koalab.jpg'],
      urls: ['https://koalab.com/', 'https://www.linkedin.com/company/koa-lab/'],
      vanity: 'about_koalabs',
      network: true,
      bio: 'Koa Labs is a Seed Fund for promising start-ups.',
    }),
    new Person({
      id: 'people/f9',
      displayName: 'Ron Adner',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.occupation,  'Advisor',  0, new Date('2018-03-30')),
        new Tag(TagType.organization, 'Strategy Insight Group', 0, new Date('2018-03-30')),
        ...['go-to-market strategies', 'new opportunity analaysis', 'internal ecosystem alignment', 'building internal capabilities'].map(v => new Tag(TagType.skill, v, 0, new Date('2018-03-30' ), 100)),
      ],
      photos: ['/images/about/ron.jpg'],
      urls: ['https://www.strategyinsightgroup.com/', 'https://ronadner.com/', 'https://www.linkedin.com/in/ron-adner-0724a25/'],
      vanity: 'about_ron',
      network: true,
      bio: 'Strategy Insight Group is a boutique consultancy that provides strategic advisory services and corporate training to a wide variety of industries.'
    }),
    new Person({
      id: 'people/f10',
      displayName: 'Remedy',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.jobTitle,  'Product Development',  0, new Date('2018-06-18')),
        new Tag(TagType.organization, 'Remedy', 0, new Date('2018-06-18') ),
        ...['funding', 'technical resources', 'sr technical leadership', 'product leadership'].map(v => new Tag(TagType.skill, v, 0, new Date('2018-06-18' ), 100)),
      ],
      photos: ['/images/about/remedy.jpg'],
      urls: ['https://www.remedyproduct.com/', 'https://www.linkedin.com/company/remedyproduct/'],
      vanity: 'about_remedy',
      network: true,
      bio: 'Remedy supported AskFora in testing and automated quality assurance.'
    }),
    new Person({
      id: 'people/f11',
      displayName: 'Perkins Cove Partners',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.occupation,  'Investor',  0, new Date('2021-10-19')),
        new Tag(TagType.organization, 'Perkins Cove', 0, new Date('2021-10-19')),
        new Tag(TagType.skill, 'seed investor', 0, new Date('2021-10-19' ), 100),
      ],
      photos: ['/images/about/perkins.jpg'],
      urls: ['https://www.perkinscovepartners.com/', 'https://www.linkedin.com/company/perkins-cove-partners/'],
      vanity: 'about_perkins',
      network: true,
      bio: 'We simplify the lives of founders by providing "committed capital" from proven operators.',
    }),
    new Person({
      id: 'people/f12',
      displayName: 'Yael Gal',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.occupation,  'Advisor',  0, new Date('2021-12-15')),
        new Tag(TagType.organization, 'SAP', 0, new Date('2012-05-01')),
        ...['future of work', 'global employer branding', 'hr tech expert', 'hr consultant for startups', 'executive coach', 'diversity lead'].map(v => new Tag(TagType.skill, v, 0, new Date('2021-12-15' ), 100)),
      ],
      photos: ['/images/about/yael.jpg'],
      urls: ['https://linkedin.com/in/yaelgal', 'https://people.sap.com/yael.gal'],
      vanity: 'about_yael',
      network: true,
      bio: 'SAP Global HR, "Future of Work" Expert, Global Employer Branding, HR Tech Expert, HR Consultant for Startups, Executive Coach, Diversity Lead.'
    }),
    new Person({
      id: 'people/f13',
      displayName: 'Emmanuel Tesone',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.occupation,  'Advisor',  0, new Date('2023-03-01')),
        ...['multi-channel', 'digital marketing', 'brand development', 'strategic planning', 'leading remote teams', 'capital raising'].map(v => new Tag(TagType.skill, v, 0, new Date('2023-03-01' ), 100)),
      ],
      photos: ['/images/about/manu.jpg'],
      urls: ['https://www.linkedin.com/in/manutesone'],
      vanity: 'about_manu',
      network: true,
      bio: 'C-level executive, board member, adviser, investor, and entrepreneur.'
    }),


    new Person({
      id: 'people/f14',
      displayName: 'Firsthand Alliance',
      comms: [''],
      tags: [
        new Tag(TagType.occupation,  'Investor',  0, new Date('2023-08-22')),
        new Tag(TagType.organization, 'Firsthand Alliance', 0, new Date('2023-08-22')),
        new Tag(TagType.skill, 'seed investor', 0, new Date('2023-08-22' ), 100),
      ],
      photos: ['/images/about/firsthand.jpg'],
      urls: ['https://www.firsthand.vc/', 'https://www.linkedin.com/company/firsthandvc/'],
      vanity: 'about_firsthand',
      network: true,
      bio: 'Firsthand Alliance is a community initially started by acquired founders and executives who have learned a lot, done a lot at our Salesforce time.',
    }),

    new Person({
      id: 'people/f15',
      displayName: 'Blue Ocean Opportunity',
      comms: [''],
      tags: [
        new Tag(TagType.occupation,  'Investor',  0, new Date('2023-08-23')),
        new Tag(TagType.organization, 'Blue Ocean Opportunity', 0, new Date('2023-08-23')),
        new Tag(TagType.skill, 'seed investor', 0, new Date('2023-08-23' ), 100),
      ],
      photos: ['/images/about/blueocean.jpg'],
      urls: [''],
      vanity: 'about_blueocean',
      network: true,
      bio: '',
    }),

    new Person({
      id: 'people/f16',
      displayName: 'Rachael Ellison',
      comms: ['<EMAIL>'],
      tags: [
        new Tag(TagType.jobTitle,  'GTM',  0, new Date('2023-09-01')),
        new Tag(TagType.organization, 'AskFora', 0, new Date('2023-09-01') ),

        ...['future of work', 'strategic hr', 'organizational development', 'executive coaching'].map(v => new Tag(TagType.skill, v, 0, new Date('2023-09-01' ), 100)),
      ],
      photos: ['/images/about/rachael.jpg'],
      urls: ['https://www.rachaellowellellison.com', 'https://www.linkedin.com/in/rachaeltellison'],
      vanity: 'about_rachael',
      network: true,
      bio: 'Dedicated strategist and researcher in the work-life space, launching impactful B2B and B2C solutions that address the intersection of work-family conflict and organizational effectiveness.',
    }),
  ],

  ABOUT_ASK_FORA: ['AskFora was created in 2018 by Omer Trajman. '],

  ABOUT_EMAIL_SIGNATURE: `-Fora
Connector Extraordinaire

"Better work with people you know"

About: https://askfora.com/about
Privacy policy: https://askfora.com/privacy
Terms of service: https://askfora.com/terms`,

  DRAFT_EMAIL_SIGNATURE: `
  ---
  Connected with help from AskFora (https://askfora.com). Better work with people you know.`
};
