import { CategoryInfo, ContractInfo, PersonInfo, ProjectRate, Reply, ServerChatResponse } from '../types/shared';

const nevermind = {
  id: `cancel_no_${new Date().getTime()}`,
  ping: -1,
  info: [],
  hide: 10000,
  forget: true,
  reply: [{ label: 'Ok, no worries.' }]
};

const EMBED_FILTER_SEARCH_KEYS = [
  'name',
  'conditions.att',
  'conditions.value',
]

const EMBED_CATEGORY_SEARCH_KEYS = [
  'label',
  'skills',
]

const EMBED_LISTS_SEARCH_KEYS = [
  'title',
].concat(EMBED_FILTER_SEARCH_KEYS.map(k => `filters.${k}`));

const EMBED_PLANS_SEARCH_KEYS = [
  'title'
].concat(EMBED_FILTER_SEARCH_KEYS.map(k => `filters.${k}`))
  .concat(EMBED_CATEGORY_SEARCH_KEYS.map(k => `skill_categories.${k}`));

const EMBED_ANALYSES_SEARCH_KEYS = [
  'title'
].concat(EMBED_FILTER_SEARCH_KEYS.map(k => `filters.${k}`))
  .concat(EMBED_CATEGORY_SEARCH_KEYS.map(k => `categories.${k}`))
  .concat(EMBED_CATEGORY_SEARCH_KEYS.map(k => `target_skills.${k}`))
  .concat(EMBED_CATEGORY_SEARCH_KEYS.map(k => `focus_skill.${k}`));

const EMBED_PROJECT_SEARCH_KEYS = [
  'skills',
  'title',
  'notes',
  'background',
  'requirements',
  'deliverables',
]

const EMBED_SEARCH_KEYS = [
  'bio',
  'vanity',
  'id',
  'name',
  'nick',
  'groups',
  'meta',
  'comms.link',
  'links.link',
  'extra.start',
  'extra.value',
  'extra.tags.value',
  'description.label',
  'description.text',
  'description.link',
  'description.date',
  'tasks.due',
  'tasks.title',
  'tasks.id',
  'tasks.notes',
  'recommendation',
  'skills.value',
  'orgs',
  'notes',
].concat(EMBED_PROJECT_SEARCH_KEYS.map(k => `projects.${k}`))
  .concat(EMBED_PROJECT_SEARCH_KEYS.map(k => `templates.${k}`))
  .concat(EMBED_LISTS_SEARCH_KEYS.map(k => `lists.${k}`))
  .concat(EMBED_PLANS_SEARCH_KEYS.map(k => `plans.${k}`))
  .concat(EMBED_ANALYSES_SEARCH_KEYS.map(k => `analyses.${k}`))

const EMBED_PEOPLE_SEARCH_KEYS = EMBED_SEARCH_KEYS.map(k => `people.${k}`);
const EMBED_CANDIDATE_SEARCH_KEYS = EMBED_SEARCH_KEYS.map(k => `candidates.${k}`);

const colorMap = {
  red: 'rgb(255, 99, 132)',
  green: 'rgb(193, 225, 193)',
  blue: 'rgb(53, 162, 235)',
  yellow: 'rgb(252, 243, 207)',
  orange: 'rgb(255, 206, 86)',
  brown: 'rgb(255, 159, 64)',
  violet: 'rgb(153, 102, 255)',
  grey: 'rgb(128,128,128)',
  pink: 'rgb(255, 199, 232)',
  teal: 'rgb(75, 192, 192)',
}

function alpha(c, a) {
  const [x, r, g, b] = c.match(/rgba?\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*/)
  return `rgba(${r},${g},${b},${a})`;
}

export default {
  ACCEPT: 'Accept',
  DECLINE: 'Decline',
  ADD_CANDIDATE: 'Add Candidate',
  DRAFT_INVITE: 'Draft Invite',
  LINK_PERSON: 'Link Person',
  ADD_PERSON: 'Add Person',
  APPROVE_PAYMENT: "If you're satisfied with the work, go ahead and approve to release payment.",
  APPROVE_PAYMENT_BUTTON: (e: number) => `Approve Payment of $${e.toLocaleString()}`,
  ASK_REFERRAL_BUTTON: 'Get referral',
  AGREEMENT_DECLINED: 'Agreement Declined',

  TITLE: 'AskFora',
  TAGLINE: 'Better work with people you know',

  ALREADY_KNOW_FORA: 'Already Know Fora?',
  GET_STARTED_WITH_FORA: 'Get Started With Fora',
  ASKFORA_PERMISSIONS: 'AskFora never sends emails or deletes any data without your express permission.',
  ASKFORA_TALENT: 'Find the hidden talent in your network',
  ASKFORA_EXPERT: 'Ask experts in your network',
  ASKFORA_GETHIRED: 'Get hired by people you know',

  PEOPLE: 'People',
  EXPERTS: 'Experts',
  PROJECTS: 'Jobs',
  NOTES: 'Notes',
  ARCHIVES: 'Archives',

  NOTIFY_MEETINGS: 'Meetings with other people',
  NOTIFY_EVENTS: 'Events without attendees',
  NOTIFY_DRAFTS: 'Draft emails',
  NOTIFY_FOLLOW_UP: 'Email follow ups',
  NOTIFY_NEW_CONTACT: 'New contact follow ups',
  NOTIFY_AWAY: 'Notify when away',
  // BETA_LABEL: 'You can check out my beta at',
  ACCEPT_PROPOSAL: c => `Do you accept ${c.nick}'s proposal?`,

  NO_MEETINGS_LAST: 'No meetings in the last 30 days.',
  NO_MEETINGS_NEXT: 'No meetings scheduled in the next 30 days.',
  NO_MEETINGS: 'No meetings coming up or in the last 30 days.',
  LAST_MET: (w: string, a?: string, p?: string) => `You last met${p && p.length ? ` ${p}` : ''} on ${w}${a && a.length ? ` about ${a}` : ''}`,
  NEXT_MEETING: (w: string, a?: string, p?: string) => `Your next meeting${p && p.length ? ` ${p}` : ''} on ${w}${a && a.length ? ` about ${a}` : ''}`,
  EVENT_PREFIX: e => {
    const now = new Date();
    return e < now ? 'Last met about' : 'Next meeting about'
  },

  EXPLORE_NET: 'Explore Your Network',

  CANDIDATE_ADDED_ALREADY_FOUND: (p: Partial<PersonInfo>) => `You've already added ${p.nick ? p.nick : p.name.split(' ')[0]} as a candidate to this job.`,
  CANDIDATE_ADDED_ALREADY_SENT: (p: Partial<PersonInfo>) => `You've already invited ${p.nick ? p.nick : p.name.split(' ')[0]} to this job. You can check the status of your invites on the "Select Candidates" screen.`,
  CANDIDATE_ADDED_PICK_MATCH: `I found a few possible matches. Who do you want to add?`,

  CANDIDATE_FOUND_NONE: `I couldn't find anyone`,
  CANDIDATE_FOUND_ONE: `I found a match.`,
  CANDIDATE_FOUND: `I found some people who might match.`,
  CANDIDATE_ANALYZE: `Analyzing the list of people.`,

  CANDIDATE_ANSWER: 'Answer',
  CANDIDATE_LIST: 'Responses to your Job',
  CANDIDATE_INTERESTED: "I'm Interested",
  CANDIDATE_REFER: 'Refer Someone',
  CANDIDATE_NOT_INTERESTED: 'Not Interested',

  CANNOT_DELETE_NOTE: "I can't delete this note because it's attached to a job",

  NOTES_PERSONAL: 'Personal Notes',
  NOTES_PROJECT: 'Job Notes',

  CHECK_BALANCE: 'You can check your balance',
  CLIENT: 'Client',
  ADMIN: 'Admin',
  SERVICE_OFFERING: 'Service Offering',
  TEMPLATE: 'Template',
  TEMPLATE_PUBLIC: 'Public',
  CLIENT_FINISH_PAY: "Tap 'Finish' when the job is complete and you'll be able to release payment",
  COMPLETED: 'Congratulations, you\'ve completed your job!',
  COMPLETED_OR_ARCHIVED: (n: number) => `${n} Completed or Archived Job${n > 1 ? 's' : ''}`,
  CONNECT_STRIPE: 'You must have a payment account before you can start this job',
  CONNECT_STRIPE_BUTTON: 'Setup Payout Account',
  CONTRACTOR: 'Freelancer',
  CONTRACTOR_SUBMIT: nick => `Tap 'Submit' to let ${nick} know that you've completed the job`,
  CONTRACTOR_FINISH_PAY: nick => `When ${nick} confirms that the job is complete,  you'll be able to request payment`,
  CONTRACT_DECLINED: 'The agreement has been canceled.',
  QUESTION: 'Question',
  SHARED_QUESTION: 'Shared Question',
  PUBLIC_QUESTION: 'Public Question',
  PRIVATE_QUESTION: 'Public Question',
  ANSWER: 'Answer',

  CONTRACTOR_START: "Get Hired",

  CONNECT_CALENDAR: "Link your contacts and calendar to see events, meetings, and people you meet.",
  CONNECT_NETWORK: "Link your network to explore the skills of people you know!",
  LINK: 'Link',
  DISCONNECT: 'Unlink',
  SYNC: 'Sync',
  CONTACTS: 'Contacts',

  TOP_RESULT_SKILLS: 'Skillsets',
  YOUR_CONTACTS: (n, m?) => m ? 'Your Contacts' : `Your Contacts (${Number(n).toLocaleString()})`,
  YOUR_TEAM: n => `Your Team (${Number(n).toLocaleString()})`,
  USER_CONTACTS: n => `Users You Know (${Number(n).toLocaleString()})`,
  SECOND_CONNECTIONS: (n, m?) => m ? '2nd Contacts' : `2nd Connections (${Number(n).toLocaleString()})`,
  YOUR_GROUPS: n => `Your Groups (${Number(n).toLocaleString()})`,
  GROUP_CONNECTIONS: (n, m?) => m ? 'Group Contacts' : `Group Contacts (${Number(n).toLocaleString()})`,
  FORA_USERS: n => `All AskFora Users (${Number(n).toLocaleString()})`,
  FORA_CONNECTIONS: (n, m?) => m ? 'User Contacts' : `All AskFora User Contacts (${Number(n).toLocaleString()})`,

  PAYMENT: 'Payout Accounts',
  CONTACTS_CALENDAR_NOTES: 'Contacts, Calendar, Notes',
  MY_NETWORK: 'Your Network',
  ASKFORA_ACCOUNT: 'AskFora Account',
  ABOUT: 'About',
  DATA: 'Data Usage',
  PRIVACY: 'Privacy',
  SECURITY: 'Security',
  TERMS: 'Terms',
  DASHBOARD: 'Dashboard',
  UPLOAD: 'Upload',
  HOW_TO: 'How to',
  DELETE_ACCOUNT: 'Delete Account',
  VIEW: 'View',
  CUSTOM: 'Custom',
  PRIMARY: 'Primary',

  FORA_NETWORK: (c?: string) => [`${c ? c : 'This person'} is a member of Fora's network.`, `You can connect with ${c ? c : 'them'} or search your network and find people you know with the skills you're looking for.`],
  FORA_READY_NETWORK: (c?: string) => [`${c ? c : 'This person'} is a professional member of Fora's network and ready to be hired.`, `You can connect with ${c ? c : 'them'} or search your network and find people you know with the skills you're looking for.`],
  FORA_PREMIER_NETWORK: (c?: string) => [`${c ? c : 'This person'} is a premier member of Fora's network.`, `You can connect with ${c ? c : 'them'} or search your network and find people you know with the skills you're looking for.`],

  CONNECT_PROFILE_PROMOTE: 'Personal Invitation',
  CONNECT_PROFILE_PROMOTE_TEXT: ['Share your personal invitation link to grow your network.', 'Tap to copy the phrase or share directly from the social icons:'],

  CONNECT_SHARE_COPY_TEXT: 'Connect with me on AskFora:',

  LEARNING_NETWORK: "I'm learning about skills in your network",

  CREATE_NOTE: 'Create a Note',

  DECLINED: 'Declined',

  DESCRIPTION: 'Description',
  DESCRIPTION_HINT: 'What are you looking for help with?',
  REQUIREMENTS: 'Requirements',
  QUALIFICATIONS: 'Qualifications',
  REQUIREMENTS_HINT: 'Skills, tools, experience, education',
  DELIVERABLES: 'Deliverables',
  DELIVERABLES_HINT: 'When is this job considered complete?',
  BACKGROUND: 'Additional Notes',
  BACKGROUND_HINT: 'Details about you, the company, the job',
  QUESTION_HINT: 'What question do you need an expert to answer? (required)',
  ANSWER_HINT: 'Your reply to the question',

  INFO_EMPTY_SCHEDULE: "You've got nothing scheduled!",
  INFO_LEARNING: "I'm learning about you and your network",
  INFO_NOTIFY: "My answers won't be great until I'm done learning. I'll email you as soon as everything is ready.",
  INFO_PEOPLE: "Explore skills from people you know",
  INFO_EXPERT: "Get expert answers from your network",
  INFO_PROJECT: "Work with people you know and trust",
  INFO_NOTES: "Keep private notes on people and jobs",

  FORA_SENDS_BUTTON: 'Fora sends',
  ALL_INVITES: 'Select Everyone',
  ALL_REFERRALS: 'Select Everyone',
  SEND_QUESTION: 'Send Question',
  SHARE_QUESTION: 'share question',
  SHARE_REFERRALS: 'Ask For Referrals',
  ALL_INTROS: 'Get All Introductions',
  GET_STARTED: a => a ? 'Select a candidate to get started' : 'Login to get started',
  GET_INVOICE: 'Download Invoice',

  SHARE_TEMPLATE: 'Get Hired',

  INVITE_EMPTY: `There's no one to invite. Try importing contacts, updating the job skills, and searching again.`,
  REFERRAL_EMPTY: `There's no one to get a referral for. Try importing contacts, updating the job skills, and searching again.`,
  INTRO_EMPTY: `There's no on to introduce you to. Try updating the job skills, and searching again.`,

  HELP_CHAT: answers => {
    return {
      hint: 'Ask Fora...',
      answers,
      id: `help_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: 'How can I help?' }],
    };
  },

  INTERESTED: 'Interested in your Job',

  INVITE_ERROR_STARTED: 'I can\'t invite new candidates to a job that you already started',

  DRAFT_MESSAGE: (skills, duration, rate, budget: number, start, referral) => {
    const comp = budget ?
      rate.length ? ` and the compensation is up to $${budget.toLocaleString()}.`
        : `The job pays a fixed fee of $${budget.toLocaleString()}.`
      : '.';
    const time = rate.length ? `It will take about ${duration} ${rate}` : '';
    return `I'm working with Fora (cc'd) to find someone with experience \
in ${skills} for a short term engagement. ${time}${comp} \
I would like to find someone by ${start}. Are you interested${referral ? ' or could you recommend someone' : ''}?`
  },

  INVITE_MESSAGE: (name, skills, duration, rate, budget: number, start, referral) => {
    const comp = budget ?
      rate.length ? ` and the compensation is up to $${budget.toLocaleString()}.`
        : `The job pays a fixed fee of $${budget.toLocaleString()}.`
      : '.';
    const time = rate.length ? `It will take about ${duration} ${rate}` : '';

    return `I'm helping ${name} find someone for a short term job \
who has these skills and experience: ${skills}. ${time}${comp} \
${name} would like to find someone by ${start}. Are you interested${referral ? ' or could you recommend someone' : ''}?`
  },

  EXPERT_MESSAGE: (name, skills, notes, duration, rate, budget: number, start, referral) => {
    return `I'm helping ${name} find someone knowledgable about ${skills} to answer this question: 

"${notes}"`
  },

  SOURCING_DRAFT_MESSAGE: (skills, start, referral, stype) => {
    let job_type = ''
    switch (stype) {
      case 'full':
        job_type = 'remote full time';
        break;
      case 'part':
        job_type = 'remote part time';
        break;
      case 'contracting':
        job_type = 'remote contracting';
        break;
    }
    return `I'm working with Fora (cc'd) to find someone with experience \
in ${skills} for a ${job_type} job. \
I would like to find someone by ${start}. Are you interested${referral ? ' or could you recommend someone' : ''}?`
  },

  SOURCING_INVITE_MESSAGE: (name, skills, start, referral, stype) => {
    let job_type = ''
    switch (stype) {
      case 'full':
        job_type = 'remote full time';
        break;
      case 'part':
        job_type = 'remote part time';
        break;
      case 'contracting':
        job_type = 'remote contracting';
        break;
    }

    return `I'm helping ${name} find someone for a ${job_type} job \
who has these skills and experience: ${skills}. \
${name} would like to find someone by ${start}. Are you interested${referral ? ' or could you recommend someone' : ''}?`

  },

  INVITE_LINK_TEXT: l => `You can see details here: ${l}...`,

  EXPERT_LINK_TEXT: l => `If you can help or know someone who can, click here: ${l}...`,

  LOGIN_CHAT: reply => {
    return {
      hint: undefined,
      answers: [], //'Prviacy & Security'],
      id: `login_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply,
    };
  },

  DUPLICATE_SKILL_CHAT: {
    hint: undefined,
    answers: [],
    id: `duplicate_${new Date().getTime()}`,
    info: [],
    ping: -1,
    reply: [{ label: "You've already added this keyword." }],
  },

  SPECIFIC_SKILL_CHAT: local_chat => {
    return {
      hint: undefined,
      answers: [],
      id: `specific_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: "Could you be more specific?" }],
      local_chat,
    }
  },

  LOGIN_SIGN_IN: 'Follow the link and sign in with your',
  SIGN_IN: 'Sign In',

  GET_CODE: 'Get Code',
  LOGIN: 'Login',
  CHECK_EMAIL: 'Check Your Email',

  LOGIN_LABEL: (providers, pick) => {
    return `I'll get going as soon as you sign in with your ${providers} account.${pick}`;
  },

  META_HINT: '( current role, occupation, or speciality )',

  MORE_NOTES: (n, m) => `${n}${m ? ' more' : ''} Note${n > 1 ? 's' : ''}`,

  NO_REPLIES: 'No replies yet',

  NOTES_PRIVATE: 'Notes are private and can reference people',

  NOTE_SEARCH_KEYS: ['created', 'due', 'title', 'notes'].concat(EMBED_PEOPLE_SEARCH_KEYS),
  EVENT_SEARCH_KEYS: ['label', 'link', 'text', 'date'].concat(EMBED_PEOPLE_SEARCH_KEYS),
  LIST_SEARCH_KEYS: EMBED_LISTS_SEARCH_KEYS.concat(EMBED_CANDIDATE_SEARCH_KEYS),
  PLAN_SEARCH_KEYS: EMBED_PLANS_SEARCH_KEYS.concat(EMBED_CANDIDATE_SEARCH_KEYS),
  ANALYES_SEARCH_KEYS: EMBED_ANALYSES_SEARCH_KEYS.concat(EMBED_CANDIDATE_SEARCH_KEYS),
  PEOPLE_SEARCH_KEYS: EMBED_SEARCH_KEYS,

  ASSIGN_MOBILE: 'Assign',
  ASSIGN: 'Assign Manager',
  POST_AND_SHARE: 'Post and Share',
  SHARE_MOBILE: 'Share',
  FIND_CANDIDATES: 'Find Candidates',
  FIND_MOBILE: 'Find',
  FIND_EXPERTS: 'Find Experts',
  SEND_INVITE: 'Send Invite',

  NOTE_SHARE_PEOPLE: 'Adding People does not share your note. You can click here if you want to share it.',
  NOTE_LINKED_PEOPLE: 'People related to this private note',
  NOTE_PLACEHOLDER: 'Write a private note, reference people, email if you want to share it.',

  PROFILE_PROMPT_OPEN: ["A complete profile lets other users learn more about you, your background, and your skills and experience.", "It’s easy to add and update your profile information. The areas that need attention are highlighted in yellow."],
  PROFILE_PROMPT_50: "I noticed you didn't add much to your profile. This is how I match you for jobs when people ask me for help.",
  PROFILE_PROMPT_75: "Keep going with your profile. The more information you add, the better your odds of being found and hired.",
  PROFILE_PROMPT: "Great job adding to your profile. Share the link with people you know, spread the word, and get hired.",
  PROFILE_PROMPT_SKILLS: "A good place to start is by adding keywords to let others know what you're good at.",
  PROFILE_PROMPT_LINKS: "You can link to your portfolio, your publications, and your social profiles to showcase your talents.",
  PROFILE_PROMPT_META: "Add a tag line or highlight your role or occupation to give hiring managers a sense of how you can help them.",
  PROFILE_PROMPT_TAGS: "Make sure your work and education is up to date so people know your qualifications.",
  PROFILE_PROMPT_BIO: "Your bio helps people learn about your background and passions.",
  PROFILE_PROMPT_TEMPLATES: "Get hired directly from your profile. Create a Freelance Service to describe your work and set your rate.",

  PROFILE_PREVIEW: "Preview your Profile",
  PROFILE_PUBLIC_VIEW: 'Public View',

  CREATE_BIO: 'Add your bio to share about yourself',

  PROFILE_ALL_JOBS: 'Show all Jobs',
  PROFILE_ALL_NOTES: 'Show all Notes',
  PROFILE_TAKE_NOTES: 'Take a private Note',
  PROFILE_COMPLETE: 'Complete Your Profile',
  PROFILE_PROMOTE: 'Promote Yourself',
  PROFILE_PROMOTE_TEXT: 'Share your personal profile link to get hired. Tap to copy the phrase or share directly from the social icons:',
  PROFILE_TEMPLATES: 'Freelance Service Offerings',
  PROFILE_TEMPLATES_TIPS: `Service Offerings make it easier to get found for jobs you want to work on.`,
  PROFILE_TEMPLATES_UNLOCK: 'Get Ready to Hire and unlock Service Offerings',
  PROFILE_CREATE_TEMPLATE: 'Create Freelance Service Offering',
  PROFILE_BIO: u => `About ${u}`,
  PROFILE_EDIT_BIO: 'Bio',
  PROFILE_WORK_EXPERIENCE: 'Work and Education',
  PROFILE_LINKS: 'Links',
  PROFILE_SKILLS: 'Add and edit your specific skills',
  PROFILE_RECOMMEND: u => `Recommend ${u}`,
  PROFILE_MUTUAL_CONNECTIONS: 'Mutual Connections',
  PROFILE_RECOMMENDATIONS: 'Recommendations',
  PROFILE_MY_RECOMMENDATIONS: 'Your Recommendations',
  PROFILE_RECEIVED_RECOMMENDATIONS: 'Received Recommendations',
  PROFILE_GIVEN_RECOMMENDATIONS: 'People You Recommend',
  PROFILE_PEOPLE_RECEIVED_RECOMMENDATIONS: u => `People Recommend ${u}`,
  PROFILE_PEOPLE_GIVEN_RECOMMENDATIONS: u => `People ${u} Recommends`,
  RECOMMENDATION_HINT: n => `Add a recommendation for ${n}. Other people will be able to see this on ${n}’s profile.`,
  PROFILE_PROFILE: n => `${n ? n : ''}${n ? "'s " : ''}Profile`,
  PROFILE_EXPERIENCE: n => `${n ? n : ''}${n ? "'s " : ''}Experience`,
  PROFILE_CONNECTIONS: n => `${n ? n : ''}${n ? "'s " : ''}Connections`,
  PROFILE_GOALS: n => `${n ? n : ''}${n ? "'s " : ''}Learning Goals`,

  SUGGESTED_SKILLS: 'Suggested skills',
  ADD_SKILL: 'Add skill',
  NO_SKILLS: 'No skills',
  LOADING_SUGGESTED_SKILLS: 'Looking for suggested skills...',
  NO_SUGGESTED_SKILLS_ADD: 'Add skills or update your bio for suggestions.',
  NO_SUGGESTED_SKILLS_FULL: 'Nothing to add, your skills are great!',
  GET_FOUND: 'Get found for jobs with a complete profile',
  LEARN_SELF: 'Discover Skills from Public Sources',
  LEARN_ADD_SKILLS: 'Add your skills to get recommendations on learning goals and courses',
  IMPORT_PROFILE: 'Import from LinkedIn',
  IMPORT_MOBILE: 'Import',
  LEARNING_SELF_PROMPT: "I'm checking public sources for information about your experience and skills. I’ll let you know when I’ve updated your profile. You can edit anything on your profile at any time.",
  SUGGEST_SKILLS_PROMPT: "Scroll down to see my suggestions",
  LINK_ACCOUNTS: 'Link Accounts',
  LINK_GOOGLE: 'Link Google Account',
  LINK_MICROSOFT: 'Link Microsoft Account',
  IMPORT_LINKEDIN: 'Import LinkedIn Connections',
  IMPORT_FACEBOOK: 'Import Facebook Connections',
  IMPORT_ICLOUD: 'Import iCloud Contacts',
  IMPORT_CONTACTS: 'Import Contacts',
  UPLOAD_CONTACTS: 'Upload Custom People Lists',
  ADD_CONTACTS: 'Add People You Meet',
  ADD_NAME: 'Add Contact',
  ADD_CARD: 'Scan Business Card',
  IMPORT_PEOPLE: 'Import People',
  IMPORT_TIPS: 'Combine all the people you know from everywhere for a complete picture of your professional network.',
  LAST_IMPORT: 'Imported People',

  STRIPE_OPTIONS: {
    style: {
      base: {
        backgroundColor: '#f9fdff',
        fontFamily: '"Lucida Grande", Helvetica, Arial, sans-serif',
        fontSmoothing: "antialiased",
        fontSize: "14px",
        iconColor: '#2185d0',
      },
      invalid: {
        color: "#fa755a",
        iconColor: "#fa755a",
      },
    },
  },

  PAYMENT_APPEARANCE: {
    theme: 'flat',
    variables: {
      fontFamily: '"Lucida Grande", Helvetica, Arial, sans-serif',
      fontSizeBase: '14px',
      // spacingUnit
      borderRadius: '8px',
      colorPrimary: '#2185d0',
      colorBackground: '#f9fdff',
      // colorText
      // colorDanger
    },
  },

  PAID_STATUS: p => {
    if (!p.amount) return 'This job had no fee';
    const date_format = Intl.DateTimeFormat().resolvedOptions();
    const pay_date = new Date(p.created).toLocaleDateString(date_format.locale, { year: 'numeric', month: 'long', day: 'numeric' });
    return `This job was paid on ${pay_date} and should be deposited in 3-5 business days.`;
  },

  PERSON_NOT_FOUND: 'I couldn\'t find anyone',
  PICK_ONE_PERSON: 'Who did you mean?',
  PICK_ONE_CANDIDATE: 'Check to pick one interested candidate',
  PICK_SOURCING_CANDIDATE: `Select the candidate who accepted the job`,
  PICK_PROVIDER: ' Which do you want to use?',
  PIN_AND_SEND: (count) => `${count} candidate${count === 1 ? '' : 's'}. You can check and send multiple times.`,
  PIN_CANDIDATES: 'Scroll and Check Candidates',
  SEARCH_CHOOSE: "You'll choose how to contact people.",
  PIN_AND_INVITE_CANDIDATES: "Check candidates and tap 'Draft'",
  PIN_AND_GET_REFERAL: "Check candidates and tap 'Referral'",
  PIN_EXPERTS: "Group People", // Check candidates and share your question.",
  ASK_INTRO_CANDIDATES: "Tap the handshake icon and Fora will send you an introduction.",
  ADD_CANDIDATE_PROMPT: 'Who would you like to add to this job?',
  ADD_CANDIDATE_HINT: '<NAME_EMAIL>',
  ADD_CANDIDATE_CANCEL: 'Nevermind',
  ADD_CANDIDATE_CONFIRM: 'No worries',
  GET_INTRO: 'Get Intro',
  PROGRESS: (d, r) => ` of ${d} ${r}`,
  PROJECT_EXPERT_SENT: `Success! I've sent your question and will email you when you get an answer`,
  PROJECT_EXPERT_SELECT_SHARE: `Pick one or more people to share your question with.`,
  PROJECT_SENT: [`Success! I've sent an e-mail invitation to your job.`,
    "I'll e-mail you as candidates express interest and you can view the current status by going to the Select step.",
  ],
  PROJECT_READY: "You're ready to go! You can track progress by selecting 'Progress'",
  PROJECT_READY_PAY: "You're ready to go! Tap 'Progress' and then 'Start' to get going",
  PROJECT_READY_START: f => `Tap 'Start' to ${f > 0 ? 'deposit payment with a credit card' : 'get going'}`,

  EXPERT_HELP: 'Expert request',
  PROJECT_EXPERT_PEOPLE: 'Experts in your network',
  PROJECT_ANSWER_PEOPLE: 'Expert answers',
  PROJECT_ANSWER_SHARED: 'Shared Answers',

  JAB_SEARCH_KEYS: [
    'project.skills',
    'project.notes',
  ],

  PROJECT_SEARCH_KEYS: [
    'start',
    'end',
    'skills',
    'notes',
    'title',
    // 'candidates.name',
    // 'candidates.meta',
    // 'candidates.extra.value',
    // 'candidates.description.label',
    // 'candidates.description.text',
    'contractor.name',
    'contractor.meta',
    'contractor.extra.value',
    'contractor.description.label',
    'contractor.description.text',
    'client.name',
    'client.meta',
    'client.extra.value',
    'client.description.label',
    'client.description.text',
    'me_candidate.name',
    'me_candidate.meta',
    'me_candidate.extra.value',
    'me_candidate.description.label',
    'me_candidate.description.text',
  ],

  TEMPLATE_SUMMARY: (d, r, b) => `${d ? d : ''} ${r} $${b.toLocaleString()}`,
  PROJECT_SHORT_SUMMARY: (p, d, r) => `${p} of ${d} ${r}`,

  PROJECT_EARNED: (e: number, b: number) => `($${e.toLocaleString()} of $${b.toLocaleString()})`,

  PROJECT_SUMMARY: (p, d, r, e: number, b: number) => ` ${p} of ${d} ${r} ($${e.toLocaleString()} of $${b.toLocaleString()})`,

  PROJECT_FIXED_FEE: 'This is a fixed duration job',
  PROJECT_EARNED_DETAIL: (n, e, b) => `${n} $${e.toLocaleString()} out of $${b.toLocaleString()} deposited`,
  PROJECT_SKILLS: 'Skills',
  PROJECT_FEE: 'Fee',
  PROJECT_DURATION: 'Duration',
  PROJECT_START: 'Find By',
  PROJECT_END: 'Complete By',
  PROJECT_CLIENT: 'Manager',
  PROJECT_SOURCING_URL_TIP: 'Applicants will be shown this link to apply',
  PROJECT_SOURCING_URL: 'Application Link',
  PROJECT_SOURCING_TYPE: 'Type',
  PROJECT_PUBLIC: 'Public',
  PROJECT_DATES: 'My Dates are Flexible',

  PROJECT_SOURCING: 'Sourcing',

  PROJECT_EXPERT_CLIENT: 'Find Expert',
  PROJECT_EXPERT_CANDIDATE: n => `Answer ${n ? n : ''}${n ? "'s " : ''}Question`,
  PROJECT_EXPERT_ANSWER_PUBLIC: 'My Answer (Public)',
  PROJECT_EXPERT_ANSWER_SHARED: g => `My Answer (Shared with ${g})`,
  PROJECT_EXPERT_ANSWER_PRIVATE: n => `My Answer (Only ${n} can see this)`,
  PROJECT_EXPERT_ANSWER: 'My Answer',
  PROJECT_EXPERT_ANSWERS: 'Experts',
  PROJECT_EXPERT_QUESTION: 'Question',
  PROJECT_EXPERT_PUBLIC: 'Public',
  PROJECT_EXPERT_SHARED: 'Shared',
  PROJECT_EXPERT_SAVE: 'Update Answer',
  PROJECT_EXPERT_CANCEL: 'Cancel',
  PROJECT_EXPERT_REMOVE: 'Remove Answer',
  PROJECT_EXPERT_EXPERTISE: 'Expertise',

  PROJECT_STEPS_FIND: 'Find',
  PROJECT_STEPS_CLIENT: 'Client',
  PROJECT_STEPS_CONTRACTOR: 'Select',
  PROJECT_STEPS_SELECT: 'Select Candidate',
  PROJECT_STEPS_REVIEW: 'Review Client',
  PROJECT_STEPS_REVIEW_PROPOSAL: 'Review Contractor',
  PROJECT_STEPS_TRACK: 'Track Work',

  PROJECT_STEPS_REVIEW_TIPS: 'See client details',
  PROJECT_STEPS_REVIEW_PROPOSAL_TIPS: 'See contractor details',
  PROJECT_STEPS_SELECT_TIPS: 'See candidate details',

  PROJECT_TEMPLATE: 'Service Offering',
  PROJECT_START_TEMPLATE: 'Freelance Service',
  PROJECT_DESCRIPTION_SHORT: 'Details',
  PROJECT_REVIEW_JOB_SHORT: 'Review',
  PROJECT_DESCRIPTION: 'Job Details',
  PROJECT_DESCRIPTION_TIP: 'Goals, dates, fees',
  PROJECT_REVIEW_JOB: 'Review Job',

  PROJECT_NOT_STARTED: 'Not Started',
  PROJECT_REVIEW: 'For Review',
  PROJECT_DEPOSIT: 'Deposit Payment',
  PROJECT_REVIEW_PROPOSAL: 'Review Proposal',
  PROJECT_SELECT: 'Select',
  PROJECT_RESPONSES: 'Responses',
  PROJECT_SELECT_TIP: 'Pick a contractor',
  PROJECT_RESPONSES_TIP: 'View interest',
  PROJECT_SIGN: 'Sign Agreement',
  PROJECT_SETUP_PAYMENT: 'Set up Payment',
  PROJECT_PROGRESS: 'In Progress',
  PROJECT_PROGRESS_TITLE: 'Progress',
  PROJECT_WAITING: 'Waiting',
  PROJECT_COMPLETE: 'Complete',
  PROJECT_COMPLETED: 'Completed',
  PROJECT_NOT_COMPLETED: 'Not Completed',
  PROJECT_RELEASE: 'Release Payment',
  PROJECT_GET_PAID: 'Get Paid',
  PROJECT_DECLINED: 'Declined',

  REFUND: a => `I'll refund you a total of $${a} for escrow and fees.`,
  CONTRACTOR_CANCEL: u => `I'll let ${u} know that you are not able to complete this job.`,
  CLIENT_DECLINE: u => `I'll let ${u} know that you did not accept this job and archive it for you.`,

  PROJECT_PROGRESS_NO_COMP: 'This job has no compensation',
  PROJECT_PROGRESS_FIXED_FEE: b => `This job pays a fixed fee of $${b.toLocaleString()}`,

  PROMPT_EMAIL: 'Want to see People you E-mail?',
  PROMPT_ORGANIZER: 'Want to see People you meet?',
  PROMPT_CONNECT: 'Import contacts to search your network',
  PROMPT_SEARCH: 'Import contacts for better search results',
  ADD_A_CONTACT: 'Add a Contact',
  ADD_CONTACT: 'Add Contact',
  BUSINESS_CARD: 'Business Card',
  REQUEST_INTRO: 'Request Intro',
  NO_PEOPLE_FOUND: 'No people found',
  SEARCH_FORA_NETWORK: "Try Fora's network and ask for an introduction",
  NO_PEOPLE_FOUND_JOB: "I couldn't find any candidates in your network",
  NO_PEOPLE_FOUND_EXPERT: "I couldn't find any experts in your network",
  NO_PEOPLE_FOUND_FORA_NETWORK: "No one in Fora's network is a match",
  SEARCH_SKILLS: "Update the skills you're looking for and try searching again",
  PICK_SKILLS: 'Pick from top skills',
  YOUR_SKILLS: 'Your top skills',
  SEARCHING: "Searching for candidates...",
  SEARCHING_FOUND: () => {
    return {
      info: [], ping: -1,
      id: `search_Found_${new Date().getTime()}`,
      reply: [
        { label: `I found some candidates for your job.` },
        { label: `You can also add candidates, invite people by sharing this job, or update the skills and ask me to search again.` },
        { label: `Go ahead and pin the candidates you want to invite and tap "Draft Invite."` },
      ],
    }
  },

  SEARCHING_NOT_FOUND: () => {
    return {
      info: [], ping: -1,
      id: `search_not_found_${new Date().getTime()}`,
      answers: ['Import LinkedIn', 'Import Facebook', 'Import iCloud'],
      reply: [
        { label: `I wasn't able to find any candidates for your job.` },
        { label: `You can add candidates, invite people by sharing this job, or update the skills and ask me to search again.` },
        { label: `You can also import LinkedIn, Facebook, or iCloud contacts to expand your search.` },
      ],
    }
  },

  IMPORT_FACEBOOK_PROMPT: [
    "Happy to help import your Facebook contacts in a few simple steps. I've also emailed these to you.",
    // "Facebook doesn't support automatically syncing contacts at this time so there are a few simple steps below to help you get connected.",
    "Start by opening Facebook settings from here:"],
  IMPORT_ICLOUD_PROMPT: [
    "Happy to help import your Apple iCloud contacts in a few simple steps. I've also emailed these to you.",
    // "Apple doesn't support automatically syncing contacts at this time so there are a few simple steps below to help you get connected.",
    "Start by signing in to your iCloud contacts from here:"],
  IMPORT_LINKEDIN_PROMPT: [
    // "LinkedIn doesn't support automatically syncing contacts at this time so there are a few simple steps below to help you get connected.",
    "Happy to help import your LinkedIn contacts in a few simple steps. I've also emailed these to you.",
    "Start by opening LinkedIn settings from here:"],

  IMPORT_UPLOAD: "When your export is ready, just tap the upload button below, select the file, and I'll take care of the rest",

  RECOMMENDED: 'Recommended Someone',

  REFER_MESSAGE: (name, skills, duration, rate, budget: number, start) => {
    const nick = name.split(' ')[0];
    const comp = budget ? ` and the compensation is up to $${budget.toLocaleString()}.` : '.';
    return `I'm helping ${name} find someone for a short term job \
who has these skills and experience: ${skills}. It will take about ${duration} ${rate}${comp}

${nick} would like your contact (candidate) to consider this job. \
If you think (candidate) might be interested, please forward this email on \
${nick}'s behalf or ask Fora to send a referral. \
${nick} would like to find someone by ${start}.`
  },

  REQUEST_PAYMENT: "Go ahead and request payment.",
  REQUEST_PAYMENT_BUTTON: (e: number) => `Request Payment of $${e.toLocaleString()}`,
  REVIEW_CONTRACT: 'Please review and sign your agreement', // when it loads',

  RESEND_MESSAGE: (name, skills, duration, rate, budget: number, start, referral) => {
    const comp = budget ? ` and the compensation is up to $${budget.toLocaleString()}.` : '.';
    const find_by = start ? `${name} would like to find someone by ${start}. ` : '';
    return `${name} is still looking for someone for a short term job \
who has these skills and experience: ${skills}. As a reminder, it will take about ${duration} ${rate}${comp} \
${find_by}Are you interested${referral ? ' or could you recommend someone' : ''}?`
  },

  SOURCING_RESEND_MESSAGE: (name, skills, start, referral, stype) => {
    let job_type = ''
    switch (stype) {
      case 'full':
        job_type = 'remote full time';
        break;
      case 'part':
        job_type = 'remote part time';
        break;
      case 'contracting':
        job_type = 'remote contracting';
        break;
    }
    const find_by = start ? `${name} would like to find someone by ${start}. ` : '';
    return `${name} is still looking for someone for a ${job_type} job \
who has these skills and experience: ${skills}. 
${find_by}Are you interested${referral ? ' or could you recommend someone' : ''}?`

  },

  REPEAT_PROJECT: 'Repeat Job',

  SELECTED_EMAIL: n => `${n} will receive an email with next steps`,
  SEND_AGREEMENT: 'Send Agreement',
  SEND_PROPOSAL: 'Propose Job',
  SEND_INTEREST: (p: Partial<PersonInfo>) => `Let ${p.nick ? p.nick : p.name.split(' ')[0]} know you're interested`,
  SIGN_AGREEMENT: 'Sign Agreement',
  SIGN_CONTRACT: 'You have a service agreement to review and sign',
  SIGN_CONTRACT_BUTTON: 'Review Agreement',
  START_PROMPT: (b: number) => {
    if (b) return `'Start' to deposit $${b.toLocaleString()} and begin the job. You control when your deposit is released.`;
    return `'Start' to begin the job.`;
  },
  START_SEARCH_LOGIN: 'To start my search, I need to learn a bit more about you.',
  STRIPE_GROUP: 'Which payment account do you want to use?',
  STRIPE_PROMPT: 'You have a connected Stripe account. Would you like to use your balance for payment?',
  STRIPE_WAITING: "I can't connect to the payment server, an you try again?",
  STRIPE_ERROR: 'Whoops, there was a problem with your payment. Can you try again?',
  SUBMIT_JOB: 'Submit Job',
  SUBMITTED: nick => `${nick} has submitted your job for review. Tap 'Finish' if the job is complete.`,
  ASK_EXPERT: 'Share Question',
  ASKED_EXPERT: 'Shared',
  NO_ANSWERS: 'There are no answers to your question yet.',
  START_JOB: 'Hire for a Job',
  START_JOB_TEMPLATE: c => c && c.nick ? `Hire ${c.nick} for this job` : 'New Job',
  START_JOB_TEMPLATE_HINT: 'You can edit details before sending the invitation',
  START_PROPOSAL: 'Propose a Job',
  CREATE_TEMPLATE: 'New Offering',
  PROPOSE_TEMPLATE: 'Propose this Job',
  START_ACCEPT_PROPOSAL: c => `Accept ${c.nick}'s proposal to get started.`,

  NEW_ASK: 'New People Search',
  NEW_ANALYSIS: 'New Talent Analysis',
  NEW_PLAN: 'New Development Plan',
  NEW_SOURCING: 'New Sourcing Search',
  NEW_FREELANCE: 'New Freelancing Search',
  NEW_GOAL: 'New Learning Goal',
  NEW_ROLE: 'Add Team Skillset',
  SEARCH_GOALS: 'Add a specific learning goal',
  NEW_GOAL_PREFIX: 'new learning goal on',
  NEW_NOTE: 'New Note',

  SOURCING_PROMPT_TIP: 'What skills are you sourcing for?',
  FREELANCE_PROMPT_TIP: 'What skills are you sourcing for?',

  LEARNING_TIP: `Work through the recommended courses and check to mark them completed. I'll add the course skills to your profile.`,

  PLAN_TIP: `Add or remove skills to this plan, then assign team members and track their progress.`,
  PLAN_TIP_ASSIGN: `Assign team members and track their progress.`,
  PLAN_CANNOT_DELETE: t => `I can't delete your ${t} plan.`,

  ANALYSIS_TIP: `Edit this analysis using the configuration menu on the top left.`,

  NO_ACTIVE_PROJECT: 'There is not active project',

  DRAFT_MEETING: 'Ask to Meet',
  START_EXPERT: 'Ask a Question',
  EDIT_PROFILE: 'Update your Skills',
  LANDING_PROMPT_SKILL: (n, s) => `${n} is a freelancer in my network skilled in ${s}. Who do you know that knows ${s}?`,
  LANDING_PROMPT_NETWORK: n => `${n} is a freelancer in my network. Do you know anyone like ${n}?`,
  LANDING_ACTION_NETWORK: "Hire from your network.",
  LANDING_ACTION_GET_HIRED: "Get discovered and hired by people you know.",
  LANDING_PROMPT_START: "I'm Fora. I help you find hidden talent in your professional network. Tap below to discover who you know.",
  LANDING_ACTION_DISCOVER: "Engage with people you know",
  LANDING_PROMPT_SEARCH: "Here are freelancers in my network who I can introduce you to. Sign up and I'll search your network for people you can hire.",
  LANDING_ACTION_SEARCH: "Search My Network",
  LANDING_LEARN_MORE: 'Learn More',
  LANDING_ACTION_CLIENT: "I'm Looking to Hire",
  LANDING_ACTION_FREELANCER: "I'm a Freelancer",
  LANDING_PROMPT_FREELANCER: "Create a profile to get found and hired by clients in your network.",
  LANDING_PROMPT_CONTRACT: 'My contract protects both clients and freelancers.',
  LANDING_ACTION_SECURITY: "Is My Data Safe?",

  LANDING_PROMPT_INFO: "Learn how to retain talent and improve productivity.",
  LANDING_ACTION_INFO: "More Information",
  LANDING_PROMPT_TEAMS: "Effectively connect employees across your organization.",
  LANDING_ACTION_TEAMS: "Get Teams",

  PROFILE_STEPS: [
    {
      title: 'Profile Information',
      description: 'Add your photo, tagline, contact info, and links',
    },
    {
      title: 'Ready to Hire',
      description: 'Include bio, work and education, and keywords to get found for jobs',
    },
    {
      title: 'Premier',
      description: 'Create a public freelance service offering to get hired',
    },
  ],

  SHARE_COPY_TEXT: 'Hire me on AskFora:',
  SHARE_COPY_GROUP_TEXT: 'Connect with me on AskFora:',

  CONNECT_SHARE_PROMPT: 'Grow your network on AskFora. Send a link to a colleague or post on Facebook, LinkedIn, and Twitter',
  CONNECT_SHARE_SUBJECT: 'Do better work with me on AskFora.',
  CONNECT_SHARE_BODY: u => `How do you do better work? On AskFora, with people you know. AskFora is an easy way to find freelancers that you already know with the skills you need for your short term jobs. If you're a freelancer looking for more work, you can create a personal profile to get found more easily. Build your network by connecting with me at ${u}`,
  CONNECT_SHARE_TWITTER: u => `I'm using AskFora to connect with people I know for freelance work. Clients find people in their network with the right skills for their short term jobs and freelancers can share their profile to help clients hire them more easily. Build your network by connecting with me at ${u}`,
  CONNECT_SHARE_FACEBOOK: u => `I'm using AskFora to connect with people I know for freelance work. Clients find people in their network with the right skills for their short term jobs and freelancers can share their profile to help clients hire them more easily. Build your network by connecting with me at ${u}`,
  CONNECT_SHARE_LINKEDIN: u => `I'm using AskFora to connect with people I know for freelance work. Clients find people in their network with the right skills for their short term jobs and freelancers can share their profile to help clients hire them more easily. Build your network by connecting with me at ${u}`,
  CONNECT_SHARE_PROFILE_EMAIL_SUBJECT: 'Connect with me on AskFora',
  CONNECT_SHARE_PROFILE_EMAIL_BODY: url => `How do you do better work? On AskFora, with people you know. Just describe the job and set a rate. AskFora handles the agreement, progress tracking, and payment processing. Build your network by connecting with me from this link: ${url}`,
  CONNECT_SHARE_PROFILE_FACEBOOK_QUOTE: "How do you do better work? On AskFora, with people you know. Just describe the job and set a rate. AskFora handles the agreement, progress tracking, and payment processing. Build your network by connecting with me.",
  CONNECT_SHARE_PROFILE_TWITTER_TITLE: "How do you do better work? On AskFora, with people you know. Just describe the job, set a rate. AskFora handles agreement, progress tracking, and payment processing. Build your network by connecting with me.",

  SHARE_PROMPT: 'Help us spread the word. Send a link to a colleague or post on Facebook, LinkedIn, and Twitter',
  SHARE_PROMPT_PROMO: 'Share the love! Refer colleagues to AskFora through March 31, 2023 and earn a personalized ad.',
  SHARE_SUBJECT: 'Have you heard about AskFora?',
  SHARE_BODY: u => `Have you seen AskFora? It's an easy way to find freelancers that you already know with the skills you need for your short term jobs. If you're a freelancer looking for more work, you can create a personal profile to get found more easily. Check it out at ${u}`,
  SHARE_TWITTER: `I'm using AskFora to connect with people I know for freelance work. Clients find people in their network with the right skills for their short term jobs and freelancers can share their profile to help clients hire them more easily.`,
  SHARE_FACEBOOK: `I'm using AskFora to connect with people I know for freelance work. Clients find people in their network with the right skills for their short term jobs and freelancers can share their profile to help clients hire them more easily.`,
  SHARE_LINKEDIN: `I'm using AskFora to connect with people I know for freelance work. Clients find people in their network with the right skills for their short term jobs and freelancers can share their profile to help clients hire them more easily.`,

  SHARE_JOB_SUBJECT: 'I just completed a job on AskFora!',
  SHARE_JOB_BODY: u => `I just completed a job using AskFora. AskFora makes it really easy to connect with people you know for short term work. Payment processing, progress tracking, and protection under a fair service agreement are all handled seamlessly through the site. Check it out at ${u}`,
  SHARE_JOB_TWITTER: `I just completed a job using AskFora! AskFora handles the agreement, progress tracking, and payment processing so you can do better work with people you know.`,
  SHARE_JOB_FACEBOOK: `I just completed a job using AskFora! AskFora handles the agreement, progress tracking, and payment processing so you can do better work with people you know`,
  SHARE_JOB_LINKEDIN: `I just completed a job using AskFora! AskFora handles the agreement, progress tracking, and payment processing so you can do better work with people you know.`,

  SHARE_PROFILE_EMAIL_SUBJECT: 'Hire me on AskFora',
  SHARE_PROFILE_EMAIL_BODY: url => `I'm using AskFora to make it easy to hire me for short term work. You just need to describe the job and set a rate. AskFora handles the agreement, progress tracking, and payment processing. You can hire me on AskFora from this link: ${url}`,
  SHARE_PROFILE_FACEBOOK_QUOTE: "I'm using AskFora to make it easy to hire me for short term work. You just need to describe the job and set a rate. AskFora handles the agreement, progress tracking, and payment processing.",
  SHARE_PROFILE_TWITTER_TITLE: "Hire me on AskFora quick & easy. Just describe the job, set a rate. AskFora handles agreement, progress tracking, and payment processing.",

  SHARE_PROFILE_GROUP_EMAIL_SUBJECT: 'Connect me on AskFora',
  SHARE_PROFILE_GROUP_EMAIL_BODY: url => `I'm using AskFora to make it easy to learn new skills. You just need to describe what you want to learn and AskFora finds online courses and recommends contacts based on your current skills. You can connect with me on AskFora from this link: ${url}`,
  SHARE_PROFILE_GROUP_FACEBOOK_QUOTE: "I'm using AskFora to make it easy to learn new skills. You just need to describe you want to learn and AskFora .",
  SHARE_PROFILE_GROUP_TWITTER_TITLE: "Learn with me on AskFora quick & easy. Just describe what you want to learn. AskFora finds online courses and recommends contacts based on your current skills.",

  SHARE_TUTORIAL: u => `I'm using AskFora to learn how to do better work with people I know. Check it out at ${u}`,
  SHARE_TUTORIAL_SUBJECT: 'Have you heard about AskFora?',
  SHARE_TUTORIAL_TWITTER: `I'm using AskFora to learn how to do better work with people I know.`,
  SHARE_TUTORIAL_FACEBOOK: `I'm using AskFora to learn how to do better work with people I know.`,
  SHARE_TUTORIAL_LINKEDIN: `I'm excited to be working with AskFora to improve my skills and capabilities. AskFora helps me deliver better results and do better work with people I know.`,
  SHARE_UNLOCK: `Share AskFora and get free access to this lesson`,
  SHARE_UNLOCK_TEN: `Earn a free lesson for every 10 referrals`,
  SHARE_TUTORIAL_URL: u => `Try a free lesson with my personal link: ${u}`,

  SHARE_PUBLIC_PROFILE_SUBJECT: `View this certified skills profile on AskFora`,
  SHARE_PUBLIC_PROFILE: u => `Check out this AskFora certified skills profile ${u}`,
  SHARE_PUBLIC_PROFILE_FACEBOOK: `View this certified skills profile on AskFora`,
  SHARE_PUBLIC_PROFILE_LINKEDIN: `View this certified skills profile on AskFora`,

  NEW_JAB: 'New Job Template',
  NEW_SOURCING_JAB: 'New Sourcing Template',
  ADMIN_JAB: 'Ask an Administrator to Create Templates',
  JAB_PROMPT: 'Create a New Job Link',
  JAB_EDIT: 'Edit a Job Link',
  JAB_PROJECT_SUBJECT: 'Do better work with AskFora',
  JAB_PROJECT_EMAIL_SUBJECT: 'Do better work with AskFora',
  JAB_PROJECT_EMAIL_BODY: (s, u) => `Get a quick referral from someone you know and trust at AskFora. Here's a ${s} job spec: ${u}`,
  JAB_PROJECT_FACEBOOK_QUOTE: s => `Get a quick referral from someone you know and trust at AskFora. Here's a ${s} job spec.`,
  JAB_PROJECT_TWITTER_TITLE: s => `Get a quick referral from someone you know and trust at AskFora. Here's a ${s} job spec:`,
  JAB_PROJECT_LINKEDIN: (s, u) => `Get a quick referral from someone you know and trust at AskFora. Here's a ${s} job spec: ${u}`,
  JAB_TOOLS: 'Template Tools',
  SHARE_JAB: 'Share Template',
  JAB_COPY: 'Quick Copy',
  NEW_JOB: 'New Job from Template',

  LONG_JAB_PROJECT_EMAIL_BODY: (s, u) => `Try AskFora to do better work with people you know. Fora searches your network for trusted quality candidates. Here's a ${s} job spec: ${u}`,
  LONG_JAB_PROJECT_FACEBOOK_QUOTE: s => `Try AskFora to do better work with people you know. Fora searches your network for trusted quality candidates. Here's a ${s} job spec.`,
  LONG_JAB_PROJECT_TWITTER_TITLE: s => `Try AskFora to do better work with people you know. Fora searches your network for trusted quality candidates. Here's a ${s} job spec: `,
  LONG_JAB_PROJECT_LINKEDIN: (s, u) => `Try AskFora to do better work with people you know. Fora searches your network for trusted quality candidates Here's a ${s} job spec: ${u}`,

  SHARE_PROJECT_EMAIL_SUBJECT: 'Available for this job?',
  SHARE_PROJECT_EMAIL_BODY: url => `I'm using AskFora to find help. Take a look and let me know if you're interested: ${url}`,
  SHARE_PROJECT_FACEBOOK_QUOTE: "I'm using AskFora to find help. Take a look and let me know if you're interested.",
  SHARE_PROJECT_TWITTER_TITLE: "I'm using AskFora to find help. Take a look and let me know if you're interested.",

  SHARE_PROPOSAL_EMAIL_SUBJECT: 'Do better work with AskFora',
  SHARE_PROPOSAL_EMAIL_BODY: url => `Try AskFora to do better work with people you know. Take a look and let me know if this service helpful. ${url}`,
  SHARE_PROPOSAL_FACEBOOK_QUOTE: "Try AskFora to do better work with people you know. Take a look and let me know if this service helpful.",
  SHARE_PROPOSAL_TWITTER_TITLE: "Try AskFora to do better work with people you know. Take a look and let me know if this service helpful.",

  SHARE_TEMPLATE_EMAIL_SUBJECT: 'Available to hire',
  SHARE_TEMPLATE_EMAIL_BODY: url => `I'm available to hire on AskFora. Take a look and let me know if I can help.  ${url}`,
  SHARE_TEMPLATE_FACEBOOK_QUOTE: "I'm available to hire on AskFora. Take a look and let me know if I can help.",
  SHARE_TEMPLATE_TWITTER_TITLE: "I'm available to hire on AskFora. Take a look and let me know if I can help.",

  SHARE_PROJECT_EXPERT_EMAIL_SUBJECT: 'Looking for your expertise',
  SHARE_PROJECT_EXPERT_EMAIL_BODY: url => `I'm using AskFora to find an expert. Take a look and let me know if you can help: ${url}`,
  SHARE_PROJECT_EXPERT_FACEBOOK_QUOTE: "I'm using AskFora to find an expert. Take a look and let me know if you can help.",
  SHARE_PROJECT_EXPERT_TWITTER_TITLE: "I'm using AskFora to find an expert. Take a look and let me know if you can help.",

  SEARCH_MESSAGE: "Search your network or engage people around the world.",

  TIMEOUT_ERROR: 'I missed that. Can you try again?',

  UNSELECT_COMPLETE: 'Your job is complete.', // You can go ahead and release payment.',
  UNSELECT_ESCROW: f => `You're ready to start this job. You can still delete it if something changes ${f > 0 ? "and I'll refund your deposit" : ''}`,
  UNSELECT_STARTED: "You've already started this job. Make sure your progress is up to date and let me know when you're finished.",
  UPLOADING: 'Uploading, sit tight...',
  UPLOAD_ERROR: 'I had some trouble uploading your file. Can you try again?',
  PHOTO_ERROR: 'I had some trouble uploading your photo. You may need to save it as a smaller file and try again.',

  USER_SENDS_BUTTON: "I'll send",

  LOADING_FIRST: 'Loading your network...',
  LOADING_NETWORK: "You sure are popular! Loading other users in your network...",
  LOADING_GROUP: "Wow, you know a lot of people...almost found everyone...",

  VIEW_AGREEMENT: 'Download Agreement',
  VIEW_CONTRACT: 'Loading...',

  WAITING_ON_CLIENT: (c: Partial<ContractInfo>) => `Waiting on ${c.client_name} to sign the service agreement`,

  WAITING_ON_SEND: 'Share this Job',
  WAITING_ON_SEND_PROPOSAL: (p?: Partial<PersonInfo>) => `Share this proposal with ${p.nick ? p.nick : p.name ? p.name.split(' ')[0] : 'client'}`,
  WAIT_AGREEMENT: 'Waiting for Agreement',
  WAITING_ON_ACCEPT: (p?: Partial<PersonInfo>) =>
    `Waiting on ${p.nick ? p.nick : p.name ? p.name.split(' ')[0] : 'client'} to review your proposal`,
  WAITING_ON_AGREEMENT: (p?: Partial<PersonInfo>) =>
    `Waiting on ${p.nick ? p.nick : p.name ? p.name.split(' ')[0] : 'client'} to send the service agreement`,
  WAITING_ON_CONTRACT: (p?: Partial<PersonInfo>) =>
    `Waiting on ${p.nick ? p.nick : p.name ? p.name.split(' ')[0] : 'other party'} to review the service agreement`,
  WAITING_ON_DEPOSIT: (f: number, p: Partial<PersonInfo>) =>
    `Waiting on ${p.nick ? p.nick : p.name ? p.name.split(' ')[0] : 'client'} to ${f > 0 ? 'deposit payment' : 'start your job'}`,
  WAITING_ON_PAYMENT_SETUP: (p: Partial<PersonInfo>) =>
    `Waiting on ${p.nick ? p.nick : p.name ? p.name.split(' ')[0] : 'freelancer'} to set up a payment account`,
  WAITING_ON_SELECT: (p?: Partial<PersonInfo>) =>
    `Waiting on ${p.nick ? p.nick : p.name ? p.name.split(' ')[0] : 'client'} to pick someone`,
  WAITING_FOR_REPLY: 'Waiting for a Reply',
  WAITING_ON_START: (p?: Partial<PersonInfo>) => `Waiting on ${p.nick} to start this job `,

  WORKING_WITH: n => `You're working with ${n} on this job`,

  /* ==================== AskFora v3 below here ======================== */
  INIT_CHAT: () => {
    return {
      id: `init_${new Date().getTime()}`, info: [], ping: -1,
      answers: ['Sign Up for Free', 'Login'], //, 'Demo'],
      reply: [
        // { label: `Hi, I'm Fora! I help you make better use of your professional network, and manage your team based on their skills.` },
        { label: `Hi, I'm Fora. I can help you make better use of your professional network and develop your skills through learning goals.` },
        { label: `You can sign up for free, or tap 'Login' if you already have an account.` }
      ]
    }
  },

  INIT_REPEAT: () => {
    return {
      id: `init_${new Date().getTime()}`, info: [], ping: -1,
      answers: ['Sign Up for Free', 'Login'], //, 'Demo'],
      reply: [
        { label: `Sorry, I'm not quite sure how to help with that, but if you let you know a bit more about you I can try again.` },
        { label: `If we've met, you can tap 'Login.' Otherwise go ahead and sign up for free.` }
      ]
    }
  },

  HELP_KWDS: [
    'help', 'how do', 'tell', 'show', 'examples', 'example', 'features', 'feature', 'tutorial',
    'help', 'tips', 'learn more',
    'tip',
    'find work', 'freelance', 'freelancing', 'contracting', 'profile', 'edit profile', 'share profile', 'share my profile', 'edit my profile', 'complete profile', 'complete my profile', 'my profile', 'show profile', 'show my profile',
    'profile', 'edit profile', 'share profile', 'share my profile', 'edit my profile', 'complete profile', 'complete my profile', 'my profile', 'show profile', 'show my profile',
    'something else',
    'what',
    'examples', 'example', 'features', 'feature', 'how',
    'find people',
    'data', 'privacy', 'terms', 'security', 'about', 'blog',
  ],

  ASKS: 'Searches',
  ASKS_TIPS: 'Find people you know by name, skill, or industry.',
  ASKS_SUBSCRIBE: `AskFora Professional offers advanced search features and personalized learning goals to help you learn new skills.`,
  HOME: 'Home',
  CLOSE: 'Close',
  NETWORK: 'Network',
  EXPLORE: 'Explore',
  ENGAGE: 'Engage',
  TEAMS: 'Teams',
  ROLES: 'Skillsets',
  TALENT: 'Talent',
  TALENT_TIPS: 'Understand your talent ecosystem by analyzing team skillsets and mapping talent to future needs.',
  ANALYZE: 'Analyze',
  DEVELOP: 'Development',
  DEVELOP_TIPS: `Assign learning goals to help team members develop their skills`,
  // `Identify learning goals for team members and AskFora will provider personalizd online course recommendations.`,
  // 'Plan for company growth by identifying internal candidates whose skills align with those needed in future roles.',
  DEVELOP_PLAN: `Directional upskilling through team wide learning goals.`,
  DEVELOP_PEOPLE: `People with assigned learning goals`,
  MAP: 'Map',
  PLAN: 'Plan',
  DIRECTORY: 'Directory',
  EMPLOYEES: 'Team Directory',
  SOURCING: 'Sourcing',
  SOURCING_TIPS: 'Source candidates from your network to fill open positions in your organization.',
  // SOURCE: 'Source',
  EVENTS: 'Events',
  JOBS: 'Freelancing',
  JOBS_TIPS: 'Hire freelancers from your network.',
  HIRE_TIPS: 'Get discovered and hired by people you know.',
  LEARNING: 'Learning',
  SHARE: 'Share',
  PROFILE: 'Profile',
  SETTINGS: 'Settings',
  LOGOUT: 'Logout',
  MORE_MENU: 'More...',

  LAST_WEEK: 'Last Week',
  OLDER: 'Before Last Week',

  ASK_DELETE_CONFIRM: `Deleting this search will remove all analyses and expert requests, and it will not longer be shared.`,

  LEARNING_SKILLS: 'Review your Skills',
  LEARNING_EDIT_BIO: 'Update your Bio',
  LEARNING_GOALS: 'Learning Goals',
  LEARNING_GOALS_TIP: 'Achieve learning goals through continued education.',
  LEARNING_GOALS_EG: 'AskFora recommends online courses for your learning goals.', // so you can improve your skills.',
  LEARNING_GOALS_EG_TRACK: 'Track learning goals on the left menu. Keep your skills updated on the right.',
  LEARNING_GOALS_EG_MOBILE: 'AskFora recommends online courses for your learning goals so you can improve your skills.',
  LEARNING_GOALS_EG_MOBILE_TRACK: 'Track learning goals on the left menu. Keep your skills updated by tapping the bottom left trophy.',
  //'Track courses by tapping your learning goals on the left menu and keep your skills updated by tapping the bottom left.',
  // `Search for goals you want to achieve, including job and industry details. Be as specific as possible. Fora will find matching goals and recommend online learning courses to improve your skills.`,
  ACHIEVED_GOALS: `Learning goals you've achieved`,
  LEARNING_GOALS_ADD: `AskFora recommends online courses for your learning goals so you can improve your skills.`,
  LEARNING_GOALS_START: `Start by asking Fora to discover your skills from public sources`,
  LEARNING_GOALS_ADD_STEPS: [
    `1. Ask Fora to discover skills from public data sources.`,
    `2. Update your bio and review your skills`,
    `3. Your learning goals will be listed in the menu on the left.`,
  ],
  LEARNING_PROMPT_TIP: 'What learning goals do you have? Include job and industry details. E.g. Mobile UX designer for AI SaaS.',
  LEARNING_EXPERTS: 'Recommended Contacts',
  LEARNING_RECS: 'Recommended Courses',
  COURSES_TIPS: `Mark courses as completed to add those skills to your profile. Remove courses that you're not interested in.`,
  LEARNING_COMPLETE: 'Completed Courses',
  LEARNING_IGNORE: 'Archived Courses',
  LEARNING_POST: `Skills You'll Learn:`,
  LEARNED_SKILLS: 'Achieved Goals',
  COMPLETE_COURSE: 'Completed',
  REMOVE_COURSE: 'remove course',
  COURSE_TYPES: 'Learning Platforms',
  LEARNING_SUGGESTIONS: 'Pick a suggested Learning Goal',
  RESET_GOALS: 'Reset all learning goals',
  RESET: 'Reset',
  RECOMMEND_GOALS_TIP: 'AskFora can recommend learning goals based on your skills',
  RECOMMEND_GOAL: 'Get learning goal suggestions',
  LEARNING_GOALS_RESET: `Resetting will delete all your learning goals and remove skills that were learned from completed courses.`,
  LEARNING_GOAL_ACHIEVED: g => `Congratulations! You've achieved proficiency in your ${g} Learning Goal. 
    You'll see it as an achievement on the Learning page instead of on the left. 
    You can always continue working on courses related to this goal.`,
  LEARNING_GOAL_COMPLETE: `Great job completing a course! I've added the skills you learned to your profile.`,
  LEARNING_GOAL_UNCOMPLETE: `I reset your course and adjusted your skills.`,

  ROLES_TIP: `Create and refine the skillsets that your team can use for learning goals and workforce planning.`,
  ROLES_ADD: `Each skillset includes a set of skills that can be used to find, evaluate, and assign learning goals to team members.`,

  ALISON: 'Alison',
  LINKEDIN: 'LinkedIn Learning',
  SKILLSHARE: 'SkillShare',
  EDX: 'edX',
  UOPX: 'University of Phoenix',
  TED: 'TED Talks',
  MASTERCLASS: 'MasterClass',
  UDEMY: 'Udemy',
  COURSERA: 'Coursera',

  NOTES_TIPS: `Take private notes that you can link to jobs and people.`,
  TAKE_NOTES: `Keep notes about people you work with.`,


  SEARCH: 'Search...',
  SEARCH_QUICK: 'Quick Search...',
  SEARCH_CONTACTS: 'Search people you know...',
  SEARCH_NETWORK: 'Search your network...',
  SEARCH_TEAM: 'Search your team...',
  SEARCH_EVENTS: 'Search your events...',
  SEARCH_NOTES: 'Search your notes...',
  SEARCH_LISTS: 'Search your lists...',
  SEARCH_PLAN: 'Search your plans...',
  SEARCH_JOBS: 'Search your jobs...',
  SEARCH_PROFILE: 'Search your profile...',
  SEARCH_SETTINGS: 'Search your settings...',

  // HOME_TIPS: ['Who am I meeting today?', 'Who should I reconnect with?', 'What else can you do?'], //'What skills should I explore?', 'Suggest someone to meet'],

  SKILLS_GROUP: 'Skills Group',

  GET_TO_KNOW: 'Get to know people',
  KNOW_INFO: u => `Tap to learn more about ${u.name}`,

  NOTES_INFO: 'Tap a person to view skills, when you met, and keep notes. ',

  SEARCH_PEOPLE: 'Maintain connections',
  SEARCH_INFO: 'Tap a question to find out the answer',

  ASK_EXPERTS: 'Learn from people',
  ASK_INFO: 'Tap a skill and ask a question that experts will answer',
  ASK_BODY: (title, url) => `Here's my shared list of "${title}" people: ${url}`,
  ASK_SHARE: 'Share this search',

  FIND_FREELANCER: 'Hire people',
  FREELANCE_INFO: 'Tap a skill to search for freelancers who can help',

  SHORTCUTS: 'Shortcuts',

  RECENT_PEOPLE: 'Recent People',
  MY_CONTACTS: c => `Directory of People you Know (${c})`,
  CONTACTS_TIP: "An integrated list of all the people you know, with quick view of their top skills and your private notes. Browse and search top skills to find exactly who you're looking for.",
  EXPLORE_SKILLS: (c?) => c ? `Explore Skills in Your Network (${c})` : 'Explore Skills',
  EXPLORE_SKILLS_TIP: 'Browse the skills of people in your first, second, and group connections',
  TOP_SKILLS: 'Top skills',
  TOP_SKILLS_TEAM: 'Top skills in your organization',
  TOP_SKILLS_TEAM_TIP: 'More common skills are larger and darker blue',
  KEEP_IN_TOUCH: 'Lists of people to stay in touch with',
  NETWORK_TIP: "Maintain and grow your network with referrals and introductions. Create lists of people you can share with colleagues. Use one-tap reminders to stay in touch with people in your network.",
  ENGAGE_TIP: ["Discover people with the skills and experience you're looking for.", "Poll your network to find the answers you need.", "Intelligent matching helps you discover people in your network who can help."],
  NETWORK_SHORTCUTS: ['Find people I know', 'Introduce colleagues', 'Ask for introductions'],
  ENGAGE_SHORTCUTS: ['Skills to learn', 'Discover people', 'Expand my network'], // ['Skills to learn', 'Discover people with skills in my network', 'Expand my network']
  CATEGORY_RANKS: 'Category ranks',
  CATEGORY_SKILLS: 'Top Skills To Learn',

  RECENT_EMPLOYEES: 'Recent Employees',
  MAKE_CONNECTIONS: 'Maintain your network',
  LEARN_DISCOVER: `Engage with your network`,

  PEOPLE_DIRECTORY: 'People Directory',
  LIST_INFO: 'Your saved people',
  LIST_PEOPLE: 'Saved People',

  FILTER: "Describe the people you're looking for",
  FILTER_REMOVE: 'remove filter',
  FILTER_LABEL: 'Any skill and all criteria',
  FILTER_ADD_PERSON: 'Add a person',
  FILTER_ADD: 'Add filter',
  FILTER_ADD_CRITERIA: 'Add criteria',
  FILTER_REMOVE_CRITERIA: 'remove criteria',
  CATEGORY_ADD: 'Add category',

  CATEGORY_SHOW: `Show matching people`,
  CATEGORY_GENERATE: `Generate`,

  ENGAGE_INFO: "Engage people by skill",
  ENGAGE_SKILL_CATEGORIES: 'Matching Skillsets',
  ENGAGE_QUALITY: 'Proficiency',
  ENGAGE_QUALITY_SHORT: 'Score',
  ENGAGE_SKILL_CATEGORIES_TIP: 'Skillsets are based on people who match your filters.',
  ENGAGE_PEOPLE: "People in Related Skillsets",
  ENGAGE_CONFIG: 'Filter and Find People',
  ENGAGE_CONFIG_TIP: 'Pick from suggested skills or add some to search. Review and edit related skillsets. Then scroll down to see people in each category.',
  ENGAGE_ACTIONS: 'Check people to add them',
  ASK_SEARCH_ANALYZE: `You'll need to search for some people before I can run an analysis.`,
  ASK_SEARCH_EXPLORE: `You'll need to search for some people before I can show their skills.`,
  ASK_SEARCH_EXPERT: `You'll need to search for some people before you can ask a question.`,

  ENGAGE_QUESTIONS: 'Your Questions and Answers',
  ENGAGE_QUESTIONS_TIP: "Ask questions, send polls, and keep track of responses.",

  ANALYZE_INFO: "Analyze and develop team skills",
  ANALYZE_CATEGORIES: 'Current team skillsets',

  FUTURE_ROLES: 'Refine future skillsets you need, then tap to save and categorize team members.',
  ANALYZE_NO_RESULTS: `I wasn't able to find anyone that's a fit for your future skillsets.`,
  ANALYZE_RESULTS_NO_CATS: `I found some people who match your future skillsets. I can generate the current skillsets to group people based on their skills or you can manually add current skillsets to analyze.`,
  ANALYZE_RESULTS: [`I found some people who match your future skillsets. I've grouped people into skillsets based on their skills.`,
    `You can review and edit the current skillsets, update the future skillsets, or add additional filters by tapping 'Edit Analysis' at the top.`],

  // TEAM_ANALYSIS: `Analyze the skills distribution of your team, discover team members hidden skills, plan strategically for future skills based on your team's strengths and weaknesses, identify development areas for individuals and teams, and source candidates in your team's professional network to fill open roles and find new new talent.`,
  TEAM_ANALYSIS: "Analyze, plan, and develop team skills",
  TEAM_ANALYSIS_MOBILE: 'Analyze, plan, develop',
  TEAM_PROMPT: 'What skillsets are you planning for?',
  TEAM_PROMPT_TIP: 'Skillsets to plan for',
  TEAM_PLAN: 'Team Skill Plans',
  TEAM_SKILLS: 'Team Skills ',
  ANALYZE_TEAM: 'Current team skills',
  EMPLOYEES_TIP: 'Search your team by skill, see notes and details at a glance, search and filter by tenure, department, seniority and any attribute.',
  MAP_TEAM: 'Future role mapping',
  DEVELOP_TEAM: 'Skillset development',
  SOURCE_TEAM: 'Identify candidates in your extended network',
  // DEVELOPMENT_TIP: 'Analyze skills of people on your team, plan for the future skills that align with your strategic direction, and identify learning goals for team members.',
  SOURCE_TIP: 'Find candidates based on up to date skills. Source from the professional networks of your entire team. Track views, interests, and referrals.',
  ANALYSIS_EMPTY: 'No people or skills to analyze',
  ANALYSIS_DATA_EMPTY: 'No matching people',
  PLAN_EMPTY: 'Add future skills to generate a plan',
  EMBED_ANALYSIS: 'Skill Category Analysis',
  ANALYSIS_MSGS: [
    'Analyzing skills',
    'Measuring twice',
    'Accessing databanks',
    'Checking for fit',
    'Organizing data models',
    'Optimizing mappings',
    'Staging analysis',
    'Preparing to analyze',
    'Reading the manual',
    'Double checking the model',
    'Burning GPU cycles',
    'Compounding interest',
    'Clearing the cache',
    'Fetching skills',
    'Taking a quick breather',
    'Computing mappings',
  ],

  ANALYZE_SHORTCUT: 'Analyze Team Skills',
  SOURCE_SHORTCUT: 'Source Candidates',

  TEAM_TIP: 'Setup team analysis with filters, and current and future skillsets',
  TEAM_CONFIG_ANALYZE: 'Skills to analyze',
  TEAM_CONFIG_COUNT: 'Number of People',
  ADD_SET: 'Add future set',
  REMOVE_SET: 'remove set',
  TEAM_CONFIG_RELATED: 'Releated skills',
  TEAM_CONFIG_FILTER: 'Filter people by attributes',
  TEAM_CONFIG: 'Filter Team Members',
  TEAM_EDIT: 'Edit Analysis',
  TEAM_VIEW: 'View Analysis',

  CURRENT_SKILLS: 'Current Team Skills',
  FUTURE_SKILLS: 'Future Skillsets',

  DEVELOP_KEY: 'Size shows number of employees. Darker colors show percent fit',

  INTRODUCE: 'Introduce',
  INTRODUCE_COLLEAGUES: 'Introduce your colleagues',
  INTRO_TIP: 'Get help drafting introductions between people you know.',
  INTRO_COMMAND: 'Make an introduction',
  INTRO_SUGGEST: 'Suggest introductions',

  ASK_SELECT: 'Select people',
  ASK_INTRO: 'Ask for an introduction',
  ASK_TIP: 'Expand your network by getting introduced to new people',
  ASK_COMMAND: 'Ask for an introduction',
  ASK_SUGGEST: 'Suggested introduction requests',
  ASK_CATEGORIES: 'Skillsets',

  NO_RESULTS: 'No results',

  FIND_PEOPLE: 'Stay connected',
  SHARE_PEOPLE: 'My Saved People',
  SHARE_TIP: 'Remember people for your reference and to recommend',
  LIST_REFRESH: 'Refresh people',
  LIST_ACTIONS: 'Share and engage with people',
  LIST_ENGAGE: 'Engage with people',
  LIST_HIRE: 'Hire for work',
  LIST_CONFIGURE: 'Manage saved people',
  LIST_TIPS: 'Refresh to find new people, create a new engagement plan to ask questions, or create a new job with these people as candidates. Share this list of people you recommend.',
  LIST_PUBLIC: 'Publicly Visible',
  LIST_SHARE: 'Share list',

  FIND_COMMANDS: s => [
    //"Who did I meet for the first time last week?",
    //"Who haven't I met in a while?",
    //"Who should I catch up with?",
    s ? `Who should I talk to about ${s}?` : undefined,
  ],

  LEARN_SKILLS: 'People help you learn skills',
  LEARN_TIP: 'Who can help me learn about...',

  DISCOVER_SKILLS: 'Explore skills in your network',
  DISCOVER_TIP: 'What skills are related to...',

  FILTER_TITLE: 'New Filter',
  CATEGORY_TITLE: 'Category Title',
  CATEGORY_HINT: 'Skills you want to categorize',

  EXPAND_NETWORK: 'Engage with experts in your network',
  EXPAND_TIP: 'Engage with your network to find experts who can help you learn new skills, answer questions, and solve problems.',
  ADVANCED_SEARCH: 'Advanced Search',
  ADVANCED_TIP: 'Use boolean expressions to search',
  DEVELOP_EMPTY: 'No one is a good match to develop for this skill category',
  DEVELOP_TIP: 'Select a category to identify employees to develop',
  PLAN_MAP: 'Talent Plan Skills Map',
  PLAN_TITLE: 'Plan Title',

  DEVELOP_COMPLETED: (a, b) => `Completed ${a}/${b} courses`,
  DEVELOP_NOT_STARTED: `No courses yet`,
  DEVELOP_UPDATE: d => `Last updated on ${d}`,

  ASK_HELP: 'How can Fora help you find people?',
  // ASK_PROMPTS: ['Who am I meeting today?', 'What skills should I learn?', 'Tell me about people I know'],
  ASK_PROMPTS: ['Who am I meeting today?', 'Who should I meet with?', "Help me find people.", 'What else can you do?'], //, 'Find experts I know.'], //, 'What are my team members good at?' ],
  ASK_NEXT: "What can you do once you've found people?",
  ASK_POWER: "Power your results with  ",
  ASK_HELP_LEARNING: 'What can you do?',
  ASK_PEOPLE: 'Search for people then tap',

  ASK_QUESTION: 'Ask Question',
  ASK_MEET: 'Ask to Meet',
  SEND_MEET: 'Send Invite',
  ASK_QUESTION_MOBILE: 'Ask Question',
  ASK_MEET_MOBILE: 'Meet',
  ASK_EMAIL: 'Send E-mail',
  ASK_EMAIL_MOBILE: 'Send E-mail',
  PROPOSED_TIMES: 'Proposed times:',
  MESSAGE_TIP: 'What message would you like Fora to send?',

  NEW_QUESTION: 'New Question',

  INCLUDE_NETWORK: 'Include extended network',

  HOME_PEOPLE: 'Shortcuts',
  DATE_PEOPLE: 'Today People',
  NOTES_PEOPLE: 'Notes People',
  REQUEST_PEOPLE: 'Expert People',
  PROJECT_PEOPLE: 'Job People',
  RESULT_PEOPLE: 'Result People',
  FOUND_PEOPLE: 'Found People',
  DISCOVER_PEOPLE: 'Engage People',
  EXPERT_REQUESTS: 'Related Questions and Meetings',
  PROJECT_TEMPLATES: 'Job Templates',
  PROJECT_TEMPLATE_TIPS: 'Templates make it easy to save frequently used details and start new freelancer searches.',
  SOURCING_TEMPLATES: 'Sourcing Templates',
  SOURCING_TEMPLATE_TIPS: 'Templates make it easy to save frequently used details and start new sourcing searches.',
  SOURCING_PEOPLE: 'Sourcing People',
  SOURCING_PEOPLE_TIPS: 'Find internal and external candidates who have the skills that match your job needs.',
  PEOPLE_LIST: 'Search Results',
  ROLE_PEOPLE: 'Team Members',
  PROJECT_TEMPLATE_ADMIN: `Ask an Administrator to Create Templates`,

  MENU_NOTES: 'My Notes',
  MENU_DEVELOP: 'My Plans',
  MENU_GOALS: 'My Learning Goals',
  MENU_PLANS: 'Team Plans',
  MENU_SEARCH: 'My Searches',
  MENU_SOURCING: 'Job Sourcing',
  MENU_FREELANCE: 'Freelance Jobs',
  MENU_EXPERT: 'Expert Questions',
  MENU_SKILLSETS: 'Team Skillsets',

  SUBSCRIPTION_PRO: 'AskFora Professional Subscription',
  SUBSCRIPTION_PRO_PAYMENT: 'Your payment is due',
  SUBSCRIPTION_PRO_TRIAL: (e: string) => `Your trial ends on ${e}`,
  SUBSCRIPTION_TRIAL_MSG: (p: string, e: string) => `You're currently on a trial version of AskFora ${p} that ends on ${e}`,
  SUBSCRIPTION_ADD_PAYMENT: `Add a payment method to continue your subscription`,

  SUBSCRIPTION_TEAMS: `AskFora Teams Subscription`,
  SUBSCRIPTION_TEAMS_SUB: (s: number, p: string, d: string) => `${s} seats @ ${p} renews on ${d}`,
  SUBSCRIPTION_TEAMS_SUB_PAYMENT_DUE: (s: number, p: string) => `Payment is due for ${s} seats @ ${p}`,
  SUBSCRIPTION_TEAMS_TRIAL: (e: string) => `Your trial ends on ${e}`,

  SUBSCRIBE: 'Subscribe',
  SUBSCRIBE_PRO_MONTHLY: '$5/Month',
  SUBSCRIBE_PRO_YEARLY: '$48/Year ($4/Month)',
  SUBSCRIBE_PRO_CHANGE: 'Change to',
  SUBSCRIBE_TEAMS_MONTHLY: (s: number) => `$${10 * s}/Month`,
  SUBSCRIBE_TEAMS_YEARLY: (s: number) => `$${96 * s}/Year ($${8 * s}/Month)`,
  CANCEL_SUBSCRIPTION: 'Cancel',
  SUBSCRIBE_PAYMENT: `To complete your subscription, make sure you keep your payment information up to date!`,
  SUBSCRIBE_CONFIRM: [
    `Congratulations and welcome to AskFora Professional!`,
    `You have access to advanced features to connect with your professional network, and personalized course recommendations to keep your skills up to date.`,
  ],
  SUBSCRIBE_CHANGE: `I've updated your subscription.`,
  SUBSCRIBE_CANCEL: `I've canceled your subscription.`,

  GOAL_CANNOT_DELETE: `You cannot delete this learning goal.`,

  TAKE_ASSESSMENT: 'Take Assessment',
  START_LEARNING: 'Start Learning',
  ASSESSMENT_INTRO: [`Great. I have a few questions for you to help figure out what skills you already have.`,
    `Pick the answer you feel most accurately reflects your skills. If you’re not sure about an answer or don’t feel confident about your knowledge, choose "I’m not sure"`],
  ASSESSMENT_DONE: `All set! I'm updating your skills now...`,
  ASSESSMENT_CANCELED: `No worries, you can take the assessment again later.`,
  ASSESSMENT_NEXT: `When you're ready, tap Start Learning and I'll find some online courses to help you meet your learning goal.`,
  ASSESSMENT_EMPTY: `I couldn't create an assessment for this learning goal.`,

  MORE: 'more...',
  LESS: 'less',

  WAIT: {
    id: 'wait',
    info: [],
    ping: undefined,
    // reply: [{ label: '• • •' }],
    reply: [{ label: '\u2022 \u2022 \u2022' }],
  },

  DEFAULT_CHAT: u => {
    const locale = Intl.DateTimeFormat().resolvedOptions().locale;
    const day = new Date().toLocaleDateString(locale, { weekday: 'long' });
    return {
      hint: 'e.g. Who am I meeting with today?',
      answers: ["What's next?", "Help me find someone", "Make an introduction"], //, "Share a tip", "Tell me a joke"],
      id: `init_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [
        u ? { label: `Hi ${u.name}, happy ${day}! How can I help you today?` }
          : { label: `Hey there, happy ${day}! How can I help you today?` }
      ],
    }
  },

  ASK_CHAT: has_people => {
    return {
      hint: '',
      answers: [],
      id: `ask_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: has_people ? [{ label: `Check any number of people, then go back and select an action from the lighting menu.` }]
        : [{ label: `I haven't found any people in your search. Add someone by name or email, or go back and ask me to search for someone.` }],
    }
  },

  EXPERT_CHAT: has_people => {
    return {
      hint: '',
      answers: [],
      id: `ask_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: has_people ? [{ label: `Check any number of people, then tap 'Send Question'.` }]
        : [{ label: `I haven't found any people in your search. Add someone by name or email, or go back and ask me to search.` }],
    }
  },

  GOAL_PROC: (r) => {
    return {
      hint: '',
      answers: [],
      id: `goal_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{
        label: [
          `I'm looking up learning goals that match your skills. This may take a few minutes.`,
          `Digging through the wealth of knowledge that can help you achieve your learning goals.`,
          `Crossing my "t"s and dotting my "i"s.`,
          `Still refining my results.`,
          `Give me a little bit longer, it looks like there are many options to consider.`,
          `Almost there. Making sure I get some good suggestions.`,
        ][r % 6]
      }]
    }
  },

  GOAL_CHAT: (categories: CategoryInfo[], callback: (category: CategoryInfo, txt: string) => Promise<void>) => {
    const answers = [...categories.map(c => c.label), 'Something else'];

    return {
      hint: '',
      answers,
      id: `goal_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: `Here are some learning goals based on your skills that I can recommend courses for. Which one would you like to pursue?` }],
      local_chat: async m => {
        const category = categories.find(c => c.label.toLowerCase() === m.toLowerCase());
        callback(category, m);
        return {};
      }
    }
  },

  GOAL_ELSE_CHAT: `Let me know a learning goal you're interested in and I’ll recommend courses you can take.`,

  ASSIGN_PROC: (r) => {
    return {
      hint: '',
      answers: [],
      id: `assign_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{
        label: [
          `I'm searching for courses that match your learning goal.`,
          `Checking the latest the internet has to offer.`,
          `I'm reviewing a list of potential courses.`,
          `Refining my course recommendations.`,
          `Still working on an exhaustive search.`,
          `Just about done. I've got some great courses for you.`,
        ][r % 6]
      }]
    }
  },

  GOAL_SUCCESS: g => {
    return {
      hint: '',
      hide: 10000,
      answers: [],
      id: `goal_${new Date().getTime()}`,
      info: [g],
      ping: -1,
      reply: [{ label: `Here's your learning goal. You can refine the skills - the more specific the better.` },
      { label: `When your skills are set, ask me to help assess your skills in this learning goal or if you're ready, start learning.` }
      ]
    }

  },

  GOAL_COURSE_SUCCESS: (g) => {
    return {
      hint: '',
      hide: 10000,
      answers: [],
      id: `goal_${new Date().getTime()}`,
      info: [g],
      ping: -1,
      reply: [{ label: `I found some courses for this learning goal.` }]
    }
  },

  GOAL_EMPTY_COURSES: (g) => {
    return {
      hint: '',
      hide: 10000,
      answers: [],
      id: `goal_${new Date().getTime()}`,
      info: [g],
      ping: -1,
      reply: [{ label: `I found a learning goal but was not able to recommend any courses that fit.` }]
    }
  },

  GOAL_EMPTY: () => {
    return {
      hint: '',
      hide: 10000,
      answers: [],
      id: `goal_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: `I wasn't able to find any courses for your learning goal.` }]
    }
  },

  JOB_CHAT: has_people => {
    return {
      hint: '',
      answers: [],
      id: `ask_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: has_people ? [{ label: `Check any number of people, then tap 'Draft Invite'.` }]
        : [{ label: `I haven't found any people in your search. Add someone by name or email, or go back and ask me to search.` }],
    }
  },

  PROPOSE_CHAT: has_people => {
    return {
      hint: '',
      answers: [],
      id: `ask_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: has_people ? [{ label: `Check any number of people, then tap 'Propose'.` }]
        : [{ label: `I haven't found any people in your search. Add someone by name or email, or go back and ask me to search.` }],
    }
  },

  REPEAT_PROJECT_CHAT: t => {
    return {
      hint: '',
      hide: 10000,
      answers: [],
      id: `project_repeat_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: `Here's a copy of your ${t} job` }],
    }
  },

  LEARN_CHAT: (local_chat) => {
    return {
      hint: 'https://linkedin.com/in/username or https://fb.me/askfora',
      answers: [],
      id: `learn_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: `Are there any social profiles you want me to look at?` }],
      local_chat,
    }
  },

  LEARN_CHAT_SKILLS: (local_chat) => {
    return {
      hint: 'e.g. Design, UX, Customer Development',
      answers: [],
      id: `learn_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: `Are there any specific skills you'd like to add?` }],
      local_chat,
    }
  },

  LEARN_CHAT_REPLY: () => {
    return {
      hint: '',
      hide: 10000,
      answers: [],
      id: `learn_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: `I've updated your profile.` }],
    }
  },

  LEARN_CHAT_REPLY_MORE: () => {
    return {
      hint: '',
      hide: 10000,
      answers: [],
      id: `learn_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [{ label: `I've updated your profile. Add some more specific skills so I can make good learning goal recommendations.` }],
    }
  },

  ROLE_CHAT: (local_chat) => {
    return {
      id: `role_${new Date().getTime()}`,
      hint: '',
      answers: [],
      info: [],
      ping: -1,
      local_chat,
      reply: [{ label: 'What would you like to call this skillset?' }]
    }
  },

  ROLE_CONFIRM: () => {
    return {
      id: `role_${new Date().getTime()}`,
      hint: '',
      answers: [],
      info: [],
      ping: -1,
      reply: [{ label: `I added this skillset and suggested skills that might be relevant. You can add or remove skills to customize for your organization.` }],
    }
  },

  DEVELOP_CONFIRM: (p) => {
    return {
      id: `develop_${new Date().getTime()}`,
      hint: '',
      hide: 10000,
      answers: [],
      info: [],
      ping: -1,
      reply: [{ label: `I've assigned a learning goal to ${p.name}` }],
    }

  },

  COLOR_MAP: colorMap,

  ALPHA: alpha,

  DROPDOWN_TEMPLATE: c => {
    return [
      `div.ui.dropdown.analyze.chart_color.${c} {`,
      `  background-color: ${alpha(colorMap[c], '0.5')}!important`,
      '}',
      '',
      `.ui.menu .ui.dropdown .menu > .item.${c} {`,
      `  background-color: ${alpha(colorMap[c], '0.5')}!important`,
      '}',
      '',
    ].join('\n');
  },

  HIDE_REPLY: nevermind,

  NEVERMIND_REPLY: (p?: string) => {
    if (p) {
      const r = {};
      r[`${p} nevermind`] = nevermind;
      return r;
    }

    return { nevermind };
  },

  CONFIRM_CANCEL: c => {
    const yes = { id: `cancel_yes_${new Date().getTime()}`, ping: -1, info: [], hide: 10000, forget: true, reply: [{ label: 'Your subcription is canceled. You can re-subscription at any time.' }], callback: c };
    const no = { id: `cancel_no_${new Date().getTime()}`, ping: -1, info: [], hide: 10000, forget: true, reply: [{ label: 'Ok, no worries.' }] };

    return {
      info: [], ping: -1,
      id: `confirm_cancel_${new Date().getTime()}`,
      answers: ['Yes', 'Nevermind'],
      reply: [
        { label: `Are you sure you want to cancel your subscription?` }
      ],
      quick_replies: {
        yes,
        "yes please": yes,
        "yes, please": yes,
        no,
        "no not": no,
        "no not yet": no,
        "no, not yet": no,
        nevermind: no,
      },
    };
  },

  CONFIRM_DELETE_NOTE: (t, d, c) => {
    const yes = { id: `delete_note_yes_${new Date().getTime()}`, ping: -1, info: [], hide: 10000, forget: true, close: true, reply: [{ label: 'All set' }], callback: c };
    const no = { id: `delete_note_no_${new Date().getTime()}`, ping: -1, info: [], hide: 10000, forget: true, reply: [{ label: 'Ok, no worries.' }] };
    const from = d ? ` from ${d}` : '';
    return {
      info: [], ping: -1,
      id: `confirm_delete_${new Date().getTime()}`,
      answers: ['Yes please', 'No, not yet'],
      reply: [
        { label: `Should I delete your ${t} note${from}?` }
      ],
      quick_replies: {
        yes,
        "yes please": yes,
        "yes, please": yes,
        no,
        "no not": no,
        "no not yet": no,
        "no, not yet": no,
        nevermind: no,
      },
    }
  },

  CONFIRM_DELETE: (t, e, w, c) => {
    const yes = { id: `delete_yes_${new Date().getTime()}`, ping: -1, info: [], hide: 10000, forget: true, reply: [{ label: 'All set' }], callback: c };
    const no = { id: `delete_no_${new Date().getTime()}`, ping: -1, info: [], hide: 10000, forget: true, reply: [{ label: 'Ok, no worries.' }] };
    return {
      info: [], ping: -1,
      id: `confirm_delete_${new Date().getTime()}`,
      answers: ['Yes please', 'No, not yet'],
      reply: [
        { label: `Should I delete your ${t} ${e}?` },
        { label: w },
      ],
      quick_replies: {
        yes,
        "yes please": yes,
        "yes, please": yes,
        no,
        "no not": no,
        "no not yet": no,
        "no, not yet": no,
        nevermind: no,
      },
    }
  },

  FILTER_WARN_REFRESH: callback => {
    const yes = {
      info: [], ping: -1,
      id: `filter_warn_refresh_no_${new Date().getTime()}`,
      hide: 5000,
      forget: true,
      reply: [{ label: "Fetching new results for you..." }],
      callback,
    } as ServerChatResponse;

    const no = {
      info: [], ping: -1,
      id: `filter_warn_refresh_no_${new Date().getTime()}`,
      hide: 5000,
      forget: true,
      reply: [{ label: "Ok, just remember to tap the refresh icon." }],
    } as ServerChatResponse;

    const quick_replies = { yes, no, 'not yet': no, };

    return {
      info: [], ping: -1,
      id: `filter_warn_refresh_${new Date().getTime()}`,
      answers: ['Yes', 'Not yet'],
      reply: [
        { label: "Your results may be out of date until you refresh. Would you like me to update based on your changes?" }
      ],
      local_chat: (m) => quick_replies[m.toLowerCase()], // for demo
      quick_replies,
    } as ServerChatResponse;
  },

  FILTER_NO_PEOPLE_FOUND: "I couldn't find anyone to add",

  EXPERT_QUESTION: local_chat => {
    return {
      info: [], ping: -1,
      id: `expert_${new Date().getTime()}`,
      answers: ['Nevermind'],
      hint: '',
      forget: true,
      reply: [
        { label: 'What advice are you looking for or what question do you need answered?' },
        { label: "You'll be able to edit this later" },
      ],
      quick_replies: {
        nevermind: {
          info: [], ping: -1,
          id: `expert_cancel_${new Date().getTime()}`,
          forget: true,
          reply: [{ label: 'No worries' }],
        }
      },
      local_chat,
    }
  },

  EXPERT_CREATED: {
    info: [],
    ping: -1,
    id: `expert_created`,
    forget: true,
    hide: 10000,
    answers: [],
    hint: '',
    reply: [
      { label: `Here's your question. Tap Send Question and I'll share it with the experts from your search.` },
      { label: `You can also add or remove experts, and post or share a link to this question.` },
    ]

  },

  CONTRACTOR_CONFIRM_SELECT: (nick: string, local_chat) => {
    return {
      info: [], ping: -1, id: `selected_sign_${new Date().getTime()}`, hide: 10000, answers: ['Yes', 'No'], hint: '',
      reply: [{ label: `Do you want to select ${nick} for this job?` }],
      local_chat,
    }
  },

  CONTRACTOR_SELECT_ERROR: (nick?: string) => {
    if (nick) return `You can't select two people for the same job. You'll need to unselect ${nick} first.`;
    else return "I can't select this candidate. It looks like there may be a problem with this job.";
  },

  CONTRACTOR_SELECTED_SIGN: n => {
    return {
      info: [], ping: -1, id: `selected_sign_${new Date().getTime()}`, hide: 10000, answers: [], hint: '', sticky: true,
      reply: [
        {
          label: `Great! I'll email ${n} with the standard service agreement, which you'll countersign. You can see a copy`,
          link: '/contract.pdf',
          text: 'here',
        }
      ]
    }
  },

  CONTRACTOR_SELECTED: (nick: string, ready: boolean, rate: ProjectRate, fee: number, service_fee: number, duration: number, escrow: boolean) => {
    const reply: Reply[] = [];

    if (!fee || ready) {
      let amount = fee;
      if ([ProjectRate.hourly, ProjectRate.daily].includes(rate)) amount *= duration;
      const add_fee = service_fee & amount;
      const fee_reply = !isNaN(add_fee) && add_fee > 0 ? ` and a fee of $${add_fee.toLocaleString()}` : '';
      const deposit = escrow ? `get started` : `deposit $${amount.toLocaleString()}${fee_reply} with a credit card`;
      reply.push({ label: `To get started, go to 'Progress' and tap 'Start' to ${deposit}` });
      if (!escrow) reply.push({ label: `I'll hold on to your payment until the job is complete and you release the funds.` });
    } else {
      reply.push({ label: `Great! I'll let you know when ${nick} has set up a payment account, then to get started you can go to 'Progress' and tap 'Start' to deposit funds with a credit card.` });
    }

    return {
      info: [], ping: -1, id: `contractor_selected_${new Date().getTime()}`, hide: 10000, answers: [], hint: '',
      reply,
    }
  },

  CLIENT_START_PROMPT: (ask_deposit: boolean, amount: number, service_fee: number) => {
    const fee = service_fee > 0 ? ` and a fee of $${service_fee.toLocaleString()}` : '';
    let deposit = `deposit $${amount} and a fee of $${fee} with a credit card`;
    if (!ask_deposit) deposit = 'get started';

    const reply: Reply[] = [];
    reply.push({ label: `Great! To get started, go to 'Progress' and tap 'Start' to ${deposit}` });
    if (ask_deposit) reply.push({ label: "I'll hold on to your payment until the job is complete and you release the funds." });

    return {
      info: [], ping: -1, id: `contractor_selected_${new Date().getTime()}`, hide: 10000, answers: [], hint: '',
      reply,
    }

  },

  CLIENT_WAIT_READY: (nick: string) => `${nick} needs to set up a payment account before you can deposit funds and start the job. I sent an email reminder and will let you know when I’m ready to accept your deposit.`,


  UNSELECT_ERROR: c => `You can't change contractors once the job is started. You should let ${c} know and then finish the job with the agreed upon progress.`,

  UNSELECT_SUCCESS: 'All set. Go ahead and select someone else for your job.',

  SELECTED_CONTRACTOR_PROGRESS: 'Track your progress and submit your job when completed.',

  /* =============================================================================================== */

  TUTORIAL_GET_STARTED: `Select a tutorial to get started`,
  TUTORIAL_WELCOME: `Welcome to AskFora`,

  PREASSESSMENT_START: 'Start Preassessment',

  LESSON_START: 'Start',

  TUTORIAL_INTRO: `I'm here to help you learn. Feel free to ask me questions anytime.`,

  TUTOR_NO_REPLY: `I'm sorry, I can't help with that yet.`,

  TUTORIAL_SELECT_INTRO: [
    `Before we get started, I have a few questions to get a sense of how you'd approach some things today.`,
    `There are no right or wrong answers. Just pick what makes sense to you. Ready?`
  ],

  TUTORIAL_START_PREASSESSED: (skills: string) => 
    [
      `Based on your answers, I've updated your skills, including ${skills}.`,
      `Tap start on a lesson to being. Each lesson includes an assesment so I can keep your skills updated.`,
      `You can see your skills anytime by tapping your email address from the menu in the top left.`,
    ],

  TUTORIAL_START: [
    `Your tutorial is ready. `,
    `I'm here to help you learn. Feel free to ask me questions anytime`,
  ],

  TUTORIAL_DELAY: [
    'No worries. Come back anytime to get started',
  ],

  TUTOR_CHAT: u => {
    const locale = Intl.DateTimeFormat().resolvedOptions().locale;
    const day = new Date().toLocaleDateString(locale, { weekday: 'long' });
    return {
      hint: 'I have a question about this tutorial',
      answers: [],
      id: `init_${new Date().getTime()}`,
      info: [],
      ping: -1,
      reply: [
        u && u.name ? { label: `Hi ${u.name}, happy ${day}! How can I help you with this lesson?` }
          : { label: `Hey there, happy ${day}! How can I help you with this lesson?` }
      ],
    }
  },

  TUTOR_SUBSCRIBE: `Unlimited learning for only $3.99/month`,

  SHARE_TUTORIAL_UNLOCK: [
    `Thanks for sharing AskFora. You now have free access to this lesson.`,
    `You can share AskFora with others to get free access to more lessons.`
  ],

  NEXT_LESSON: `Great work. Time for another lesson?`,

  LESSON_DELAY: `I'll be here whenever you're ready.`,

  PRACTICE_PROMPTS: `You've completed your tutorial! Time to practice what you've learned.`,

  PRACTICE_COMPLETE: 'Congratulations, you successfully completed this practice!',

  CERTIFICATE: `I'm adding a certificate of completion to your profile, which you can share with your colleagues.`,

  TUTORIAL_COMPLETE: 'Congratulations, you successfully completed this tutorial!',

  REVIEW_PRACTICE: 'Review Practice',

  CONFIRM_DELETE_ACCOUNT: (y, n) => {
    const yes = { id: `delete_yes_${new Date().getTime()}`, ping: -1, info: [], hide: 10000, forget: true, reply: [{ label: 'Sorry to see you go' }], callback: y };
    const no = { id: `delete_no_${new Date().getTime()}`, ping: -1, info: [], hide: 10000, forget: true, reply: [{ label: `Your progress is safe. It's a good time to keep learning` }], callback: n };
    return {
      info: [], ping: -1,
      id: `confirm_delete_${new Date().getTime()}`,
      answers: [`Yes, I'm sure`, 'No, I want to keep learning'],
      reply: [
        { label: `Should I delete your AskFora account?` },
        { label: `This will permnantely erase all of your tutorials.` },
      ],
      quick_replies: {
        yes,
        "yes please": yes,
        "yes, please": yes,
        "yes, i'm sure": yes,
        "yes i'm sure": yes,
        "yes im sure": yes,
        "yes i sure": yes,
        "sure": yes,
        no,
        "no not": no,
        "no not yet": no,
        "no, not yet": no,
        "no, i want to keep learning": no,
        nevermind: no,
      },
    }
  },
};
