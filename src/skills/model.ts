import * as tf from "@tensorflow-models/universal-sentence-encoder";
import _ from 'lodash';
import { kmeans } from 'ml-kmeans';
import { AddressInfo } from 'net';
import { setTimeout as timer } from "timers/promises";
import { cosineSimilarity } from 'vector-cosine-similarity';

import logging from '../utils/logging';

import server from './serve';

import config from '../config';

let _model: tf.UniversalSentenceEncoder;
let model_loader: Promise<void>;

const LOG_NAME = 'skills.model';

export async function loadModel(silent = false): Promise<tf.UniversalSentenceEncoder> {
  require("@tensorflow/tfjs-node");
  if (model_loader) await model_loader;
  else {
    let loaded;
    model_loader = new Promise<void>(c => loaded = c);
    if (!silent) logging.infoF(LOG_NAME, 'loadModel', 'Loading model');
    let model_server;
    let host;
    if (config.isRunningOnGoogle()) {
      host = `https://storage.googleapis.com/${config.get('GOOGLE_CLOUD_PROJECT')}-model`;
    } else {
      model_server = await server();
      const port = (model_server.address() as AddressInfo).port;
      host = `http://localhost:${port}`;
    }
    for(let i = 0; i < 10; i++) {
      try {
        _model = await tf.load({
          vocabUrl: `${host}/vocab.json`,
          modelUrl: `${host}/model.json`,
        }); // vocabUrl modelUrl
        loaded();
        break;
      } catch(e) {
        logging.warnF(LOG_NAME, 'loadModel', `Error loading model`, e);
        await timer(Math.pow(i, 3) * 100);
      }
    }
    if (model_server) model_server.close();
    if( _model) {
      if (!silent) logging.infoF(LOG_NAME, 'loadModel', 'Model loaded');
    } else logging.warnF(LOG_NAME, 'loadModel', 'Model NOT loaded');
  }

  return _model;
}

export async function skillVector(skills: string[]): Promise<number[]> {
  if (!skills || !skills.length || !skills.filter(s => s && s.length).length) return null;
  const model = await loadModel();
  const vector = (await model.embed([skills.map(s => s.toLowerCase()).sort().join(' ')])).arraySync()[0];
  return vector;
}

export async function groupSkills(skills: string[], groups = 20): Promise<string[][]> {
  if (skills.length === 0) return [];
  if (skills.length === 1) return [skills];

  const skill_vectors: {skill: string, vector: number[]}[] = [];
  await Promise.all(_.uniq(skills).map(async skill => {
    skill_vectors.push({ skill, vector: await skillVector([skill])});
  }));

  if(!skill_vectors.length) return [];
  if(skill_vectors.length === 1) return [skills];

  const clusters = kmeans(skill_vectors.map(v => v.vector), Math.min(groups, skill_vectors.length - 1), { maxIterations: 10 });
  const vmap: string[][] = clusters.centroids.map(c => []); 
  for(const svec of skill_vectors) {
    let best = 0;
    let weight = 0;
    for(let j = 0; j < clusters.centroids.length; j++) {
      const sim = cosineSimilarity(clusters.centroids[j], svec.vector);
      if (sim > weight) {
        weight = sim;
        best = j;
      }
    }

    vmap[best].push(svec.skill);
  }

  return vmap;
}