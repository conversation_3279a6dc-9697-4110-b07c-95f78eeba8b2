/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import axios from 'axios';
import fs from 'fs';
import _ from 'lodash';
import { mean, std, sum } from 'mathjs';
import { kmeans } from 'ml-kmeans';
import snowball from 'node-snowball';
import numbered from 'numbered';
import path from 'path';
import PDLJS from 'peopledatalabs';
import pos from 'pos';
import { setTimeout as timer } from "timers/promises";
import { v4 as uuid } from 'uuid';
import { cosineSimilarity } from 'vector-cosine-similarity';
import xml2js from 'xml2js';

import { Category, ExtendedCategory, GLOBAL_SKILL_CATEGORIES, ScoreStat, Skill, SkillCategory } from '../types/globals';
import { GlobalType, Person, Tag } from '../types/items';
import { Color, EducationLevel, Fit, MappedCandidate, TagType, Uid, findTypeTags } from '../types/shared';
import { User } from '../types/user';
import { groupSkills, loadModel, skillVector } from './model';

import { LANGUAGE, arrayIncludes, cleanWordSet, combineWords, flatten, includesAny, selectWeigthMax, stripPuncs, weightMax } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import config from '../config';
import data from '../data';

const TAGGER = new pos.Tagger();

const LOG_NAME = 'skills.skills';

let pdl_client;

export const SKILL_WORDS = JSON.parse(fs.readFileSync(path.resolve(__dirname, '..', 'files', 'skills.json'), { encoding: 'utf-8' }));

config.onLoad('skill', async (silent: boolean) => {
  const pdl_key = config.get('PDL_API_KEY');
  if (pdl_key) {
    pdl_client = new PDLJS({ apiKey: pdl_key });
  }

  loadModel(silent);
  setTimeout(loadSkills, 100);
  setTimeout(loadSkillCategories, 100);
  if (!config.isBackgroundProcess()) {
    // setInterval(loadSkills, 600 * 1000);
    // setInterval(loadSkillCategories, 600 * 1000);

    data.dataCheck().then(() => {
      data.groups.categoryGroups().then(groups => {
        groups.forEach(group =>
          //setInterval(() => 
            loadSkillCategories(group.id) //, 600 * 1000)
        )
      });
    });
  }

}, true, 10000);

const skills_cache: { [key: string]: Skill[] } = {};
let skill_categories: { [key: string]: SkillCategory[] } = {};
let category_skills: { [key: string]: Set<string> } = {};

export async function loadSkills() {
  const data_skills = await data.skills.load();

  data_skills.forEach(cacheSkill);
}

export function skillsLoaded() {
  return Object.keys(skills_cache).length > 0;
}

export function skillCategoriesLoaded(group_id: Uid = GLOBAL_SKILL_CATEGORIES) {
  if (!group_id) group_id = GLOBAL_SKILL_CATEGORIES;
  return skill_categories && skill_categories[group_id]; // && skill_categories[group_id].length > 0;
}

export function skillCache(skill: string, exact = false): Skill[] {
  const stem = snowball.stemword(skill);
  const matches = skills_cache[skill.toLowerCase().trim()];
  const stem_matches = skills_cache[stem.toLowerCase().trim()];
  if (exact && matches) return matches.filter(match => match.skill === skill.toLowerCase().trim());
  else if (!matches) return stem_matches
  else if (stem_matches) return _.uniqBy(matches.concat(stem_matches), 'skill');
  return matches;
}

function cacheSkill(skill: Skill) {
  const lskill = skill.skill.trim();
  if (skills_cache[lskill]) {
    if (!skills_cache[lskill].map(s => s.id).includes(skill.id)) skills_cache[lskill].push(skill)
  } else skills_cache[lskill] = [skill];
}

async function saveSkillCategories(reset = true) {
  if (skillCategoriesLoaded(GLOBAL_SKILL_CATEGORIES)) {
    await data.skills.saveCategories(skill_categories[GLOBAL_SKILL_CATEGORIES], reset);
    category_skills[GLOBAL_SKILL_CATEGORIES] = new Set(flatten(skill_categories[GLOBAL_SKILL_CATEGORIES].map(sc => sc.skills)));
  }
}

export async function loadSkillCategories(group_id: Uid = GLOBAL_SKILL_CATEGORIES) {
  while (!data || !data.skills) await timer(100);

  if (!group_id) {
    const groups = Object.keys(skill_categories);
    if (groups.length) return Promise.all(_.uniq([GLOBAL_SKILL_CATEGORIES, ...groups]).map(loadSkillCategories));
    group_id = GLOBAL_SKILL_CATEGORIES;
  }

  skill_categories[group_id] = await data.skills.loadCategories(group_id === GLOBAL_SKILL_CATEGORIES ? undefined : group_id);
  if (!skill_categories[group_id] && skill_categories[group_id].length === 0 && group_id === GLOBAL_SKILL_CATEGORIES) {
    logging.infoF(LOG_NAME, 'loadSkillCategories', 'No categories found');
    // await buildCategories();
  }
  if (skill_categories[group_id]) category_skills[group_id] = new Set(flatten(skill_categories[group_id].map(sc => sc.skills)));
  else skill_categories[group_id] = [];
}

export function skillCategoryById(group_id: Uid, id: Uid): SkillCategory {
  if (!group_id) group_id = GLOBAL_SKILL_CATEGORIES;
  return skill_categories[group_id].find(sc => sc.id === id);
}

export function skillCategoriesByIds(group_id: Uid, ids: Uid[]): SkillCategory[] {
  if (!group_id) group_id = GLOBAL_SKILL_CATEGORIES;
  return skill_categories[group_id].filter(sc => ids.includes(sc.id));
}

export function getSkillCategory(group_ids: Uid[], global: boolean, skill: string, expand = true): SkillCategory {
  if (global) {
     if(!group_ids) group_ids = [GLOBAL_SKILL_CATEGORIES];
     else if(!group_ids.includes(GLOBAL_SKILL_CATEGORIES)) group_ids = [...group_ids, GLOBAL_SKILL_CATEGORIES];
  } else if(!group_ids) group_ids = [GLOBAL_SKILL_CATEGORIES];
  const categories = findSkillCategories(group_ids, skill, expand);
  if (categories && categories.length) return categories[0].category;
}

export function allSkillCategories(group_id: Uid) {
  if (!group_id) group_id = GLOBAL_SKILL_CATEGORIES;
  return skill_categories[group_id];
}

export function updateSkillCategory(group_id: Uid, sc: SkillCategory) {
  if (skill_categories[group_id]) {
    skill_categories[group_id] = [sc,
      ...skill_categories[group_id].filter(s => s.id !== sc.id)];
    category_skills[group_id] = new Set(flatten(skill_categories[group_id].map(sc => sc.skills)));
  }
}

export function removeSkillCategory(group_id: Uid, sc: SkillCategory) {
  if (skill_categories[group_id]) {
    skill_categories[group_id] = skill_categories[group_id].filter(s => s.id !== sc.id);
    category_skills[group_id] = new Set(flatten(skill_categories[group_id].map(sc => sc.skills)));
  }
}

export function isCategorySkill(group_id: Uid, s: string) {
  if (!group_id) group_id = GLOBAL_SKILL_CATEGORIES;
  return category_skills[group_id] && category_skills[group_id].has(s);
}

export function findSkillCategories(group_ids: Uid[], skill: string, expand = true): { category: SkillCategory, weight: number }[] {
  if (!skill) return [];
  let lskill = [skill.toLowerCase().trim()];
  if (expand) {
    for (const s of skill.split(' ').concat([skill])) {
      const sc = skillCache(s, true);
      if (sc && sc.length) {
        sc.forEach(s => {
          if (s && s.synonyms && s.synonyms.length) lskill = lskill.concat(s.synonyms)
        });
        lskill = _.uniq(lskill);
      }
    }
  }

  if (logging.isDebug()) logging.debugF(LOG_NAME, 'findSkillCategory', `Searching ${skill} ${expand ? ' expanded to ' : ''}${JSON.stringify(lskill)}`);
  const categories = flatten(group_ids.map(group_id =>
    skill_categories[group_id] ? skill_categories[group_id].filter(sc => arrayIncludes(sc.skills, lskill)) : []
  ));
  if (logging.isDebug()) logging.debugF(LOG_NAME, 'findSkillCategory', `Found ${categories ? categories.length : 'no'} categories`);
  if (!categories.length) return [];
  if (categories.length === 1) return [{ category: categories[0], weight: 1 }];

  const weighted_categories = categories.map(category => {
    let weight = 0;
    let boost = 1;
    for (let i = 0;i < category.skills.length;i++) {
      for (const s of lskill) {
        if (category.skills[i].includes(s)) {
          weight += category.weights[i];
          const b = parsers.boost(s);
          if (b > boost) boost = b;
        }
      }
    }

    weight *= boost;

    return { category, weight };
  });

  if (logging.isDebug()) logging.debugF(LOG_NAME, 'findSkillCategory', `Weighted categories ${weighted_categories.map(w => w.category.id + ': ' + w.weight).join(', ')}`);

  return weighted_categories.sort((a, b) => b.weight - a.weight);
}

// build a word weighted Catetogry from a weighted SkillCategory build from skills
// look for (and filter) an expanded skill set, weighed against the initial skills
export async function mapSkillCategory(cs: { category: SkillCategory, weight: number, skills: string[] }, ignore: string[], color?: Color): Promise<ExtendedCategory> {
  const search_skills = _.uniq(cs.skills);
  const cs_skills = search_skills.length < 20 ? filterWords(cs.category.skills) : []; //.filter(s => !parsers.ignore(s, true, true, ignore) && !parsers.name(s)) : [];

  // TODO: sort and pick cs.sc skills that best match cs
  const cat_skills = _.uniq([...search_skills, ...cs_skills].map(s => s.toLowerCase())).filter(s => !parsers.ignore(s, true, true, ignore) && !parsers.mask(s) && !parsers.name(s));

  let related_skills = cat_skills.length < 10 ? await topRelatedSkills(cat_skills) : [];

  const all_skills = _.uniq([...cat_skills, ...related_skills]).map(parsers.remap);

  const remove: number[] = [];
  for (let i = 0;i < all_skills.length;i++) {
    const check = all_skills[i];
    const subs = all_skills.find((s, n) => n !== i && s.includes(check));
    if (subs) remove.push(i);
  }

  const filtered_skills = _.uniq(all_skills.filter((s, i) => !remove.includes(i) && !parsers.ignore(s) && !parsers.mask(s) && !parsers.name(s)));
  if (config.isEnvOffline() || filtered_skills.length > 3) {
    const word_map = await data.skills.skillMatrix(parsers.expandWords(filtered_skills), parsers.expandWords(search_skills));

    const min_weight = cs.weight / cs.category.skills.length;

    const weighted_skills: { skill: string, weight: number }[] = [];
    for (const skill of filtered_skills) {
      let fs_set = word_map[skill];
      if (!fs_set) {
        const fs_map = skill.split(' ').map(fs => word_map[fs]).filter(x => x)
        const fs_set_exp: { [key: string]: number[] } = {};
        for (const fs of fs_map) {
          for (const ss in fs) {
            if (fs_set_exp[ss]) fs_set_exp[ss].push(fs[ss]);
            else fs_set_exp[ss] = [fs[ss]];
          }
        }

        fs_set = {};
        for (const fs in fs_set_exp) {
          fs_set[fs] = mean(fs_set_exp[fs]);
        }
      }

      let total_weight = 0;
      for (const ss of search_skills) {
        let add_weight = min_weight;
        if (ss === skill) add_weight = 1;
        else if (fs_set[ss]) add_weight = fs_set[ss];
        else {
          const ss_part = ss.split(' ');
          if (ss_part.length) {
            const ss_set = ss_part.map(s => fs_set[s]).filter(x => x);
            if (ss_set.length) {
              const max_weight = weightMax(ss_set);
              const fweights = ss_set.filter(w => w >= max_weight);

              const w = mean(fweights);
              if (!isNaN(w)) add_weight = w;
            }
          }
        }

        total_weight += add_weight;
      }

      const weight = total_weight / search_skills.length;

      if (weight >= min_weight) weighted_skills.push({ skill, weight });
    }

    if (!weighted_skills.length) return null

    const weight = mean(weighted_skills.map(w => w.weight));
    weighted_skills.forEach(ws => {
      if (ws.weight === 1) ws.weight = weight;
    });

    const weight_set = weighted_skills.map(w => w.weight);
    const max = weightMax(weight_set);
    const skill_set = weighted_skills.filter(s => s.weight > max).sort((a, b) => b.weight - a.weight).slice(0, 10)

    const skill_vector = flatten(weighted_skills.map(s => {
      if (s.weight > 1) return [s.skill, s.skill, s.skill];
      if (s.weight >= 0.5) return [s.skill, s.skill];
      return [s.skill];
    }));

    const skills = skill_set.map(s => s.skill);
    const weights = skill_set.map(s => s.weight);
    const vector = await skillVector(skill_vector);

    logging.infoF(LOG_NAME, 'mapSkillCategory', `Category ${cs.category.id} has ${weighted_skills.length} skills ${max} max`);

    return {
      id: cs.category.id,
      skills,
      label: _.startCase(skills[0]),
      color,
      manual: false,
      weights,
      vector,
      score: weight,
    };
  }
}

export function makeSkillCategory(cs: { category: SkillCategory, weight: number, skills: string[] }, cat_skills?: string[], color?: Color): ExtendedCategory {
  const lcs = cat_skills ? cat_skills.map(s => s.toLowerCase()) : undefined;
  let label = lcs.filter(l => cs.skills.find(s => l.includes(s))).sort((a,b) => b.length - a.length)[0];
  if(label?.length) {
    label = _.startCase(label);
  } else {
    const fit_label = lcs ? cs.skills.map(s => s.toLowerCase()).find(s => lcs.includes(s) || lcs.includes(parsers.remap(s))) : parsers.remap(cs.skills[0].toLocaleString());
    label = fit_label ? _.startCase(fit_label) : _.startCase(cs.skills[0]);
  }
  return {
    id: cs.category.id,
    skills: _.uniqBy(cs.category.skills, s => s.toLowerCase()),
    label,
    color,
    manual: false,
    weights: cs.category.weights,
    vector: cs.category.vector,
    score: cs.weight,
  }
}

export async function peopleCategoryMap(user: User, people: Partial<Person>[], group_ids?: Uid[], global?: boolean): Promise<{ [key: string]: ExtendedCategory[] }> {
  if (group_ids) {
    await Promise.all(group_ids.map(async group_id => {
      if (!skillCategoriesLoaded(group_id)) await loadSkillCategories(group_id);
      while (!skillsLoaded() || !skillCategoriesLoaded(group_id)) await timer(100);
    }));
  }

  const cat_map = await data.skills.peopleCategories(user, people, group_ids, global);
  const results: { [key: string]: ExtendedCategory[] } = {};
  for (const cm of cat_map) {
    const id = cm.vanity ? `profile/${cm.vanity}` : cm.person_id;
    let category = skillCategoryById(group_ids.find(group_id => skillCategoryById(group_id, cm.category_id)), cm.category_id);
    const cat = makeSkillCategory({
      category,
      weight: cm.score,
      skills: cm.match
    });
    if (results[id]) results[id].push(cat);
    else results[id] = [cat];
  }

  return results;
}

export async function peopleCategories(group_ids: Uid[], global: boolean, people: Person[], ignore?: string[], related?: string[], threshold = 0.3, count = undefined): Promise<Category[]> {
  if (!people || !people.length) return [];

  if (!count) count = Math.min(Math.ceil(people.length / 3), 12);

  logging.infoF(LOG_NAME, 'peopleCategories', `${group_ids ? JSON.stringify(group_ids) : ''} Finding ${count} categories for ${people.length} people`);

  const all_people_skills = people ? flatten(people.map(c => findTypeTags(c.tags, TagType.skill).filter(t => t.index >= 0 && !parsers.ignore(t.value, true, true, ignore) && !parsers.name(t.value)))) as Tag[] : [];
  const people_skill_weights = all_people_skills.map(cs => cs.bias ? cs.bias : cs.index);
  const people_skill_max = weightMax(people_skill_weights);
  const skills = all_people_skills.filter(t => t.index >= people_skill_max || t.bias >= people_skill_max)
    .sort((a, b) => Math.max(b.bias, b.index) - Math.max(a.index, a.bias)).map(t => t.value);

  const related_skills = related && related.length ? await filterRelated(related, skills, threshold ) : skills;

  logging.infoF(LOG_NAME, 'peopleCategories', `${group_ids ? JSON.stringify(group_ids) : ''} Clustering ${JSON.stringify(related_skills)}`);

  let vmap: string[][] = [related_skills];
  if(related_skills.length > count) {
    const skill_vectors: {skill: string, vector: number[]}[] = [];
    await Promise.all(related_skills.map(async skill => {
      skill_vectors.push({ skill, vector: await skillVector([skill])});
    }));

    const clusters = kmeans(skill_vectors.map(v => v.vector), count, { maxIterations: 10 });
    vmap = clusters.centroids.map(c => []); 
    for(const svec of skill_vectors) {
      let best = 0;
      let weight = 0;
      for(let j = 0; j < clusters.centroids.length; j++) {
        const sim = cosineSimilarity(clusters.centroids[j], svec.vector);
        if (sim > weight) {
          weight = sim;
          best = j;
        }
      }

      vmap[best].push(svec.skill);
    }
  }

  logging.infoF(LOG_NAME, 'peopleCategories', `${group_ids ? JSON.stringify(group_ids) : ''} Searching categories matching ${JSON.stringify(related_skills)} in ${vmap.length} clusters`);

  const cat_set = flatten(
    await Promise.all(vmap.map(async rs => {
      const cats = await searchCategories(group_ids, global, _.uniq(rs), [], ignore, /*people.length === 1*/false, [], 0.99, 3)
      return cats.filter(cs => cs.weight && cs.category.skills.length >= 12).sort((a,b) => b.weight - a.weight).slice(0,1);
    })));
  // const cats = cat_set.filter(cs => cs.weight && cs.category.skills.length >= 12);

  logging.infoF(LOG_NAME, 'peopleCategories', `${group_ids ? JSON.stringify(group_ids) : ''} Found  ${cat_set.length} categories`);

  const all_cats = _.uniqBy(cat_set, 'category.id').map((cs, i) => makeSkillCategory(cs, related_skills, Object.values(Color)[i % 10]));
  const sorted_cats = all_cats.filter(x => x && x.skills.length).sort((a, b) => b.score - a.score);

  logging.infoF(LOG_NAME, 'peopleCategories', `${group_ids ? JSON.stringify(group_ids) : ''} All ${all_cats.length} categories sorted ${sorted_cats.length}`);

  return sorted_cats;
}

export async function searchCategories(group_ids: Uid[], global: boolean, skills: string[], addskills: string[], ignore = [], expand = true, skip_cats: Uid[] = [], threshold = 0.3, count = undefined): Promise<{ category: SkillCategory, weight: number, skills: string[] }[]> {
  if (group_ids) {
    await Promise.all(group_ids.map(async group_id => {
      if (!skillCategoriesLoaded(group_id)) await loadSkillCategories(group_id);
      while (!skillsLoaded() || !skillCategoriesLoaded(group_id)) await timer(100);
    }));
  }

  const eskill = expand ? (await expandSkills([...skills, ...addskills], 0.5)).filter(s => !parsers.ignore(s) && !parsers.mask(s) && !parsers.name(s))
    : addskills.filter(s => !parsers.ignore(s) && !parsers.mask(s) && !parsers.name(s)).map(s => s.toLowerCase().trim());

  const lskill = _.uniq(skills.map(s => s.toLowerCase()));

  let categories: SkillCategory[];
  let cat_set: { category: SkillCategory, skills: string[], stats: ScoreStat }[];

  logging.infoF(LOG_NAME, 'searchCategories', `${group_ids ? JSON.stringify(group_ids) : ''} Searching categories for ${expand ? 'expanded ' : ''}skills ${JSON.stringify(lskill)} plus ${JSON.stringify(eskill)}`);

  if (config.isEnvOffline()) {
    categories = flatten(group_ids.map(group_id => skill_categories[group_id] ? skill_categories[group_id].filter(sc => _.intersection(lskill, sc.skills).length) : []));
  } else {
    global = global || !group_ids || group_ids.includes(GLOBAL_SKILL_CATEGORIES);
    if (group_ids) group_ids = group_ids.filter(g => g !== GLOBAL_SKILL_CATEGORIES);
    cat_set = await data.skills.findCategories(group_ids, global, lskill, eskill, skip_cats, skills.length ? Math.min(skills.length * 10, 50) : 10);
  }

  logging.infoF(LOG_NAME, 'searchCategories', `${group_ids ? JSON.stringify(group_ids) : ''} Found ${cat_set.length} categories`);
  // cat_set = cat_set.filter(cs => _.intersection(cs.skills, [...lskill, ...eskill]).length);

  // logging.infoF(LOG_NAME, 'searchCategories', `${group_ids ? JSON.stringify(group_ids) : ''} Filtered ${cat_set.length} categories`);
  if (count && cat_set.length > count) {
    const cats = cat_set.filter(cs => cs.stats.score_avg);
    if (cats.length >= count) {
      const weights = cats.map(cs => cs.stats.score_avg);
      const max = weightMax(weights);

      const pass_cats = cats.filter(cs => cs.stats.score_avg >= max);

      // const sg = gini.unordered(pass_cats.map(c => c.stats.score_stddev));

      const filtered_cats = pass_cats.sort((a, b) => b.stats.score_stddev - a.stats.score_stddev).slice(0, count);

      // const filtered_cats = sg < 0.15 ? pass_cats.sort((a,b) => b.stats.score_avg - a.stats.score_avg).slice(0, count) :
      //  pass_cats.sort((a,b) => b.stats.score_min - a.stats.score_min).slice(0, count);
      logging.infoF(LOG_NAME, 'searchCategories', `Filtered ${filtered_cats.length}/${cats.length} categories count: ${count} max weight:${max} weights: ${JSON.stringify(weights)}`);
      cat_set = filtered_cats;
    }
  }

  // logging.infoF(LOG_NAME, 'searchCategories', `${group_ids ? JSON.stringify(group_ids) : ''} Found ${cat_set.length} categories`);
  const word_map = await data.skills.skillMatrix(_.uniq( flatten(cat_set.map(cs => cs.skills))), lskill);

  const weighted_categories = await Promise.all(cat_set.map(async cs => {
    const related_skills = await filterRelated(cs.skills, skills, threshold, false, word_map);
    const max = weightMax(cs.category.weights);
    const include_index = cs.category.weights.map((w, i) => { return { w, i } }).filter(x => x.w >= max).map(x => x.i);
    const weighted_skills = cs.category.skills.map((s, i) => { return { s, i } }).filter(x => include_index.includes(x.i)).map(x => x.s);
    const expanded_skills = expand ? await expandSkills([...related_skills, ...weighted_skills, ...cs.skills], 0.99) : [...cs.skills, ...skills].map(s => s.toLowerCase().trim());
    cs.category.skills = _.uniqBy(filterWords(
      [...expanded_skills, ...related_skills, ...skills, ...weighted_skills]
        .filter(s => !parsers.ignore(s, false, false, ignore) && !parsers.mask(s) && !parsers.name(s)).map(parsers.remap)
    ), x => x.toLowerCase());

    // cs.category.vector = await skillVector(cs.category.skills);
    const cs_skills = _.uniqBy(cs.skills?.length ? cs.skills : skills, x => x.toLowerCase());
    const cs_vec = await skillVector(cs_skills);
    const weights = cs.category.skills.map(s => 0);
    await Promise.all(cs.category.skills.map(async (s,i) => {
      const sv = await skillVector([s]);
      weights[i] = cosineSimilarity(cs_vec, sv);
    }));

    const new_max = weightMax(weights);
    cs.category.weights = weights.filter(x => x >= new_max);
    cs.category.skills = cs.category.skills.map((s,i) => { return {s,i}}).
      filter(x => weights[x.i] >= new_max).map(x => x.s);

    return {
      category: cs.category,
      skills: cs_skills,
      weight: cs.stats.score_avg,
    }
  }));

  if (logging.isDebug()) logging.debugF(LOG_NAME, 'searchCategories', `Weighted categories ${weighted_categories.map(w => w.category.id + ': ' + w.weight).join(', ')}`);

  return weighted_categories.sort((a, b) => b.weight - a.weight);

}

export async function generateCategories(group_ids: Uid[], global: boolean, cat_skills: string[], add_skills: string[] = [], ignore: string[] = [], count = 3, skip_cats: Uid[] = [], expand = true, inv = false): Promise<ExtendedCategory[]> {
  if (group_ids) {
    await Promise.all(group_ids.map(async group_id => {
      if (!skillCategoriesLoaded(group_id)) await loadSkillCategories(group_id);
      while (!skillsLoaded() || !skillCategoriesLoaded(group_id)) await timer(100);
    }));
  }

  let search_skills = cat_skills;

  if (expand) {
    const split_words = cat_skills.map(s => s.split(' '));
    const word_parts = flatten(split_words.filter(s => s.length > 1));
    const joined_words = combineWords(split_words.filter(s => s.length === 1).map(x => x[0])).filter(s =>
      group_ids.find(g => isCategorySkill(g, s))
    );
    const meaning_words = flatten(cat_skills.map(s => parsers.findMeaning(s)));

    search_skills = _.uniqBy([...cat_skills, ...word_parts, ...joined_words, ...meaning_words], x => x.toLowerCase())
      .filter(s => !parsers.ignore(s, true, true, ignore) && !parsers.mask(s) && !parsers.name(s))
  }

  logging.infoF(LOG_NAME, 'generateCategories', `Searching for [${search_skills}] and [${add_skills}]`);
  const cats = await searchCategories(group_ids, global, search_skills, add_skills, ignore, expand, skip_cats, 0.3, count);
  if (!cats.length) return [];

  const all_cats = cats.filter(x => x).sort((a, b) => inv ? a.weight - b.weight : b.weight - a.weight).slice(0, count);
  logging.infoF(LOG_NAME, 'generateCategories', `Generated ${all_cats.length} categories`);

  // refine skills
  await Promise.all(all_cats.map(async cs => {
    /* if(cs.category.skills.length >= 20) {
      cs.category.skills = _.uniqBy(await filterRelated(cat_skills, cs.category.skills, 0.99), s => s.toLowerCase());
    }*/

    if (cs.category.skills.length < 20) {
      const group_skills = await groupSkills([...cs.skills, ...cs.category.skills], Math.min(Math.max(Math.round(cs.category.skills.length / 10), 10), 20));

      group_skills.sort((a, b) =>
        _.intersection(b, cs.skills).length -
        _.intersection(a, cs.skills).length
      );

      cs.category.skills = [...cs.skills, ...cs.category.skills];
      for (const gs of group_skills) {
        if (cs.category.skills.length >= 20) break;
        cs.category.skills = _.uniqBy([...cs.category.skills, ...await filterRelated(cs.category.skills, gs, 0.99)], s => s.toLowerCase());
      }
    }

    cs.category.vector = await skillVector(cs.category.skills);
  }));

  const sorted_cats = all_cats.map((cs, i) => makeSkillCategory(cs, cat_skills, Object.values(Color)[i % 10]));

  const categories: ExtendedCategory[] = [];

  const cat_ids: Uid[] = [];

  for (const skill of cat_skills) {
    const vskill = await skillVector([skill]);
    const mcat = sorted_cats.filter(c => !cat_ids.includes(c.id));
    let scat = mcat.sort((a, b) => cosineSimilarity(vskill, b.vector) - cosineSimilarity(vskill, a.vector))[0];
    if (scat) {
      logging.infoF(LOG_NAME, 'generateCategories', `Adding ${scat.id} for ${skill}`);
      scat.label = _.startCase(skill);
      if (!scat.skills) scat.skills = [skill];
      else if (!scat.skills.includes(skill)) scat.skills.push(skill);
      categories.push(scat);
      cat_ids.push(scat.id);
    }
  }

  return categories;
}

export async function skillGroup(group_ids: Uid[], global: boolean, skills: string[], locale: string, max = 10, expand = false): Promise<string[]> {
  if (group_ids) {
    if (global && !group_ids.includes(GLOBAL_SKILL_CATEGORIES)) group_ids = [...group_ids, GLOBAL_SKILL_CATEGORIES];
    await Promise.all(group_ids.map(async group_id => {
      if (!skillCategoriesLoaded(group_id)) await loadSkillCategories(group_id);
      while (!skillsLoaded() || !skillCategoriesLoaded(group_id)) await timer(100);
    }));
  } else if (global) group_ids = [GLOBAL_SKILL_CATEGORIES];
  else group_ids = [];

  const category_skills: string[] = []; /*flatten(skills.map(skill => { 
    const cat_set = findSkillCategories(group_ids, skill);
    return cat_set ? flatten(cat_set.map(cs => cs.category.skills)) : [];
  }));*/

  let similar_category_skills: string[] = [];
  if (category_skills.length < 10) {
    const similar_categories = await searchCategories(group_ids, global, skills, [], [], expand); // similarCategories(skill, 0.3);
    similar_category_skills = flatten(similar_categories.map(cs => cs.category.skills));
  }


  let related_skills: string[] = [];
  if ([...category_skills, ...similar_category_skills].length < max) {
    const new_skills = await relatedSkills(skills, locale, false);
    if (new_skills) {
      related_skills = _.uniq(flatten(Object.values(new_skills)).map(s => parsers.complete(s)));
    }
  }

  let related_words: string[] = [];
  if ([...category_skills, ...similar_category_skills, ...related_skills].length < max) {
    if (expand) related_words = await data.skills.relatedWords(parsers.expandWords(skills, false));
    else related_words = await data.skills.relatedWords(skills);
  }

  let skill_set = (await filterRelated(skills, cleanWordSet([...category_skills, ...similar_category_skills, ...related_skills, ...related_words]
    .filter(s => !parsers.ignore(s, false, false, EducationLevel) && !parsers.mask(s) && !parsers.name(s))
    .map(parsers.remap), true, true), 0.99)).slice(0,max);

  const filtered_set = skill_set.map((s, i) => { return { s, i } }).map(x => x.s);
  return filtered_set;

}

export async function relatedSkills(skills: string[], locale?: string, wait_lookup = true): Promise<{ [key: string]: string[] }> {
  const new_skills: { [key: string]: string[] } = {};

  if (skills && skills.length) {
    const lnew_skills = skills ? skills.map(s => s.toLowerCase()) : [];
    await Promise.all(skills.map(async (skill) => {
      new_skills[skill] = [];
      const lookups = await lookupSkill([skill], locale, false, wait_lookup);
      let parse = true;
      if (lookups) {
        for (const lookup of lookups.filter(l => l.synonyms && l.synonyms.length)) {
          for (const synonym of lookup.synonyms) {
            const lsyn = synonym.toLowerCase();
            if (!lnew_skills.includes(lsyn) && !parsers.ignore(lsyn)) {
              lnew_skills.push(lsyn);
              new_skills[skill].push(synonym);
              parse = false;
            }
          }
        }
      }

      if (parse) {
        if (!new_skills[skill]) new_skills[skill] = [];
        const lookup_skills = skill.split(' ');
        if (lookup_skills.length > 2) {
          const ns = await relatedSkills(lookup_skills, locale, wait_lookup);
          if (ns) new_skills[skill] = [...new_skills[skill], ...Object.keys(ns), ...flatten(Object.values(ns))]
        }
      }
    }));
  }

  return new_skills;
}

export async function topRelatedSkills(skills: string[], locale?: string): Promise<string[]> {
  const related = await relatedSkills(skills, locale, false);

  const all_related_skills = related && Object.keys(related) ?
    flatten(Object.values(related))
      .filter(s => !parsers.ignore(s) && !parsers.mask(s) && !parsers.name(s))
      .map(parsers.remap).filter(s => !skills.includes(s)) : [];

  return filterRelated(skills, all_related_skills);
}

export async function filterRelated(skills: string[], all_related_skills: string[], threshold = 0.3, remap = false, word_map?: {[key:string]: {[key:string]: number}}): Promise<string[]> {
  if (!all_related_skills.length) return [];

  let related_skills: string[] = [];

  const search_skills = skills;

  const filter_skills = all_related_skills;

  const lss = _.uniq(search_skills.map(s => s.toLowerCase()));
  if (!word_map) word_map = await data.skills.skillMatrix(lss, _.uniq(filter_skills.map(s => s.toLowerCase())));

  let ex_map: {[key:string]: string[]} = {};

  if (Object.keys(word_map).length === 0) {
    for(const skill of skills) {
      ex_map[skill] = parsers.expandWords(skills, false);
    }
    word_map = await data.skills.skillMatrix([...lss, ...flatten(Object.values(ex_map))], _.uniq(filter_skills.map(s => s.toLowerCase())));
  }

  if (Object.keys(word_map).length === 0) return all_related_skills;

  const rww: { [key: string]: number } = {};
  for (const rw of lss) {
    let vals = [];
    if (word_map[rw]) vals = Object.values(word_map[rw]).filter(x => x < 1 && x > threshold / 10)
    if (ex_map[rw]) {
      for(const ex_rw of ex_map[rw]) {
        if (word_map[ex_rw]) vals = [...vals, ...Object.values(word_map[ex_rw]).filter(x => x < 1 && x > threshold / 10)];
      }
    }

    if (vals.length) {
      let max = weightMax(vals);
      if (threshold) max = max * threshold;
      rww[rw] = max;
    }
  }

  related_skills = _.uniqBy(flatten<string>(Object.keys(rww).map(skill => {
    const max = rww[skill];
    let wm = skill in word_map ? Object.keys(word_map[skill]).filter(related => word_map[skill][related] >= rww[skill]) : [];
    if (skill in ex_map) {
      const ex_weights:{[key:string]: number[]} = {};
      for(const eskill of ex_map[skill].filter(ex_skill => ex_skill in word_map)) {
        for(const ex_wm in word_map[eskill]) {
          if(ex_weights[ex_wm]) ex_weights[ex_wm].push(word_map[eskill][ex_wm]);
          else ex_weights[ex_wm] = [word_map[eskill][ex_wm]];
        }
      }

      const ew = Object.keys(ex_weights).filter(ex_wm => {
        const stddev: number = std(ex_weights[ex_wm]) as any as number;
        return stddev >= max;
      });

      wm = [...wm, ...ew];
    }
    return wm;
  })), x => x.toLowerCase());

  if (remap) {
    const lrs = related_skills.map(s => s.toLowerCase());
    const rel = skills.filter(s => includesAny(s.toLowerCase(), lrs));
    related_skills = _.uniqBy([...rel, ...await filterRelated(skills, rel, threshold)], s => s.toLowerCase());
  } else if(ex_map) {
    const sv = await Promise.all(skills.map(s => skillVector([s])));
    const rs = await Promise.all(related_skills.map(async skill => { return { skill, vector: await skillVector([skill]) }}));
    const remove = [];
    for(const r of rs) {
      let match = false;
      for(const s of sv) {
        const cs = cosineSimilarity(r.vector, s);
        if (cs >= threshold) {
          match = true;
          break;
        }
      }
      if (!match) remove.push(r.skill);
    }
    related_skills = related_skills.filter(rs => !remove.includes(rs));
  }

  return related_skills;
}

export async function filterRelatedWeights(skills: string[], all_related_skills: string[], threshold = 0.3, expand = false): Promise<{ skill: string, weight: number }[]> {
  if (!all_related_skills.length) return [];

  let related_skills: { skill: string, weight: number }[] = [];

  const search_skills = expand ? parsers.expandWords(skills) : skills;

  const filter_skills = expand ? parsers.expandWords(all_related_skills) : all_related_skills;

  const word_map = await data.skills.skillMatrix(_.uniq(search_skills.map(s => s.toLowerCase())), _.uniq(filter_skills.map(s => s.toLowerCase())));

  const rww: { [key: string]: number } = {};
  for (const rw in word_map) {
    const vals = Object.values(word_map[rw]).filter(x => x < 1 && x > threshold / 10)
    rww[rw] = vals.length ? mean(vals) : 0;
  }

  const weights = Object.values(rww);
  let max = weightMax(weights);
  if (threshold) max = max * threshold;
  related_skills = _.uniqBy(flatten<{ skill: string, weight: number }>(Object.keys(rww).filter(skill => skill in word_map).map(skill =>
    Object.keys(word_map[skill]).map(related => { return { skill: related, weight: word_map[skill][related] } })
      .filter(sw => sw.weight >= max))
  ), sw => sw.skill.toLowerCase()).sort((a, b) => b.weight = a.weight);

  return related_skills;
}

export async function expandSkills(skills: string[], threshold = 0.3): Promise<string[]> {
  let xskill: string[] = [];

  const skills_parts = _.uniq([...skills, ...parsers.expandWords(flatten(skills.map(s => s.split(' '))), false) ].map(s => s.toLowerCase()));
 //.filter(s => !parsers.ignore(s) && !parsers.mask(s) && !parsers.name(s))

  for (const s of skills_parts) {
    xskill.push(s);
    const sc = skillCache(s, true);
    if (sc && sc.length) {
      sc.forEach(s => {
        if (s && s.synonyms && s.synonyms.length) xskill = xskill.concat(s.synonyms.filter(s => !parsers.ignore(s) && !parsers.mask(s) && !parsers.name(s)))
      });
    }
  }

  return filterRelated(skills, xskill, threshold);
}

export async function lookupPDL(lskill: string): Promise<{ synonyms: string[]; initialisms: string[] }> {
  const pdl_key = config.get('PDL_API_KEY');
  let synonyms = [];
  if (pdl_key) {
    try {
      const skill = await pdl_client.skill({ skill: lskill });
      /* const skill = await axios({
        method: 'get',
        url: `https://api.peopledatalabs.com/v5/skill/enrich?skill=${lskill}`,
        headers: {
          accept: 'application/json',
          'content-type': 'application/json',
          'x-api-key': pdl_key
        }
      });*/

      if (skill && skill.data && skill.data.data) {
        if (skill.data.data.similar_skills) synonyms = synonyms.concat(skill.data.data.similar_skills);
        if (skill.data.data.relevant_job_titles) synonyms = synonyms.concat(skill.data.data.relevant_job_titles);
      }
    } catch (e) {
      if (e.response && e.response.status === 404) logging.warnF(LOG_NAME, 'lookupPDL', `Skill not found in PDL ${lskill}`);
      else if (e.response && e.response.message && e.response.message.includes('No records were found')) logging.warnF(LOG_NAME, 'lookupPDL', `Skill not found in PDL ${lskill}`);
      else logging.errorF(LOG_NAME, 'lookupPDL', `Error looking up ${lskill} in PDL`, e);
    }
  }
  return { synonyms: processSkills(synonyms), initialisms: [] };
}

// #833 - We don't want to call skill search for any of these matching characters
// const IGNORED = ['_', '.', ', ', '!', '"', '', '\'', '/', '/', '$', ']', '*'];
export async function lookupBH(lskill: string): Promise<{ synonyms: string[]; initialisms: string[] }> {
  let synonyms = [];
  const bh_key = config.get('BH_THESAURUS_KEY');

  if (bh_key) {
    try {
      const url = `https://words.bighugelabs.com/api/2/${bh_key}/${encodeURI(lskill)}/json`;
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Search ${url}`);
      const bh_skill: any = await axios(url);
      if (bh_skill && bh_skill.data) {
        if (bh_skill.data.noun) {
          if (bh_skill.data.noun.syn) {
            synonyms = synonyms.concat(bh_skill.data.noun.syn);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} noun syn: ${bh_skill.data.noun.syn}`);
          }
          if (bh_skill.data.noun.sim) {
            synonyms = synonyms.concat(bh_skill.data.noun.sim);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} noun sim: ${bh_skill.data.noun.sim}`);
          }
          if (bh_skill.data.noun.rel) {
            synonyms = synonyms.concat(bh_skill.data.noun.rel);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} noun rel: ${bh_skill.data.noun.rel}`);
          }
        }

        if (bh_skill.data.verb) {
          if (bh_skill.data.verb.syn) {
            synonyms = synonyms.concat(bh_skill.data.verb.syn);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} verb syn: ${bh_skill.data.verb.syn}`);
          }
          if (bh_skill.data.verb.sim) {
            synonyms = synonyms.concat(bh_skill.data.verb.sim);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} verb sim: ${bh_skill.data.verb.sim}`);
          }
          if (bh_skill.data.verb.rel) {
            synonyms = synonyms.concat(bh_skill.data.verb.rel);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} verb rel: ${bh_skill.data.verb.rel}`);
          }
        }

        if (bh_skill.data.adjective) {
          if (bh_skill.data.adjective.syn) {
            synonyms = synonyms.concat(bh_skill.data.adjective.syn);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} adj syn: ${bh_skill.data.adjective.syn}`);
          }
          if (bh_skill.data.adjective.sim) {
            synonyms = synonyms.concat(bh_skill.data.adjective.sim);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} adj sim: ${bh_skill.data.adjective.sim}`);
          }
          if (bh_skill.data.adjective.rel) {
            synonyms = synonyms.concat(bh_skill.data.adjective.rel);
            if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupBH', `Skill ${lskill} adj rel: ${bh_skill.data.adjective.rel}`);
          }
        }
      }
    } catch (e) {
      if (e.response && e.response.status === 404) logging.warnF(LOG_NAME, 'lookupBH', `Skill not found in BH ${lskill}`);
      else logging.errorF(LOG_NAME, 'lookupBH', `Error looking up ${lskill} in BH`, e);
    }
  }

  return { synonyms: processSkills(synonyms), initialisms: [] };
}

export async function lookupWiki(lskill: string): Promise<{ synonyms: string[]; initialisms: string[] }> {
  let synonyms = [];
  try {
    const url = `https://en.wiktionary.org/w/api.php?format=json&action=query&titles=${encodeURI(lskill)}&rvprop=content&rvslots=main&prop=revisions`;
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupWiki', `Search ${url}`);
    const wiki_skill: any = await axios(url);
    if (wiki_skill && wiki_skill.data && wiki_skill.data.query && wiki_skill.data.query.pages) {
      for (const index in wiki_skill.data.query.pages) {
        const page = wiki_skill.data.query.pages[index];
        if (page && page.revisions && page.revisions.length &&
          page.revisions[0].slots && page.revisions[0].slots.main) {
          const content = page.revisions[0].slots.main['*'];
          if (content) {
            // if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupWiki', `Content:\n${content}`);

            const raw_nouns = content.match(/\[\[[\w\s?]+\]\]/g);
            const nouns = raw_nouns ? raw_nouns.map(t => t.replace(/\[/g, '').replace(/\]/g, '')) : [];

            const raw_syn = content.match(/sense\|[\w\s]+/g);
            const syns = raw_syn ? raw_syn.map(s => s.split('|')[1]) : [];

            const see_syn = content.match(/seeSynonyms\|en\|[\w\s]+/g);
            const see_synonyms = see_syn ? see_syn.map(s => s.split('|')[2]) : [];

            const raw_hyp = content.match(/\n\|[\w ]+/g);
            const hyponyms = raw_hyp ? raw_hyp.map(s => s.split('|')[1]) : [];

            if (logging.isDebug()) {
              logging.debugF(LOG_NAME, 'lookupWiki', `Skill ${lskill} nouns: ${nouns}`);
              logging.debugF(LOG_NAME, 'lookupWiki', `Skill ${lskill} synonyms: ${syns}`);
              logging.debugF(LOG_NAME, 'lookupWiki', `Skill ${lskill} seeSynonyms: ${see_synonyms}`);
              logging.debugF(LOG_NAME, 'lookupWiki', `Skill ${lskill} hyponyms: ${hyponyms}`);
            }

            synonyms = synonyms.concat(syns).concat(see_synonyms).concat(hyponyms).concat(nouns).slice(0, 10);
          }
        }
      }
    }
  } catch (e) {
    if (e.response && e.response.status === 404) logging.warnF(LOG_NAME, 'lookupWiki', `Skill not found in Wiki ${lskill}`);
    else logging.errorF(LOG_NAME, 'lookupWiki', `Error looking up ${lskill} in Wiki`, e);
  }

  return { synonyms: processSkills(synonyms), initialisms: [] };
}

export async function lookupInitials(lskill: string): Promise<{ synonyms: string[]; initialisms: string[] }> {
  let synonyms = [];
  let initialisms = [];

  try {
    const url = `http://acronyms.silmaril.ie/cgi-bin/xaa?${encodeURI(lskill)}`;
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupInitials', `Search ${url}`);
    const initial: any = await axios(url);
    if (initial && initial.data) {
      const data = await xml2js.parseStringPromise(initial.data);
      if (data && data.acronym && data.acronym.found) {
        for (const found of data.acronym.found) {
          if (found.acro) {
            for (const acronym of found.acro) {
              if (acronym.expan && Array.isArray(acronym.expan)) {
                for (const expan of acronym.expan) {
                  switch (typeof expan) {
                    case 'string':
                      initialisms.push(expan);
                      break;
                    case 'object':
                      if (expan.a && Array.isArray(expan.a)) {
                        for (const a of expan.a) {
                          if (a['_'] && typeof a['_'] === 'string') synonyms.push(a['_']);
                        }
                      }
                      break;
                    default:
                      logging.infoF(LOG_NAME, 'lookupIniitials', `Unknown type of acronym.expan for ${lskill}`);
                  }
                }
              } else logging.infoF(LOG_NAME, 'lookupIniitials', `Unknown type of acronym for ${lskill}`);
            }
          }
        }
      }
    }
  } catch (e) {
    if (e.response && e.response.status === 404) logging.warnF(LOG_NAME, 'lookupInitials', `Skill not found in Initials ${lskill}`);
    else logging.errorF(LOG_NAME, 'lookupInitials', `Error looking up ${lskill} in Acronyms`, e);
  }

  return { synonyms: processSkills(synonyms), initialisms: processSkills(initialisms) };
}

const STEM_LANGUAGES = [
  'arabic',
  'danish',
  'dutch',
  'english',
  'finnish',
  'french',
  'german',
  'hungarian',
  'italian',
  'norwegian',
  'portuguese',
  'spanish',
  'swedish',
  'romanian',
  'tamil',
  'turkish',
];

function processSkills(skill_set): string[] {
  return parsers.findMeaning(skill_set.join(' ')).join(' ').toLowerCase().split(' ').filter(s => !parsers.ignore(s));
}

export function filterWords(words: string[]) {
  const stems = words.map(w => snowball.stemword(w.toLowerCase()));
  return words.filter(w => w.split(' ').length > 1 || !stems.includes(w.toLowerCase()));
}

export function filterSkills(skill_set: string[], language: string, filter_ignore = '', filter_words = true): string[] {
  const filter_skill = _.uniq(skill_set.filter(s => s.length > 2 && s !== filter_ignore &&
    !parsers.ignore(s) &&
    numbered(numbered(s)) !== s.toLowerCase() &&
    !parsers.TIME_INDICATORS.includes(s)))

  if (filter_words) {
    const tags = TAGGER.tag(filter_skill);
    return _.uniq(tags.filter(t => t[1] === 'NN' || t[1].startsWith('VB') || t[1][0] == 'J')
      .map(t => snowball.stemword(stripPuncs(t[0], true), language)));
  } else if (filter_ignore && filter_ignore.length) {
    return parsers.matchSpeech(filter_ignore, filter_skill);
  }

  return filter_skill;
}

export async function lookupSkill(skills_incoming: string | string[], locale = null, stem = true, wait_lookup = true): Promise<Skill[]> {
  let language = locale ? LANGUAGE[locale.split('-')[0]] : 'english';

  if (language) {
    language = language.toLowerCase();
    if (!STEM_LANGUAGES.includes(language)) {
      if (language.includes(' ')) {
        let match = null;
        for (const lang in language.split(' ')) {
          if (STEM_LANGUAGES.includes(lang)) {
            match = lang;
            break;
          }
        }

        if (match) language = match;
        else language = 'english';
      } else language = 'english';
    }
  }

  let in_skills: string[];
  if (!skills_incoming) in_skills = [];
  else if (!Array.isArray(skills_incoming)) in_skills = [stripPuncs(skills_incoming, true).toLowerCase()];
  else in_skills = _.uniq(skills_incoming.map(s => stripPuncs(s, true).toLowerCase()));

  if (config.isEnvOffline()) {
    return await Promise.all(in_skills.map(async s => {
      const skill = new Skill({
        id: s,
        skill: s,
        synonyms: [snowball.stemword(s, language)],
        initialisms: [],
        rates: [],
      });
      await skill.vectorize();
      return skill
    }));
  }

  const stem_skills = stem ? in_skills.map(s => snowball.stemword(s, language)) : [];
  in_skills = filterSkills([...in_skills, ...stem_skills], language, '', false);

  if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupSkill', `Looking for ${in_skills}`);
  const ds_skills = [];
  let match_skills = [];
  for (const skill of in_skills) {
    const cached = skillCache(skill, true);
    if (cached && cached.length) {
      const match_cache = cached; //.filter(c => c.stem === stem || (stem && c.stem === undefined));
      if (match_cache.length) match_skills = match_skills.concat(match_cache);
      else ds_skills.push(skill);
    } else ds_skills.push(skill);
  }

  if (ds_skills && ds_skills.length) {
    const load_ds_skills = (await data.skills.find(ds_skills, true, true)); //.filter(s => s.stem === stem || (stem && s.stem === undefined));
    match_skills = match_skills.concat(load_ds_skills);
  }

  // result
  const skills: { [key: string]: Skill } = {};

  // need to do lookups
  let lookup_skills: string[] = [];

  // matched by skill
  const matched_lookup: { [key: string]: Skill } = {}
  match_skills.forEach(s => matched_lookup[s.skill] = s);

  for (const skill of match_skills) {
    matched_lookup[skill.skill] = skill;
    // exact match
    if (in_skills.includes(skill.skill)) skills[skill.skill] = skill;
    else {
      // synonym match
      const syn_match = _.intersection(in_skills, skill.synonyms);
      if (syn_match.length) {
        if (!skills[skill.skill]) {
          skills[skill.skill] = skill;
          for (const syn of syn_match) {
            if (!matched_lookup[syn] && !lookup_skills.includes(syn)) lookup_skills.push(syn);
          }
        }
      }

      // initials match
      const init_match = _.intersection(in_skills, skill.initialisms);
      if (init_match.length) {
        if (!skills[skill.skill]) {
          skills[skill.skill] = skill;
          for (const init of init_match) {
            if (!matched_lookup[init] && !lookup_skills.includes(init)) lookup_skills.push(init);
          }
        }
      }
    }
  }

  lookup_skills = _.uniq(lookup_skills.concat(in_skills.filter(s => !skills[s])));

  if (lookup_skills.length) {
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'lookupSkill', `Searching for ${lookup_skills}`);
    const save_skills: Skill[] = [];
    const await_ls = Promise.all(lookup_skills.filter(s => s && s.length).map(async (lskill) => {
      let saved = false;
      let bh, wiki, acro_skills, pdl_skills;

      await Promise.all([
        lookupBH(lskill).then(b => bh = b),
        lookupWiki(lskill).then(w => wiki = w),
        lookupInitials(lskill).then(a => acro_skills = a),
        lookupPDL(lskill).then(p => pdl_skills = p),
      ]);

      const synonyms = filterSkills([
        ...bh.synonyms,
        ...wiki.synonyms,
        ...acro_skills.synonyms,
        ...pdl_skills.synonyms,
      ], language, lskill, stem);

      if (synonyms.length) {
        const add_skill = new Skill({ id: lskill, skill: lskill, synonyms, initialisms: [], rates: [], stem });

        save_skills.push(add_skill);
        skills[lskill] = add_skill;
        saved = true;
      }

      const initials = filterSkills([
        ...bh.initialisms,
        ...wiki.initialisms,
        ...acro_skills.initialisms,
      ], language, lskill, false);

      for (const acro of initials.filter(a => a && a.length)) {
        // map in acronyms to existing skill or create a new one
        const acro_result = await data.skills.byName(acro)
        if (acro_result && acro_result.initialisms) {
          if (!acro_result.initialisms.includes(lskill)) {
            acro_result.initialisms.push(lskill);
            acro_result.stem = stem;
            save_skills.push(acro_result);
            saved = true;
            skills[acro] = acro_result;
          }
        } else {
          let bh, wiki, acro_skills, pdl_skills;
          await Promise.all([
            lookupBH(acro).then(b => bh = b),
            lookupWiki(acro).then(w => wiki = w),
            lookupInitials(acro).then(a => acro_skills = a),
            lookupPDL(acro).then(p => pdl_skills = p),
          ]);

          const synonyms = filterSkills([
            ...bh.synonyms,
            ...wiki.synonyms,
            ...acro_skills.synonyms,
            ...pdl_skills.synonyms,
          ], language, acro, stem);

          /* const initials = filterSkills([
            ...bh.initialisms,
            ...wiki.initialisms,
            ...acro_skills.initialisms,
          ], language, acro, false); */


          if (acro && acro.length) {
            const acro_skill = new Skill({ id: acro, skill: acro, synonyms, initialisms: [lskill], rates: [], stem });
            save_skills.push(acro_skill);
            saved = true;
            skills[acro] = acro_skill;
          }
        }
      }

      if (!saved && lskill && lskill.length) {
        save_skills.push(new Skill({ id: lskill, skill: lskill }));
      }
    }));

    const saver = async () => {
      if (save_skills.length) {
        await Promise.all(save_skills.map(s => s.vectorize()));
        save_skills.forEach(cacheSkill);
        await data.skills.saveAll(save_skills);
      }
    }

    if(wait_lookup) {
      await await_ls;
      await saver();
    } else await_ls.then(() => saver());
  }

  return Object.values(skills);
}

export async function fitPeople(user: User, categories: Partial<Category>[], people: Partial<Person>[]): Promise<Fit> {
  const fit: Fit = {};
  if (!people?.length) return fit;

  let cat_map:{category_id: Uid, person_id: Uid, vanity: string, score: number}[] = [];
  const all_people = people.slice();
  const awaiters = [];
  while(all_people.length) {
    awaiters.push(new Promise<void>(async c => {
      const cm = await data.skills.mapCategories(user, categories, all_people.splice(0,100));
      if(cm) cat_map = cat_map.concat(cm);
      c();
    }));
  }

  await Promise.all(awaiters);

  for (const cm of cat_map) {
    let sc = people.find(uc => (cm.vanity && uc.vanity === cm.vanity) || (cm.person_id && uc.id === cm.person_id));
    if (sc) {
      if (sc.vanity) {
        if (!fit[`profile/${sc.vanity}`]) fit[`profile/${sc.vanity}`] = {};
      } else if (!fit[sc.id]) fit[sc.id] = {};

      if (sc.vanity) fit[`profile/${sc.vanity}`][cm.category_id] = cm.score;
      else fit[sc.id][cm.category_id] = cm.score;
    }
  }

  for (const missing of people.filter(p => (!p.vanity && !fit[p.id]) || (p.vanity && !fit[`profile/${p.vanity}`]))) {
    if (missing.vanity) {
      if (!fit[`profile/${missing.vanity}`]) fit[`profile/${missing.vanity}`] = {};
    } else if (!fit[missing.id]) fit[missing.id] = {};

  }

  return fit;
}

export async function scoreCandidateCategories(categories: Partial<ExtendedCategory>[], person: Partial<Person>, all_skills = false): Promise<ExtendedCategory[]> {
  const matches: ExtendedCategory[] = [];
  try {
    const skills = parsers.expandWords(flatten(categories.map(s => s.skills.map(x => x.toLowerCase()))));
    const related_skills = [...person.skillsBatch]; //, ...categories.map(s => s.label.toLowerCase())]

    logging.infoF(LOG_NAME, 'scoreCandidateCategories', `Matrixing [${skills}] x [${related_skills}]`);

    const word_map = await data.skills.skillMatrix(skills, related_skills);

    logging.infoF(LOG_NAME, `scoreCandidateCategories`, `Computing ${categories.length} categories`);

    const category_loaders = categories.filter(c => c.skills && c.skills.length).map(async (category) => {
      const vector = category.vector?.length ? category.vector : await skillVector(category.skills);

      let score = 0;
      const pskills = await filterRelated(category.skills, person.skillsBatch, 0.99);
      if (pskills.length) {
        const skill_tags = person.tags.filter(t => pskills.includes(t.value));
        const full_weight = skill_tags.filter(t => Math.max(t.index, t.bias) >= 75).map(t => t.value);
        const half_weight = skill_tags.filter(t => Math.max(t.index, t.bias) < 75 && Math.max(t.index, t.bias) >= 35).map(t => t.value);
        const low_weight = skill_tags.filter(t => Math.max(t.index, t.bias) < 35).map(t => t.value);
        const fvec = full_weight.length ? await skillVector(full_weight) : undefined;
        const hvec = half_weight.length ? await skillVector(half_weight) : undefined;
        const lvec = low_weight.length ? await skillVector(low_weight) : undefined;
        const fscore = fvec ? cosineSimilarity(vector, fvec) : 0;
        const hscore = hvec ? cosineSimilarity(vector, hvec) : 0;
        const lscore = lvec ? cosineSimilarity(vector, lvec) : 0;

        if (fscore) {
          if (hscore) {
            if (lscore) score = (fscore + hscore * 0.75 + lscore * 0.25) / 3;
            else score = (fscore + hscore * 0.75) / 2;
          } else if (lscore) score = (fscore + lscore * 0.25) / 2;
          else score = fscore;
        } else if (hscore) {
          if (lscore) score = (fscore + lscore * 0.25) / 2;
          else score = hscore;
        } else score = lscore;
      }

      if (!score) {
        const wskills = person.weightedSkills;
        const wvec = wskills.length ? await skillVector(wskills) : undefined;

        score = wvec ? cosineSimilarity(vector, wvec) : 0;
      }

      const weights = category.skills.map((s, i) => {
        const sl = s.toLowerCase();
        let weight_set = word_map[s] ? Object.values(word_map[s]) : [];
        if (!weight_set.length) weight_set = word_map[sl] ? Object.values(word_map[sl]) : [];
        const swm = weight_set.length ? selectWeigthMax(weight_set.filter(w => !isNaN(w) && w > 0)) : [];
        let weight = swm.length ? mean(swm) : 0;
        if (!weight) {
          let sw = parsers.expandWords([s]);
          let ws = flatten(sw.map(w => word_map[w] ? Object.values(word_map[w]) : []))
          if (!ws.length) ws = flatten(sw.map(w => w.toLowerCase()).map(w => word_map[w] ? Object.values(word_map[w]) : []))
          const ws_swm = ws.length ? selectWeigthMax(ws.filter(w => !isNaN(w) && w > 0)) : [];
          weight = ws_swm.length ? mean(ws_swm) : 0;
        }
        return weight;
      });

      const max = weightMax(weights);
      const ws = category.skills.map((skill, i) => { return { skill, weight: weights[i] } }).filter(sw => all_skills || sw.weight >= max).sort((a, b) => b.weight - a.weight);

      matches.push({
        id: category.id,
        label: category.label,
        skills: ws.map(sw => sw.skill),
        vector,
        score,
        weights: ws.map(sw => sw.weight),
        manual: false,
      });
    });

    await Promise.all(category_loaders);
  } catch (e) {
    logging.errorF(LOG_NAME, 'scoreCandidateCategories', `Error scoring candidate: ${person.id} for categories ${categories.map(c => c.id)}`, e);
  }

  return matches;
}

export async function mapCandidateCategories(user: User, categories: Partial<ExtendedCategory>[], people: Partial<Person>[]): Promise<MappedCandidate[]> {
  const mapped_candidates: MappedCandidate[] = []; //{id: Uid, vanity: string, matches: {label:string; id: string, score: number}[]}[] = [];

  logging.infoFP(LOG_NAME, 'mapCandidateCategories', user.profile, `Mapping ${people.length} people to ${categories.length} categories`);

  try {
    const cat_map = await data.skills.mapCategories(user, categories, people);

    for (const cscore of cat_map) {
      if (cscore.score) {
        let scored_candidate = mapped_candidates.find(uc => (cscore.vanity && uc.vanity === cscore.vanity) || (cscore.person_id && uc.id === cscore.person_id));
        if (scored_candidate) {
          scored_candidate.matches.push({
            label: categories.find(c => c.id === cscore.category_id).label,
            id: cscore.category_id,
            score: cscore.score,
          });
        } else mapped_candidates.push({
          id: cscore.person_id ? cscore.person_id : cscore.vanity ? `profile/${cscore.vanity}` : undefined,
          vanity: cscore.vanity,
          matches: [{
            label: categories.find(c => c.id === cscore.category_id).label,
            id: cscore.category_id,
            score: cscore.score,
          }]
        });
      }
    }

    const mapped_ids = mapped_candidates.map(c => c.id);
    logging.infoFP(LOG_NAME, 'mapCandidateCategories', user.profile, `Mapped ${mapped_ids.length}/${people.length} people with ${sum(mapped_candidates.map(c => c.matches.length))} total matches in ${categories.length} categories`);
    people.filter(c => !mapped_ids.includes(c.id)).forEach(c => {
      mapped_candidates.push({ id: c.id, vanity: c.vanity, matches: [] });
    })

    mapped_candidates.forEach(c => c.matches.sort((a, b) => b.score - a.score));

    mapped_candidates.sort((a, b) => {
      if (a.matches.length > b.matches.length) return -1;
      else if (a.matches.length < b.matches.length) return 1;

      let a_max = a.matches.reduce((a, b) => b.score > a ? b.score : a, 0);
      let b_max = b.matches.reduce((a, b) => b.score > a ? b.score : a, 0);
      return b_max - a_max;
    });
  } catch (e) {
    logging.errorFP(LOG_NAME, 'mapCandidateCategories', user.profile, `Error mapping candidates`, e);
  }

  return mapped_candidates;
}

export async function buildCategories(rebuild = false) {
  // if (!skillsLoaded()) await loadSkills();
  // if (!Object.keys(skills_cache).length) return;
  logging.infoF(LOG_NAME, 'buildCategoires', `${rebuild ? 'Rebuilding' : 'Updating'} skill categories`);

  const skill_map: { [key: string]: string } = {};
  const initialization = [];
  const version = new Date().getTime();

  if (skill_categories[GLOBAL_SKILL_CATEGORIES] && !rebuild) {
    for (const sc of skill_categories[GLOBAL_SKILL_CATEGORIES]) {
      initialization.push(sc.vector);
      for (const skill of sc.skills) {
        skill_map[skill] = sc.id;
      }
    }

    logging.infoF(LOG_NAME, 'buildCategoires', `Initializing with ${initialization.length} skill category vectors`);
  }

  await data.skills.exportSkills();

  let [skills, user_skills, courses] = await Promise.all([data.skills.loadSynonyms(), data.skills.loadUser(), data.courses.load()]);

  const cvecs = courses.filter(c =>
    c.skills.filter(s => !(parsers.skillExtract(s) in skill_map)).length > 0
  ).map(c => c.vector);

  /*/ testing
  skills.splice(50);
  user_skills.splice(50);
  cvecs.splice(50);
  /*/

  const cat_count = Math.round(Math.min(config.get('SKILL_CATEGORY_COUNT', 1000), skills.length / 5));

  logging.infoF(LOG_NAME, 'buildCategories', `${skills.length} skills ${user_skills.length} users' skills ${cvecs.length} course skills`);

  const sksv: { [key: string]: number[] } = {}
  const skv: { [key: string]: number[] } = {}

  await loadModel();

  for (const skill of skills) {
    const pskill = parsers.skillExtract(skill.skill);
    if (pskill.length && !(pskill in skv) && !(pskill in skill_map) && !(pskill in sksv)) {
      try {
        skv[pskill] = skill.vector;

        if (!skill_categories || !Object.keys(skill_categories).length || !rebuild) {
          initialization.push(skv[pskill]);
        }

        const psyn = skill.synonyms ? skill.synonyms.map(parsers.skillExtract).filter(s => s.length) : [];
        if (psyn.length) sksv[pskill] = skill.svector;
        else sksv[pskill] = skv[pskill];
      } catch (e) {
        console.error(`Error modeling ${JSON.stringify(skill)}`, e);
      }
    }
  }

  await Promise.all(user_skills.filter(s => s && s.skills && s.skills.length && !(s.id in sksv) && !(s.id in skill_map)).map(async user_skill => {
    try {
      sksv[user_skill.id] = await skillVector(user_skill.skills.map(s => parsers.remap(s)));
    } catch (e) {
      console.error(`Error modeling user ${user_skill.id} with ${user_skill.skills.length} skills`, e);
    }
  }));

  const kwds = [];
  const test = [];
  const vecs = cvecs.filter(v => v && v.length); /* && 
    !Object.values(skv).find(s => cosineSimilarity(s, v) >= 0.9) &&
    !Object.values(sksv).find(s => cosineSimilarity(s, v) >= 0.9)
  );*/

  for (const s in sksv) {
    if (skv[s] && !(kwds.includes(s))) {
      kwds.push(s);
      test.push(skv[s]);
    }
    if (!skv[s] || cosineSimilarity(skv[s], sksv[s]) < 0.9) vecs.push(sksv[s]);
  }

  if (initialization && !rebuild) {
    if (initialization.length < cat_count) {
      while (initialization.length < cat_count) {
        initialization.push(_.sample(Object.values(sksv)));
      }
    } else initialization.splice(cat_count);
  }

  // cluster on synonyms
  logging.infoF(LOG_NAME, 'buildCategories', `${vecs.length} vectors`);
  const maxIterations = 30;
  let clusters;
  while (vecs.length) {
    let vcs = vecs.splice(0, 5000);
    if (vecs.length <= cat_count) vcs = vcs.concat(vecs.splice(0))
    if (initialization.length) clusters = kmeans(vcs, cat_count, { initialization, maxIterations });
    else clusters = kmeans(vcs, cat_count, { maxIterations });

    Object.assign(initialization, clusters.centroid);
    logging.infoF(LOG_NAME, 'buildCategories', `${vecs.length} vectors remaining`);
  }

  let new_sc: SkillCategory[] = [];
  let empty = 0;
  let single = 0;

  if (clusters) {
    logging.infoF(LOG_NAME, 'buildCategories', `${clusters.centroids.length} clusters`);

    const scmap: { skill: string; weight: number }[][] = clusters.centroids.map(c => []);
    for (let i = 0;i < kwds.length;i++) {
      let best = 0;
      let weight = 0;
      for (let j = 0;j < clusters.centroids.length;j++) {
        const sim = cosineSimilarity(clusters.centroids[j], test[i]);
        if (sim > weight) {
          weight = sim;
          best = j;
        }
      }

      weight *= parsers.boost(kwds[i]);

      if (weight >= 0.5) scmap[best].push({ skill: kwds[i], weight, });
      else logging.infoF(LOG_NAME, 'buildCategories', `Not mapping ${kwds[i]} weight ${weight}`);
    }

    for (let i = 0;i < scmap.length;i++) {
      const sc = scmap[i];

      if (sc.length === 0) empty++;
      else if (sc.length === 1) {
        single++;
        logging.infoF(LOG_NAME, 'buildCategories', `Not saving single skill category ${sc[0].skill}`);
      } else {
        const ordered_map = sc.sort((a, b) => b.weight - a.weight);
        const skills = ordered_map.map(o => o.skill);
        while (JSON.stringify(skills).length > 1300) skills.pop();
        const weights = ordered_map.map(o => o.weight).slice(0, skills.length);

        const vector = await skillVector(skills);

        new_sc.push(
          new SkillCategory({
            id: uuid(),
            version,
            skills,
            weights,
            vector,
          })
        );
      }
    }
  } else logging.warnF(LOG_NAME, 'buildCategories', `No clusters found`);

  if (!rebuild && new_sc) {
    logging.infoF(LOG_NAME, 'buildCategories', `Merging ${new_sc.length} skills with ${skill_categories.length} existing`)

    let ignore: Uid[] = [];
    let rebuild: SkillCategory[] = [];
    let rids: Uid[] = [];

    for (const nsc of new_sc) {
      for (const sc of skill_categories[GLOBAL_SKILL_CATEGORIES]) {
        const match = cosineSimilarity(nsc.vector, sc.vector);
        if (match >= 0.40) {
          ignore.push(nsc.id);
          if (!rids.includes(sc.id)) {
            rebuild.push(sc);
            rids.push(sc.id);
          }

          const remove_skills: string[] = [];

          let sco: { skill: string, weight: number }[] = [];
          const nsco: { skill: string, weight: number }[] = [];
          for (let i = 0;i < sc.skills.length;i++) {
            sco.push({ skill: sc.skills[i], weight: sc.weights[i] });
          }

          for (let i = 0;i < nsc.skills.length;i++) {
            const sci = sc.skills.indexOf(nsc.skills[i]);
            if (sci === -1 || sc.weights[sci] < nsc.weights[i]) {
              if (sci >= 0) remove_skills.push(nsc.skills[i]);
              nsco.push({ skill: nsc.skills[i], weight: nsc.weights[i] });
            }
          }

          sco = sco.filter(s => !remove_skills.includes(s.skill));

          const ordered_map = [...sco, ...nsco].sort((a, b) => b.weight - a.weight);

          const skills = ordered_map.map(o => o.skill);
          while (JSON.stringify(skills).length >= 1300) skills.pop();
          const weights = ordered_map.map(o => o.weight).slice(0, skills.length);

          sc.skills = skills;
          sc.weights = weights;
        }
      }
    }

    new_sc = new_sc.filter(sc => !ignore.includes(sc.id));

    logging.infoF(LOG_NAME, 'buildCategories', `Rebuilding vectors for ${rebuild.length} merged categories`)

    await Promise.all(rebuild.map(async sc => {
      sc.vector = await skillVector(sc.skills);
      while (JSON.stringify(sc.skills).length >= 1300) sc.skills.pop();
    }));

    logging.infoF(LOG_NAME, 'buildCategories', `Combining ${new_sc.length} new and existing and ${skill_categories.length} existing categories`)

    Object.assign(skill_categories[GLOBAL_SKILL_CATEGORIES], new_sc);
    if (logging.isDebug()) console.log(JSON.stringify(skill_categories));

    logging.infoF(LOG_NAME, 'buildCategories', `${skill_categories[GLOBAL_SKILL_CATEGORIES].length} combined categories`)

    // clean up old categories
    let rem_cats: SkillCategory[] = skill_categories[GLOBAL_SKILL_CATEGORIES].filter(s => s.skills.length <= 1);
    if (skill_categories[GLOBAL_SKILL_CATEGORIES].length > cat_count) {
      skill_categories[GLOBAL_SKILL_CATEGORIES].sort((a, b) => b.skills.length - a.skills.length);
      rem_cats = _.uniqBy(rem_cats.concat(skill_categories[GLOBAL_SKILL_CATEGORIES].splice(cat_count, skill_categories[GLOBAL_SKILL_CATEGORIES].length)), 'id');
    }

    if (rem_cats && rem_cats.length) await data.skills.deleteCategories(rem_cats);
    await saveSkillCategories(false);
  } else {
    if (logging.isDebug()) console.log(JSON.stringify(skill_categories));
    skill_categories[GLOBAL_SKILL_CATEGORIES] = new_sc.filter(s => s.skills.length > 1);
    await saveSkillCategories(rebuild);

    await data.plugins.bigQueryPlugin().exportGlobal(GlobalType.SkillCategory);
  }

  const histogram = skill_categories[GLOBAL_SKILL_CATEGORIES].map(g => g.skills.length).sort((a, b) => b - a);
  const stats = computeStats(histogram);

  logging.infoF(LOG_NAME, 'buildCategories', `empty: ${empty} single: ${single} ${JSON.stringify(stats)}`);
  logging.infoF(LOG_NAME, 'buildCategories', JSON.stringify(histogram));
}

function computeStats(raw_numbers: number[]) {
  const numbers = raw_numbers.filter(n => n !== 0);
  const sorted_numbers = numbers.sort((a, b) => a - b);
  const length = sorted_numbers.length;

  // Minimum value
  const min = sorted_numbers[0];

  // Maximum value
  const max = sorted_numbers[length - 1];

  // Median value
  const median =
    length % 2 === 0
      ? (sorted_numbers[length / 2 - 1] + sorted_numbers[length / 2]) / 2
      : sorted_numbers[Math.floor(length / 2)];

  // Mean value
  const csum = sum(sorted_numbers);
  const cmean = csum / length;

  // Standard deviation
  const squared_diff = sorted_numbers.map((num) => (num - cmean) ** 2);
  const variance = sum(squared_diff) / length;
  const stdv = Math.sqrt(variance);

  return {
    clusters: numbers.length,
    min: min,
    max: max,
    median: median.toFixed(2),
    mean: cmean.toFixed(2),
    stdv: stdv.toFixed(2),
  };
}
