import { Octokit } from '@octokit/rest';
import axios, { AxiosRequestConfig } from 'axios';
import crypto from 'crypto';
import { google } from 'googleapis';
import { decode } from 'html-entities';
import https from 'https';
import soup from 'jssoup';
import _ from 'lodash';
import { LRUCache } from 'lru-cache';
import PDLJS from 'peopledatalabs';
import { setTimeout } from "timers/promises";
import { TweetV2, TwitterApi } from 'twitter-api-v2';

import config from '../config';
import data from '../data';
import { skillVector } from './model';

import ForaUser from '../session/user';

import { Course } from '../types/globals';
import { Person, Tag } from '../types/items';
import { EducationLevel, JobTags, OrgTags, TagType, Uid, findTypeIndex, findTypeIndexes, findTypeTag, findTypeValue, findTypeValues } from '../types/shared';

import { DAYS, HOURS, MINUTES, SECONDS, sameDay, } from '../utils/datetime';
import { arraysIntersect, flatten, hash, mergeTags, permuteName, saveOne, saveOneTypeValue, sortTags } from '../utils/funcs';
import { Meanings, addMeaning, saveMeaningTags } from '../utils/imports';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

const search = google.customsearch('v1');
const kgsearch = google.kgsearch('v1');
let pdl_client;

type LearnSource = {
  url: string;
  host: string;
  path: string;
  query: string;
  data: any;
}

const DEBUG = (require('debug') as any)('fora:skills:learn');
const LOG_NAME = 'skills.learn';

const QUOTA_EXPIRE = 'search_quota_expire_date';

const IGNORE_PHOTO = [
  'licdn',
  'fbsbx',
];

const COURSE_IGNORE = [
  'course',
  'courses',
  'course',
  'class',
  'classes',
  'university',
  'college',
  'learn',
  'prerequisite',
  'applies',
  'catalog',
  'online class',
  'online courses',
  'student',
  'students',
  'complete',
  'eligible',
  'course is eligible',
];

const PDL_SUPPORT = [
  'facebook.com',
  'fb.me',
  'linkedin.com',
  'twitter.com',
  't.co',
  'xing.com',
  'indeed.com',
  'github.com',
  'meetup.com',
  'instagram.com',
  'quora.com',
  'gravatar.com',
  'klout.com',
  'stackoverflow.com',
  'angellist.com',
  'angel.co',
  'youtube.com',
  'foursquare.com',
  'pinterest.com',
  'vimeo.com',
  'about.me',
  'ello.co',
  'flickr.com',
  'crunchbase.com',
  'dribble.com',
  'google.com',
  'wordpress.com',
  'myspace.com',
  'behance.net',
  'soundcloud.com',
  'reddit.com',
  'gitlab.com',
  'medium.com',
];

const url_cache = new LRUCache({max: 100 });

config.onLoad('learn', async (silent: boolean) => {
  const pdl_key = config.get('PDL_API_KEY');
  if (pdl_key) {
    pdl_client = new PDLJS({apiKey: pdl_key});
  }
});

export default class Learn {
  user: ForaUser;
  offline_data: {[key:string]: any};
  person: Person;
  now = new Date();
  meanings: Meanings = {};
  job_tags: Tag[] = [];
  photos: string[] = [];
  other_urls: string[] = [];
  urls: string[] = [];
  url_meanings: {[key:string]: Meanings} = {};
  urlHandlers: {[key:string]: (source: LearnSource) => Promise<void>} = {};
  min_override: number | undefined = undefined;

  static async matchPeople(user: ForaUser, people: Person[], force: boolean, index?: number): Promise<Person[]> {
    logging.infoFP(LOG_NAME, 'matchPeople', user.profile, `Matching up to ${people.length} people`);

    let found = 0;

    const match_people = await data.people.matchPeople(user, people, true, index);
    const match_comms: {[key:string]: Partial<Person>[]} = {};
    const match_names: {[key:string]: Partial<Person>[]} = {};

    logging.infoFP(LOG_NAME, 'matchPeople', user.profile, `Matched ${match_people.length}/${people.length} people`);

    for (const match of match_people) {
      const emails = match.comms ? parsers.findEmail(match.comms) : null;
      if (emails) {
        for (const email of emails) {
          if (match_comms[email]) match_comms[email].push(match);
          else match_comms[email] = [match];
        }
      }

      const name = match.displayName.toLowerCase();
      if (match_names[name]) match_names[name].push(match);
      else match_names[name] = [match];
    }

    const learn_ids: Uid[] = [];
    const people_ids: {[key:string]: Person} = {};

    const skip_index: number[] = [];

    for (const index in people) {
      const person = people[index];
      const emails = person.comms ? parsers.findEmail(person.comms) : null;
      let found_people = emails ? flatten(emails.map(e => match_comms[e]).filter(p => p)) : [];
      if (!found_people.length && person.displayName) {
        found_people = match_names[person.displayName.toLowerCase()];
        if (found_people && found_people.length) {
          const orgs = person.tags ? person.tags.filter(t => t.type as TagType === TagType.organization).map(t => t.value.toLowerCase()) : null;
          if (orgs && orgs.length) {
            found_people = found_people.filter(p => 
              (p.tags ? p.tags.filter(t => t.value && t.type as TagType === TagType.organization && orgs.includes(t.value.toLowerCase())) : []).length > 0
            )
          }
        }
      }

      if (found_people && found_people.length) {
        found++;
        // Already have a global copy of the person
        logging.infoFP(LOG_NAME, 'matchPeople', user.profile, `Merging found person ${person.id} from ${found_people.length} global ${found_people.length === 1 ? 'person' : 'people'} --> '${person.displayName}'`);
        // if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'matchPeople', user.profile, `\t${found_people.length} people --> '${person.displayName}'`);
        for (const found_person of found_people) {
          const merged = peopleUtils.mergePeople(person, found_person, true);
          if (!force) skip_index.push(parseInt(index));
          if (merged && !learn_ids.includes(person.id)) {
            learn_ids.push(person.id);
          }
        }
        const orgs = person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
        parsers.cleanTags(person.tags, person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
      }

      if (people_ids[person.id]) {
        skip_index.push(parseInt(index));
        peopleUtils.mergePeople(people_ids[person.id], person, true);
      } else {
        people_ids[person.id] = person;
      }
    }

    logging.infoFP(LOG_NAME, 'matchPeople', user.profile, `Matched ${found}/${people.length} people`);


    return people.map((p,i) => { return {p,i}}).filter(({p,i}) => !skip_index.includes(i)).map(({p,i}) => p);
  }

  static async learnPeople(user: ForaUser, people: Person[], force = false): Promise<Person[]> {
    if (!people || !people.length) {
      logging.warnFP(LOG_NAME, 'learnPeople', user.profile, `No people to learn`);
      return;
    }

    const no_name = people.filter(p => !p.displayName || !p.displayName.length);
    if (no_name.length) {
      logging.warnFP(LOG_NAME, 'learnPeople', user.profile, `Not learning ${no_name.length}/${people.length} with no name: ${JSON.stringify(no_name)}`);
    }

    people = people.filter(p => p.displayName && p.displayName.length);

    logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Learning ${people.length} people ${force ? 'force' : ''}`);

    // Learn anyone new or outdated
    const searchers: Promise<void>[] = [];
    const learners: Promise<void>[] = [];
    const matchers: Promise<void>[] = [];

    const did_learn: Date[] = [];
    const did_search: Date[] = [];

    let learn_count = 0;
    const learn_people: Person[] = [];

    let index = 0;
    const match_people = people.slice();
    while(match_people.length) {
      matchers.push(new Promise(async c => {
        const lp = await Learn.matchPeople(user, match_people.splice(0,20), force, index++);
        lp.forEach(p => learn_people.push(p));
        c();
      }));

      if (matchers.length === 10) {
        logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Waiting for ${matchers.length} matchers. ${match_people.length} people in queue.`);
        await Promise.all(matchers);
        matchers.splice(0);
      }
    }

    logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Waiting for final ${matchers.length} matchers. ${match_people.length} people in queue.`);
    await Promise.all(matchers);

    logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Matching complete learning ${learn_people.length} people`);

    for(const person of learn_people) {
      if (force || !person.learned || !person.learned.length || person.learned.reduce((a,b) => a > b ? a : b, new Date(0)).getTime() === 0) {
        learn_count++;

        learners.push(new Promise<void>(async (c) => {
          const learn = new Learn(user);
          let search_done;
          searchers.push(new Promise<void>(c => search_done = c));
          const save_person = await learn.learnPerson(person, false, force, () => { 
            did_search.push(new Date()); 
            logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Done searching ${JSON.stringify(person)}`);
            search_done(); 
          }).catch(e => {
            logging.errorFP(LOG_NAME, 'learnPeople', user.profile, `Error learning ${JSON.stringify(person)}`, e);
          }).finally(() => did_learn.push(new Date()));
          if (save_person) peopleUtils.mergePeople(person, save_person, true);
          c();
        }));
      } // else learn_people.push(person);

      while (searchers.length >= 20) {
        logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Waiting on ${searchers.length} searches`);
        await Promise.any([Promise.all(searchers), setTimeout(5000)]);
        logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Checking on ${searchers.length - did_search.length} searches`);
        if (did_search.length === searchers.length) {
          searchers.splice(0);
          did_search.splice(0);
        }
      }

      while (learners.length >= 300) {
        logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Waiting on ${learners.length} learners`);
        await Promise.any([Promise.all(learners), setTimeout(5000)]);
        logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Checking on ${learners.length - did_learn.length} searches`);
        if (did_learn.length === learners.length) {
          learners.splice(0);
          did_learn.splice(0);
        }
      }

      // stagger starts
      await setTimeout(50);
    }

    logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Final wait on ${learners.length} learners`);
    while(learners.length) {
      await Promise.all([Promise.all(learners), setTimeout(5000)]);
      logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Checking on ${learners.length - did_learn.length} searches`);
      if (did_learn.length === learners.length) {
        learners.splice(0);
        did_learn.splice(0);
      }
    }

    logging.infoFP(LOG_NAME, 'learnPeople', user.profile, `Learned ${learn_count}/${people.length} people`);
    return learn_people;
  }

  constructor(user: ForaUser, offline_data: {[key: string]: any} = null, min_override?: number) {
    this.user = user;
    this.offline_data = offline_data;
    this.min_override = min_override;
    this.urlHandlers = {
      'twitter.com': this.learnTwitter.bind(this),
      't.co': this.learnTwitter.bind(this),
      'linkedin.com': this.learnLinkedIn.bind(this),
      'crunchbase.com': this.learnPDL.bind(this), //learnCrunchbase,
      'github.com': this.learnGithub.bind(this),
      'blogger.com': this.learnBlogger.bind(this),
      'blogspot.com': this.learnBlogger.bind(this),
      'facebook.com': this.learnPDL.bind(this), // learnFacebook,
      'fb.me': this.learnPDL.bind(this), // learnFacebook,
      'instagram.com': this.learnPDL.bind(this),
      'slideshare.com': this.learnSlideshare.bind(this),
      'flickr.com': this.learnPDL.bind(this), // learnFlickr,
      'about.me': this.learnAbout.bind(this),
      'angel.co': this.learnAngelList.bind(this),
      'angellist.com': this.learnAngelList.bind(this),
      'pinterest.com': this.learnPinterest.bind(this),
      'medium.com': this.learnMedium.bind(this),
      'reddit.com': this.learnReddit.bind(this),
      'youtube.com': this.learnPDL.bind(this), // learnYoutube,
      'plus.google': this.learnGoogle.bind(this),
      'quora.com': this.learnQuora.bind(this),
      'wikipedia.com': this.learnWikipedia.bind(this),
      'credly.com': this.learnCredly.bind(this),
      'askfora.com': this.learnAskFora.bind(this),
      'localhost': this.learnDefault.bind(this),
      'alison.com': this.learnAlison.bind(this),
      'edx.org': this.learnEdX.bind(this),
      'phoenix.edu': this.learnPhoenix.bind(this), 
      'ted.com': this.learnTed.bind(this),
      'masterclass.com': this.learnMasterClass.bind(this),
      'coursera.org': this.learnCoursera.bind(this),
      'udemy.com': this.learnUdemy.bind(this),
      'test0.com': this.test0.bind(this),
      'test1.com': this.test1.bind(this),
    };
  }

  async test0(source: LearnSource) {
    await setTimeout(5000);
    this.addMeaning('testing foo bar baz bing bada boom');
  }

  async test1(source: LearnSource) {
    try {
      await axios({url: 'http://foo.bar.baz', method: 'GET' });
    } catch(e) {
      logging.warnFP(LOG_NAME, 'test1', this.user.profile, `Error`, e);
    }
  }

  /**************
   * Google Search
   **************/
  async searchEntities(new_index: number) {
    if (!this.person) return new_index;
    // run google searches on person and each organization looking for URLs
    let oq = '';

    const org_tags = findTypeValues(this.person.tags, [TagType.organization, TagType.school]);
    if (org_tags.length) {
      const orgs = org_tags.map(tag => `"${tag}"`).join(' OR ');
      oq = ` AND (${orgs})`;
    } else {
      logging.infoFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} skipping with no orgs`);
      return new_index;
    }

    const q = `${this.person.displayName}${oq}`;
    logging.infoFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} q = ${q}`);

    // don't search for single words
    if (q.split(' ').length < 2) return;

    const args = {
      q,
      start: 1,
      cx: config.get('GOOGLE_SEARCH_ID'),
      auth: config.get('GOOGLE_SEARCH_API_KEY'),
    };

    let quota_expire_date = await data.plugins.cachePlugin().get(QUOTA_EXPIRE);
    let google_quota = false;

    // reset google quota at midnight PST (i.e. now !== quota_expire_date
    if (quota_expire_date && sameDay(this.now, new Date(quota_expire_date), 480)) google_quota = true;

    let delay = 500;
    const timeout = SECONDS(this.now, 30); // timeout in 30s
    let now = new Date();
    while (now < timeout && args.start <= 10) { // && !google_quota) 
      let e,r;
      await Promise.any([
        new Promise<void>(async (c, r) => {
          let wait = 100;
          while(wait < 30000) {
            try {
              const cse = config.get('SEARCH_SITE_RESTRICT') || google_quota ? search.cse.siterestrict : search.cse;
              [e,r] = await new Promise<[any,any]>(l => cse.list(args, (e,r) => l([e,r])));
              if (!e || (e.code !== 'ECONNRESET' && e.statusCode !== 'ECONNRESET' && (!e.message || !e.message.includes('quota')))) {
                c();
                break;
              }
              await setTimeout(wait);
              wait += 2;
            } catch (err) {
              logging.errorFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} Search Entities Error`, err);
              c();
            }
          }
        }), setTimeout(30000)]);

      if (e) {
        if (e.code === 403 || e.code === '403' || ((e.code === 429 || e.code == '429') && e.message && e.message.includes('Queries per day'))) {
          if (!google_quota) {
            google_quota = true;
            quota_expire_date = new Date()
            await data.plugins.cachePlugin().set(QUOTA_EXPIRE, quota_expire_date, { expires: 3600 * 24 });
            logging.warnFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} Max daily quota searching entities for ${q} ${args.start}`, e);
          } else break;
        } else if(e.code == 429) {
          // throttled
          await setTimeout(delay);
          logging.warnFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} Rate limit quota searching entities for ${q} ${args.start}`, e);
          delay *= 2;
        } else {
          logging.errorFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} Error searching entities for ${q} ${args.start}`, e);
          break;
        }
      } else if(r && r.data && r.data.items) {
        // look for best linkedin if it's in the list
        const items = []
        let li;
        for (const item of r.data.items) {
          if (item.link && item.link.startsWith('https://www.linkedin.com/')) {
            if (li) {
              // compare for best match
              let li_name_title;
              let li_names_title;
              let li_orgs_title;

              let li_name_snippet;
              let li_names_snippet;
              let li_orgs_snippet;

              let li_names_link;

              if (li.title) {
                li_name_title = li.title.includes(this.person.displayName);
                li_names_title = this.person.names ? this.person.names.map(n => li.title.includes(n)).length : 0;
                li_orgs_title = org_tags.map(o => li.title.includes(o)).length;
              }

              if (li.snippet) {
                li_name_snippet = li.snippet.includes(this.person.displayName);
                li_names_snippet = this.person.names ? this.person.names.map(n => li.snippet.includes(n)).length : 0;
                li_orgs_snippet = org_tags.map(o => li.snippet.includes(o)).length;
              }

              if (li.link) {
                const link = li.link.toLowerCase();
                li_names_link = this.person.names ? this.person.names.map(n => link.includes(n.toLowerCase())).length : 0;
              }

              let xi_name_title;
              let xi_names_title;
              let xi_orgs_title;

              let xi_name_snippet;
              let xi_names_snippet;
              let xi_orgs_snippet;

              let xi_names_link;

              if (item.title) {
                xi_name_title = item.title.includes(this.person.displayName);
                xi_names_title = this.person.names ? this.person.names.map(n => item.title.includes(n)).length : 0;
                xi_orgs_title = org_tags.map(o => item.title.includes(o)).length;
              }

              if (item.snippet) {
                xi_name_snippet = item.snippet.includes(this.person.displayName);
                xi_names_snippet = this.person.names ? this.person.names.map(n => item.snippet.includes(n)).length : 0;
                xi_orgs_snippet = org_tags.map(o => item.snippet.includes(o)).length;
              }

              if (item.link) {
                const link = item.link.toLowerCase();
                xi_names_link = this.person.names ? this.person.names.map(n => link.includes(n.toLowerCase())).length : 0;
              }

              if(li_name_title && xi_name_title) {
                if(li_name_snippet && xi_name_snippet) {
                  if (li_orgs_title.length === xi_orgs_title.length) {
                    if (li_orgs_snippet.length === xi_orgs_snippet.length) {
                      if (li_names_title.length === xi_names_title.length) {
                        if (li_names_snippet.length === xi_names_snippet.length) {
                          if(xi_names_link && !li_names_link) li = item;
                          // else keep li
                        } else if(xi_names_snippet.length > li_names_snippet.length) li = item;
                        // else keep li
                      } else if(xi_names_title.length > li_names_title.length) li = item;
                      // else keep li
                    } else if(xi_orgs_snippet.length > li_orgs_snippet.length) li = item;
                    // else keep li
                  } else if(xi_orgs_title.length > li_orgs_title.length) li = item;
                  // else keep li
                } else if(xi_name_snippet) li = item;
                // else keep li
              } else if(xi_name_title) li = item;
              // else keep li

            } else li = item;
          } else items.push(item);
        }

        if (li) items.push(li);

        for (const item of items) {
          if (item.link) saveOne(this.urls, item.link);

          if (item.title) this.addMeaning(item.title, 50);
          if (item.snippet) this.addMeaning(item.snippet, 20);

          if (item.pagemap) {
            if (item.pagemap.hcard) new_index = this.learnHCard(item.pagemap.hcard, new_index);
            if (item.pagemap.person) new_index = this.learnPGPerson(item.pagemap.person, new_index);

            if (item.pagemap.cse_image && item.pagemap.cse_image.length) {
              for (const image of item.pagemap.cse_image) {
                if (image.src && !parsers.checkKeyword(image.src.toLowerCase(), IGNORE_PHOTO)) {
                  saveOne(this.photos, image.src);
                }
              }
            } else if (item.pagemap.cse_thumbnail && item.pagemap.cse_thumbnail.length) {
              for (const image of item.pagemap.cse_thumbnail) {
                if (!parsers.checkKeyword(image.src.toLowerCase(), IGNORE_PHOTO)) {
                  saveOne(this.photos, image.src);
                }
              }
            }

            if (item.pagemap.metatags) {
              for (const meta of item.pagemap.metatags) {
                for (const m in meta) {
                  if (m.includes('title')) this.addMeaning(meta[m], 50);
                  if(m.includes('description')) this.addMeaning(meta[m], 20);
                  if (m.includes('url')) saveOne(this.urls, meta[m]);
                  if (m.includes('image') && !parsers.checkKeyword(meta[m].toLowerCase(), IGNORE_PHOTO)) {
                    saveOne(this.photos, meta[m]);
                  }
                }
              }
            }
          }
        }
        
        if (r.data.queries.nextPage) args.start = r.data.queries.nextPage[0].startIndex;
        else break;
      } else {
        logging.warnFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} Timeout searching entities for ${q} ${args.start}`);
        break;
      }
      now = new Date();
    }

    if(now >= timeout) logging.warnFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} Total timeout searching entities for ${q} ${args.start}`);
    else if(args.start > 100) logging.warnFP(LOG_NAME, 'searchEntities', this.user.profile, `${this.person.id} Max pages searching entities for ${q} ${args.start}`);

    return new_index;
  }

  /**************
   * Learn from HCARDs in search results - save to a public only and the private learned person
   **************/
  learnHCard(hcard, new_index: number): number {
    // handle HCARD schema updates
    // fn - full name
    // url - url
    // photo - photo url
    // title - title
    for (const card of hcard) {
      // sanity check that the name matches
      let name_match = 0;
      if (card.fn) {
        const [d,n,p] = permuteName(card.fn);
        for (const name of n) {
          const added = saveOne(this.person.names, name);
          if (!added) name_match++;
        }
      }

      if (name_match) {
        if (card.photo) {
          const url = parsers.findUrl(card.photo);
          if (url) {
            saveOne(this.photos, url);
          }
        }

        if (card.org) {
          saveOneTypeValue(this.job_tags, new Tag(TagType.organization, hcard[new_index].org, new_index, this.now));
        }

        if (card.title) {
          saveOneTypeValue(this.job_tags, new Tag(TagType.jobTitle, card.title, new_index, this.now));
        }

        if (card.url) {
          const url = parsers.findUrl(card.url);
          if (url) {
            if (name_match) {
              saveOne(this.person.urls, url);
            } else saveOne(this.other_urls, url);
          }
        }

        new_index++;
      }
    }

    return new_index;
  }

  /**************
   * Learn from Person in search results - save to a public only and the private learned person
   **************/
  learnPGPerson(search_person, new_index: number): number {
    // handle PERSON schema updates
    // org - organization
    // role - role
    // image - photo url
    // name - full name
    // additionalname - handle
    // worksfor - company
    // homelocation - location
    // email - ?
    // url - url
    // follows - url
    for (const pg_person of search_person) {
      // sanity check that the name matches
      let name_match = false;
      if (pg_person.name) {
        const [d,n,p] = permuteName(pg_person.name);
        for (const name of n) {
          const added = saveOne(this.person.names, name);
          if (!added) name_match = true;
        }
      }

      if (name_match) {
        if (pg_person.org) {
          saveOneTypeValue(this.job_tags, new Tag(TagType.organization, pg_person.org, new_index, this.now));
        }

        if (pg_person.role) {
          saveOneTypeValue(this.job_tags, new Tag(TagType.jobTitle, pg_person.role, new_index, this.now));
        }

        if (pg_person.worksfor) {
          saveOneTypeValue(this.job_tags, new Tag(TagType.organization, pg_person.worksfor, new_index, this.now));
        }

        if (pg_person.additionalname) {
          const [, n] = permuteName(pg_person.additionalname);
          for (const name of n) {
            saveOne(this.person.names, name);
          }
        }

        if (pg_person.email) {
          const email = parsers.findEmail(pg_person.email);
          if (email) {
            for (const e of email) {
              saveOne(this.person.comms, e);
            }
          }
        }

        if (pg_person.image) {
          const url = parsers.findUrl(pg_person.image);
          if (url) {
            saveOne(this.photos, url);
          }
        }
      }

      // if(item.pagemap.person[pindex].homelocation)
      if (pg_person.url) {
        const url = parsers.findUrl(pg_person.url);
        if (url) {
          if (name_match) {
            saveOne(this.person.urls, url);
          } else saveOne(this.other_urls, url);
        }
      }

      // if(item.pagemap.person[pindex].follows)
    }

    return new_index;
  }



  /**************
   * Google Knowledge Graph Search
   **************/
  async searchGraph(new_index: number) {
    if (!this.person) return new_index;

    if (!this.person.displayName || !this.person.displayName.length) {
      logging.warnFP(LOG_NAME, 'searchGraph', this.user.profile, `${this.person.id} No display name to search`);
      return new_index;
    }

    const args = {
      query: this.person.displayName,
      auth: config.get('GOOGLE_SEARCH_API_KEY'),
    };

    const [e, r] = await new Promise<[any,any]>(async (c,r) => {
      let wait = 100;
      while(wait < 60000){
        try {
          kgsearch.entities.search(args).then(res => c([null, res]), err => {
            if (err.code !== 'ECONNRESET' && err.statusCode !== 'ECONNRESET' 
              && err.code !== 429 && err.statusCode !== 429) c([err, null]);
          });
          await setTimeout(wait);
          wait *= 2;
        } catch(e) {
          logging.errorFP(LOG_NAME, 'searchGraph', this.user.profile, `${this.person.id} Search Graph Error`, e);
          c([e, null]);
        }
      }
    });

    if (e) {
      logging.errorFP(LOG_NAME, 'searchGraph', this.user.profile,  `${this.person.id} Error searching graph for ${this.person.displayName}`, e);
    } else if(r) {
      if (r.data && r.data.itemListElement) {
        for (const item of r.data.itemListElement) {
          if (item.result) {
            if (item.result['@type']) {
              if(item.result['@type'].includes('Person')) {
                const [d,n,p] = permuteName(item.result.name);
                for (const name of n) {
                  saveOne(this.person.names, name);
                }
              }

              if (item.result['@type'].includes('Organization')) {
                saveOneTypeValue(this.job_tags, new Tag(TagType.organization, item.result.name, new_index, this.now));
                new_index++;

                if (item.result.detailedDescription) {
                  if (item.result.detailedDescription.articleBody) this.addMeaning(item.result.detailedDescription.articleBody, 10);
                  if (item.result.detailedDescription.url) saveOne(this.other_urls, item.result.detailedDescription.url);
                }
              }

              if (item.result['@type'].includes('Event') && item.resultname && item.result.name.length) {
                this.addMeaning(item.result.name, 20);
              }
            }
          }
        }
      }
    }
    return new_index;
  }

  /**************
   * Generic URL search
   **************/
  /**function hasName(names, text) {
    return true;
    for (const name of names) if (text.indexOf(name) > -1) return true;
    return false;
  }*/

  learnProfile(s: any) {
    const meta = s.findAll(TagType.meta).map(a => a.attrs).filter(a => a.property);

    const raw_name = meta.find(a => a.name === 'author');
    let name = raw_name && raw_name.content ? decode(raw_name.content) : null;

    const raw_username = meta.find(a => a.property === 'profile:username');
    const username = raw_username && raw_username.content ? decode(raw_username.content) : null;

    const raw_first_name = meta.find(a => a.property === 'profile:first_name');
    const first_name = raw_first_name && raw_first_name.content ? decode(raw_first_name.content) : null;

    const raw_last_name = meta.find(a => a.property === 'profile:last_name');
    const last_name = raw_last_name && raw_last_name.content ? decode(raw_last_name.content) : null;

    const raw_url = meta.find(a => a.property === 'article:author');
    const url = raw_url ? raw_url.content : null;

    const raw_description = meta.find(a => a.name === 'description');
    const description = raw_description && raw_description.content ? decode(raw_description.content) : null;

    if (!name) name = first_name || last_name ? `${first_name ? first_name : ''} ${last_name ? last_name : ''}`.trim() : username;

    this.learnName(name);
    if (username) saveOne(this.person.names, username);
    if (first_name) saveOne(this.person.names, first_name);
    if (last_name) saveOne(this.person.names, last_name);

    if (url && url.length && !this.urls.includes(url)) {
      this.urls.push(url);
      if (!this.person.urls.includes(url)) this.person.urls.push(url);
    }

    if (description && description.length) this.addMeaning(description, 20);
  }

  learnOG(s: any) {
    const meta = s.findAll(TagType.meta).map(a => a.attrs).filter(a => a.property);

    const og = {
      image: undefined,
      title: undefined,
      desc: undefined,
      url: undefined,
    }

    const raw_photo = meta.find(a => a.property === 'og:image');
    og.image = raw_photo ? raw_photo.content : null;

    const raw_title = meta.find(a => a.property === 'og:title');
    og.title = raw_title && raw_title.content ? decode(raw_title.content) : null;

    const raw_desc = meta.find(a => a.property === 'og:description');
    og.desc = raw_desc && raw_desc.content ? decode(raw_desc.content) : null;

    const raw_url = meta.find(a => a.property === 'og:url');
    og.url = raw_url ? raw_url.content : null;

    return og;
  }

  learnTags(s: any, tags = ['div', 'p', 'span', 'a', 'th', 'td', 'strong'], bias = 1, ignore = []) {
    if (!tags) return;
    let t = s.descendants.length === 1 ? [s.text] : [];

    for (const tag of tags) {
      t = t.concat(s.findAll(tag).filter(d => d.descendants.length === 1).map(d => d.text)).filter(d => d);
    }

    t = t.concat(s.findAll(TagType.meta).filter(d => d.attrs.name === 'keywords').map(d => d.attrs.content).filter(d => d));

    if (logging.isDebug(this.user.profile)) DEBUG('learnDefault %s::%s::T = %O', this.user.profile, this.person ? this.person.id : '', t);
    if (t.length) this.addMeaning(t, bias, ignore);
    else logging.warnFP(LOG_NAME, 'learnTags', this.user.profile, `${this.person ? this.person.id : ''} Found nothing for ${JSON.stringify(tags)}`);
  }

  learnName(name) {
    if (name && name.length) {
      if (!this.person.displayName || !this.person.displayName.length) { // || person.displayName.split(' ').length < name.split(' ').length) {
        this.person.displayName = name;
      }

      saveOne(this.person.names, name);
      const [d,n,p] = permuteName(name);
      for (const nn of n) saveOne(this.person.names, nn);
      for (const pn of p) saveOne(this.person.names, pn);
    }
  }

  async fetchUrl(source: Partial<LearnSource>, add_config?: AxiosRequestConfig) {
    if (!source.url) return null;

    let headers = null;
    if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'fetchUrl', this.user.profile, `${this.person ? this.person.id : ''} Fetching ${source.url} from ${source.host}`);
    try {
      const data = url_cache.get(source.url);
      if (data) return data;

      const url = new URL(source.url);

      const controller = new AbortController();

      const config: AxiosRequestConfig = {
        signal: controller.signal,
        method: 'get',
        url: `${url.origin}${url.pathname}`,
        params: parsers.findParams(url.search),
        httpsAgent: new https.Agent({  rejectUnauthorized: false }),
        headers: {
          host: source.host, // `${host}`,
          accept: 'application/json,text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
          'accept-encoding': 'gzip, deflate, sdch',
          'accept-language': 'en-US,en;q=0.9',
          'cache-control': 'max-age=0',
          dnt: 1,
          // connection: 'keep-alive', 
          'upgrade-insecure-requests': 1,
          'user-agent': 'Mozilla/5.0 (X11; CrOS x86_64 14588.123.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.72 Safari/537.36',
        },
      };

      if (add_config) {
        Object.assign(config, add_config);
      }

      let aborted = false;

      let res;
      setTimeout(10000).then(x => {
        if (!res) {
          logging.warnFP(LOG_NAME, 'fetchUrl', this.user.profile, `Timed out in 10s trying to reach ${source.url}`);
          aborted = true;
          controller.abort();
        }
      });

      res = await axios(config);
      if (res) {
        headers = res.headers;
        if (!aborted) controller.abort();

        if (res.data) url_cache.set(source.url, res.data);
        return res.data;
      }

    } catch (err) {
      logging.warnFP(LOG_NAME, 'fetchUrl', this.user.profile, `${this.person ? this.person.id : ''} Issue learning ${JSON.stringify(source)}`, err);
    }
  }

  async learnDefault(source: LearnSource, tags?: string[], bias = 1, meta = false) {
    if (logging.isDebug(this.user.profile)) DEBUG('learnDefault %s::%s::URL = %s', this.user.profile, this.person ? this.person.id : '', source.url);
    try {
      let data = config.isEnvOffline() ? source.data : null;
      if (!config.isEnvOffline()) {
        // remove numerics from host for headers
        // const host = source.host.replace(/^www\d/, 'www');
        data = await this.fetchUrl(source);
      }

      if (data) {
        const s = new soup(data);
        if (tags && tags.length) this.learnTags(s, tags, bias);
        else {
          this.learnTags(s, ['div'], 30);
          this.learnTags(s, ['a', 'h1', 'h2', 'h3', 'strong'], 20);
          this.learnTags(s, ['span', 'p'], 10);
        }

        if (meta) {
          const title = s.find('title');
          if (title) {
              saveOneTypeValue(this.job_tags, new Tag(TagType.title, title.text.trim()));
          }
        }
      }
      else logging.infoFP(LOG_NAME, 'learnDefault', this.user.profile, `${this.person ? this.person.id : ''} no data from ${source.url}`);
    } catch (err) {
      logging.warnFP(LOG_NAME, 'learnDefault', this.user.profile, `${this.person ? this.person.id : ''} Issue learning ${JSON.stringify(source)}`, err);
    }
  }

  async learnAlison(source: LearnSource) {
    try {
      const data = source.data ? source.data : await this.fetchUrl(source);
      const info = typeof data === 'string' ? JSON.parse(data) : data; 
      const course = info.course;
      const modules = info.modules;
      let link;

      const alison_ignore = ['alison', 'range', 'online', 'career goals', 'successful completion',
        'live demonstration', 'live demonstrations',
        'lessons summary', 'lesson summary', 'learning summary',
        'learning outcomes', 'learning outcome', 
        'course assessment', 'part 1', 'part 2', ...COURSE_IGNORE];

      const suffix = new RegExp(` (- )?(${alison_ignore.join('|')})$`, 'i');

      if (course) {
        if (course.slug) link = `https://alison.com/course/${course.slug}`;

        if (course.title) {
          this.addMeaning(course.title, 50, alison_ignore);
          saveOneTypeValue(this.job_tags, new Tag(TagType.title, course.title));
        } else if (course.slug) {
          saveOneTypeValue(this.job_tags, new Tag(TagType.title, _.startCase(course.slug.replace(/-/g, ' '))));
        }

        if (course.headline) this.addMeaning(course.title, 30, alison_ignore);
        if (course.outcomes) this.addMeaning(parsers.eText(new soup(course.outcomes)), 20, alison_ignore);
        if (course.summary) this.addMeaning(parsers.eText(new soup(course.summary)), 10, alison_ignore);
        if (course.short_summary) this.addMeaning(course.short_summary, 10, alison_ignore);
        if (course._embed?.tags) {
          course._embed?.tags.forEach(tag => {
            this.addMeaning(tag.name, 50, alison_ignore);
            this.addMeaning(tag.description, 20, alison_ignore);
          });
        }

        if(link) this.urls.push(link);
      }

      if (modules) {
        for (const module of modules) {
          if (module.title) this.addMeaning(module.title, 40, alison_ignore);
          if (module.headine) this.addMeaning(module.headline, 30, alison_ignore);
          if (module.topics) {
            module.topics.forEach(t => {
              const name = t.name.replace(suffix, ' ');
              this.addMeaning(name, 20, alison_ignore);
            });
          }
        }
      }

    } catch(e) {
      logging.errorF(LOG_NAME, 'learnAlison', `Error learning ${source.url}`, e)
    }
  }

  async learnUdemy(source: LearnSource) {
    const data = source.data ? source.data : await this.fetchUrl(source);
    const udemy_ignore = [ 
      "provides in-person data science",
      "practical manner",
      "lecture comes",
      "full coding screencast",
      "corresponding code notebook",
      "whatever manner is",
      "best you",
      "will start helping you get python installed",
      "your computer",
      "regardless your operating system",
      "its linux",
      "windows",
      "wide variety",
      "topics",
      "much more",
      "course is taught",
      "lead instructor",
      "app brewery",
      "london",
      "leading in-person programming bootcamp",
      "course has been updated to be",
      "2024 ready you 'll be learning latest tools",
      "technologies used",
      "large companies",
      "such apple",
      "google",
      "course does",
      "n't cut corners",
      "there are beautiful animated explanation videos",
      "beautiful real-world projects",
      "which you will get to build",
      "curriculum was developed",
      "period",
      "four years",
      "comprehensive student testing",
      "feedback",
      "million students",
      "how to code",
      "many have gone to change their lives",
      "becoming professional developers",
      "starting their own tech startup",
      "ll save yourself 12,000 usd",
      "enrolling",
      "still get access to",
      "same teaching materials",
      "learn same instructor",
      "our in-person programming bootcamp",
      "constantly updated new content",
      "new projects",
      "modules determined",
      "animated video lectures",
      "will make",
      "realistic experience",
      "higher success",
      "your future",
      "100 completely original practice questions",
      "you need more work",
      "gain confidence",
      "you will pass real exam",
      "live examples",
      "which day course",
      "you will get",
      "hands-on examples",
      "worldwide have enrolled our online courses",
      "youtube tutorials help",
      "course is to instruct",
    ];

    try {
      const s = new soup(data);

      const base = s.find('base');
      if (base?.attrs?.href) {
        const link = `https://www.udemy.com${s.find('base').attrs.href}`;
        this.urls.push(link);

        const learn = s.findAll('li').filter(x => x.parent.parent.name === 'body').map(x => x.text)
          .filter(x => x.split(' ').length < 5);

        if (learn.length) this.addMeaning(learn, 30, udemy_ignore);

        const dcp = s.findAll('div').find(x => x.attrs['data-component-props']);
        if (dcp) {
          try {
            const raw = decode(dcp.attrs['data-component-props']);
            const script = JSON.parse(raw);
            const course = script.serverSideProps.course;
            const t = new soup(course.description);

            const learn = t.findAll('li').map(x => x.text);
            this.addMeaning(learn, 30, udemy_ignore);
            this.addMeaning(course.headline, 40, udemy_ignore);
            this.addMeaning(course.objectives, 40, udemy_ignore);


            this.addMeaning(t.findAll('p').map(x => decode(x.text)), 20, [...udemy_ignore, ...parsers.expandWords(course.instructors.instructors_info.map(i => i.display_name))]);
          } catch(e) {
            logging.warnF(LOG_NAME, 'learnUdemy', `Can't parse course data`, e);
          }
        }

        const title = s.find('h1')?.text
        if (title) saveOneTypeValue(this.job_tags, new Tag(TagType.title, title));
      }
    } catch(e) {
      return null;
    }
  }

  async learnCoursera(source: LearnSource) {
    const data = source.data ? source.data : await this.fetchUrl(source);

    const coursera_ignore = ['week', 'week 1', 'week 2', 'week 3', 'week 4', 'week 5', 'week 6', 'week 7', 'week 8', 'week 9', 'week 10'];

    try {
      let course;
      const s = new soup(data).findAll('script').find(s => s.attrs['type'] === 'application/ld+json');
      try {
        course = JSON.parse(s.text);
      } catch(e) {
        return null;
      }

      if (course) {
        const learn = new Learn(new ForaUser('anonymous'), null, 0);

        if(course.name){ 
          this.addMeaning(course.name, 50, coursera_ignore);
          saveOneTypeValue(this.job_tags, new Tag(TagType.title, course.name));
        }

        if(course.desciprtion) {
          this.addMeaning(course.decsription, 30, coursera_ignore);
        }

        if(course.about) {
          this.addMeaning(course.about, 50, coursera_ignore);
        }

        if(course.teaches) {
          this.addMeaning(course.teaches, 40, coursera_ignore);
        }

        if(course.syllabusSections) {
          course.syllabusSections.forEach(ss => {
            if (ss.name) this.addMeaning(ss.name, 40, coursera_ignore);
            if (ss.description) this.addMeaning(ss.decsription, 30, coursera_ignore);
          });
        }


        const url = course['@id'];
        if (url) this.urls.push(url);
      }
  
    } catch(e) {
      logging.errorF(LOG_NAME, 'learnCoursera', `Error learning ${source.url}`, e)
    }
    
  }

  async learnEdX(source: LearnSource) {
    try {
      const data = source.data ? source.data : await this.fetchUrl(source);
      const course = typeof data === 'string' ? JSON.parse(data) : data; 

      if (course.result.pageContext.course) {
        const title = course.result.pageContext.course.title;
        const link = course.path.startsWith('http') ? course.path : `https://www.edx.org${course.path}`;

        const edx_ignore = ['edx', 'harvardx', 'mitx', ...COURSE_IGNORE];

        if (course.result.pageContext.course.shortDescription) {
          this.addMeaning(parsers.eText(new soup(course.result.pageContext.course.shortDescription)), 30, edx_ignore);
        }

        if (course.result.pageContext.course.fullDescription) {
          this.addMeaning(parsers.eText(new soup(course.result.pageContext.course.fullDescription)), 20, edx_ignore);
        }

        if (course.result.pageContext.course.syllabusRaw) {
          this.addMeaning(parsers.eText(new soup(course.result.pageContext.course.syllabusRaw)), 10, edx_ignore);
        }

        if (course.result.pageContext.course.faq) {
          this.addMeaning(parsers.eText(new soup(course.result.pageContext.course.faq)), 10, edx_ignore);
        }

        this.urls.push(link);

        saveOneTypeValue(this.job_tags, new Tag(TagType.title, title));
      }
    } catch(e) {
      logging.errorF(LOG_NAME, 'learnEdx', `Error learning ${source.url}`, e)
    }
  }

  async learnPhoenix(source: LearnSource) {
    const data = source.data ? source.data : await this.fetchUrl(source);
    const course = typeof data === 'string' ? JSON.parse(data) : data; 

    const link = `https://www.phoenix.edu/online-courses/${course.code.replace(/\//,'').toLowerCase()}`;
    const title = course.title;

    const objectives = new soup(course.objectives);
    const desc_i = new soup(course.descriptionInternal);
    const desc_p = new soup(course.descriptionPublic);

    const categories = course.categories.map(x => x.name).join('. ');
    const subjects = course.subjects.map(x => x.name).join('. ');
    const sections = _.uniqBy(course.sections, 'code').map(x => x['sectionTitle']).join('. ');

    const obj_skills = parsers.eText(objectives, x => x.toLowerCase().startsWith('week'), y => y.replace(/&reg;/g, ''));
    const desc_i_skills = parsers.eText(desc_i, x => x.toLowerCase().startsWith('week'), y => y.replace(/&reg;/g, ''));
    const desc_p_skills = parsers.eText(desc_p, x => x.toLowerCase().startsWith('week'), y => y.replace(/&reg;/g, ''));

    const uopx_ignore = ['uopx', 'unversity', 'phoenix', ...COURSE_IGNORE];

    this.addMeaning(title, 50, uopx_ignore);
    this.addMeaning(obj_skills, 40, uopx_ignore);
    this.addMeaning(desc_i_skills, 10, uopx_ignore);
    this.addMeaning(desc_p_skills, 10, uopx_ignore);

    this.addMeaning(categories, 30, uopx_ignore);
    this.addMeaning(subjects, 30, uopx_ignore);
    this.addMeaning(sections, 30, uopx_ignore);

    this.urls.push(link);

    saveOneTypeValue(this.job_tags, new Tag(TagType.title, title));
  }

  async learnTed(source: LearnSource) {
    const data = source.data ? source.data : await this.fetchUrl(source);
    
    const page = new soup(data);

    let jdesc;
    let paragraphs;
    let topics;
    let names = [];

    const script = page.findAll('script').filter(s => s && s.attrs.id === '__NEXT_DATA__');
    if (script && script.length) {
      const j = JSON.parse(script[0].text);
      jdesc = decode(j.props.pageProps.videoData.description);
      paragraphs = decode(j.props.pageProps.transcriptData.translation?.paragraphs.reduce((a,b) => a.concat(b.cues), []).map(c => c.text).join(' '));
      topics = decode(j.props.pageProps.videoData.topics.nodes.map(n => n.name));

      let speakers = j.props.pageProps.videoData.speakers.nodes;
      if (speakers && speakers.length) {
        for(const speaker of speakers) {
          let first = '';
          let middle = '';
          let last = '';
          if(speaker.firstname && speaker.firstname.length) first = speaker.firstname.toLowerCase();
          if(speaker.middlename && speaker.middlename.length) middle = speaker.middlename.toLowerCase();
          if(speaker.lastname && speaker.lastname.length) last = speaker.lastname.toLowerCase();
          if (speaker.whoTheyAre) speaker.whoTheyAre.toLowerCase().split(' ').forEach(n => names.push(n));

          const [d, n, p] = permuteName(`${first} ${middle} ${last}`.replace(/ {2}/, ' '))
          if (n && n.length) names = names.concat(n.map(x => x.toLowerCase()));
          else if(d && d.length) names.push(d.toLowerCase());
       }
      }
    }

    const meta = page.findAll('meta').filter(m => m && m.attrs);
    const title = decode(meta.find(m => m.attrs.name === 'title')?.attrs.content);
    const description = decode(meta.find(m => m.attrs.name === 'description')?.attrs.content);

    let link = meta.find(m => m.attrs.property === 'og:url')?.attrs.content;
    if (!link) link = source.url;

    const ted_ignore = ['ted', 'tedx', 'ted-x', 'ted-ed', 'ted+ed', 'ed', ...COURSE_IGNORE]

    if (link) {
      this.addMeaning(title, 50, [...ted_ignore, ...names]);
      this.addMeaning(topics, 30, [...ted_ignore, ...names]);
      this.addMeaning(description, 20, [...ted_ignore, ...names]);
      this.addMeaning(jdesc, 20, [...ted_ignore, ...names]);
      this.addMeaning(paragraphs, 10, [...ted_ignore, ...names]);

      this.urls.push(link);
      saveOneTypeValue(this.job_tags, new Tag(TagType.title, title));
    } else logging.warnFP(LOG_NAME, 'learnTed', this.user.profile, `No link for ${source.url}`);
  }

  async learnMasterClass(source: LearnSource) {
    const data = source.data ? source.data : await this.fetchUrl(source);

    const link = `https://www.masterclass.com${data.url}`;    

    const mc_ignore = ['masterclass', data.instructor, data.multi_instructor_name, ...COURSE_IGNORE]

    this.addMeaning(data.headline, 50, mc_ignore);
    this.addMeaning(data.category, 40, mc_ignore);
    this.addMeaning(data.skill, 30, mc_ignore);
    this.addMeaning(data.description, 20, mc_ignore);
    for (const level of Object.values(data.contents_skills)) {
      this.addMeaning(level['name_i18n'], 30, mc_ignore);
    }

    this.urls.push(link);
    saveOneTypeValue(this.job_tags, new Tag(TagType.title, data.class_name));
  }

  /**************
   * Twitter
   **************/
  async learnTwitter(source: LearnSource) {
    try {
      let data = config.isEnvOffline() ? source.data : null;

      const ignore = ['twitter'];

      let user = data && Array.isArray(data) ? null : data;
      let tweets:TweetV2[] = data && Array.isArray(data) ? data as any : null;

      let username = source.path;
      const slashi = source.path.indexOf('/');
      if (slashi > -1) username = username.slice(0, slashi);

      saveOne(this.person.names, username);

      if (!config.isEnvOffline()) {
        const twitter_token = config.get('TWITTER_BEARER_TOKEN'); 
        if (!twitter_token) return;

        const twitterClient = new TwitterApi(twitter_token);
        const roClient = twitterClient.readOnly;

        /* const twitter = new Twitter({
          consumer_key: config.get('TWITTER_CONSUMER_KEY'),
          consumer_secret: config.get('TWITTER_CONSUMER_SECRET'),
          access_token_key: config.get('TWITTER_ACCESS_TOKEN_KEY'),
          access_token_secret: config.get('TWITTER_ACCESS_TOKEN_SECRET'),
        }); */

        // const params = { screen_name: username, include_entities: true };

        // user = await new Promise<any>((c,r) => twitter.get('users/show', params, (error, response) => { if (error) r(error); c(response); }));
        const user_cache = url_cache.get(`https://twitter.com/${username}`);
        if (user_cache) user = user_cache;
        else {
          const user_data = await roClient.v2.userByUsername(username, {'user.fields': ['description', 'entities', 'profile_image_url']});
          if (user_data && user_data.data) {
            user = user_data.data;
            url_cache.set(`https://twitter.com/${username}`, user);
          }
        }

        if (user) {
          tweets = url_cache.get(`https://twitter.com/${username}/timeline`) as TweetV2[];
          if (!tweets) {
            const timeline = await roClient.v2.userTimeline(user.id, { exclude: 'retweets', 'tweet.fields': [ 'text', 'context_annotations', 'entities' ] });
            if(timeline) {
              tweets = timeline.tweets;
              if (tweets) url_cache.set(`https://twitter.com/${username}/timeline`, tweets);
            }
          }
        }
        // timeline = await new Promise<any[]>((c, r) => twitter.get('statuses/user_timeline', params, (error, response) => { if (error) r(error); c(response); }));
      }


      if (tweets) {
        for (const tweet of tweets) {
          // if (!tweet.is_quote_status && !tweet.retweeted_status) {
          // if (!user) user = tweet.user;
          const meaning = parsers.findMeaning(tweet.text).filter(m => !m.startsWith('@') && !m.startsWith('#') && !m.startsWith('http:') && !m.startsWith('https:'));
          this.addMeaning(meaning, 10, ignore);

          if (tweet.entities) {
            if (tweet.entities.hashtags) {
              for (const tag of tweet.entities.hashtags) {
                if (tag.tag) this.addMeaning(tag.tag, 30, ignore);
              }
            }

            if (tweet.entities.urls) {
              for (const url of tweet.entities.urls) {
                if (url.expanded_url) saveOne(this.urls, url.expanded_url);
              }
            }
          }

          if (tweet.context_annotations) {
            for (const ca of tweet.context_annotations.filter(c => c.entity)) {
              if (ca.entity.name) this.addMeaning(ca.entity.name, 30, ignore);
              if (ca.entity.description && ca.entity.description !== ca.entity.name) this.addMeaning(ca.entity.description, 20, ignore);
            }
          }
          // }
        }
      }

      if (user) {
        this.learnName(user.name);

        if (user.profile_image_url) saveOne(this.photos, user.profile_image_url);

        if (user.entities && user.entities.url && user.entities.url.urls) {
          for (const url of user.entities.url.urls) {
            if (url.expanded_url) {
              saveOne(this.urls, url.expanded_url);
              saveOne(this.person.urls, url.expanded_url);
            }
          }
        }

        if (user.description) {
          if (this.person.urls.includes(source.url)) {
            const has_meta = findTypeTag(this.person.tags, TagType.meta);
            if (!has_meta) saveOneTypeValue(this.person.tags, new Tag(TagType.meta, user.description, 10, this.now));
          }
          
          this.addMeaning(user.description, 20, ignore);
        }
      }
    } catch (err) {
      logging.warnFP(LOG_NAME, 'learnTwitter', this.user.profile, `${this.person ? this.person.id : ''} Issue learning from ${JSON.stringify(source)}`, err);
    }
  }

  /**************
   * AskFora
   **************/
  learnAskFora(source: LearnSource) { 
    // NOOP: don't loop
  }

  /**************
   * LinkedIn
   **************/
  async learnLinkedIn(source: LearnSource) { 
    if (source.url.includes('.com/learning')) {
      let data = source.data ? source.data : await this.fetchUrl(source);
      const course = await new soup(data);

      const title = course.find('title').text.split('|')[0].trim();
      const script = course.findAll('script').map(s => {
        try {
          return JSON.parse(s.text);
        } catch(e) {
          return null;
        }
      }).find(t => t && ['Course', 'VideoObject'].includes(t['@type']));

      const div = course.findAll('div');
      this.urls.push(script.url ? script.url : script.contentUrl);

      saveOneTypeValue(this.job_tags, new Tag(TagType.title, title));

      const linkedin_ignore = ['linkedin', 'learning', 'lynda', ...COURSE_IGNORE];

      this.addMeaning(title, 50, linkedin_ignore);
      this.addMeaning(div.filter(d => d.descendants.length === 1).map(d => d.text.replace(/(\d+h|\d+m|\d+s)/g, '').trim()).filter(d => d.length), 10, linkedin_ignore);

      this.addMeaning(script.name, 50, linkedin_ignore);
      this.addMeaning(script.description, 20, linkedin_ignore);
      if (script.hasPart) this.addMeaning(script.hasPart.map(part => `${part.map(p => p.name).join(' ')} ${part.map(p => p.description).join(' ')}`), 10, linkedin_ignore);
      if (script.syllabusSections) this.addMeaning(script.syllabusSections.map(s => s.description), 10, linkedin_ignore);
      if (script.transcript) this.addMeaning(script.transcript, 10);

      if (script.video) {
        this.addMeaning(script.video.name, 30, linkedin_ignore);
        this.addMeaning(script.video.description, 20, linkedin_ignore);

      }

    } else {
      this.learnPDL(source);
    }
    // logging.warnFP(LOG_NAME, 'learnLinkedIn', this.user.profile, `${this.person ? this.person.id : ''} Not implemented ${JSON.stringify(source)}`);
  }

  /**************
   * Crunchbase
   **************/
  async learnCrunchbase(source: LearnSource) {
    const paths = source.path.split('/');
    const upath = paths[paths.length - 1];
    const username = upath.replace('-', ' ');

    const params = {
      name: username,
      user_key: config.get('CRUNCHBASE_KEY'),
    };

    try {
      let data = config.isEnvOffline() ? source.data : null;

      if (!config.isEnvOffline()) {
        data = await this.fetchUrl({url:'https://api.crunchbase.com/v3.1/odm-people'}, { params });
      } 

      if (!data) {
        logging.infoFP(LOG_NAME, 'learnCrunchbase', this.user.profile, `${this.person ? this.person.id : ''} no data`);
        return;
      }

      let oindex = 0;
      const is = this.job_tags.length ?  findTypeIndexes(this.job_tags) : findTypeIndexes(this.person.tags);
      if (is.length) oindex = is[is.length - 1] + 1;

      for (const item of data.data.items) {
        if (item.properties.permalink === upath) {
          // item.properties.first_name;
          // item.properties.last_name;
          if (item.properties.profile_image_url) saveOne(this.photos, item.properties.profile_image_url);

          saveOneTypeValue(this.job_tags, new Tag(TagType.organization, item.properties.organization_name, oindex, new Date(item.properties.created_at * 1000)));

          saveOneTypeValue(this.job_tags, new Tag(TagType.jobTitle,  item.properties.title, oindex,  new Date(item.properties.created_at * 1000)));

          oindex++;

          if (item.properties.facebook_url) {
            saveOne(this.urls, item.properties.facebook_url);
            saveOne(this.person.urls, item.properties.facebook_url);
          }
          if (item.properties.linkedin_url) {
            saveOne(this.urls, item.properties.linkedin_url);
            saveOne(this.person.urls, item.properties.linkedin_url);
          }
          if (item.properties.twitter_url) {
            saveOne(this.urls, item.properties.twitter_url);
            saveOne(this.person.urls, item.properties.twitter_url);
          }

          break;
        }
      }
    } catch (err) {
      logging.warnFP(LOG_NAME, 'learnCrunchbase', this.user.profile, `${this.person ? this.person.id : ''}  Issue getting crunchbase data ${JSON.stringify(source)}`, err);
    }
  }

  /**************
   * Github
   **************/
  async learnGithub(source: LearnSource) {
    const ignore = ['github'];

    try {
      let username = source.path;
      const slashi = source.path.indexOf('/');
      if (slashi > -1) username = username.slice(0, slashi);

      const auth = {
        username: config.get('GITHUB_USER'),
        password: config.get('GITHUB_TOKEN'),
      };

      let data = config.isEnvOffline() ? source.data : null;

      if (!config.isEnvOffline()) {
        data = await this.fetchUrl({url: `https://api.github.com/users/${username}`}, { auth });
      }

      if (data) {
        // check org is known
        // const orgs = findTypeValues(person.tags, TagType.organization);
        // if (!orgs.includes(data.company)) return;
        this.learnName(data.name);
        if (data.company) {
          let index = this.job_tags.length ?  findTypeIndex(this.job_tags, JobTags, true) : findTypeIndex(this.person.tags, JobTags, true);
          if (!index) index = 0;
          else index++;
          saveOneTypeValue(this.job_tags, new Tag(TagType.organization, data.company, index, data.created_at ? new Date(data.created_at) : this.now));
        }
        if (data.url) {
          saveOne(this.urls, data.url);
          if (this.person.urls.includes(source.url)) saveOne(this.person.urls, data.url);
        }
        if (data.blog) {
          saveOne(this.urls, data.blog);
          if (this.person.urls.includes(source.url)) saveOne(this.person.urls, data.blog);
        }
        if (data.avatar_url && this.person.urls.includes(source.url)) saveOne(this.photos, data.avatar_url);

        if (data.bio) {
          const has_meta = findTypeTag(this.person.tags, TagType.meta);
          if (!has_meta) saveOneTypeValue(this.person.tags, new Tag(TagType.meta, data.bio, 10, this.now));
          this.addMeaning(data.bio, 30, ignore);
        }
      } else logging.infoFP(LOG_NAME, 'learnGithub', this.user.profile, `${this.person ? this.person.id : ''} no data`);

      if(!config.isEnvOffline()) {
        const gh = new Octokit({auth:config.get('GITHUB_TOKEN')} );
        const repos_res = await gh.repos.listForUser({ username });

        const repos = repos_res.data;
        for (const repo of repos) {
          this.addMeaning(repo.name, 20);
          // repos[index].homepage

          if (repo.description) this.addMeaning(repo.description, 20);

          if (repo.language) this.addMeaning(repo.language, 10);
          else {
            try {
              const lang_res = await this.fetchUrl({url:repo.languages_url}, { auth });
              if (lang_res) this.addMeaning(Object.keys(lang_res), 10);
            } catch (err) {
              logging.warnFP(LOG_NAME, 'learnGithub', this.user.profile, `${this.person ? this.person.id : ''} Issue fetching github languages ${repo.languages_url}`, err);
            }
          }

          try {
            const comments_res = await this.fetchUrl({url: repo.comments_url.split('{')[0]}, { headers: { Accept: 'application/vnd.github-commitcomment.text+json' }, auth });
            if (comments_res) this.addMeaning(comments_res.body_text, 10);
          } catch (err) {
            logging.warnFP(LOG_NAME, 'learnGithub', this.user.profile, `${this.person ? this.person.id: ''} Issue fetching github comments ${repo.comments_url.split('{')[0]}`, err);
          }

          if (repo.has_issues) {
            try {
              const issue_res = await this.fetchUrl({url: repo.issue_comment_url.split('{')[0]}, { headers: { Accept: 'application/vnd.github-commitcomment.text+json' }, auth });
              if (issue_res) this.addMeaning(issue_res.body_text, 10);
            } catch (err) {
              logging.warnFP(LOG_NAME, 'learnGithub', this.user.profile, `${this.person ? this.person.id : ''} Issue fetching github issue comments ${repo.issue_comment_url.split('{')[0]}`, err);
            }
          }
        }
      }
    } catch (err) {
      logging.warnFP(LOG_NAME, 'learnGithub', this.user.profile, `${this.person ? this.person.id : ''} Issue learning from ${JSON.stringify(source)}`, err);
    }
  }

  /**************
   * Blogger
   **************/
  async learnBlogger(source: LearnSource) {
    return this.learnDefault(source, ['p', 'a'], 20);
  }

  /**************
   * Facebook
   **************/
  async learnFacebook(source: LearnSource) {
    return this.learnDefault(source, ['span'], 20);
  }

  /**************
   * Slideshare
   **************/
  async learnSlideshare(source: LearnSource) {
    let username = source.path;
    const slashi = source.path.indexOf('/');
    if (slashi > -1) username = username.slice(0, slashi);

    source.url = `https://www.slideshare.net/${username}`;
    await this.learnDefault(source, ['span', 'a', 'li'], 20);

    try {
      let data = config.isEnvOffline() ? source.data : null;
      if (!config.isEnvOffline()) data = await this.fetchUrl({url:`${source.url}/presentations`});
  
      if (data) {
        const s = new soup(data);

        const IGNORE_SS_PATHS = ['presentations', 'followers', 'clipboards', 'documents', 'videos', 'infographics'];
        const links = s
          .findAll('a')
          .map(d => d.attrs['href'])
          .filter(d => d.indexOf(`/${username}/`) > -1)
          .filter(d => !IGNORE_SS_PATHS.includes(d));

        for (const link of links) {
          const url = `https://www.slideshare.net${link}`;
          if (!this.urls.includes(url)) this.urls.push(url);
        }
      }
    } catch (err) {
      logging.warnFP(LOG_NAME, 'learnSlideshare', this.user.profile, `${this.person ? this.person.id : ''} Issue getting slideshare presentations ${source.url}/presentations`, err);
    }
  }

  /**************
   * Flickr
   **************/
  async learnFlickr(source: LearnSource) {
    // NOOP, not much here, need to validate breadth of work
    // addMeaning(meanings, ['photography', 'photographer', 'photos', 'pictures'], 10);
  }

  /**************
   * About.me
   **************/
  async learnAbout(source: LearnSource) {
    return this.learnDefault(source, ['p'], 20);
  }

  /**************
   * AngelList
   **************/
  async learnAngelList(source: LearnSource) {
    try {
      const ignore = ['angel', 'angel.co'];

      let data = config.isEnvOffline() ? source.data : null;
      if (!config.isEnvOffline()) {
        // remove numerics from host for headers
        // const host = source.host.replace(/^www\d/, 'www');

        data = await this.fetchUrl(source);
      }

      if (data) {
        const s = new soup(data);
        const og = this.learnOG(s);

        if (og.image && og.image.length) saveOne(this.photos, og.image);
        if (og.url && og.url.length) {
          saveOne(this.urls, og.url);
          saveOne(this.person.urls, og.url);
        }

        this.learnName(og.title);
          
        if (og.desc && og.desc.length) {
          if (this.person.urls.includes(source.url)) {
            const has_meta = findTypeTag(this.person.tags, TagType.meta);
            if (!has_meta) saveOneTypeValue(this.person.tags, new Tag(TagType.meta, og.desc, 10, this.now));
          }
          this.addMeaning(og.desc, 20, ignore);
        }

        this.learnTags(s, ['div'], 30, ignore);
        this.learnTags(s, ['a', 'h1', 'h2', 'h3', 'strong'], 20, ignore);
        this.learnTags(s, ['span', 'p'], 10, ignore);
      }
    } catch(err) {
      logging.warnFP(LOG_NAME, 'learnAngel', this.user.profile, `${this.person ? this.person.id : ''} Issue getting angel list profile ${JSON.stringify(source)}`, err);
    }

  }

  /**************
   * Medium
   **************/
  async learnMedium(source: LearnSource) {
    const ignore = ['medium'];
    try {
      let article = false;
      let data = config.isEnvOffline() ? source.data : null;
      const medium_url = new URL(source.url);
      if (medium_url.host.startsWith('www.') || medium_url.host.startsWith('medium.') || source.path.length === 0 || source.path == '/') {
        let username = source.path;
        const slashi = username.indexOf('/');
        if (slashi > -1) username = username.slice(0, slashi);
        if (!config.isEnvOffline()) { 
          data = await this.fetchUrl({url: username && username.length > 1 ? `https://medium.com/${username}` : source.url});
        }
      } else {
        article = true;
        if (!config.isEnvOffline()) data = await this.fetchUrl({url: source.url});
      }

      if (data) {
        const s = new soup(data);
        this.learnProfile(s);

        const og = this.learnOG(s);
        if (article) {
          if (og.title && og.title.length) this.addMeaning(og.title, 50, ignore);
          if (og.desc && og.desc.length) this.addMeaning(og.desc, 20, ignore);
          const s = new soup(data);
          this.learnTags(s, ['div'], 30, ignore);
          this.learnTags(s, ['a', 'h1', 'h2', 'h3', 'strong'], 20, ignore);
          this.learnTags(s, ['span', 'p'], 10, ignore);
        } else {
          const graph = s.findAll('script').find(s => s.text.startsWith('window.__APOLLO_STATE__'));
          if (graph) {
            const j = JSON.parse(graph.text.slice(graph.text.indexOf('{')));
            const root = j['ROOT_QUERY'];
            if (root) {
              const user_key = Object.keys(root).find(k => k.startsWith('user({"id":"'));
              if (user_key) {
                const user = j[root[user_key].__ref];
                if (user) {
                  this.learnName(user.name);
                  if (user.bio) {
                    if (this.person.urls.includes(source.url)) {
                      if (!this.person.bio || !this.person.bio.length) this.person.bio = user.bio;
                      else {
                        const has_meta = findTypeTag(this.person.tags, TagType.meta);
                        if (!has_meta) saveOneTypeValue(this.person.tags, new Tag(TagType.meta, user.bio, 10, this.now));
                      }
                    }
                    this.addMeaning(user.bio, 30, ignore);
                  }
                  if (user.username) saveOne(this.person.names, user.username);
                }
              }
            }
          }

          if (og.image && og.image.length) saveOne(this.photos, og.image);

          if (og.url && og.url.length) {
            saveOne(this.urls, og.url);
            saveOne(this.person.urls, og.url);
          }

          if (og.title && og.title.length) {
            const name = parsers.findNames(og.title).filter(n => n !== 'Medium').join(' ');
            this.learnName(name);
          } 

          if (og.desc && og.desc.length) {
            if (this.person.urls.includes(source.url)) {
              if (!this.person.bio || !this.person.bio.length) this.person.bio = og.desc
              else {
                const has_meta = findTypeTag(this.person.tags, TagType.meta);
                if (!has_meta) saveOneTypeValue(this.person.tags, new Tag(TagType.meta, og.desc, 10, this.now));
              }
            }
            this.addMeaning(og.desc, 20, ignore);
          }
        
          this.learnTags(s, ['div'], 30, ignore);
          this.learnTags(s, ['a', 'h1', 'h2', 'h3', 'strong'], 20, ignore);
          this.learnTags(s, ['span', 'p'], 10, ignore);
        }

        const host = s.findAll('link').filter(l => l.attrs['rel'] === 'canonical').map(l => l.attrs['href'])[0];
        const links = _.uniq(s.findAll('a').map(d => d.attrs['href']).filter(d => d.length > 1 && d.startsWith('/') && !d.startsWith('/?')).map(l => l.split('?')[0]));
        // const links = s .findAll('a') .map(d => d.attrs['href']) .filter(d => d.slice(0, 2) === '/p/');
          
        for (const link of links) {
          const url = `${host}${link}`;
          saveOne(this.urls, url);
        }
      }
    } catch (err) {
      logging.warnFP(LOG_NAME, 'learnMedium', this.user.profile, `${this.person ? this.person.id : ''} Issue getting medium profile ${JSON.stringify(source)}`, err);
    }
  }

  /**************
   * Pinterest
   **************/
  async learnPinterest(source) {
    if (source.path.startsWith('pin/')) {
      // TODO: https://developers.pinterest.com/docs/sdks/js/
    } else return this.learnPDL(source);
  }

  /**************
   * Reddit
   **************/
  async learnReddit(source) {
    const username = source.path.match(/(?:\/)([^/]*)(?:\/)?/)[1]; // .replace(/\//g, '');

    try {
      let data = config.isEnvOffline() ? source.data : null;
      if (!config.isEnvOffline()) {
        data = await this.fetchUrl({ url: `https://api.reddit.com/user/${username}/about` });
        let res = await this.fetchUrl({ url: `https://api.reddit.com/user/${username}/overview` });
        if (!data) data = {data:{}};
        data.data['children'] = res.children;
      }

      if (data && data.data) {
        if (data.data.icon_img) saveOne(this.photos, data.data.icon_img);
        this.learnName(data.data.name);
        if (data.data.subreddit) {
          this.learnName(data.data.subreddit.title);
          if (data.data.subreddit.display_name_prefixed) {
            if (data.data.subreddit.display_name_prefixed.startsWith('u/')) this.learnName(data.data.subreddit.display_name_prefixed.slice(2));
            else this.learnName(data.data.subreddit.display_name_prefixed);
          }
          if (data.data.subreddit.public_description) this.addMeaning(data.data.subreddit.public_description, 20);
          if (data.data.subreddit.url) {
            const url = `https://reddit.com${data.data.subreddit.url}`
            saveOne(this.urls, url);
            saveOne(this.person.urls, url);
          }
        }

        if(data.data.children) {
          for (const c of data.data.children) {
            if (c.data.link_title) this.addMeaning(c.data.link_title, 50);
            if (c.data.subreddit) this.addMeaning(c.data.subreddit, 30);
            if (c.data.body) this.addMeaning(c.data.body, 10);
            if (c.data.selftext) this.addMeaning(c.data.selftext, 10);
            if (c.data.title) this.addMeaning(c.data.title, 50);
          }
        }
      }
    } catch (err) {
      if (err.reponse && err.response.status === 404) logging.warnFP(LOG_NAME, 'learnReddit', this.user.profile, `${this.person ? this.person.id : ''} reddit comments not found ${JSON.stringify(source)}`);
      else logging.warnFP(LOG_NAME, 'learnReddit', this.user.profile, `${this.person ? this.person.id : ''} Issue getting reddit comments ${JSON.stringify(source)}`, err);
    }
  }

  /**************
   * Youtube
   **************/
  async learnYoutube(source) {
    // TODO
    // return learnDefault(profile, meanings, person, source);
  }

  /**************
   * Google Plus
   **************/
  async learnGoogle(source) {
    // TODO
    return this.learnDefault(source);
  }

  /**************
   * Quora
   **************/
  async learnQuora(source) {
    return this.learnDefault(source, ['span'], 20);
  }

  /**************
   * Credly
   **************/
  async learnCredly(source) {
    if (logging.isDebug(this.user.profile)) DEBUG('learnCredly %s::%s::URL = %s', this.user.profile, this.person.id, source.url);
    try {
    // remove numerics from host for headers
      // const host = source.host.replace(/^www\d/, 'www');
      let data = config.isEnvOffline() ? source.data : null;
      if (!data && !config.isEnvOffline()) {
        // remove numerics from host for headers
        // const host = source.host.replace(/^www\d/, 'www');

        const config: AxiosRequestConfig = {
          method: 'get',
          url: source.url,
          headers: {
            host: source.host, // `${host}`,
            accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9',
            'accept-encoding': 'gzip, deflate, sdch',
            'accept-language': 'en-US,en;q=0.9',
            'cache-control': 'max-age=0',
            dnt: 1,
            connection: 'keep-alive', 
            'upgrade-insecure-requests': 1,
            'user-agent': 'Mozilla/5.0 (X11; CrOS x86_64 14588.123.0) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/101.0.4951.72 Safari/537.36',
          },
        };


            // Accept: '*/*',
            // 'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1',
            // 'User-Agent': 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
            // 'User-Agent': 'Mozilla/5.0 (Mobile; rv:15.0) Gecko/15.0 Firefox/15.0',

        const res = await axios(config);
        data = res.data;
      }

      if (data) {
        const s = new soup(data);

        const h = s.findAll('h1').find(x => x.attrs.class && x.attrs.class.includes('ac-heading--badge-name-hero')).text;
        const st = s.findAll('div').filter(x => x.attrs.class && x.attrs.class.includes('c-profile-header__subtitle')).map(x => x.text);
        const dt = s.findAll('div').find(x => x.attrs.class && x.attrs.class.includes('c-profile-header__description'));
        const a = s.findAll('a').map(x => x.attrs.href).filter(x => x && x.startsWith('http') && !x.includes('credly.com'));
        const b = s.findAll('div').filter(x => x.attrs.class && x.attrs.class.includes('c-badge')).map(x => x.text);
        const i = s.findAll('img').find(x => x.attrs.class && x.attrs.class.includes('cr-avatar__image'));

        const [d,n,p] = permuteName(h);
        saveOne(this.person.names, d);
        for (const x of n) saveOne(this.person.names, x);

        if (i && i.src) saveOne(this.photos, i.src);

        if (st.length) {
          const title_job = st[0].split('|');
          this.addMeaning(st[0], 50);

          let oindex = 0;
          const is = this.job_tags.length ? findTypeIndexes(this.job_tags) : findTypeIndexes(this.person.tags);
          if (is.length) oindex = is[is.length - 1] + 1;

          saveOneTypeValue(this.job_tags, new Tag(TagType.jobTitle, title_job[0], oindex, this.now));

          oindex++;

          if (title_job.length > 1) {
            saveOneTypeValue(this.job_tags, new Tag(TagType.organization, title_job[1], oindex, this.now));
          }

          if (st.length > 1) {
            this.addMeaning(st[1], 50);
          }
        }

        for (const u of a) saveOne(this.urls, u);

        if (dt && dt.text.length) {
          this.addMeaning(dt.text, 10);
          // look for urls
        }

        if (b.length) this.addMeaning(b, 50);
      }
    } catch (err) {
      logging.warnFP(LOG_NAME, 'learnCredly', this.user.profile, `${this.person ? this.person.id : ''} Issue learning ${JSON.stringify(source)}`, err);
    }
  }

  /**************
   * People Data Labs
   **************/
  async savePDL(source, pdl_id, data) {
    const savePDLURL = (url, to_person = false) => {
      if (url && url.length) {
        const proto_url = `https://${url}`;
        saveOne(this.urls, proto_url);
        if (to_person) {
          saveOne(this.person.urls, proto_url);
          url_cache.set(`PDL:${proto_url}`, data);
        }
        return true;
      }
      return false;
    }
    
    let pdl_comms = [];
    if (data.personal_emails) {
      const emails = parsers.findEmail(data.personal_emails);
      if (emails) pdl_comms = pdl_comms.concat(emails);
    }
    if (data.work_email) {
      const emails = parsers.findEmail(data.work_email);
      if (emails) pdl_comms = pdl_comms.concat(emails);
    }
    if (data.mobile_phone) {
      const phone = parsers.findPhone(data.mobile_phone);
      if (phone) pdl_comms = pdl_comms.concat(phone);
    }
    if (data.phone_numbers) {
      const phone = parsers.findPhone(data.phone_numbers);
      if (phone) pdl_comms = pdl_comms.concat(phone);
    }
    if (data.emails) {
      const emails = parsers.findEmail(data.emails.map(e => e.address).filter(e => e));
      if (emails) pdl_comms = pdl_comms.concat(emails);
    }

    for(const comm of pdl_comms) {
      const email = crypto.createHash('sha256').update(comm.toLowerCase()).digest('hex')
      url_cache.set(`PDL:${email}`, data);
    }

    if (this.person.self) {
      if (!this.person.comms || !this.person.comms.length) return;

      if (!arraysIntersect(this.person.comms, pdl_comms)) {
        logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} PDL_ID ${data.id} Comms ${JSON.stringify(pdl_comms)} don't match ${JSON.stringify(source)}`);
        return;
      }
    }

    if (data.id) {
      logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} PDL_ID ${data.id}`);
      const profile_count = data.profiles ? data.profiles.length : 1;
      if (pdl_id) {
        pdl_id.value = data.id;
        pdl_id.start = this.now;
        pdl_id.bias = pdl_id.bias > profile_count ? pdl_id.bias : profile_count;
      } else {
        const new_id = new Tag(TagType.PDL_ID, data.id, 0, new Date(), profile_count);
        if (saveOneTypeValue(this.person.tags, new_id)) pdl_id = new_id;
      }

      url_cache.set(pdl_id, data);
    } else logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} No PDL ID `);

    const emails = parsers.findEmail(this.person.comms);
    if (!emails || !emails.length) {
      const pdl_emails = parsers.findEmail(pdl_comms);
      if (pdl_emails) {
        for (const email of pdl_emails) saveOne(this.person.comms, email);
      }
    }

    this.learnName(data.full_name); 

    if (data.first_name && data.first_name.length) saveOne(this.person.names, data.first_name);
    if (data.middle_name && data.middle_name.length) saveOne(this.person.names, data.middle_name);
    if (data.last_name && data.last_name.length) saveOne(this.person.names, data.last_name);

    savePDLURL(data.linkedin_url, true);
    savePDLURL(data.facebook_url, true);
    savePDLURL(data.twitter_url, true);
    savePDLURL(data.github_url, true);const result = await pdl_client.person.retrieve({id: pdl_id.value});

    if (data.profiles && data.profiles.length) {
      for (const social of data.profiles) {
        savePDLURL(social.url);
        if (social.username) saveOne(this.person.names, social.username);
      }
    } else {
      logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} PDL_ID ${data.id} has no profiles`);
    }

    let index = this.job_tags.length ? findTypeIndex(this.job_tags, JobTags, true) : findTypeIndex(this.person.tags, JobTags, true);
    if (!index) index = 0;
    else index++;

    if (data.photo && data.photo.length) this.photos.push(data.photo); 

    const job_start = data.job_start_date ? MINUTES(HOURS(new Date(data.job_start_date), 12), this.user.offset) : data.job_last_updated ? MINUTES(HOURS(new Date(data.job_last_updated), 12), this.user.offset) : this.now;
    const job_end = data.job_end_date ? MINUTES(HOURS(new Date(data.job_end_date), 12), this.user.offset) : null;
    if (data.industry && data.industry.length) {
      this.addMeaning(data.industry, 20);
      saveOneTypeValue(this.job_tags, new Tag(TagType.industry, data.industry, 200, job_start, 100));
    }
    if (data.job_title && data.job_title.length) {
      this.addMeaning(data.job_title, 50);
      saveOneTypeValue(this.job_tags, new Tag(TagType.jobTitle, data.job_title, index, job_start));
    }
    if (data.job_title_role && data.job_title_role.length) {
      this.addMeaning(data.job_title_role, 40);
      saveOneTypeValue(this.job_tags, new Tag(TagType.department, data.job_title_role, index, job_start));
    }
    if (data.job_title_sub_role && data.job_title_sub_role.length) {
      this.addMeaning(data.job_title_sub_role, 30);
      saveOneTypeValue(this.job_tags, new Tag(TagType.jobDescription, data.job_title_sub_role, index, job_start));
    }
    if (data.job_title_levels) {
      for (const level of data.job_title_levels) {
        this.addMeaning(level, 30);
        // saveOneTypeValue(this.job_tags, new Tag( TagType.skill,  level.toLowerCase(), 200, job_start,  100 ));
      }
    }
    if (data.job_company_name && data.job_company_name.length) {
      saveOneTypeValue(this.job_tags, new Tag(TagType.organization, data.job_company_name, index, job_start));
      saveOneTypeValue(this.job_tags, new Tag(TagType.range, data.job_company_name, index, job_end));
      this.addMeaning(data.job_company_name, 20);
      // saveOneTypeValue(this.job_tags, new Tag( TagType.skill, data.job_company_name.toLowerCase(), 200, job_start,  100 ));
      url_cache.set(`PDL:${data.full_name}:${data.job_company_name}`, data);
    }

    if (data.company_industry && data.company_industry.length) {
      this.addMeaning(data.company_industry, 20);
      saveOneTypeValue(this.job_tags, new Tag(TagType.industry, data.industry, 200, job_start, 100));
      // saveOneTypeValue(this.job_tags, new Tag(TagType.skill, data.company_industry.toLowerCase(), 200, job_start, 100));
    }

    let saved_url = savePDLURL(data.job_company_website, true);
    savePDLURL(data.job_company_linkedin_url);
    savePDLURL(data.job_company_facebook_url);
    savePDLURL(data.job_company_twitter_url);

    if (data.interests) for (const interest of data.interests) saveOneTypeValue(this.job_tags, new Tag( TagType.skill, interest.toLowerCase(), 200, this.now,  100 ));
    if (data.skills) for (const skill of data.skills) saveOneTypeValue(this.job_tags, new Tag( TagType.skill, skill.toLowerCase(), 200, this.now,  100 ));

    if (data.experience) {
      for (const exp of data.experience) {
        index++;
        const exp_date = exp.start_date ? MINUTES(HOURS(new Date(exp.start_date), 12), this.user.offset) : this.now;
        const end_date = exp.end_date ? MINUTES(HOURS(new Date(exp.end_date), 12), this.user.offset) : null;
        if (exp.company) {
          if (exp.company.industry && exp.company.industry.length) {
            this.addMeaning(data.company_industry, 20);
            saveOneTypeValue(this.job_tags, new Tag(TagType.industry, exp.company.industry, index, exp_date, 100));
            // saveOneTypeValue(this.job_tags, new Tag(TagType.skill, exp.company.industry.toLowerCase(), 200, exp_date, 100));
          }
          if (exp.company.name && exp.company.name.length) {
            saveOneTypeValue(this.job_tags, new Tag(TagType.organization, exp.company.name, index, exp_date));
            if (end_date) saveOneTypeValue(this.job_tags, new Tag(TagType.range, exp.company.name, index, end_date));
            // saveOneTypeValue(this.job_tags, new Tag(TagType.skill, exp.company.name.toLowerCase(), 200, exp_date,  100 ));
            this.addMeaning(exp.company.name, 20);
            url_cache.set(`PDL:${data.full_name}:${exp.company.name}`, data);
          }

          saved_url = savePDLURL(exp.company.website, !saved_url);
          savePDLURL(exp.company.linkedin_url);
          savePDLURL(exp.company.facebook_url);
          savePDLURL(exp.company.twitter_url);
        }

        if (exp.title) {
          if (exp.title && exp.title.length) {
            this.addMeaning(exp.title, 50);
            saveOneTypeValue(this.job_tags, new Tag(TagType.jobTitle, exp.title, index, exp_date));
          }
          if (exp.title_role && exp.title_role.length) {
            this.addMeaning(exp.title_role, 40);
            saveOneTypeValue(this.job_tags, new Tag(TagType.department, exp.title_role, index, exp_date));
          }
          if (exp.title_sub_role && exp.title_sub_role.length) {
            this.addMeaning(exp.title_sub_role, 30);
            saveOneTypeValue(this.job_tags, new Tag(TagType.jobDescription, exp.title_sub_role, index, exp_date));
          }
          if (exp.title_levels) {
            for (const level of exp.title_levels) {
              this.addMeaning(level, 30);
              // saveOneTypeValue(this.person.tags, new Tag( TagType.skill, level.toLowerCase(), 200, exp_date,  100 ));
            }
          }

          if (exp.title.name && exp.title.name.length) {
            this.addMeaning(exp.title.nae, 50);
            saveOneTypeValue(this.job_tags, new Tag(TagType.jobTitle, exp.title.name, index, exp_date));
          }
          if (exp.title.role && exp.title.role.length) {
            this.addMeaning(exp.title.role, 40);
            saveOneTypeValue(this.job_tags, new Tag(TagType.jobDescription, exp.title.role, index, exp_date));
          }
          if (exp.title.sub_role && exp.title.sub_role.length) {
            this.addMeaning(exp.title.sub_role, 30);
            saveOneTypeValue(this.job_tags, new Tag(TagType.jobDescription, exp.title.sub_role, index, exp_date));
          }
          if (exp.title.levels) {
            for (const level of exp.title.levels) {
              this.addMeaning(level, 30);
              // saveOneTypeValue(this.person.tags, new Tag( TagType.skill, level.toLowerCase(), 200, exp_date,  100 ));
            }
          }
        }
      }
    }

    if (data.education) {
      for (const edu of data.education) {
        index++;
        const edu_date = edu.start_date ? MINUTES(HOURS(new Date(edu.start_date), 12), this.user.offset) : this.now;
        const end_date = edu.end_date ? MINUTES(HOURS(new Date(edu.end_date), 12), this.user.offset) : this.now;
        if (edu.school) {
          if (edu.school.name && edu.school.name.length) {
            saveOneTypeValue(this.job_tags, new Tag(TagType.school, edu.school.name, index, edu_date));
            saveOneTypeValue(this.person.tags, new Tag(TagType.skill, edu.school.name.toLowerCase(), 200, edu_date,  100 ));
            if (end_date) saveOneTypeValue(this.job_tags, new Tag(TagType.range, edu.school.name, index, end_date));
            url_cache.set(`PDL:${data.full_name}:${edu.school.name}`, data);
          }

          if (edu.school.type) saveOneTypeValue(this.person.tags, new Tag(TagType.skill, edu.school.type.toLowerCase(), 200, edu_date,  100 ));

          saved_url = savePDLURL(edu.school.website, !saved_url);
          savePDLURL(edu.school.linkedin_url);
          savePDLURL(edu.school.facebook_url);
          savePDLURL(edu.school.twitter_url);
        }

        for (const degree of edu.degrees) {
          this.addMeaning(degree, 50);
          // saveOneTypeValue(this.person.tags, new Tag(TagType.skill, degree.toLowerCase(), 200, edu_date,  100 ));
        }

        for (const major of edu.majors) {
          this.addMeaning(major, 50);
          // saveOneTypeValue(this.person.tags, new Tag(TagType.skill, major.toLowerCase(), 200, edu_date,  100 ));
        }

        for (const minor of edu.minors) {
          this.addMeaning(minor, 40);
          // saveOneTypeValue(this.person.tags, new Tag(TagType.skill, minor.toLowerCase(), 200, edu_date,  100 ));
        }
      }
    }

    /* 
      result.data.location_names []
      result.data.regions []
      result.data.countries []
      result.data.job_company_location_name
      result.data.job_company_location_locality
      result.data.job_company_location_metro
      result.data.job_company_location_region
      result.data.job_company_location_country
      result.data.job_company_location_continent
      result.data.location_name
      result.data.location_locality
      result.data.location_metro
      result.data.location_region
      result.data.location_country
      result.data.location_continent
      */

      return pdl_id;
  }

  async learnPDL(source) {
    /* for now only learn self
    if (!this.person.self) {
      logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `Skipping ${this.person ? this.person.id : ''} not self`);
      return;
    }*/

    if (source.url && source.url.includes('linkedin.com/learning')) return this.learnDefault(source);

    if (pdl_client || config.isEnvOffline()) {
      let pdl_id = this.person.tags ? findTypeTag(this.person.tags, TagType.PDL_ID) : null;
      try {
        // check for all know urls
        let data: any[] = config.isEnvOffline() && source.data ? [source.data] : null;
        const request_set = [];
        if (!config.isEnvOffline()) {
          if (!data && pdl_id) data = url_cache.get(pdl_id.value) as any[];

          if (!data && pdl_id && pdl_id.value && pdl_id.bias) {
            if (!pdl_id.start || DAYS(new Date(pdl_id.start), 30) < this.now) {
              logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} Retrieving PDL ${pdl_id.value}`);
              try {
                const result = await pdl_client.person.retrieve({id: pdl_id.value});
                if (result && result.status == 200) {
                  data = [result.data];
                  url_cache.set(pdl_id.value, data);
                }
              } catch(e) {
                logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `Error retrieiving ${pdl_id.value}`, e);
              }
            } else {
              logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} Skipping PDL ${pdl_id.value} retrieved at ${pdl_id.start}`);
              data = [];
            }
          } else if(pdl_id) {
            logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} Re-fetching PDL ${JSON.stringify(pdl_id)}`);
          }

          if (!data) {
            const profile_urls = source.url ? _.uniq([source.url].concat(this.person.urls.filter(u => {
              try {
                if (u) {
                  const url = new URL(u);
                  const domain = url.hostname.split('.').slice(-2).join('.').toLowerCase();
                  return PDL_SUPPORT.includes(domain);
                }
                return false;
              } catch(e) {
                logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `Not matching invalid url ${u}`);
              }
            }).filter(u => u))) : null;

            const emails = this.person.comms ? parsers.findEmail(this.person.comms): null
            const phone = this.person.comms ? parsers.findPhone(this.person.comms): null
            const names = this.person.names ? this.person.names.filter(n => !n.includes(',') && n.split(' ').length >= 2 && n.split(' ').slice(-1)[0].length > 1).filter(n => n.length > 4) : 
              this.person.displayName && this.person.displayName.length > 4 ? [this.person.displayName] : [];

            let atts = [];
            if (profile_urls && profile_urls.length) atts.push('urls');
            if (emails && emails.length) atts.push('emails');
            if (phone && phone.length) atts.push('phones');
            if (names && names.length) atts.push('names');

            while(atts.length) {
              logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `Querying for ${this.person ? this.person.id : ''} with attributes ${atts}`);
              if (atts.includes('urls')) {
                atts = atts.filter(a => a !== 'urls');
                logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `Querying for ${this.person ? this.person.id : ''}: ${JSON.stringify(profile_urls)}`);
                for (const profile of profile_urls) {
                  request_set.push({
                    params: {
                      profile,
                      min_likelihood: 6,
                    }
                  });
                }
              } else if(atts.includes('emails')) {
                atts = atts.filter(a => a !== 'emails');
                request_set.push({
                  params: {
                    email_hash: emails.map(email => crypto.createHash('sha256').update(email.toLowerCase()).digest('hex')),
                    data_include: 'profiles',
                    required: 'profiles',
                    min_likelihood: 6,
                  }
                });
              } else if(atts.includes('phones')) {
                atts = atts.filter(a => a !== 'phones');
                request_set.push({
                  params: {
                    phone,
                    data_include: 'profiles',
                    required: 'profiles',
                    min_likelihood: 6,
                  }
                });
              } else if(atts.includes('names')) {
                atts = atts.filter(a => a !== 'names');
                let org_tags = findTypeValues([...this.person.tags, ...this.job_tags], TagType.organization).filter(o => !parsers.ignore(o.toLowerCase()));
                let school_tags = findTypeValues([...this.person.tags, ...this.job_tags], TagType.school).filter(o => !parsers.ignore(o.toLowerCase()));

                if (names && names.length) {
                  for (const company of org_tags) {
                    for (const name of names) {
                      request_set.push({
                        params: {
                          name,
                          company,
                          min_likelihood: 6,
                          data_include: 'profiles',
                          required: 'profiles',
                        }
                      });
                    }
                  }

                  for (const school of school_tags) {
                    for (const name of names) {
                      request_set.push({
                        params: {
                          name,
                          school,
                          min_likelihood: 6,
                          data_include: 'profiles',
                          required: 'profiles',
                        }
                      });
                    }
                  }
                }
              
                if (request_set.length) {
                  logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `Querying for ${this.person ? this.person.id : ''} by names: ${JSON.stringify(names)} orgs: ${JSON.stringify(org_tags)} schools: ${JSON.stringify(school_tags)}`);
                }
              } else break;
            
              if (request_set.length) {
                logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `${request_set.length} requests for ${this.person ? this.person.id : ''}`);
                let result;
                let wait = 500;
                while(!result && (!data || !data.length)) {
                  try {
                    for (const req of request_set) {
                      if (req.params.profile) {
                        data = url_cache.get(`PDL:${req.params.profile}`) as any[];
                        if (data && data.length) break;
                      }

                      if(req.params.email_hash) {
                        data = url_cache.get(`PDL:${req.params.email_hash}`) as any[];
                        if (data && data.length) break;
                      }

                      if(req.params.phone) {
                        data = url_cache.get(`PDL:${req.params.phone}`) as any[];
                        if (data && data.length) break;
                      }

                      if (req.params.name) {
                        if (req.params.company) {
                          data = url_cache.get(`PDL:${req.params.name}:${req.params.company}`) as any[];
                          if (data && data.length) break;
                        }

                        if (req.params.school) {
                          data = url_cache.get(`PDL:${req.params.name}:${req.params.school}`) as any[];
                          if (data && data.length) break;
                        }
                      }
                    }

                    if (!data || !data.length) {
                      while(request_set.length) {
                        const requests = request_set.splice(0,100);
                        const batch_result = await pdl_client.person.bulk.enrichment({requests});
                        if (batch_result && batch_result.items) {
                          if (result && result.items) result.items = [...result.items, batch_result.items];
                          else result = batch_result;
                        }
                      }
                      if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'learnPDL', this.user.profile, `Received ${JSON.stringify(result)} from PDL request ${JSON.stringify(request_set)}`);
                    }
                  } catch(e) {
                    if (e.message && e.message.includes('too quick') && wait < 512000) {
                      await setTimeout(wait);
                      wait *= 2;
                    }
                    else {
                      logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `Error fetching ${request_set.length} requests`, e);
                      break;
                    }
                  }
                }

                if (result && result.items) {
                  data = result.items.filter(item => item.status === 200).map(item => item.data);
                  // if (data && data.length) break;
                }

                request_set.splice(0);
              }

              if (data && data.length) break;
            }
          }
        }

        if (data && data.length) {
          logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} Learning ${data.length} results for from ${JSON.stringify(source)} ${JSON.stringify(request_set)}`);
          for (const d of data.sort((a,b) => {
            if (a.profiles && a.profiles.length) {
              if(b.profiles && b.profiles.length) {
                return a.profiles.length - b.profiles.length;
              } else return 1;
            } else if(b.profiles && b.profiles.length) return -1;
            return 1;
          })) {
            pdl_id = this.savePDL(source, pdl_id, d);
          }
        } else logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} No data from ${JSON.stringify(source)}`);
      } catch(err) {
        logging.warnFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} Issue learning ${JSON.stringify(source)}`, err);
      }
    } else logging.infoFP(LOG_NAME, 'learnPDL', this.user.profile, `${this.person ? this.person.id : ''} No client or key`);
  }

  /**************
   * Pinterest
   **************/
  async learnWikipedia(source) {
    return this.learnDefault(source);
  }

  /**************
   * Parse and find handler
   **************/
  async learnUrl(url: string, data?: any, meta = false) {
    let source: LearnSource;

    try {
      if (url.endsWith('.pdf') || url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif') || url.endsWith('.svg')) return;
      if (logging.isDebug(this.user.profile)) DEBUG('learnUrl %s::%s::URL = %s', this.user.profile, this.person ? this.person.id : '', url);

      const host = url.match(/(?:\/\/)([^/]*)/)[1]; //.replace(/\//g, '');

      const pindex = url.indexOf(host) + host.length + 1;
      const qindex = url.indexOf('?', pindex);

      source = {
        url,
        host,
        path: qindex > -1 ? url.slice(pindex, qindex) : url.slice(pindex),
        query: qindex > -1 ? url.slice(qindex + 1, url.length) : '',
        data,
      };

      let learned = false;
      let last_index = Number.MAX_SAFE_INTEGER;
      let handler: (source: LearnSource) => Promise<void>;
      let handler_index;
      for (const index in this.urlHandlers) {
        let has_index = host.endsWith(index) ? host.indexOf(index) : -1;
        if (has_index > -1 && has_index < last_index) {
          last_index = has_index;
          handler = this.urlHandlers[index];
          handler_index = index;
        }
      }

      if(handler) {
        if (config.isEnvOffline() || data) {
          const sd = this.offline_data ? this.offline_data[handler_index] : data;
          if (sd && Array.isArray(sd)) {
            for (const s of sd) {
              source.data = s;
              await handler(source);
            }
          } else {
            source.data = sd;
            await handler(source);
          }
        }
        else await handler(source);
        learned = true;
      }

      if (!learned) await this.learnDefault(source, ['div', 'p', 'span', 'a'], 10, meta);
    } catch (err) {
      logging.warnFP(LOG_NAME, 'learnUrl', this.user ? this.user.profile : undefined, `${this.person ? this.person.id : ''} Issue learning ${JSON.stringify(source)} from ${url}`, err);
    }
  }

  /**************
   * Tally results
   **************/
  addMeaning(meaning: string | string[], bias = 1, do_ignore = []) {
    const lnames = (this.person && this.person.names ? this.person.names.map(n => n.toLowerCase()) : []).concat(['self']);
    const jobs = this.person && this.person.tags ? findTypeValues(this.person.tags, JobTags).map(j => j.toLowerCase()) : [];
    addMeaning(this.meanings, meaning, bias, [...lnames, ...jobs, ...EducationLevel, ...do_ignore]);
  }

  async lookupPerson(): Promise<Partial<Person>> {
    let found_person;
    if (this.person.self) found_person = this.person;
    else {
      const emails = parsers.findEmail(this.person.comms);
      if (emails && emails.length) {
        found_person = await data.people.matchByComms(this.user, emails, this.person.urls.filter(u => u && u.length)).catch(err => {
          logging.errorFP(LOG_NAME, 'default', this.user.profile, `Learn looking up person ${this.person ? this.person.id : ''}`, err);
        });
      } 

      if (!found_person) {
        // search by name and org
        const orgs = this.person.tags ? this.person.tags.filter(t => OrgTags.includes(t.type) && t.value).map(t => t.value.toLowerCase()) : null;
        if (orgs && orgs.length) {
          found_person = await data.people.matchByOrgs(this.user, this.person.displayName, orgs).catch(err => {
            logging.errorFP(LOG_NAME, 'default', this.user.profile, `Learn looking up person ${this.person ? this.person.id : ''}`, err);
          });
        }
      }
    }

    return found_person;
  }

  async search() {
    const now = new Date();
    if (config.get('GOOGLE_SEARCH_ID')) {
      let new_index = 0;
      const indexes = findTypeIndexes(this.person.tags);
      if (indexes.length) new_index = indexes[indexes.length - 1] + 1;

      new_index = await this.searchEntities(new_index);
      await this.searchGraph(new_index);
    }

    if (!config.isEnvOffline()) await this.learnPDL({});
    logging.infoFP(LOG_NAME, 'search', this.user.profile, `${this.person ? this.person.id : ''} Searched in ${new Date().getTime() - now.getTime()}ms`);
  }

  filterUrls(urls: string[]) {
    const pdl_id = this.person.tags ? findTypeTag(this.person.tags, TagType.PDL_ID) : null;
    const has_pdl = pdl_id && pdl_id.value && pdl_id.bias &&
        (!pdl_id.start || DAYS(new Date(pdl_id.start), 30) < this.now);

    // group urls by hostname
    // group PDL urls, ignore if there's a PDL_ID
    const host_map: {[key:string]: string[]} = {};
    for(const url of urls.filter(url => {
      const lurl = url ? url.toLowerCase() : '';
      return lurl.startsWith('http://') || lurl.startsWith('https://');
    })) {
      try {
        const murl = new URL(url);
        const base = murl.hostname.split('.').slice(-2).join('.')
        if (PDL_SUPPORT.includes(base) && has_pdl) continue;

        if (host_map[base]) {
          if (!host_map[base].includes(url)) host_map[base].push(url)
        } else host_map[base] = [url];
      } catch(e) {
        logging.warnFP(LOG_NAME, 'filterUrls', this.user.profile, `Skipping malformed url ${url}`);
      }
    }

    for(const base in host_map) {
      // sort by most paths and shortest final path
      host_map[base] = host_map[base].sort((a,b) => {
        const al = a.split('/').length;
        const bl = b.split('/').length;
        if (al === 0) return 1;
        if (bl === 0) return -1;
        if (al === bl) return b.split('/')[bl - 1].length - a.split('/')[al - 1].length;
        return bl - al;
      });
    }

    const sorted_urls = [];
    let keys = Object.keys(host_map).sort((a,b) => Object.values(host_map[b]).length - Object.values(host_map[a]).length);
    while (keys.length) {
      const del_keys = [];
      for (const key of keys) {
        const [url] = host_map[key].splice(0,1);
        sorted_urls.push(url);
        if (host_map[key].length === 0) del_keys.push(key);
      }

      keys = keys.filter(k => !del_keys.includes(k));
    }

    return sorted_urls;
  }

  async crawl() {
    const now = new Date();
    // max 10 URLs from google that don't have a name match
    this.urls.splice(10 + this.person.urls.length);
    logging.infoFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Learning ${this.person.urls.length} personal URLs and up to ${this.urls.length} found URLs`);

    const url_set = this.filterUrls([...this.person.urls, ...this.urls, ...this.other_urls]);

    await Promise.all(url_set.slice(0,50).map(this.learnUrl.bind(this)));

    logging.infoFP(LOG_NAME, 'crawl', this.user.profile, `${this.person ? this.person.id : ''} Crawled in ${new Date().getTime() - now.getTime()}ms`);
  }

  async learnCourse(url: string, data?: any): Promise<Course> {
    if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'learnCourse', this.user.profile, `Learning course ${url}`);

    await this.learnUrl(url, data);

    if (this.urls.length) {
      const learn_tags = saveMeaningTags(this.meanings, [...EducationLevel], 1, this.min_override);

      let skills = learn_tags.sort((a,b) => b.bias - a.bias).map(t => t.value); 
      skills = skills.map(parsers.remap).filter(s => parsers.latin(s));
      const vector = await skillVector(skills);

      const rem: number[] = [];
      const priority: {[key:number]: number} = {};

      for(let i = 0; i < skills.length; i++) {
        if (!rem.includes(i)) {
          for (let j = i + 1; j < skills.length; j++) {
            if (!rem.includes(j)) {
              if(skills[j].includes(skills[i]) && skills[j].length >= skills[i].length) {
                if (j in priority && priority[j] > i) priority[j] = i;
                else if(!(j in priority)) priority[j] = i;
                rem.push(j);
              }
              else if(skills[i].includes(skills[j]) && skills[i].length >= skills[j].length) rem.push(j);
            }
          }
        }
      }

      for (const j in priority) {
        skills[priority[j]] = skills[j];
      }

      skills = skills.filter((s,i) => !rem.includes(i));

      while(JSON.stringify(skills).length > 1300) skills.pop();

      const link = this.urls[0];
      const title = findTypeValue(this.job_tags, TagType.title);

      return new Course({id: hash(link.toLowerCase()), title, link, skills, vector});
    }

    return null;
  }

  async learnLink(url: string, meta = false): Promise<Tag[]> {
    logging.infoFP(LOG_NAME, 'learnLink', this.user.profile, `Learning link ${url}`);

    await this.learnUrl(url, null, meta);

    const learn_tags = saveMeaningTags(this.meanings, [...EducationLevel], 1, this.min_override);
    const title = findTypeTag(this.job_tags, TagType.title);
    if (title) return [...learn_tags, title];
    return learn_tags;
  }

  /**************
   * Main driver
   **************/
  async learnPerson(person: Person, lookup = true, force = false, did_search?: () => void ) : Promise<Person> {
    if (!person.id) {
      logging.warnFP(LOG_NAME, 'learnPerson', this.user.profile, `Not learning person with no id ${JSON.stringify(person.comms)}`);
      if (did_search) did_search();
      return;
    }

    this.now = new Date();
    this.person = new Person(person);
    this.person.self = person.self;
    this.meanings = {};
    this.photos = [];
    this.urls = [];

    logging.infoFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Learning person`);

    const found_person = lookup ? await this.lookupPerson() : person;

    if (found_person && found_person.learned && found_person.learned.length && 
      found_person.learned.map(l => new Date(l)).reduce((a,b) => a > b ? a : b, new Date(0)).getTime() > 0) {
      // Already have a global copy of the person
      logging.infoFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Merging found person from global '${found_person.displayName}' --> '${this.person ? this.person.displayName : ''}'`);
      peopleUtils.mergePeople(this.person, found_person, true);

      try { 
        const orgs = this.person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
        parsers.cleanTags(this.person.tags, this.person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
      } catch(e) {
        logging.errorFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Error cleaning tags ${JSON.stringify(this.person.tags)}`, e);
        if (did_search) did_search();
        throw e;
      }

      this.person.learned = found_person.learned.sort((a,b) => b.getTime() - a.getTime()).slice(0,1);
    }

    if (!found_person || !found_person.learned || !found_person.learned.length || 
      Math.max(...found_person.learned.map(l => new Date(l).getTime())) < DAYS(this.now, -30).getTime() || force) {

      try {
        const has_urls = this.person.urls ? this.person.urls.filter(u => u && !u.includes('askfora')) : [];
        if (has_urls && has_urls.length < 5) await this.search();
      } catch(e) {
        logging.errorFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Error searching`, e);
      }

      if (did_search) did_search();

      try {
        await this.crawl();
      } catch(e) {
        logging.errorFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Error crawling`, e);
      }

      const lnames = (this.person.names ? this.person.names.map(n => n.toLowerCase()) : []).concat(['self']);
      const jobs = this.person.tags ? findTypeValues(this.person.tags, JobTags).map(j => j.toLowerCase()) : [];
 
      const learn_tags = saveMeaningTags(this.meanings, [...lnames, ...jobs, ...EducationLevel], this.person.urls.length + this.urls.length, this.min_override);

      if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Merging tags ${JSON.stringify(learn_tags)}`);
      mergeTags(this.person.tags, learn_tags);
      mergeTags(this.person.tags, this.job_tags);

      // reorder jobs if there are new ones
      if (this.job_tags.length) {
        const job_tags = this.person.tags.filter(t => JobTags.includes(t.type));
        if (job_tags.length) {
          const non_job_tags = this.person.tags.filter(t => !JobTags.includes(t.type));
          sortTags(job_tags);
          this.person.tags = job_tags.concat(non_job_tags);
        }
      }

      this.person.learned = [this.now];

      if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Cleaning tags ${JSON.stringify(this.person.tags)}`);
      try { 
        const orgs = this.person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
        parsers.cleanTags(this.person.tags, this.person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
      } catch(e) {
        logging.errorFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Error cleaning tags ${JSON.stringify(this.person.tags)}`, e);
        throw e;
      }

      for (const photo of this.photos) {
        if (!parsers.checkKeyword(photo.toLowerCase(), IGNORE_PHOTO)) {
          saveOne(this.person.photos, photo);
        }
      }

    } else if (did_search) did_search();

    logging.infoFP(LOG_NAME, 'learnPerson', this.user.profile, `${this.person ? this.person.id : ''} Done learning person`);

    return this.person;
  }
}
