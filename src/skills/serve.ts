import { readFile } from 'fs';
import { createServer } from 'http';
import _ from 'lodash';
import path from 'path';

let port = _.random(40000, 50000);

const types = {
  json: 'application/json',
  '': 'application/octet-stream',
};

import config from '../config';

let server;

function handler(req, res) {
  const base_path = config.isRunningOnGoogle() ? path.resolve(__dirname) : path.resolve(__dirname, '..', '..', 'lib');
  const root = path.normalize(path.resolve(base_path, 'files', 'model'));

  // console.log(`${req.method} ${req.url}`);

  const extension = path.extname(req.url).slice(1);
  const type = types[extension];
  const supported_extension = Boolean(type);

  if (!supported_extension) {
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end('404: File not found');
    return;
  }

  let file_name = req.url;
  const file_path = path.join(root, file_name);
  const isPathUnderRoot = path.normalize(path.resolve(file_path)).startsWith(root);

  if (!isPathUnderRoot) {
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end('404: File not found');
    return;
  }

  readFile(file_path, (err, data) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/html' });
      res.end('404: File not found');
    } else {
      res.writeHead(200, { 'Content-Type': type });
      res.end(data);
    }
  });
}

async function listen() {
  let retry = 1;
  while(retry < 10) {
    try {
      await server.listen(port);
      break;
    } catch(e) {
      if (e.code === 'EADDRINUSE' || (e.message && e.message.includes('EADDRINUSE'))) port = _.random(40000, 50000);
      else throw e;
    }
  }
}

export default async function serve() {
  server = createServer(handler);
  server.on('error', e => {
    if (e.cause === 'EADDRINUSE' || (e.message && e.message.includes('EADDRINUSE'))) {
      port = _.random(40000, 50000);
      listen();
    } else throw e;
  });

  let running;
  const runp = new Promise(c => running = c);

  server.on('listening', () => running(server));

  listen();

  return runp;
}