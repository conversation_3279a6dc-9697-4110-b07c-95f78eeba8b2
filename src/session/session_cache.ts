import diff from 'changeset';
import _ from 'lodash';
import os from 'os';
import util from 'util';
import { v4 as uuid } from 'uuid';
import v8 from 'v8';

import config from '../config';
import data from '../data';
import lang from '../lang';

import { Session } from '../types/express';
import { InternalError } from '../types/globals';
import { Group } from '../types/group';
import { GlobalType, Person } from '../types/items';
import { Uid } from '../types/shared';
import { FORA_PROFILE } from '../types/user';

import { DAYS, HOURS, MINUTES, SECONDS } from '../utils/datetime';
import { compress, formatStack, hash } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import DataCache from './data_cache';
import Dialog, { SubDialog, Topics } from './dialog';
import ForaUser from './user';

const LOG_NAME = 'utils.SessionCache';

export interface ActiveSession {
  dialog?: Dialog;
  hash?: string;
  last: Date;
  loading?: Promise<void>;
  running?: Promise<void>;
  saved?: any;
  stack?: string[];
  path?: string;
  run_id?: Uid;
  wait_queue?: {stack_id: Uid, path: string, stack: string[]}[];
}

export interface SessionOptions {
  offset?: number;
  timeZone?: string;
  locale?: string;
  create_new?: boolean; // create if not found
  read_only?: boolean; // don't wait or save
  check_tokens?: boolean;
  stack?: string;
  group?: Group;
  stack_id?: string;
  no_cache?: boolean; // don't load cache
  wait_full_reload?: Promise<void>; // don't do a full cache reload until resolved
  ephemeral?: boolean;
  max_wait?: number; // force max wait
}

// in process cache to avoid session save/load race conditions
const active_sessions: { [key: string]: ActiveSession } = {};

function setActiveSession(id, active_session: ActiveSession, override = true) {
  if (!active_sessions[id] || override) active_sessions[id] = active_session;
}

async function cleanupSessions() {
  // clear out sessions that haven't been touched for a day
  const now = new Date();
  const clear: string[] = [];
  for (const id in active_sessions) {
    const session = active_sessions[id];
    if (!session.running) {
      if (session.dialog && session.dialog.user && session.dialog.user.isAuthenticated()) {
        if (session.dialog.user.isGuestAccount()) { 
          if (HOURS(session.last, 48) < now) clear.push(id);
        } else if (DAYS(session.last, 5) < now) clear.push(id);
      } else if (MINUTES(session.last, 5) < now) clear.push(id);
    }
  }

  for (const id of clear) {
    const active_session = active_sessions[id];
    if (active_session) {
      if (active_session.loading) await active_session.loading;
      if (active_session.running) await active_session.running;
    }
    await deleteSession(id);
  }

  let gm = process.env.GAE_MEMORY_MB;
  if (!gm) gm = 'n/a';

  const ost = os.totalmem() / 1024 / 1024;
  const osf = os.freemem() / 1024 / 1024;
  const ht = process.memoryUsage().heapTotal / 1024 / 1024;
  const hu = process.memoryUsage().heapUsed / 1024 / 1024;

  logging.infoF( LOG_NAME, 'cleanupSessions', util.format( 'cleanup: MB Memory: %s Total: %s Free: %s Load: %s% Heap: %s Used: %s Alloc: %s% sessions %s',
      gm,
      Math.round(ost),
      Math.round(osf),
      Math.round((1 - osf / ost) * 100),
      Math.round(ht),
      Math.round(hu),
      Math.round((hu / ht) * 100),
      Object.keys(active_sessions).length,
    ),
  );
  logging.infoF(LOG_NAME, 'cleanupSessions', `cleared sessions ${JSON.stringify(clear)}`);
  if (logging.isVerbose()) logging.verboseF(LOG_NAME, 'cleanupSessions', `heap information: ${JSON.stringify(v8.getHeapStatistics())}`);
}

// just delete the session caches
export async function deleteSession(session_id, user: ForaUser = null) {
  if (logging.isVerbose()) logging.verboseF(LOG_NAME, 'deleteSession', session_id);
  await data.sessions.delete(session_id).catch(err => logging.errorF(LOG_NAME, 'deleteSession', `Error deleting session ${session_id}`, err));
  // TODO: reset sessions in active_session
  releaseSession(null, session_id, Error().stack, true);
  // releaseSession(user ? user.profile : null, session_id, Error().stack, true);

  // Expire the sessions in cache
  let cache = null;
  if (user) {
    cache = new DataCache(user, null, user.loaded_groups ? Object.values(user.loaded_groups) : [], null);
    await cache.doneReloading();
    await cache.delCaches(session_id).catch(err => logging.errorFP(LOG_NAME, 'deleteSessions', user.profile, 'Error deleting caches', err));
    await DataCache.delSessionCaches(user.profile).catch(err => logging.errorF(LOG_NAME, 'deleteSessions', `Error deleting session ${session_id}`, err));
  }
  await DataCache.delSessionCaches(session_id).catch(err => logging.errorF(LOG_NAME, 'deleteSessions', `Error deleting session ${session_id}`, err));
  return cache;
}

// logout and delete all caches
export async function logoutSessions(user: ForaUser): Promise<void> {
  logging.infoFP(LOG_NAME, 'logoutSessions', user.profile, '');
  const sessions = await data.plugins.storagePlugin().findAtt(null, GlobalType.Session, 'profile', [user.profile]);

  if (sessions && sessions.length) {
    for (const session of sessions) await deleteSession(session.id);
  }

  const cache = new DataCache(user, null, user.loaded_groups ? Object.values(user.loaded_groups) : [], null);
  await cache.delCaches().catch(err => logging.errorFP(LOG_NAME, 'logoutSessions', user.profile, 'Error deleting caches', err));
  await DataCache.delSessionCaches(user.profile).catch(err => logging.errorFP(LOG_NAME, 'logoutSessions', user.profile, 'Error deleting profile session caches', err));
}

export async function waitForSession(profile: Uid, path: string, session_id: string, stack_id: string, call_stack = null, wait_timeout = 90000, retry = true): Promise<boolean> {
  const active_session = active_sessions[session_id];
  const stack = formatStack(call_stack ? call_stack : Error().stack);
    logging.infoFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} Called for ${path ? path : ''} ${stack}`);

  if (active_session) {
    if (active_session.running) logging.infoFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} Checking wait from ${path} for ${stack} active: ${active_session.path} ${active_session.run_id}  wait queue: ${JSON.stringify(active_session.wait_queue)}`);
    if (!active_session.wait_queue) active_session.wait_queue = [{path, stack, stack_id}];
    else active_session.wait_queue.push({path, stack, stack_id});

    while (active_session.running && new Date() < SECONDS(active_session.last, wait_timeout / 1000)) {
      const topic = active_session.dialog ? active_session.dialog.topic : 'no dialog';
      const ro = active_session.dialog && active_session.dialog.read_only ? 'read only ' : '';
      logging.infoFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} Waiting on running from ${path ? path : ''} ${topic} ${ro} on ${active_session.path} ${active_session.run_id} ${active_session.stack} to run ${stack}`);
      if (!active_session.dialog && !active_session.loading && !active_session.running) {
        logging.infoFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} Skipping wait no dialog not loading from ${path ? path : ''} ${topic} ${ro} ${active_session.run_id} ${active_session.stack} to run ${stack}`);
        break;
      }

      const r = active_session.running;
      logging.infoFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} ${path} waiting for ${active_session?.path} ${active_session?.running ? 'running' : ''}  ${active_session?.running ? 'loading' : ''}`);
      await Promise.any([r, new Promise(c => setTimeout(c, wait_timeout))]);
      let do_load = false;
    
      while (active_session.wait_queue && active_session.wait_queue.length) {
        if (active_session.wait_queue[0].stack_id === stack_id) {
          do_load = true;
          break;
        }
        else {
          await new Promise(c => setTimeout(c, 1000));
          if (active_session.running) break;
        }
      }

      if (do_load) {
        if (active_session.loading) {
          const topic = active_session.dialog ? active_session.dialog.topic : 'no dialog';
          const ro = active_session.dialog && active_session.dialog.read_only ? 'read only ' : '';
          logging.infoFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} Waiting on loading from ${path ? path : ''} ${topic} ${ro} on ${active_session.path} ${active_session.run_id} ${active_session.stack} to run ${stack}`);
          const l = active_session.loading;
          await l;
        }
      }
    }

    if (active_session.wait_queue && active_session.wait_queue.length && active_session.wait_queue[0].stack_id === stack_id) {
      active_session.wait_queue.splice(0,1);
    } else if (new Date() > SECONDS(active_session.last, wait_timeout / 1000) &&
      (!active_session.wait_queue || active_session.wait_queue.find(q => q.stack_id == stack_id)))  {
      logging.warnFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} Skipping queue last ${active_session.last} timeout ${wait_timeout/1000}: ${JSON.stringify(active_session.wait_queue)}`);
      active_session.wait_queue = active_session.wait_queue.filter(q => q.stack_id !== stack_id);
    } else if(active_session.wait_queue) {
      if (retry) {
        active_session.wait_queue = active_session.wait_queue.filter(q => q.stack_id !== stack_id);
        if (new Date() < SECONDS(active_session.last, wait_timeout / 1000) && retry) {
          await new Promise(c => setTimeout(c, 1000));
          return waitForSession(profile, path, session_id, stack_id, call_stack, wait_timeout);
        }
      } else logging.warnFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} Expected ${path} from ${stack} Invalid wait queue: ${JSON.stringify(active_session.wait_queue)}`);
      return false;
    }
    
    const topic = active_session.dialog ? active_session.dialog.topic : 'no dialog';
    logging.infoFP(LOG_NAME, 'waitForSession', profile, `${session_id}:${stack_id} Running ${path ? path : ''} Topic ${topic} ${stack}`);
    if (active_session.saved) active_session.saved(); 
    active_session.run_id = stack_id;
    active_session.running = new Promise<void>(c => { active_session.saved = c; });
    active_session.stack = stack;
    active_session.path = path;
  }

  return true;
}

export function clearSessionWait(profile: Uid, session_id: string, stack_id: string, call_stack = null) {
  const active_session = active_sessions[session_id];
  const stack = formatStack(call_stack ? call_stack : Error().stack);
  logging.infoFP(LOG_NAME, 'clearSessionWaut', profile, `${session_id} Clearing waiting stack ${stack_id}`);
  if (active_session) {
    if (active_session.run_id === stack_id) return releaseSession(profile, session_id, call_stack); 
    else if(active_session.wait_queue) active_session.wait_queue = active_session.wait_queue.filter(q => q.stack_id !== stack_id);
  }
}

export function releaseSession(profile: Uid, session_id: string, call_stack = null, delsession = false) {
  const active_session = active_sessions[session_id];
  const stack = formatStack(call_stack ? call_stack : Error().stack);
  const path = active_session?.path ? active_session.path : '';
  logging.infoFP(LOG_NAME, 'releaseSession', profile, `${session_id}:${active_session ? active_session.run_id : ''} Releasing ${delsession ? 'and deleting ' : ''}${active_session && active_session.running ? 'running' : ''} session ${path} wait queue: ${active_session ? JSON.stringify(active_session.wait_queue) : '[]'}`);
  if (active_session) {
    if (active_session.wait_queue?.length) {
      // logging.infoFP(LOG_NAME, 'releaseSession', profile, `wait queue: ${JSON.stringify(active_session.wait_queue)}`);
      // make sure it's not in the queue
      active_session.wait_queue = active_session.wait_queue.filter(q => q.stack_id !== active_session.run_id);
    }

    if (delsession) {
      delete active_sessions[session_id];
      active_session.dialog = null;
    }

    if (active_session.running) {
      // if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'releaseSession', profile, `${session_id} Releasing running session ${path} ${active_session.run_id} ${active_session.stack}`);
      // logging.infoFP(LOG_NAME, 'releaseSession', profile, `session stack ${active_session.stack}`);
      if (active_session.loading) {
        logging.warnFP(LOG_NAME, 'releaseSession', profile, `${session_id}:${active_session.run_id} Active session was loading ${active_session.path} stack ${active_session.stack}`);
        active_session.loading = null;
      }
      const saved = active_session.saved;
      const running_check = active_session.running;
      active_session.saved = null;
      active_session.run_id = null;
      active_session.stack = null;
      active_session.running = null; 
      saved();
      if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'releaseSession', profile, `${session_id} Released session ${path} ${stack}`);
    } else if (active_session.dialog && !active_session.dialog.read_only) {
      if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'releaseSession', profile, `${session_id}:${active_session.run_id}  Releasing session not running ${path} ${stack}`);
    } else {
      if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'releaseSession', profile, `${session_id}:${active_session.run_id}  Read only session not running ${path} ${active_session.stack} #### this stack ${stack}`);
    }
    // logging.infoFP(LOG_NAME, 'releaseSession', profile, `this stack ${stack}`);
  } else {
    if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'releaseSession', profile, `${session_id} No session ${stack}`);
  }
}

export async function waitForLoad(profile: Uid, session_id: Uid, call_stack = null) {
  const active_session = active_sessions[session_id];
  const stack = formatStack(call_stack ? call_stack : Error().stack);
  if (active_session && active_session.loading) {
    if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'waitforLoad', profile, `Waiting for session ${session_id} to load ${stack}`);
    await active_session.loading;
    if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'waitforLoad', profile, `Finished waiting for session ${session_id} to load ${stack}`);
  }
}

export async function restoreSession(dialog: Dialog, scache: any, saved: Date, read_only: boolean, no_cache?: boolean, wait_full_reload?: Promise<void>, lstack?: string) {
  if (!scache && !read_only && !no_cache) {
    // if (logging.isVerbose(active_session.dialog.user.profile)) 
    logging.infoFP(LOG_NAME, 'restoreSession', dialog.user.profile, `${dialog.session.id} reloading session ${dialog.session.guest ? ' guest': ''} actions and dialog history`);
    scache = await DataCache.getSessionCache(dialog.session.id, ['session']);
  }

  const scache_saved = new Date(scache?.session?.saved);
  if (scache_saved >= saved) {
    // get all the caches
    scache = await DataCache.getSessionCache(scache.key, ['session', 'context', 'history']);
    saved = scache_saved;
  } else scache = undefined;

  if (scache?.session) {
    // if (logging.isVerbose(active_session.dialog.user.profile)) 
    logging.infoFP(LOG_NAME, 'restoreSession', dialog.user.profile, `${dialog.session.id} restoring cached session ${dialog.session.guest ? ' guest': ''} actions and dialog history`);
    await dialog.groupsLoad(scache.session.group_host);
    await dialog.restore(scache.session, /*scache.actions,*/ scache.context, scache.history);
  }

  dialog.saved = saved;

  let me;

  // cache work
  const cache = dialog.cache;

  // load cache will try and get the latest if the hashes match, otherwise reload
  // const mes = cache.me && cache.me.id && cache.me.id !== ANONYMOUS_ID ? await cache.loadPeopleIds(cache.me.id, false) : [null];
  // me = mes && mes.length ? mes[0] : cache.user ? await data.people.getUserPerson(cache.user, cache.user.profile) : null;

  // don't need to load for read ony with no cache flag
  if ((!read_only || !no_cache) && !dialog.isAnonymousAccount()) {
    const oldid = cache.me ? cache.me.id : null;
    me = cache.me;
    const emails = me && me.comms ? parsers.findEmail(me.comms) : null;

    logging.infoFP(LOG_NAME, 'restoreSession', dialog.user.profile, `${dialog.session.id} loading cache`);
    await cache.loadCache([], wait_full_reload, lstack);
    if (oldid && (!me || oldid !== me.id || !emails || !emails.length) && !read_only && oldid !== `people/${FORA_PROFILE}`) {
      cache.purgePersonCache(new Person({id:oldid, vanity: me.vanity, comms: me.comms})).then(() => {
        cache.remapPersonCache(oldid, cache.me);
      });
      try {
        await cache.cachePerson(cache.me, false);
      } catch(e) {
        logging.warnFP(LOG_NAME, 'restoreSession', dialog.user.profile, `Error caching me ${JSON.stringify(cache.me)}`, e);
      }
    }

    me = cache.me;
    if (!me || !me.id || !me.displayName) {
      // const mes = cache.me && cache.me.id && cache.me.id !== ANONYMOUS_ID ? await cache.loadPeopleIds(cache.me.id, false) : [null];
      me = cache.user ? await data.people.getUserPerson(cache.user, cache.user.profile) : null;
    } else if(oldid === me.id) me = null; // don't need to keep it
  } else {
    if (logging.isVerbose(cache.user.profile) && lstack) logging.verboseFP(LOG_NAME, 'restoreSession', cache.user.profile, `Skipping cache load from ${lstack.split('\n')[1]}`);
    if (me && me.id) {
      const people = await cache.loadPeopleIds(me.id);
      if (people && people.length) cache.me = people[0];
    }
  }

  // make sure active session dialog is still active
  dialog.loaded = dialog.cache.loaded;
  if (me && !read_only) await dialog.setSelf(me, true, false);
}

export async function loadSession(path: string, express_session: Session, cookies: any = {}, options: SessionOptions = {}): Promise<Dialog> {
  let loaded: (s:string) => void = null;
  let saved = new Date(0);
  let profile = express_session.profile;
  const now = new Date();
  
  let active_session = active_sessions[express_session.id];

  const cookie_name = config.get('COOKIE');
  const old_cookie = cookies[cookie_name ? cookie_name : 'Fora'];
  const lstack = options.stack ? formatStack(options.stack, ['session_cache'])[0] : formatStack(Error().stack, ['session_cache'])[0]
  logging.infoFP(LOG_NAME, 'loadSession', express_session.profile, `${express_session.id}${express_session.guest ? ' guest' : ''} Path ${path} Cookie ${old_cookie} IP ${express_session.ip} ${options.read_only ? 'RO': 'R/W'} Stack ${lstack}`);

  try {
    // dialog may be in one of five places:
    // here in memory
    // serialized in memcache by profile
    // serialized in memcache by session id (for anonymous)
    // serialized in memcache by an old session
    // serialized in the datastore by an old session

    let has_session = options.read_only;
    if (!options.read_only) has_session = await waitForSession(express_session.profile, path, express_session.id, options.stack_id ? options.stack_id : uuid(), options.stack ? options.stack : Error().stack, options.max_wait);

    if (!has_session) {
      logging.warnFP(LOG_NAME, 'loadSession', profile, `Failed to load session for ${path}`);
      return null;
    }

    let do_init = false;
    let delta = active_session ? now.getTime() - new Date(active_session.last).getTime() : Number.MAX_VALUE;
    if (!active_session) {
      logging.infoF(LOG_NAME, 'loadSession', `${express_session.id} Creating active_session ${path}`);
      //////////////////////////////////////
      // setup a new session
      //////////////////////////////////////
      let as_saved;
      active_session = {
        loading: options.read_only ? null : new Promise<void>(c => {
          loaded = (s: string) => {
            logging.infoF(LOG_NAME, 'loadSession', `${express_session.id} Done creating active_session ${path}${s? ' from ': ''}${s?s:''}`);
            c(); 
          }
        }),
        last: now,
        running: options.read_only ? null : new Promise<void>(c => { as_saved = c; }),
      };
      active_session.saved = as_saved;

      if (!options.ephemeral) setActiveSession(express_session.id, active_session);
      if (!options.read_only && !options.ephemeral) {
        logging.infoF(LOG_NAME, 'loadSession', `::${express_session.id} new session ${path} ${options.read_only ? 'read only' : ''} hash ${express_session.session_hash}`);
        setActiveSession(express_session.id, active_session);
      }
    } else if (!options.read_only || !active_session.dialog) {
      //////////////////////////////////////
      // wait for the active session to load
      //////////////////////////////////////
      while (active_session.loading && active_session.dialog) {
        let timer;
        if (options.max_wait) {
          timer = setTimeout(() => {
            logging.warnF(LOG_NAME, 'loadSession', `${express_session.id} Force releasing active_session ${path} with Topic ${topic} hash ${express_session.session_hash}`);
           if (active_session.dialog) releaseSession(active_session.dialog ? active_session.dialog.user.profile : null, express_session.id);
          }, options.max_wait);
        }
        const topic = active_session.dialog ? active_session.dialog.topic : 'no dialog';
        logging.infoF(LOG_NAME, 'loadSession', `${express_session.id} Waiting for active_session ${path} with Topic ${topic} hash ${express_session.session_hash}`);
        // const last_date = active_session.last;
        await active_session.loading;
        if (timer) clearTimeout(timer);
        // if (active_session.last == last_date) 
        active_session.loading = null;
      }

      if (!options.read_only) {
        logging.infoF(LOG_NAME, 'loadSession', `${express_session.id} Loading active_session ${path}`);
        active_session.loading = new Promise<void>(c => { 
          loaded = (s:string) => {
            logging.infoF(LOG_NAME, 'loadSession', `${express_session.id} Done loading active_session ${path}${s? ' from ': ''}${s?s:''}`);
            c(); 
          }
        });
        // active_session.running = new Promise<void>(c => { active_session.saved = c; });
      }
      profile = active_session.dialog && active_session.dialog.user ? active_session.dialog.user.profile : null;
      if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'loadSession', profile, `::${active_session.last} delta ${now.getTime() - new Date(active_session.last).getTime()}`);
      active_session.last = now;
      if (active_session.dialog) saved = active_session.dialog.saved;
    }

    //////////////////////////////////////
    // here in memory, make sure it's the latest and the hashes match
    //////////////////////////////////////
    let scache = null;
    if (active_session.dialog) {
      if (logging.isVerbose(active_session.dialog.user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', active_session.dialog.user.profile, `::${express_session.id} checking active ${path} topic ${active_session.dialog.topic} ${JSON.stringify(active_session.dialog.context)}`);
      if(active_session.dialog.read_only && !options.read_only) {
        if (logging.isVerbose(active_session.dialog.user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', active_session.dialog.user.profile, `::${express_session.id} clear dialog ${path} ignoring read only active session`);
        active_session.dialog = null;
      } else if (!active_session.hash || active_session.hash !== express_session.session_hash) {
        scache = await DataCache.getSessionCache(active_session.dialog.session.id, ['session']);
        // make sure session cache and dialog still exist
        if (scache && scache.session && active_session.dialog && new Date(scache.session.saved) > active_session.dialog.saved) {
          if (logging.isVerbose(active_session.dialog.user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', active_session.dialog.user.profile, `::${express_session.id} clear dialog ${path} has cache ${new Date(scache.session.saved)} newer than memory ${active_session.dialog.saved}`);
          scache = null;
          if (loaded) loaded('mismatch hash');
          if (options.read_only) active_session = { last: now, };
          else active_session.dialog = null;
        } else if(active_session.dialog && !active_session.dialog.isAuthenticatedNonGuest()) {
          // check the session id and get last saved
          scache = await DataCache.getSessionCache(express_session.id, ['session']);
          if (scache && scache.session && new Date(scache.session.saved) > saved) saved = new Date(scache.session.saved);
        }
      } else if (express_session.session_hash && !options.read_only && !options.no_cache) {
        if (logging.isVerbose(active_session.dialog.user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', active_session.dialog.user.profile, `::${express_session.id} clear dialog ${path} hash mismatch mem:${active_session.hash} != client:${express_session.session_hash}`);
        //if (options.read_only) active_session = { last: now, };
        //else 
        active_session.dialog = null;
      }
    }

    if (active_session.dialog) {
      //////////////////////////////////////
      // found one, confirm session match or bail out
      //////////////////////////////////////
      if (active_session.dialog.session && active_session.dialog.session.id !== express_session.id) {
        if (loaded) {
          loaded('mismatch id');
          active_session.loading = null;
        }
        if (active_session.saved) {
          active_session.saved();
          active_session.running = null;
        }
        throw new Error(`Session id mismatch on dialog ${active_session.dialog.session.id} ${express_session.id} from ${path} ${options.stack}`);
      }
      active_session.dialog.session = express_session;
      if (!active_session.dialog.user) throw new Error(`Loaded dialog from active session with no user ${express_session.id}`);
      if (!options.read_only) do_init = await active_session.dialog.user.needsInit();
      const topic_loaded = active_session.dialog ? `${active_session.dialog.topic} ${active_session.dialog.loaded}` : 'no dialog';
      if (active_session.dialog && !active_session.dialog.user) throw new Error(`Loaded dialog from active session with no user ${express_session.id}`);
      logging.infoFP(LOG_NAME, 'loadSession', active_session.dialog ? active_session.dialog.user.profile : null, `::${express_session.id} active ${path} Topic ${topic_loaded} do init ${do_init}`);
      // scache = null;
    } else {
      //////////////////////////////////////
      // not in memory or memory wasn't the latest, find a serialized session
      //////////////////////////////////////
      let dsess = null;

      //////////////////////////////////////
      // check memecache by session id
      //////////////////////////////////////
      if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `Looking for session ${express_session.id} in cache.`);
      scache = await DataCache.getSessionCache(express_session.id, ['session', 'user']);

      // check memcache by profile if not anonymouse or guest
      if (!scache && express_session.profile && express_session.profile !== FORA_PROFILE && !express_session.profile.startsWith('g-')) {
        // checked_profile = true;
        if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `Looking in profile cache for session ${express_session.id}.`);
        scache = await DataCache.getSessionCache(express_session.profile, ['session', 'user']);
      }

      if (scache && scache.session) {
        dsess = scache.session;
        if(!express_session.profile || dsess.profile === express_session.profile) {
          if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `Found profile ${express_session.profile} for session ${express_session.id} in cache last: ${dsess.last} saved: ${dsess.saved}`);
          if (scache.user) dsess = _.merge(dsess, scache.user);
        } else {
          logging.warnFP(LOG_NAME, 'loadSession', express_session.profile, `Session profile mismatch: ${dsess.profile} != ${express_session.profile} for session ${express_session.id}`);
          dsess = null;
        }
      }

      //////////////////////////////////////
      // check memecache by session id
      //////////////////////////////////////
      /*if (!dsess) {
        if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `Looking for session ${express_session.id} in cache.`);
        scache = await DataCache.getSessionCache(express_session.id, ['session', 'user', 'context']);
        if (scache) dsess = scache.session;
        if (dsess) {
          if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `Found session ${express_session.id} in cache last: ${dsess.last} saved: ${dsess.saved}`);
          if (scache.user) dsess = _.merge(dsess, scache.user);
        }
      }*/

      //////////////////////////////////////
      // check memcache and datastore for an old session
      //////////////////////////////////////
      if (!dsess && old_cookie) {
        const cookie = decodeURIComponent(old_cookie).split('s:');
        if (cookie.length > 1) {
          const old_session = cookie[1].split('.')[0];
          if (old_session !== express_session.id) {
            if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `::${old_session} looking for old session in cache.`);
            scache = await DataCache.getSessionCache(old_session, ['session', 'user', 'context']);
            if (scache && scache.session) {
              if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `::${old_session} restoring for old session from cache.`);
              dsess = scache.session;
              if (dsess) {
                if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `::${old_session} found old session for new session ${express_session.id} in cache last: ${dsess.last} saved: ${dsess.saved}`);
                if (!new Date(dsess.saved).getTime()) dsess = null;
                else if (scache.user) dsess = _.merge(dsess, scache.user);
              }
            }
          } 
          
          if (!scache) {
            if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `::${old_session} looking for old session in datastore.`);
            dsess = await data.sessions.byId(old_session);
            if (dsess) {
              do_init = !dsess.session || !dsess.session.guest; //true;
              if (dsess) { if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `::${old_session} found old session for new session ${express_session.id} in datastore last: ${dsess.last} saved: ${dsess.saved}`); }
            }
            else if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `::${old_session} session for new session ${express_session.id} not found in datastore`);
          }
        }
      }

      //////////////////////////////////////
      // if we didn't have the profile and now do, check memcache again
      //////////////////////////////////////
      /*if (!checked_profile && dsess && dsess.profile) {
        const load_profile = dsess.profile;
        if (logging.isVerbose(load_profile)) logging.verboseFP(LOG_NAME, 'loadSession', load_profile, `Looking for load profile in cache.`);
        scache = await DataCache.getSessionCache(load_profile, ['session', 'user']);
        if (scache) {
          dsess = scache.session;
          if (dsess) {
            if(dsess.profile === load_profile) {
              if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `Re-fetching profile from cache session ${express_session.id} in datastore last: ${dsess.last} saved: ${dsess.saved}`);
              if (scache.user) dsess = _.merge(dsess, scache.user);
            } else {
              logging.warnFP(LOG_NAME, 'loadSession', express_session.profile, `Session profile mismatch: ${dsess.profile} != ${load_profile}`);
              scache = null;
              dsess = null;
            }
          }
        }
      }*/

      //////////////////////////////////////
      // restore session from cache or create a new one
      //////////////////////////////////////
      if (dsess) {
        if (logging.isVerbose(dsess.profile)) logging.verboseFP(LOG_NAME, 'loadSession', dsess.profile, `::${express_session.id} loading serialized session ${path} and topic ${dsess.topic} last: ${dsess.last} saved ${dsess.saved} GMT${(-1 * options.offset ? options.offset : 0) / 60}`);
        if (active_session.dialog && (!active_session.dialog.read_only || options.read_only)) {
          active_session.dialog.session = express_session;
          // if (logging.isVerbose(dsess.profile)) 
          logging.infoFP(LOG_NAME, 'loadSession', dsess.profile, `::${express_session.id} dialog found on session ${path} ${express_session.guest ? ' guest': ''} while restoring`);
        }
        else {
          active_session.dialog = new Dialog(express_session, dsess.topic, dsess, options.read_only);
          if (scache && scache.context) active_session.dialog.context = scache.context;
          if (!active_session.dialog.user) throw new Error(`Loaded dialog from session with no user ${dsess.profile}`);
          do_init = do_init || await active_session.dialog.user.needsInit();
          if (!options.ephemeral) setActiveSession(express_session.id, active_session, !options.read_only);
        }
      } else if (express_session.profile && express_session.profile !== FORA_PROFILE && !express_session.guest) {
        //////////////////////////////////////
        // new session
        //////////////////////////////////////
        const user = new ForaUser(express_session.profile);
        if (user.isAnonymousAccount() || user.isGuestAccount()) {
          logging.infoF(LOG_NAME, 'loadSession', `::${express_session.id} creating new guest session from profile ${express_session.profile}`);
          // throw new InternalError(500, `Cannot create session from anonymous or guest profile ${express_session.profile}`);
          active_session.dialog = new Dialog(express_session, Topics.DEFAULT, null, options.read_only);
          active_session.dialog.user = user;
          delete express_session.profile;
          do_init = false;
        } else {
          logging.infoF(LOG_NAME, 'loadSession', `::${express_session.id} creating new session from profile ${express_session.profile}`);
          const initialized = await data.users.init(user, options.check_tokens ? options.check_tokens : false, true);
          if (initialized) {
            active_session.dialog = new Dialog(express_session, Topics.DEFAULT, null, options.read_only);
            active_session.dialog.user = user;
            do_init = false;
            delta = 0;
          } else {
            logging.warnF(LOG_NAME, 'loadSession', `::${express_session.id} failed to initialize new session from profile ${express_session.profile}. Logging out.`);
            await logoutSessions(user);
            const loaded_check = active_session.loading;
            const old_loaded = loaded;
            if (!options.read_only) releaseSession(express_session.profile, express_session.id, options.stack ? options.stack : Error().stack);

            if (options.create_new) {
              logging.warnFP(LOG_NAME, 'loadSession', user.profile, 'Init user fail, create new true, resetting');
              if (logging.isVerbose(user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', user.profile, `::${express_session.id} ${JSON.stringify(user.serialize(false))}`);

              active_session = {
                dialog:  new Dialog(express_session, Topics.DEFAULT, null, options.read_only),
                loading: options.read_only ? null : new Promise<void>(c => {
                  loaded = (s: string) => {
                    logging.infoF(LOG_NAME, 'loadSession', `${express_session.id} Done resetting active_session ${path}${s? ' from ': ''}${s?s:''}`);
                    c(); 
                  }
                }),
                last: now,
                running: active_session.running,
                saved: active_session.saved,
              };

              active_session.dialog.user = user;
              delete express_session.profile;
              do_init = false;

              if (!options.ephemeral) setActiveSession(express_session.id, active_session);
              if (old_loaded) old_loaded('init failed and rset');
              if (logging.isDebug(user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', user.profile, `Loaded check ${loaded_check}`);
            } else {
              logging.warnFP(LOG_NAME, 'loadSession', user.profile, `::${express_session.id} init user fail, create new false - ${JSON.stringify(user.serialize(false))}`);
              releaseSession(express_session.profile, express_session.id, options.stack)
              // if (reset) releaseSession(express_session.profile, express_session.id, options.stack ? options.stack : Error().stack);
              if (old_loaded) old_loaded('init failed no reset');
              active_session.loading = null;
              if (active_session.saved) {
                active_session.saved();
                active_session.running = null;
              }
              return null;
            }
          }
        }
      }
    }

    ////////////////////////////////////////////
    // End trying to load dialog, now init user and check caches
    ///////////////////////////////////////////

    let ro_dialog: Dialog = null;

    // if we found a dialog...
    if (!active_session.dialog && active_session.loading && options.read_only) {
      logging.infoFP(LOG_NAME, 'loadSession', express_session.profile, `::${express_session.id} waiting on loading session ${active_session.path} with no dialog ${active_session.stack}`); 
      await active_session.loading;
    }

    if (active_session.dialog) {
      if (do_init) {
        if (await data.users.init(active_session.dialog.user, options.check_tokens ? options.check_tokens : false, true)) {
          // need to resave user in session after init
          if (active_session.dialog) {
            const saved_settings = await data.users.userSettings(active_session.dialog.user);
            if (saved_settings) active_session.dialog.saveSettings(saved_settings, true);
            await this.userSave(active_session.dialog);
          }
          do_init = false;
          delta = 0;
        } else {
          const profile = active_session.dialog.user.profile;
          logging.warnFP(LOG_NAME, 'loadSession', profile, `::${express_session.id} re-init user fail session, create new ${options.create_new}`);
          await logoutSessions(active_session.dialog.user);
          const loaded_check = active_session.loading;
          // if ('loading' in active_session) active_session.loading = null;
          const old_loaded = loaded;
          if (!options.read_only) releaseSession(express_session.profile, express_session.id, options.stack ? options.stack : Error().stack);

          if (options.create_new) {
            if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'loadSession', profile, `${express_session.id} ${JSON.stringify(active_session.dialog.user.serialize(false))}`);
            active_session = {
              dialog:  new Dialog(express_session, Topics.DEFAULT, null, options.read_only),
              loading: options.read_only ? null :new Promise<void>(c => {
                loaded = (s: string) => {
                  logging.infoF(LOG_NAME, 'loadSession', `${express_session.id} Done resetting after init active_session ${path}${s? ' from ': ''}${s?s:''}`);
                  c(); 
                }
              }),
              last: now,
              running: active_session.running,
              saved: active_session.saved,
            };

            active_session.dialog.user = lang.init.FORA_USER();
            await active_session.dialog.loadFora();
            delete express_session.profile;
            do_init = false;

            if (!options.ephemeral) setActiveSession(express_session.id, active_session);
            if (old_loaded) old_loaded('reinit failed - resetting');
          } else {
            logging.warnFP(LOG_NAME, 'loadSession', profile, `::${express_session.id} not creating session for ${JSON.stringify(active_session.dialog.user.serialize(false))}`);
            // if (reset) releaseSession(express_session.profile, express_session.id, options.stack ? options.stack : Error().stack);
            releaseSession(express_session.profile, express_session.id, options.stack)
            if (old_loaded) old_loaded('reinit failed - not resetting');
            active_session.loading = null;
            if (active_session.saved) {
              active_session.saved();
              active_session.running = null;
            }
            return null;
          }
        }
      } else {
        //check for new settings
        const saved_settings = await data.users.userSettings(active_session.dialog.user);
        if (saved_settings) active_session.dialog.saveSettings(saved_settings, true);
      }
    } 
 
    ///////////////////////////////////////////
    // create new if none found
    ///////////////////////////////////////////
    if (!active_session.dialog && options.create_new) {
      // if there's no dialog and create_new is true, return a new one
      logging.infoF(LOG_NAME, 'loadSession', `::${express_session.id} creating new session for`);
      active_session.dialog = new Dialog(express_session, Topics.DEFAULT, null, options.read_only);
      active_session.dialog.user = lang.init.FORA_USER();
      await active_session.dialog.loadFora();
    }


    ///////////////////////////////////////////
    // restore from cache
    ///////////////////////////////////////////
    if (active_session.dialog) {
      // still have it
      if (!active_session.dialog && active_session.loading && options.read_only) await active_session.loading;

      await this.restoreSession(active_session.dialog, scache, saved, options.read_only, options.no_cache, options.wait_full_reload, lstack);

      if (active_session.dialog) {
        if (logging.isVerbose(active_session.dialog.user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', active_session.dialog.user.profile, `::${active_session.dialog.session.id} cache loaded for last: ${active_session.last} saved: ${active_session.dialog.saved} loaded: ${active_session.dialog.loaded}`);
      } else if (logging.isVerbose(express_session.profile)) logging.verboseFP(LOG_NAME, 'loadSession', express_session.profile, `::${express_session.id} lost dialog while loading cache for`);

    }

    ////////////////////////////////////////////
    // Prep the session to run
    ///////////////////////////////////////////
    if (active_session.dialog) {
      if (!active_session.dialog && active_session.loading) {
        logging.infoFP(LOG_NAME, 'loadSession', express_session.profile, `::${express_session.id} ${path} waiting on ${active_session.path} to finish loading`);
        await active_session.loading;
      }

      // Make sure all the groups are loaded if we still have the dialog
      if (active_session.dialog) {
        const existing_host = active_session.dialog.group_host ? active_session.dialog.group_host.host : null;
        const reload = ((existing_host && existing_host !== express_session.hostname) || delta > 300000) && !options.read_only;

        const auth = active_session.dialog.isAuthenticatedNonGuest();
        // override group if specified
        if (options.group) {
          if (logging.isVerbose(active_session.dialog.user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', active_session.dialog.user.profile, `::${express_session.id} overriding groups ${existing_host} ${express_session.hostname} ${reload ? 'reload' : ''} to ${options.group.id}`);
          active_session.dialog.group_host = options.group;
        } else if(auth) {
          if (logging.isVerbose(active_session.dialog.user.profile)) logging.verboseFP(LOG_NAME, 'loadSession', active_session.dialog.user.profile, `::${express_session.id} checking user and groups ${existing_host} ${express_session.hostname} ${reload ? 'reload' : ''}`);
          await active_session.dialog.groupsLoad(existing_host && existing_host === express_session.hostname ? existing_host : express_session.hostname, reload);
        }


        if (active_session.dialog) {
          if(!active_session.dialog.user) logging.warnF(LOG_NAME, 'loadSession', `No user in dialog for active session ${active_session.dialog.session.id}`);

          if (auth) {
            try { 
              const me = await active_session.dialog.setSelf(null);
              if (!me || !me.id) await active_session.dialog.deleteSession();
            } catch(e) {
              logging.errorFP(LOG_NAME, 'loadSession', active_session.dialog && active_session.dialog.user ? active_session.dialog.user.profile : null, `::${express_session.id} error setting self`, e);
              throw e;
            }
          }

          if (delta > 300000 && active_session.dialog && !options.read_only) await active_session.dialog.loadInternalMessages(options.read_only);

          if (options.read_only) {
            if (active_session.loading) await active_session.loading;
            // if (!active_session.dialog && active_session.loading) await active_session.loading;
            // if (active_session.dialog) {
            //  if (!active_session.dialog && active_session.loading) await active_session.loading;
            //  if (active_session.dialog && active_session.running) {
            //    logging.warnFP(LOG_NAME, 'loadSession', active_session.dialog ? active_session.dialog.user.profile : null, `::${active_session.dialog.session.id} ${path} waiting on ${active_session.path} running stack ${active_session.stack}`);
            //    await active_session.running;
            //  }
              if (active_session.dialog) ro_dialog = active_session.dialog.getReadOnly();
              else logging.warnF(LOG_NAME, 'loadSession', `::${express_session.id} Failed to load read only, lost the active dialog last: ${active_session.last}`);
          //  } else logging.warnFP(LOG_NAME, 'loadSession', express_session.id, 'Lost active session dialog during load');
            const dialog_info = active_session.dialog ? ` with Topic ${active_session.dialog.topic} last: ${active_session.last} saved: ${active_session.dialog.saved} loaded ${active_session.dialog.loaded}` : ' without dialog';
            logging.infoFP(LOG_NAME, 'loadSession', active_session.dialog ? active_session.dialog.user.profile : null, `${express_session.id} Loaded ${path} read only ${express_session.guest ? ' guest': ''}${dialog_info}`);
          } else if(active_session.dialog) { // make sure we still have dialog
            active_session.dialog.clearActions();
            const dialog_info = active_session.dialog ? ` with Topic ${active_session.dialog.topic} last: ${active_session.last} saved: ${active_session.dialog.saved} loaded: ${active_session.dialog.loaded}` : ' without dialog';
            logging.infoFP(LOG_NAME, 'loadSession', active_session.dialog ? active_session.dialog.user.profile : null, `${express_session.id} Loaded ${path} ${express_session.guest ? ' guest': ''}${dialog_info}`);
          }
        }
      }
    }

    if (active_session.dialog) {
      if (config.isEnvOffline()) {
        logging.infoFP(LOG_NAME, 'context', active_session.dialog.user.profile, JSON.stringify(active_session.dialog.context));
      } else if (active_session.dialog.user.debug || config.isEnvDevelopment()) {
        logging.infoFP(LOG_NAME, 'context', active_session.dialog.user.profile, JSON.stringify(Object.keys(active_session.dialog.context)));
        if (logging.isVerbose(active_session.dialog.user.profile)) logging.verboseFP(LOG_NAME, 'context', active_session.dialog.user.profile, JSON.stringify(active_session.dialog.context));
      }
    }

    // final initialization for all dialogs
    if (active_session.dialog && active_session.dialog.isAnonymousAccount()) cleanupSessions().catch(e => logging.errorF(LOG_NAME, 'loadSession', `${express_session.id} Error cleaning up sessions from ${path}`, e));

    const loaded_check = active_session.loading;
    let rdialog = active_session.dialog;

    if (ro_dialog) {
      const dialog_info = active_session.dialog ? ` with Topic ${active_session.dialog.topic} last: ${active_session.last} saved: ${active_session.dialog.saved} loaded: ${active_session.dialog.loaded}` : ' without dialog';
      logging.infoFP(LOG_NAME, 'loadSession', active_session.dialog.user.profile, `${express_session.id} Finished loading ${path} read only ${express_session.guest ? ' guest' : ''}${dialog_info}`);
      // if (loaded) loaded();
      // active_session.loading = null;
      rdialog = ro_dialog;
    } else {
      logging.infoFP(LOG_NAME, 'loadSession', active_session.dialog && active_session.dialog.user ? active_session.dialog.user.profile : null, `::${express_session.id} finished loading session ${express_session.guest ? ' for guest' : ''}`);
      if (active_session.dialog) {
        if (active_session.dialog.read_only || active_session.dialog.cache.read_only) throw new InternalError(500, `${active_session.dialog.session.id} Returning ${path} read only sesssion when not requested`);
        active_session.hash = hash(compress(active_session.dialog.serializeSession()));
        active_session.dialog.session.session_hash = active_session.hash;
      } 

      if (loaded) loaded('success');
      active_session.loading = null;
    }

    return rdialog;
  } catch (err) {
    logging.errorFP(LOG_NAME, 'loadSession', profile, `::${express_session.id} error loading express session`, err);
    const timeout = {};
    const pending = active_session.loading && Promise.race([active_session.loading, timeout]).then(r => r === timeout);
    // releaseSession(express_session.profile, express_session.id, options.stack ? options.stack : Error().stack);
    deleteSession(express_session.id);
    active_session.dialog = null;
    // if ('loading' in active_session) active_session.loading = null;
    if (loaded) loaded('error loading');
    active_session.loading = null;
    return null;
  }
}

export function saveActiveSession(session_id, dialog: Dialog) {
  if (active_sessions[session_id]) {
    if (logging.isVerbose(dialog.user ? dialog.user.profile : null)) logging.verboseFP(LOG_NAME, 'saveActiveSession', dialog.user ? dialog.user.profile : null, `::${session_id} updating active session hash ${dialog.session.session_hash}`);
    active_sessions[session_id].dialog = dialog;
    active_sessions[session_id].hash = dialog.session.session_hash;
  } else {
    logging.infoFP(LOG_NAME, 'saveActiveSession', dialog.user ? dialog.user.profile : null, `::${session_id} new active session hash ${dialog.session.session_hash}`);
    active_sessions[session_id] = { dialog, hash:dialog.session.session_hash, last: new Date() };
  }
}

export function _shouldSaveSession(dialog: Dialog, active: any, cached: any): boolean {
  const d_saved = new Date(_.get(dialog, 'saved', new Date(0)));
  const a_saved = new Date(_.get(active, 'dialog.saved', new Date(0)));
  const c_saved = new Date(_.get(cached, 'session.saved', new Date(0)));
  const profile = dialog && dialog.user ? dialog.user.profile : null;

  if (dialog.read_only) {
    logging.infoFP(LOG_NAME, 'shouldSaveSession', profile, `FALSE read only Topic ${dialog.topic} dialog:${d_saved.toISOString()} active:${a_saved.toISOString()} cache:${c_saved.toISOString()}`);
    return false;
  }

  if (d_saved.getTime() >= a_saved.getTime() && d_saved.getTime() >= c_saved.getTime()) {
    if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'shouldSaveSession', profile, `TRUE dialog Topic ${dialog.topic} dialog:${d_saved.toISOString()} active:${a_saved.toISOString()} cache:${c_saved.toISOString()}`);
    return true;
  } else if (a_saved.getTime() > d_saved.getTime()) {
    logging.warnFP(LOG_NAME, 'shouldSaveSession', profile, `FALSE active Topic ${dialog.topic} dialog:${d_saved.toISOString()} active:${a_saved.toISOString()} cache:${c_saved.toISOString()}`);
    return false;
  } else {
    logging.warnFP(LOG_NAME, 'shouldSaveSession', profile, `FALSE cached Topic ${dialog.topic} dialog:${d_saved.toISOString()} active:${a_saved.toISOString()} cache:${c_saved.toISOString()}`);
    return false;
  }
}

export function _latestSession(dialog: Dialog, active: any, cached: any): any {
  const d_saved = new Date(_.get(dialog, 'saved', new Date(0)));
  const a_saved = new Date(_.get(active, 'dialog.saved', new Date(0)));
  const c_saved = new Date(_.get(cached, 'session.saved', new Date(0)));

  const profile = dialog && dialog.user ? dialog.user.profile : null;

  if (d_saved.getTime() >= a_saved.getTime() && d_saved.getTime() >= c_saved.getTime()) {
    if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'latestSession', profile, `Returning dialog Topic ${dialog.topic} dialog:${d_saved.toISOString()} active:${a_saved.toISOString()} cache:${c_saved.toISOString()}`);
    return dialog.serializeSession();
  } else if (a_saved.getTime() > d_saved.getTime() && a_saved.getTime() > c_saved.getTime()) {
    logging.warnFP(LOG_NAME, 'latestSession', profile, `Returning active Topic ${active.dialog.topic} dialog:${d_saved.toISOString()} active:${a_saved.toISOString()} cache:${c_saved.toISOString()}`);
    return active.dialog.serializeSession();
  } else if(cached.session) {
    logging.warnFP(LOG_NAME, 'latestSession', profile, `Returning cached Topic ${cached.session.topic} dialog:${d_saved.toISOString()} active:${a_saved.toISOString()} cache:${c_saved.toISOString()}`);
    return cached.session;
  } else {
    logging.warnFP(LOG_NAME, 'latestSession', profile, `Returning empty session dialog:${d_saved.toISOString()} active:${a_saved.toISOString()} cache:${c_saved.toISOString()}`);
    return null;
  }
}

export async function saveSession(dialog: Dialog, merge_context: string, persist: boolean) {
  const active_session = active_sessions[dialog.session.id];
  const key = dialog.session.id;
  const expires = dialog.cacheExpires();
  const scache = await DataCache.getSessionCache(key);
  const old_hash = dialog.session.session_hash;
  const now = new Date();

  const should_save = _shouldSaveSession(dialog, active_session, scache);

  let save_session;
  if (should_save) {
    dialog.saved = now;
    save_session = dialog.serializeSession();
  } else if (merge_context) {
    const save_context = dialog.context ? dialog.context[merge_context] : null;
    if (save_context) {
      save_session = _latestSession(dialog, active_session, scache);
      if (!save_session) {
        logging.warnFP(LOG_NAME, 'saveSession', dialog.user.profile, `Fetching session cache for ${dialog.session.id}`);
        let scache = await DataCache.getSessionCache(dialog.session.id);
        if (scache) save_session = scache.session;
        else {
          logging.warnFP(LOG_NAME, 'saveSession', dialog.user.profile, `Fetching session cache for ${dialog.cacheKey()}`);
          let scache = await DataCache.getSessionCache(dialog.cacheKey());
        if (scache) save_session = scache.session;
        }
      }
      if (save_session && dialog.context && dialog.context[merge_context]) {
        logging.infoFP(LOG_NAME, 'saveSession', dialog.user.profile, `Merging ${merge_context} from ${dialog.topic} into ${save_session.topic}`);
        logging.infoFP(LOG_NAME, 'saveSession', dialog.user.profile, JSON.stringify(diff(dialog.context[merge_context], save_context)));
        dialog.context[merge_context] = save_context;
        dialog.saved = now;
        save_session.saved = dialog.saved;
        if (config.isEnvOffline()) {
          logging.infoFP(LOG_NAME, 'context', dialog.user.profile, JSON.stringify(dialog.context));
        } else if (dialog.user.debug || config.isEnvDevelopment()) {
          logging.infoFP(LOG_NAME, 'context', dialog.user.profile, JSON.stringify(Object.keys(dialog.context)));
          if (logging.isVerbose(dialog.user.profile)) logging.verboseFP(LOG_NAME, 'context', dialog.user.profile, JSON.stringify(dialog.context));
        }
      } else logging.warnFP(LOG_NAME, 'saveSession', dialog.user.profile, `Not merging ${merge_context} ${save_session === null ? 'null save_session': ''} ${dialog.context === null ? 'null context': ''} ${dialog.context[merge_context] === null ? 'null merge_context' : ''}`)
    } else logging.warnFP(LOG_NAME, 'saveSession', dialog.user.profile, `No save_context for ${merge_context}`)
  }

  dialog.session.guest = dialog.user.isGuestAccount();

  if (save_session && (should_save || (dialog.context && merge_context && dialog.context[merge_context]))) {
    if (logging.isVerbose(dialog.user.profile)) logging.verboseFP(LOG_NAME, 'saveSession', dialog.user.profile, `Saving session cache for ${dialog.session.id}${dialog.session.guest ? ' guest' : ''}`);
    const user_session = dialog.user.serialize();
    const history = dialog.history.map(h => {
      const sd: Partial<SubDialog> = {};
      Object.assign(sd, h);
      if(sd.info) delete sd.info;
      return sd;
    });
    await DataCache.saveSessionCache(key, {session:save_session, /*actions:dialog.actions,*/ history, context: dialog.context, user: user_session}, expires)
      .catch(e => logging.errorF(LOG_NAME, 'saveSession', 'Error saving session cache', e));

    // also save session cache by profile if we're logged in
    if (dialog.isAuthenticated()) {
      await DataCache.saveSessionCache(dialog.cacheKey(), {session:save_session, /*actions:dialog.actions,*/ history, context: dialog.context, user: user_session}, expires);
      // if (!dialog.user.resave) await data.users.globalRegister(dialog.user);
    }
    dialog.session.session_hash = hash(compress(save_session));
    if (dialog.user.profile && should_save) saveActiveSession(dialog.session.id, dialog);

    if (dialog.user.isAuthenticated()) dialog.session.profile = save_session.profile;
    else if(dialog.session.profile) delete dialog.session.profile;
  } else logging.infoFP(LOG_NAME, 'saveSession', dialog.user.profile, `NOT saving session cache for ${dialog.session.id} ${save_session === null ? 'null save_session' : ''} should_save: ${should_save} merge_context: ${merge_context} ${dialog.context[merge_context] ? 'merged' : 'no thing to merge'}`);

  if (dialog.user.isAuthenticatedNonGuest() && persist && dialog.session.session_hash !== old_hash && !config.isEnvOffline()) {
    if (dialog.user.profile === FORA_PROFILE) throw new Error(`Trying to save anonymous profile session ${dialog.session.id} to data store`);
    if (logging.isVerbose(dialog.user.profile)) logging.verboseFP(LOG_NAME, 'saveSession', dialog.user.profile, `datastore ${dialog.session.id} hash ${dialog.session.session_hash} topic ${dialog.topic} saved ${dialog.saved}`);
    await data.sessions.save(dialog.session.id, save_session);
  } else {
    logging.infoFP(LOG_NAME, 'saveSession', dialog.user.profile, `skip datastore ${dialog.session.id} hash ${dialog.session.session_hash} topic ${dialog.topic} saved ${dialog.saved}`);
  }

  if (!dialog.read_only) {
    logging.infoFP(LOG_NAME, 'saveSession', dialog.user.profile, `Releasing session for ${dialog.session.id}${dialog.session.guest ? ' guest' : ''} Topic ${dialog.topic}`);
    // await new Promise(c => dialog.session.save(c));
    releaseSession(dialog.user.profile, dialog.session.id, Error().stack);
  } else logging.infoFP(LOG_NAME, 'saveSession', dialog.user.profile, `NOT releasing session for ${dialog.session.id}`);
}

export async function userSave(dialog: Dialog) {
  const key = dialog.cacheKey();
  const expires: number = dialog.cacheExpires();
  const user = dialog.user.serialize();
  await DataCache.saveSessionCache(key, {user}, expires);
}

// save just the dialog object to memory, no current session
export async function quickSave(dialog: Dialog) {
  let profile = dialog.user.profile;
  if (!profile) profile = FORA_PROFILE;

  logging.infoFP(LOG_NAME, 'quickSave', profile, `Session=${dialog.session.id} profile=${dialog.session.profile} hash=${dialog.session.session_hash} saved=${dialog.saved} Topic ${dialog.topic}`);

  const active_session = active_sessions[dialog.session.id];
  const scache = await DataCache.getSessionCache(dialog.session.id);

  if (_shouldSaveSession(dialog, active_session, scache)) {
    dialog.saved = new Date();
    const save_session = dialog.serializeSession();

    // save session cache by profile unless we don't have a profile yet
    const key = dialog.session.id;
    const expires: number = dialog.cacheExpires();
    if (dialog.user.isGuestAccount()) {
      const user_session = dialog.user.serialize();
      await DataCache.saveSessionCache(key, {session:save_session, context: dialog.context, user: user_session}, expires);
    } else {
      await DataCache.saveSessionCache(key, {session:save_session, context: dialog.context}, expires);
      if (dialog.user.isAuthenticatedNonGuest()) await DataCache.saveSessionCache(dialog.cacheKey(), {session:save_session, context: dialog.context}, expires);
    }
    dialog.session.session_hash = hash(compress(save_session));
    if (dialog.user.profile) saveActiveSession(dialog.session.id, dialog);
    if (dialog.user.isAuthenticated()) dialog.session.profile = dialog.user.profile;
    dialog.session.guest = dialog.user.isGuestAccount();
  }

  if (!dialog.read_only) releaseSession(dialog.user.profile, dialog.session.id, Error().stack);
}
