import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import data from '../data';

import DataCache from './data_cache';

import lang from '../lang';

import { Group } from '../types/group';

import { Event, GlobalType, Person, Tag } from '../types/items';
import { Filter, Relation, TagType, TimeVal, Uid, UndefinedPersonInfo, filterSkills, filterTags } from '../types/shared';

import { skillGroup } from '../skills';
import People from './entities/people';

import { Action, ActionType } from '../types/globals';
import { MIDNIGHT, WEEK_START, compareTimeVals } from '../utils/datetime';
import { checkFilter, clearFilter, filterPeople, filterWeightedPeople } from '../utils/filter';
import { stringList } from '../utils/format';
import { flatten, prefixWords, weightMax } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

const DEBUG = (require('debug') as any)('fora:data:entities.People');
const LOG_NAME = 'data.entities.people';

interface SearchOptions { 
  network: 'exclude' | 'include' | 'only'; // = 'exclude'; 
  require_email?: boolean; 
  mandatory_tags?: Tag[][]; 
  group_search_ids?: Uid[]; 
  additional_user_ids?: Uid[]; 
  only_users?: boolean
}

export default class Search {
  cache: DataCache = null;
  people: People = null;

  constructor(cache: DataCache, people: People) {
    this.cache = cache;
    this.people = people;
  }

  async searchGroupPeople(group_search_ids: Uid[], terms: string[], tag_types: TagType[] = [TagType.skill], network = false): Promise<Partial<Person>[]> {
    let people: Partial<Person>[] = [];
    try {
      const additional_user_ids = [];  
      const mandatory_tags = [];
      let only_users = true;

      if (group_search_ids && group_search_ids.length) {
        for (const group_id of group_search_ids) {
          const filter = {name:'groups', op: '=', val: group_id};
          const keys = await data.plugins.storagePlugin().findKeysByFilter(null, GlobalType.User, [filter]);
          keys.filter(k => k.name !== this.cache.user.profile).forEach(k => additional_user_ids.push(k.name));

          if (this.cache.user.loaded_groups) {
            const group = this.cache.user.loaded_groups[group_id];
            if (group.search_mandatory_tags && group.search_mandatory_tags.length) mandatory_tags.push(group.search_mandatory_tags);
            if (group.search_groups_contacts) only_users = false;
          }
        }
      }

      const filter = peopleUtils.seniorTitle(terms) ? null : peopleUtils.JOB_TITLES.slice();

      let network_people: {person: Partial<Person>, weight: number, stddev: number}[] = [];
      if (network && !only_users) {
        if (tag_types.includes(TagType.skill)) network_people = await this.people.findBySkills(terms, { network: 'include', mandatory_tags, additional_user_ids, only_users});
        else network_people = await this.people.findByTerms(terms, { network: 'include', mandatory_tags, additional_user_ids, only_users});
      }

      const result_users = await data.people.bigQuerySearchGroupUserStats(this.cache.user, additional_user_ids, terms, filter);

      const result_people = peopleUtils.mergeResultPeople([...result_users, ...network_people]);
      logging.infoFP(LOG_NAME, 'searchGroupPeople', this.cache.user.profile, `Found ${result_people ? result_people.length : 'no'} people searching by ${JSON.stringify(tag_types)}`);

      let filter_people = filterWeightedPeople(this.cache.user, this.cache.me, result_people, mandatory_tags, only_users);

      const weights = filter_people.map(wp => wp.weight);
      const max = weightMax(weights);
      people = filter_people.filter(wp => wp.weight >= max).sort((a,b) => b.stddev - a.stddev).map(wp => wp.person);

      people.filter(p => p.id).forEach(person => this.cache.cachePerson(person));
    } catch (err) {
      logging.errorFP(LOG_NAME, 'searchGroupPeople', this.cache.user.profile, 'Error querying for group people', err);
    }

    return people;
  }

  async updateSearchFilters(filters: Filter[], message: string, context: string[], actions: Action[], check_people: Partial<UndefinedPersonInfo>[], check_names: string[], network: boolean) {
    let skill_search = false;
    let name_search = false;
    let match_people: Partial<Person>[] = [];
    // if ((!match_people || !match_people.length) && (people && people.length)) match_people = _.uniqBy(people, 'id');

    // check for matched people
    if (check_people && check_people.length) {
      const matches = _.uniqBy(check_people.filter(c => c.id).map(c => { return { id: c.id, names: [c.name]}}), 'id');
      if (matches && matches.length) {
        match_people = matches;
        const name_set = parsers.expandWords(flatten(matches.map(m => m.names)));
        filters.forEach(f => clearFilter(f, [...name_set, ...prefixWords(lang.find.FIND_KWD, name_set)]));
        skill_search = true;
        name_search = check_names && check_names.length > 0;
      } else {
        check_names = _.uniq([...check_names ? check_names : [], ...check_people.map(c => c.name).filter(n => n)])
      }
    }

    // check for names
    if (name_search && (!match_people || !match_people.length)) {
      if (check_names && check_names.length) {
        let matches = await this.people.findByName(check_names);
        if (!matches?.length) {
          const mandatory_tags: Tag[][] = [];
          const group_ids: Uid[] = []; 
          if (this.cache.user.loaded_groups) {
            for (const group of Object.values(this.cache.user.loaded_groups) as Group[]) {
              group_ids.push(group.id);
              mandatory_tags.push(group.search_mandatory_tags);
            }
          }
          const match_names = await this.people.getNames(check_names, true, Object.keys(this.cache.user.groups));
          if (match_names) matches = match_names.map(name => { return { names: [name] }});
        }

        if (matches?.length) match_people = [...match_people, ...matches];
      }
    }

    // if (!skill_search) filters.forEach(f => removeConditions(f, [TagType.skill]));

    const lmessage = parsers.skillExtract(message.toLowerCase());
    const parts = lmessage.split(' ');

    // fix skill values
    filters.forEach(filter => {
      const skills = filter.conditions.filter(c => c.att === TagType.skill);
      skills.forEach(c => {
        c.value = _.uniq(flatten(c.value.map(v => parsers.findMeaning(v))));
      }); 
    });

    // positions
    const positions = peopleUtils.titles(message);
    if (positions) {
      filters.push({ id: uuid(), name: 'positions', network, conditions: [{
        att: TagType.jobTitle,
        rel: Relation['='],
        value: positions,
      }]});
    }

    const skills = flatten(filters.map(f => flatten(f.conditions.filter(c => c.att === TagType.skill).map(c => (c.value as string[]).map(s => s.toLowerCase())))));
    if (!skills.includes(lmessage) && !context.includes(lmessage) && _.intersection(context, parts).length === 0) {
      const skill_filter = filters.find(f => f.conditions.find(c => c.att === TagType.skill));
      if (skill_filter) {
        const skill_cond = skill_filter.conditions.find(c => c.att === TagType.skill);
        if (skill_cond) (skill_cond.value as string[]).push(message);
        else skill_filter.conditions.push({att: TagType.skill, rel: Relation['~'], value: [message]});
      }
      else filters.push({id: uuid(), name: message, conditions: [{att: TagType.skill, rel: Relation['~'], value: [message]}]});
    }

    if (match_people.length) {
      // remove skill filters that match people and orgs
      const names =  _.uniq(actions.filter(a => [ActionType.ENTITY].includes(a.type) && a.context).map(a => a.context)); 
      const orgs = _.uniq(actions.filter(a => [ActionType.ORG].includes(a.type) && a.context).map(a => a.context));
      const name_set = parsers.expandWords(check_names);
      filters.forEach(f => clearFilter(f, [...names, ...orgs, ...name_set, 
        ...prefixWords(lang.find.FIND_KWD, [...names, ...orgs, ...name_set])], [TagType.skill]));

      if(names) {
        const name = stringList(_.uniq(names.filter(n => n)).slice(0,3), 'or');
        filters.push({id: uuid(), name, network, conditions: [{
          att: TagType.person,
          rel: Relation['='],
          value: names,
        }]});
      }

      const exact = match_people.filter(p => p.id);
      const partial = match_people.filter(p => !p.id);
      if (exact.length) {
        const ids =  _.uniq(exact.map(p => p.id));
        const people = await this.cache.loadPeopleIds(ids);
        const match = people.filter(p => checkFilter(p, filters, this.cache));
        const value = match.map(p => p.id);

        const name = stringList(_.uniq(match.map(p => p.displayName).filter(n => n)).slice(0,3), 'or')

        filters.push({id: uuid(), name, network, conditions: [{
          att: TagType.person,
          rel: Relation['='],
          value,
        }]});
      }

      if (partial.length) {
        const value = _.uniqBy(flatten(partial.map(p => p.names)), a => a.toLowerCase());
        filters.push({id: uuid(), name: stringList(value, 'or'), network, conditions: [{
          att: TagType.person,
          rel: Relation['~'],
          value,
        }]});
      }
    }


  }

  globalSearch(person) {
    return data.people.matchByComms(this.cache.user, person.comms, person.urls);
  }

  async searchSkills(skills: string[], options: SearchOptions): Promise<Partial<Person>[]> {
    const people_weights = await this.people.findBySkills(skills, options);

    if(people_weights ) {
      const weights = people_weights.map(wp => wp.weight);
      const max = weightMax(weights);
      return people_weights.filter(wp => wp.weight >= max).sort((a,b) => b.stddev - a.stddev).map(wp => wp.person);
    }

    return [];
  }

  async searchTerms(terms: string[], options: SearchOptions): Promise<Partial<Person>[]> {
    const people_weights = await this.people.findByTerms(terms, options);

    if(people_weights ) {
      const weights = people_weights.map(wp => wp.weight);
      const max = weightMax(weights);
      return people_weights.filter(wp => wp.weight >= max).sort((a,b) => b.stddev - a.stddev).map(wp => wp.person);
    }

    return [];
  }

  async searchByEmail(comms: string[], network = false, mandatory_tags?: Tag[][], group_search_ids?: Uid[]): Promise<Partial<Person>[]> {
    const additional_user_ids: Uid[] = group_search_ids && group_search_ids.length ? await data.groups.groupUserIds(this.cache.user, group_search_ids) : null;

    let aliases = null;
    if (network) {
      aliases = this.cache.me ? parsers.findEmail(this.cache.me.comms) : [this.cache.user.email];
      if (!aliases.includes(this.cache.user.email)) aliases.push(this.cache.user.email);
    }

    const emails = parsers.findEmail(comms);
    try {
      const result_people = emails && emails.length ? await data.people.bigQueryByEmails(this.cache.user, emails, aliases, additional_user_ids) : [];
      const filter_people = filterPeople(this.cache.user, this.cache.me, result_people, mandatory_tags, false);
      filter_people.filter(p => p.id).forEach(person => this.cache.cachePerson(person));
      return filter_people;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'searchByEmail', this.cache.user.profile, 'Error querying for people', err);
    }
    return [];
  }

  async searchByName(names: string[], network = false, mandatory_tags?: Tag[][], group_search_ids?: Uid[]): Promise<Partial<Person>[]> {
    const additional_user_ids: Uid[] = group_search_ids && group_search_ids.length ? await data.groups.groupUserIds(this.cache.user, group_search_ids) : null;
    let aliases = null;
    if (network) {
      aliases = this.cache.me ? parsers.findEmail(this.cache.me.comms) : [this.cache.user.email];
      if (!aliases.includes(this.cache.user.email)) aliases.push(this.cache.user.email);
    }

    try {
      const result_people = names && names.length ? await data.people.bigQueryByNames(this.cache.user, names, aliases, additional_user_ids) : [];
      const filter_people = filterPeople(this.cache.user, this.cache.me, result_people, mandatory_tags, false);
      filter_people.filter(p => p.id).forEach(person => this.cache.cachePerson(person));
      return filter_people;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'searchByName', this.cache.user.profile, 'Error querying for people', err);
    }
    return [];
  }

  async searchByFilter(filters: Filter[], group?: Group): Promise<Partial<Person>[]> {
    const searches: Promise<Partial<Person>[]>[] = [];
    
    if (filters) {
      for (const filter of filters) {
        let event_people_ids = [];

        const event_conditions = filter.conditions.filter(c => c.att === TagType.event);

        // check for event as a filter and as parameter
        let f_events: Event[];
        for (const condition of event_conditions) {
          const condition_tv = condition.value[0] as TimeVal;
          let condition_date = condition_tv.start;
          if (condition_tv.day) condition_date = MIDNIGHT(condition_date, this.cache.user.offset);
          if (condition_tv.week) condition_date = WEEK_START(condition_date, this.cache.user.offset);

          let c_events: Event[];

          switch(condition.rel) {
            case Relation['=']:
            case Relation['$']:
              c_events = this.cache.events.filter(event => compareTimeVals({start: new Date(event.start), text: undefined}, condition.value[0] as TimeVal, this.cache.user.offset));
              break;
            case Relation['<']:
              c_events = this.cache.events.filter(event => new Date(event.start).getTime() < condition_date.getTime())
              break;
            case Relation['>']:
              c_events = this.cache.events.filter(event => new Date(event.start).getTime() > condition_date.getTime())
              break;
            case Relation['<>']:
              break;
            case Relation['><']:
              break;
          }

          if (c_events && c_events.length) {
            if (!f_events) f_events = c_events.slice();
            else f_events = _.intersectionBy(f_events, c_events, 'id');
          } else f_events = [];
        }

        if (f_events && f_events.length) event_people_ids = flatten(f_events.map(e => e.people)).map(p => p.id);

        const lookup_ids = flatten<Uid>(filter.conditions.filter(c => c.att === TagType.person && c.rel === Relation['=']).map(c => c.value as string[]));
        const lookup_names = flatten<string>(filter.conditions.filter(c => c.att === TagType.person && c.rel === Relation['~']).map(c => c.value as string[]));

        // Person lookups
        if (lookup_ids.length || event_people_ids.length) searches.push(this.people.byId(_.uniq([...lookup_ids, ...event_people_ids])));
        if (lookup_names.length) searches.push(this.people.findByName(lookup_names));

        const skills = filterSkills(filter, [Relation['='], Relation['~']]);

        const tag_types = Object.values(TagType);
        const search_tag_types = filter.conditions.map(c => c.att).filter(t => tag_types.includes(t as TagType)) as TagType[];

        const search_skills = skills.length < 12 ? parsers.expandWords(skills) : [];

        // const related_skills = await filterRelated(skills, this.cache.me.skillsBatch, 0.99, false, true);

        const groups = this.cache.user.categoryGroups();
        const group_ids: Uid[] = groups ? groups.map(g => g.id) : undefined;
        let global_cats = !groups || !groups.length || groups.reduce((a,b) => a || b.global_categories, false);
        const found_skills = skills.length + search_skills.length < 12 ? await skillGroup(group_ids, global_cats, skills, this.cache.user.locale, 10, true) : [];

        let terms = filterTags(filter, [Relation['='], Relation['~']], [TagType.skill, TagType.event]);
        terms = [...terms, ...flatten(terms.filter(t => t).map(t => t.toLowerCase().split(' ').filter(s => s.length > 2 && !parsers.ignore(s) && !parsers.mask(s) && !parsers.name(s))))];

        const g_searches = this.cache.user.search_groups;
        const mandatory_tags = this.cache.user.mandatory_tags;

        if (group) {
          searches.push(this.searchGroupPeople(
            [group.id], 
            skills && skills.length ? [...skills, ...search_skills, ...found_skills] : terms, 
            skills && skills.length ? [TagType.skill] : search_tag_types, filter.network ));
        } 

        if(skills && skills.length) {
          searches.push(this.searchSkills([...skills, ...search_skills, ...found_skills], {
              network: filter.network ? 'include' : 'exclude', 
              require_email: false, 
              mandatory_tags, 
              group_search_ids: group ? [group.id] : filter.network ? g_searches : []
          }));
        } else {
          searches.push(this.searchTerms(terms, {
              network: filter.network ? 'include' : 'exclude', 
              require_email: false, 
              mandatory_tags, 
              group_search_ids: group ? [group.id] : filter.network ? g_searches : []
          }));
        }
      }
    }

    const people = _.uniqBy(flatten<Partial<Person>>(await Promise.all(searches)).filter(p => p), p => p.vanity ? `profile/${p.vanity}` : p.id);

    logging.infoFP(LOG_NAME, 'searchByFilters', this.cache.user.profile, `Found ${people.length} people for ${JSON.stringify(filters)}`);

    const save_network: Person[] = []

    people.forEach(r => {
      if(r.network || !r.id) {
        r.tempId();
        save_network.push(new Person(r));
      }
    });

    if (save_network.length) await data.people.saveAll(this.cache.user, save_network);

    return people;
  }

} 