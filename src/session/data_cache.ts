import diff from 'changeset';
import _ from 'lodash';
import { setTimeout } from "timers/promises";
import util from 'util';
import { v4 as uuid } from 'uuid';

import { InternalError, NameMap } from '../types/globals';
import { Group } from '../types/group';
import { Contract, Event, GlobalType, Message, Note, Person, Project, Task, getTypes } from '../types/items';
import { Context } from '../types/plugins';
import { ANONYMOUS_ID, EntityType, TagType, Uid, findTypeValues } from '../types/shared';

import { MAX_CACHE_VAL, MINUTES, SECONDS, sortEvents, sortMessages, } from '../utils/datetime';
import { STRIP_REGEX, arraysIntersect, compress, formatStack, hash, permuteName, slimEntity, uncompress, wordSet } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import config from '../config';
import data from '../data';
import lang from '../lang';
import { SourceController } from '../sources/source_controller';
import { FORA_PROFILE } from '../types/user';
import { SubDialog } from './dialog';
import ForaUser from './user';

const LOG_NAME = 'utils.DataCache';
const DEBUG = (require('debug') as any)('fora:utils:cache');

export const MAX_EXPIRES = 0x278d00;
// const MAX_PEOPLE_CACHE = 500;

// prettier-ignore
const _EntityCaches: string[] = [
  'events', 'tasks', 'messages', 'contracts', 'projects', // 'people_names', 'org_people',
  //'people_events', 'people_tasks', 'people_messages', 'people_projects',
]; // 'people', 'notes', 'people_notes'

const _SessionCaches: string[] = ['session', /*'actions',*/ 'history', 'context', 'user'];

const _EntityCacheMap: { [key: string]: EntityType } = {
  events: EntityType.Event,
  tasks: EntityType.Task,
  messages: EntityType.Message,
  contracts: EntityType.Contract,
  projects: EntityType.Project,

  // notes: EntityType.Note,
  // people_events: EntityType.Event,
  // people_tasks: EntityType.Task,
  // people_messages: EntityType.Message,
  // people: EntityType.Person,
  // people_names: EntityType.Person,
  // org_people: EntityType.Person,
  // people_notes: EntityType.Note,
  // people_projects: EntityType.Project,
};

export default class DataCache {
  me: Person = null;
  cache_clear: string[] = [];
  cache_dirty: string[] = [];
  cache_hash: {[key: string]: {hash: string; count: number}} = {};
  contracts: { [key: string]: Contract } = {};
  events: Event[] = [];
  groups: Group[] = [];
  loaded: Date = new Date(0);
  loaded_people: Date = new Date(0);
  cache_lock = false;
  messages: Message[] = [];
  notes: { [key: string]: Note } = {};
  projects: { [key: string]: Project } = {};
  session_id: string = null;
  tasks: { [key: string]: Task} = {};
  user: ForaUser = null;
  timed_out = false;
  read_only = false;
  people_loaded: Promise<void> = null;

  // secondary index cache points from people and events to items
  // people: { [key: string]: Person } = {};
  /*people_events: { [key: string]: Event['id'][] } = {}; // {email:[events]} contains duplicates
  people_messages: { [key: string]: Message['id'][] } = {}; // {email:[message]} contains duplicates
  people_notes: { [key: string]: Note['id'][] } = {}; // {person.id:[note_id]} contains duplicates
  people_projects: { [key: string]: Project['id'][] } = {};
  people_tasks: { [key: string]: Task['id'][] } = {}; // {email:[tasks]} contains duplicates*/

  // for unit testing
  orgs: string[];
  people_names: { [key: string]: {[key: string]: string}} = {};

  constructor(user: ForaUser, me: Person, groups: Group[], session_id: string, read_only = false,
    people_names: { [key: string]: {[key: string]: string}} = undefined,
    orgs: string[] = undefined) {

    this.session_id = session_id;
    this.user = user ? user : new ForaUser(FORA_PROFILE);
    this.me = me ? new Person(me) : me;
    if (this.me) {
      this.me.self = true;
      if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'constructor', this.user.profile, `Initializing me to ${JSON.stringify(this.me)}`);
    }
    this.groups = groups;
    this.cache_dirty = [];
    this.cache_hash = {};
    this.read_only = read_only;

    if (config.isEnvOffline()) {
      this.people_names = people_names;
      this.orgs = orgs;
    }
  }
      
  clone(source: DataCache) {
    this.read_only = true;
    this.cache_hash = source.cache_hash;
    this.loaded = source.loaded;
    this.loaded_people = source.loaded_people;
    this.user = source.user;
    this.me = source.me;
    this.groups = source.groups;
    for (const cache of _EntityCaches) this[cache] = source[cache];
    this.cache_dirty = [];
  }

  // marks a cache as dirty so it gets saved to memcache
  cacheDirty(cache: string | string[] = null) {
    if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    if (!cache) {
      this.cache_dirty = _EntityCaches;
      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'cacheDirty', this.user.profile, `Marking ALL caches dirty`);
    }
    else if (Array.isArray(cache)) cache.forEach(c => this.cacheDirty(c));
    else {
      if (!this[cache]) throw new Error(`cacheDirty: can't mark unknown dirty cache ${cache}`);
      if (!this.cache_dirty.includes(cache)) {
        this.cache_dirty.push(cache);
        if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'cacheDirty', this.user.profile, `Marking cache dirty ${cache} with ${Array.isArray(this[cache]) ? this[cache].length : Object.keys(this[cache]).length} count ${JSON.stringify(this.cache_hash[cache])}`);
      }
      // this.cache_hash[cache] = {hash:null, count:this.cache_hash[cache] ? this.cache_hash[cache].count : 0};
    }
  }

  // cache key is either profile if we have it for session for anonymous
  cacheKey() {
    return this.user.isAuthenticatedNonGuest() ? this.user.profile : this.session_id;
  }

  // add a person to the cache and will save back to memcache
  async cachePerson(person: Partial<Person>, map_details = true) {
    // if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    if (person.id === null || person.id === undefined || person.id.length <= 0) throw new InternalError(500, `Cannot cache person with no id ${person.id}`, person);
    if (person.displayName === null || person.displayName === undefined || person.displayName.length <= 0) throw new InternalError(500, `Not caching person with no displayName ${person.id}`, person);
    if (person.id.startsWith('people/f')) return; // about info
    const existing = await data.plugins.cachePlugin().lookupCacheAttVal(this.user.profile, EntityType.Person, 'id', person.id);
    if (existing) {
      const person_diff = diff(existing, person);
      if (!person_diff.length) return;
    }

    if(person.comms) {
      const bad_comms = person.comms.filter(c => typeof c !== 'string');
      if (bad_comms.length) {
        logging.warnFP(LOG_NAME, 'cachePerson', this.user.profile, `${person.id} Not caching bad comms ${bad_comms}`);
        person.comms = person.comms.filter(c => c && typeof c === 'string');
      }
    }
    
    await data.plugins.cachePlugin().cacheAttVal(this.user.profile, EntityType.Person, 'id', person.id, person, 5 * 24 * 3600);

    if (map_details) {
      this.mapPersonDetails(person);
    }
  }

  cacheExpires() {
    let expires: number = 5 * 24 * 3600; //MAX_EXPIRES;
    // sessions without profile should expire quickly
    if (this.user.isGuestAccount()) expires = 48 * 3600;
    else if (!this.user.isAuthenticated()) expires = 600;
    return expires;
  }

  // returns info about the cache
  cacheStats() {
    const stats = {cache_dirty: this.cache_dirty, };
    for (const cache in _EntityCaches) {
      if (this[cache]) {
        stats[cache] = {
          count: this[cache].length,
          size: JSON.stringify(this[cache]).length,
          hash: this.cache_hash[cache].hash,
          memcount: this.cache_hash[cache].count,
        };
      }
    }
    return stats;
  }

  // delete all the caches in mem cache that need to be cleared
  async cleanCache() {
    if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    if (this.cache_clear.length) {
      logging.infoFP(LOG_NAME, 'cleanCache', this.user.profile, `Clearing caches for ${this.session_id}: ${this.cache_clear}`);
      const release = await this.waitForReload(90000, new Error().stack);
      // const cache_saves = [];
      for (const cache of this.cache_clear) {
        await this.delCache(cache);
        this.cache_hash[cache] = {hash:null, count : 0};
        if (Array.isArray(this[cache])) this[cache] = [];
        else this[cache] = {};
      }
      this.cache_clear = [];

      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'cleanCache', this.user.profile, `Save cache_hash ${JSON.stringify(this.cache_hash)}`);

      await this.setCache(this.cacheKey(), 'cache_hash', this.cache_hash).catch(e => {
        logging.errorFP(LOG_NAME, 'cleanCache', this.user.profile, 'Error in clearing cache_hash', e);
      });

      if (release) await this.doneReloading();
    }
  }

  clearCacheDirty() {
    this.cache_dirty = [];
  }

  // delete all caches
  async delCaches(key: string = null) {
    if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    for (const cache of _EntityCaches) await this.delCache(cache, key ? key : this.cacheKey());
  }

  // delete specific cache from profile and session caches
  async delCache(cache: string, key: string = null) {
    if (this.read_only && cache !== 'reload') throw new InternalError( 400, 'Cannot update readonly cache');
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'delCache', this.user.profile, `Deleting cache ${cache}`);
    if (key) await DataCache._delCache(key, cache, this.cache_hash[cache] ? this.cache_hash[cache].count : 0);
    else {
      await DataCache._delCache(this.user.profile, cache, this.cache_hash[cache] ? this.cache_hash[cache].count : 0);
      await DataCache._delCache(this.session_id, cache, this.cache_hash[cache] ? this.cache_hash[cache].count : 0);
    }
  }

  // delete the reloading entry
  async doneReloading(full_reload = false) {
    if (!this.cache_lock) {
      logging.infoFP(LOG_NAME, 'doneReloading', this.user.profile, `Done ${full_reload ? 'reloading' : 'loading'}, no cache lock not releasing`);
      return;
    }
    const reload = await DataCache.getCache(this.cacheKey(), 'reload');
    const hold: { id: string; start: Date } = reload ? reload.value : { id: null, start: new Date(0) };
    logging.infoFP(LOG_NAME, 'doneReloading', this.user.profile, `Done ${full_reload ? 'reloading' : 'loading'}, releasing ${JSON.stringify(hold)}`);
    this.cache_lock = false;
    await this.delCache('reload').catch(e => {
      logging.errorFP(LOG_NAME, 'doneReloading', this.user.profile, `Error in finishing ${full_reload ? 'reloading' : 'loading'}`, e);
    });
  }

  // clear our cache, will refresh from memcache
  flushCaches(flush_types: EntityType[] = getTypes()) {
    if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    let caches = [];
    if (flush_types.includes(EntityType.Event)) caches = caches.concat(['events'/*, 'people_events'*/]);
    if (flush_types.includes(EntityType.Task)) caches = caches.concat(['tasks'/*, 'people_tasks'*/]);
    if (flush_types.includes(EntityType.Message)) caches = caches.concat(['messages'/*, 'people_messages'*/]);
    if (flush_types.includes(EntityType.Note)) caches = caches.concat(['notes'/*, 'people_notes'*/]);
    if (flush_types.includes(EntityType.Contract)) caches = caches.concat('contracts');
    if (flush_types.includes(EntityType.Project)) caches = caches.concat(['projects' /*, 'people_projects'*/]);
    // if (flush_types.includes(EntityType.Person)) caches = caches.concat(['people', 'people_names', 'org_people']);

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'flushCaches', this.user.profile, `flushing ${JSON.stringify(flush_types)}: ${JSON.stringify(caches)}`);

    for(const cache of caches) {
      // if (Array.isArray(this[cache])) this[cache] = [];
      // else this[cache] = {};

      this.cache_hash[cache] = {hash:null, count:this.cache_hash[cache] ? this.cache_hash[cache].count : 0};
      if (!this.cache_clear.includes(cache)) this.cache_clear.push(cache);
    }

    this.cache_dirty = this.cache_dirty.filter(c => !caches.includes(c));
  }

  groupByEmail(email: string): Group {
    if (email) {
      for (const group of this.groups) {
        if (this.user.groups && this.user.groups[group.id] && group.email_domain) {
          if (Array.isArray(group.email_domain)) {
            if (group.email_domain.find(ed => email.endsWith(ed))) return group;
          } else if ((group.email_domain as string).length && email.endsWith(group.email_domain)) return group;
        }
      }
    }
  }

  getGroup(group_host: Group = null): Group {
    if (group_host) return group_host;
    return this.groupByEmail(this.user.email);
  }

  getNotifyGroup(email: string, group_host: Group): Group {
    if (group_host && group_host.mail_as_group) return group_host;
    const email_group = this.groupByEmail(email);
    return this.getGroup(email_group);
  }

  hashCache(value): string {
    return hash(compress(value));
  }

  async loadSelf() {
    if (!this.user.isGuestAccount() &&
      (!this.me || !this.me.self || !this.me.id.startsWith('people/a') || !this.me.comms || !this.me.comms.includes(this.user.email))
    ) {
      if (this.me && this.me.id && this.me.id !== ANONYMOUS_ID && this.me.id.startsWith('people/a')) {
        const people = await this.loadPeopleIds(this.me.id);
        if (people && people.length) this.me = people[0];

        const email = this.me && this.me.comms ? parsers.findEmail(this.me.comms) : null;
        if (!email || !email.length) {
          this.me = await data.people.byId(this.user, this.me.id);
          if (this.me) {
            this.me.self = true;
            this.me.vanity = this.user.vanity;

            const vanity = await data.plugins.storagePlugin().getVanity(this.me.vanity);
            if (vanity) this.me.bio = vanity.bio;
          }
        }
      }

      const email = this.me && this.me.comms ? parsers.findEmail(this.me.comms) : null;
      if (!email || !email.length || !this.me || !this.me.id || !this.me.id.startsWith('people/a') || this.me.id === ANONYMOUS_ID) {
        const self = await data.people.getUserPerson(this.user, this.user.profile);
        if (self) {
          this.me = self;
          this.me.vanity = this.user.vanity;
          await data.people.save(this.user, self as Person);
        } else logging.warnFP(LOG_NAME, 'loadSelf', this.user.profile, `Error getting loading self`);
      }

      if (this.me) this.me.checkSelf(this.user.profile);
    }
  }

  // load caches that are out of date from memcache
  async loadCache(reload_types: EntityType[] = [], wait_full_reload: Promise<void> = null, stack: string = null, sync_reload = false): Promise<void> {
    logging.infoFP(LOG_NAME, 'loadCache', this.user.profile, `Loading cache for session ${this.session_id} dirty ${JSON.stringify(this.cache_dirty)}`);
    const key = this.cacheKey();

    const release = await this.waitForReload(180000, stack ? stack : new Error().stack);

    // if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Getting cache hash`);
    const ch = await DataCache.getCache(key, 'cache_hash');
    if (ch) {
      if (logging.isVerbose(this.user.profile)) {
        logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Old cache_hash ${JSON.stringify(this.cache_hash)}`);
        logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `New cache_hash ${JSON.stringify(ch.value)}`);
      }
      this.cache_hash = ch.value;
    } else logging.warnFP(LOG_NAME, 'loadCache', this.user.profile, `No cache hash for session ${this.session_id}`);

    const load_caches: string[] = [];
    const reload_caches: string[] = [];

    if (!this.user.isAnonymousAccount()) {
      await this.loadSelf();

      const cache_loads = [];
      for (const cache of _EntityCaches) {
        const c_hash = this[cache] ? this.hashCache(this[cache]) : '';
        if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Checking ${cache} items ${Array.isArray(this[cache]) ? this[cache].length : Object.keys(this[cache]).length} ${this.cache_dirty.includes(cache) ? 'dirty' : ''} hash local: ${c_hash} cached: ${JSON.stringify(this.cache_hash[cache])}`);
        // check if we need to fetch from memcache
        if (!this.cache_hash[cache] || !this.cache_hash[cache].hash || (c_hash !== this.cache_hash[cache].hash && !this.cache_dirty.includes(cache))) {
          if (logging.isVerbose(this.user.profile)) {
            if (this.cache_hash[cache]) logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Loading ${cache} hash:${this.cache_hash[cache].hash} != cache:${c_hash}`);
            else logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Loading ${cache}`);
          }

          cache_loads.push(new Promise<void>(async c => {
            const comp = await DataCache.getCache(key, cache);

            if (comp) {
              load_caches.push(cache);

              // migrating tasks
              if (cache === 'tasks' && Array.isArray(comp.value)) {
                this.tasks = {};
                for (const task of comp.value) this.tasks[task.id] = task;
              } else this[cache] = comp.value;

              if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Loaded ${cache} items ${Array.isArray(this[cache]) ? this[cache].length : Object.keys(this[cache]).length}`);

              if (logging.isVerbose(this.user.profile)) {
                const rehash = this[cache] ? this.hashCache(this[cache]) : '';
                if (this.cache_hash[cache] && this.cache_hash[cache].hash !== rehash) {
                  logging.warnFP(LOG_NAME, 'loadCache', this.user.profile, `Mismatched cache hash ${cache} ${this.cache_hash[cache].hash} ${rehash}`);
                }
              }
              this.cache_hash[cache] = { hash: comp.hash, count: comp.count };
            } else {
              this.cache_hash[cache] = null;
              reload_caches.push(cache);
              const entity_type = _EntityCacheMap[cache];
              if (!reload_types.includes(entity_type)) reload_types.push(entity_type);
            }
            c();
          }));
        }
      }
      if (reload_caches.length) logging.warnFP(LOG_NAME, 'loadCache', this.user.profile, `Missing caches for ${JSON.stringify(reload_caches)} at ${key}, reloading`);
      await Promise.all(cache_loads);
      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Done loading ${load_caches.length} caches`);
      // logging.infoFP(LOG_NAME, 'loadCache', this.user.profile, `Done loading ${cache_loads.length} caches`);
    } else logging.infoFP(LOG_NAME, 'loadCache', this.user.profile, `Not loading caches for anonymous user`);

    if (logging.isVerbose(this.user.profile) && load_caches.length) logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Loaded new values for ${JSON.stringify(load_caches)} session ${this.session_id}`);

    const loaded = await DataCache.getCache(key, 'loaded');
    const cache_loaded = loaded ? new Date(loaded.value) : new Date(0);
    if (this.loaded < cache_loaded) this.loaded = cache_loaded;
    const loaded_people = await DataCache.getCache(key, 'loaded_people');
    this.loaded_people = loaded_people ? new Date(loaded_people.value) : new Date();
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadCache', this.user.profile, `Loaded ${this.loaded.getTime()} people ${this.loaded_people.getTime()} now ${new Date().getTime()}`);

    if (reload_types.length) {
      logging.infoFP(LOG_NAME, 'loadCache', this.user.profile, `Reloading cache for ${JSON.stringify(reload_caches)} session ${this.session_id}`);
      // logging.warnFP(LOG_NAME, 'loadCache', this.user.profile, `Reloading cache for ${JSON.stringify(reload_types)}`);
      await this.reloadCache(reload_types, true, wait_full_reload, sync_reload);
    } else if (this.user.isAuthenticatedNonGuest()) await this.reloadCache(null, false, wait_full_reload, sync_reload);

    if (release) this.doneReloading();
  }

  async loadContracts() {
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadContracts', this.user.profile, 'Loading Contracts');
    if (!this.read_only) await data.users.refreshClear(this.user, EntityType.Contract);
    const start = new Date().getTime();
    this.contracts = {};
    const contractsList: Contract[] = await data.contracts.load(this.user);
    contractsList.forEach((contract: Contract) => {
      this.contracts[contract.id] = contract;
    });
    if (!this.read_only) this.cacheDirty('contracts');
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadContracts', this.user.profile, `Done loading Contracts in ${new Date().getTime() - start}`);
  }

  // load tasks from db
  async loadEvents(when: Date = null) {
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadEvents', this.user.profile, 'Loading Events');
    if (!this.read_only) await data.users.refreshClear(this.user, EntityType.Event);
    const start = new Date().getTime();
    const event_list = [];
    // const people_events = {};
    const cache_people = [];

    let events: Event[] = null;
    if (when && !config.isEnvOffline()) events = await data.events.eventsByDate(this.user, when);
    else events = await data.events.load(this.user);

    if (events) {
      events.forEach((event: Event) => {
        event.start = new Date(event.start);
        event.end = new Date(event.end);

        if (event.people) {
          for (const person of event.people) {
            if (!person.self) {
              if (!person.id) logging.warnFP(LOG_NAME, 'loadEvents', this.user.profile, `Found event ${event.id} person without id: ${JSON.stringify(person, null, 2)}`);
              else if (!person.self) {
                let [_d, n, _p] = permuteName(person.displayName);
                if (person.names) n = n.concat(person.names);
                if (!n.includes('Holidays')) {
                  event_list.push(event);
                }
              }
            }
            if (!cache_people.includes(person.id)) cache_people.push(person.id);
          }
        } else event_list.push(event);
      });
    }

    this.events = sortEvents(event_list);
    // this.people_events = people_events;

    if (!this.read_only) this.cacheDirty(['events' /*, 'people_events'*/]);
    this.loadPeopleIds(cache_people).catch(e => {
      logging.errorFP(LOG_NAME, 'loadEvents', this.user.profile, 'Error loading people for contracts', e);
    });

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadEvents', this.user.profile, `Done loading Events in ${new Date().getTime() - start}`);
  }

  // load emails from db
  async loadMessages(when: Date = null) {
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadMessages', this.user.profile, 'Loading Messages');
    if (!this.read_only) await data.users.refreshClear(this.user, EntityType.Message);
    const start = new Date().getTime();

    const message_list = [];
    // const people_messages = {};
    const cache_people = [];

    let messages: Message[] = null;
    if (when && !config.isEnvOffline()) messages = await data.messages.messagesByDate(this.user, when, when);
    else messages = await data.messages.load(this.user);

    messages.forEach((message: Message) => {
      message.received = new Date(message.received);
      message_list.push(message);
      if (logging.isVerbose(this.user.profile)) DEBUG('loadMessages: processing %j', message);
      if (message.sender) {
        if (!cache_people.includes(message.sender.id)) cache_people.push(message.sender.id);
      }

      if (message.recipient) {
        for (const person of message.recipient) {
          if (!person.self) {
            if (person.id) {
              if (!cache_people.includes(person.id)) cache_people.push(person.id);
            } else logging.warnFP(LOG_NAME, 'loadMessages', this.user.profile, `Found message ${message.id} with recipient without id: ${JSON.stringify(person, null, 2)}`);
          }
        }
      }
    });

    this.messages = sortMessages(message_list);
    // this.people_messages = people_messages;

    if (!this.read_only) this.cacheDirty(['messages' /*, 'people_messages'*/]);
    this.loadPeopleIds(cache_people).catch(e => {
      logging.errorFP(LOG_NAME, 'loadMessages', this.user.profile, 'Error loading people for messages', e);
    });

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadMessages', this.user.profile, `Done loading Messages in ${new Date().getTime() - start}`);
  }

  async loadNotes() {
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadNotes', this.user.profile, 'Loading Notes');
    if (!this.read_only) await data.users.refreshClear(this.user, EntityType.Note);
    const start = new Date().getTime();
    const people_notes = {};
    const notes = {};
    const notes_list: Note[] = await data.notes.load(this.user);
    const load_people_ids = [];
    for (const note of notes_list) {
      notes[note.id] = note;

      if (note.people) {
        for (const person of note.people) {
          if (!load_people_ids.includes(person.id)) load_people_ids.push(person.id);
          peopleUtils.savePeopleMatch(people_notes, person.id, note.id);
        }
      }
    }

    this.notes = notes;
    // this.people_notes = people_notes;

    if (!this.read_only) this.cacheDirty(['notes', 'people_notes']);
    this.loadPeopleIds(load_people_ids).catch(e => {
      logging.errorFP(LOG_NAME, 'loadNotes', this.user.profile, 'Error loading people for notes', e);
    });

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadNotes', this.user.profile, `Done loading Notes in ${new Date().getTime() - start}`);
  }

  // load people from db
  async loadPeople(r = 1000) {
    if (this.read_only) return;

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, 'Waiting to Load People');
    if (this.people_loaded) await this.people_loaded;

    await data.users.refreshClear(this.user, EntityType.Person);

    let done;
    this.people_loaded = new Promise(c => done = c);
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, 'Loading People');
    let start = new Date().getTime();

    const people_names = {};
    const org_people = {};

    let people: Partial<Person>[] = null;
    if (!config.isEnvOffline() && this.user.isAuthenticatedNonGuest() && SourceController.firstRunDone(this.user, EntityType.Person) && this.user.exported) {
      try { 
        people = await data.people.getNamesAndOrgs(this.user, false); 
        if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, `Loaded ${people.length} people from BigQuery in ${new Date().getTime() - start}`); 
      } catch (e) { logging.warnFP(LOG_NAME, 'loadPeople', this.user.profile, 'Cannot load people names from big query, using datastore', e); }
    }

    if (!people || !people.length) people = await data.people.load(this.user, ['id', 'displayName', 'names', 'tags', 'comms']);

    if (!people) {
      logging.warnFP(LOG_NAME, 'loadPeople', this.user.profile, `Cannot load people names from datastore, retrying in ${r}ms`);
      this.people_loaded = null;
      done();
      await setTimeout(r);
      return this.loadPeople(r * 2);
    }

    const load_time = new Date().getTime() - start;
    start = new Date().getTime();
    const cache_people = people.slice();
    while(cache_people.length) {
      await Promise.all(cache_people.splice(0,50).map(async person => {
        try {
          await data.plugins.cachePlugin().clearCacheAttVal(this.user.profile, EntityType.Person, 'id', person.id);
          peopleUtils.mapPersonDetails(people_names, org_people, person);
        } catch (err) {
          logging.errorFP(LOG_NAME, 'loadPeople', this.user.profile, 'Error loading people', err);
        }
      }));
    }
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, `Mapped ${people.length} people to ${Object.keys(people_names).length} names and ${Object.keys(org_people).length} orgs in ${new Date().getTime() - start}`); 

    const updates = [];

    for (const name in people_names) {
      updates.push(await data.plugins.cachePlugin().cacheAttVal(this.user.profile, EntityType.Person, 'people_names', name, people_names[name], this.cacheExpires));
    }

    for (const org in org_people) {
      updates.push(await data.plugins.cachePlugin().cacheAttVal(this.user.profile, EntityType.Organization, 'org_people', org, org_people[org], this.cacheExpires));
    }

    await Promise.all(updates);

    /* this.people_names = people_names;
    this.org_people = org_people;*/

    // this.cacheDirty('people_names');
    // this.cacheDirty('org_people');
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, 'Saving cache');
    let release = false;
    try {
      release = await this.waitForReload(180000, new Error().stack);
      await this._saveCache(this.cacheExpires());

      this.loaded_people = new Date();
      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, `Saving loaded ${this.loaded_people.getTime()}`);
      await this.setCache(this.cacheKey(), 'loaded_people', this.loaded.getTime()).catch(e => {
        logging.errorFP(LOG_NAME, 'loadPeople', this.user.profile, 'Error setting loaded in memcache', e);
      });

      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, 'Saving user');
      if (this.user.isAuthenticatedNonGuest()) await data.users.quickSave(this.user);

      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, `Reloaded people at ${this.loaded_people.getTime()} last refresh ${new Date(this.user.last_refresh).getTime()}`);
      if (release) this.doneReloading(true);
    } catch(e) {
      logging.errorFP(LOG_NAME, 'loadPeople', this.user.profile, 'Error reloading people cache', e);
      if (release) this.doneReloading(true);
    }

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeople', this.user.profile, `Done loading ${people.length} People and ${Object.keys(people_names).length} names in ${new Date().getTime() - start + load_time}`);
    done();

    // TODO - look for people we don't know who are scheduled in events, emailed, etc.
  }

  async mapPersonDetails(person: Partial<Person>, oldid: Uid = null) {
    const people_names: NameMap = {}; // { id: string } } = {}; // {name:{id:displayName}o
    const org_people: NameMap = {}; // {org:[person_id]}

    peopleUtils.mapPersonDetails(people_names, org_people, person);

    const updates = [];

    for (const name in people_names) {
      const pn = await this.peopleName(name);
      if (pn) {
        for (const id in pn) {
          if (!(id in people_names[name])) people_names[name][id] = pn[id];
        }
      } 
      if (oldid && oldid in people_names[name]) delete people_names[name][oldid];
      updates.push(data.plugins.cachePlugin().cacheAttVal(this.user.profile, EntityType.Person, 'people_names', name, people_names[name], this.cacheExpires));
    }

    for (const org in org_people) {
      const og = await this.orgPeople(org);
      if(og) {
        for (const id in og) {
          if (!(id in org_people[org])) org_people[org][id] = og[id];
        }
        if (oldid && oldid in org_people[org]) delete org_people[org][oldid];
      }
      updates.push(data.plugins.cachePlugin().cacheAttVal(this.user.profile, EntityType.Organization, 'org_people', org, org_people[org], this.cacheExpires));
    }

    await Promise.all(updates);
  }

  // Map of person.id to displayName
  async peopleName(name: string): Promise<{[key:string]: string}> {
    if (config.isEnvOffline() && this.people_names) return this.people_names[name];
    const people_name = (await data.plugins.cachePlugin().lookupCacheAttVal(this.user.profile, EntityType.Person, 'people_names', name)) as {[key:string]: string};
    return people_name;
  }

  // Map of person.id to displayName
  async orgPeople(org: string): Promise<{[key:string]: string}> {
    if (config.isEnvOffline() && this.orgs) return this.orgs.includes(org) ? {} : null;
    const org_people = (await data.plugins.cachePlugin().lookupCacheAttVal(this.user.profile, EntityType.Organization, 'org_people', org)) as {[key:string]: string};
    return org_people;
  }

  async loadPeopleIds(ids: Uid | Uid[], cache = true): Promise<Person[]> {
    if (!ids) throw new Error('loadPeopleIds: Missing ids');
    if (!(ids instanceof Array)) ids = [ids];
    else {
      const f = ids.filter(i => i && i !== ANONYMOUS_ID);
      if (f.length !== ids.length) {
        logging.warnFP(LOG_NAME, 'loadPeopleIds', this.user.profile, `Incoming ids = ${util.format(ids)}`);
        throw new Error('loadPeopleIds: Missing some ids');
      }
    }

    try {
      let people: Person[] = [];
      const matched: Person[] = [];
      const not_matched: Uid[] = [];

      // check the cache
      for (const id of ids) {
        let person: Person;
        if (id === this.me.id || (id.startsWith('profile/') && id.split('/')[1] === this.me.vanity)) person = new Person(this.me);
        else if (id.startsWith('people/f')) person = lang.about.ABOUT_INFO.find(p => p.id === id);
        else {
          person = await data.plugins.cachePlugin().lookupCacheAttVal(this.user.profile, EntityType.Person, 'id', id);
          if (person && person.tags && person.tags.length) {
            const rperson = new Person(person);
            if (person.self || (this.me && this.me.id === rperson.id)) {
              rperson.self = true;
              rperson.bio = this.user.bio;
              rperson.vanity = this.user.vanity;
            }
            person = rperson;
            if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadPeopleIds', this.user.profile, `Found ${id} in the cache`);
          } else person = null;
        } 

        if (person && person.displayName) matched.push(person);
        else not_matched.push(id);
      }

      // return if everyone's in the cache
      if (not_matched.length)  {
        const people_matches = await data.people.byAttributeId(this.user, not_matched);
        for (const person of people_matches) {
          if (person.self || (this.me && this.me.id === person.id)) {
            person.self = true;
            person.bio = this.user.bio;
            person.vanity = this.user.vanity;
          }
          if (!person.id && person.vanity) person.id = `profile/${person.vanity}`;
          people.push(new Person(person));
        }
      }

      if (people.length) {
        await data.people.updatePeopleWithProfiles(this.user, people);
        if (cache) {
          people.filter(p => p.id?.startsWith('people/')).forEach(p => this.cachePerson(p));
        }
      }

      const lookup = matched.filter(p => !p.network || !p.vanity).map(p => p.id);
      const lookup_ids = lookup.length ? await data.plugins.storagePlugin().peopleCheckIds(this.user, lookup) : [];

      const result_people = [...people, ...matched.filter(p => p.network && p.vanity), ...matched.filter(p => lookup_ids.includes(p.id))];

      let m_id = 0;
      for (let i = 0; i < result_people.length; i++) {
        if(!result_people[i].id || !ids.includes(result_people[i].id)) m_id = i;
      }

      const sorted_people = result_people.sort((a,b) => {
        let a_id = ids.indexOf(a.id);
        let b_id = ids.indexOf(b.id);
        if (a_id === -1) {
          if (a.vanity) a_id = ids.indexOf(`profile/${a.vanity}`);
          if (a_id === -1) a_id = m_id;
        }
        if (b_id === -1) {
          if (b.vanity) b_id = ids.indexOf(`profile/${b.vanity}`);
          if (b_id === -1) b_id = m_id;
        }
        return a_id - b_id;
      });

      return sorted_people;

    } catch (err) {
      logging.errorFP(LOG_NAME, 'loadPeopleIds', this.user.profile, 'Error', err);
    }

    return [];
  }

  async loadProjects(do_cache = false ) {
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadProjects', this.user.profile, 'Loading Projects');
    if (!this.read_only) await data.users.refreshClear(this.user, EntityType.Project);

    const start = new Date().getTime();
    const projects = {};
    //const people_projects = {};
    const cache_people = [];

    let found_projects = await data.projects.load(this.user);
    let project_admin = [];

    if (this.user.loaded_groups) {
      for (const group_id in this.user.loaded_groups) {
        if (this.user.isAdmin(group_id)) {
          const filter = {name: 'groups', op: '=', val: group_id};
          const keys = await data.plugins.storagePlugin().findKeysByFilter(null, GlobalType.User, [filter]);
          while(keys.length) {
            await Promise.all(keys.splice(0,20).map(k => k.name).map(async user_id => {
              const load_user = new ForaUser(user_id);
              const group_projects = await data.projects.load(load_user);
              found_projects = found_projects.concat(group_projects);
              project_admin = project_admin.concat(group_projects.map(p => p.id));
            }));
          }
        }
      }
    }

    for (const project of found_projects) {
      if (!project.client) {
        logging.warnFP(LOG_NAME, 'loadProjects', this.user.profile, `Found project with no client ${project.id}`);
        continue;
      }

      this.projectRole(this.user, project, this.me);

      if (!project.client.self && (!project.candidates || !project.candidates.filter(c => c.askfora_id).map(c => c.askfora_id).includes(this.user.profile)) && project_admin.includes(project.id)) 
         project.admin = true;

      if (!project.client.self && !project.me_candidate && !project.admin) {
        if ((this.user && project.client.askfora_id === this.user.profile) || 
          (this.me && project.client.id === this.me.id)) {
            logging.warnFP(LOG_NAME, 'loadProject', this.user.profile, `Project doesn't belong ${project.id} to ${this.me ? this.me.id : 'no one'} - fixing`);
            project.client.self = true;
          } else {
            logging.warnFP(LOG_NAME, 'loadProject', this.user.profile, `Project doesn't belong ${project.id} to ${this.me ? this.me.id : 'no one'} - not fixing because ids don't match`);
          }
      }
      projects[project.id] = project;

      let people = [];
      if (project.client) {
        if (project.client.self) {
          people = project.candidates ? project.candidates.slice() : [];
          if (project.contractor) people.push(project.contractor);
        } else people = [project.client];
      }
    }

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadProjects', this.user.profile, `Found projects = ${logging.formatAny(projects)}`);
    this.projects = projects;
    // this.people_projects = people_projects;

    if (!this.read_only) this.cacheDirty(['projects'/*, 'people_projects'*/]);
    if (cache_people.length && do_cache) {
        this.loadPeopleIds(cache_people).catch(e => {
        logging.errorFP(LOG_NAME, 'loadProjects', this.user.profile, 'Error loading people for projects', e);
      });
    }

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadProjects', this.user.profile, `Done loading Projects in ${new Date().getTime() - start}`);
  }

  // load tasks from db
  async loadTasks(when: Date = null) {
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadTasks', this.user.profile, 'Loading Tasks');
    if (!this.read_only) await data.users.refreshClear(this.user, EntityType.Task);

    const start = new Date().getTime();
    const new_tasks = {};
    //const people_tasks = {};

    let tasks: Task[] = null;
    if (when && !config.isEnvOffline()) tasks = await data.tasks.tasksByDate(this.user, when, when);
    else tasks = await data.tasks.load(this.user);

    tasks.forEach((task: Task) => {
      task.due = new Date(task.due);
      new_tasks[task.id] = task; // task_list.push(task);

      /*if (task.people) {
        for (const person of task.people) {
          if (person && person.id) peopleUtils.savePeopleMatch(people_tasks, person.id, task.id);
        }
      }*/
    });

    this.tasks = new_tasks; // funcs.sortTasks(task_list);
    // this.people_tasks = people_tasks;

    if (!this.read_only) this.cacheDirty(['tasks'/* , 'people_tasks'*/]);

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'loadTasks', this.user.profile, `Done loading Tasks in ${new Date().getTime() - start}`);
  }

  projectRole(user: ForaUser, proj: Partial<Project>, me: Partial<Person>) {
    if (proj.client /*&& !proj.client.askfora_id*/ && proj.proposal && proj.client.comms &&  me &&
      ( proj.client.comms.includes(user.email) || arraysIntersect(proj.client.comms, me.comms)) &&
      ( !proj.contractor || proj.contractor.askfora_id !== user.profile )
      ) {
        proj.client.askfora_id = user.profile;
        proj.client.id = me.id;
    }

    if (proj.client && (proj.client.askfora_id === user.profile || (proj.client.askfora_id == FORA_PROFILE && user.create_guest))) {
      proj.client.self = true;
      proj.client.comms = [user.email];
      if (me) proj.client.id = me.id;
      if (proj.contractor && proj.contractor.self) delete proj.contractor.self;
      if (proj.me_candidate && proj.me_candidate.self) delete proj.me_candidate.self;
      if (proj.candidates) for (const candidate of proj.candidates) delete candidate.self;
    } else if (proj.contractor && proj.contractor.askfora_id === user.profile) {
      proj.contractor.self = true;
      proj.me_candidate = new Person(proj.contractor);
      proj.me_candidate.self = true;
      proj.me_candidate.state = proj.contractor.state;
      proj.me_candidate.answer = proj.contractor.answer;
      proj.me_candidate.ready = proj.contractor.ready;
      proj.me_candidate.askfora_id = user.profile;
      proj.me_candidate.comms = [user.email];
      if (me) proj.me_candidate.id = me.id;
      slimEntity(proj.me_candidate);
      if (proj.client && proj.client.self) delete proj.client.self;
    } else {
      if (proj.client && proj.client.self) delete proj.client.self;
      if (proj.contractor && proj.contractor.self) delete proj.contractor.self;

      let comms = [user.email];
      if (me) comms = _.uniq(comms.concat(me.comms));

      if (proj.candidates && !proj.me_candidate) {
        for (const candidate of proj.candidates) {
          for (const comm of comms) {
            if (candidate.comms.includes(comm)) {
              proj.me_candidate = new Person(candidate);
              proj.me_candidate.state = candidate.state;
              proj.me_candidate.answer = candidate.answer;
              proj.me_candidate.ready = candidate.ready;
              proj.me_candidate.comms = [user.email];
              proj.me_candidate.askfora_id = user.profile;
              proj.me_candidate.self = true;
              if (me) proj.me_candidate.id = me.id;
              break;
            }
          }

          if (proj.me_candidate) break;
          delete candidate.self;
        }
      }
    }
  }

  async purgePersonCache(person: Partial<Person> = null) {
    if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    if (person === null) this.flushCaches([EntityType.Person]);
    else {
      // delete this.people[person.id];
      await data.plugins.cachePlugin().clearCacheAttVal(this.user.profile, EntityType.Person, 'id', person.id);
      if (person.vanity) await data.plugins.cachePlugin().clearCacheAttVal(this.user.profile, EntityType.Person, `id`, `profile/${person.vanity}`);
      if (person.comms) {
        for (const comm of person.comms) {
          await data.plugins.cachePlugin().clearCacheAttVal(this.user.profile, EntityType.Person, 'comms', comm);
        }
      }
      this.removePersonDetails(person);
      // this.cacheDirty(['people_names', 'org_people']);
    }
  }

  async removePersonDetails(person: Partial<Person>) {
    let updates = [];

    const name_set = wordSet(person.names, true, STRIP_REGEX);
    for (const index in name_set) {
      const name = name_set[index];
      const people_names = await this.peopleName(name);
      if (people_names[name] && people_names[name][person.id]) {
        delete people_names[name][person.id];
        updates.push(data.plugins.cachePlugin().cacheAttVal(this.user.profile, EntityType.Person, 'people_names', name, people_names[name], this.cacheExpires));
      }
    }

    const orgs = person.tags ? findTypeValues(person.tags, TagType.organization).filter(o => o) : [];
    for (const index in orgs) {
      const org = orgs[index].toLowerCase();
      const org_people = await this.orgPeople(org);
      if (org_people[org] && org_people[org][person.id]) {
        delete org_people[org][person.id];
        updates.push(data.plugins.cachePlugin().cacheAttVal(this.user.profile, EntityType.Organization, 'org_people', org, org_people[org], this.cacheExpires));
      }
    }

    const org_set = wordSet(orgs, true, STRIP_REGEX);
    for (const index in org_set) {
      const org = org_set[index];
      const org_people = await this.orgPeople(org);
      if (org_people[org] && org_people[org][person.id]) {
        delete org_people[org][person.id];
        updates.push(data.plugins.cachePlugin().cacheAttVal(this.user.profile, EntityType.Organization, 'org_people', org, org_people[org], this.cacheExpires));
      }
    }

    await Promise.all(updates);
  }

  async refreshCache(refresh_types: EntityType|EntityType[] = null) {
    if (!refresh_types) refresh_types = getTypes();
    await data.users.refreshSet(this.user, refresh_types);
  }

  async reloadCache(reload_types: EntityType|EntityType[] = null, force = false, wait_full_reload: Promise<void> = null, sync_reload = false): Promise<void> {
    // if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Reload ${reload_types}${force ? ' force': ''}`);
    let release = false;
    const key = this.cacheKey();
    const now = new Date();

    try {
      // Can't have an async call inside a filter
      // if (!force) reload_types = reload_types.filter(t => data.refreshNeeded(this.user, t) >= this.loaded);
      if (!reload_types) reload_types = getTypes(); // _EntityCaches;
      else if (!Array.isArray(reload_types)) reload_types = [reload_types];
      const reload_types_filtered = [];
      if (!force) {
        release = await this.waitForReload(180000, new Error().stack);
        const loaded = await DataCache.getCache(key, 'loaded');
        const loaded_people = await DataCache.getCache(key, 'loaded_people');
        if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Compare loaded this: ${this.loaded.getTime()} cache: ${loaded ? new Date(loaded.value).getTime() : null} people: ${this.loaded_people.getTime()} people_cache: ${loaded_people ? new Date(loaded_people.value).getTime() : null}`);
        // this.loaded = new Date(loaded ? loaded.value : 0);
        this.loaded_people = new Date(loaded_people ? loaded_people.value : 0);

        for (const entity_type of reload_types) {
          const refreshed_date = await data.users.refreshNeeded(this.user, entity_type);
          if (entity_type === EntityType.Person) {
            if (refreshed_date.getTime() && logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `${entity_type} refreshed: ${refreshed_date.getTime()} loaded: ${this.loaded_people.getTime()}`);
            if (refreshed_date >= this.loaded_people) reload_types_filtered.push(entity_type);
          } else {
            if (refreshed_date.getTime() && logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `${entity_type} refreshed: ${refreshed_date.getTime()} loaded: ${this.loaded.getTime()}`);
            if (refreshed_date >= this.loaded) reload_types_filtered.push(entity_type);
          }
        }
        reload_types = reload_types_filtered;
      } else if (reload_types.length) release = await this.waitForReload(90000, new Error().stack);

      if (reload_types.length) {
        if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Reloading caches: ${force ? 'force ' : ' '}${reload_types}`);

        const loaders = [];
        let when = null;

        // first load, do it in two stages
        if (!this.tasks.length && !this.events.length && !this.messages.length) when = new Date();

        const start = new Date().getTime();
        this.me = await data.people.getUserPerson(this.user, this.user.profile);

        if (logging.isVerbose(this.user.profile)) {
          if (this.me) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Updated me to ${this.me.id} in ${new Date().getTime() - start}`); 
          else logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `No me after ${new Date().getTime() - start}`); 
        }

        /*
        if (!this.user.isGuestAccount() && !this.user.isAnonymousAccount()) {
          // safe to reload everything
          if (reload_types.includes(EntityType.Person)) this.loadPeople(); // loaders.push(this.loadPeople());
          if (reload_types.includes(EntityType.Event)) loaders.push(this.loadEvents(when));
          if (reload_types.includes(EntityType.Task)) loaders.push(this.loadTasks(when));
          if (reload_types.includes(EntityType.Message)) loaders.push(this.loadMessages(when));
          // if (reload_types.includes(EntityType.Note)) loaders.push(this.loadNotes());
          if (reload_types.includes(EntityType.Contract)) loaders.push(this.loadContracts());
          if (reload_types.includes(EntityType.Project)) loaders.push(this.loadProjects()); //true));
        }

        if (loaders.length) { 
          await Promise.all(loaders).catch(err => { logging.errorFP(LOG_NAME, 'reloadCache', this.user.profile, 'Error reloading data', err); });
        }*/

        try {
          if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, 'Saving cache');
          // await this.waitForReload(180000, new Error().stack);
          await this._saveCache(this.cacheExpires());

          this.loaded = now;
          if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `saving loaded ${this.loaded.getTime()}`);
          if (!this.read_only) {
            await this.setCache(key, 'loaded', this.loaded.getTime()).catch(e => {
              logging.errorFP(LOG_NAME, 'reloadCache', this.user.profile, 'Error setting loaded in memcache', e);
            });
          }

          if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, 'Saving user');
          if (this.user.isAuthenticatedNonGuest()) await data.users.quickSave(this.user);
          // await this.saveSession();

          if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Reloaded caches at ${this.loaded} last refresh ${new Date(this.user.last_refresh)}`);
        } catch(e) {
          logging.errorFP(LOG_NAME, 'reloadCache', this.user.profile, 'Error reloading initial cache', e);
        }

        // completely async, go cache some people
        if(this.user.isAuthenticatedNonGuest()) {
          if (config.isEnvOffline()) {
            await Promise.all([
              // this.cachePeople(),
              this.loadEvents(),
              this.loadTasks(),
              this.loadMessages(),
              this.loadProjects(true),
            ]);
          } else if (!this.read_only) {
            const full_reload = new Promise<void>(async (c) => {
              if (wait_full_reload) {
                if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Waiting full reload at ${this.loaded} last refresh ${new Date(this.user.last_refresh)}`);
                await wait_full_reload;
              }

              const reloaders = [];
              // if (reload_types.includes(EntityType.Person)) reloaders.push(this.cachePeople());
              // if (reload_types.includes(EntityType.Project)) reloaders.push(this.loadProjects());
              if (when && reload_types.includes(EntityType.Event)) reloaders.push(this.loadEvents());
              if (when && reload_types.includes(EntityType.Task)) reloaders.push(this.loadTasks());
              if (when && reload_types.includes(EntityType.Message)) reloaders.push(this.loadMessages());

              if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Waiting for full reload caches at ${this.loaded} last refresh ${new Date(this.user.last_refresh)}`);
              await Promise.all(reloaders);
              await this.waitForReload(180000, new Error().stack);
              try { 
                if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, 'Saving cache after full reload');
                await this._saveCache(this.cacheExpires()).catch(e => logging.errorFP(LOG_NAME, 'reloadCache', this.user.profile, 'Error saving cache after caching people', e));

                this.loaded = now;
                if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Saving loaded ${this.loaded.getTime()}`);
                await this.setCache(key, 'loaded', this.loaded.getTime()).catch(e => {
                  logging.errorFP(LOG_NAME, 'reloadCache', this.user.profile, 'Error setting loaded in memcache', e);
                });

                // if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, 'Saving user');
                // await data.users.quickSave(this.user);

                if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Full reloaded caches at ${this.loaded} last refresh ${new Date(this.user.last_refresh)}`);
              } catch(e) {
                logging.errorFP(LOG_NAME, 'reloadCache', this.user.profile, `Error reloading caches at ${this.loaded} last refresh ${new Date(this.user.last_refresh)}`, e);
              }
              if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'reloadCache', this.user.profile, `Done full reload caches at ${this.loaded} last refresh ${new Date(this.user.last_refresh)}`);
              await this.doneReloading(true);
              c();
            }).catch(e => {
              logging.errorFP(LOG_NAME, 'reloadCache', this.user.profile, 'Error reloading full cache', e);
              this.doneReloading(true);
            });

            if (sync_reload) await full_reload;
          }
        }
      }
    } catch (err) {
      if (release) await this.doneReloading(true);
      throw err;
    }

    if (release) await this.doneReloading(true);
  }

  async remapPersonCache(oldid: Uid, new_person: Person) {
    if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    const newid = new_person.id;
    if (!newid) throw new InternalError(401, 'Cannot update person without id', new_person);
    if (oldid === newid) return;
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'remapPersonCache', this.user.profile, `Remapping ${oldid} to ${newid}`);
    // const new_person = new Person(await data.plugins.cachePlugin().lookupCacheAttVal(this.user.profile, EntityType.Person, 'id', newid));

    /*peopleUtils.remapPersonMap(this.people_events, oldid, newid);
    peopleUtils.remapPersonMap(this.people_tasks, oldid, newid);
    peopleUtils.remapPersonMap(this.people_messages, oldid, newid);
    peopleUtils.remapPersonMap(this.people_notes, oldid, newid);
    peopleUtils.remapPersonMap(this.people_projects, oldid, newid);*/

    await this.mapPersonDetails(new_person, oldid);
    // this.cacheDirty(['people_names', 'org_people']);

    const saves = [];

    // events.people
    for (const event of this.events.filter(e => e.people)) {
      const person = event.people.find(p => p.id === oldid);
      if (person) {
        person.id = newid;
        this.cacheDirty(['events'/*, 'people_events'*/]);
        saves.push(data.events.save(this.user, event));
      }
    }

    // messages.sender
    // messages.recipient
    for (const message of this.messages) {
      let changed = false;
      if (message.recipient) {
        const person = message.recipient.find(p => p.id === oldid);
        if (person) {
          person.id = newid;
          person.displayName = new_person.displayName;
          person.comms = new_person.comms;
          changed = true;
        }
      }

      if (message.sender.id === oldid) {
        message.sender.id = newid;
        message.sender.displayName = new_person.displayName;
        message.sender.comms = new_person.comms;
        changed = true;
      }

      if (changed) {
        this.cacheDirty(['messages'/*, 'people_messages'*/]);
        saves.push(data.messages.save(this.user, message));
      }
    }

    // tasks.title
    // tasks.note
    for (const task of Object.values(this.tasks).filter(t => t.people)) {
      const person = task.people.find(p => p.id === oldid);
      if (person) {
        person.id = newid;
        person.displayName = new_person.displayName;
        person.comms = new_person.comms;
        this.cacheDirty(['tasks'/*, 'people_tasks'*/]);
        saves.push(data.tasks.save(this.user, task));
      }
    }

    /* const change_notes = this.people_notes[newid];
    if (change_notes) {
      for (const index in change_notes) {
        const note_id = change_notes[index];
        const note = this.notes[note_id];
        if (note.people) {
          for (const pindex in note.people) {
            if (note.people[pindex].id === oldid) {
              note.people[pindex].id = newid;
              this.cacheDirty(['notes', 'people_notes']);
              saves.push(data.notes.save(this.user, note));
              break;
            }
          }
        }
      }
    }*/

    for (const project of Object.values(this.projects)) {
      if (project.client && project.client.id === oldid) {
        project.client.id = newid;
        project.client.comms = new_person.comms;
        project.client.displayName = new_person.displayName;
      }

      if (project.contractor && project.contractor.id === oldid) {
        project.contractor.id = newid;
        project.contractor.comms = new_person.comms;
        project.contractor.displayName = new_person.displayName;
      }

      if (project.candidates) {
        for (const candidate of project.candidates) {
          if (candidate.id === oldid) {
            candidate.id = newid;
            candidate.comms = new_person.comms;
            candidate.displayName = new_person.displayName;
            break;
          }
        }
      }
    }

    await Promise.all(saves);
  }

  async saveCache(expires: number = MAX_EXPIRES) {
    if (this.read_only) throw new InternalError( 400, 'Cannot update readonly cache');
    this._saveCache(expires);
  }

  async _saveCache(expires: number = MAX_EXPIRES) {
    const cache_saves = [];
    const key = this.cacheKey();
    if (this.cache_dirty.length) { if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'saveCache', this.user.profile, `Saving ${key} dirty ${JSON.stringify(this.cache_dirty)}`); }
    for (const cache of this.cache_dirty) {
      if (!this[cache]) throw new Error(`saveCache: can't save unknown dirty cache ${cache}`);

      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'saveCache', this.user.profile, `Saving cache ${cache} ${JSON.stringify(this[cache]).length} bytes`);
      cache_saves.push(new Promise<void>(async c => {
        this.cache_hash[cache] = await this.setCache(key, cache, this[cache], expires);
        c();
      }));
    }

    await Promise.all(cache_saves);
    this.clearCacheDirty(); // cache_dirty = [];

    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'saveCache', this.user.profile, `Save cache_hash ${JSON.stringify(this.cache_hash)}`);

    await this.setCache(key, 'cache_hash', this.cache_hash, expires).catch(e => {
      logging.errorFP(LOG_NAME, 'saveCache', this.user.profile, 'Error in clearing cache_hash', e);
    });
  }

  async waitForReload(timeout_ms, cstack: string = null) {
    if (this.cache_lock) return false;
    const stack = formatStack(cstack ? cstack :  new Error().stack);
    const timeout = SECONDS(new Date(), timeout_ms / 1000);
    const key = this.cacheKey();
    const id = uuid();

    let reload = await DataCache.getCache(key, 'reload');
    if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'waitForReload', this.user.profile, `Reload ${key} = ${JSON.stringify(reload)}`);

    let hold: { id: string; start: Date; stack: string[] } = reload ? reload.value : { id, start: new Date(0), stack: null };
    hold.start = new Date(hold.start);

    const now = new Date();

    // max 5 minute lock
    if (hold.start.getTime() && (hold.start < config.loadTime() || hold.start < MINUTES(now, -5))) {
      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'waitForReload', this.user.profile, `Clearing outdated cache ${id} hold from ${hold.start} ${hold.stack}`);
      hold.start = new Date(0);
    }

    // timeout after max wait
    const max_wait = SECONDS(hold.start, timeout_ms / 1000);
    if (logging.isVerbose(this.user.profile) && hold.start.getTime() && max_wait > now) logging.verboseFP(LOG_NAME, 'waitForReload', this.user.profile, `Waiting ${id} to reload from ${JSON.stringify(hold)}`);

    let wait = 10;
    while (hold.start.getTime() && max_wait > new Date()) {
      if (timeout < new Date()) {
        this.timed_out = true;
        throw new Error(`waitForReload: timeout ${id} waiting for hold ${JSON.stringify(hold)} ${stack}`);
      }

      await setTimeout(wait);

      wait *= 2;
      reload = await DataCache.getCache(key, 'reload');
      hold = reload ? reload.value : { id, start: new Date(0) };
      hold.start = new Date(hold.start);
    }

    if (hold.start.getTime() && max_wait < new Date()) {
      this.timed_out = true;
      throw new Error(`waitForReload: max time ${id} waiting for hold ${JSON.stringify(hold)} ${max_wait} < ${new Date()} ${stack}`);
    } else if (logging.isVerbose(this.user.profile) && reload) logging.verboseFP(LOG_NAME, 'waitForReload', this.user.profile, `After ${wait} ${key} = ${JSON.stringify(reload)}`);

    const start = new Date();

    await this.setCache(key, 'reload', { id, start, stack }, 300).catch(e => {
      logging.errorFP(LOG_NAME, 'waitForReload', this.user.profile, `Error setting reload ${id} ${start} in memcache`, e);
    });

    reload = await DataCache.getCache(key, 'reload');
    hold = reload ? reload.value : { id, start: new Date(0), stack: null };
    hold.start = new Date(hold.start);

    if (hold.id !== id) {
      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'waitForReload', this.user.profile, `Waiting again ${id} ${start} to reload ${JSON.stringify(hold)}`);
      return this.waitForReload(timeout, cstack);
    } else {
      this.cache_lock = true;
      if (logging.isVerbose(this.user.profile) && reload) logging.verboseFP(LOG_NAME, 'waitForReload', this.user.profile, `Done ${id} waiting to reload from ${JSON.stringify(hold)}`);
      return true;
    }
  }

  async setCache(key: string, cache: string, value: any, expires: number = MAX_EXPIRES) {
    if (this.read_only && !['cache_hash', 'loaded', 'loaded_people', 'reload'].includes(cache)) throw new InternalError( 400, 'Cannot update readonly cache');
    const ch = this.cache_hash[cache];
    const comp = compress(value);
    const c_hash = hash(comp);
    if (!ch || ch.hash !== c_hash) {
      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'setCache', key, `Saving ${cache} cache ${c_hash} to ${key}`);
      try {
        const count = await DataCache._setCache(key, cache, comp, expires);
        if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'setCache', key, `Cached ${cache}(${count}) into ${key}_${cache}_N.`);
        return {hash: c_hash, count};
      } catch (err) {
        logging.errorFP(LOG_NAME, 'setCache', key, `Error saving ${cache} for key ${key}`, err.stack);

        data.plugins.cachePlugin().reset();
      }
    }

    return null;
  }

  static async getCache(key: string, cache: string): Promise<{ hash: string; count: number; value } | void> {
    let start = new Date().getTime();
    if (logging.isVerbose()) logging.verboseFP(LOG_NAME, 'getCache', key, `Loading ${cache} cache from ${key}`);

    try {
      const comps = [];
      let cache_index = 0;

      let loading = true;
      while (loading) {
        try {
          const compi = await data.plugins.cachePlugin().get(`${key}_${cache}_${cache_index}`);
          if (compi && compi.value) {
            if (compi.value.data && compi.value.data.length) comps.push(Buffer.from(compi.value.data));
            else if (compi.value.length) comps.push(compi.value);
            // new Buffer(compi));
            else {
              loading = false;
              break;
            }
          } else {
            loading = false;
            break;
          }
        } catch (err) {
          logging.errorFP(LOG_NAME, 'getCache', key, `Error getting ${cache} for ${key}`, err);
        }

        cache_index++;
      }

      if (comps.length) {
        let comp = Buffer.concat(comps);
        if ((comp as any).data) comp = (comp as any).data;
        const c_hash = hash(comp);
        const value = uncompress(comp);
        if (logging.isVerbose()) logging.verboseFP(LOG_NAME, 'getCache', key, `Loaded ${cache} from ${key} with ${cache_index + 1} count ${comp.length} bytes`);

        if (value) {
          if (logging.isVerbose()) logging.verboseFP(LOG_NAME, 'getCache', key, `Done loading ${cache} cache from ${key} in ${new Date().getTime() - start}`);
          return { hash: c_hash, value, count:cache_index + 1 };
        }
        else if (logging.isVerbose()) logging.verboseFP(LOG_NAME, 'getCache', key, `Error uncompressing ${cache} cache for ${key}`);
      } else if (logging.isVerbose()) logging.verboseFP(LOG_NAME, 'getCache', key, `Empty ${cache} cache for ${key}`);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'getCache', key, `Error loading ${cache} for ${key}`, err);
    }

    if (logging.isVerbose()) logging.verboseFP(LOG_NAME, 'getCache', key, `Done loading EMPTY ${cache} cache from ${key} in ${new Date().getTime() - start}`);
    return null;
  }

  static async _delCache(key: string, cache: string, count: number = null) {
    if (!count) {
      const dcache = await DataCache.getCache(key, cache);
      if (dcache) count = dcache.count;
    }

    if (count && !isNaN(count)) {
      for (let i = 0; i < count; i++) {
        await data.plugins.cachePlugin().delete(`${key}_${cache}_${i}`);
      }
    } else await data.plugins.cachePlugin().delete(`${key}_${cache}_0`);
  }

  static async _setCache(key: string, cache: string, comp: string, expires: number = MAX_EXPIRES): Promise<number> {
    const comp_length = comp.length;

    let set_final = false;
    let cache_index = 0;
    while (comp.length || !set_final) {
      const scomp = comp.slice(0, MAX_CACHE_VAL);
      comp = comp.slice(MAX_CACHE_VAL);

      if (scomp.length === 0) set_final = true;

      await data.plugins.cachePlugin().set(`${key}_${cache}_${cache_index}`, scomp, { expires })
        .catch(err => {
          if (err) logging.errorFP(LOG_NAME, 'setCache', key, `Error setting ${cache}(${comp_length}) for ${key}`, err);
        });
      cache_index++;
    }

    if (logging.isVerbose()) logging.verboseFP(LOG_NAME, 'setCache', key, `Saved ${cache} in ${key} with ${cache_index} count ${comp_length} bytes`);

    return cache_index + 1;
  }

  static async delSessionCaches(key: string): Promise<void> {
    if (logging.isVerbose()) logging.verboseF(LOG_NAME, 'delSessionCache', `deleting ${key}`);
    for (const cache of _SessionCaches) {
      await DataCache._delCache(key, cache);
    }
  }

  static async getSessionCache(key: string, which: string[] = _SessionCaches): Promise<{key: string; session: any; history: Partial<SubDialog>[]; /*actions: Action[];*/ context: Context; user: any}> {
    const scache = {key, session:null, history: null, /*actions:null,*/ context: null, user: null};
    let found = false;
    for (const cache of which) {
      const sc = await DataCache.getCache(key, cache);
      if (sc && sc.value) {
        scache[cache] = sc.value;
        found = true;
      }
    }

    if (logging.isVerbose()) logging.verboseF(LOG_NAME, 'getSessionCache', `loading ${Object.keys(scache)} from ${key}`);
    return found ? scache : null;
  }

  static async saveSessionCache(key: string, value: {session?: any; context?: Context; history?: Partial<SubDialog>[]; /*actions?: Action[];*/ user?: any}, expires: number): Promise<void> {
    if (logging.isVerbose()) logging.verboseF(LOG_NAME, 'saveSessionCache', `saving ${Object.keys(value)} to ${key}`);
    for (const cache of _SessionCaches) {
      if (value[cache]) {
        const comp = compress(value[cache]);
        await DataCache._setCache(key, cache, comp, expires);
      }
    }
  }
}
