import { <PERSON>sonConvert, OperationMode, ValueCheckingMode } from 'json2typescript';
import _ from 'lodash';
import util from 'util';
import { v4 as uuid } from 'uuid';

import { AuthProvider } from '../auth/auth_provider';
import config from '../config';
import data from '../data';
import { SourceKeys } from '../sources';
import { GoogleEventsSourceMetadata } from '../sources/events/google/google_events_source_metadata';
import { MicrosoftEventsSourceMetadata } from '../sources/events/microsoft/microsoft_events_source_metadata';
import { SourceController } from '../sources/source_controller';

import { NormalizedProviderToken } from '../types/auth';
import { User as GUser, InternalError, Notification, Tracking, WebPush } from '../types/globals';
import { Group } from '../types/group';
import { GlobalType, Location, Tag } from '../types/items';
import { AuthContext, AuthLevel, AuthPermissions, AuthProviders, Condition, DisallowableAuthContext, EntityType, ForaUserSettings, NotificationEmail, NotificationSetting, NotificationType, PushSetting, Relation, Uid, ViewProjects, Widget } from '../types/shared';
import { FORA_GUEST_ID, FORA_PROFILE, User } from '../types/user';

import { MINUTES } from '../utils/datetime';
import { stringIncludesAll } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

const DEBUG = (require('debug') as any)('fora:utils:user');
const LOG_NAME = 'utils.ForaUser';

const ACCOUNT_TOKEN = 'access_token';

export default class ForaUser extends User {
  static offline_notify_map: { [key: string]: any } = {}; // ???
  public readonly nonIndexedFields = ['accounts', 'notify', 'tokens', 'sync_tokens', 'settings', 'data_sources', 'bio'];

  // accounts: { [key: string]: any }; // Map of account data - top level key is the provider
  // data_sources: { [key: string]: {[key:string]: ISourceMetadata} }; // Metadata for all data sources available Type => account_id => metadata
  // full_refresh: EntityType[]; // List of entities that need a full refresh
  // groups: { [key: string]: string[] }; // Map of Groups user is a member of, with permissions for that group
  loaded_groups: { [key: string]: Group }; // Loaded Group objects that the user is a member of
  // locale: string; // Locale - user's language, region and any special variant preferences
  // notify: any[]; // Firebase notification tokens
  // offset: number; // minutes from GMT
  // profile: Profile; // Users profile ID. Comes from the ID of the user that is logged in via OAuth
  // provider: AuthProviders; // The provider this user is using for OAuth
  // refreshed: EntityType[]; // List of types of data that has recently been refreshed for the user
  // refreshing: boolean; // Flag indicating that we are refreshing data for the user
  // settings: ForaUserSettings; // Users settings
  // imports:  { id: Uid; date: Date; type: ImportType }[];
  resave: boolean; // 

  messages: string[] = [];
  notifications: Notification[] = [];

  //tracking: Tracking;
  create_guest = false;

  // @deprecated
  // sync_tokens: any; // DEPRECATED: Sync tokens are now in accounts
  // timeZone: string; // Timezone of the user

  constructor(profile: string, provider?: AuthProviders) {
    super({ id: profile });
    this.provider = provider;
    this.profile = profile;
    this.name = null;
    this.email = null;
    this.exported = false;
    this.groups = {};
    this.loaded_groups = null;
    this.refreshing = null;
    this.offset = 0;
    this.locale = 'en-US';
    this.timeZone = 'Etc/Zulu';
    this.notify = [];
    this.start = new Date();
    this.last = new Date(0);
    this.last_refresh = new Date(0);
    this.imports = [];

    this.permissions = {} as any;
    this.permissions[AuthLevel.Email] = AuthPermissions.None;
    this.permissions[AuthLevel.Organizer] = AuthPermissions.None;
    this.permissions[AuthLevel.OrganizerSync] = AuthPermissions.None;
    this.permissions[AuthLevel.Basic] = AuthPermissions.None;
    this.permissions[AuthLevel.Demo] =  AuthPermissions.None;

    ForaUser.ensureAccounts(this);
    this.full_refresh = [];
    this.settings = { authenticated: false } as ForaUserSettings;
    this.refreshed = [];
    this.data_sources = {};

    this.resave = false;

    if (profile && profile.startsWith('g-')) this.createGuest(true);
  }

  get debug(): boolean {
    return (
      config.isEnvOffline() ||
      (this.loaded_groups
        ? Object.values(this.loaded_groups)
          .map(g => g.debug)
          .reduce((a, b) => a || b, false)
        : false)
    );
  }

  get offline_notify(): any {
    if (config.isEnvOffline()) return ForaUser.offline_notify_map[this.profile];
    return null;
  }

  get location(): Location {
    const code = this.locale.split('-')[1];
    if (code) return parsers.COUNTRIES[code];
    return null;
  }

  get tokens() {
    return this.getTokens(this.provider, this.profile);
  }

  set tokens(new_tokens) {
    this.setTokens(new_tokens, this.provider, this.profile);
  }

  get auth_group() {
    if (this.loaded_groups) {
      for (const group of Object.values(this.loaded_groups)) {
        if (group.provider === this.provider && group.provider_settings) return group;
      }
    }
    return null;
  }

  getTokens(provider: AuthProviders, profile?: Uid): any {
    if (profile === FORA_PROFILE) return null;
    if (!provider || !this.hasAccount(provider, profile)) {
      logging.warnFP(LOG_NAME, 'getTokens', this.profile, 'Looking for tokens with no provider');
      return null;
    }
    if (profile) return _.get(this.accounts, `${provider}.${profile}`);
    const default_token_set = _.get(this.accounts, `${provider}`);
    if (default_token_set) {
      const default_tokens = Object.values(default_token_set) as any[];
      if (default_tokens.length) {
        const default_token = default_tokens.find(t => t && 'default' in t);
        return default_token ? default_token : default_tokens[0];
      }
    }
  }

  setTokens(new_tokens: any, provider: AuthProviders, profile?: Uid, group?: Group) {
    ForaUser.ensureAccounts(this);
    if (!provider) throw new Error(`Cannot set tokens with no provider on ${profile}`);
    if (!this.accounts[provider]) this.accounts[provider] = {};
    if (profile) {
      if (this.accounts[provider][profile]) {
        const existing_tokens = this.accounts[provider][profile];
        switch(provider) {
          case AuthProviders.Google: 
            this.accounts[AuthProviders.Google][profile] = AuthProvider.google.mergeTokens(existing_tokens, new_tokens);
            break;
          case AuthProviders.Microsoft: 
            this.accounts[AuthProviders.Microsoft][profile] = AuthProvider.microsoft.mergeTokens(existing_tokens, new_tokens);
            break;
          case AuthProviders.Msal: 
            this.accounts[AuthProviders.Msal][profile] = AuthProvider.msal.mergeTokens(existing_tokens, new_tokens);
            break;
          case AuthProviders.Okta:
            this.accounts[AuthProviders.Okta][profile] = AuthProvider.okta.mergeTokens(existing_tokens, new_tokens);
            break;
          case AuthProviders.Offline: 
            this.accounts[AuthProviders.Offline][profile] = AuthProvider.offline.mergeTokens(existing_tokens, new_tokens);
            break;
          case AuthProviders.Email:
            this.accounts[AuthProviders.Email][profile] = new_tokens;
            break;
          default: 
            this.accounts[provider][profile] = _.merge(existing_tokens, new_tokens);
            break;
        } 
      } else this.accounts[provider][profile] = new_tokens;

      if (group) this.accounts[provider][profile].group = group.id;
    }

    for (const account in this.accounts) {
      if (FORA_PROFILE in this.accounts[account]) {
        logging.warnFP(LOG_NAME, 'set tokens', profile, 'Clearning anonymous user in google accounts');
        delete this.accounts[account][FORA_PROFILE];
      }
    }

    SourceController.checkPlugins(this);
    this.addAccountSetting(provider);
  }

  clearOfflineNotify() {
    if (config.isEnvOffline() && ForaUser.offline_notify_map[this.profile]) delete ForaUser.offline_notify_map[this.profile];
  }

  createGuest(force = false) {
    if (force || (!this.isAuthenticated() && this.permissions.demo !== AuthPermissions.Guest)) {
      this.permissions.demo = AuthPermissions.Guest; // turn off demo
      this.id = FORA_GUEST_ID;
      if (!this.profile.startsWith('g-')) this.profile = `g-${uuid()}`;
      this.resave = false;
      logging.infoFP(LOG_NAME, 'createGuest', this.profile, 'Creating guest account');
    }
  }

  deleteAccount(account_type: AuthProviders, account_id: string = null) {
    if (account_id) {
      if (this.accounts[account_type]) {
        delete this.accounts[account_type][account_id];
        if (Object.keys(this.accounts[account_type]).length === 0) this.removeAccountSetting(account_type);
      }
    } else {
      delete this.accounts[account_type];
      this.removeAccountSetting(account_type);
      if (account_type === AuthProviders.Stripe) {
        let group_stripe = false;
        if (this.loaded_groups) {
          for (const group of Object.values(this.loaded_groups)) {
            if (group.accounts && group.accounts[AuthProviders.Stripe]) {
              group_stripe = true;
              break;
            }
          }
        }

        if (group_stripe) this.addAccountSetting(AuthProviders.Stripe);
        else this.removeAccountSetting(AuthProviders.Stripe);
      }
    }

    if (this.isAuthenticatedNonGuest()) this.resave = true;
  }

  hasAccount(account_type: AuthProviders, account_id: string = null) {
    if (account_type === AuthProviders.Stripe && this.loaded_groups) {
      for (const group of Object.values(this.loaded_groups)) {
        if (group.accounts && group.accounts[AuthProviders.Stripe] &&
          Object.values(group.accounts[AuthProviders.Stripe]).length) return true;
      }
    }

    if (this.accounts) {
      if (this.accounts[account_type]) {
        if (account_id) {
          return this.accounts[account_type][account_id] !== null &&
            this.accounts[account_type][account_id] !== undefined; /* &&
            this.accounts[account_type][account_id][ACCOUNT_TOKEN] !== null &&
            this.accounts[account_type][account_id][ACCOUNT_TOKEN] !== undefined;*/
        }
        /* return (this.accounts[account_type][ACCOUNT_TOKEN] !== null && 
          this.accounts[account_type][ACCOUNT_TOKEN] !== undefined) || */
        return ( this.accounts[account_type] && Object.values(this.accounts[account_type]).length > 0 /* &&
            Object.values(this.accounts[account_type]).filter(a => a[ACCOUNT_TOKEN] !== null && a[ACCOUNT_TOKEN] !== undefined).length > 0*/
          );
      }
    }
    return false;
  }

  getAccountIds(account_type?: AuthProviders | AuthProviders[]) {
    let accounts = [];
    if (account_type) {
      if (!Array.isArray(account_type)) account_type = [account_type];
      for (const account of account_type) {
        if (this.accounts[account]) accounts = accounts.concat(Object.keys(this.accounts[account]));
      }
    } else {
      for (const provider in this.accounts) accounts = accounts.concat(Object.keys(this.accounts[provider]));
    }
    return accounts;
  }

  getAccountType(id: Uid): AuthProviders {
    for (const provider in this.accounts) {
      if (id in this.accounts[provider]) return provider as AuthProviders;
    }
  }

  getAccountGroup(id: Uid): Group {
    for (const provider in this.accounts) {
      if (id in this.accounts[provider]) {
        const tokens = this.accounts[provider][id];
        if (tokens && tokens.group && this.loaded_groups) return this.loaded_groups[tokens.group];
      }
    }
  }

  isAnonymousAccount() {
    return FORA_PROFILE === this.profile && !this.create_guest;
  }

  // level can be organizer or email and values are 'full' or 'none'
  isAuthenticated(level: AuthLevel = null, provider?: AuthProviders, profile?: string): boolean {
    if (level && ![AuthLevel.Email, AuthLevel.OrganizerSync, AuthLevel.Organizer, AuthLevel.Demo, AuthLevel.Basic].includes(level)) {
      if (logging.isDebug(this.profile)) logging.debugFP(LOG_NAME, 'isAuthenticated', this.profile, `Unknown auth level ${level} with provider ${this.provider}`);
      throw new Error(`Unknown user auth permission level ${level}`);
    }

    if (this.isGuestAccount()) return true;

    if (provider) {
      const tokens = profile ? this.getTokens(provider, profile) : this.getTokens(provider);
      if (!level || (tokens && tokens.permissions && tokens.permissions === level)) return true;
      else if (!profile && Object.keys(this.accounts[provider]).length > 1) {
        for(const account of Object.values(this.accounts[provider])) {
          if (account.permissions && account.permissions === level) return true;
        }
      }
    }

    if (!this.hasAccount(provider ? provider : this.provider, profile)) return false;

    const tokens = this.getTokens(provider ? provider : this.provider, profile);

    if ((tokens && tokens[ACCOUNT_TOKEN]) || level === AuthLevel.Demo) {
      return !level || this.permissions[level] === AuthPermissions.Full || (tokens && tokens.permissions === level);
    }

    return false;
  }

  isAuthenticatedNonGuest(scope = null) {
    return this.isAuthenticated(scope) && !this.isGuestAccount()
  }

  isGuestAccount(): boolean {
    if (this.create_guest) return true;
    if(this.permissions.demo === AuthPermissions.Guest) return true;
    if (!this.profile) throw new InternalError(500, `Non guest with no profile ${JSON.stringify(this.permissions)}`);
    if (this.profile.startsWith('g-')) throw new InternalError(500, `Non guest with guest profile ${this.profile}  ${JSON.stringify(this.permissions)}`);
  }

  isRefreshRequested(entityType: EntityType) {
    if (this.full_refresh) return this.full_refresh.includes(entityType);
    else return false;
  }

  isAdmin(group_id: Uid) {
    if (group_id) {
      const group = this.loaded_groups[group_id]
      const roles = this.groups[group_id];
      if (group && group.roles && roles && Array.isArray(roles)) {
        for (const role of roles) {
          if (group.roles[role] && group.roles[role].admin) return true;
        }
      }
    }    
    return false;
  }

  adminGroups(): Uid[] {
    let admin_groups: Uid[] = [];
    if (this.groups && this.loaded_groups) {
      for (const group_id in this.groups) {
        const group = this.loaded_groups[group_id];
        if (group) {
          const roles = this.groups[group_id]
          for (const role of roles) {
            if (group.roles && group.roles[role] && group.roles[role].admin) {
              admin_groups.push(group_id);
              break;
            }
          }
        } else logging.warnFP(LOG_NAME, 'adminGroups', this.profile, `Missing group ${group_id}`);
      }
    }

    return admin_groups;
  }

  hasHostGroup(host: string): boolean {
    if (this.loaded_groups) {
      for (const group of Object.values(this.loaded_groups)) {
        if (group.host && (group.host === host || group.host === `${host}.askfora.com`)
        ) return true;
      }
    }
    return false;
  }

  addAccountSetting(provider: AuthProviders) {
    if (!this.settings.accounts) this.settings.accounts = [provider];
    else if (!this.settings.accounts.includes(provider)) this.settings.accounts.push(provider);
  }

  removeAccountSetting(provider: AuthProviders) {
    if (this.settings.accounts) this.settings.accounts = this.settings.accounts.filter(a => a !== provider);
  }

  emailGroup(): Group {
    if (this.loaded_groups) {
      for (const group of Object.values(this.loaded_groups)) {
        if (group.email_domain && group.email_domain.length && group.email_domain.find(ed => this.email.endsWith(ed))) return group;
      }
    }
  }

  categoryGroups(): Group[] {
    if (this.loaded_groups) {
      return Object.values(this.loaded_groups).filter(g => g.categories);
    }
  }

  groupRedirect(): string {
    if (this.loaded_groups) {
      for (const group of Object.values(this.loaded_groups)) {
        if (group.redirect) return group.redirect;
      }
    }
  }

  loadGroups(groups: {[key:string]: Group}): void {
    // if (this.loaded_groups) return;

    const providers: AuthProviders[] = [];
    this.loaded_groups = {};

    let debug = false;
    // const group_providers: AuthProviders[] = [];

    if(this.groups && Object.keys(this.groups).length) {
      for(const id in this.groups) {
        const group = groups[id];
        // Only add the group if its one of the ones we are assigned to
        if (group) {
          this.loaded_groups[group.id] = group;
          if (group.provider && !providers.includes(group.provider)) providers.push(group.provider);

          if (group.accounts) {
            for (const provider of (Object.keys(group.accounts) as AuthProviders[])) {
              if (!providers.includes(provider)) providers.push(provider);
            }
          }

          if (!this.settings.projects) this.settings.projects = { view: ViewProjects.Manage }; // , as_contractor: false ;
          this.settings.projects.skip_contracting = group.skip_contracting ? true : false; // || this.settings.projects.skip_contracting ;
          this.settings.projects.skip_payment = group.skip_payment ? true : false; // || this.settings.projects.skip_payment;
          if (group.accounts && group.accounts[AuthProviders.Stripe] && Object.values(group.accounts[AuthProviders.Stripe]).length) {
            this.addAccountSetting(AuthProviders.Stripe);
          } if (group.debug) debug = true;
        }
      }
    }

    if (!providers.length) {
      if (this.isAuthenticatedNonGuest()) providers.push(this.provider);
      for (const default_provider of AuthProvider.clientAuthNameSet()) {
        if (default_provider.provider && default_provider.provider.length && !providers.includes(default_provider.provider)) providers.push(default_provider.provider);
      }
    }

    this.settings.providers = providers;

    this.updateNotifications();

    if (this.isAuthenticatedNonGuest()) this.updatePermissions();

    if (debug) logging.debugFor(this.profile);
    else logging.clearDebugFor(this.profile);
  }

  async loadGroupsFromDatastore(force = false): Promise<void> {
    if (this.loaded_groups && !force) return;

    // const group_loaders = Object.keys(this.groups).map(g => data.groups.byId(g).catch(e => logging.errorFP(LOG_NAME, 'loadGroupsFromDatastore', this.profile, 'Error loading groups', e)));
    const groups = await data.groups.byIds(Object.keys(this.groups)); //(await Promise.all(group_loaders)).filter(g => g) as Group[];
    const group_set: {[key:string]: Group} = {};
    groups.forEach(g => group_set[g.id] = g);
    return this.loadGroups(group_set);
  }

  get mandatory_tags(): Tag[][] {
    const m_tags: Tag[][] = [];
    if (this.loaded_groups) {
      for (const g_id in this.loaded_groups) {
        const group: Group = this.loaded_groups[g_id];
        if (group.search_mandatory_tags && group.search_mandatory_tags.length) m_tags.push(group.search_mandatory_tags);
      }
    }

    return m_tags;
  }

  get search_groups(): Uid[] {
    const g_searches: Uid[] = [];
    if (this.loaded_groups) {
      for (const g_id in this.loaded_groups) {
        const group: Group = this.loaded_groups[g_id];
        if (group.search_groups_contacts) g_searches.push(group.id);
      }
    }

    return g_searches;
  }

  get group_conditions(): Condition[][] {
    const conditions: Condition[][] = [];
    if (this.loaded_groups) {
      for (const g_id in this.loaded_groups) {
        const group: Group = this.loaded_groups[g_id];
        if (group.search_mandatory_tags && group.search_mandatory_tags.length) {
          conditions.push(group.search_mandatory_tags.map(tag => { return { att: tag.type, rel: Relation['='], value: [tag.value], } }));
        }
      }
    }

    return conditions;
  }

  paymentAccount(account_id = null) {
    if (this.hasAccount(AuthProviders.Stripe)) {
      const stripe_account = this.getTokens(AuthProviders.Stripe, account_id);
      return stripe_account ? stripe_account.stripe_user_id : null;
    }
    if (this.hasAccount(AuthProviders.Wise)) {
      const wise_account = this.getTokens(AuthProviders.Wise);
      return wise_account ? wise_account.id : null;
    }
    return null;
  }

  groupPaymentAccount(group_id?: Uid) {
    if (this.loaded_groups) {
      if (group_id) {
        const group = this.loaded_groups[group_id];
        if (group && group.accounts && group.accounts[AuthProviders.Stripe] && 
            Object.values(group.accounts[AuthProviders.Stripe]).length) return Object.values(group.accounts[AuthProviders.Stripe])[0];
      } else {
        for (const group of Object.values(this.loaded_groups)) {
          if (group.accounts && group.accounts[AuthProviders.Stripe] && 
            Object.values(group.accounts[AuthProviders.Stripe]).length) return Object.values(group.accounts[AuthProviders.Stripe])[0];
        }
      }
    }
    return null;
  } 

  async needsInit() {
    // only init every 15 minutes or more
    if (this.isAuthenticated(AuthLevel.Demo) || this.isGuestAccount()) return false;
    // return !config.isEnvOffline() && 
    const last = new Date(this.last);
    const set_update = await data.plugins.cachePlugin().lookupCacheAttVal(this.profile, GlobalType.User, 'user', 'update');
    const updated = new Date(set_update);
    return last < MINUTES(new Date(), -15) || last < updated ||
      (this.tokens && new Date(this.tokens.expires_at) < new Date());
  }

  async setUpdate() {
    return data.plugins.cachePlugin().cacheAttVal(this.profile, GlobalType.User, 'user', 'update', new Date(), 15 * 60);
  }

  registerOfflineNotify(fn) {
    if (config.isEnvOffline()) ForaUser.offline_notify_map[this.profile] = fn;
  }

  // grab persisted attributes
  restore(pd: User) {
    if (pd.profile) {
      this.profile = pd.profile;
      this.id = pd.profile;
    }

    if (pd.name) this.name = pd.name;
    if (pd.email) this.email = pd.email;
    if (pd.vanity) this.vanity = pd.vanity;
    if (pd.bio) this.bio = pd.bio;
    if (pd.start && new Date(pd.start).getTime()) this.start = pd.start;

    if(pd.tracking) this.tracking = pd.tracking;
    if (pd.exported || pd.exported === undefined) this.exported = true;

    const pd_last = new Date(pd.last);
    if (new Date(this.last) < pd_last) this.last = pd_last;

    const pd_last_refresh = new Date(pd.last_refresh);
    if (this.last_refresh < pd_last_refresh) {
      this.last_refresh = pd_last_refresh;
      if (pd.refreshed) this.refreshed = _.uniq([...this.refreshed, ...pd.refreshed]);
    }

    if (!this.refreshed) this.refreshed = [];

    if(!this.affiliate) this.affiliate = pd.affiliate;

    if (pd.offset) {
      // convert DS store dates back to time if needed
      if (`${pd.offset}`.includes('T')) this.offset = new Date(pd.offset).getTime();
      else this.offset = pd.offset;
    }
    if (pd.locale) this.locale = pd.locale;
    if (pd.timeZone) this.timeZone = pd.timeZone;

    if (pd.notify && pd.notify.length) this.notify = pd.notify;
    else if (!this.notify) this.notify = [];

    if (pd.groups && Array.isArray(pd.groups) && pd.groups.length) {
      this.groups = {};
      pd.groups.forEach(g => {
        this.groups[g] = [];
      });
    } else if (pd.groups && !Array.isArray(pd.groups) && Object.keys(pd.groups).length) this.groups = pd.groups;
    else if (!this.groups) this.groups = {};
 
    const gpd = pd as any as GUser;
    if (gpd.messages) this.messages = gpd.messages.slice(-3);
    if (gpd.notifications) {
      for (const notify of gpd.notifications.slice(-3)) {
        if (notify['data'] || notify['fcm_options'] || notify['notification']) {
          this.notifications.push({webpush:notify as WebPush});
        }
        else this.notifications.push(notify);
      }
    }

    if (pd.full_refresh) this.full_refresh = pd.full_refresh;
    else if (!this.full_refresh) this.full_refresh = [];

    if (pd.settings) {
      // note a race condition where saved settings w/o calendars overrides settings with calendars
      const saved_settings = JSON.parse(Buffer.from(pd.settings.toString(), 'base64').toString());
      let restore_calendars = null;
      if (this.settings && this.settings.calendars) {
        if(this.settings.calendars.sources && Object.keys(this.settings.calendars.sources).length) restore_calendars = this.settings.calendars.sources;
        else if (Object.keys(this.settings.calendars).length) restore_calendars = this.settings.calendars;
      }

      this.settings = saved_settings;

      if (restore_calendars && (!saved_settings.calendars || !Object.keys(saved_settings.calendars).length ||
          !saved_settings.calendars.sources || !Object.keys(saved_settings.calendars.sources).length)) {
        const enabled = restore_calendars.enabled;
        const disallowed = restore_calendars.disallowed;

        delete restore_calendars.enabled;
        delete restore_calendars.disallowed;
        delete restore_calendars.name;
        delete restore_calendars.selected;
        delete restore_calendars.sync;
        delete restore_calendars.sources;

        this.settings.calendars = {
          sources: restore_calendars,
          enabled,
          disallowed
        }
      }

      this.updateNotifications(saved_settings.notifications);

      switch (this.settings.active) {
        case EntityType.Person:
          if (this.settings.people.active && 
            (!this.settings.people.active.id || !this.settings.people.active.vanity || !this.settings.people.active.name)) {
              this.settings.people.active = null;
              this.settings.active = null;
            }
          break;
      }

      if (!this.settings.analyses) this.settings.analyses = { enabled: false, disallowed: false };

    } else if (!this.settings) this.settings = { authenticated: false } as ForaUserSettings;

    if (this.refreshing === null) this.refreshing = pd.refreshing;

    ///////////////////////////
    // Accounts
    ForaUser.ensureAccounts(this);
    ForaUser.ensureAccounts(pd);
    if (logging.isVerbose(this.profile)) logging.verboseFP(LOG_NAME, 'restore', this.profile, `provider options = ${this.provider}, ${pd.provider}`);
    if (pd.provider) this.provider = pd.provider as AuthProviders;
    if (logging.isVerbose(this.profile)) logging.verboseFP(LOG_NAME, 'restore', this.profile, `provider after = ${this.provider}`);

    const account_types = Array.from(new Set([...Object.keys(this.accounts ? this.accounts : {}), ...Object.keys(pd.accounts ? pd.accounts : {})]));
    if (logging.isVerbose(this.profile)) logging.verboseFP(LOG_NAME, 'restore', this.profile, `account_types = ${account_types}`);

    if (logging.isVerbose(this.profile)) {
      DEBUG('restore: %s: this.accounts before %o', this.profile, this.accounts);
      DEBUG('restore: %s: pd.accounts before %o', this.profile, pd.accounts);
    }

    for (const key of account_types) {
      switch(key) {
        case AuthProviders.Google:
          this.accounts[AuthProviders.Google] = AuthProvider.google.reconcileExistingTokens(this.accounts[AuthProviders.Google], pd.accounts[AuthProviders.Google]);
          break;
        case AuthProviders.Microsoft:
          this.accounts[AuthProviders.Microsoft] = AuthProvider.microsoft.reconcileExistingTokens(this.accounts[AuthProviders.Microsoft], pd.accounts[AuthProviders.Microsoft]);
          break;
        case AuthProviders.Msal:
          this.accounts[AuthProviders.Msal] = AuthProvider.msal.reconcileExistingTokens(this.accounts[AuthProviders.Msal], pd.accounts[AuthProviders.Msal]);
          break;
        case AuthProviders.Okta:
          this.accounts[AuthProviders.Okta] = AuthProvider.okta.reconcileExistingTokens(this.accounts[AuthProviders.Okta], pd.accounts[AuthProviders.Okta]);
          break;
        case AuthProviders.Stripe:
          // fix old mapping
          if (pd.accounts[key].stripe_user_id) this.accounts[AuthProviders.Stripe][(pd.accounts[key] as any as NormalizedProviderToken)['stripe_user_id']] = pd.accounts[key] as any as NormalizedProviderToken;
          else this.accounts[key] = _.merge(this.accounts[key], pd.accounts[key]);
          break;
        case AuthProviders.Offline:
          this.accounts[AuthProviders.Offline] = AuthProvider.offline.reconcileExistingTokens(this.accounts[AuthProviders.Offline], pd.accounts[AuthProviders.Offline]);
          break;
        default:
          this.accounts[key] = _.merge(this.accounts[key], pd.accounts[key]);
          break;
      }
    }

    if (logging.isVerbose(this.profile)) DEBUG('restore: %s: this.accounts after %o', this.profile, this.accounts);

    // Legacy repair of old users that did not have a provider
    if (!this.provider) {
      if (this.accounts[AuthProviders.Google] && Object.keys(this.accounts[AuthProviders.Google])) {
        if (logging.isVerbose(this.profile)) DEBUG('restore: %s: defaulting provider to Google', this.profile);
        this.provider = AuthProviders.Google;
      } else if (this.accounts[AuthProviders.Microsoft] && Object.keys(this.accounts[AuthProviders.Microsoft])) {
        if (logging.isVerbose(this.profile)) DEBUG('restore: %s: defaulting provider to Microsoft', this.profile);
        this.provider = AuthProviders.Microsoft;
      } else {
        logging.warnFP(LOG_NAME, 'restore', this.profile, 'Unable to determine provider for legacy user');
      }
    }

    // make sure provider and primary account line up
    if (!this.hasAccount(this.provider)) {
      if (this.hasAccount(AuthProviders.Email)) this.provider = AuthProviders.Email;
      else if (this.hasAccount(AuthProviders.Saml)) this.provider = AuthProviders.Saml;
      else if (this.hasAccount(AuthProviders.Okta)) this.provider = AuthProviders.Okta;
      else if (this.hasAccount(AuthProviders.Google)) this.provider = AuthProviders.Google;
      else if (this.hasAccount(AuthProviders.Microsoft)) this.provider = AuthProviders.Microsoft;
      else if (this.hasAccount(AuthProviders.Msal)) this.provider = AuthProviders.Msal;
    }

    if(this.hasAccount(this.provider)) {
      const tokens = this.tokens;

      if (tokens && tokens[ACCOUNT_TOKEN]) {
        this.settings.authenticated = true;
        if (!this.settings.calendars) {
          this.settings.calendars = { 
            enabled: false,
            disallowed: false,
            sources: {},
          };
        }
      }
    }

    if (this.hasAccount(AuthProviders.Stripe)) this.addAccountSetting(AuthProviders.Stripe);
    if (this.hasAccount(AuthProviders.Wise)) this.addAccountSetting(AuthProviders.Wise);

    ///////////////////////////
    // Data Sources Metadata
    if (logging.isVerbose(this.profile)) DEBUG('restore: %s: this.data_sources before %o', this.profile, this.data_sources);
    if (pd.data_sources) {
      // Legacy migration
      this.data_sources = ForaUser.decodeSupportingLegacy(pd.data_sources);

      // map data sources
      const accounts = this.getAccountIds();
      const source_plugins = data.plugins.sourcePlugins();

      const del_sources: SourceKeys[] = [];
      const keep_sources: SourceKeys[] = [];

      for (const s in this.data_sources) {
        const source = s as SourceKeys;
        const plugin = source_plugins.get(source);
        if (plugin) {
          for (const provider of plugin.providers) {
            for (const account of accounts) {
              if (this.hasAccount(provider, account)) {
                if (!keep_sources.includes(source)) keep_sources.push(source);

                const md_keys = this.data_sources[source] ? Object.keys(this.data_sources[source]) : undefined;
                if (md_keys?.length && !md_keys.includes(account)) {
                  if (md_keys.includes('enabled')) {
                    const new_source = {};
                    new_source[account] = this.data_sources[source];
                    this.data_sources[source] = new_source;
                  }
                }
              } else {
                if (!keep_sources.includes(source) && !del_sources.includes(source)) del_sources.push(source);
              }
            }
          }
        } else logging.warnFP(LOG_NAME, 'restore', this.profile, `Unknown source plugin for ${source}`);

        // clean up sources not bound to an account
        if (this.data_sources[source]) {
          const md_keys = Object.keys(this.data_sources[source]);
          if (md_keys.length && md_keys.includes('enabled')) {
            if (!keep_sources.includes(source) && !del_sources.includes(source)) del_sources.push(source);
          }
        }
      }

      for(const source of del_sources.filter(d => !keep_sources.includes(d))) {
        if (source in this.data_sources) delete this.data_sources[source];
      }
    }
    // This will force a proper restore of the metadata stored in data_sources
    SourceController.checkPlugins(this);
    if (logging.isVerbose(this.profile)) DEBUG('restore: %s: this.data_sources after %o', this.profile, this.data_sources);

    ///////////////////////////
    // Update calendar settings from metadata
    if (this.data_sources[SourceKeys.GoogleEvents] || this.data_sources[SourceKeys.MicrosoftEvents]) {
      if (!this.settings.calendars) this.settings.calendars = {
        enabled: true,
        disallowed: false,
        sources: {}
      }

      let cal_metadata: GoogleEventsSourceMetadata | MicrosoftEventsSourceMetadata;
      let all_cal_ids = [];
      for (const provider in this.accounts) {
        for (const account in this.accounts[provider]) {
          switch (provider) {
            case AuthProviders.Google:
              cal_metadata = this.data_sources[SourceKeys.GoogleEvents][account] as GoogleEventsSourceMetadata;
              break;
            case AuthProviders.Microsoft:
            case AuthProviders.Msal:
              cal_metadata = this.data_sources[SourceKeys.MicrosoftEvents][account] as MicrosoftEventsSourceMetadata;
              break;
            case AuthProviders.Offline:
              cal_metadata = this.data_sources[SourceKeys.GoogleEvents][account] as GoogleEventsSourceMetadata;
              break;
          }

          if (cal_metadata && cal_metadata.calendars) {
            if (!this.settings.calendars || !this.settings.calendars.sources) {
              const sources = this.settings.calendars ? this.settings.calendars as any : {};
              delete sources.enabled;
              delete sources.disallowed;
              delete sources.name;
              delete sources.selected;
              delete sources.sync;
              this.settings.calendars = {
                enabled: false,
                disallowed: false,
                sources,
              }
            }

            const cal_ids = cal_metadata?.getCalendarsIds();
            if (cal_ids && cal_ids.length) {
              this.settings.calendars.enabled = true;
              this.settings.calendars.disallowed = false;
            }

            if (cal_ids) {
              for (const cal_id of cal_ids) {
                const calendar = cal_metadata.getCalendar(cal_id);
                if (!this.settings.calendars.sources[cal_id]) {
                  this.settings.calendars.sources[cal_id] = {
                    name: calendar.name,
                    sync: calendar.sync,
                    selected: calendar.selected,
                  };
                }
              }

              all_cal_ids = all_cal_ids.concat(cal_ids);
            }
          }
        }
      }

      const del_ids = [];
      for (const id in this.settings.calendars.sources) {
        if (!all_cal_ids.includes(id)) del_ids.push(id);
      }

      for (const id of del_ids) delete this.settings.calendars.sources[id];
    }
    if (logging.isVerbose(this.profile)) DEBUG('restore: %s: this.settings after %o', this.profile, this.settings);

    ///////////////////////////
    // legacy migration

    if (!this.settings.push && this.profile) this.settings.push = PushSetting.Ask;

    if (pd.calendars && !(pd.calendars instanceof Array)) this.settings.calendars = JSON.parse(Buffer.from(pd.calendars.toString(), 'base64').toString());

    // This permission gets set regardless of tokens (and can be updated by tokens)
    const demo_full = _.some([this.permissions, pd.permissions], [AuthPermissions.Demo, AuthPermissions.Full]);
    const demo_guest = _.some([this.permissions, pd.permissions], [AuthPermissions.Demo, AuthPermissions.Guest]);
    if (demo_full && this.profile.startsWith('-g')) throw new InternalError(500, `Guest user has full demo setting ${JSON.stringify(pd.permissions)} was ${JSON.stringify(this.permissions)}`);
    this.permissions.demo = demo_full ? AuthPermissions.Full : demo_guest ? AuthPermissions.Guest : AuthPermissions.None;

    ///////////////////////////
    // Restore saved permissions
    if (pd.permissions && pd.permissions[AuthLevel.Organizer] && pd.permissions[AuthLevel.Organizer] === AuthPermissions.Full) {
      this.permissions[AuthLevel.Organizer] = AuthPermissions.Full;
    }

    if (pd.permissions && pd.permissions[AuthLevel.OrganizerSync] && pd.permissions[AuthLevel.OrganizerSync] === AuthPermissions.Full) {
      this.permissions[AuthLevel.OrganizerSync] = AuthPermissions.Full;
    }

    /* if (pd.permissions && pd.permissions[AuthLevel.Email] && pd.permissions[AuthLevel.Email] === AuthPermissions.Full) {
      this.permissions[AuthLevel.Email] = AuthPermissions.Full;
    } */
    this.permissions[AuthLevel.Email] = AuthPermissions.None;

    if (pd.imports) this.imports = pd.imports;
  }

  async revokeTokens(provider?: AuthProviders, profile?: Uid) {
    if (!provider) provider = this.provider;
    if (!profile && provider === this.provider) profile = this.profile;

    try { 
      await AuthProvider.revokeToken(provider, profile, this.getTokens(provider, profile)).catch(err => logging.warnFP(LOG_NAME, 'handleError', this.profile, 'Error revoking tokens', err));
    } catch (err) {
      logging.errorFP(LOG_NAME, 'revokeTokens', this.profile, 'Error revoking  tokens', err)
    }

    // keep scope
    const tokens = this.getTokens(provider, profile);
    if (tokens) {
      //if (this.tokens) {
      const scope = tokens.scope;
      if (!this.accounts[provider]) this.accounts[provider] = {};
      this.accounts[provider][profile] = new NormalizedProviderToken(provider, null, AuthProvider.clientAuthPermissions(this, provider, this.auth_group));
      this.accounts[provider][profile].scope = scope;
      // this.setTokens({ scope }, provider, profile);
      logging.infoFP(LOG_NAME, 'revokeTokens', this.profile, `Keeping scope ${JSON.stringify(scope)}`);
    } else {
      logging.infoFP(LOG_NAME, 'revokeTokens', this.profile, 'Clearing scope');
      this.setTokens({}, provider, profile);
    }

    this.removeAccountSetting(this.provider);
  }

  enablePro() {
    if (!this.settings.learning) this.settings.learning = { enabled: true, disallowed: false };
    else this.settings.learning.enabled = true;

    this.settings.widgets = Object.values(Widget);
  }

  disablePro() {
    if (!this.settings.learning) this.settings.learning = { enabled: false, disallowed: false };
    else this.settings.learning.enabled = false;

    this.settings.widgets = [];
  }

  saveSettings(in_settings: ForaUserSettings, cache?, override = false) {
    // TODO - check for new calendars in this.settings
    // Clean the settings
    const _settings = this.settingsCleanIncoming(in_settings);

    if (this.isAuthenticatedNonGuest() || _settings.authenticated === false) {
      if (_settings.calendars && _settings.calendars.sources) {
        if (this.settings.calendars && this.settings.calendars.sources) {
          for (const cal in this.settings.calendars.sources) {
            if (_settings.calendars.sources[cal]) {
              this.settings.calendars.sources[cal].selected = _settings.calendars.sources[cal].selected;
            } 
          }
        }
      }

      if (logging.isVerbose(this.profile)) DEBUG(`saveSettings::${this.profile}::Settings before = ${util.format(this.settings)}`);
      // this.settings = _.merge(this.settings, _settings);
      this.settings.active = _settings.active;
      this.settings.info = _settings.info;
      this.settings.push = _settings.push;

      if (_settings.messages) {
        if (this.settings.messages) this.settings.messages.enabled = _settings.messages.enabled;
        else this.settings.messages = _settings.messages;
      }

      if (_settings.notes) {
        if (this.settings.notes) {
          
          // check that active entities exist
          if (_settings.notes.active && cache && cache.tasks[_settings.notes.active.id]) this.settings.notes.active = { id: _settings.notes.active.id, type: _settings.notes.active.type };
          else this.settings.notes.active = null;

          this.settings.notes.enabled = _settings.notes.enabled;
          this.settings.notes.filter = _settings.notes.filter;
          this.settings.notes.hide_info_msg = _settings.notes.hide_info_msg;
          this.settings.notes.show_completed = _settings.notes.show_completed;
          this.settings.notes.tab = _settings.notes.tab;
        } else this.settings.notes = _settings.notes;
      }
      if (_settings.people) {
        if (this.settings.people) {
          if (_settings.people.active && 
            (_settings.people.active.id || _settings.people.active.vanity || _settings.people.active.name)) this.settings.people.active = { id: _settings.people.active.id, vanity: _settings.people.active.vanity, name: _settings.people.active.name, type: EntityType.Person };
          else this.settings.people.active = null;
          this.settings.people.enabled = _settings.people.enabled;
          this.settings.people.filter = _settings.people.filter;
        } else this.settings.people = _settings.people;
      }

      if (_settings.projects) {
        if (this.settings.projects) {
          if (_settings.projects.active && cache && cache.projects[_settings.projects.active.id]) {
            this.settings.projects.active = { id: _settings.projects.active.id, type: _settings.projects.active.type, update_date: _settings.projects.active.update_date };
          }
          else this.settings.projects.active = null;

          this.settings.projects.view = _settings.projects.view;
          this.settings.projects.filter = _settings.projects.filter;
        } else this.settings.projects = _settings.projects;
      }

      if (_settings.tasks) {
        if (this.settings.tasks) {
          this.settings.tasks.enabled = _settings.tasks.enabled;
          this.settings.tasks.filter = _settings.tasks.filter;
        } else this.settings.tasks = _settings.tasks;
      }

      if (_settings.notifications) this.updateNotifications(_settings.notifications, true);
      if (_settings.templates) this.settings.templates = _settings.templates;

      if (override) {
        if (_settings.learning) this.settings.learning = _settings.learning;
        if (_settings.widgets) this.settings.widgets = _settings.widgets;
      }
      if (logging.isVerbose(this.profile)) DEBUG(`saveSettings::${this.profile}::Settings after = ${util.format(this.settings)}`);
    }
  }

  // returns the main user entry for DS
  save(encode = true, update_last = true) {
    if (this.profile) {
      this.last = update_last ? new Date() : this.last;
      const save_user = {
        type: this.type,
        schema: this.schema,
        id: this.profile,
        profile: this.profile,
        name: this.name,
        email: this.email,
        affiliate: this.affiliate,
        start: this.start,
        last: this.last,
        offset: this.offset,
        locale: this.locale,
        timeZone: this.timeZone,
        notify: this.notify,
        permissions: this.permissions,
        accounts: encode ? Buffer.from(JSON.stringify(this.accounts)).toString('base64') : this.accounts,
        provider: this.provider,
        settings: encode ? Buffer.from(JSON.stringify(this.settings)).toString('base64') : this.settings,
        refreshed: this.refreshed,
        last_refresh: this.last_refresh,
        groups: this.groups,
        vanity: this.vanity,
        bio: this.bio ? this.bio.slice(0,1500) : undefined,
        tracking: this.tracking,
      };

      if (logging.isDebug(this.profile)) logging.debugFP(LOG_NAME, 'save', this.profile, `Saving user at ${this.last} ${this.last_refresh.getTime()}`);
      return save_user;
    }
  }

  // returns the update entr yfor DS
  saveUpdate(encode = true) {
    if (this.profile) {
      const save_user = {
        type: this.type,
        schema: this.schema,
        id: `update_${this.profile}`,
        profile: this.profile,
        last_refresh: this.last_refresh,
        refreshing: this.refreshing,
        full_refresh: this.full_refresh,
        exported: this.exported,
        refreshed: this.refreshed,
        data_sources: encode ? this.serializeDataSources() : this.data_sources,
        imports: this.imports,
      };

      if (logging.isDebug(this.profile)) {
      logging.debugFP(LOG_NAME, 'saveUpdate', this.profile, `Saving user at ${this.last} ${this.last_refresh.getTime()}`);
        DEBUG(`user:saveUpdate: user.data_sources = ${util.format(this.data_sources)}`);
        DEBUG(`user:saveUpdate: sds = ${util.format(this.serializeDataSources())}`);
        DEBUG(`user:saveUpdate: user = ${util.format(save_user)}`);
        DEBUG(`user:saveUpdate: Saving user update ${this.profile} at ${this.last} refresh ${this.refreshing} at ${this.last_refresh}`);
      }

      return save_user;
    }
  }

  // returns one serialized object for the session and debugging
  serialize(encode = true) {
    const ssd = {};

    const user_save_update = this.saveUpdate(encode);
    const user_save = this.save(encode);

    for (const att in user_save_update) {
      if (user_save_update[att] === null || user_save_update[att] === undefined) delete user_save_update[att];
    }

    for (const att in user_save) {
      if (user_save[att] === null || user_save[att] === undefined) delete user_save[att];
    }

    _.merge(ssd, user_save_update);
    _.merge(ssd, user_save);

    return ssd;
  }
  
  setLocale(lc) {
    if (lc) {
      try { 
        const locale = lc.split(';')[0].split(',')[0];
        const date_check = new Date().toLocaleString(locale, {timeZone: this.timeZone});
        if (logging.isDebug(this.profile)) logging.debugFP(LOG_NAME, 'setLocale', this.profile, `Locale check ${date_check}`);
        this.locale = locale;
      } catch (err) {
        logging.errorFP(LOG_NAME, 'get', this.profile, `Invalid locale ${lc}`, err);
      }
 
    }
  }

  setOffset(o: string) {
    const q_offset = o ? parseInt(o, 10) : undefined; 
    const offset = q_offset !== undefined  && !isNaN(q_offset) ? q_offset : undefined;
    if (offset !== undefined) this.offset = offset;
  }

  setTimeZone(tz: string) {
    if (tz) {
      try {
        // check timezone
        const timeZone = tz.trim().replace(/ (:?\d+)$/, '+$1');
        const date_check = new Date().toLocaleString(this.locale, {timeZone});
        if (logging.isDebug(this.profile)) logging.debugFP(LOG_NAME, 'setTimezone', this.profile, `TimeZone check ${date_check}`);
        this.timeZone = timeZone;
      } catch (err) {
        logging.errorFP(LOG_NAME, 'get', this.profile, `Invalid timeZone ${tz}`, err);
      }
    }
  }


  nameAccount(account_type: AuthProviders, account_id: string, name: string) {
    if (this.accounts[account_type] && this.accounts[account_type][account_id]) {
      if (this.accounts[account_type][account_id].name !== name) {
        this.accounts[account_type][account_id].name = name;
        this.resave = true;
      }
    }
  }

  defaultAccount(account_type: AuthProviders, account_id: string) {
    if (this.accounts[account_type]) {
      for (const profile in this.accounts[account_type]) {
        if (profile === account_id) {
          if (this.accounts[account_type][profile].default !== true) {
            this.accounts[account_type][profile].default = true;
            this.resave = true;
          }
        } else if('default' in this.accounts[account_type][profile]) {
          delete this.accounts[account_type][profile].default
          this.resave = true;
        }
      }
    }
  }

  track(t: Partial<Tracking>, context?: string) {
    if (!t) return;
    if (!this.tracking &&
      t.utm_id ||
      t.utm_source ||
      t.utm_medium ||
      t.utm_campaign ||
      t.utm_term ||
      t.utm_content) this.tracking = new Tracking({
        id: uuid(), 
        profile: this.profile,
        date: new Date(), 
        vanity: this.vanity,
        start: this.start,
        last: this.last,
        context,
    });

    if (this.tracking) {
      this.tracking.profile = this.profile;
      this.tracking.vanity = this.vanity;
      this.tracking.start = this.start;
      this.tracking.last = this.last;
      if (context) this.tracking.context = context;
    }

    if (t.utm_id) this.tracking.utm_id = t.utm_id;
    if (t.utm_source) this.tracking.utm_source = t.utm_source;
    if (t.utm_medium) this.tracking.utm_medium = t.utm_medium;
    if (t.utm_campaign) this.tracking.utm_campaign = t.utm_campaign;
    if (t.utm_term) this.tracking.utm_term = t.utm_term;
    if (t.utm_content) this.tracking.utm_content = t.utm_content;
  }

  clearTrack() {
    this.tracking = null;
  }

  updateAccount(account_type: AuthProviders, account_id: string, tokens: any) {
    ForaUser.ensureAccounts(this);
    // if (account_id) {
    if (!this.accounts[account_type]) this.accounts[account_type] = {};
    this.accounts[account_type][account_id] = tokens;
    this.addAccountSetting(account_type);

    this.resave = true;
  }

  updateNotifications(merge_set?: NotificationSetting[], update?: boolean) {
    // take the combination of merge_set and group notifications, keeping group over merge
    const nset: {[key: string]: {
      notify: NotificationSetting;
      group?: boolean;
      merge?: boolean;
    }[];} = {};

    if (this.loaded_groups) {
      for (const group of Object.values(this.loaded_groups)) {
        if (group.notifications) {
          for (const notify of group.notifications) {
            if (nset[notify.type]) nset[notify.type].push({ notify, group: true });
            else nset[notify.type] = [{notify, group: true}];
          }
        }
      }
    }

    if (merge_set) {
      for (const notify of merge_set) {
        if (nset[notify.type]) nset[notify.type].push({ notify, merge: true });
        else nset[notify.type] = [{notify, merge: true}];
      }
    }

    if (this.settings.notifications) {
      for (const notify of this.settings.notifications) {
        if (nset[notify.type]) nset[notify.type].push({ notify });
        else nset[notify.type] = [{notify}];
      }
    }

    const new_notify: NotificationSetting[] = [];

    for (const notify_set of Object.values(nset)) {
      if (notify_set.length === 1) new_notify.push(notify_set[0].notify);
      else {
        const group_notify = notify_set.filter(n => n.group)[0];
        if (group_notify) new_notify.push(group_notify.notify);
        else if (update) { // merge merge over existing
          const merge_notify = notify_set.filter(n => n.merge)[0];
          if (merge_notify) new_notify.push(merge_notify.notify);
          else {
            const has_notify = notify_set.filter(n => !n.merge)[0];
            if (has_notify) new_notify.push(has_notify.notify);
          }
        } else { // don't override existing
          const has_notify = notify_set.filter(n => !n.merge)[0];
          if (has_notify) new_notify.push(has_notify.notify);
          else {
            const merge_notify = notify_set.filter(n => n.merge)[0];
            if (merge_notify) new_notify.push(merge_notify.notify);
          }
        }
      }
    }

    // always notify onboarding
    if (!new_notify.find(n => n.type === NotificationType.Onboarding)) {
      new_notify.push({ type: NotificationType.Onboarding, email: NotificationEmail.Now, background: false, chat: false });
    }

    const ntypes = Object.values(NotificationType);
    this.settings.notifications = new_notify.filter(n => ntypes.includes(n.type));
  }

  updatePermissions() {
    this.settings.authenticated = AuthProvider.isValid(this.tokens);

    if (!this.settings.info) this.settings.info = { enabled: true };
    if (!this.settings.projects) this.settings.projects = { view: ViewProjects.Manage };
    else if (this.settings.projects.view === null || this.settings.projects.view === undefined) {
      this.settings.projects.view = ViewProjects.Manage;
    }

    if (this.settings.messages) this.settings.messages.enabled = false; // true;
    else this.settings.messages = { enabled: false, disallowed: true };

    if (this.settings.calendars) this.settings.calendars.enabled = false;
    else this.settings.calendars = { enabled: false, disallowed: true, sources: {} };

    for (const provider of Object.keys(this.accounts) as AuthProviders[]) {
      if (this.hasAccount(provider)) {
        // set permissions and settings based on context and token scope
        // let permission_scope: AuthContext = AuthContext.App;
        const permission_scope = AuthProvider.getAuthContext(this, provider);

        // start with most most permission based on tokens
        switch (permission_scope) {
          case AuthContext.AuthChat:
            this.permissions[AuthLevel.Email] = AuthPermissions.None;
            this.permissions[AuthLevel.Organizer] = AuthPermissions.Full;
            this.permissions[AuthLevel.OrganizerSync] = AuthPermissions.Full;
            this.permissions[AuthLevel.Basic] = AuthPermissions.Full;
            this.settings.calendars.enabled = true;
            break;
          case AuthContext.AuthSyncOrganizer:
            this.permissions[AuthLevel.Email] = AuthPermissions.None;
            this.permissions[AuthLevel.Organizer] = AuthPermissions.Full;
            this.permissions[AuthLevel.OrganizerSync] = AuthPermissions.Full;
            this.permissions[AuthLevel.Basic] = AuthPermissions.Full;
            this.settings.calendars.enabled = true;
            break;
          case AuthContext.AuthNoEmail:
          case AuthContext.App:
            this.permissions[AuthLevel.Email] = AuthPermissions.None;
            this.permissions[AuthLevel.Organizer] = AuthPermissions.Full;
            if (!this.permissions[AuthLevel.OrganizerSync]) this.permissions[AuthLevel.OrganizerSync] = AuthPermissions.None;
            this.permissions[AuthLevel.Basic] = AuthPermissions.Full;
            this.settings.calendars.enabled = true;
            break;
          case AuthContext.AuthNoOrganizer:
          default:
            // bare bones
            this.permissions[AuthLevel.Email] = AuthPermissions.None;
            if (!this.permissions[AuthLevel.Organizer]) this.permissions[AuthLevel.Organizer] = AuthPermissions.None;
            if (!this.permissions[AuthLevel.OrganizerSync]) this.permissions[AuthLevel.OrganizerSync] = AuthPermissions.None;
            this.permissions[AuthLevel.Basic] = AuthPermissions.Full;
            break;
        }

        this.updateSetting('people', AuthProvider.getScopesForPeople(this), DisallowableAuthContext.Contact, false, true, provider);
        this.updateSetting('imports', AuthProvider.getScopesForFiles(this), DisallowableAuthContext.File, true, true, provider);
        this.updateSetting('messages', AuthProvider.getScopesForMessages(this), DisallowableAuthContext.Mail, false, true, provider);
        this.updateSetting('notes', null /*AuthProvider.getScopesForNotes(this)*/, DisallowableAuthContext.Note, true, true, provider);
        this.updateSetting('tasks', null /*AuthProvider.getScopesForTasks(this)*/, DisallowableAuthContext.Task, true, true, provider);
        this.updateCalendarSetting();
      }
    }
  }

  protected settingsCleanIncoming(dirty: ForaUserSettings): ForaUserSettings {
    if (logging.isVerbose(this.profile)) DEBUG(`settingsCleanIncoming::${this.profile}::Incoming changes raw = ${util.format(dirty)}`);
    const cleaned = JSON.parse(JSON.stringify(dirty, this.settingsIncomingFieldReplacer));
    if (logging.isVerbose(this.profile)) DEBUG(`settingsCleanIncoming::${this.profile}::Incoming changes cleaned = ${util.format(cleaned)}`);

    if (cleaned.calendars && Object.keys(cleaned.calendars).length && !cleaned.calendars.sources) {
      const sources = cleaned.calendars;

      cleaned.calendars = {
        enabled: true,
        disallowed: false,
        sources,
      }
    }

    return cleaned;
  }

  private settingsIncomingFieldReplacer(key: string, value: any) {
    if (key === 'disallowed') return undefined;
    else if (key === 'useFallback') return undefined;
    else if (key === 'unauthorized') return undefined;
    else return value;
  }

  protected updateCalendarSetting(): void {
    if (!this.settings.calendars) this.settings.calendars = { enabled: false, disallowed: false, sources: {} };
    for (const g_id in this.loaded_groups) {
      const group: Group = this.loaded_groups[g_id];
      if (group.disallowed  && group.disallowed.includes(DisallowableAuthContext.Calendar)) {
        this.settings.calendars.enabled = false;
        this.settings.calendars.disallowed = true;
        break;
        // for (const cal in this.settings.calendars.sources) ForaUser.disallowSetting(this.settings.calendars.sources[cal]);
      }
    }
  }

  /**
   * Update a given setting with the following logic.
   * - IFF there is any group that has it as disallowed
   *   - IFF `use_fallback` is set to true, we tell the setting to use its fallback
   *   - ELSE we disallow the setting
   * - ELSE IF we have the scope for this setting, enable the setting
   * - ELSE
   *   - IFF `use_fallback` is set to true, we tell the setting to use its fallback
   *   - ELSE we disable the setting
   *
   * @param context - The disallowable context we are checking
   * @param use_fallback - Should we direct the setting (and its related code) to use a fallback?
   * @param required_scopes - The scopes that are required for this setting/context to be enabled
   * @param setting_key - The key for the setting we are dealing with
   */
  protected updateSetting(setting_key: string, required_scopes: string[][], context: DisallowableAuthContext, use_fallback: boolean, upgrade_only?: boolean, provider?: AuthProviders): void {
    if (!this.settings[setting_key]) this.settings[setting_key] = { enabled: true, disallowed: true, useFallback: use_fallback };
    const setting = this.settings[setting_key];

    for (const g_id in this.loaded_groups) {
      const group: Group = this.loaded_groups[g_id];
      if (group.disallowed && group.disallowed.includes(context)) return ForaUser.disallowSetting(setting, use_fallback);
    }

    const tokens = this.getTokens(provider);

    if (setting.unauthorized) {
      // Leave it as it was
    } else {
      setting.disallowed = false;
      setting.unauthorized = false;

      if (tokens && required_scopes && required_scopes.length && 
          required_scopes.reduce((passed,scope) => passed || stringIncludesAll(tokens.scope, scope), false)) {
        if (!('enabled' in setting)) setting.enabled = true;
        setting.enabled = true;
        setting.useFallback = false;
      } else if (use_fallback) {
        if (!('enabled' in setting)) setting.enabled = true;
        setting.enabled = true;
        setting.useFallback = true;
      } else {
        if (!upgrade_only) setting.enabled = false;
        setting.useFallback = false;
      }
    }
  }

  setSetting(setting, value): Promise<void> {
    if (logging.isVerbose(this.profile)) logging.debugFP(LOG_NAME, 'setSetting', this.profile, `Setting ${setting} before = ${util.format(this.settings[setting])}`);
    this.settings[setting] = _.merge(this.settings[setting], value);
    if (logging.isVerbose(this.profile)) logging.debugFP(LOG_NAME, 'setSetting', this.profile, `Setting ${setting} after = ${util.format(this.settings[setting])}`);

    if (this.isAuthenticatedNonGuest()) this.resave = true;
    return;
  }

  private serializeDataSources() {
    const jsonConvert = new JsonConvert();
    if (config.isSilly()) jsonConvert.operationMode = OperationMode.LOGGING;
    jsonConvert.ignorePrimitiveChecks = false; // don't allow assigning number to string etc.
    jsonConvert.valueCheckingMode = ValueCheckingMode.ALLOW_NULL;

    const serialized = {};
    _.forOwn(this.data_sources, (value, key) => {
      if (logging.isVerbose(this.profile)) DEBUG('serializeDataSources: %o before = %O', key, value);
      for (const account in value) {
        if (!serialized[key]) serialized[key] = {};
        serialized[key][account] = jsonConvert.serialize(value[account]);
      }
      if (logging.isVerbose(this.profile)) DEBUG('serializeDataSources: %o after = %O', key, serialized[key]);
    });
    return Buffer.from(JSON.stringify(serialized)).toString('base64');
  }

  protected static decodeSupportingLegacy(value: any): any {
    if (typeof value === 'string' || value instanceof String) {
      if (value[0] === '{') return JSON.parse(value as string);
      else return JSON.parse(Buffer.from(value as string, 'base64').toString());
    } else {
      return value;
    }
  }

  protected static ensureAccounts(user: User): void {
    if (!user.accounts) user.accounts = {};
    else user.accounts = ForaUser.decodeSupportingLegacy(user.accounts);

    for (const provider in AuthProviders) {
      const value = AuthProviders[provider];
      if (!user.accounts[value]) user.accounts[value] = {};
    }
  }

  private static disallowSetting(setting: { disallowed: boolean; enabled: boolean; unauthorized?: boolean; useFallback?: boolean }, use_fallback?: boolean): void {
    if (setting.unauthorized) {
      // Leave it as it was
    } else if (use_fallback) {
      setting.enabled = true;
      setting.disallowed = true;
      setting.useFallback = true;
      setting.unauthorized = false;
    } else {
      setting.enabled = false;
      setting.disallowed = true;
      setting.useFallback = false;
      setting.unauthorized = false;
    }
  }
}
