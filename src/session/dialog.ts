import diff from 'changeset';
import _ from 'lodash';
import 'lodash.product';
import util from 'util';
import { v4 as uuid } from 'uuid';

import config from '../config';
import lang from '../lang';

import { Action, ActionType, Message as GlobalMessage, InternalError, Notification, Topic } from '../types/globals';
import { Group } from '../types/group';
import { Event, GlobalType, IEntity, Message, Note, Person, Project, Task, getTypes } from '../types/items';
import { Context, Plugin } from '../types/plugins';
import { ANONYMOUS_ID, AuthLevel, AuthProviders, BasicInfo, EntityType, Filter, ForaUserSettings, GroupSettings, Info, NotificationType, PushSetting, Reply, ServerChatResponse, Uid } from '../types/shared';
import { FORA_PROFILE, User } from '../types/user';

import { DAYS, MINUTES, activeEvent, allDay, finishedEvent, future, priorityEvent, sameDay, sameMonth, sameWeek, sameYear, sortEvents, sortMessages, sortTasks, upcomingEvent } from '../utils/datetime';
import { makeFilters } from '../utils/filter';
import { formatFullTime, localeDate, localeDayShort, localeDowMonthDay } from '../utils/format';
import { compress, flatten, formatStack, hash, randomPhrase, saveOne, stringsCompare } from '../utils/funcs';
import { eventInfo, messageInfo, promptInfo, taskInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';
import PersonGraph from '../utils/person_graph';

import { AuthProvider } from '../auth/auth_provider';

import { createCustomer, lookupCustomer } from '../sources/hyperline_controller';
import { askAbout } from '../sources/vertex_controller';

import { internalUpdate } from '../routes/update';
import DataCache from './data_cache';
import * as SessionCache from './session_cache';

import data from '../data';
import Analyses from './entities/analyses';
import Asks from './entities/asks';
import Contracts from './entities/contracts';
import Courses from './entities/courses';
import Events from './entities/events';
import Goals from './entities/goals';
import Messages from './entities/messages';
import Notes from './entities/notes';
import People from './entities/people';
import Projects from './entities/projects';
import Skills from './entities/skills';
import Tasks from './entities/tasks';
import Tutorials from './entities/tutorials';
import Search from './search';

const DEBUG = (require('debug') as any)('fora:utils:dialog');
const LOG_NAME = 'utils.Dialog';

export type Profile = User['id'];
export type DialogSession = any;
export type Command = any;

export const Topics: { [key: string]: Topic } = {}; // tslint:disable-line:variable-name

// We should have a method for adding namespaced topics, much like a redux reducer.
export function TOPIC(t: string): Topic {
  if (Topics[t]) logging.errorF(LOG_NAME, 'TOPIC', `Cannot create topic ${t}, it already exists`, null);
  Topics[t] = t;
  return t;
}

export interface SubDialog {
  id: Uid;
  answers: any[];
  date: Date;
  hint: string;
  info: any[];
  next_topic?: Topic;
  page?: number;
  prompt: Reply[];
  quick_replies?: { [key: string]: ServerChatResponse };
  replies: any[];
  sent: boolean;
  suggestion: string;
  topic: Topic;
  last_command: any;
  open: {type: EntityType, id: Uid}; // v2 open an item
}

export class ActivitySummary {
  event_count: number;
  event_people_ids: Uid[];
  event_time: Date;
  full_day: boolean;
  next: IEntity[];
  next_types: EntityType[];
  people_ids: Uid[];
  quip: string;
  total_time: number; // time in ms
}

export const HANDOFF_PING = 1;
/*export const MIN_BG_PING = 10000;

export const ping = period => ({ ping: period });
export const REPING = config.isEnvOffline() ? ping(1): ping(100);
export const QUICK_PING = config.isEnvOffline() ? ping(10) : ping(1000);
export const DELAY_PING = config.isEnvOffline() ? ping(30) : ping(2000);
export const SLOW_PING = config.isEnvOffline() ? ping(10) : ping(10000);
export const DEFAULT_PING = ping(610000);
export const INTERNAL_PING = ping(Number.MIN_SAFE_INTEGER);
export const NO_PING = ping(-1);*/

const plugins: { [key: string]: Plugin } = {};
const masked_topics: Topic[] = [];
const topic_plugins: { [key: string]: Plugin } = {};
let reset_context: string[] = []; // always reset these

const group_set: {[key:string]: Group} = {};
let groups_loaded: Date = new Date(0);

export default class Dialog {
  actions: Action[] = []; //filled by /src/proc processors
  filters: Filter[] = [];
  context: Context = {}; // used by plugins to keep state
  history: SubDialog[] = []; // track history of replies
  private command: Command = null; // commands to send in next reply

  // for plugin async work
  plugin_processing = false;
  plugin_done = false;
  last_run = false;
  proc_context = undefined;
  proc_topic = undefined;

  re_run = false;

  cache: DataCache = null;

  analyses: Analyses = null;
  asks: Asks = null;
  contracts: Contracts = null;
  courses: Courses = null;
  events: Events = null;
  goals: Goals = null;
  skills: Skills = null;
  messages: Messages = null;
  notes: Notes = null;
  people: People = null;
  projects: Projects = null;
  tasks: Tasks = null;
  tutorials: Tutorials = null;

  search: Search = null;

  group_host: Group = undefined; // The group for the host the current request came in on
  last_error: Error = null;
  loaded: Date;
  message: GlobalMessage;
  read_only = false;
  restore_topic: Topic;
  saved: Date;
  session: DialogSession;
  topic: Topic;

  constructor(session, topic: Topic = null, serde = null, read_only = false) {
    this.read_only = read_only;

    // datastore cached non-persisted attributes
    this.topic = serde ? serde.topic : topic;
    this.session = session;
    this.restore_topic = serde ? serde.restore_topic : null;
    this.context = serde && serde.context ? serde.context : {};
    this.message = serde ? serde.message : null;

    this.cache = new DataCache(null, serde ? serde.me : null, Object.values(group_set), session.id, this.read_only);
    this.restore(serde);

    this.analyses = new Analyses(this.cache);
    this.asks = new Asks(this.cache);
    this.contracts = new Contracts(this.cache);
    this.courses = new Courses(this.cache);
    this.events = new Events(this.cache);
    this.goals = new Goals(this.cache);
    this.skills = new Skills(this.cache);
    this.messages = new Messages(this.cache);
    this.notes = new Notes(this.cache);
    this.people = new People(this.cache);
    this.projects = new Projects(this.cache, this.people, this.contracts, this.search);
    this.tasks = new Tasks(this.cache);
    this.tutorials = new Tutorials(this.cache);

    this.search = new Search(this.cache, this.people);
  }

  restore(serde = null, /*actions?: Action[],*/ context?: Context, history?: SubDialog[]) { 
    this.loaded = serde ? new Date(serde.loaded) : new Date(0);
    this.saved = serde ? new Date(serde.saved) : new Date(0);

    if (!this.cache.groups) {
      this.cache.groups = Object.values(group_set);
      this.user.loadGroups(group_set);
    }

    // this.group_set = serde && serde.group_set ? serde.group_set.map(g => new Group(g)) : [];

    this.group_host = serde && group_set ? group_set[serde.group_host] : undefined;

    if (serde) this.command = serde.command;
    // else this.defaultPing();

    // if (actions) this.actions = actions;
    if (context) this.context = context;
    if (history) {
      this.history = history;
      this.history.forEach(d => {
        d.date = new Date(d.date);
      });
    }

    if (serde) this.cache.user.restore(serde);
  }

  getReadOnly(): Dialog {
    if (this.read_only) return this;
    const ro_dialog = new Dialog(this.session, this.topic, _.merge(this.serializeSession(), this.user.serialize()), true);
    ro_dialog.cache.clone(this.cache);
    ro_dialog.contracts = this.contracts;
    ro_dialog.events = this.events;
    ro_dialog.messages = this.messages;
    ro_dialog.notes = this.notes;
    ro_dialog.people = this.people;
    ro_dialog.projects = this.projects;
    ro_dialog.tasks = this.tasks;
    ro_dialog.group_host = this.group_host;
    ro_dialog.clearActions();
    return ro_dialog;
  }

  // returns all actions that match the actionType
  actionValues(action_type: ActionType | ActionType[]): any [] {
    if (!Array.isArray(action_type)) action_type = [action_type]
    return this.actions.filter(a => action_type.includes(a.type)).map(a => a.value);
  }

  getActions(action_type: ActionType | ActionType[]): Action[] {
    if (!Array.isArray(action_type)) action_type = [action_type]
    return this.actions.filter(a => action_type.includes(a.type));
  }

  // add info to support an action
  addAction(type: ActionType, values: any | any[], context?: string) {
    if (logging.isVerbose) logging.verboseFP(LOG_NAME, 'addAction', this.user ? this.user.profile : null, `Adding ${type} = ${JSON.stringify(values)} @ ${context}`);
    if (Array.isArray(values)) {
      for (const index in values) this.actions.push({ id: uuid(), value: values[index], type, context });
    } else this.actions.push({ id: uuid(), value: values, type, context });
  }

  makeFilters() {
    this.filters = makeFilters(this.actions, this.message.message);
  }

  addFilters(filters: Filter |Filter[]) {
    if (!this.filters) this.filters = [];
    if (Array.isArray(filters)) this.filters = this.filters.concat(filters);
    else this.filters.push(filters);
  }

  removeFilter(id: Uid) {
    if (this.filters) this.filters = this.filters.filter(f => f.id !== id);
  }

  clearFilters(kwd?: string | string[]) {
    if (kwd) {
      if (!Array.isArray(kwd)) kwd = [kwd.toLowerCase()];
      else kwd = kwd.map(k => k.toLowerCase());
      this.filters.forEach(f => {
        f.conditions = f.conditions.filter(c => !c.value || !c.value.length || 
          !(typeof c.value[0] === 'string') || _.intersection((c.value as string[]).map(c => c ? c.toLowerCase() : ''), kwd).length === 0);
      });
      this.filters = this.filters.filter(f => f.conditions.length);
    }
    else this.filters = [];
  }

  /////////////////////////////////
  // Replies

  addAnswers(answers: string | string[], clear = false, add = true) {
    let curr_dialog = this.currentDialog();
    if (!(answers instanceof Array)) {
      answers = [answers];
    }

    if (clear || curr_dialog == null) curr_dialog = this.newDialog({ answers });
    else {
      if (add) curr_dialog.answers = curr_dialog.answers.concat(answers);
      else curr_dialog.answers = answers;
      curr_dialog.sent = false;
      this.log();
    }

    return curr_dialog;
  }

  addHint(hint: string, clear = false) {
    const curr_dialog = this.currentDialog();
    if (clear || curr_dialog == null) this.newDialog({ hint });
    else {
      curr_dialog.hint = hint;
      curr_dialog.sent = false;
      this.log();
    }
  }

  // adds info to the dialog structure
  addInfo(new_info, clear = false, add = true, keep_next_topic = false) {
    if (!new_info) throw new Error('Attempt to add a null info!');

    let curr_dialog = this.currentDialog();
    if (!(new_info instanceof Array)) new_info = [new_info];
    if (clear || curr_dialog == null) curr_dialog = this.newDialog({ info: new_info, next_topic: keep_next_topic && curr_dialog ? curr_dialog.next_topic : null  });
    else {
      if (add) curr_dialog.info = curr_dialog.info.concat(new_info);
      else curr_dialog.info = new_info;
      curr_dialog.sent = false;
      this.log();
    }

    return curr_dialog;
  }

  setOpen(info: Partial<Info>) {
    let curr_dialog = this.currentDialog();
    if (!curr_dialog) curr_dialog = this.newDialog({open: {type: info.type, id: info.id}});
    else {
      curr_dialog.open = {type: info.type, id: info.id}; 
      this.log();
    }

    return curr_dialog;
  }

  addPage() {
    const last_dialog = this.lastDialog();
    if (last_dialog) last_dialog.page = last_dialog.replies.length - 1;
  }

  // adds a prompt
  addPrompt(new_prompt: string | string[] | BasicInfo | BasicInfo[], clear = false, add = true, keep_next_topic = false) {
    if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'addPrompt', this.user.profile, `utils.dialog.addPrompt: prompt = ${JSON.stringify(new_prompt)}, clear = ${clear}, add = ${add}`);
    let curr_dialog = this.currentDialog();

    let set_prompt: BasicInfo[] = [];
    if (new_prompt instanceof Array) {
      set_prompt = (new_prompt as any[]).map(p => {
        if (p['label']) return p;
        return promptInfo(p);
      }) as BasicInfo[];
    } else {
      if (new_prompt['label']) set_prompt = [new_prompt as BasicInfo];
      else set_prompt = [promptInfo(new_prompt as string)];
    }

    if (clear || curr_dialog === null) curr_dialog = this.newDialog({ prompt: set_prompt, next_topic: keep_next_topic && curr_dialog ? curr_dialog.next_topic : null });
    else {
      if (add) curr_dialog.prompt = curr_dialog.prompt.concat(set_prompt);
      else curr_dialog.prompt = set_prompt;
      curr_dialog.sent = false;
      curr_dialog.date = new Date();
      this.log();
    }

    return curr_dialog;
  }

  addQuickReply(msg: string | string[], new_qr: string | string[] | BasicInfo | BasicInfo[], command?: any) {
    const curr_dialog = this.currentDialog();
    let reply: BasicInfo[] = null;

    if (Array.isArray(new_qr)) reply = Array.from<string | BasicInfo>(new_qr).map(r => (r['label'] ? (r as BasicInfo) : promptInfo(r as string)));
    else reply = new_qr['label'] ? [new_qr as BasicInfo] : [promptInfo(new_qr as string)];

    const quick_replies = {};
    const qr  = {};
    if (command) Object.assign(qr, command);
    qr['id'] = hash(new Date().getTime().toString() + JSON.stringify(reply));
    qr['reply'] = reply;
    
    if (Array.isArray(msg)) {
      for (const m of msg) quick_replies[m.toLowerCase()] = qr;
    } else quick_replies[msg.toLowerCase()] = qr;

    if (curr_dialog === null) this.newDialog({ quick_replies });
    else if (!curr_dialog.quick_replies) curr_dialog.quick_replies = quick_replies;
    else Object.assign(curr_dialog.quick_replies, quick_replies);
  }

  // add replies and record the next topic to go to
  addReplies(new_replies: Partial<IEntity> | Partial<IEntity>[], next_topic = null) {
    if (next_topic && !topic_plugins[next_topic]) {
      logging.errorFP(LOG_NAME, 'addReplies', this.user.profile, `Unknown topic ${next_topic}`, null);
      throw new Error(`Unknown topic ${next_topic}`);
    }

    let replies = [];
    if (Array.isArray(new_replies)) replies = new_replies;
    else if (new_replies) replies.push(new_replies);

    return this.newDialog({
      replies,
      next_topic,
    });
  }

  addSuggestion(suggestion, clear = false) {
    let curr_dialog = this.currentDialog();
    if (clear || curr_dialog == null) curr_dialog = this.newDialog({ suggestion });
    else {
      curr_dialog.suggestion = suggestion;
      curr_dialog.sent = false;
      this.log();
    }
  }

  // add more replies to the last dialog
  appendReplies(new_replies, next_topic = null) {
    const curr_dialog = this.currentDialog();
    if (!(new_replies instanceof Array)) new_replies = [new_replies];

    if (curr_dialog == null) return this.addReplies(new_replies, next_topic);
    else curr_dialog.replies = curr_dialog.replies.concat(new_replies);
    curr_dialog.next_topic = next_topic;
    this.log();
    return curr_dialog;
  }

  //////////////
  // State management

  asyncError(err, context = null) {
    logging.errorFP(LOG_NAME, 'asyncError', this.user.profile, `Out of band Error in ${this.session.id} for ${this.user ? this.user.profile : 'no user'} ${err.args ? JSON.stringify(err.args) : ''}`, err);

    // this gets serialized, can't save the error
    this.context.error = {
      stack: err.stack,
      code: err.code,
      message: err.message,
      name: err.name,
      config: err.config,
      args: err.args,
    };
    SessionCache.quickSave(this).catch(e => logging.errorFP(LOG_NAME, 'asyncError', this.user.profile, 'Error quick saving after async error', e));
    if (context) this.reset(context);
    if (this.isProcessing()) this.doneProcessing();
  }

  cacheExpires() {
    return this.cache.cacheExpires();
  }

  cacheKey() {
    return this.isAuthenticated() ? this.user.profile : this.session.id;
  }

  // return true if there are actions matching the type and value
  checkActionValues(action_type, value) {
    const acts = this.actionValues(action_type);
    if (acts && acts.indexOf(value) > -1) return true;
    return false;
  }

  checkTopic(topic: Topic | Topic[]): boolean {
    if (topic instanceof Array) return topic.includes(this.topic);
    return this.topic === topic;
  }

  // clear out all actions
  clearActions(type: ActionType = null) {
    if (type) this.actions = this.actions.filter(a => a.type !== type);
    else this.actions = [];
  }

  clearCommand() {
    this.command = null;
    if (this.message) this.message.command = null;
  }

  // recent any context for the current dialog
  clearContext(att?: string) {
    if (att) delete this.context[att];
    else this.context = {}; // new Context();

    this.plugin_processing = false;
    this.plugin_done = false;
    this.last_run = false;
  }

  resetContext(exclude = []) {
    for (const context of reset_context) {
      if (!exclude || !exclude.includes(context)) delete this.context[context];
    }

    this.plugin_processing = false;
    this.plugin_done = false;
    this.last_run = false;
  }

  // make sure the last dialog is empty
  clearDialog(keep_topic = false) {
    const curr_dialog = this.currentDialog();

    if (curr_dialog && (curr_dialog.replies.length || curr_dialog.prompt.length || curr_dialog.info)) {
      if (keep_topic) this.newDialog({ next_topic: curr_dialog.next_topic });
      else this.newDialog();
    }
  }

  clearSticky() {
    if (this.command) {
      delete this.command.sticky;
      this.command.unstick = true;
    } else this.command = { unstick: true };
  }

  async createGuest(wait = false) {
    if (this.read_only) throw new InternalError(500, 'Cannot create guest on read only session');
    if (!this.isAuthenticated()) {
      if (wait) {
        this.user.createGuest();
        this.session.guest = true;
        this.session.profile = this.user.profile;
        this.cache.cacheDirty();
        this.cache.cache_hash = {};
        this.groupsLoad(this.group_host?.id);
      } else this.user.create_guest = true;
    }
  }

  currentDialog() {
    return this.lastDialog();
  }

  currentPlugin(): Plugin {
    return topic_plugins[this.topic];
  }

  async deleteSession(clear_context = true) {
    if (clear_context) this.clearContext();
    this.flushCaches();
    this.cache = await SessionCache.deleteSession(this.session.id, this.user);
    this.user = lang.init.FORA_USER();
    await this.loadFora();
    if (!config.isEnvOffline() && this.session.destroy) {
      await new Promise(c => this.session.destroy(c)).catch(e => this.asyncError(e));
    }

    if(this.session.guest) delete this.session.guest;
    if(this.session.profile) delete this.session.profile;
    if(this.session.session_hash) delete this.session.session_hash;

    // if (clear_context) this.clearSessionWait();
  }

  flushCaches(flush_types: EntityType[] = getTypes(), history = true) {
    logging.infoFP(LOG_NAME, 'flushCaches', this.user.profile, `Flushing caches ${flush_types}`);
    if (!this.read_only) this.cache.flushCaches(flush_types);
    this.loaded = new Date(0);
    if (history) {
      this.actions = [];
      this.history = [];
    }
  }

  getCommand() {
    let c = null;
    if (this.command) {
      c = {};
      Object.assign(c, this.command);
    }
    return c;
  }

  getInternalMessages(): string[] {
    const messages = this.user.messages;
    this.user.messages = [];
    if (messages && messages.length) {
      data.users.messagesClear(this.user).catch(e => logging.errorFP(LOG_NAME, 'getInternalMessages', this.user.profile, 'Error clearing internal user messages', e));
    }
    return messages;
  }

  async getInternalNotifications(type: NotificationType = null, clear = true): Promise<Notification[]> {
    const notifications = this.user.notifications.filter(n => !type || n.type === type);
    if (clear && notifications && notifications.length) {
      this.user.notifications = this.user.notifications.filter(n => type && n.type !== type);
      await data.users.notificationsClear(this.user, type).catch(e => logging.errorFP(LOG_NAME, 'getInternalNotifications', this.user.profile, 'Error clearing internal user notifications', e));
    }
    return notifications;
  }

  async clearInternalNotifications(type: NotificationType = null) {
    await this.getInternalNotifications(type);
  }

  async clearInternalNotification(id: Uid) {
    return data.users.notificationClear(this.user, id);
  }

  async generateVanity(me: Person = null) {
    if ((!me && !this.me) || this.user.vanity) return;

    const group = this.getGroup();

    await data.users.generateVanity(this.user, me ? me : this.me, group);
  }

  getPersonInfo(
    person: Partial<Person> = null,
    when: Date = null,
    events: Event[] = null,
    tasks: Task[] = null,
    messages: Message[] = null,
    notes: Note[] = null,
    // projects: Project[] = null,
    // contracts: Contract[] = null
   ) {
    if (!this.isAuthenticated(AuthLevel.Email)) messages = null;
    else if (!messages) {
      if (!person || person.self) messages = this.cache.messages;
      else messages = sortMessages(this.cache.messages.filter(m => 
        (m.sender && m.sender.id === person.id) ||
        (m.recipient && m.recipient.find(r => r.id === person.id))));
    }

    if (!this.isAuthenticated(AuthLevel.Organizer)) {
      events = null;
      tasks = null;
    } else {
      if (!events) {
        if (!person || person.self) events = this.cache.events;
        else events = sortEvents(this.cache.events.filter(e => e.people && e.people.find(p => p.id === person.id)));
      }

      if (!tasks) {
        if (!person || person.self) tasks = sortTasks(Object.values(this.cache.tasks));
        else tasks = sortTasks(Object.values(this.cache.tasks).filter(t => t.people && t.people.find(p => p.id === person.id))); 
      }
    }

    /*if (!notes) {
      if (!person || person.self) notes = Object.values(this.cache.notes);
      else notes = findEntitiesItems(person.id, this.cache.people_notes, this.cache.notes);
    }*/

    /*if (!projects) {
      if (!person || person.self) {
        projects = Object.values(this.cache.projects);
      } else /{
        projects = Object.values(this.cache.projects).filter(p => 
          (p.client && p.client.id === person.id) ||
          (p.contractor && p.contractor.id === person.id) ||
          (p.candidates && p.candidates.find(c => c.id === person.id)));
      }
    }*/

    let contracts;
    if (!person || person.self) contracts = Object.values(this.cache.contracts);
    /*if (!contracts && (!person || person.self)) {
    }*/

    const email_group = this.user.email ? this.cache.groupByEmail(this.user.email) : null;
    let group = this.cache.getGroup(email_group);
    if (!group) group = this.group_host;

    let vanity = person ? person.vanity : null;
    if (this.me && (!person || person.id === this.me.id)) {
      person = this.me;
      person.self = true;
      vanity = this.user.vanity;
    }

    const info = peopleUtils.getPersonInfo(this.user.profile, this.me, person, when, events, tasks, messages, notes, [], contracts, group, vanity, this.user.locale, this.user.timeZone, this.user.offset);

    // if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'getPersonInfo', this.user.profile, JSON.stringify(info));

    return info;
  }

  // return all replies of a given type
  getRepliesType(type: EntityType | GlobalType, first_only = false): any[] {
    let replies = [];
    const curr_dialog: SubDialog = this.currentDialog();

    if (curr_dialog) {
      if (first_only) {
        if (curr_dialog.replies.length && curr_dialog.replies[0].type === type) {
          replies.push(curr_dialog.replies[0]);
        }
      } else {
        replies = curr_dialog.replies.filter(r => r.type === type);
      }
    }

    return replies;
  }

  getLastRepliesType(type: EntityType | EntityType[], recent_only = true): any[] {
    let replies = [];
    if (!Array.isArray(type)) type = [type];
    for(const d of this.history.slice().reverse().slice(0, recent_only ? 2 : this.history.length)) {
      replies = replies.concat(d.replies.filter(r => type.includes(r.type)));
      if (replies.length && recent_only) break;
    }

    return replies;
  }

  getTopicRepliesType(type: EntityType | EntityType[], topics: Topic[]): any[] {
    let replies = [];
    if (!Array.isArray(type)) type = [type];
    for(const d of this.history.slice().reverse()) {
      if (topics.includes(d.topic)) {
        replies = replies.concat(d.replies.filter(r => type.includes(r.type)));
      } else break;
    }
    return replies;
  }

  getGroup(email_group?: Group): Group {
    return this.cache.getGroup(email_group ? email_group : this.group_host);
  }

  groupByEmail(email: string): Group {
    const email_group = this.cache.groupByEmail(email);
    if (email_group) return email_group;
    if (this.group_host && email && this.group_host.email_domain) { 
      if (Array.isArray(this.group_host.email_domain)) {
         if(this.group_host.email_domain.find(ed => email.endsWith(ed))) return this.group_host;
      } else if(email.endsWith(this.group_host.email_domain as any as string)) return this.group_host;
    }
    return null;
  }

  getNotifyGroup(email?: string): Group {
    return this.cache.getNotifyGroup(email ? email : this.user.email, this.group_host);
  }

  groupByHost(hostname: string): Group {
    if (!hostname) {
      logging.warnFP(LOG_NAME, 'groupByHost', this.user.profile, `No hostname`);
      return null;
    }

    if (!group_set) {
      logging.warnFP(LOG_NAME, 'groupByHost', this.user.profile, `Cannot find group host ${hostname}`);
      return null;
    }

    if (hostname) {
      for (const group of Object.values(group_set)) {
        if (group.host && group.host.length && stringsCompare(group.host, hostname, true, true)) return group;
      }
    }

    return null;
  }

  groupById(id: Uid): Group {
    if (id) return group_set[id];
  }

  groupByIdOrHost(id: Uid, host_name: string): Group {
    // make sure groups are up to date
    return id ? this.groupById(id) : host_name ? this.groupByHost(host_name) : null;
  }

  async groupsLoad(hostname: string, reload = false): Promise<Group> {
    let loaded = false;
    if (MINUTES(groups_loaded, 1) < new Date() || !Object.keys(group_set).length || reload) {
      const groups = await data.groups.groups();
      groups.forEach(g => group_set[g.id] = g);
    }

    if (group_set) {
      loaded = true;
      this.user.loadGroups(group_set);
    } else {
      logging.warnFP(LOG_NAME, 'groupsLoad', this.user.profile, 'Groups not loaded');
      return;
    }

    this.cache.groups = Object.values(group_set);
    const host = hostname ? this.groupByHost(hostname) : undefined;
    if (host) {
      loaded = loaded || !this.group_host || host.name !== this.group_host.name;
      this.group_host = host;
    }
    groups_loaded = new Date();

    return this.group_host;
  }

  groupSettings(): {[key: string]: GroupSettings} {
    const group_settings: {[key: string]: GroupSettings} = {};
    for (const group_id in this.user.loaded_groups) {
      const group = this.user.loaded_groups[group_id];
      group_settings[group_id] = {
        id: group_id,
        admin: this.user.isAdmin(group_id),
        name: group.name,
        host: this.group_host && this.group_host.id === group_id,
        hostname: group.host,
        company:group.company_name,
        provider:group.provider,
        notifications:group.notifications,
        imports: group.import_maps && Object.keys(group.import_maps).length ? true : false,
        accounts: group.accounts ? Object.keys(group.accounts) as AuthProviders[] : [],
        redirect: group.redirect,
      };
    }

    if (this.group_host && (!this.isAuthenticated() || this.isGuestAccount())) {
      if (group_settings[this.group_host.id]) group_settings[this.group_host.id].host = true;
      else {
        group_settings[this.group_host.id] = { 
          id: this.group_host.id,
          admin: this.isAuthenticatedNonGuest() && this.user.isAdmin(this.group_host.id),
          host: true,
          name: this.group_host.name,
          company: this.group_host.company_name,
          provider:this.group_host.provider,
          notifications: this.isAuthenticatedNonGuest() ? this.group_host.notifications : null,
          imports: this.isAuthenticatedNonGuest() && this.group_host.import_maps && Object.keys(this.group_host.import_maps).length ? true : false,
          accounts: this.isAuthenticatedNonGuest() && this.group_host.accounts ? Object.keys(this.group_host.accounts) as AuthProviders[] : [],
          redirect: this.group_host.redirect,
        }
      }
    }
    return group_settings;
  }

  startProcessing(context: string) { 
    logging.infoFP(LOG_NAME, 'startProcessing', this.user.profile, `Processing context ${context} topic ${this.topic}`);
    this.plugin_processing = true;
    this.last_run = false;
    this.proc_context = context;
    this.proc_topic = this.topic;
  } 

  async doneProcessing(context?: string ) { 
    logging.infoFP(LOG_NAME, 'doneProcessing', this.user.profile, `Done processing context ${context} topic ${this.proc_topic}`);
    this.last_run = false;
    this.plugin_done = true; 
    this.reRun();
    await this.safeSaveSession(context);
  }

  resetProcessing() {
    logging.infoFP(LOG_NAME, 'resetProcessing', this.user.profile, `Reset processing ${this.proc_context} topic ${this.proc_topic}`);
    this.plugin_processing = false;
    this.plugin_done = false;
    this.last_run = false;
    this.proc_context = undefined;
    this.proc_topic = undefined;
  }

  isProcessing(or_done = true) { 
    if (or_done) return this.plugin_processing || this.plugin_done ;
    return this.plugin_processing && !this.plugin_done 
  }

  isDoneProcessing() { return this.plugin_done; }  

  // returns true on first init
  async initUser(self_person: Person, provider: AuthProviders, context = 'unknown', group_host?: Group): Promise<boolean> {
    logging.infoF(LOG_NAME, 'initUser', `user = ${this.user.id}, self = ${self_person ? self_person.id : 'none'}, provider = ${provider}`);
    if (!this.user.settings.calendars) this.user.settings.calendars = { enabled: false, disallowed: false, sources: {}};
    else if (!this.user.settings.calendars.sources) {
      const sources = Object.keys(this.user.settings.calendars).length ? this.user.settings['calendars'] as any : null;
      this.user.settings.calendars = {
        enabled: sources && Object.keys(sources).length > 0,
        disallowed: false,
        sources,
      }
    }

    if (!this.user.settings.push) this.user.settings.push = PushSetting.Ask;

    await this.groupsLoad(this.group_host?.host, true);

    // API calls using SAML only give us the ID of the user
    if (this.user.email) {
      const parts = this.user.email.split('@');
      if (parts.length > 1) {
        const domain = `@${parts[1]}`;

        if(group_host) {
          // login is on the default host
          if ((!group_host.email_domain || !group_host.email_domain.length || 
              group_host.email_domain.includes(domain)) &&  group_host.auto_add) {
            if (!this.user.groups) this.user.groups = {};
            if (!this.user.groups[group_host.id]) this.user.groups[group_host.id] = [];
          }
        } else if (this.group_host && this.group_host.auto_add) {
          if ((!this.group_host.email_domain || !this.group_host.email_domain.length || 
              this.group_host.email_domain.includes(domain)) && this.group_host.auto_add) {
            if (!this.user.groups) this.user.groups = {};
            if (!this.user.groups[this.group_host.id]) this.user.groups[this.group_host.id] = [];
          }
        } else {
          let group: Group;
          if (this.user.loaded_groups) {
            for (const gid in this.user.loaded_groups) {
              if (this.user.loaded_groups[gid].auto_add && this.user.loaded_groups[gid].email_domain && this.user.loaded_groups[gid].email_domain.includes(domain)) {
                group = this.user.loaded_groups[gid];
                break;
              }
            }
          }

          if (!group) group = await data.groups.byDomain(domain);
          if (group && group.auto_add) {
            if (!this.user.groups) this.user.groups = {};
            if (!this.user.groups[group.id]) this.user.groups[group.id] = [];
          }
        }
      }
    }

    await this.groupsLoad(this.group_host?.name);
    this.user.updatePermissions();

    let existing_user = self_person && self_person.id ? await data.people.byId(this.user, self_person.id) : null;
    if (!existing_user && self_person && self_person.comms) {
      existing_user = await data.people.getUserPerson(this.user, this.user.profile);
    }

    if (!existing_user || !this.me || existing_user.id !== this.me.id) this.flushCaches();

    if (existing_user === null) {
      // for new users, make sure there aren't any old bigquery locks
      await data.plugins.bigQueryPlugin().endOp(this.user.profile);
      this.user.start = new Date();
    } else {
      if (self_person) peopleUtils.mergePeople(existing_user, self_person, true);
      self_person = existing_user;
    }

    // if (!self_person.id || self_person.id === ANONYMOUS_ID || !self_person.id.startsWith('people/a')) self_person.id = `people/a${this.user.profile}`; //self_person.tempId();

    if(self_person) self_person.bio = this.user.bio;

    // subscription
    const customer = await lookupCustomer(this.user);
    if(!customer) await createCustomer(this.user);

    const guser = await data.users.globalRegister(this.user, context);
    await this.setSelf(self_person, true);

    this.user.affiliate = guser.affiliate;

    if (this.read_only) await data.users.quickSave(this.user);
    else {
      await data.users.save(this.user, true);
      if (this.me) {
        if(!this.me.displayName || this.me.id === 'people/anonymous') this.me = await data.people.getUserPerson(this.user);
        await this.cache.cachePerson(this.me, false);
      }
      else if(this.isAuthenticatedNonGuest()) throw new InternalError(500, 'Initializing with no me');
      this.cache.mapPersonDetails(this.me);
    }

    if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'initUser', this.user.id, `Self person = ${util.inspect(this.me)} User = ${util.inspect(this.user)}`);

    // make sure to clean up the session id cache so this doesn't get overwritten
    if (!this.read_only) await SessionCache.deleteSession(this.session.id);

    return existing_user !== null;
  }

  isAnonymousAccount() {
    return !this.user || this.user.isAnonymousAccount();
  }

  // scope can be organizer or email and values are 'full' or 'none'
  isAuthenticated(scope?:AuthLevel) {
    return this.user && this.user.isAuthenticated(scope);
  }

  isAuthenticatedNonGuest(scope = null) {
    return this.isAuthenticated(scope) && !this.isGuestAccount();
  }

  isGuestAccount(): boolean {
    return this.user && this.user.isGuestAccount();
  }

  isMaskedTopic() {
    return masked_topics.includes(this.topic);
  }

  lastChat() {
    // when did we last chat with the user?
    let last_date = new Date(0);
    for (let index = this.history.length - 1; index >= 0; index--) {
      const a_dialog: SubDialog = this.history[index];
      if (a_dialog.prompt.length) {
        last_date = new Date(a_dialog.date);
        break;
      }
    }
    return last_date;
  }

  // return the last dialog
  lastDialog(prev = 0) {
    if (this.history.length > prev) return this.history[this.history.length - prev - 1];
    return null;
  }

  lastPrompt(context: string, prompt: string | string[] | BasicInfo | BasicInfo[], options: {sticky?: boolean; clear?: boolean; add?: boolean; hide?: number, open?: Partial<Info>} = {}) {
    this.addPrompt(prompt, options && options.clear !== undefined? options.clear : false, options && options.add !== undefined ? options.add: true);
    if (options.sticky) {
      this.setSticky();
      //this.clearPing();
    } // else this.defaultPing();
    if (options.hide !== null && options.hide !== undefined) this.setHide(options.hide);
    if (options.open) this.setOpen(options.open);
    this.clearContext(context);
    this.setTopic(Topics.DEFAULT);
  }

  async loadFora() {
    if (this.user.isAuthenticated()) return;

    logging.infoFP(LOG_NAME, 'loadFora', this.user.profile, `Loading default Fora data for ${this.session.id} ${this.user.profile}`);
    // loads information about Fora
    const fora = lang.init.FORA_INFO();
    if (this.me && this.me.id === fora.id) {
      return; //people[fora.id]) return;
    }

    fora.self = !this.isAuthenticated();
    if (fora.self) this.setSelf(fora);

    const other_people = [];
    for (const person of lang.about.ABOUT_INFO) {
      other_people.push(this.cache.cachePerson(person));
    }

    await Promise.all(other_people);

    this.loaded = new Date();
  }

  async loadInternalMessages(read_only: boolean) {
    if (this.isAuthenticatedNonGuest()) {
      const [messages, notifications] = await data.users.messagesAndNotifications(this.user);
      if (messages) {
        if (!this.user.messages) this.user.messages = [];
        for (const index in messages) saveOne(this.user.messages, messages[index]);
      }

      // const notifications = await data.users.notifications(this.user, read_only);
      if (notifications) {
        if (!this.user.notifications) this.user.notifications = [];
        this.user.notifications = _.uniqWith(this.user.notifications.concat(notifications), (a: Notification, b: Notification) => {
          if (!a || !b) return false;
          if (!a.webpush || !b.webpush) return false;
          const awp = a.webpush;
          const bwp = b.webpush;
          if (awp.notification && bwp.notification && awp.notification.tag === bwp.notification.tag) return true;
          if (awp.fcm_options && bwp.fcm_options && awp.fcm_options.link === bwp.fcm_options.link) return true;
          if (awp.data && awp.data.message && bwp.data && bwp.data.message && awp.data.message === bwp.data.message) return true;
          return false;
        });
      }
    }
  }

  log(): void {
    const save_log = this.saveLog();
    if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'log', this.user.profile, `DIALOG: ${JSON.stringify(save_log)}`);
  }

  async logMessage() {
    if (this.message) {
      logging.infoFP(LOG_NAME, 'logMessage', this.user.profile, this.message.message);
      data.plugins.storagePlugin().logMessage(this.message).catch(e => logging.errorFP(LOG_NAME, 'logMessage', this.user.profile, 'Error logging message', e));
    } else logging.warnFP(LOG_NAME, 'logMessage', this.user.profile, 'No message');
  }

  mapGroupProjectSettings(project: Project) {
    this.projects.mapGroupSettings(project, this.group_host, Object.values(this.user.loaded_groups));
  }

  get me(): Person {
    return this.cache.me;
  }

  set me(nm: Person) {
    this.cache.me = nm;
  }

  get user() {
    return this.cache.user;
  }

  set user(u) {
    this.cache.user = u;
  }
 
  newDialog(proto?: Partial<SubDialog>): SubDialog {
    const now = new Date();

    const new_dialog: SubDialog = {
      id: uuid(),
      prompt: [],
      info: [],
      sent: false,
      replies: [],
      topic: this.topic,
      date: now,
      next_topic: null,
      answers: [],
      hint: null,
      suggestion: null,
      last_command: null,
      open: null,
    };

    if (proto) Object.assign(new_dialog, proto);

    this.history.push(new_dialog);
    // this.log();
    return new_dialog;
  }

  // create a new dialog that is the same as the previous minus the first reply
  nextDialog(include: any = null): SubDialog {
    const curr_dialog = this.currentDialog();

    if (curr_dialog !== null) {
      let replies = curr_dialog.replies;
      curr_dialog.replies = [replies.shift()];
      if (include) {
        const ireplies = [];
        for (const index in replies) {
          const reply = replies[index];
          if (include(reply)) ireplies.push(reply);
        }
        replies = ireplies;
      }
      return this.addReplies(replies, curr_dialog.next_topic);
    }
  }

  nextTopic(next_topic?: Topic) {
    let curr_dialog = this.currentDialog();
    if (!next_topic) next_topic = this.topic;
    if (curr_dialog == null) curr_dialog = this.newDialog({ next_topic });
    else curr_dialog.next_topic = next_topic;
    return curr_dialog;
  }

  /*noPing() {
    this._setPing(NO_PING);
  }*/

  async queryPeople(people: Partial<Person>[]): Promise<PersonGraph[]> {
    logging.infoFP(LOG_NAME, 'queryPeople', this.user.profile, `Querying ${people.length} people`);
    const local_people = await this.people.findByComms(parsers.findEmail(flatten(people.map(p => p.comms))));
    return Promise.all(people.map(async p => {
      const lp = local_people.find(l => _.intersection(l.comms, p.comms).length);
      return new PersonGraph(this, p, lp ? lp.id : p.id);
    }));
  }

  /* quickPing() {
    this._setPing(QUICK_PING);
  } */

  // reload data from local store
  async reloadData(reload_types: (EntityType|GlobalType)[] = null, force = false) {
    if (this.isAuthenticated()) {
      if (!reload_types) {
        reload_types = [GlobalType.Group];
        reload_types = reload_types.concat(getTypes());
      }
      logging.infoFP(LOG_NAME, 'reloadData', this.user.profile, `reloadCache: ${this.user.profile} last ${this.loaded} refresh ${this.user.last_refresh}, force: ${force}`);
      await this.cache.reloadCache(reload_types as EntityType [], force);
      // await this.cache.saveCache(this.cacheExpires());
      if (reload_types.includes(GlobalType.Group)) {
        await this.groupsLoad(this.group_host?.host, true);
      }
      this.loaded = new Date();
    } else if (this.loaded.getTime() === 0) await this.loadFora();
    else logging.infoFP(LOG_NAME, 'reloadData', this.user.profile, `Skipping cache reload for ${this.session.id} ${this.user.profile} loaded ${this.loaded}`);
  }

  removeAction(action: Action) {
    this.actions = this.actions.filter(a => a.id !== action.id);
  }

  // remove info to support an action
  removeActions(type: ActionType, value: any = null, ignore_case = false) {
    if (!value) this.actions = this.actions.filter(a => a.type !== type);
    else if(typeof value === 'object') {
      if (Array.isArray(value)) {
        this.actions = this.actions.filter(a => !a.value || a.type !== type ||  (ignore_case && typeof a.value !== 'string') ||
          value.map(v => diff(ignore_case ? a.value.toLowerCase() : a.value, v).find(x => x.type === 'put')).filter(x => !x).length == 0 
        );
      } else {
        this.actions = this.actions.filter(a => !a.value || a.type !== type || (ignore_case && typeof a.value !== 'string') ||
          diff(ignore_case ? a.value.toLowerCase() : a.value, value).find(x => x.type === 'put') === undefined);
      }
    } else {
      if (ignore_case && typeof value === 'string') value = value.toLowerCase();
      else ignore_case = false;

      if (Array.isArray(value)) {
        this.actions = this.actions.filter(a => !a.value || a.type !== type || (ignore_case && typeof a.value !== 'string') ||
          ( (ignore_case && typeof a.value === 'string' && !value.includes(a.value.toLowerCase())) ||
           (!ignore_case && !value.includes(a.value) )));
      } else {
        this.actions = this.actions.filter(a => !a.value || a.type !== type || (ignore_case && typeof a.value !== 'string') ||
         ( (ignore_case && typeof a.value === 'string' && a.value.toLowerCase() !== value) || 
          (!ignore_case && a.value !== value)));
      }
    }
    logging.infoFP(LOG_NAME, 'removeActions', this.user.profile, `Removed ${type} actions matching ${JSON.stringify(value)} :: ${JSON.stringify(this.actions)}`);
  }

  removeActionContext(kwd: string | string[]) {
    if (!Array.isArray(kwd)) kwd = [kwd.toLowerCase()];
    else kwd = kwd.map(k => k.toLowerCase());

    this.actions = this.actions.filter(a => !a.context || !parsers.checkKeyword(a.context.toLowerCase(), kwd));
  }

  /*rePing() {
    this._setPing(REPING);
  }*/

  reRun() {
    this.re_run = true;
  }

  clearRun() {
    this.re_run = false;
  }

  get doReRun() {
    return this.re_run;
  }

  reset(context: string = null, keep_topic = false) {
    logging.infoFP(LOG_NAME, 'reset', this.user.profile, `clear ${context} topic ${keep_topic ? this.topic : 'Topics.DEFAULT'}`);
    if (context) this.clearContext(context);
    // this.clearPing();
    // this.defaultPing();
    const current_dialog = this.currentDialog();
    if (!current_dialog || ((!current_dialog.prompt || !current_dialog.prompt.length) &&
      (!current_dialog.info || !current_dialog.info.length))) this.setHide();
    if (!keep_topic) this.setTopic(Topics.DEFAULT);
  }

  restoreTopic() {
    if (this.restore_topic !== null) {
      this.topic = this.restore_topic;
      this.restore_topic = null;
    }
  }

  safeSaveSession(merge_context?: string) {
    const stack = formatStack(Error().stack);
    const merge = merge_context ? `merge ${merge_context} ` : '';
    logging.infoFP(LOG_NAME, 'safeSaveSession', this.user.profile, `Async save of ${this.session.id} Topic ${this.topic} ${merge}from ${stack}`);
    return this.saveSession(true, merge_context).catch(e => this.asyncError(e));
  }

  async saveSession(persist = false, merge_context: string = null) {
    if (this.user.create_guest ) {
      let context
      if (this.isAnonymousAccount()) {
        context = {}
        Object.assign(context, this.context);
      }
      await SessionCache.waitForLoad(this.user.profile, this.session.id); 
      this.user.createGuest();
      this.session.guest = true;
      this.session.profile = this.user.profile;
      if (context) Object.assign(this.context, context);
      this.cache.cacheDirty();
      this.cache.cache_hash = {};
      this.user.create_guest = false;
    }

    let profile = this.user.profile;
    if (!profile) profile = FORA_PROFILE;

    if (config.isEnvOffline()) {
      logging.infoFP(LOG_NAME, 'context', profile, JSON.stringify(this.context));
    } else if (this.user.debug || config.isEnvDevelopment()) {
      if (Object.keys(this.context).length) logging.infoFP(LOG_NAME, 'context', profile, JSON.stringify(Object.keys(this.context)));
      if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'context', profile, JSON.stringify(this.context));
    }

    if (this.cache.timed_out) {
      logging.warnFP(LOG_NAME, 'saveSession', this.user.profile, 'Releasing session due to timeout');
      // this.clearSessionWait();
    }

    if (this.isAuthenticatedNonGuest()) await data.users.track(this.user);

    if (this.read_only) {
      logging.infoFP(LOG_NAME, 'saveSession', this.user.profile, `Not saving read only session ${this.session.id} in ${this.topic}`);

      // undirty the caches
      this.cache.clearCacheDirty();

      if (this.user.resave) {
        await data.users.save(this.user);
      }
    } else {
      logging.infoFP(LOG_NAME, 'saveSession', this.user.profile, `Saving session ${this.session.id}${this.session.guest ? ' guest' : ''} Topic ${this.topic}`);
      // keep at most 5 history
      let i = 5;
      while(compress(this.history).length > 1048487 && i < this.history.length) {
        this.history = this.history.slice(this.history.length - i);
        i = i + 1;
      }

      if (this.user.profile) {
        const expires: number = this.cacheExpires();

        if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'saveSession', this.user.profile, `Saving caches for ${this.session.id}: '${this.cache.cache_dirty}'`);
        await this.cache.saveCache(expires);
      }

      if (logging.isVerbose(this.user.profile)) logging.verboseFP(LOG_NAME, 'saveSession', this.user.profile, `Cleaning caches for ${this.session.id}: '${this.cache.cache_dirty}'`);
      await this.cache.cleanCache();

      if (this.user.resave) {
        await data.users.save(this.user);
        await data.users.globalRegister(this.user);
      }

      // force save new dialog over older cache
      if (persist && (!this.saved || !(new Date(this.saved).getTime()))) this.saved = new Date();

      await SessionCache.saveSession(this, merge_context, persist);
    }
  }

  async saveSettings(settings: ForaUserSettings = null, override = false) {
    if (settings) this.user.saveSettings(settings, this.cache, override);
    if (this.user.isAuthenticatedNonGuest()) await data.users.save(this.user);
  }

  async selectCalendar(calid, select) {
    if (this.user.settings.calendars.sources && this.user.settings.calendars.sources[calid]) {
      this.user.settings.calendars.sources[calid].selected = select;
      const saves = [];
      saves.push(data.users.save(this.user));
      if (!this.user.refreshing) saves.push(data.users.save(this.user, true));
      await Promise.all(saves);
    }
  }

  async sendInternalMessage(msg: string): Promise<void> {
    await data.users.message(this.user, msg);
    if (!this.user.messages) this.user.messages = [];
    saveOne(this.user.messages, msg);
  }

  serializeSession(): DialogSession {
    const ssd = {
      profile: this.user.profile,
      topic: this.topic,
      me: this.me ? { id: this.me.id, vanity: this.user.vanity } : null,
      restore_topic: this.restore_topic,
      messages: this.user.messages.slice(0,1),
      notifications: this.user.notifications.slice(0,1),
      last: this.user.last,
      loaded: this.loaded,
      saved: this.saved,
      groups: this.user.groups,
      group_host: this.group_host?.id,
    };

    // _.merge(ssd, this.user.serialize());
    return ssd;
  }

  // clears the chats before the next prompt
  setClear() {
    if (this.command) this.command.clear = true;
    else this.command = { clear: true };
  }

  // hides the chats
  setHide(timeout = 1) {
    if (this.command) this.command.hide = timeout;
    else this.command = { hide: timeout };
  }

  setClose() {
    if (this.command) this.command.close = true;
    else this.command = { close: true };
  }

  // sets the max chat height as percent 1-100
  setMax(max: number = null) {
    if (max && max > 0 && max < 100) {
      if (this.command) this.command.max = max;
      else this.command = { max };
    } else if (this.command) delete this.command.max;
  }

  // sets the client redirect
  setRedirect(url) {
    if (this.command) this.command.redirect = url;
    else this.command = { redirect: url };
  }

  setRefresh() {
    if (this.command) this.command.refresh = true;
    else this.command = { refresh: true };
  }

  setLogout() {
    if (this.command) this.command.logout = true;
    else this.command = { logout: true }
  }

  async setSelf(person: Person = null, resolve = false, do_save = true): Promise<Person> {
    let loaded = false;
    let no_diff;
    const emails = this.me && this.me.comms ? parsers.findEmail(this.me.comms) : null;
    if (!person && (!this.me || !emails || !emails.length) && this.user.email) {
      loaded = true;
      logging.warnFP(LOG_NAME, 'setSelf', this.user.profile, `Re-fetching self because ${!person ? 'no arg' : ''} ${!this.me ? 'no me' : ''} ${!emails || !emails.length ? 'no emails' : ''}`);
      const me_people = this.me && this.me.id ? await this.cache.loadPeopleIds(this.me.id) : null;
      if (me_people && me_people[0] && me_people[0].displayName && me_people[0].comms && me_people[0].comms.length) {
        const emails = parsers.findEmail(me_people[0].comms);
        if (emails && emails.length && emails.includes(this.user.email)) person = me_people[0];
      }
      if (!person) person = await data.people.getUserPerson(this.user, this.user.profile);
      if (this.me) no_diff = diff(this.me, person);
    }

    let me = this.me;
    if (me) {
      if(!me.id) logging.warnFP(LOG_NAME, 'setSelf', this.user.profile, `Current me has no id ${JSON.stringify(this.me)}`);
      else if(this.isAuthenticatedNonGuest() && me.id === `people/${FORA_PROFILE}`) {
        logging.warnFP(LOG_NAME, 'setSelf', this.user.profile, `Current me has anonymous id ${JSON.stringify(this.me)}`);
        this.me = null;
        me = null;
      }
    }

    let save = null; //!!this.me == true ? null : 'me';
    if (person) {
      if (resolve) {
        const sr = await data.people.resolveSavePerson(this.user, person);
        if (sr.remove && this.user.isAuthenticatedNonGuest()) {
          /* for (const cindex in sr.remove.comms) {
            data.plugins.cachePlugin().clearCacheAttVal(this.user.profile, EntityType.Person, 'comms', sr.remove.comms[cindex]);
          }*/
          if (!sr.remove.comms.includes(this.user.email) && sr.remove.id !== person.id) {
            logging.warnFP(LOG_NAME, 'setSelf', this.user.profile, `Removing self ${sr.remove.id} to set ${person.id}`);
            sr.remove.self = false;
            data.plugins.cachePlugin().clearCacheAttVal(this.user.profile, EntityType.Person, 'id', sr.remove.id);
            await data.people.delete(this.user, sr.remove);
          }
        }
        if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'setSelf', this.user.profile, `Setting me to resolve ${JSON.stringify(sr.save)}`);
        //restore but not anonymous
        if (sr.save) {
           if(sr.save.id === ANONYMOUS_ID) {
            data.plugins.cachePlugin().clearCacheAttVal(this.user.profile, EntityType.Person, 'id', sr.save.id);
            // await data.people.delete(this.user, sr.save);
           } else me = sr.save;
        }
        if (me && !me.id) logging.warnFP(LOG_NAME, 'setSelf', this.user.profile, `Resolve save me has no id ${JSON.stringify(this.me)}`);
        if (!me) {
          me = new Person(person);
          save = 'person';
        }
      } else {
        if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'setSelf', this.user.profile, `Setting me to person ${JSON.stringify(person)}`);
        me = new Person(person);
        if (me && !me.id) logging.warnFP(LOG_NAME, 'setSelf', this.user.profile, `Person me has no id ${JSON.stringify(this.me)}`);
      }

      me.bio = person.bio;
      this.cache.user.bio = person.bio;
    } else {
      if (me && !me.bio && this.cache.user.bio) me.bio = this.cache.user.bio;
    }

    if (me && this.user.isAuthenticatedNonGuest() ) {
      if (!me.id) {
        logging.warnFP(LOG_NAME, 'setSelf', this.user.profile, `Trying to set me without ID about to fail: ${JSON.stringify(me)} ::: ${this.me ? JSON.stringify(this.me) : 'no me set yet'}`);
      }

      const group = this.getGroup();
      if (this.user.isAuthenticatedNonGuest() && !this.user.vanity) await this.generateVanity(me);

      const host = group && group.host ? group.host : config.get('DEFAULT_HOST');
      const vanity_url = `https://${host}/profile/${this.user.vanity}`;

      const csave = me.checkSelf(this.user.profile, this.user.vanity, vanity_url);
      if (!save) save = csave;

      const me_diff = diff(this.me, me);
      const meta_diff = no_diff ? diff(me_diff, no_diff) : me_diff;
      const net_diff = meta_diff.filter(d => d.key && d.key.length && d.value && (d.value.length || (typeof d.value === 'object' && Object.keys(d.value).length)));
      if ((this.me && net_diff.length) || !!save) {
        if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'setSelf', this.user.profile, `Me diff: ${JSON.stringify(me_diff)}`);
        if (me.id === null || me.id === undefined || me.id.length <= 0) throw new InternalError(500, `Cannot set self to person with no id, was "${this.me ? this.me.id : ''}"`, me);
        if (me.displayName === null || me.displayName === undefined || me.displayName.length <= 0) throw new InternalError(500, `Cannot set self to person with no displayName ${me.id}, was "${this.me ? this.me.displayName : ''}"`, me);

        this.me = me;
        this.cache.cachePerson(this.me);

        if (this.user.isAuthenticatedNonGuest() && (net_diff.length || (do_save && (save && save !== 'me'))) && !loaded && !this.read_only) {
          if (save !== null) logging.infoFP(LOG_NAME, 'setSelf', this.user.profile, `Saving self to update ${save}`);
          else logging.infoFP(LOG_NAME, 'setSelf', this.user.profile, `Saving self because diff: ${JSON.stringify(me_diff)}`);
          this.me = await this.people.save(this.me);
        }
      } else {
        if(!this.me) {
          logging.infoFP(LOG_NAME, 'setSelf', this.user.profile, `Setting but not saving because no me, or no diff ${net_diff} or no save ${save}`);
          this.me = me;
        } else logging.infoFP(LOG_NAME, 'setSelf', this.user.profile, `Not saving because no diff ${net_diff} or no save ${save}`);
      }
    } else {
      if (me) {
        logging.infoFP(LOG_NAME, 'setSelf', this.user.profile, `Not saving unauthenticated self`);
        if(!this.me) this.me = me;
      } else logging.warnFP(LOG_NAME, 'setSelf', this.user.profile, `Trying to set self with no me, returning ${this.me ? this.me.id : 'no id'} from ${formatStack(new Error().stack)}`);
    }

    return this.me;
  }

  setSticky() {
    if (this.command) {
      this.command.sticky = true;
      delete this.command.hide;
    }
    else this.command = { sticky: true };
  }

  setTopic(topic: Topic, save = false) {
    if (!topic || !topic_plugins[topic]) {
      logging.errorFP(LOG_NAME, 'setTopic', this.user.profile, `Unknown topic ${topic}`, null);
      throw new Error(`Unknown topic ${topic}`);
    }
    if (topic === this.topic) return;
    logging.infoFP(LOG_NAME, 'setTopic', this.user.profile, `Setting topic from ${this.isMaskedTopic() ? 'MASKED' : ''} ${this.topic} to ${topic}${save ? ' and saving restore topic' : '' }`);
    if (save) this.restore_topic = this.topic;
    this.topic = topic;
  }

  // check whether to refresh session cache from google, return true if refreshed
  updateSession(force_refresh = false, refreshTypes = getTypes()) {
    if (!this.isAuthenticated()) return false;

    const now = new Date();
    logging.infoFP(LOG_NAME, 'updateSession', this.user.profile, `Checking for update at ${now} to ${this.user.profile} from ${new Date(this.user.last)}, force: ${force_refresh}`);
    if (force_refresh) {
      new Promise(async () => {
        logging.infoFP(LOG_NAME, 'updateSession', this.user.profile, `Updating ${this.user.profile}`);
        /* this.user.full_refresh = refreshTypes;
        this.user.refreshing = true;
        await data.users.save(this.user, true);*/

        // Run refresh on any plugins that are enabled
        await internalUpdate({profile: this.user.profile, reset: refreshTypes});
      }).catch(e => logging.errorFP(LOG_NAME, 'updateSession', this.user.profile, 'Error reloading data after on-demand update', e));

      return true;
    } else if (new Date(this.user.last) < MINUTES(now, -30)) {
      internalUpdate({profile: this.user.profile});
    }

    return false;
  }

  // return settings
  userSettings(): ForaUserSettings {
    const settings: ForaUserSettings = {} as ForaUserSettings;
    Object.assign(settings, this.user.settings);

    // check that active entities exist
    if (settings.notes && settings.notes.active) {
      if (!this.cache.tasks[settings.notes.active.id]) settings.notes.active = null; 
    }

    if (settings.projects && settings.projects.active) {
      if (!this.cache.projects[settings.projects.active.id]) settings.projects.active = null; 
    }

    const group_settings = this.group_host ? this.group_host :
      this.user.loaded_groups ? Object.values(this.user.loaded_groups).find(g => !g.host || !g.host.length) : null;

    if (group_settings) {
      if (settings.projects) {
        settings.projects.skip_payment = settings.projects.skip_payment || group_settings.skip_payment; 
        settings.projects.skip_contracting = settings.projects.skip_contracting || group_settings.skip_contracting; 
      }

      // group settings
      if (this.user.isAuthenticatedNonGuest() && this.user.groups && Object.keys(this.user.groups).includes(group_settings.id)) {
        if (group_settings.learning) {
          if (!settings.learning) settings.learning = { disallowed: false, enabled: true};
          else if (!settings.learning.disallowed) settings.learning.enabled = true;
        } else if (!settings.learning) settings.learning = { disallowed: false, enabled: false};

        if (group_settings.teams && this.user.isAdmin(group_settings.id)) {
          if (!settings.analyses) settings.analyses = { disallowed: false, enabled: true};
          else settings.analyses.enabled = true;

          if (!settings.development) settings.development = { disallowed: false, enabled: true};
          else settings.development.enabled = true;
        } else {
          if (settings.analyses) settings.analyses.enabled = false;
          if (settings.development) settings.development.enabled = false;
        }

        if (group_settings.sourcing) {
          if (settings.projects) settings.projects.sourcing = true;
          else settings.projects = { sourcing: true };
        } else if(settings.projects) settings.projects.sourcing = false;

        if (group_settings.widgets) {
          settings.widgets = _.uniq([...settings.widgets ? settings.widgets : [], ...group_settings.widgets]);
        }
      }

      if (group_settings.accounts) {
        const group_accounts = Object.keys(group_settings.accounts) as AuthProviders[];
        if (!settings.providers) settings.providers = [this.user.provider];
        for(const account of group_accounts) {
          if (!settings.providers.includes(account)) settings.providers.push(account);
        }
      }
    } else {
      if(settings.analyses) delete settings.analyses;
      if(settings.development) delete settings.development;
    }

    return settings;
  }

  runAsync(context: string, c: () => void) {
    if (this.isProcessing()) return;
    this.startProcessing(context);
    this.waitForRun(c).catch(e => this.asyncError(e))
    .finally(() => 
      this.doneProcessing(context)
    );
  }

  async runSession() { 
    await this.waitForRun(); 
  }

  private async waitForRun(c?: () => void) {
    let has_session = false
    while (!has_session) {
      has_session = await SessionCache.waitForSession(this.user.profile, 'dialog', this.session.id, uuid(), Error().stack, this.isProcessing(false) ? 240000 : undefined, this.isProcessing(false) ? false : true);
      if (!has_session) await new Promise(c => setTimeout(c, 1000));
    }
    await SessionCache.restoreSession(this, null, this.saved, this.read_only);
    if (c) {
      logging.infoFP(LOG_NAME, 'waitForRun', this.user.profile, `Running async`);
      await c();
      logging.infoFP(LOG_NAME, 'waitForRun', this.user.profile, `Done running async`);
    }
    return;
  }

  clearSessionWait(stack_id: string) {
    this.cache.timed_out = false;
    this.cache.doneReloading();
    if (!this.read_only) {
      SessionCache.clearSessionWait(this.user.profile, this.session.id, stack_id, Error().stack);
      // return SessionCache.releaseSession(this.user.profile, this.session.id, Error().stack);
    }
    else logging.infoFP(LOG_NAME, 'clearSessionWait', this.user.profile, `Not releasing read only session ${this.session.id}`);
  }

  // TODO: figure out logging user (GD) vs dialog
  private saveLog(): any {
    const sdialog: any = {};
    if (this.currentDialog) {
      const ldialog = this.currentDialog();
      if (ldialog) {
        sdialog.topic = ldialog.topic;
        // TODO(juniorfoo) - should SubDialog have message
        // sdialog.message = ldialog.message;
        sdialog.date = ldialog.date;
        sdialog.prompt = ldialog.prompt;
        sdialog.nreplies = ldialog.replies.length;
      }
    }

    return {
      profile: this.user.profile,
      name: this.user.name,
      email: this.user.email,
      start: this.user.start,
      last: this.user.last,
      topic: this.topic,
      command: this.command,
      last_dialog: sdialog,
    };
  }

  // check if we have actions of a certain type
  static checkActions(actions, action_type: ActionType | ActionType[]): boolean {
    if (!Array.isArray(action_type)) action_type = [action_type];
    return !!actions.find(a => action_type.includes(a.type));
  }

  // get an array of items of NONE type
  static emptyItems(num: number): IEntity[] {
    const items = [];
    for (let i = 0; i < num; i++) items.push({ type: EntityType.None });
    return items;
  }

  static getActionValues(actions: Action[], action_type: ActionType) {
    const values = [];
    for (const item in actions) if (actions[item].type === action_type) values.push(actions[item].value);
    return values;
  }

  static getMaskedTopics(): Topic[] {
    return masked_topics;
  }

  static getPlugins(): { [key: string]: Plugin } {
    return plugins;
  }

  static getTopicPlugin(topic: Topic) {
    return topic_plugins[topic];
  }

  // loads a session from cache or database or creates a new one
  static async loadSession(path: string, session: DialogSession, cookies = {}, options: SessionCache.SessionOptions = {
    offset: undefined, timeZone: undefined, locale: undefined, create_new:false, read_only:false, check_tokens:true, stack:Error().stack}): Promise<Dialog> {
    let dialog = await SessionCache.loadSession(path, session, cookies, options);

    // make sure user is still authenticated
    if (dialog && options.check_tokens && dialog.user.isAuthenticatedNonGuest() && !AuthProvider.isValid(dialog.user.tokens)) {
      await SessionCache.logoutSessions(dialog.user);
      if (!options.read_only) await dialog.deleteSession(false);
    }

    if (dialog) {
      if (!dialog.group_host || (dialog.group_host && dialog.group_host.host !== dialog.session.hostname) ||
          !dialog.loaded || new Date(dialog.loaded).getTime() > MINUTES(new Date(), -5).getTime()) {
        dialog.group_host = await data.groups.byHost(dialog.session.hostname);
      }
    }

    if(dialog.user) {
      if(options.offset !== undefined && !isNaN(options.offset)) dialog.user.offset = options.offset; 
      if(options.timeZone !== undefined) dialog.user.setTimeZone(options.timeZone);
      if(options.locale !== undefined) dialog.user.setLocale(options.locale);

      if(!dialog.user.affiliate) {
        const guser = await data.users.globalById(dialog.user.profile);
        if(guser) dialog.user.affiliate = guser.affiliate;
      }
    }

    return dialog;
  }

  async chatAbout(message: string) {
    const history = await DataCache.getCache(this.cache.cacheKey(), `about_history`);

      const response = await askAbout(message, history ? history.value : undefined);
      if (response) {
        if (response.history) await this.cache.setCache(this.cache.cacheKey(), `about_history`, response.history);
        return response.answer;
      }
   
  }

  // register a global topic, optionally masking it from interruption
  static registerPlugin(reg_topics: Topic | Topic[], plugin: Plugin, mask = false) {
    if (!(reg_topics instanceof Array)) reg_topics = [reg_topics];

    let first = false;
    for (const topic of reg_topics) {
      // if (topics[topic]) throw new Error(`Topic ${topic} already defined`);
      // topics[topic] = topic;
      if (!topic) {
        logging.errorF(LOG_NAME, 'registerPlugin', `Invalid topic ${topic} from plugin ${plugin.name}`, null);
        throw new Error(`Invalid topic ${topic} from plugin ${plugin.name}`);
      }

      topic_plugins[topic] = plugin;
      if (!first) plugins[topic] = plugin;
      first = true;
      if (mask) masked_topics.push(topic);
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'registerPlugin', `Registered topic ${topic}`);
    }

    if (plugin.reset_context) reset_context = reset_context.concat(plugin.reset_context);
  }

  notificationItems(): {events: Event[], tasks: Task[], messages: Message[]} {
  // get what's next for this user profile
    let events = this.cache.events.slice();
    let tasks = sortTasks(Object.values(this.cache.tasks));
    let messages = this.cache.messages.slice();

    if (!this.isAuthenticated(AuthLevel.Email)) messages = [];

    if (this.user.settings.notifications) {
      for (const notify_setting of this.user.settings.notifications) {
        switch (notify_setting.type) {
          case NotificationType.Reminder:
            if (!notify_setting.chat) tasks = [];
            break;
          case NotificationType.Meeting:
            if (!notify_setting.chat) events = [];
            break;
          case NotificationType.Event:
            if (!notify_setting.chat) {
              // filter to events with > 1 attendee
              const nevents = [];
              for (const event of events) {
                if (event.people.length > 1) nevents.push(event);
              }
              events = nevents;
            }
            break;
          case NotificationType.Draft:
            if (!notify_setting.chat) {
              // filter to non-drafts
              const nmessages: Message[] = [];
              for (const message of messages) {
                if (!message.draft) nmessages.push(message);
              }
              messages = nmessages;
            }
            break;
          case NotificationType.Followup:
            break;
          case NotificationType.Message:
            if (!notify_setting.chat) messages = [];
            break;
          case NotificationType.Import:
            break;
        }
      }
    }

    switch (this.user.settings.push) {
      case PushSetting.AskQuiet:
      case PushSetting.Quiet:
      case PushSetting.PushQuiet:
        events = [];
        tasks = [];
        messages = [];
        break;
      default:
        break;
    }

    tasks = sortTasks(tasks.filter(t => (t.due && !isNaN(new Date(t.due).getTime()) && new Date(t.due).getTime() > 0 ) &&
      (!t.completed || isNaN(new Date(t.completed).getTime()) || new Date(t.completed).getTime() <= 0)));

    return { events, tasks, messages };
  }

  activitySummary(
      next_types: (EntityType | GlobalType)[],
      events: Event[] = [],
      tasks: Task[] = [],
      messages: Message[] = [],
      event_time = null,
      active_time = null,
      full_day = false,
      scope: 'day'|'week'|'month'|'year' = 'day',
    ): ActivitySummary {
      // default to now if event time is null
      if (event_time === null) event_time = new Date();
      if (active_time === null) active_time = event_time;

      let event_count = 0;
      let total_time = 0;

      let next = [];

      // people related to the day
      const people_ids = [];
      const event_people_ids = [];

      if (logging.isDebug(this.user.profile)) logging.debugFP(LOG_NAME, 'activitySummary', this.user.profile, `Looking at ${JSON.stringify(next_types)} ${events ? events.length : 0} events ${tasks ? tasks.length : 0} tasks ${ messages ? messages.length : 0} messages event time: ${event_time.toISOString()} active time: ${active_time.toISOString()} ${full_day ? 'full day': ''}`);

      // add events, tasks, messages to the list
      for (const item in events) {
        const event = events[item];
        const start = new Date(event.start);
        const end = new Date(event.end);

        // look for events the same day as the query time
        let range_filter = false;
        switch(scope) {
          case 'day':
            range_filter = (sameDay(event_time, start, this.user.offset) || sameDay(event_time, end, this.user.offset));
            break;
          case 'week':
            range_filter = (sameWeek(event_time, start, this.user.offset) || sameWeek(event_time, end, this.user.offset));
            break;
          case 'month':
            range_filter = (sameMonth(event_time, start, this.user.offset) || sameMonth(event_time, end, this.user.offset));
            break;
          case 'year':
            range_filter = (sameYear(event_time, start, this.user.offset) || sameYear(event_time, end, this.user.offset));
            break;
        }

        if (this.events.calendars.selected(event.calendarId) && range_filter) {
          // include all events unless we're reporting on the full day and it's an all day event
          event.all_day = allDay(event, event_time);
          if (!(full_day && event.all_day)) {
            event.priority = priorityEvent(event, active_time);
            event.active = activeEvent(event, active_time);
            event.upcoming = upcomingEvent(event, active_time);
            event.finished = finishedEvent(event, active_time);
            next.push(event);
            if (!event.all_day) {
              total_time += end.getTime() - start.getTime(); // accumulate time in ms
              event_count++;
            }

            if (full_day && event.people) {
              for (const operson of event.people) {
                if (!operson.self && operson.id !== this.me.id && !people_ids.includes(operson.id) && 
                  (!operson.names || !operson.names.includes('Holiday'))) people_ids.push(operson.id);
                event_people_ids.push(operson.id);
              }
            }
          }
        }
      }

    // check for tasks overdue or due today
    if (tasks) {
      if (full_day) {
        const day_tasks = tasks.filter(t => new Date(t.due).getTime() <= 0 || sameDay(event_time, new Date(t.due), this.user.offset));
        for (const task of tasks.filter(t => t.people)) {
          for (const operson of task.people) {
            if (!operson.self && operson.id !== this.me.id && !people_ids.includes(operson.id)) people_ids.push(operson.id);
          }
        }
        next = next.concat(day_tasks);
      } else next = next.concat(tasks);
    }

    // check for old unread email
    if (messages) {
      for (const message of _.reverse(messages.filter(m => m.sender && !(m.sender.self || m.read) || m.draft))) {
        message.priority = this.priorityMessage(message, events);
        next.push(message);

        if (full_day) {
          if (!message.sender.self && message.sender.id !== this.me.id && !people_ids.includes(message.sender.id)) {
            people_ids.push(message.sender.id);
          }

          if (message.recipient) {
            for (const operson of message.recipient) {
              if (!operson.self && operson.id !== this.me.id && !people_ids.includes(operson.id)) {
                people_ids.push(operson.id);
              }
            }
          }
        }
      }
    }

    // sort next list by priority
    // order is:
    //  active events by start,
    //  same day following events by start,
    //  tasks due today,
    //  other tasks,
    //  messages,
    //  preceding events by start
    // return -1 for earlier, 1 for later

    function sortEventTask(e, t, o) {
      if (e.priority && !e.finished && !e.all_day) return -1;

      const t_due = new Date(t.due);
      if (t_due.getTime() > 0 && sameDay(active_time, t_due, o)) return 1;
      if (e.priority && e.finished && MINUTES(new Date(e.start), 30) >= active_time) return -1;
      return 1;
    }

    const m30 = MINUTES(event_time, -30);
    const p30 = MINUTES(event_time, 30);

    next.sort((e0, e1) => {
      if (e0.type === EntityType.Event) {
        const e0_start = new Date(e0.start);
        if (e1.type === EntityType.Event) {
          // sorting two events
          const e1_start = new Date(e1.start);

          // all day is last
          if (e1.all_day) return -1;
          if (e0.all_day) return 1;

          // active first, sort by attendee count largest first
          if (e0.active) {
            if (e1.active) {
              if (e0.priority) {
                if (!e1.priority) return -1;
              } else if (e1.priority) return 1;
              if (e0_start === e1_start && e0.people && e1.people) return e0.people.length >= e1.people.length ? -1 : 1;
              return e0_start <= e1_start ? -1 : 1;
            }
            return -1;
          }

          // then starting, sort by attendee count largest first
          if (e0.upcoming) {
            if (e1.active) return 1; // active first
            if (e1.upcoming) {
              if (e0.priority) {
                if (!e1.priority) return -1;
              } else if (e1.priority) return 1;
              if (e0_start === e1_start && e0.people && e1.people) return e0.people.length >= e1.people.length ? -1 : 1;
              return e0_start < e1_start ? -1 : 1;
            }
            return -1;
          }

          // just finished, most recent first
          if (e0.finished) {
            if (e1.active || e1.upcoming) return 1; // active and upcoming first
            if (e1.finished) {
              if (e0.priority) {
                if (!e1.priority) return -1;
              } else if (e1.priority) return 1;
              if (e0_start === e1_start && e0.people && e1.people) return e0.people.length >= e1.people.length ? -1 : 1;
              return e0_start >= e1_start ? -1 : 1; // recent first
            }
            return -1;
          }
        }

        // tasks due today come after events that are active or within an hour
        // other tasks come after events finished today
        // events not today or more than an hour away come after tasks
        if (e1.type === EntityType.Task) return sortEventTask(e0, e1, this.user.offset);

        if (e1.type === EntityType.Message) {
          if (e0.priority && !e0.finished) return -1;
          if (e1.priority) return 1;
          return -1;
        }

        return -1;
      }

      // order tasks by - due +/- 30 min, overdue, coming up, no due date
      if (e0.type === EntityType.Task) {
        if (e1.type === EntityType.Event) return -1 * sortEventTask(e1, e0, this.user.offset);

        if (e1.type === EntityType.Task) {
          const e0_due = new Date(e0.due);
          const e1_due = new Date(e1.due);

          if (e0_due.getTime() <= 0) return 1;
          if (e0_due > m30 && e0_due < p30) {
            if (e1_due > m30 && e1_due < p30) return e0_due.getTime() - e1_due.getTime();
            return -1;
          }
          if (e1_due > m30 && e1_due < p30) return 1;
          if (e0_due < event_time) {
            if (e1_due < event_time) return e0_due.getTime() - e1_due.getTime();
            return -1;
          }
          if (e1_due < event_time) return 1;
          return e0_due.getTime() - e1_due.getTime();
        }

        if (e1.type === EntityType.Message && e1.priority) return 1;

        return -1;
      }

      if (e0.type === EntityType.Message) {
        if (e1.type === EntityType.Event) {
          if (e1.priority && !e1.finished) return 1;
          if (e0.priority) return -1;
          return 1;
        }

        if (e1.type === EntityType.Task && e0.priority) return -1;

        if (e1.type === EntityType.Message) {
          if (e0.priority) {
            if (e1.priority) {
              if (e0.draft && !e1.draft) return -1;
              if (!e0.draft && e1.draft) return 1;
              return new Date(e0.received) >= new Date(e1.received) ? -1 : 1;
            }
            return -1;
          }
          if (e1.priority) return 1;
          return new Date(e0.received) <= new Date(e1.received) ? -1 : 1;
        }

        return -1;
      }
    });

    let quip = null;
    if (next.length) {
      // if (full_day && future(event_time, offset, true)) {
      if (event_count < 4 || total_time < 4 * 3600 * 1000) quip = randomPhrase(lang.whats_next.NEXT_WITTY_EMPTY);
      else if (event_count < 8 || total_time < 8 * 3600 * 1000) quip = randomPhrase(lang.whats_next.NEXT_WITTY_MEDIUM);
      else quip = randomPhrase(lang.whats_next.NEXT_WITTY_FULL);
      // }
    /*} else if(this.filters && this.filters.length){
      quip = lang.defaultanswers.ACKNOWLEDGE();*/
    } else {
      // if there are no events, say something witty on an empty day or if it's not a recent ping
      if (full_day && future(event_time, this.user.offset, true)) quip = randomPhrase(lang.whats_next.NEXT_WITTY_EMPTY);
      else quip = randomPhrase(lang.whats_next.NEXT_WITTY);
    }

    return {
      next,
      next_types,
      event_time,
      event_count, // total events
      total_time, // total time in ms
      quip,
      full_day,
      event_people_ids,
      people_ids,
    } as ActivitySummary;
  }


  // priority messages have people with events
  priorityMessage(message, events) {
    if (message.draft) return true;

    const people_ids = [];
    if (!message.sender.self) people_ids.push(message.sender.id);
    if (message.recipient) {
      for (const index in message.recipient) {
        const person = message.recipient[index];
        if (!person.self) people_ids.concat(person.id);
      }
    }

    for (const index in events) {
      const event = events[index];
      if (event.people) {
        for (const pindex in event.people) if (people_ids.indexOf(event.people[pindex].id) !== -1) return true;
      }
    }

    return false;
  }

  formatNext(next: IEntity, now: Date, full_day: boolean, offset: number, locale: string, timeZone: string): {prompt: BasicInfo|BasicInfo[]; info: any; answers?: string[]} {
    let format: {prompt: BasicInfo|BasicInfo[]; info: any; answers?: string[]} = null;

    switch (next.type) {
      case EntityType.Event: {
          const event = next as Event;
          // check if event just finished
          if (finishedEvent(event, now)) {
            format = {
              prompt: this.LAST_EVENT(event, full_day, offset, locale, timeZone),
              info: eventInfo(event, full_day, offset, locale, timeZone),
              answers: full_day ? null : lang.whats_next.FINISHED_ANSWERS,
            }
          }

          // check if an event is underway
          if (activeEvent(event, now)) {
            // check if an event is all day (starts or ends not today)
            format = {
              prompt: this.ACTIVE_EVENT(event, full_day, offset, locale, timeZone),
              info: eventInfo(event, full_day, offset, locale, timeZone),
              answers: full_day ? null : lang.whats_next.ATTENDEE_ANSWERS(event),
            }
          }

          // check if an event is yet to start
          if (upcomingEvent(event, now)) {
            format = {
              prompt: this.NEXT_EVENT(event, full_day, offset, locale, timeZone),
              info: eventInfo(event, full_day, offset, locale, timeZone),
              answers: full_day ? null : lang.whats_next.ATTENDEE_ANSWERS(event),
            }
          }
        }
        break;
      case EntityType.Task:
        format = {
          prompt: this.NEXT_TASK(next, full_day, offset, locale, timeZone),
          info: taskInfo(next as Task, full_day, offset, locale, timeZone),
          answers: lang.whats_next.TASK_ANSWERS(next),
        }
        break;
      case EntityType.Message:
        format = {
          prompt: this.NEXT_MESSAGE(next, full_day, offset, locale, timeZone),
          info: messageInfo(next as Message, full_day, offset, locale, timeZone),
          answers: lang.whats_next.MESSAGE_ANSWERS(next),
        }
        break;
      case EntityType.Person:
        format = {
          prompt: promptInfo(lang.person_info.FOUND),
          info: this.getPersonInfo(next as Person),
        }
        break;
    }
    return format;
  }

  NEXT_EVENT(event, full_day, offset, locale, tz, _active = false) {
    const time = formatFullTime(event.start, offset, locale, tz);
    return lang.whats_next.NEXT_EVENT(time, event, event.people ? event.people.filter(p => !p.self).map(p => this.getPersonInfo(new Person(p))) : []);
  }

  ACTIVE_EVENT(event, full_day, offset, locale, tz) {
    const time = localeDayShort(event.end, locale, tz);
    const prompt = [lang.whats_next.ACTIVE_EVENT(time, event, event.people ? event.people.filter(p => !p.self).map(p => this.getPersonInfo(new Person(p))) : [])];
    if (!full_day) prompt.push(lang.whats_next.NOTES);
    return prompt;
  }

  LAST_EVENT(event, full_day, offset, locale, tz) {
    if (full_day) {
      const time = formatFullTime(event.end, offset, locale, tz);
      return lang.whats_next.LAST_EVENT(time, event);
    }
    // const time = localeDayShort(event.end, locale. tz);
    return lang.whats_next.NO_EVENT(event);
  }

  NEXT_TASK(task, full_day, offset, locale, tz) {
    const now = new Date();
    const due = new Date(task.due);

    let time = null;
    if (due.getTime()) {
      if (due < DAYS(now, -7)) time = localeDowMonthDay(due, locale, tz);
      else if (due < now) time = localeDate(due, locale, tz);
      else time = localeDate(due, locale, tz);
    }

    return lang.whats_next.NEXT_TASK(task.id, task.title, time, due >= now);
  }

  NEXT_MESSAGE(message, full_day, offset, locale, tz) {
    let msg = null;
    let people = [];

    if (message.sender.self) {
      // const rcpt = null;
      if (message.draft || !message.recipient || message.recipient.length === 0) {
        let draft_info = '';
        if (message.subject && message.subject.length) draft_info = 'about';
        else if(message.recipient && message.recipient.length) draft_info = 'with';
        msg = lang.whats_next.STARTED_DRAFT(draft_info);
      } else {
        people = message.recipient;
        msg = lang.whats_next.LAST_EMAIL(localeDowMonthDay(message.received, locale, tz));
      }
    } else {
      people = [message.sender];
      msg = lang.whats_next.FOLLOW_UP();
    }

    return promptInfo(msg, lang.whats_next.MAIL_LINK(message.id), message.subject, people.map(p => this.getPersonInfo(new Person(p))));
  }

  ALLDAY_EVENT(event, full_day, _offset, _locale, _tz) {
    if (full_day) return lang.whats_next.ALL_DAY(event);
    return lang.whats_next.ALSO_DAY(event);
  }
}

export { Dialog };

