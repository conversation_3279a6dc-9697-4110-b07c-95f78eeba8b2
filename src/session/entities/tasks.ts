import { v4 as uuid } from 'uuid';
import data from '../../data';
import * as itemTypes from '../../types/items';
import logging from '../../utils/logging';
import DataCache from '../data_cache';

type Task = itemTypes.Task;
const LOG_NAME = 'data.entities.Tasks';

export default class Tasks {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async complete(task: Task) {
    const rtask = await data.tasks.complete(this.cache.user, new itemTypes.Task(task));
    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'complete', this.cache.user.profile, `removed ${task.id} remaining ${this.cache.tasks}`);
    this.cache.tasks[rtask.id] = rtask;
    this.cache.cacheDirty('tasks');
    return rtask;
  }

  async uncomplete(task: Task) {
    const rtask = await data.tasks.uncomplete(this.cache.user, new itemTypes.Task(task));
    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'complete', this.cache.user.profile, `removed ${task.id} remaining ${this.cache.tasks}`);
    this.cache.tasks[rtask.id] = rtask;
    this.cache.cacheDirty('tasks');
    return rtask;
  }

  async create(task: Task): Promise<Task> {
    let new_task: Task = task;
    task.created = new Date();
    if (!task.id) task.id = uuid();
    new_task = await data.tasks.create(this.cache.user, task);
    const people_ids = [];

    if (new_task) {
      /*if (new_task.people) {
        for (const person of new_task.people) {
          if (person.id) {
            people_ids.push(person.id);
            peopleUtils.savePeopleMatch(this.cache.people_tasks, person.id, new_task.id);
            if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'create', this.cache.user.profile, `Caching ${person.id} on task ${new_task.id}`);
            this.cache.cacheDirty('people_tasks');
          } else {
            logging.warnFP(LOG_NAME, 'create', this.cache.user.profile, `Not caching person with no id on task ${new_task.id}`);
          }
        }
      }*/

      this.cache.tasks[new_task.id] = new_task; // .push(new_task);
      // funcs.sortTasks(this.cache.tasks);
      this.cache.cacheDirty('tasks');

      if (people_ids.length) await this.cache.loadPeopleIds(people_ids);
    }
    return new_task;
  }

  async delete(task: Partial<Task>) {
    await data.tasks.delete(this.cache.user, new itemTypes.Task(task));
    delete this.cache.tasks[task.id]; // = this.cache.tasks.filter(t => t.id !== task.id);
    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'complete', this.cache.user.profile, `removed ${task.id} remaining ${this.cache.tasks}`);
    // funcs.sortTasks(this.cache.tasks);
    this.cache.cacheDirty('tasks');

    /*if (task.people) {
      for (const index in task.people) {
        const person = task.people[index];
        const person_tasks = this.cache.people_tasks[person.id];
        if (person_tasks) {
          this.cache.cacheDirty('people_tasks');
          const ptask_index = person_tasks.indexOf(task.id);
          if (ptask_index !== -1) person_tasks.splice(ptask_index, 1);
        } else {
          logging.warnFP(LOG_NAME, 'complete', this.cache.user.profile, `Missing person ${person.id} from people_tasks for ${task.id}`);
        }
      }
    }*/
  }

  async reload(task: Task): Promise<Task> {
    let rtask = null;
    if (!task.taskListId) return task;

    try { rtask = await data.tasks.reload(this.cache.user, task); } 
    catch (e) { logging.errorFP(LOG_NAME, 'reload', this.cache.user.profile, `Error reloading task ${task.id} from source`, e); }

    if (!rtask) {
      delete this.cache.tasks[task.id];
      if (!this.cache.read_only) this.cache.cacheDirty('tasks');

      /*if (task.people) {
        for (const person of task.people) {
          if (this.cache.people_tasks[person.id]) {
            this.cache.people_tasks[person.id] = this.cache.people_tasks[person.id].filter(id => id !== task.id);
            if (!this.cache.read_only) this.cache.cacheDirty('people_tasks');
          }
        }
      }*/
    }

    return rtask;
  }

  async update(task: Task): Promise<Task> {
    let rtask;
    try { rtask = await data.tasks.update(this.cache.user, task); } 
    catch (e) { logging.errorFP(LOG_NAME, 'update', this.cache.user.profile, 'Error updating task to datastore', e); }
    for (const index in this.cache.tasks) {
      if (this.cache.tasks[index].id === task.id) {
        this.cache.tasks[index] = task;
        this.cache.cacheDirty('tasks');
        break;
      }
    }

    /*if (task.people) {
      for (const index in task.people) {
        const person = task.people[index];
        if (person.id) {
          peopleUtils.savePeopleMatch(this.cache.people_tasks, person.id, task.id);
          this.cache.cacheDirty('people_tasks');
        }
      }

      for (const index in this.cache.people_tasks) {
        const pt = this.cache.people_tasks[index];
        for (const index in pt) {
          if (pt[index] === task.id) {
            pt.slice(index as any, 1);
            this.cache.cacheDirty('people_tasks');
            break;
          }
        }
      }
    }*/

    return rtask;
  }
}
