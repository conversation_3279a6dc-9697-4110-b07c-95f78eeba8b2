import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import data from '../../data';

import { answerPracticum, askTutor, startPracticum } from '../../sources/vertex_controller';

import { TemplateType } from '../../types/globals';
import { Tutorial } from '../../types/items';
import { NotificationType, Uid } from '../../types/shared';

import { mapURL } from '../../utils/funcs';
import logging from '../../utils/logging';
import notifyUser, { NotifyType } from '../../utils/notify';

import DataCache from '../data_cache';
import ForaUser from '../user';

const LOG_NAME = 'session.entities.Tutorial';

export default class Tutor {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async chat(id: Uid, message: string): Promise<string> {
    const tutorial = await this.get(id);
    if(tutorial) {
      const history = await DataCache.getCache(this.cache.cacheKey(), `tutorial_${tutorial.id}_history`);

      const response = await askTutor(message, tutorial, history ? history.value : undefined);
      if (response) {
        if (response.history) await this.cache.setCache(this.cache.cacheKey(), `tutorial_${tutorial.id}_history`, response.history);
        return response.answer;
      }
    }
    return undefined;
  }

  async practice(id: Uid, message?: string): Promise<string> {
    const tutorial = await this.get(id);
    if(tutorial) {
      if(tutorial.status?.practiced) return 'You have already completed this tutorial.';
      const history = await DataCache.getCache(this.cache.cacheKey(), `tutorial_${tutorial.id}_practice_history`);
      let response;
      if(message?.length) {
        response = await answerPracticum(message, tutorial, history ? history.value : undefined);
      } else response = await startPracticum(tutorial, history ? history.value : undefined);

      if (response) {
        if (response.history) {
          await this.cache.setCache(this.cache.cacheKey(), `tutorial_${tutorial.id}_practice_history`, response.history);
          if(!tutorial.practice) {
            tutorial.practice = {
              hash: uuid(),
              value: response.history,
              count: response.history.length,
            }
          } else {
            tutorial.practice.value = response.history;
            tutorial.practice.count = response.history.length;
          }
          await data.tutorials.save(this.cache.user, tutorial);
        }
        return response.answer;
      }
    }
    return undefined;
  }

  async assess(tutorial_id: Uid, lesson: number, value: { role: string; parts: { text: string; }[] }[]): Promise<Tutorial> {
    const tutorial = await this.get(tutorial_id);
    if(!tutorial?.lesson_set || lesson >= tutorial.lesson_set.length) return;

    if(!tutorial.assessments) tutorial.assessments = [];

    while(tutorial.assessments.length < lesson + 1) tutorial.assessments.push({} as any);
    tutorial.assessments[lesson] = {
      hash: uuid(),
      value,
      count: value.length,
    }

    return data.tutorials.save(this.cache.user, tutorial);
  }

  async create(tutorial: Tutorial): Promise<Tutorial> {
    return data.tutorials.save(this.cache.user, tutorial);
  }

  async get(id: Uid): Promise<Tutorial> {
    let tutorial;
    if(this.cache.user.isAuthenticatedNonGuest()) {
      tutorial = await data.tutorials.get(this.cache.user, id);
    }
    if(!tutorial) {
      const templates = new ForaUser('tutorial');
      tutorial = await data.tutorials.get(templates, id);
    }
    return tutorial;
  }

  async load(all?: boolean, host?: string, host_only?: boolean): Promise<Tutorial[]> {
    let all_tutorials: Tutorial[] = [];
    let my_tutorials: Tutorial[] = [];
    const guest = this.cache.user.isGuestAccount();
    if(all || guest) {
      const guest_user = new ForaUser('tutorial');
      all_tutorials = await data.tutorials.load(guest_user);
    }
    
    if(!guest) {
      my_tutorials = await data.tutorials.load(this.cache.user);
    }

    const tutorials: Tutorial[] = my_tutorials.slice();
    const has_tutorials = my_tutorials.map(t => t.id);

    let catalog_set: Tutorial[] = [];
    if(guest) {
      logging.infoFP(LOG_NAME, 'load', this.cache.user.profile, `Loading all public tutorials for guest ${host ? host : ''}`);
      catalog_set = all_tutorials.filter(t => t.public && (
        (!host && !t.hosts?.length) || 
        (host && (t.hosts?.includes(host) || (!t.hosts?.length && !host_only)))
      ));
    }
    else if(host) {
      logging.infoFP(LOG_NAME, 'load', this.cache.user.profile, `Loading public tutorials for ${host ? host : ''} ${host_only ? 'host_only' : ''}`);
      catalog_set = all_tutorials.filter(t => t.hosts?.includes(host) || (!t.hosts?.length && !host_only));
    }
    else {
      logging.infoFP(LOG_NAME, 'load', this.cache.user.profile, `Loading non-private tutorials`);
      catalog_set = all_tutorials.filter(t => !t.private);
    }

    for(const t of catalog_set) {
      if(!has_tutorials.includes(t.id)) tutorials.push(t);
    }

    return tutorials;
  }

  async save(tutorial: Tutorial): Promise<Tutorial> {
    if(this.cache.user.isAuthenticatedNonGuest()) {
      return data.tutorials.save(this.cache.user, tutorial);
    }
  }

  async complete(tutorial_id) {
    if(tutorial_id && this.cache.user.isAuthenticatedNonGuest()) {
      const saved = await this.get(tutorial_id);
      if(saved && saved.practice?.value && saved.status && !saved.status.practiced) {
        saved.status.practiced = new Date();

        const values = saved.practice.value.filter(v => v.parts?.length && v.parts[0].text);
        if(values?.length && values[0].role === 'user') values.splice(0,1);

        const variables:any  = { 
          url: mapURL(`/p`, this.cache.getGroup()),
          name: this.cache.user.name, 
          values: values.length,
        }

        values.forEach((value, i) => {
          variables[`value_${i}`] = value.parts[0].text;
        });

        const notify = {
          email: {
            rcpts: [{Email: this.cache.user.email, Name: this.cache.user.name}],
          },
          type: NotificationType.Tutorial,
          template: TemplateType.Completed,
          variables,
        }

        await notifyUser(this.cache.user, notify, NotifyType.EmailOnly);

        await data.tutorials.saveCertificate(this.cache.user, saved);

        return data.tutorials.save(this.cache.user, saved);
      }
    }
  }

  async update(tutorial: Partial<Tutorial>): Promise<Tutorial> {
    if(tutorial.id && this.cache.user.isAuthenticatedNonGuest()) {
      let saved = await this.get(tutorial.id);
      if(!saved) saved = new Tutorial({id: tutorial.id, status:{}});

      saved.status = {
        lesson: tutorial.status.lesson !== undefined ? tutorial.status.lesson : saved.status?.lesson,
        preassessed: tutorial.status.preassessed !== undefined ? tutorial.status.preassessed : saved.status?.preassessed,
        assessed: tutorial.status.assessed !== undefined ? tutorial.status.assessed : saved.status?.assessed,
        practiced: tutorial.status.practiced !== undefined ? tutorial.status.practiced : saved.status?.practiced,
        unlocked: _.uniq([...tutorial.status.unlocked !== undefined ? tutorial.status.unlocked : [], ...saved.status && saved.status.unlocked !== undefined ? saved.status.unlocked : []]),
      }
      return data.tutorials.save(this.cache.user, saved);
    }
 
  }

}