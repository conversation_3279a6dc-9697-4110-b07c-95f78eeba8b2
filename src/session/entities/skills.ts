import _ from 'lodash';

import DataCache from '../data_cache';

import { Category, ExtendedCategory, SkillCategory } from '../../types/globals';
import { Group } from '../../types/group';
import { Person } from '../../types/items';
import { EducationLevel, Uid } from '../../types/shared';
import { combineWords, flatten } from '../../utils/funcs';
import parsers from '../../utils/parsers';

import data from '../../data';

import { filterRelated, generateCategories, isCategorySkill, peopleCategories, peopleCategoryMap, skillGroup } from '../../skills';

const LOG_NAME = 'entities.skills';

export default class Goals {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  isSkill(skill: string) {
    const groups = this.cache.user.categoryGroups();
    let global_cats = !groups || !groups.length;
    if (groups) {
      for(const group of groups) {
        const is_skill = isCategorySkill(group.id, skill);
        if (is_skill) return true;

        global_cats = global_cats || group.global_categories;
      }
    }

    if (global_cats) return isCategorySkill(undefined, skill);
    return false
  }

  async categories(cat_skills: string[], add_skills: string[] = [], count = 3, skip_cats: Uid[] = [], expand = true, inv = false): Promise<ExtendedCategory[]> {
    if (!cat_skills) return [];

    if (expand) {
      const single_skills = flatten(add_skills.map(s => s.toLowerCase().split(' ')))
        .filter(s => !add_skills.includes(s) && !parsers.ignore(s) && !parsers.name(s) && !parsers.mask(s) && this.isSkill(s));
      const cw = combineWords([...add_skills.filter(s => s.split(' ').length === 1), ...single_skills]);
      const joined_words = cw;
      const skills = _.uniq([...add_skills, ...joined_words].map(s => s.toLowerCase()));

      add_skills = _.uniqBy([...add_skills, ...await filterRelated(skills, this.cache.me.skillsBatch, 0.99)], x => x.toLowerCase())
    }

    const lower_names = this.cache.me && this.cache.me.names ? this.cache.me.names.map(n => n.toLowerCase()) : this.cache.user.name.toLowerCase().split(' ');
    const jobs = this.cache.me.jobTags.map(j => j.value.toLowerCase());
    const ignore = _.uniq([...lower_names, ...jobs, ...EducationLevel]);

    const groups = this.cache.user.categoryGroups();
    const group_ids: Uid[] = groups ? groups.map(g => g.id) : undefined;
    let global_cats = !groups || !groups.length || groups.reduce((a,b) => a || b.global_categories, false);

    return generateCategories(group_ids, global_cats, cat_skills, add_skills, ignore, count, skip_cats, expand, inv);
  }

  async group(skills: string[]):  Promise<string[]> {
    const groups = this.cache.user.categoryGroups();
    const group_ids: Uid[] = groups ? groups.map(g => g.id) : undefined;
    let global_cats = !groups || !groups.length || groups.reduce((a,b) => a || b.global_categories, false);

    return skillGroup(group_ids, global_cats, skills, this.cache.user.locale, 10, skills.length < 10);
  }

  async expand(skill: string, max = 10):  Promise<string[]> {
    const groups = this.cache.user.categoryGroups();
    const group_ids: Uid[] = groups ? groups.map(g => g.id) : undefined;
    let global_cats = !groups || !groups.length || groups.reduce((a,b) => a || b.global_categories, false);

    return skillGroup(group_ids, global_cats, [skill], this.cache.user.locale, max, true);
  }

  async peopleCategories(people: Partial<Person[]>, related?: string[]): Promise<Category[]>{
    let lower_names = this.cache.me && this.cache.me.names ? this.cache.me.names.map(n => n.toLowerCase()) : this.cache.user.name.toLowerCase().split(' ');
    let jobs = this.cache.me.jobNames.map(j => j.toLowerCase());

    if (people) {
      people.forEach(p => {
        lower_names = lower_names.concat(p.names ? p.names.map(n => n.toLowerCase()) : p.displayName.toLowerCase().split(' '));
        jobs = jobs.concat(p.jobNames.map(j => j.toLowerCase()));
      });
    }

    const ignore = _.uniq([...lower_names, ...jobs, ...EducationLevel]);

    const groups = this.cache.user.categoryGroups();
    const group_ids: Uid[] = groups ? groups.map(g => g.id) : undefined;
    let global_cats = !groups || !groups.length || groups.reduce((a,b) => a || b.global_categories, false);

    return peopleCategories(group_ids, global_cats, people, ignore, related, 0.5);
  }

  async peopleCategoryMap(people: Partial<Person>[]): Promise<{[key:string]: ExtendedCategory[]}> {
    const groups = this.cache.user.categoryGroups();
    const group_ids: Uid[] = groups ? groups.map(g => g.id) : undefined;
    let global_cats = !groups || !groups.length || groups.reduce((a,b) => a || b.global_categories, false);

    return peopleCategoryMap(this.cache.user, people, group_ids, global_cats);
  }

  async saveCategory(group: Group, sc: SkillCategory) {
    const rsc = await data.skills.saveCategory(group, sc);
    data.skills.exportSkillCategories(group);
    return rsc;
  }

  async deleteCategory(group: Group, sc: SkillCategory) {
    const rsc = await data.skills.deleteCategory(group, sc);
    data.skills.exportSkillCategories(group);
    return rsc;
  }
}