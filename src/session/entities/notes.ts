import data from '../../data';
import * as itemTypes from '../../types/items';
import DataCache from '../data_cache';

type Note = itemTypes.Note;

export default class Notes {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async create(note: Note): Promise<Note> {
    note.created = new Date();
    // if (!note.people || note.people.length === 0) note.people = [this.me];

    const new_note: Note = await data.notes.create(this.cache.user, new itemTypes.Note(note));

    if (new_note) {
      this.cache.notes[new_note.id] = new_note;
      this.cache.cacheDirty('notes');

      /*if (new_note.people) {
        for (const index in new_note.people) {
          const person = new_note.people[index];
          if (person.id) {
            peopleUtils.savePeopleMatch(this.cache.people_notes, person.id, new_note.id);
            this.cache.cacheDirty('people_notes');
          }
        }
      }*/
    }

    return new_note;
  }

  async delete(note: Note) {
    await data.notes.delete(this.cache.user, note);

    if (this.cache.notes[note.id]) {
      delete this.cache.notes[note.id];
      this.cache.cacheDirty('notes');
    }

    /*if (note.people) {
      for (const index in note.people) {
        const person = note.people[index];
        const person_notes = this.cache.people_notes[person.id];
        const pnotes_index = person_notes.indexOf(note.id);
        if (pnotes_index !== -1) {
          person_notes.splice(pnotes_index, 1);
          this.cache.cacheDirty('people_notes');
        }
      }
    }*/
  }

  async update(note: Note): Promise<Note> {
    await data.notes.save(this.cache.user, new itemTypes.Note(note));

    // const old_note = this.cache.notes[note.id];
    // TODO: unmap people not longer linked
    this.cache.notes[note.id] = note;
    this.cache.cacheDirty('notes');

    /*if (note.people) {
      for (const index in note.people) {
        const person = note.people[index];
        const person_notes = this.cache.people_notes[person.id];
        this.cache.cacheDirty('people_notes');
        const pnotes_index = person_notes.indexOf(note.id);
        if (pnotes_index !== -1) person_notes.splice(pnotes_index, 1);
      }

      for (const index in note.people) {
        const person = note.people[index];
        if (person.id) {
          peopleUtils.savePeopleMatch(this.cache.people_notes, person.id, note.id);
          this.cache.cacheDirty('people_notes');
        }
      }
    }*/

    return note;
  }
}
