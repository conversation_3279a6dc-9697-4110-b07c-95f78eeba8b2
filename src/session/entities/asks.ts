
import { Ask, dehydrate<PERSON>erson } from '../../types/items';

import { Uid } from '../../types/shared';

import data from '../../data';

import ForaUser from '..//user';
import DataCache from '../data_cache';

export default class AsksData {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async get(id: Uid): Promise<Ask> {
    return data.asks.get(this.cache.user, id);
  }

  async load(): Promise<Ask[]> {
    return data.asks.load(this.cache.user);
  }

  async update(ask: Ask): Promise<Ask> {
    ask.last_update = new Date();
    ask.candidates = ask.candidates ? ask.candidates.map(c => dehydratePerson(c)) : [];
    return data.asks.update(this.cache.user, ask);
  }

  async create(ask: Partial<Ask>): Promise<Ask> {
    ask.created = new Date();
    ask.last_update = ask.created;
    ask.candidates = ask.candidates ? ask.candidates.map(c => dehydrate<PERSON>erson(c)) : [];
    return data.asks.create(this.cache.user, new Ask(ask));
  }

  async delete(ask: Partial<Ask>) {
    return data.asks.delete(this.cache.user, new Ask(ask));
  }

  async share(ask: Partial<Ask>, is_public?: boolean, groups?: Uid[]) {
    return data.asks.share(this.cache.user, ask, is_public, groups);
  }

  async getShared(id: Uid): Promise<Ask> {
    const shared = await data.asks.getShared(this.cache.user, id);
    if (shared) {
      const user = new ForaUser(shared.profile);
      const ask = data.asks.get(user, shared.id);
      return ask;
    }

    return null;
  }
}