import { Analysis, dehydratePerson } from '../../types/items';
import { Uid } from '../../types/shared';


import data from '../../data';

import DataCache from '../data_cache';

export default class AnalysisData {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async get(id: Uid): Promise<Analysis> {
    return data.analyses.get(this.cache.user, id);
  }

  async load(): Promise<Analysis[]> {
    return data.analyses.load(this.cache.user);
  }

  async update(analysis: Analysis): Promise<Analysis> {
    analysis.candidates = analysis.candidates.map(c => dehydratePerson(c));
    return data.analyses.update(this.cache.user, analysis);
  }

  async create(analysis: Partial<Analysis>): Promise<Analysis> {
    analysis.candidates = analysis.candidates.map(c => dehydratePerson(c));
    return data.analyses.create(this.cache.user, new Analysis(analysis));
  }

  async delete(analysis: Partial<Analysis>) {
    return data.analyses.delete(this.cache.user, new Analysis(analysis));
  }
}