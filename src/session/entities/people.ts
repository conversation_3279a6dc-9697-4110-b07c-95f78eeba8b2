import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import config from '../../config';
import data from '../../data';
import { doExport } from '../../routes/update';

import { SourceController } from '../../sources/source_controller';
import DataCache from '../data_cache';



import { IMAGES, InternalError, Introduction, TemplateType } from '../../types/globals';
import { Document, GlobalType, Person, Project, Tag } from '../../types/items';
import { EntityType, NotificationType, ProfileStatus, SkillStat, Stats, TagType, Uid } from '../../types/shared';
import { FORA_PROFILE } from '../../types/user';


import { filterPeople, filterWeightedPeople } from '../../utils/filter';
import { STRIP_REGEX, flatten, mapURL, weightMax, wordSet } from '../../utils/funcs';
import logging from '../../utils/logging';
import { mailTemplate } from '../../utils/mail';
import parsers from '../../utils/parsers';
import peopleUtils from '../../utils/people';

const DEBUG = (require('debug') as any)('fora:data:entities.People');
const LOG_NAME = 'data.entities.people';

interface SearchOptions { 
  network: 'exclude' | 'include' | 'only'; // = 'exclude'; 
  require_email?: boolean; 
  mandatory_tags?: Tag[][]; 
  group_search_ids?: Uid[]; 
  additional_user_ids?: Uid[]; 
  only_users?: boolean
}

export default class People {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async addPhoto(person: Person, file) {
    const ext_in = file.originalname ? file.originalname.lastIndexOf('.') : -1;
    const ext = ext_in !== -1 ? file.originalname.slice(ext_in + 1) : null;

    const mime = ext && IMAGES.includes(ext) ? `image/${ext}` : 'image/png';

    const photo_doc = new Document({
      body: file.buffer,
      mime: file.mimetype && file.mimetype.startsWith('image') ? file.mimetype : mime,
      created: new Date(),
      props: {
        originalname: file.originalname,
        encoding: file.encoding,
        size: file.size ? file.size : file.buffer.length,
      },
    });

    const saved_photo = await data.people.savePhoto(this.cache.user, photo_doc);
    const url = `askfora://${this.cache.user.profile}/${saved_photo.id}`;
    if (!person.photos) person.photos = [url];
    else person.photos.splice(0,0,url);
    await this.save(person);
    return url;
  }

  async allPeople(network = false): Promise<Partial<Person>[]> {
    const people = await data.people.getIds(this.cache.user, network);
    if (network) return await Promise.all(people.map(p => this.temp(p, true)));
    return people;
  }

  async groupPeople(include_self = false, include_network = false, group_ids?: Uid[], page?: number): Promise<Partial<Person>[]> {
    let user_ids = await data.groups.groupUserIds(this.cache.user, group_ids);
    if (user_ids && user_ids.length) {
      let connections = include_self ? [] : [this.cache.user.profile];
      if (include_network) {
        let emails;
        if (this.cache.me && this.cache.me.comms) emails = parsers.findEmail(this.cache.me.comms);
        if (!emails || !emails.length) emails = [this.cache.user.email];
        connections = connections.concat(await data.people.connectedUserProfiles(this.cache.user, emails));
      }
      user_ids = user_ids.filter(g => !connections.includes(g));
      const people = await data.people.getUserPeople(this.cache.user, user_ids, page);
      if (people) return Promise.all(people.map(p => p.network ? this.temp(p, true) : p));
    }
    return [];
  }

  async groupPromote(group_ids: Uid[]) { 
    const global_users = await data.users.globalByGroup(group_ids);
    const users = global_users.filter(g => g.promote);
    const people = await data.people.vanityByIds(users.map(u => u.id));
    if (people) return people.map(v => new Person({
      type: EntityType.Person,
      id: `profile/${v.vanity}`,
      vanity: v.vanity,
      // comms: v.comms,
      tags: v.tags,
      photos: v.photos,
      names: v.names,
      nickName: v.nickName,
      displayName: v.displayName,
      urls: v.urls,
    }));

   return [];
  }

  async userPeople(terms: string[], tag_types: TagType[] = [TagType.skill], as_guest = false): Promise<Partial<Person>[]> {
    let user_comms;
    if (this.cache.user.isAuthenticatedNonGuest() && !as_guest) {
      user_comms = this.cache.me && this.cache.me.comms ? parsers.findEmail(this.cache.me.comms) : [this.cache.user.email];
    }

    if (tag_types.includes(TagType.skill)) {
      const groups = this.cache.user.categoryGroups();
      const group_ids: Uid[] = groups ? groups.map(g => g.id) : undefined;
      let global_cats = !groups || !groups.length || groups.reduce((a,b) => a || b.global_categories, false);

      return data.people.bigQuerySearchUserStats(user_comms ? this.cache.user : null, group_ids, global_cats, user_comms, terms);
    }
    return data.people.bigQuerySearchUsers(user_comms ? this.cache.user : null, user_comms, tag_types, terms);
  }

  async temp(person: Partial<Person>, network: boolean = undefined): Promise<Person> {
    const temp_person = new Person(person);
    temp_person.comms = temp_person.comms.filter(c => c !== temp_person.id && c.toLowerCase() !== this.cache.user.email);
    temp_person.tempId();
    if (network !== undefined) temp_person.network = network;
    let new_person = temp_person;
    if (this.cache.user.isAuthenticatedNonGuest()) await this.cache.cachePerson(new_person);
    return temp_person;
  }

  async create(person: Partial<Person>): Promise<Person> {
    if (!this.cache.user.isAuthenticatedNonGuest()) throw new InternalError(500, 'Cannot create person for anonymous or guest user', person);
    person.id = null;
    person.network = false;
    if (this.cache.people_loaded) await this.cache.people_loaded;
    const new_person = await data.people.newPerson(this.cache.user, person, this.cache);
    await this.cache.cachePerson(new_person);
    if (person.id && person.id !== new_person.id) {
      await this.cache.remapPersonCache(person.id, new_person);
      await this.cache.purgePersonCache(person);
    }

    return new_person;
  }

  async delete(person: Partial<Person>, remote_delete = true) {
    if (person.self) throw new InternalError(400, `Cannot delete self ${person.id}`, person);
    await this.cache.purgePersonCache(person);
    await data.people.delete(this.cache.user, person, remote_delete && !config.isEnvOffline());
  }

  async deletePhoto(person: Person, photo_id: Uid): Promise<void> {
    if (person.photos) {
      const skip_card = person.photos[0] !== photo_id;
      person.photos = person.photos.filter(p => p !== photo_id);
      await this.save(person, skip_card);
    }
    if (photo_id.startsWith('askfora://')) {
      const id = photo_id.slice(`askfora://${this.cache.user.profile}/`.length);
      return data.people.deletePhoto(this.cache.user, id);
    }
  }

  export() {
    doExport(this.cache.user.profile); //data.people.bigQueryExport(this.cache.user);
  }

  async loadPeople(people: Partial<Person>[]): Promise<Person[]> {
    if (!people || !people.length) return [];

    const users = people.filter(c => c.vanity && !c.self && c.vanity !== this.cache.me.vanity);
    const contacts = people.filter(c => !c.vanity && !c.self && c.id !== this.cache.me.id);
    const self = people.find(c => c.self || c.vanity === this.cache.me.vanity || c.id === this.cache.me.id) ? [this.cache.me] : [];

    const vanity_map: {[key:string]: Uid} = {};
    users.forEach(u => vanity_map[u.vanity] = u.id);

    const loaded_people =  [...self, ...(await this.byId(contacts.filter(c => c.id).map(c => c.id))),
        ...(await data.users.globalVanities(this.cache.user, users.map(u => u.vanity).filter(v => !v.startsWith('g-') && v !== FORA_PROFILE), true)).map(u => {
          u.id = vanity_map[u.vanity];
          return new Person(u);
      })];

    return loaded_people
  }

  async byId(ids: Uid | Uid[], cache = true): Promise<Person[]> {
    if (!ids || !ids.length) return [];
    const people = await this.cache.loadPeopleIds(ids, cache);
    // await data.people.updatePeopleWithProfiles(this.cache.user, people);
    return people;
  }

  async findConnections(comms: string[]): Promise<Person[]> {
    const connections = (await data.people.connectionsByAttributeComms(this.cache.user, comms));
    const return_people = connections.map(p => new Person(p));
    return_people.forEach(person => this.cache.cachePerson(person));
    return return_people;
  }

  // returns a list of people
  // find people based on name or phone in list  
  async findByComms(comms: string[], network = false, cache = true): Promise<Partial<Person>[]> {
    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'findByComms', this.cache.user.profile, `Looking for ${JSON.stringify(comms)}`);
    const result_people: Person[] = [];
    const pids: Uid[] = [];

    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'findByComms', this.cache.user.profile, `Searching datastore for ${JSON.stringify(comms)}`);

    const people = await data.people.byAttributeComms(this.cache.user, comms);
    for (const person of people) {
      if (person) {
        if (person.id === this.cache.me.id) result_people.push(new Person(this.cache.me));
        else if(!pids.includes(person.id)){
          const new_person = new Person(person);
          if (this.cache.me && person.id === this.cache.me.id) new_person.self = true;
          else new_person.comms.filter(c => c !== this.cache.user.email);
          if (!new_person.id) logging.warnFP(LOG_NAME, 'findByComms', this.cache.user.profile, `Trying to return datastore match without id ${JSON.stringify(new_person)}`);
          else {
            result_people.push(new_person);
            if (cache) this.cache.cachePerson(new_person);
          }
        }
        pids.push(person.id);
      }
    }

    return result_people;
  }
  // returns list of people
  // find people based on name in a list
  async findByName(names: string[]): Promise<Partial<Person>[]> {
    let match_people: {[key:string]: string} = null;

    // set of lowercase single word names
    const name_set = wordSet(names, true, STRIP_REGEX);
    for (const name of name_set) {
      // match one name, {id:displayName}
      if (name.length === 1) continue;
      let candidate = await this.cache.peopleName(name); // this.cache.people_names[name]; // {id:displayName}

      if (!candidate) {
        candidate = {};
        // check for prefixes of 3 characters or more
        let pname = name;
        while(pname.length > 3) {
          pname = pname.slice(0, -1);
          const partial_candidate = await this.cache.peopleName(pname); // Object.keys(this.cache.people_names).filter(n => n.startsWith(name));
          if (partial_candidate) {
            for (const c_id in partial_candidate) candidate[c_id] = partial_candidate[c_id];
          }
        }
     }

      if (!match_people) {
        match_people = candidate;
      } else if(candidate) {
        const new_matches: {[key:string]: string} = {};
        for (const c_id in candidate) {
          if (candidate[c_id] && match_people[c_id]) new_matches[c_id] = candidate[c_id];
        }
        match_people = new_matches;
      }
    }

    const matches: Partial<Person>[] = [];
    if (match_people) {
      for (const m_id in match_people) {
        if (match_people[m_id]) matches.push(new Person({ id: m_id, displayName: match_people[m_id] }));
      }
    }
    return matches;
  }

  // returns a list of people
  async findByOrg(orgs: string[]): Promise<Partial<Person>[]> {
    let match_people = null;

    // set of lowercase single word names
    const org_set = wordSet(orgs, true, STRIP_REGEX);
    for (const index in org_set) {
      // match one name, {id:displayName}
      if (org_set[index].length === 1) continue;
      const candidate = await this.cache.orgPeople[org_set[index]];
      if (!match_people) {
        match_people = candidate;
      } else {
        const new_matches = {};
        for (const c_id in candidate) {
          if (candidate[c_id] && match_people[c_id]) new_matches[c_id] = candidate[c_id];
        }
        match_people = new_matches;
      }
    }

    const matches: Partial<Person>[] = [];
    if (match_people) {
      for (const m_id in match_people) {
        if (match_people[m_id]) matches.push(new Person({ id: m_id, displayName: match_people[m_id] }));
      }
    }
    return matches;
  }

  async findByTerms(terms: string[], options: SearchOptions): Promise<{person: Partial<Person>, weight: number, stddev: number}[]> {
    // network: 'exclude' | 'include' | 'only' = 'exclude', require_email?: boolean, mandatory_tags?: Tag[][], group_search_ids?: Uid[], additional_user_ids?: Uid[], only_users?: boolean) 

    let people: {person: Partial<Person>, weight: number, stddev: number}[] = [];

    try {
      let add_ids = options.additional_user_ids ? options.additional_user_ids.slice() : [];
      if (options.group_search_ids && options.group_search_ids.length) {
        for (const group_id of options.group_search_ids) {
          const filter = {name:'groups', op: '=', val: group_id};
          const keys = await data.plugins.storagePlugin().findKeysByFilter(null, GlobalType.User, [filter]);
          if (keys) keys.filter(k => k.name !== this.cache.user.profile).forEach(k => add_ids.push(k.name));
        }
      }
      if (logging.isDebug()) DEBUG('findByTerms: groups --> users : %O', add_ids);

      let emails = null;
      if (['include', 'only'].includes(options.network)) {
        emails = this.cache.me ? parsers.findEmail(this.cache.me.comms) : [this.cache.user.email];
        if (!emails.includes(this.cache.user.email)) emails.push(this.cache.user.email);
      }

      const result_people = terms.length ? await data.people.bigQueryByTerms(this.cache.user, terms, emails, add_ids, options.require_email, options.network === 'only') : [];
      let filter_people = filterPeople(this.cache.user, this.cache.me, result_people, options.mandatory_tags, options.only_users);

      people = peopleUtils.mergeResultPeople(filter_people.map(person => { return { person }}));

      people.filter(p => p.person.id).forEach(rp => this.cache.cachePerson(rp.person));
    } catch (err) {
      logging.errorFP(LOG_NAME, 'findByTerms', this.cache.user.profile, 'Error querying for people', err);
    }

    return people;
  }

  async findBySkills(user_skills: string[], options: SearchOptions): Promise<{
      person: Partial<Person>, 
      weight: number,
      stddev: number,
    }[]> {
    if (!user_skills) return [];

    if (logging.isDebug()) {
      // DEBUG('findBySkills: skills - %O', search_skills);
      if (options.mandatory_tags) DEBUG('findBySkills: mandatory tags - %O', options.mandatory_tags);
      if (options.group_search_ids) DEBUG('findBySkills: group search ids - %O', options.group_search_ids);
    }

    // Find all the users that are in the shared groups where `search_groups_contacts` is true

    try {
      let add_ids = options.additional_user_ids ? options.additional_user_ids.slice() : [];
      if (options.group_search_ids && options.group_search_ids.length) {
        for (const group_id of options.group_search_ids) {
          const filter = {name:'groups', op: '=', val: group_id};
          const keys = await data.plugins.storagePlugin().findKeysByFilter(null, GlobalType.User, [filter]);
          if (keys) keys.filter(k => k.name !== this.cache.user.profile).forEach(k => add_ids.push(k.name));
        }
      }
      if (logging.isDebug()) DEBUG('findBySkills: groups --> users : %O', add_ids);

      let emails = null;
      if (options.network && ['include', 'only'].includes(options.network)) {
        emails = this.cache.me ? parsers.findEmail(this.cache.me.comms) : [this.cache.user.email];
        if (!emails.includes(this.cache.user.email)) emails.push(this.cache.user.email);
      }

      const search_terms = _.uniq(user_skills);

      if (logging.isDebug()) DEBUG('findBySkills: skills flattened - %O', search_terms);
      const filter = peopleUtils.seniorTitle(search_terms) ? null : peopleUtils.JOB_TITLES.slice();
       
      const result_people = search_terms.length ? await data.people.bigQueryBySkills(this.cache.user, search_terms, filter, emails, add_ids, options.require_email, options.network === 'only') : [];

      if (logging.isDebug()) DEBUG('findBySkills: persons - %O', result_people);

      result_people.forEach(rp => {
        if (!rp.person.id) rp.person.tempId();
      });

      let filter_people = peopleUtils.mergeResultPeople(result_people);

      filter_people = filterWeightedPeople(this.cache.user, this.cache.me, result_people, options.mandatory_tags, options.only_users);

      const weights = filter_people.map(wp => wp.weight);
      const max = weightMax(weights);
      const people = filter_people.filter(wp => wp.weight >= max).sort((a,b) => b.stddev - a.stddev);

      people.filter(p => p.person.id).forEach(rp => this.cache.cachePerson(rp.person));
      
      logging.infoFP(LOG_NAME, 'findBySkills', this.cache.user.profile, `Found ${people.length} people matching ${JSON.stringify(user_skills)}`);

      return people;

    } catch (err) {
      logging.errorFP(LOG_NAME, 'findBySkills', this.cache.user.profile, 'Error querying for people', err);
    }

    return [];
  }

  async findIntroductions(search_skills: string[] = null, max: number = null): Promise<Partial<Person>[]> {
    let emails;
    if (this.cache.me && this.cache.me.comms) emails = parsers.findEmail(this.cache.me.comms);
    if (!emails || !emails.length) emails = [this.cache.user.email];
    const connections = await data.people.byConnections(this.cache.user, search_skills, emails);
    const comms = parsers.findEmail(flatten(connections.map(c => c.comms)));
    const known = await data.people.byAttributeComms(this.cache.user, comms);
    const known_comms = flatten(known.map(c => c.comms));
    const unknown = connections.filter(c => _.intersection(c.comms, known_comms).length === 0);
    if (max) return unknown.slice(0, max);
    return unknown;
  }

  async findConnectedUserPeople(): Promise<Partial<Person>[]> {
    const connections = await data.people.connections(this.cache.user);
    return connections.filter(c => !c.vanity || c.vanity !== this.cache.user.vanity);
  }

  async introductionById(id: Uid): Promise<Introduction> {
    const intro = await data.people.introductionById(id);
    if (intro && (intro.requested_by === this.cache.user.profile || intro.intro_to === this.cache.user.profile)) return intro;
    return null;
  }

  async findMyIntroductionRequests(): Promise<Introduction[]> {
    return data.people.introductionsByAtt('requested_by', this.cache.user.id);
  }

  async findIntroductionsWaitingForMe(): Promise<Introduction[]> {
    return data.people.introductionsByAtt('intro_to', this.cache.user.id);
  }

  async saveIntroduction(intro: Introduction) {
    if (intro.intro_to === this.cache.user.profile || intro.requested_by === this.cache.user.profile) return data.people.saveIntroduction(intro);
    return intro;
  }

  async createIntroduction(intro_to: Uid, about_project?: Project) {
    const intro = new Introduction({
      id: uuid(),
      created: new Date(),
      project: about_project ? about_project.id : null,
      skills: about_project ?
        about_project.searched_skills ? about_project.searched_skills : 
        about_project.skills.split(' ') : null,
      requested_by: this.cache.user.id,
      intro_to,
    });
    return data.people.saveIntroduction(intro);
  }

  async acceptIntroduction(id: Uid): Promise<Introduction> {
    const intro = await data.people.introductionById(id);
    if (intro.intro_to === this.cache.user.id) {
      intro.accepted = new Date();
      return data.people.saveIntroduction(intro);
    }
  }

  async getPhoto(photo_id: Uid): Promise<Document> {
    const id = photo_id.slice(`askfora://${this.cache.user.profile}/`.length);
    return data.people.photoById(this.cache.user, id);
  }

  async getStats(network = false, groups = false): Promise<Stats> {
    const group_user_ids = groups ? await data.groups.groupUserIds(this.cache.user) : null;
    const user_comms = this.cache.user.email ? [this.cache.user.email] : null;

    if (SourceController.firstRunDone(this.cache.user, EntityType.Person) && this.cache.user.exported) return data.people.getStats(this.cache.user, user_comms, group_user_ids, network);
    return data.people.getStats(null, user_comms, group_user_ids, network);
  }

  async getGlobalTags(limit: number): Promise<SkillStat[]> {
    return data.people.getGlobalTags(this.cache.user, limit);
  }

  async getGlobalUserTags(limit: number): Promise<SkillStat[]> {
    let user_comms;
    if (this.cache.user.isAuthenticatedNonGuest()) {
      if (this.cache.me && this.cache.me.comms) user_comms = parsers.findEmail(this.cache.me.comms);
      if ((!user_comms || !user_comms.length) && this.cache.user.email) user_comms = [this.cache.user.email];
      if (!SourceController.firstRunDone(this.cache.user, EntityType.Person) || !this.cache.user.exported) return data.people.getGlobalUserTags(null, null, limit);
    }
    return data.people.getGlobalUserTags(this.cache.user, user_comms, limit);
  }

  async getNames(names: string[], network = false, group_search_ids?: Uid[]): Promise<string[]> {
    const additional_user_ids: Uid[] = group_search_ids && group_search_ids.length ? await data.groups.groupUserIds(this.cache.user, group_search_ids) : null;
    let aliases = null;
    if (network) {
      aliases = this.cache.me ? parsers.findEmail(this.cache.me.comms) : [this.cache.user.email];
      if (!aliases.includes(this.cache.user.email)) aliases.push(this.cache.user.email);
    }

    try {
      const result_names = names && names.length ? await data.people.lookupNames(this.cache.user, names, aliases, additional_user_ids) : [];
      return result_names;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'searchByName', this.cache.user.profile, 'Error querying for people', err);
    }
    return [];
  }

  async getOrgs(orgs: string[], network = false, group_search_ids?: Uid[]): Promise<string[]> {
    const additional_user_ids: Uid[] = group_search_ids && group_search_ids.length ? await data.groups.groupUserIds(this.cache.user, group_search_ids) : null;
    let aliases = null;
    if (network) {
      aliases = this.cache.me ? parsers.findEmail(this.cache.me.comms) : [this.cache.user.email];
      if (!aliases.includes(this.cache.user.email)) aliases.push(this.cache.user.email);
    }

    try {
      const result_orgs = orgs && orgs.length ? await data.people.lookupOrgs(this.cache.user, orgs, aliases, additional_user_ids) : [];
      return result_orgs;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'searchByName', this.cache.user.profile, 'Error querying for people', err);
    }
    return [];
  }

  async getTags(limit: number, network = false, groups = false): Promise<SkillStat[]> {
    if (!this.cache.user.email) throw new InternalError(500, 'Cannot get tags without user email');
    const group_user_ids = groups ? await data.groups.groupUserIds(this.cache.user) : undefined;
    const user_comms = this.cache.me && this.cache.me.comms ? parsers.findEmail(this.cache.me.comms) : [this.cache.user.email];
    if (!user_comms.length) user_comms.push(this.cache.user.email);
    return data.people.getTags(this.cache.user, user_comms, limit, network, group_user_ids ? group_user_ids.filter(i => i !== this.cache.user.profile) : undefined);
  }

  async getOrgStats(limit: number, network = false, groups = false): Promise<SkillStat[]> {
    if (!this.cache.user.email) throw new InternalError(500, 'Cannot get tags without user email');
    const group_user_ids = groups ? await data.groups.groupUserIds(this.cache.user) : null;
    const user_comms = this.cache.me && this.cache.me.comms ? this.cache.me.comms : [this.cache.user.email];
    if (!user_comms.length) user_comms.push(this.cache.user.email);
    return data.people.getOrgs(this.cache.user, user_comms, limit, network, group_user_ids);
  }

  //map from people with ids to a thin object we can store in other entities that reference these people
  async loadEntityPeople(people: Partial<Person>[]): Promise<Partial<Person>[]> {
    // const ids = people.map(p => p.id).filter(id => id);
    const found_people = await this.loadPeople(people); // findById(ids);
    return found_people.map(p => {
      return { 
        id: p.id,
        displayName: p.displayName,
        nickName: p.nickName,
        network: p.network,
        photos: p.photos,
        vanity: p.vanity,
      }
    });
  }

  async save(person: Person, skip_card = false): Promise<Person> {
    const oldid = person.id;
    person = await data.people.savePerson(this.cache.user, person);
    const savers: Promise<any>[] = [
      this.cache.cachePerson(person, oldid !== person.id),
      this.cache.remapPersonCache(oldid, person),
    ];

    let regen_card = false;

    if (person.self) {
      // TODO check for update to ready or premier
      // ProfileStatus.Basic => Ready => Premier
      // NotificationType.Onboarding, TemplateType.Ready
      // NotificationType.Onboarding, TemplateType.Premier
      this.cache.me = person;
     
      if (!this.cache.user.vanity) {
        await data.users.generateVanity(this.cache.user, person, this.cache.getGroup());
        savers.push(data.users.save(this.cache.user, false, false, false));
      }

      if (this.cache.user && !skip_card) {
        const vanity = await data.users.vanity(this.cache.user.vanity);
        if (!vanity || vanity.status === ProfileStatus.Basic || vanity.status === ProfileStatus.Ready) {
          const new_vanity = await data.users.saveVanity(this.cache.user, person);
          if (!vanity || vanity.status !== new_vanity.status) {
            let template;
            if (new_vanity.status === ProfileStatus.Premier) template = TemplateType.Premier;
            else if(new_vanity.status === ProfileStatus.Ready && (!vanity || vanity.status === ProfileStatus.Basic)) template = TemplateType.Ready;

            if (template) {
              const name = this.cache.me ? this.cache.me.displayName : this.cache.user.name;
              const profile_url = mapURL(`/app/profile/${this.cache.user.vanity}`, this.cache.getGroup());
              savers.push( 
                mailTemplate([{ Email: this.cache.user.email, Name: name}], [],
                  NotificationType.Onboarding,
                  template,  
                  { firstname: name.split(' ')[0], profile: profile_url, }
                )
              );
            }
          } else savers.push(data.users.saveVanity(this.cache.user, person));
          regen_card = true;
        }
      }

    }

    await Promise.all(savers);

    // fire and forget updating card
    if (regen_card) setTimeout(() => data.profiles.get(this.cache.user, this.cache.user.vanity, true), 10000);
    return person;
  }

  async saveTemplates(templates: Uid[]): Promise<void> {
    // TODO check for update to ready or premier
    const user_vanity = this.cache.user && this.cache.user.vanity ? await data.users.vanity(this.cache.user.vanity) : null;
    const existing_status = user_vanity ? user_vanity.status : ProfileStatus.Basic;
    const new_vanity = await data.users.updateVanity(this.cache.user, {vanity: this.cache.me.vanity, templates})

    this.cache.user.settings.templates = templates;
    await data.users.save(this.cache.user);

    if (user_vanity && (existing_status === ProfileStatus.Basic || existing_status === ProfileStatus.Ready)) {
      let template;
      if (new_vanity.status === ProfileStatus.Premier) template = TemplateType.Premier;
      else if(new_vanity.status === ProfileStatus.Ready && existing_status === ProfileStatus.Basic) template = TemplateType.Ready;

      if (template) {
        const name = this.cache.me ? this.cache.me.displayName : this.cache.user.name;
        const profile_url = mapURL(`/app/profile/${this.cache.user.vanity}`, this.cache.getGroup());
        await mailTemplate([{ Email: this.cache.user.email, Name: name}], [],
          NotificationType.Onboarding,
          template,  
          { firstname: name.split(' ')[0], profile: profile_url, }
        );
      }
    }
  }

  async reload(person: Person): Promise<Person> {
    const reloaded_person = await data.people.reload(this.cache.user, person);
    if (reloaded_person) {
      if (!this.cache.read_only) {
        await this.cache.cachePerson(reloaded_person);
        if (person.id && person.id !== reloaded_person.id) {
          await this.cache.remapPersonCache(person.id, reloaded_person);
          await this.cache.purgePersonCache(person);
        }
      }
      return reloaded_person;
    } else {
      if (person.self) throw new InternalError(400, `Cannot delete self ${person.id}`, person);
      await this.cache.purgePersonCache(person);
      await data.people.delete(this.cache.user, person, false);
    }
  }
}
