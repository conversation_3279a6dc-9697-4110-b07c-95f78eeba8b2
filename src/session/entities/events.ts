import data from '../../data';
import * as itemTypes from '../../types/items';
import DataCache from '../data_cache';

import { sortEvents } from '../../utils/datetime';
import logging from '../../utils/logging';

type Event = itemTypes.Event;
const LOG_NAME = 'data.entities.Events';

class Calendars {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  get calendars() {
    return this.cache.user.settings.calendars;
  }

  selected(calid: string): boolean {
    if (calid && this.calendars && this.calendars.sources[calid]) return this.calendars.sources[calid].selected;
    return false;
  }

  async sync(calid: string, sync: boolean) {
    if (this.cache.user.settings.calendars.sources[calid]) {
      this.cache.user.settings.calendars.sources[calid].sync = sync;
      this.cache.user.settings.calendars.sources[calid].selected = sync;

      const saving = [];
      saving.push(data.users.save(this.cache.user, false, false, false));
      if (!this.cache.user.refreshing) saving.push(data.users.save(this.cache.user, true, false, false));
      await Promise.all(saving).catch(e => logging.errorF(LOG_NAME, 'sync', 'Error saving user after sync calendar', e));
    }
  }

  synced(calid: string): boolean {
    if (this.calendars.sources[calid]) return this.calendars.sources[calid].sync;
    return false;
  }
}

export default class Events {
  cache: DataCache = null;
  calendars: Calendars = null;

  constructor(cache: DataCache) {
    this.cache = cache;
    this.calendars = new Calendars(cache);
  }

  // TODO(all): test!

  calendar(id: string) {
    if (id in this.cache.user.settings.calendars.sources) {
      const cal = {};
      Object.assign(cal, this.cache.user.settings.calendars.sources[id]);
      return cal;
    }
    return null;
  }

  async cancel(event: Event) {
    await data.events.cancel(this.cache.user, event);

    for (const index in this.cache.events) {
      if (this.cache.events[index].id === event.id) {
        this.cache.events.splice(index as any, 1);
        this.cache.cacheDirty(['events'/*, 'people_events'*/]);
        break;
      }
    }

    /*for(var index in event.people) {
     var person = event.people[index];
     peopleUtils.removePeopleMatch(this.cache.people_events, person.id, event.id);
     this.cache.cacheDirty('people_events');
     } */
  }

  // this requires people objects with comms in order to send invites
  async create(event: Event) {
    /*for (const pindex in event.people) {
      const person = event.people[pindex];
      if (!person.self) peopleUtils.savePeopleMatch(this.cache.people_events, person.id, event.id);
    }*/
    const new_event = await data.events.create(this.cache.user, new itemTypes.Event(event));
    this.cache.events.push(new_event);
    this.cache.cacheDirty('events');
    // this.cache.cacheDirty('people_events');
    sortEvents(this.cache.events);
    return new_event;
  }

  async reload(event: Event) {
    const reloaded_event = await data.events.reload(this.cache.user, event);
    if (reloaded_event) {
      this.cache.events.push(reloaded_event);
      if (!this.cache.read_only) {
        this.cache.cacheDirty('events');
        //this.cache.cacheDirty('people_events');
      }
      sortEvents(this.cache.events);
      return reloaded_event;
    }
  }
}
