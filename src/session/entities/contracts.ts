import data from '../../data';
import lang from '../../lang';

import DataCache from '../data_cache';
import ForaUser from '../user';

import stripe from '../../sources/stripe_controller';

import { InternalError } from '../../types/globals';
import { Group } from '../../types/group';
import { Contract, Project } from '../../types/items';
import { AuthProviders, EntityType, NotificationType, Uid } from '../../types/shared';

import * as funcs from '../../utils/funcs';
import logging from '../../utils/logging';
import notify, { NotifyType } from '../../utils/notify';

const LOG_NAME = 'data.entities.contracts';

export default class Contracts {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async create(_contract: Partial<Contract>): Promise<Contract> {
    const contract: Contract = await data.contracts.create(this.cache.user, _contract);
    this.cache.contracts[contract.id] = contract;
    this.cache.cacheDirty('contracts');
    return contract;
  }

  async delete(contract: Partial<Contract>): Promise<void> {
    await data.contracts.delete(contract);
    delete this.cache.contracts[contract.id];
    this.cache.cacheDirty('contracts');

    if (contract.client_id === this.cache.user.profile && contract.contractor_id) await data.users.refreshSet(new ForaUser(contract.contractor_id), EntityType.Contract);
    else if(contract.contractor_id === this.cache.user.profile && contract.client_id) await data.users.refreshSet(new ForaUser(contract.client_id), EntityType.Contract);
  }

  getSignUrl(id: Uid, email: string = null) {
    const email_group = email ? this.cache.groupByEmail(email) : null;
    const group = this.cache.getGroup(email_group);
    return funcs.getContractUrl(group, id, true);
  }

  getDocUrl(id: Uid, email: string = null) {
    const email_group = email ? this.cache.groupByEmail(email) : null;
    const group = this.cache.getGroup(email_group);
    return funcs.getContractUrl(group, id, false);
  }

  getRedirectUrl(id: Uid, email: string = null, host_group?: Group) {
    const group =  host_group ? host_group :
      email ? this.cache.groupByEmail(email) : this.cache.getGroup();
    return funcs.getContractUrl(group);
  }

  async load(contract_id: Uid): Promise<Contract> {
    // find the contract doc and figure out the client
    const shared_contract = await data.contracts.byId(contract_id);
    let contract = this.cache.contracts[contract_id];
    if (shared_contract) {
      if (contract) {
        logging.infoFP(LOG_NAME, 'load', this.cache.user.profile, `Updating contract ${contract_id} from shared`);
        contract.hash = shared_contract.hash !== null && shared_contract.hash !== undefined ? shared_contract.hash : contract.hash;

        if (!contract.client_name) contract.client_name = shared_contract.client_name !== null && shared_contract.client_name !== undefined ? shared_contract.client_name : contract.client_name;
        contract.client_url = shared_contract.client_url !== null && shared_contract.client_url !== undefined ? shared_contract.client_url : contract.client_url;
        contract.client_signed = shared_contract.client_signed || contract.client_signed;
        contract.client_declined = shared_contract.client_declined || contract.client_declined;
        contract.client_reviewed = shared_contract.client_reviewed || contract.client_reviewed;

        if (!contract.contractor_name) contract.contractor_name = shared_contract.contractor_name !== null && shared_contract.contractor_name !== undefined ? shared_contract.contractor_name : contract.contractor_name;
        contract.contractor_url = shared_contract.contractor_url !== null && shared_contract.contractor_url !== undefined ? shared_contract.contractor_url : contract.contractor_url;
        contract.contractor_signed = shared_contract.contractor_signed || contract.contractor_signed;
        contract.contractor_declined = shared_contract.contractor_declined || contract.contractor_declined;
        contract.contractor_reviewed = shared_contract.contractor_reviewed || contract.contractor_reviewed;
        if (!this.cache.read_only) this.cache.cacheDirty('contracts');
      } else {
        logging.infoFP(LOG_NAME, 'load', this.cache.user.profile, `Cache contract not found ${contract_id} using shared`);
        contract = shared_contract;
        this.cache.contracts[contract.id] = contract;
        if (!this.cache.read_only) this.cache.cacheDirty('contracts');
      }
    } else logging.warnFP(LOG_NAME, 'load', this.cache.user.profile, `Shared contract not found ${contract_id}`);

    return contract;
  }

  async notify(contract: Contract, project: Project = null) {
    if (!contract.client_id) throw new InternalError(400, `Cannot notify on contract ${contract.id} without client id`, contract);
    if (!contract.contractor_id) throw new InternalError(400, `Cannot notify on contract ${contract.id} without contractor id`, contract);

    const user = new ForaUser(contract.client_id === this.cache.user.profile ? contract.contractor_id : contract.client_id);

    if (!(await data.users.init(user, false))) {
      await data.users.message(user, lang.notify.ERROR_SELF);
      return [];
    }

    await data.contracts.create(user, contract, false);

    const data_cache = new DataCache(user, null, user.loaded_groups ? Object.values(user.loaded_groups) : [], `update_${user.profile}`);
    data_cache.flushCaches([EntityType.Contract]);
    await data_cache.cleanCache();
    await data_cache.loadCache();

    // what to notify about
    if(contract.contractor_id === user.profile && !contract.contractor_reviewed) {
      // handled by the plugin - notifies contractor of being selected for projects
      return;
    }

    if (contract.client_id === user.profile && !contract.client_signed) {
      const group = this.cache.groupByEmail(contract.client_email);

      // tell client to sign the agreement
      const client_subject = lang.contract.CONTRACT_READY_SUBJECT(contract.contractor_name);
      const client_notification = {
        webpush: {
          notification: {
            tag: contract.id,
            title: lang.contract.CONTRACT_NOTIFY_TITLE,
            body: `${client_subject}.`,
            click_action: this.getSignUrl(contract.id, contract.client_email),
          },
        },
        email: {
          rcpts: [{ Name: contract.client_name, Email: contract.client_email }],
          subject: client_subject,
          message: lang.contract.CONTRACT_READY_MAIL(contract.contractor_name, this.getSignUrl(contract.id, contract.client_email)),
        },
        info: [contract],
        type: NotificationType.Contract_Ready,
        variables: {
          client_first_name: contract.client_name.split(' ')[0],
          contractor_full_name: contract.contractor_name,
          job: project ? funcs.mapURL(`/job/${project.id}`, group) : funcs.mapURL(`/jobs`, group),
        }
      };

      await notify(user, client_notification, NotifyType.PushAndEmail, group, false);
      
      // tell contractor about next steps
      const group_email = this.cache.getGroup(this.cache.groupByEmail(contract.contractor_email));
      const stripe_url = !this.cache.user.hasAccount(AuthProviders.Stripe) ? stripe.stripeConnectUrl(group_email) : null;

      if (stripe_url) {
        let client;
        let contractor;
        const projects = Object.values(this.cache.projects).filter(p => p.contract === contract.id);
        for (const project of projects) {
          client = project.client;
          contractor = project.contractor;
        }

        const contractor_subject = lang.contract.CONTRACTOR_NEXT_STEPS(contract.context);
        const contractor_notification = {
          webpush: /* stripe_url ? {
            notification: {
              tag: contract.id,
              title: 'AskFora Job',
              body: contractor_subject,
              click_action: stripe_url,
            },
          } :*/ null,
          email: {
            rcpts: [{ Name: contract.contractor_name, Email: contract.contractor_email }],
            subject: contractor_subject,
            message: lang.project.CONTRACTOR_NEXT_STEPS(contractor ? contractor.displayName : contract.contractor_name, client ? client.displayName : contract.client_name, stripe_url),
          },
          info: [contract],
          type: NotificationType.Contract_Signed,
          variables: {
            contractor_first_name: contract.contractor_name.split(' ')[0],
            job_name: 'AskFora', 
            client_first_name: contract.client_name.split(' ')[0],
          }
        };

        await notify(this.cache.user, contractor_notification,  NotifyType.EmailOnly, this.cache.groupByEmail(contract.contractor_email), false);
      }

      return;
    }

    logging.warnFP(LOG_NAME, 'notify', this.cache.user.profile, `Not sure what to notify ${user.profile} about`);

  }

  async save(contract: Contract) {
    const shared_contract = await this.load(contract.id);
    if (shared_contract) {
      contract.hash = shared_contract.hash !== null && shared_contract.hash !== undefined ? shared_contract.hash : contract.hash;
      contract.client_url = shared_contract.client_url !== null && shared_contract.client_url !== undefined ? shared_contract.client_url : contract.client_url;
      contract.client_signed = shared_contract.client_signed || contract.client_signed;
      contract.client_declined = shared_contract.client_declined || contract.client_declined;
      contract.client_reviewed = shared_contract.client_reviewed || contract.client_reviewed;

      contract.contractor_url = shared_contract.contractor_url !== null && shared_contract.contractor_url !== undefined ? shared_contract.contractor_url : contract.contractor_url;
      contract.contractor_signed = shared_contract.contractor_signed || contract.contractor_signed;
      contract.contractor_declined = shared_contract.contractor_declined || contract.contractor_declined;
      contract.contractor_reviewed = shared_contract.contractor_reviewed || contract.contractor_reviewed;
    }
    return this.create(contract);
  }

  async fetch(contract: Contract, refetch = false): Promise<Buffer> {
    return data.contracts.fetch(this.cache.user, contract, refetch);
  }
}
