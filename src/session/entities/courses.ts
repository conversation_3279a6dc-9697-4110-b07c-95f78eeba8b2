import _ from 'lodash';
import { cosineSimilarity } from 'vector-cosine-similarity';

import data from '../../data';
import DataCache from '../data_cache';

import { skillVector } from '../../skills/model';

import { Course } from '../../types/globals';
import { Uid } from '../../types/shared';

const LOG_NAME = 'entities.courses';

export default class Courses {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async courses(): Promise<Course[]> {
    let courses = await data.courses.load();
    return courses;
  }

  async get(id: Uid): Promise<Course> {
    return data.courses.get(id);
  }

  async removeCourse(course: Course)  {
    await data.courses.delete(course);
  }

  async findCourses(skills: string[], limit = 20): Promise<Partial<Course>[]> {
    // TODO: only look for group courses if group host
    const all_courses = await data.courses.findBySkills(skills, limit);
    const raw_courses = _.uniqBy(all_courses, 'id').map(c => {
      return {
        id: c.id,
        title: c.title,
        link: c.link,
        skills: c.skills,
        vector: c.vector,
        score: 0,
      }
    });

    const svec = await skillVector(skills);

    await Promise.all(raw_courses.map(async (c) => {
      c.score = cosineSimilarity(c.vector, svec)  
    }));

    // find best match and variety
    // todo, only include from group providers
    const courses = raw_courses.sort((a,b) => {
      return b.score - a.score;
    });

    return courses.slice(0,limit);
  }

  async save(course: Course): Promise<Course> {
    return data.courses.save(course);
  }

  async saveCourses(courses: Course[]): Promise<Course[]> {
    return data.courses.saveCourses(courses);
  }
}