import diff from 'changeset';
import * as fs from 'fs';
import _ from 'lodash';
import path from 'path';
import util from 'util';

import config from '../../config';
import data from '../../data';
import lang from '../../lang';

import { Email, InternalError, Referral, TemplateType, WebPush } from '../../types/globals';
import { Group } from '../../types/group';
import { Candidate, Contract, GlobalType, Person, Project, projectPerson } from '../../types/items';
import { ANONYMOUS_ID, AuthProviders, CandidateInfo, CandidateStateOrder, EntityType, NotificationType, NotifyEmailOnly, ProjectCandidateState, ProjectInfo, ProjectRate, ProjectSourcingType, TagType, Uid } from '../../types/shared';
import { FORA_PROFILE } from '../../types/user';

import { DAYS, getEndDate, getStartDate, } from '../../utils/datetime';
import { localeDowMonthDay } from '../../utils/format';
import { arrayContains, checkState, flatten, mapURL, saveOne, slimEntity } from '../../utils/funcs';
import logging from '../../utils/logging';
import notify, { NotifyType } from '../../utils/notify';
import parsers from '../../utils/parsers';
import peopleUtils from '../../utils/people';

import { DISCOUNT_FEE, SERVICE_FEE } from '../../routes/gopay';
import { lookupSkill } from '../../skills';
import Learn from '../../skills/learn';
import stripe from '../../sources/stripe_controller';
import ForaUser from '..//user';
import DataCache from '../data_cache';
import Search from '../search';
import Contracts from './contracts';
import People from './people';

const MAX_CANDIDATES = 10;
const DEBUG = (require('debug') as any)('fora:data:entities:projects');
const LOG_NAME = 'data.entities.projects';

const ADD_SKILLS = JSON.parse(fs.readFileSync(path.resolve(__dirname, '..', '..', 'files', 'add_skills.json'), { encoding: 'utf-8' }));

const PROJECT_UPDATE_FIELDS = [
  // 'accepted',
  'completed',
  'confidential',
  'contract',
  'duration',
  'end',
  'fee',
  'network',
  'notes',
  'deliverables',
  'requirements',
  'background',
  'payment',
  'progress',
  'rate',
  'refund',
  'skills',
  'title',
  'select_notice',
  'start',
  'flex_dates',
  'group_settigs',
  'service_fee',
  'transfer',
  'last_activity',
  'public',
  'groups',
  'sourcing_type',
  'sourcing_url',
];

const PROJECT_DATE_FIELDS = [
  'start',
  'end',
];

const PROJECT_REQUIRED_FIELDS = [
  'id',
  'title',
  'skills',
  'notes',
  'rate',
  'duration',
  'client',
  'start',
  'end',
];

const GROUP_FIELDS = [
  'no_referrals',
  'skip_payment',
  'skip_contracting',
  'search_mandatory_tags',
  'stripe_account',
  'simple_invite',
];

export interface ProjectNotifyOptions {
  group?: Group,
  notification?: NotificationType,
  template?: TemplateType,
  message?: string,
  when?: Date,
  referrer?: Partial<Person>,
}

export default class Projects {
  cache: DataCache = null;
  people: People = null;
  contracts: Contracts = null;
  search: Search = null;

  constructor(cache: DataCache, people: People, contracts: Contracts, search: Search) {
    this.cache = cache;
    this.people = people;
    this.search = search;
    this.contracts = contracts;
  }

  async archiveForCandidates(project: Project) {
    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'archiveForCandidates', this.cache.user.profile, `Archiving non-selected candidates for ${project.id}`);
    this.checkProject(project, true);
    for (const candidate of project.candidates) {
      if (candidate.askfora_id && (!project.contractor || candidate.askfora_id !== project.contractor.askfora_id)) {
        // create a project with no contractor, only them as a candidate and archived
        const archived_project = new Project(project);
        archived_project.archived = true;
        archived_project.escrow = null;
        archived_project.contractor = null;
        archived_project.candidates = [candidate];

        const archived_user = new ForaUser(candidate.askfora_id);
        if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'archiveForCandidates', this.cache.user.profile, `Archiving ${project.id} for ${candidate.askfora_id}`);
        await this.createForUser(archived_user, candidate, archived_project, false);
      }

      if (candidate.state === ProjectCandidateState.ACCEPTED ||
        (candidate.state !== ProjectCandidateState.SELECTED && config.isEnvOffline() && candidate.askfora_id)) {
        const candidate_emails = parsers.findEmail(candidate.comms);
        const candidate_to_email = candidate_emails && candidate_emails.length ? candidate_emails[0] : null;
        const email_candidate = {
          rcpts: [{ Name: candidate.displayName, Email: candidate_to_email }],
          ccs: [{ Name: this.cache.me.displayName, Email: this.cache.user.email }],
          subject: lang.project.CONTRACT_FILLED_SUBJECT(project),
          message: lang.project.CONTRACT_FILLED_MESSAGE(project, candidate),
        };

        const notify_project = new Project(project);
        notify_project.archived = true;

        await this.notify(notify_project, new ForaUser(candidate.askfora_id), candidate, email_candidate, { group: this.cache.groupByEmail(candidate_to_email), notification: NotificationType.Project_Closed });
      }
    }
  }

  checkProject(project: Project, save: boolean, cache: DataCache = null) {
    if (!cache) cache = this.cache;
    data.projects.checkProject(cache.user.profile, cache.me, project, save);
  }


  // locally saves a network connection
  async createProjectPerson(person: Partial<Person>, network: boolean = undefined): Promise<Person> {
    if (!this.cache.user.isAuthenticatedNonGuest()) throw new InternalError(500, 'Cannot create person for anonymous or guest user', person);
    const temp_person = new Person(person);
    temp_person.comms = temp_person.comms.filter(c => c !== temp_person.id && c.toLowerCase() !== this.cache.user.email);
    temp_person.tempId();
    if (network !== undefined) temp_person.network = network;
    let new_person = network ?  await this.people.save(temp_person, true) : await this.people.create(new Person(person));
    await this.cache.cachePerson(new_person);
    return new_person;
  }

  async create(new_project: Project, create_shared = true): Promise<Project> {
    const now = new Date();
    if (!new_project.last_update) new_project.last_update = now;
    if (!new_project.last_activity) new_project.last_activity = now;
    const save_client = new_project.client;

    const create_project = new Project(new_project);
    this.projectRole(create_project);
    if (create_project.id) this.checkProject(create_project, this.cache.user.isAuthenticated());
    const project = await data.projects.create(this.cache.user, create_project, create_shared);
    project.client = save_client;
    this.checkProject(project, this.cache.user.isAuthenticated());

    this.projectRole(project);

    if (!project.client.self && (!project.candidates || !project.candidates.filter(c => c.askfora_id).map(c => c.askfora_id).includes(this.cache.user.profile))) {
      const admin_groups = this.cache.user.adminGroups();
      if (admin_groups.length) {
        const global_client = await data.users.globalById(project.client.askfora_id)
        if(global_client && global_client.groups && _.intersection(admin_groups, global_client.groups).length) project.admin = true;
      }
    }

    if (this.cache.read_only) await this.cache.refreshCache(EntityType.Project);
    else {
      this.cache.projects[project.id] = project;
      this.cache.cacheDirty('projects');

      let people = [];
      if (project.client.askfora_id === this.cache.user.profile) {
        if (project.contractor) people.push(project.contractor);
        else if (project.candidates) people = project.candidates.slice();
      } else people.push(project.client);
    }

    return project;
  }

  async createForUser(user: ForaUser, person: Partial<Person>, project: Project, load = true) {
    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'createForUser', this.cache.user.profile, `Creating ${project.id} for ${user.profile} id ${person.id} ${load ? 'load' : ''}`);

    if (!(await data.users.init(user, false))) {
      logging.errorFP(LOG_NAME, 'createForUser', this.cache.user.profile, `Failed to initialize user ${user.profile} creating ${project.id}`, null);
      await data.users.message(user, lang.notify.ERROR_SELF);
      return [];
    }

    // let updated_project = null;
    const data_cache = new DataCache(user, null, user.loaded_groups ? Object.values(user.loaded_groups) : [], `update_${user.profile}`);
    await data_cache.loadSelf();
    const user_self = data_cache.me;

    let updated_project = new Project(project);
    if (project.contractor) updated_project.contractor = projectPerson(updated_project, project.contractor);
    if (project.candidates) updated_project.candidates = project.candidates.map(c => projectPerson(updated_project, c));
    if (updated_project.contractor && project.contractor) updated_project.contractor.state = project.contractor.state;

    // clear out ids
    if (user.profile !== this.cache.user.profile) {
      if (updated_project.client.askfora_id !== FORA_PROFILE) updated_project.client.id = null;
      if (updated_project.contractor) updated_project.contractor.id = null;
      if (updated_project.candidates) updated_project.candidates.forEach(c => { c.id = null });
    }

    if (user.id !== updated_project.client.askfora_id) {
      if (logging.isDebug(this.cache.user.profile) || logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'createForUser', this.cache.user.profile, `${user.profile} user is not client ${updated_project.client.askfora_id}`);
      if (updated_project.client.askfora_id !== FORA_PROFILE) {
        // find or create client
        const client_user = await data.users.globalById(updated_project.client.askfora_id);
        if (!client_user) logging.warnFP(LOG_NAME, 'createForUser', this.cache.user.profile, `${user.profile} missing client user ${updated_project.client.askfora_id} in project ${updated_project.id}`);
        let client_set = (await data.people.byAttributeComms(user, client_user ? [client_user.email] : parsers.findEmail(updated_project.client.comms))).filter(c => c.comms && c.id !== person.id && !c.self);
        if (!client_set || !client_set.length) {
          updated_project.client.tempId();
          updated_project.client = projectPerson(updated_project, await data.people.newPerson(user, updated_project.client, data_cache), updated_project.client);
          await data_cache.cachePerson(updated_project.client);
        } else {
          for (const c of client_set) {
            if (updated_project.client) {
              peopleUtils.mergePeople(updated_project.client, c, true);
              if (!updated_project.client.id) updated_project.client.id = c.id;
            }
            else updated_project.client = c;
          }
        }
      }

      updated_project.client.self = false;
      // updated_project.client = projectPerson(updated_project, updated_project.client);
      updated_project.client.askfora_id = project.client.askfora_id;
      updated_project.client.reminder = project.client.reminder;

      updated_project.archived = project.archived;
      updated_project.escrow = project.escrow;
      updated_project.contract = project.contract;

      if (user_self) {
        if (updated_project.contractor && updated_project.contractor.askfora_id === user.profile) {
          updated_project.contractor.self = true;
          updated_project.contractor.id = user_self.id;
          updated_project.contractor.comms = [user.email, user_self.id];
          updated_project.contractor.ready = user.hasAccount(AuthProviders.Stripe) || user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment);
        }

        const proj_candidate = updated_project.candidates.find(c => c.askfora_id === user.profile);
        if (proj_candidate) {
          proj_candidate.self = true;
          proj_candidate.id = user_self.id;
          proj_candidate.comms = [user.email, user_self.id];
          proj_candidate.ready = user.hasAccount(AuthProviders.Stripe) || user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment);
          if (!updated_project.me_candidate) updated_project.me_candidate = proj_candidate;
        }
      }
    } else if (updated_project.client.askfora_id !== this.cache.user.profile && user.profile !== this.cache.user.profile) {
      if (logging.isDebug(this.cache.user.profile) || logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'createForUser', this.cache.user.profile, `${user.profile} this user is not client ${updated_project.client.askfora_id}`);

      updated_project.client = projectPerson(updated_project, project.client);

      // don't save the local id for this candidate
      const c_i = updated_project.candidates.map((c, i) => { return { c, i } }).find(({ c, i }) => c.askfora_id === this.cache.user.profile);
      if (c_i) {
        const proj_candidate = c_i.c;
        const index = c_i.i;
        if (proj_candidate) {
          const new_candidate = projectPerson(updated_project, proj_candidate);
          if (new_candidate.comms) new_candidate.comms = new_candidate.comms.filter(c => c && c !== new_candidate.id);
          else {
            new_candidate.comms = [];
            logging.warnFP(LOG_NAME, 'createForUser', user.profile, `Project candidate without comms: ${JSON.stringify(new_candidate)}`);
          }
          if (new_candidate.id === this.cache.me.id) new_candidate.id = null;
          updated_project.candidates.splice(index, 1, new_candidate);
        }
      }

      if (updated_project.client.askfora_id === user.profile) {
        updated_project.client = projectPerson(updated_project, user_self, updated_project.client);
      }
    } else {
      if (logging.isDebug(this.cache.user.profile) || logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'createForUser', this.cache.user.profile, `${user.profile} refresh client ${updated_project.client.askfora_id}`);
      // refresh client 
      updated_project.client = projectPerson(updated_project, user_self, updated_project.client);
      updated_project.client.askfora_id = this.cache.user.profile;
    }

    updated_project = load ? await this.loadForUser(user, updated_project, user_self, false, data_cache, null, true) : updated_project;

    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'createForUser', user.profile, `Creating ${JSON.stringify(updated_project)}`);
    this.checkProject(updated_project, false, data_cache);
    data_cache.projectRole(user, updated_project, user_self);
    this.checkProject(updated_project, false, data_cache);
    await data.projects.create(user, updated_project, user.profile === updated_project.client.askfora_id);
    if (user.profile === updated_project.client.askfora_id) await this.updateShared(updated_project);

    if (user.profile === this.cache.user.profile && !this.cache.read_only) {
      const cache_project = new Project(updated_project);
      this.copyProject(cache_project, updated_project);
      this.cache.projects[cache_project.id] = cache_project;
      this.cache.cacheDirty(['projects'/*, 'people_projects'*/]);
    }
  }

  async candidateEmail(candidate: Partial<Person>): Promise<string> {
    if (candidate.askfora_id) {
      const candidate_user = await data.users.globalById(candidate.askfora_id);
      if (candidate_user && candidate_user.email) return candidate_user.email;
    }

    const emails = parsers.findEmail(candidate.comms);
    return emails && emails.length ? emails[0] : '<EMAIL>';
  }

  async clientEmail(project: Project): Promise<string> {
    if (project.client.askfora_id === this.cache.user.id) return this.cache.user.email;
    const client_user = project.client.askfora_id ? await data.users.globalById(project.client.askfora_id) : null;
    if (client_user && client_user.email) return client_user.email;

    const emails = parsers.findEmail(project.client.comms);
    return emails && emails.length ? emails[0] : '<EMAIL>';
  }

  async delete(project: Project, group?: Group): Promise<Project> {
    // escrow is https://stripe.com/docs/api#charge_object
    // refund is https://stripe.com/docs/api#refund_object
    let do_delete;

    // TODO: archvie attached note
    if ((!project.progress || project.rate ===  ProjectRate.fixed) && 
        !project.completed && project.escrow && !(project.escrow instanceof Array) && 
        project.escrow.id !== lang.project.SKIP_ESCROW && 
        (!project.refund || project.refund instanceof Array)) {
      if (project.client.self) {
        const refund = project.escrow.charges ?
          await stripe.refund(project.escrow.charges.data[0].id, project.escrow.on_behalf_of, project.escrow.amount / 100) : 
          lang.stripe.REFUND_STUB(lang.project.SKIP_CHARGE.id, project.escrow.amount);
        project.archived = true;
        project.refund = refund;
        project.last_update = new Date();           
        project.last_activity = project.last_update;
        project = await this.update(project);
      } else {
        const refund = project.escrow.charges ?
          await stripe.refund(project.escrow.charges.data[0].id, project.escrow.on_behalf_of, project.escrow.amount / 100) :
          lang.stripe.REFUND_STUB(lang.project.SKIP_CHARGE.id, project.escrow.amount);
        project.archived = true;
        project.refund = refund;
        project.last_update = new Date();           
        project.last_activity = project.last_update;
        project = await this.update(project, false);

        const to_email = await this.clientEmail(project)
        const email = {
          rcpts: [{ Name: project.client.displayName, Email: to_email }],
          subject: lang.project.CONTRACTOR_CANCEL_SUBJECT(project),
          message: lang.project.CONTRACTOR_CANCEL_MESSAGE(project, this.getUrl(project, to_email)),
        }

        await this.notify(project, new ForaUser(project.contractor.askfora_id), project.contractor, email, { group: this.cache.getNotifyGroup(to_email, group), notification: NotificationType.Project_Closed});
      }
    } else {
      if (project.completed) {
        project.archived = true;
        project.last_update = new Date();           
        project.last_activity = project.last_update;
        project = await this.update(project, project.client.self === true);
      } else {
        if (project.proposal) project.declined = true;
        project.last_update = new Date();           
        project.last_activity = project.last_update;

        // update contractor first
        await this.update(project, project.client.self);
        // await this.delete(project);
        do_delete = true;

        if (project.client.self) {
          // notify other candidates
          for (const candidate of project.candidates) {
            if (candidate.state === ProjectCandidateState.ACCEPTED || (!project.proposal && project.contractor && candidate.askfora_id == project.contractor.askfora_id)) {
              const candidate_emails = parsers.findEmail(candidate.comms);
              const candidate_to_email = candidate_emails  && candidate_emails.length ? candidate_emails[0] : null;
              const email_candidate = {
                rcpts: [{ Name: candidate.displayName, Email: candidate_to_email }],
                ccs: [{ Name: this.cache.me.displayName, Email: this.cache.user.email }],
                subject: lang.project.CONTRACT_FILLED_SUBJECT(project),
                message: lang.project.CONTRACT_FILLED_MESSAGE(project, candidate),
              };

              await this.notify(project, new ForaUser(candidate.askfora_id), candidate, email_candidate, { group: this.cache.getNotifyGroup(candidate_to_email, group), notification: NotificationType.Project_Closed});
            }
          }

          if (project.proposal && project.contractor) {
            const emails = parsers.findEmail(project.contractor.comms);
            const contractor_email = emails && emails.length ? emails[0] : null;
            const email_contractor = {
              rcpts: [{ Name: project.contractor.displayName, Email: contractor_email }],
              ccs: [{ Name: this.cache.me.displayName, Email: this.cache.user.email }],
              subject: lang.project.PROPOSAL_DECLINED_SUBJECT(project),
              message: lang.project.PROPOSAL_DECLINED_MESSAGE(project),
            };

            await this.notify(project, new ForaUser(project.contractor.askfora_id), project.contractor, email_contractor, { group: this.cache.getNotifyGroup(contractor_email, group), notification: NotificationType.Project_Proposal, template: TemplateType.Decline});
          }
        }
      }
    }

    if (do_delete) {
      await data.projects.delete(this.cache.user, project);
      delete this.cache.projects[project.id];
      this.cache.cacheDirty('projects');

      // create an archived project for every candidate who has looked at the job that isn't the contractor
      if (project.client.self) this.archiveForCandidates(project);

      let templates = await this.getTemplates(this.cache.user.vanity);
      if (templates.includes(project.id)) {
        templates = templates.filter(t => t !== project.id);
        await this.people.saveTemplates(templates);
      }
    }

    return project.archived ? project : undefined;
  }

  getProjectGroup(project: Project, email: string = null) {
    let group = null;
    if (project.group_settings && project.group_settings.group &&
      this.cache.user.loaded_groups &&
      this.cache.user.loaded_groups[project.group_settings.group]) {
      return this.cache.user.loaded_groups[project.group_settings.group];
    } else {
      const email_group = email ? this.cache.groupByEmail(email) : null;
      return this.cache.getGroup(email_group);
    }
  }

  async getTemplates(vanity?: string): Promise<Uid[]> {
    const my_vanity = this.cache.me ? this.cache.me.vanity : null;
    const user_vanity = vanity || my_vanity ? await data.users.vanity(vanity ? vanity : my_vanity) : null;
    return user_vanity ? user_vanity.templates : [];
  }

  getUrl(project: Project, email: string = null, message = null) {
    const group = this.getProjectGroup(project, email);
    if (message) return mapURL(`${config.get('NOTIFICATION_URL')}/?${message}`, group);
    else return mapURL(`${config.get('PROJECT_URL')}/${project.id}`, group);
  }

  getImportUrl(project: Project, email: string = null) {
    const group = this.getProjectGroup(project, email);
    return mapURL(`${config.get('NOTIFICATION_URL')}/settings/accounts`, group);
  }

  async sharedProjects(groups?: Uid[]): Promise<Project[]> {
    // get public projects or group projects
    const projects = await data.projects.load(this.cache.user, groups && groups.length ? false : true, groups);
    if (projects) return Promise.all(projects.map(proj => this.get(proj)));
  }

  async get(proj: Project, reload = false): Promise<Project> {
    return this.loadForUser(this.cache.user, proj, this.cache.me, reload, this.cache);
  }

  async byContract(contract_id: Uid[]): Promise<Project[]> {
    return data.projects.byContract(this.cache.user, contract_id);
  }

  async load(groups?: Uid[]): Promise<Project[]> {
    if (logging.isVerbose(this.cache.user.profile)) logging.verboseFP(LOG_NAME, 'load', this.cache.user.profile, 'Loading Projects');

    const start = new Date().getTime();
    const projects = {};

    let found_projects = [];
    let project_admin = [];

    //if (this.cache.user.loaded_groups) 
    if (groups && groups.length) {
      const gp_loaders: Promise<void>[] = [];
    // for (const group_id in this.cache.user.loaded_groups) 
      for(const group_id in groups) {
        if (this.cache.user.isAdmin(group_id)) {
          gp_loaders.push(new Promise(async c => {
            const filter = {name: 'groups', op: '=', val: group_id};
            const keys = await data.plugins.storagePlugin().findKeysByFilter(null, GlobalType.User, [filter]);
            const group_users = keys.map(k => k.name).map(user_id => new ForaUser(user_id));
            while(group_users.length) {
              const group_projects = flatten(await Promise.all(group_users.splice(0,50).map(load_user => data.projects.load(load_user))));
              const proj_ids = found_projects.map(f => f.id);
              found_projects = found_projects.concat(group_projects.filter(g => !proj_ids.includes(g.id)));
              project_admin = project_admin.concat(group_projects.map(p => p.id));
            }
            c();
          }));
        }
      }
      await Promise.all(gp_loaders);

    } else found_projects = await data.projects.load(this.cache.user);


    for (const project of found_projects) {
      if (!project.client) {
        logging.warnFP(LOG_NAME, 'load', this.cache.user.profile, `Found project with no client ${project.id}`);
        continue;
      }

      this.projectRole(project);

      if (!project.client.self && (!project.candidates || !project.candidates.filter(c => c.askfora_id).map(c => c.askfora_id).includes(this.cache.user.profile)) 
          && project_admin.includes(project.id)) project.admin = true;
      if (!project.client.self && !project.me_candidate && !project.admin) {
        if ((this.cache.user && project.client.askfora_id === this.cache.user.profile) || 
          (this.cache.me && project.client.id === this.cache.me.id)) {
            logging.warnFP(LOG_NAME, 'loadProject', this.cache.user.profile, `Project doesn't belong ${project.id} to ${this.cache.me ? this.cache.me.id : 'no one'} - fixing`);
            project.client.self = true;
          } else {
            logging.warnFP(LOG_NAME, 'loadProject', this.cache.user.profile, `Project doesn't belong ${project.id} to ${this.cache.me ? this.cache.me.id : 'no one'} - not fixing because ids don't match`);
          }
      }
      projects[project.id] = project;
    }

    if (logging.isVerbose(this.cache.user.profile)) logging.verboseFP(LOG_NAME, 'loadProjects', this.cache.user.profile, `Found projects = ${logging.formatAny(projects)}`);

    if (logging.isVerbose(this.cache.user.profile)) logging.verboseFP(LOG_NAME, 'loadProjects', this.cache.user.profile, `Done loading Projects in ${new Date().getTime() - start}`);
    return Object.values(projects);
  }

  async loadForUser(user: ForaUser, project: Project, me: Partial<Person>, reload = false, cache: DataCache, shared: Project = null, saving = false) {
    if (!project.id) return null;
    if (!shared) shared = await data.projects.projectById(project.id);
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Loading ${shared ? ' shared' : ''}${reload ? ' reload' : ''}`);
    if (!shared && user.isGuestAccount()) shared = cache.projects[project.id];
    if (!shared) return null;

    try { this.checkProject(shared, false, cache); } catch (e) {
      logging.errorF(LOG_NAME, 'load', 'shared project check failed", shared', e);
      return null;
    }

    cache.projectRole(user, project, me);

    let resave = false;

    if (reload) project = new Project(shared);
    else {
      project = new Project(project);
      const did_update = this.updateProject(user, project, shared, cache, true);
      if (did_update) resave = true;
    }

    const user_project = user.isAuthenticatedNonGuest() ? await data.projects.get(user, project.id) : null;
    if (user_project) {
      this.checkProject(user_project, false, cache);
      const did_update = this.updateProject(user, user_project, project, cache, false);
      if (did_update) resave = true;
      project = user_project;
    }

    if (!project.client) {
      throw new Error(`${LOG_NAME}.create got new project ${project.id} for ${cache.user.profile} with no client`);
    }

    if (user.profile !== project.client.askfora_id) {
      project.client.self = false;
      if (project.client.askfora_id !== FORA_PROFILE) project.client.id = null;
    }

    cache.projectRole(user, project, me);

    this.checkProject(project, false, cache);

    if (project.client.self) {
      if (!this.cache.user.isGuestAccount()) {
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Loading client self`);
        // make sure candidates exist as contacts for client
        if (project.candidates) {
          const delete_ids: Uid[] = [];
          const have_ids = project.candidates.map(c => c.id);
          const save_new: Partial<Candidate>[] = [];
          for (const candidate of project.candidates) {
            let build_candidate = projectPerson(project, candidate);
            build_candidate.network = ![ProjectCandidateState.RECOMMENDED, ProjectCandidateState.ACCEPTED, ProjectCandidateState.SELECTED, ProjectCandidateState.SUBMITTED, ProjectCandidateState.PAYMENT_REQUESTED].includes(build_candidate.state);

            const candidate_user = candidate.askfora_id ? await data.users.globalById(candidate.askfora_id) : null;
            const candidate_fora_user = candidate.askfora_id ? new ForaUser(candidate.askfora_id) : null;
            if (candidate_fora_user) await data.users.init(candidate_fora_user, false);

            let matches: Partial<Candidate>[] = candidate.id ? await cache.loadPeopleIds(candidate.id) : null;
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Loaded ${matches ? matches.length : '0'} matches for ${candidate.id}`);

            if ((!matches || !matches.length) && user.profile === this.cache.user.profile) {
              const ccomms = candidate.comms.slice().filter(c => c && c !== user.email);
              if (candidate_user && candidate_user.email && !ccomms.includes(candidate_user.email)) ccomms.push(candidate_user.email);

              matches = (await this.people.findByComms(ccomms)).filter(p => !p.self && p.id !== project.client.id && p.id !== me.id);
              if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Found ${matches ? matches.length : '0'} matches for ${candidate.id}`);
            }

            if ((!matches || !matches.length) && candidate_user && candidate_user.email) {
              matches = (await data.people.byAttributeComms(user, [candidate_user.email])).filter(p => !p.self && p.id !== project.client.id && p.id !== me.id);
              if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Comms found ${matches ? matches.length : '0'} matches for ${candidate.id}`);
            }

            let connect_candidate;
            if (!matches || !matches.length) {
              logging.warnFP(LOG_NAME, 'loadForUser', this.cache.user.profile, `Creating candidate ${candidate.id} ${JSON.stringify(candidate.comms)} because of ${project.id} for ${user.id}`);
              const candidate_vanity = candidate_user ? await data.users.vanity(candidate_user.vanity) : null;
              if (candidate_vanity) {
                connect_candidate = peopleUtils.personFromVanity(candidate_vanity);
                connect_candidate.tempId();
                connect_candidate.comms = connect_candidate.comms.filter(c => c && c !== user.email);
                connect_candidate.network = candidate.network;
                logging.warnFP(LOG_NAME, 'loadForUser', this.cache.user.profile, `${project.id} Created vanity ${candidate_user.vanity} candidate ${connect_candidate.id} from ${candidate.id} ${JSON.stringify(candidate.comms)} for ${user.id}`);
                matches = [projectPerson(project, connect_candidate, candidate)];
              }
            }

            if (matches && matches.length) {
              if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Updating considering ${matches.length} matches for candidate id ${build_candidate.id} askfora_id ${candidate.askfora_id}`);

              for (const match of matches) {
                if (match.id === build_candidate.id || !build_candidate.id) {
                  if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Match for candidate ${build_candidate.id} ${util.format(build_candidate.comms)}: ${match.id}`);
                  peopleUtils.mergePeople(build_candidate, match, true);
                  if (!build_candidate.id) {
                    build_candidate.id = match.id;
                    save_new.push(build_candidate);
                  }
                } else if (have_ids.includes(match.id)) {
                  // check for duplicate candidates
                  if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Match for exising candidate ${util.format(candidate.comms)}: ${match.id}`);

                  const have_candidate = project.candidates.find(c => c.id === match.id);
                  const check_existing = projectPerson(project, have_candidate);

                  // update to latest state
                  const c_state = CandidateStateOrder.indexOf(candidate.state);
                  const h_state = CandidateStateOrder.indexOf(have_candidate.state);
                  if (c_state > h_state) have_candidate.state = candidate.state;

                  if (!have_candidate.askfora_id && candidate.askfora_id) have_candidate.askfora_id = candidate.askfora_id
                  if (!have_candidate.ready && candidate.ready) have_candidate.ready = true
                  if (have_candidate.network && !candidate.network) have_candidate.network = true;

                  const bemail = parsers.findEmail(build_candidate.comms);
                  const bphones = parsers.findPhone(build_candidate.comms);
                  const hemail = parsers.findEmail(have_candidate.comms);
                  const hphones = parsers.findPhone(have_candidate.comms);
                  have_candidate.comms = [have_candidate.id];
                  build_candidate.comms = build_candidate.comms = [build_candidate.id];
                  if (bemail) bemail.forEach(email => saveOne(have_candidate.comms, email));
                  if (hemail) hemail.forEach(email => saveOne(have_candidate.comms, email));
                  if (bphones) bemail.forEach(phone => saveOne(have_candidate.comms, phone));
                  if (hphones) hemail.forEach(phone => saveOne(have_candidate.comms, phone));

                  if (build_candidate.id) {
                    delete_ids.push(build_candidate.id);
                    resave = true;
                  }

                  build_candidate = have_candidate;

                  const cdiff = diff(have_candidate, check_existing);
                  if (cdiff.length) {
                    save_new.push(have_candidate);
                    resave = true;
                  }
                }
              }

              if (candidate_fora_user) build_candidate.comms = [build_candidate.id, candidate_fora_user.email];
              else {
                const email = parsers.findEmail(build_candidate.comms);
                const phones = parsers.findPhone(build_candidate.comms);
                build_candidate.comms = [build_candidate.id];
                if (email) build_candidate.comms = build_candidate.comms.concat(email);
                if (phones) build_candidate.comms = build_candidate.comms.concat(phones);
              }

              if (connect_candidate) peopleUtils.updateFromProfile(build_candidate, connect_candidate);

              // check for errant selected
              if (build_candidate.state === ProjectCandidateState.SELECTED &&
                (!project.contractor || project.contractor.askfora_id !== build_candidate.askfora_id)) {
                  build_candidate.state = ProjectCandidateState.ACCEPTED;
                  resave = true;
              }

              build_candidate = projectPerson(project, build_candidate);

              if (project.contractor && project.contractor.askfora_id === build_candidate.askfora_id) {
                if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Updating contractor id to ${build_candidate.id}`);
                project.contractor = build_candidate;
              }
            } else {
              build_candidate.comms = candidate_fora_user ? [candidate_fora_user.email] : build_candidate.comms.filter(c => c && c !== candidate.id && c !== user.email);
              build_candidate.tempId();

              if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'load', user.profile, `${project.id} No match for candidate ${util.format(candidate.comms)} created ${build_candidate.id}`);

              cache.cachePerson(build_candidate);
              save_new.push(build_candidate);
            }

            if (build_candidate) {
              build_candidate.askfora_id = candidate.askfora_id;

              if (build_candidate.id !== candidate.id) {
                if (project.candidates.find(c => c.id === candidate.id)) {
                  if (!project.candidates.find(c => c.id === build_candidate.id)) project.candidates = project.candidates.map(c => c.id === candidate.id ? build_candidate : c);
                } else {
                  if (!project.candidates.find(c => c.id === build_candidate.id)) project.candidates.push(build_candidate);
                }
                delete_ids.push(candidate.id);
              } else project.candidates = project.candidates.map(c => c.id === build_candidate.id ? build_candidate : c);

              if (project.contractor && project.contractor.askfora_id === candidate.askfora_id) {
                if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Updating contractor id to new id ${build_candidate.id}`);
                project.contractor = build_candidate;
                build_candidate.network = false;
              }

              if (candidate.id !== build_candidate.id) delete_ids.push(candidate.id);

              //if (!cache.people_projects[build_candidate.id]) cache.people_projects[build_candidate.id] = [project.id];
              // else cache.people_projects[build_candidate.id].push(project.id);

              const cdiff = diff(candidate, build_candidate);
              const mask_diff = cdiff.filter(c => { 
                switch(c.type) {
                  case 'del': return !c.key || c.key.length === 0 || c.key[0] !== 'self';
                  case 'put': return (!c.key || c.key.length < 3 || !(c.key[0] === 'tags' && c.key[2] == 'start')) &&
                    c.key.value && c.key.value.length;
                  default: return true;
                }
              });
              if(mask_diff.length) {
                resave = true;
              }
            }
          }

          if (delete_ids.length) {
            project.candidates = project.candidates.filter(c => !delete_ids.includes(c.id));
            resave = true;
          }

          if (save_new.length) {
            resave = true;
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'load', user.profile, `${project.id} Saving ${save_new.length} candidates`);
            await data.people.saveAll(user, save_new.map(p => new Person(p)));
          }
        }
      }
    } else {
      // candidate or admin
      if (user.isAuthenticated()) {
        if (!project.candidates || !project.candidates.filter(c => c.askfora_id).map(c => c.askfora_id).includes(user.profile) ) {
          const admin_groups = user.adminGroups();
          if (admin_groups.length) {
            const global_client = await data.users.globalById(project.client.askfora_id)
            if(global_client && global_client.groups && _.intersection(admin_groups, global_client.groups).length) project.admin = true;
          }
        }

        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Loading not client authenticated or guest`);
        if (!project.me_candidate) {
          const me_candidate = project.candidates ? project.candidates.find(c => c.askfora_id === user.id) : null;
          if (me_candidate) project.me_candidate = projectPerson(project, me_candidate);
          else if(!project.admin) {
            project.me_candidate = projectPerson(project, me);
            resave= true;
          }
        } else if (project.me_candidate.id === ANONYMOUS_ID) {
          delete project.me_candidate;
          resave = true;
        }

        if (project.me_candidate) {
          // make sure id is set and client exists as a contact for candidates
          if ( project.me_candidate.askfora_id !== user.profile) {
            project.me_candidate.askfora_id = user.profile;
            resave = true;
          }

          const ready = user.hasAccount(AuthProviders.Stripe) || user.hasAccount(AuthProviders.Wise) || (project.group_settings && project.group_settings.skip_payment);
          if (ready !== project.me_candidate.ready) {
            project.me_candidate.ready = ready;
            resave = true;
          }

          const fee = !user.hasAccount(AuthProviders.Stripe) && user.hasAccount(AuthProviders.Wise) ? SERVICE_FEE : null;
          if (fee !== project.me_candidate.service_fee) {
              project.me_candidate.service_fee = fee;
              resave = true;
          }

          project.me_candidate.comms = [user.email, project.me_candidate.id];
          const proj_candidate = project.candidates.find(c => c.askfora_id === user.profile);

          if (this.cache.user.profile === user.profile) project.me_candidate.id = me.id
          else if (proj_candidate && project.me_candidate.id !== proj_candidate.id) {
            project.me_candidate.id = proj_candidate.id;
            resave = true;
          }

          if (proj_candidate) {
            project.me_candidate.state = proj_candidate.state;
            project.me_candidate.ready = proj_candidate.ready;
            project.me_candidate.answer = proj_candidate.answer;
            project.me_candidate.public = proj_candidate.public;
            project.me_candidate.groups = proj_candidate.groups;
            Object.assign(proj_candidate, project.me_candidate);
            resave = true;
          }

          if (shared.contractor && !project.contractor) project.contractor = shared.contractor;
          if (!shared.contractor && project.contractor) {
            project.contractor = null;
            resave = true;
          }

          if (project.contractor && project.contractor.askfora_id === project.me_candidate.askfora_id) {
            project.contractor = projectPerson(project, project.me_candidate);
            project.contractor.comms = [user.email, project.contractor.id];
            project.contractor.network = false;
            resave = true;
          }
        }
      }

      // resolve client person
      if (user.isAuthenticatedNonGuest() && project.client.askfora_id && 
        project.client.askfora_id !== FORA_PROFILE && (resave || !project.client.id)) {
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadForUser', user.profile, `${project.id} Loading not client not authenticated`);
        const save_client = project.client;
        const client_user = await data.users.globalById(project.client.askfora_id);
        let client_set;

        if (client_user) {
          if (user.profile === this.cache.user.profile) {
            client_set = await this.people.findByComms([client_user.email]);
            if (client_set.length > 1) logging.warnFP(LOG_NAME, 'loadForUser', this.cache.user.profile, `${project.id} Client set for ${client_user.email} ${client_set.length}`);
          }
          else client_set = await data.people.byAttributeComms(user, [client_user.email]);

          if (client_set) {
            const filter_client_set = client_set.filter(c => c.comms && (!c.comms.includes(user.email) || c.comms.includes(client_user.email)) && c.id !== me.id && !c.self);
            if (user.profile === this.cache.user.profile && filter_client_set.length < client_set.length) {
              logging.warnFP(LOG_NAME, 'loadForUser', this.cache.user.profile, `${project.id} Filter client set for ${project.client.askfora_id} from ${client_set.length} to ${filter_client_set.length} ${JSON.stringify(client_set.map(c => c.id))} ::: ${JSON.stringify(filter_client_set.map(c => c.id))}`);
              client_set = filter_client_set;
            }
          }

          if (!client_set || !client_set.length) {
            const client_vanity = await data.users.vanity(client_user.vanity);
            let connect_client = client_vanity ? peopleUtils.personFromVanity(client_vanity) : project.client;
            if (user.profile === this.cache.user.profile) {
              logging.warnFP(LOG_NAME, 'loadForUser', this.cache.user.profile, `${project.id} Creating client ${project.client.askfora_id} ${project.client.id} ${JSON.stringify(project.client.comms)}`);
              project.client = projectPerson(project, await this.people.temp(connect_client), project.client);
            }
          } else {
            for (const c of client_set) {
              if (project.client) {
                peopleUtils.mergePeople(project.client, c, true);
                if (!project.client.id) project.client.id = c.id;
              }
              else project.client = c;
            }

            const new_client = new Person(project.client);
            new_client.comms = new_client.comms.filter(c => c !== user.email);

            if (user_project || project.admin) {
              project.client = await data.people.save(user, new_client);
              // resave = true;
            } else project.client = new_client;
          }
          project.client = projectPerson(project, project.client, save_client);
        } else {
          // user may have deleted their account, just ignore
          logging.warnFP(LOG_NAME, 'loadForUser', this.cache.user.profile, `${project.id} no client found ${project.client.askfora_id} loading for ${user.profile}`);
        }
      } else {
        if (!project.client.askfora_id) logging.warnFP(LOG_NAME, 'loadForUser', this.cache.user.profile, `${project.id} client ${project.client.id} with no askfora_id creating for user ${user.id}`);
        project.client = projectPerson(project, project.client);
        project.client.self = false;
      }
    }

    this.checkProject(project, false, cache);

    if (user_project && resave && !saving) await this.createForUser(user, me, project, false);

    return project;
  }

  async repeat(project: Project, group?: Group): Promise<Project> {
    const as_client = !project.contractor || project.contractor.askfora_id !== this.cache.user.profile || project.client.self;

    let new_project;

    if (as_client) {
      const client = new Person(this.cache.me);
      client.self = true;
      client.askfora_id = this.cache.user.profile;
      const client_email = this.cache.user.email ? [this.cache.user.email] : parsers.findEmail(client.comms);
      client.comms = client_email ? client_email.concat(client.id) : [client.id];
      slimEntity(client);
      new_project = new Project({ client });
      logging.infoFP(LOG_NAME, 'repeat', this.cache.user.profile, `Repeating ${project.id} as client`);
    } else {
      new_project = new Project({ client: project.client, proposal: true });
      logging.infoFP(LOG_NAME, 'repeat', this.cache.user.profile, `Repeating ${project.id} as contractor`);
    }

    delete new_project.last_update;

    this.copyProject(new_project, project);
    this.mapGroupSettings(new_project, group, Object.values(this.cache.user.loaded_groups));

    delete new_project.id;
    delete new_project.archived;
    delete new_project.completed;
    delete new_project.escrow;
    delete new_project.payment;
    delete new_project.transfer;
    delete new_project.refund;
    delete new_project.accepted;
    delete new_project.declined;
    delete new_project.viewed;
    delete new_project.select_notice;

    new_project.proposal = !as_client;

    new_project.title = `Copy of ${new_project.title}`;

    const discount_fee = new_project.payment && new_project.service_fee === SERVICE_FEE ? DISCOUNT_FEE : new_project.service_fee;

    const service_fee = new_project.contractor && new_project.contractor.service_fee ?  new_project.contractor.service_fee : discount_fee;

    new_project.service_fee = service_fee;
    new_project.progress = 0;
    new_project.start = getStartDate(new_project.duration, new_project.rate);
    new_project.end = getEndDate(new_project.duration, new_project.rate, new Date(new_project.start));
    const candidates = [];

    for (const pcandidate of new_project.candidates) {
      const candidate = projectPerson(new_project, pcandidate);
      if (as_client || candidate.askfora_id === this.cache.user.profile) candidates.push(candidate);
      if (as_client || !candidate.askfora_id || candidate.askfora_id !== this.cache.user.profile) { 
        candidate.state = as_client ? ProjectCandidateState.INVITED : ProjectCandidateState.SELECTED;
      } else if(candidate.askfora_id === this.cache.user.profile) candidate.state = ProjectCandidateState.SELECTED;
    }
    new_project.candidates = candidates;

    if (as_client) {
      // move contractor to candidate
      if (new_project.contractor) {
        new_project.contractor.network = false;
        new_project.contractor.state = ProjectCandidateState.INVITED;
        new_project.candidates = [new_project.contractor];
        new_project.profile = true;
      }
      delete new_project.contractor;
      delete new_project.contract;
      delete new_project.select_notice;
    } else {
      new_project.contractor = new_project.candidates.find(c => c.state == ProjectCandidateState.SELECTED);
      new_project.contractor.self = true;
      new_project.proposal = true;
    }

    new_project.last_update = new Date();
    new_project.last_activty = new Date();

    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'repeat', this.cache.user.profile, `setup complete ${JSON.stringify(new_project)}`);

    if (new_project.proposal) {
      new_project = await this.create(new_project, false);
      await this.updateShared(new_project);
    } else new_project = await this.create(new_project);

    return new_project;
  }

  copyProject(project_to: Project, project_from: Project) {
    this.updateProject(this.cache.user, project_to, project_from, this.cache, false);
    this.projectRole(project_to);
  }

  async copyTemplate(template: Project): Promise<Project> {
    const project = new Project({ client: this.cache.me });
    this.copyProject(project, template);

    project.start = getStartDate(project.duration, project.rate);
    project.end = getEndDate(project.duration, project.rate, new Date(project.start));

    project.client = projectPerson(project, this.cache.me);
    project.client.comms = [this.cache.me.id, this.cache.user.email];
    project.client.self = true;
    project.client.askfora_id = this.cache.user.profile;
    this.projectRole(project);

    if (project.contractor) {
      project.contractor.state = ProjectCandidateState.FOUND;
      project.candidates = [projectPerson(project, project.contractor)];
      delete project.contractor.id;
      delete project.contractor;
    }

    if (project.candidates) project.candidates.forEach(c => {
      c.id = null;
      c.state = ProjectCandidateState.FOUND
    });

    await data.projects.create(this.cache.user, project);

    if (!project.contractor || project.contractor.askfora_id !== this.cache.user.profile) delete this.cache.projects[template.id];
    this.cache.projects[project.id] = project;
    return project;
  }

  updateProject(user: ForaUser, project: Project, shared: Project, cache: DataCache, update_state: boolean): boolean {
    let unselect_id: Uid = null;
    let update_from_shared = false;

    if (shared.contractor) {
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Checking shared contractor`);
      // if proj has a contractor that's not selected, don't save
      if (project.contractor && project.contractor.state === ProjectCandidateState.ACCEPTED) {
        unselect_id = project.contractor.id;
        logging.infoFP(LOG_NAME, 'updateProject', user.profile, `Unselecting shared contractor ${unselect_id} for ${project.id}`);
        project.contractor = null;
        project.contract = null;
        shared.contract = null;
        shared.contractor = null;
      }

      if (shared.contractor.askfora_id !== user.profile && shared.archived) {
        project.archived = true;
        update_from_shared = true;
      }
    }

    if (shared.contract && (!project.contract || !project.contract.length)) {
      project.contract = shared.contract;
      update_from_shared = true;
    }
    if ((!project.escrow || project.escrow instanceof Array) && shared.escrow) {
      project.escrow = shared.escrow;
      update_from_shared = true;
    }
    else if ((!project.escrow || project.escrow instanceof Array || !project.escrow.charges) && (shared.escrow && shared.escrow.charges)) {
      project.escrow = shared.escrow;
      update_from_shared = true;
    }
    if ((!project.payment || project.payment instanceof Array) && (shared.payment && shared.payment.id)) {
      project.payment = shared.payment;
      update_from_shared = true;
    }
    if ((!project.refund || project.refund instanceof Array) && (shared.refund && shared.refund.id)) {
      project.refund = shared.refund;
      update_from_shared = true;
    }

    if (project.proposal) {
      const accepted = project.accepted || shared.accepted || !!project.escrow;
      const viewed = project.viewed || shared.viewed;
      const declined = (project.declined || shared.declined) && !project.escrow;

      if (accepted !== project.accepted) {
        project.accepted = accepted;
        update_from_shared = true;
      }

      if(viewed !== project.viewed) {
        project.viewed = viewed;
        update_from_shared = true;
      }

      if (declined !== project.declined) {
        project.declined = declined;
        update_from_shared = true;
      }
    }

    project.profile = shared.profile === true || project.profile === true;

    if (!project.last_update || new Date(shared.last_update).getTime() > new Date(project.last_update).getTime()) {
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Updating from newer shared`);

      if ('start' in shared) shared.start = new Date(shared.start);
      if ('end' in shared) shared.end = new Date(shared.end);

      for (const field of PROJECT_UPDATE_FIELDS) {
        if (project[field] !== shared[field] && (shared[field] !== undefined || !PROJECT_REQUIRED_FIELDS.includes(field))) {
          project[field] = shared[field];
        }
      }

      if (!project.rate) project.rate = ProjectRate.hourly;
      if (!project.duration || isNaN(project.duration) || project.duration > 100000)  project.duration = 1;
      if (!project.start || !new Date(project.start).getTime()) project.start = getStartDate(project.duration, project.rate);
      else project.start = new Date(project.start);
      if (!project.end || !new Date(project.end).getTime()) project.end = getEndDate(project.duration, project.rate, new Date(project.start));
      else project.end = new Date(project.end);

      if (!project.completed && !shared.completed) project.archived = project.archived || shared.archived;

      if (project.client && shared.client && project.client.askfora_id !== shared.client.askfora_id && shared.client.askfora_id) {
        project.client = new Person(shared.client);
        project.client.tempId();
      }

      if (project.contractor && !update_state && !shared.contractor && !shared.candidates.find(p => p.state === ProjectCandidateState.SELECTED)) {
        if (!unselect_id) unselect_id = project.contractor.id;
        project.contractor = null;
        logging.infoFP(LOG_NAME, 'updateProject', user.profile, `Unselecting contractor ${unselect_id} of ${shared.id}`);
      }

      project.searched_skills = shared.searched_skills ? shared.searched_skills : project.searched_skills;

      project.last_update = new Date(shared.last_update);

      update_from_shared = true;
    }

    if (!project.suggested || !project.suggested.length) project.suggested = shared.suggested;

    // make sure candidates are latest
    if (shared.candidates && shared.candidates.length) {
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Shared candidates ${shared.candidates.length}`);
      if (project.candidates && project.candidates.length) {
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Project candidates ${shared.candidates.length}`);

        // make sure we keep all proj candidates not in shared
        const proj_candidate_ids: { [key: string]: { index: number; candidate: Partial<Person> } } = {};
        const proj_candidate_id_index: { [key: string]: { index: number; candidate: Partial<Person> } } = {};
        const proj_candidate_comm_index: { [key: string]: { index: number; candidate: Partial<Person> } } = {};
        for (let index = 0;index < project.candidates.length;index++) {
          const candidate = project.candidates[index];
          if (candidate.askfora_id) proj_candidate_id_index[candidate.askfora_id] = { index, candidate };
          for (const comm of candidate.comms) {
            proj_candidate_comm_index[comm] = { index, candidate };
          }

          if (candidate.id) {
            proj_candidate_ids[candidate.id] = { index, candidate };
            if (candidate.id === unselect_id) {
              if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Unselecting candidate ${unselect_id}`);
              candidate.state = ProjectCandidateState.ACCEPTED;
            }
          }
        }

        const candidates: Partial<Person>[] = [];
        const added_ids: Uid[] = [];
        const added_askfora_ids: Uid[] = [];
        for (const shared_candidate of shared.candidates) {
          // let updated = false;
          let candidate = null;
          const proj_candidate = proj_candidate_ids[shared_candidate.id]
          if (proj_candidate) {
            candidate = projectPerson(project, proj_candidate.candidate);
            delete proj_candidate_ids[shared_candidate.id];
            if (proj_candidate.candidate.askfora_id) delete proj_candidate_id_index[proj_candidate.candidate.askfora_id];
          }
          else {
            let cid = proj_candidate_id_index[shared_candidate.askfora_id];
            if (!cid) {
              const email = parsers.findEmail(shared_candidate.comms);
              if (email) {
                for (const comm of email) {
                  cid = proj_candidate_comm_index[comm];
                  if (cid) break;
                }
              }
            }

            if (cid) {
              candidate = projectPerson(project, cid.candidate);
              delete proj_candidate_id_index[shared_candidate.askfora_id];
              if (shared_candidate.id) delete proj_candidate_ids[shared_candidate.id];
            }
          }

          if (candidate) {
            const candidate_diff = diff(candidate, shared_candidate);
            if (candidate_diff.filter(d => (d.type !== 'del' || d.key.includes('groups') || d.key.includes('answer')) && !d.key.includes('self') && !d.key.includes('id')).length) {
              if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Updating shared candidate ${candidate.id} ${candidate.askfora_id} from ${shared_candidate.id} ${shared_candidate.askfora_id} because ${JSON.stringify(candidate_diff)}`);

              const candidate_id = candidate.id;
              const candidate_state = candidate.state;
              const candidate_askfora_id = candidate.askfora_id;
              const candidate_ready = candidate.ready;
              const candidate_network = candidate.network;
              const candidate_answer = candidate.answer;
              const candidate_refer_by = candidate.refer_by;
              const candidate_public = candidate.public;
              const candidate_groups = candidate.groups;

              Object.assign(candidate, shared_candidate);

              if (!candidate.id || (candidate.askfora_id && candidate.askfora_id === user.profile)) candidate.id = candidate_id;
              if (!candidate.askfora_id) candidate.askfora_id = candidate_askfora_id;
              if (candidate_ready) candidate.ready = candidate_ready;
              if (candidate_network === false) candidate.network = false;
              if (candidate_refer_by) candidate.refer_by = candidate.refer_by ? _.uniqBy(candidate.refer_by.concat(candidate_refer_by), 'vanity') : candidate_refer_by;

              if (!update_from_shared) {
                if (candidate_answer) candidate.answer = candidate_answer;
                if (candidate_public) candidate.public = candidate_public;
                if (candidate_groups) candidate.groups = candidate_groups;
                if (CandidateStateOrder.indexOf(candidate_state) > CandidateStateOrder.indexOf(candidate.state) &&
                  (update_state || new Date(shared.last_update) <= new Date(project.last_update))) {
                  candidate.state = candidate_state;
                }
              }

              if (candidate.askfora_id !== user.profile) delete candidate.self;
            }
          } else {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Adding shared candidate ${shared_candidate.id} and clearing id`);
            candidate = shared_candidate; //projectPerson(project, shared_candidate);
          }

          // reset candidates if not self match
          if (candidate.id === cache.me.id && candidate.askfora_id && candidate.askfora_id !== user.profile) candidate.id = null;

          if ((!candidate.id || !added_ids.includes(candidate.id)) &&
            (!candidate.askfora_id || !added_askfora_ids.includes(candidate.askfora_id))) {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Adding candidate ${candidate.id}`);

            candidates.push(candidate);

            if (candidate.id) {
              added_ids.push(candidate.id);
              delete proj_candidate_ids[candidate.id];
            } else if (candidate.askfora_id === cache.user.profile) {
              candidate.id = cache.me.id;
              if (project.contractor && project.contractor.askfora_id === cache.user.profile) project.contractor.id = cache.me.id;
              added_ids.push(cache.me.id)
              delete proj_candidate_ids[candidate.id];
            }

            if (candidate.askfora_id) {
              added_askfora_ids.push(candidate.askfora_id);
              delete proj_candidate_id_index[candidate.askfora_id];
            }
          }
        }

        const have_ids = candidates.map(c => c.id);
        const have_askfora_ids = candidates.map(c => c.askfora_id);
        const have_comms = flatten(candidates.map(c => c.comms)).filter(e => parsers.findEmail(e));
        for (const missing of [...Object.values(proj_candidate_ids), ...Object.values(proj_candidate_id_index)]) {
          if ((missing.candidate.id && have_ids.includes(missing.candidate.id)) ||
            (missing.candidate.askfora_id && have_askfora_ids.includes(missing.candidate.askfora_id)) ||
            (!missing.candidate.id && _.intersection(have_comms, missing.candidate.comms).length > 0)) {
            logging.warnFP(LOG_NAME, 'updateProject', user.profile, `Updating ${project.id} duplicate id ${missing.candidate.id} askfora_id ${missing.candidate.askfora_id}`);
            continue;
          }
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Adding missing candidate ${missing.candidate.id}`);
          candidates.splice(missing.index, 0, missing.candidate); //projectPerson(project, missing));
          if (missing.candidate.id) have_ids.push(missing.candidate.id);
          if (missing.candidate.askfora_id) have_askfora_ids.push(missing.candidate.askfora_id);
        }

        project.candidates = candidates;

        if (project.contractor) {
          let candidate;
          if (project.contractor.askfora_id) {
            candidate = project.candidates.find(c => c.askfora_id === project.contractor.askfora_id);
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Updating contractor by id ${candidate ? candidate.askfora_id : 'not found'}`);
          } else if (shared.contractor && shared.contractor.askfora_id) {
            candidate = project.candidates.find(c => c.askfora_id === shared.contractor.askfora_id);
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Updating contractor from shared ${candidate ? candidate.askfora_id : 'not found'}`);
          } else {
            candidate = project.candidates.find(c => checkState(c, ProjectCandidateState.SELECTED) >= 0);
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'updateProject', user.profile, `${project.id} Updating contractor from candidate states ${candidate ? candidate.askfora_id : 'not found'}`);
          }

          if (candidate && update_state) candidate.state = project.contractor.state;

          project.contractor = projectPerson(project, candidate);
          project.contractor.network = false;

          if (candidate.askfora_id !== user.profile) delete project.contractor.self;

          if (project.contractor.askfora_id === user.profile) project.contractor.self = true;
          else delete project.contractor.self;

          if (project.client.askfora_id === user.profile) project.client.self = true;
          else delete project.client.self;
        }

      } else {
        if (project.me_candidate) {
          const me_candidate = projectPerson(project, cache.me) as Candidate;
          me_candidate.askfora_id = user.profile;
          const shared_me = shared.candidates.find(c => c.askfora_id === user.profile);
          if (shared_me) {
            me_candidate.state = shared_me.state;
            me_candidate.answer = shared_me.answer;
          }
          project.candidates = shared.candidates.filter(c => c.askfora_id !== user.profile).concat(me_candidate);
        } else project.candidates = shared.candidates;

        if (!project.client) project.client = shared.client;
      }
    } else if (!project.candidates) project.candidates = [];


    if (user.isAuthenticatedNonGuest() && project.client.self && project.client.askfora_id && project.client.askfora_id === user.profile && cache.me) {
      project.client = projectPerson(project, cache.me, project.client);
    }

    this.checkProject(project, false, cache);

    return update_from_shared;
  }

  async updateClient(project: Project, force = false) {
    this.checkProject(project, true);
    const user = new ForaUser(project.client.askfora_id);

    // don't create proposal for a client until they see it
    if (!force && (project.proposal || project.client.askfora_id !== this.cache.user.profile)) {
      const client_project = await data.projects.get(user, project.id);
      if (!client_project) return;
    }
    await this.createForUser(user, project.client, project);
    this.checkProject(project, true);
  }

  mapGroupSettings(project: Project, group_host: Group, groups: Group[]) {
    if (group_host) {
      project.group_settings = {
        group: group_host.id,
        no_referrals: group_host.no_referrals,
        skip_payment: group_host.skip_payment,
        skip_contracting: group_host.skip_contracting,
        search_groups_contacts: group_host.search_groups_contacts ? [group_host.id] : null,
        search_mandatory_tags: group_host.search_mandatory_tags,
        stripe_account: group_host.accounts && group_host.accounts[AuthProviders.Stripe] &&
          Object.values(group_host.accounts[AuthProviders.Stripe]).length ?
          Object.values(group_host.accounts[AuthProviders.Stripe])[0] as string : null,
        service_fee: group_host.service_fee,
        simple_invite: group_host.simple_invite,
        sourcing: group_host.sourcing,
      }
    } else project.group_settings = {};

    if (groups) {
      for (const group of Object.values(groups)) {
        if (group.provider && !project.group_settings.group) {
          for (const field of GROUP_FIELDS) {
            if (!(field in project.group_settings)) project.group_settings[field] = group[field];
          }
        }

        if (group.search_groups_contacts) {
          if (!project.group_settings.search_groups_contacts) project.group_settings.search_groups_contacts = [group.id];
          else if (!project.group_settings.search_groups_contacts.includes(group.id)) project.group_settings.search_groups_contacts.push(group.id)
        }

        if ('service_fee' in group && (!('service_fee' in project.group_settings) || group.service_fee < project.group_settings.service_fee)) {
          project.group_settings.service_fee = group.service_fee;
          project.service_fee = group.service_fee;
        }
      }
    }
  }

  // notify a candidate or client (candidate = null)
  async notify(project: Project, user: ForaUser, candidate: Partial<Person> = null, email: Email = null, options: ProjectNotifyOptions = {}, vars = null) {
    const notify_project = new Project(project);
    const save_client = notify_project.client;

    // this.checkProject(notify_project, true);
    const as_client = user && (
      (notify_project.client && user.profile === notify_project.client.askfora_id) ||
      (project.admin && (!candidate || !candidate.askfora_id || candidate.askfora_id !== user.profile))
    );

    // user without an askfora_id can only be emailed
    let send_alert = !options.when || options.when <= new Date() ? true : false;
    // let do_create = false;
    if (!user || !user.profile) {
      send_alert = false;
      if (candidate) logging.warnFP(LOG_NAME, 'projectNotify', this.cache.user.profile, `Cannot notify on project ${notify_project.id} for candidate ${candidate.id} with no askfora id`);
      else logging.warnFP(LOG_NAME, 'projectNotify', this.cache.user.profile, `Cannot notify on project ${notify_project.id} for client ${project.client.id} with no askfora id`);
    }

    notify_project.client = save_client;

    let webpush: WebPush = null;
    let notify_type: NotifyType = NotifyType.EmailOnly;
    if (user && user.profile && send_alert) {
      const to_email = email && email.rcpts && email.rcpts.length ? email.rcpts[0].Email : null;
      const email_notify = NotifyEmailOnly.includes(/*options.template ? `${options.notification}.${options.template}` :*/ options.notification) ? NotifyType.EmailOnly : NotifyType.PushAndEmail;
      notify_type = to_email ? email_notify : NotifyType.PushOnly;
      const tag = notify_project.id;

      webpush = {
        notification: {
          tag,
          title: project.expert ? lang.project.NOTIFY_UPDATE_EXPERT_TITLE : lang.project.NOTIFY_UPDATE_TITLE,
          body: lang.project.NOTIFY_UPDATE_MESSAGE(options.notification, options.template, notify_project.title,
            notify_project.escrow && notify_project.escrow.id === lang.project.SKIP_ESCROW.id,
            project.client.displayName, project.contractor && project.contractor.displayName ? project.contractor.displayName : 
              project.expert ? 'An expert' : 'A candidate'),
          click_action: this.getUrl(notify_project, to_email, options.message),
        },
      };
    }

    if (options.notification) {
      /* const referrer = !as_client && candidate && (
        (candidate.askfora_id && candidate.askfora_id !== this.cache.user.profile) || 
        (!candidate.askfora_id && candidate.id !== this.cache.me.id)) ? this.cache.me : null;*/
      logging.infoFP(LOG_NAME, 'projectNotify', this.cache.user.profile, `notifying for ${project.id} ${options.notification} ${options.template ? options.template : ''} ${options.when ? options.when : ''}`);
      const variables = this.projectVariables(notify_project, email, as_client, options.group, candidate, options.referrer);
      if (vars) Object.assign(variables, vars);
      if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'projectNotify', this.cache.user.profile, JSON.stringify(variables));
      return notify(user, { type: options.notification, webpush, email, info: [{ id: project.id, type: project.type, refresh: true }], template: options.template, variables, when: options.when }, notify_type, options.group);
    }
  }

  projectRate(project: Project) {
    switch (project.rate) {
      case 'hourly':
      case 'fixed':
        if (project.progress === 1) return 'hour';
        return 'hours';
      case 'daily':
        if (project.progress == 1) return 'day';
        return 'days';
    }
  }

  projectVariables(project: Project, email: Email, as_client: boolean, group: Group, candidate: Partial<Person> = null, referrer: Partial<Person> = null) {
    let sourcing_type = undefined;

    if (project.rate === ProjectRate.sourcing) {
      switch(project.sourcing_type) {
        case ProjectSourcingType.full:
          sourcing_type = 'remote full time';
          break;
        case ProjectSourcingType.part:
          sourcing_type = 'remote part time';
          break;
        case ProjectSourcingType.contract:
          sourcing_type = 'remote contract';
          break;
      }
    }

    const candidate_emails = candidate && candidate.comms ? parsers.findEmail(candidate.comms) : [];
    const candidate_email = candidate_emails  && candidate_emails.length ? candidate_emails[0] : null;

    return {
      profile: project.contractor ? mapURL(`/profile/${as_client ? project.client.vanity : project.contractor.vanity}`, group) : null,
      job: mapURL(`/job/${project.id}`, as_client || !group || !group.email_domain || !candidate_email ||
        (Array.isArray(group.email_domain) ? group.email_domain.find(ed => candidate_email.endsWith(ed)) : candidate_email.endsWith(group.email_domain)) ? group : null),
      job_name: project.title,
      first_name: as_client ?
        (project.client ? project.client.nickName : '') :
        (project.contractor ? project.contractor.nickName : ''),
      number_of_candidates: `${project.candidates && project.candidates.length ? project.candidates.length : 'no'} candidate${project.candidates && project.candidates.length === 1 ? '' : 's'}`,
      client_full_name: project.client ? project.client.displayName : '',
      client_first_name: project.client ? project.client.nickName : '',
      contractor_full_name:
        candidate ? candidate.displayName : project.contractor ? project.contractor.displayName : '',
      contractor_first_name:
        candidate ? candidate.nickName : project.contractor ? project.contractor.nickName : '',
      referrer_full_name: referrer ? referrer.displayName : '',
      referrer_first_name: referrer ? referrer.nickName : '',
      referral: referrer ? 'true' : 'false',
      skills: project.skills,
      duration_length: project.duration,
      duration_unit: this.projectRate(project),
      notes: project.notes ? project.notes : null,
      message: email ? email.message : null,
      time_worked: project.progress,
      fee_earned: `$${(project.fee * (project.rate === ProjectRate.fixed ? 1 : project.progress)).toLocaleString()}`,
      total_fee: `$${(project.fee * (project.rate === ProjectRate.fixed ? 1 : project.duration)).toLocaleString()}`,
      askfora_fee: `$${((isNaN(project.service_fee) ? SERVICE_FEE : project.service_fee) * (project.fee * (project.rate === ProjectRate.fixed ? 1 : project.progress))).toLocaleString()}`,
      start_date: localeDowMonthDay(project.start, this.cache.user.locale, this.cache.user.timeZone),
      feedback_link: `mailto:<EMAIL>?subject=Feedback%20on%20${project.title ? encodeURI(project.title) : ''}%20job`,
      email_header: group ? mapURL(`/api/group/${group.id}/logo/600x200`, group, this.cache.user.profile) : mapURL(`/images/email_header.png`, group, this.cache.user.profile),
      header_url: group && group.url ? group.url : 'https://askfora.com',
      sourcing_type, 
    };
  }

  projectRole(project: Project) {
    this.cache.projectRole(this.cache.user, project, this.cache.me);
  }

  // copy any changes from the client
  receiveProject(project_info: ProjectInfo, update_fields: string[] = [], update_people: Partial<CandidateInfo>[] = []): { project: Project; updated: boolean } {
    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'receiveProject', this.cache.user.profile, `${project_info.id} Receiving project with fields ${JSON.stringify(update_fields)}}`);
    let project = this.cache.projects[project_info.id];
    if (project) {
      project = new Project(project);
      let updated = false;

      if (project.client && project.client.self) {
        if (!project_info.client) throw new Error(`Attempting to be client for project ${project_info.id} without clientt`);
        if (!project.client) throw new Error(`Attempting to be client for project ${project.id} without client in cache`);
        // if (project_info.client.askfora_id !== this.cache.user.profile) throw new Error(`Attempting to be client for project ${project_info.id} with id ${this.cache.user.profile} client is ${project_info.client.id}`);
        if (project.client.askfora_id !== this.cache.user.profile) throw new Error(`Attempting to be client for project ${project.id} with id ${this.cache.user.profile} cache client is ${project.client.id}`);

        // make sure dates are objects
        project.start = new Date(project.start);
        project.end = new Date(project.end);
        if (!project.duration || project.duration == undefined || project.duration === null) project.duration = 1;
        if ('start' in project_info) project_info.start = new Date(project_info.start);
        if ('end' in project_info) project_info.end = new Date(project_info.end);
        if ('duration' in project_info && isNaN(project_info['duration'])) project_info.duration = project.duration;

        if (update_fields) {
          for (const field of update_fields) {
            if (field in project_info && 
              (PROJECT_DATE_FIELDS.includes(field) && project_info[field].getTime() !== project[field].getTime()) ||
              (!PROJECT_DATE_FIELDS.includes(field) && project_info[field] !== project[field])) {
              if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'receiveProject', this.cache.user.profile, `${project_info.id} Project as client setting ${field} from ${project[field]} to ${project_info[field]}`);
              project[field] = project_info[field];
              updated = true;
            }
          }
        }

        if (project_info.candidates && project.candidates) {
          // don't update the cached candidate list in place
          const candidates = [];
          for (const pc of project.candidates) {
            const new_candidate = projectPerson(project, pc) as Candidate;
            candidates.push(new_candidate);
            let update_candidate = update_people.find(c => c.id === new_candidate.id);
            // if (!update_candidate) update_candidate = project_info.candidates.find(c => c.id === new_candidate.id);
            if (update_candidate) {
              if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'receiveProject', this.cache.user.profile, `${project_info.id} Project as client setting candidate ${new_candidate.id} state from ${new_candidate.state} to ${update_candidate.state}`);
              updated = (new_candidate.state !== update_candidate.state || update_candidate.answer !== undefined || update_candidate.refer_by !== undefined)
              new_candidate.state = update_candidate.state;
              new_candidate.answer = update_candidate.answer;
              new_candidate.refer_by = update_candidate.refer_by ?
                new_candidate.refer_by ? _.uniqBy(new_candidate.refer_by.concat(update_candidate.refer_by.map(r => new Person({ id: r.id, vanity: r.vanity }))), 'vanity') :
                  update_candidate.refer_by.map(r => new Person({ id: r.id, vanity: r.vanity })) : null;
              //break;
            }
          }

          project.candidates = candidates;
        }

        // contract is only set by the server
        // make sure we have valid start and end dates
        if (!project.start || !new Date(project.start).getTime()) {
          project.start = getStartDate(project.duration, project.rate, project.end && new Date(project.end).getTime() ? new Date(project.end) : null);
          if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'receiveProject', this.cache.user.profile, `${project_info.id} Project as client setting empty start to ${project.start}`);
          updated = true;
        }

        // make sure we send valid start and end back to the user
        if (!project.end || !new Date(project.end).getTime() || new Date(project.end) < new Date(project.start)) {
          project.end = getEndDate(project.duration, project.rate, new Date(project.start));
          if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'receiveProject', this.cache.user.profile, `${project_info.id} Project as client setting empty end to ${project.end}`);
          updated = true;
        }
      } else if (update_fields) {
        if (update_fields.includes('archived') && 'archived' in project_info && project.archived !== project_info.archived) {
          if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'receiveProject', this.cache.user.profile, `${project_info.id} Project as contractor setting archived from ${project.archived} to ${project_info.archived}`);
          project.archived = project_info.archived;
          updated = true;
        }

        if (update_fields.includes('progress') && 'progress' in project_info && project.progress !== project_info.progress) {
          if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'receiveProject', this.cache.user.profile, `${project_info.id} Project as contractor setting progress from ${project.progress} to ${project_info.progress}`);
          project.progress = project_info.progress;
          updated = true;
        }
      }

      if (updated) project.last_update = new Date(project_info.update_date);

      return { project, updated };
    }
  }

  async saveCandidates(project: Project, people: Partial<Person>[], state?: ProjectCandidateState, priority?: Uid[], limit = MAX_CANDIDATES): Promise<Partial<Person>[]> {
    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'saveCandidates', this.cache.user.profile, `adding ${util.format(people)}`);

    // let added_people: Uid[] = [];
    people = people.filter(p => (!this.cache.me || p.id !== this.cache.me.id) && p.comms);
    for (const person of people) {
      person.comms = parsers.findEmail(person.comms);
      if (person.id) person.comms.push(person.id);
    }

    let new_candidates: Partial<Candidate>[] = [];
    if (!project.candidates) {
      new_candidates = people.map(p => projectPerson(project, p));
      project.candidates = new_candidates;
    } else {
      const have_ids = project.candidates.map(c => c.id);
      const have_comms = flatten(project.candidates.map(c => c.comms));
      if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'saveCandidates', this.cache.user.profile, `have ids ${util.format(have_ids)}`);
      for (const person of people) {
        let person_has_comms = false;
        for (const comm of person.comms) {
          if (have_comms.includes(comm)) {
            person_has_comms = true;
            break;
          }
        }
        if (!person_has_comms && !have_ids.includes(person.id)) {
          const new_candidate = projectPerson(project, person);
          new_candidates.push(new_candidate);
          project.candidates.push(new_candidate);
          have_ids.push(person.id);
        }
      }
    }

    if (!project.client) {
      project.client = new Person(this.cache.me);
      project.client.comms = [this.cache.me.id, this.cache.user.email];
      project.client.self = true;
      project.client.askfora_id = this.cache.user.profile;
      slimEntity(project.client);
    }

    if ((!project.skills || project.skills.length === 0) && people.length === 1) {
      project.skills = people[0].displayName;
      if (!project.title) project.title = `Job with ${people[0].displayName}`;
    } else {
      const searched_skills = project.searched_skills ? [...ADD_SKILLS, ...project.searched_skills] : ADD_SKILLS.slice();
      // project.candidates = await peopleUtils.skillMatch(project.candidates, [...project.skill_set, ...searched_skills]);
         peopleUtils.sortBySkills(project.candidates, project.skills.toLowerCase().split(' '), searched_skills, true, priority, project.id, this.cache.user.profile);
      if (!config.isEnvOffline() && limit > 0) {
        project.candidates = project.candidates.filter(c => !c.network).slice(0, limit).
          concat(project.expert ? [] : project.candidates.filter(c => c.network).slice(0, limit));
      }
    }

    const final_candidate_ids = project.candidates.map(p => p.id);
    new_candidates = new_candidates.filter(c => final_candidate_ids.includes(c.id));

    for (const person of new_candidates) {
      if (state) person.state = state;
      else if (!person.state) person.state = ProjectCandidateState.FOUND;
      person.comms = parsers.findEmail(person.comms);
      const users = await data.users.globalByEmail(person.comms);
      if (users && users.length) {
        person.askfora_id = users[0].profile;
        person.comms = [users[0].email];
        person.vanity = users[0].vanity;
      }

      if (person.id) person.comms.push(person.id);

      if (person.network && !person.related_ids) {
        const email = parsers.findEmail(person.comms);
        const connections = email && email.length ? await data.people.connectionsByAttributeComms(this.cache.user, email) : null;
        if (connections && connections.length) {
          const related = _.uniq(connections.filter(p => p && p.id && p.id !== person.id && p.vanity !== person.vanity).map(p => p.id));
          person.related_ids = related;
        }
      }
    }

    if (new_candidates.length) {
      project.last_update = new Date();
      project.last_activity = project.last_update;
    }

    return new_candidates; //project.candidates.filter(c => added_people.includes(c.id));
  }

  async resolveCandidates(project: Project, for_user: ForaUser = null): Promise<Partial<Person>[]> {
    const resolved_candidates: Partial<Candidate>[] = [];
    if (project.candidates) {
      for (const candidate of project.candidates) {
        let match: Partial<Person>[] = null;
        if (candidate.id) match = await this.people.byId(candidate.id);
        if (!match || !match.length) match = (await this.people.findByComms(candidate.comms)).filter(p => !p.self);
        if (!match || !match.length) {
          const new_candidate = await this.createProjectPerson(candidate);
          logging.warnFP(LOG_NAME, 'resolveCandidates', this.cache.user.profile, `Creating candidate ${new_candidate.id} ${JSON.stringify(new_candidate.comms)} because of ${project.id}`);
          match = [new_candidate];
        }

        if (match && match.length) {
          let person = null;
          for (const p of match) {
            if (person) peopleUtils.mergePeople(person, p, true);
            else person = p;
          }
          resolved_candidates.push(projectPerson(project, person, candidate));
        }
      }
    }

    project.candidates = [];

    // make sure project is up to date and saved
    const new_candidates = await this.saveCandidates(project, resolved_candidates);
    try { await this.get(project); } catch (e) {
      logging.errorFP(LOG_NAME, 'resolveCandidates', this.cache.user.profile, `Invalid project ${project.id}`, e);
      return [];
    }

    if (project.contractor) {
      const selected = new_candidates.find(c => c.state === ProjectCandidateState.SELECTED);
      if (selected) {
        logging.debugFP(LOG_NAME, 'resolveCandidates', this.cache.user.profile, `Updating contractor to ${selected.id}`);
        project.contractor = selected;
        project.contractor.network = false;
      }
    }

    this.checkProject(project, true);

    await this.create(project);
    logging.infoFP(LOG_NAME, 'resolveCandidates', this.cache.user.profile, `Saved candidates for project ${project.id}`);

    return new_candidates; //resolved_candidates;
  }

  async resolveClient(project: Project, for_user: ForaUser = null): Promise<Partial<Person>> {
    this.checkProject(project, false);

    if (project.client.askfora_id !== FORA_PROFILE) {
      let match: Partial<Person> = null;
      if (project.client.id) [match] = await this.people.byId(project.client.id);
      if (!match) [match] = await this.people.findByComms(project.client.comms);
      if (!match) {
        logging.warnFP(LOG_NAME, 'resolveClient', this.cache.user.profile, `Creating client ${project.client.id} ${JSON.stringify(project.client.comms)} because of ${project.id}`);
        match = await this.createProjectPerson(project.client);
      }
      else if (match.network) match = await this.createProjectPerson(match); // project.client);

      project.client = projectPerson(project, match, project.client);

      this.checkProject(project, false);
      logging.infoFP(LOG_NAME, 'resolveClient', this.cache.user.profile, `Saved client for project ${project.id}`);
      return match;
    }

    return project.client;
  }

  async resolveSkills(project: Project) {
    let skill_set = project.skill_set;
    if (!skill_set || !skill_set.length) skill_set = project.skills ? project.skills.split(' ').map(t => t.toLowerCase()).filter(t => t && t.length && !parsers.ignore(t, false, false)) : [];

    if (!project.skill_set) project.skill_set = skill_set;

    if (!project.searched_skills) project.searched_skills = skill_set.slice();

    const found_skills = await lookupSkill(skill_set, this.cache.user.locale);

    for (const skill of found_skills) {
      if (!project.searched_skills.includes(skill.skill) &&
        !parsers.ignore(skill.skill)) project.searched_skills.push(skill.skill);

      if (skill.synonyms) {
        for (const synonym of skill.synonyms) {
          if (!project.searched_skills.includes(synonym) &&
            !parsers.ignore(synonym)) project.searched_skills.push(synonym);
        }
      }

      if (skill.initialisms) {
        for (const initials of skill.initialisms) {
          if (!project.searched_skills.includes(initials) &&
            !parsers.ignore(initials)) project.searched_skills.push(initials);
        }
      }
    }

    // grab url skills if sourcing
    let sp:string[] = [];
    let ns:string[]= [];
    let rs:string[] = [];
    let ds:string[] = [];
    let bs:string[] = [];

    if (project.sourcing_url) {
      const learn = new Learn(this.cache.user, undefined, 0);
      const lp = await learn.learnLink(project.sourcing_url, true);
      sp = lp ? lp.filter(t => t.type === TagType.skill).map(s => s.value) : [];
      const title = lp ? lp.find(t => t.type === TagType.title) : null;
      if (title) {
        if (!project.title || project.title === 'sourcing') project.title = title.value;
        if (!project.skills || !project.skills.length || project.skills === 'sourcing') {
          const lower_names = this.cache.me && this.cache.me.names ? this.cache.me.names.map(n => n.toLowerCase()) : this.cache.user.name.toLowerCase().split(' ');
          const jobs = this.cache.me.jobTags.map(j => j.value.toLowerCase());
          const ignore = _.uniq([...lower_names, ...jobs]);
          project.skill_set = parsers.findMeaning(title.value).filter(s => !parsers.ignore(s, false, false, ignore));
          project.skills = project.skill_set.join(' ');
        }
      }
    }

    if (project.notes && project.notes.length) ns = parsers.findMeaning(project.notes);
    if (project.deliverables && project.deliverables.length) ds = parsers.findMeaning(project.deliverables);
    if (project.requirements && project.requirements.length) rs = parsers.findMeaning(project.requirements);
    if (project.background && project.background.length) bs = parsers.findMeaning(project.background);

    project.searched_skills = _.uniq([...project.searched_skills, ...sp, ...ns, ...rs, ...ds, ...bs]).filter(s => !parsers.ignore(s.toLowerCase()));
  }

  async searchCandidates(project: Project, additional_user_ids: Uid[] = []): Promise<Partial<Person>[]> {
    await this.resolveSkills(project);

    const mandatory_tags = project.group_settings && project.group_settings.search_mandatory_tags ?
      [project.group_settings.search_mandatory_tags] : null;

    // Find all people that match skills, optional mandatory tags, and groups
    const s_people: Partial<Person>[] = await this.search.searchSkills([...project.skill_set, ...ADD_SKILLS, ...project.searched_skills], {
      network: project.expert ? 'exclude' : 'include', 
      require_email: true, 
      mandatory_tags, 
      group_search_ids: project.group_settings.search_groups_contacts, 
      additional_user_ids, 
      only_users: project.expert
    });

    if (logging.isDebug(this.cache.user.profile)) logging.debugFP(LOG_NAME, 'searchCandidates', this.cache.user.profile, util.format('unique person ids - %j', s_people.map(({id}) => id)));

    if (s_people) {
      const f_people = s_people.filter(p => (!project.expert || !this.cache.user.groups || Object.keys(this.cache.user.groups) ||
          (p.groups && this.cache.user.groups && Object.keys(this.cache.user.groups) && _.intersection(p.groups, Object.keys(this.cache.user.groups)).length)
        )
      );

      const my_matches = f_people.filter(p => !p.network && p.id);
      const my_match_comms = my_matches.reduce((a, b) => a.concat(b.comms), []);
      const network_matches = f_people.filter(p => p.network && _.intersection(p.comms, my_match_comms).length === 0);

      const new_matches: Person[] = [];
      if (!project.expert) {
        const comm_matches = {};
        const comm_set = flatten(network_matches.filter(n => n.comms).map(n => n.comms));
        const matches = await this.people.findByComms(comm_set);
        for (const match of matches) {
          if (match.comms) {
            for (const comm of match.comms) comm_matches[comm] = match;
          }
        }

        for (const net_match of network_matches) {
          const match = net_match.comms.map(c => comm_matches[c]).filter(c => c);
          // await this.people.findByComms(net_match.comms);
          if (match && match.length) {
            peopleUtils.mergePeople(match[0], net_match, true);
            if (!match[0].id) logging.warnFP(LOG_NAME, 'searchCandidates', this.cache.user.profile, `Trying to add network person without id ${JSON.stringify(match[0])}`);
            else new_matches.push(new Person(match[0]));
          } else {
            const new_person = await this.createProjectPerson(net_match, true);
            if (!new_person.id) logging.warnFP(LOG_NAME, 'searchCandidates', this.cache.user.profile, `Trying to add network person without id ${JSON.stringify(new_person)}`);
            else new_matches.push(new_person);
          }
        }
      }

      if (new_matches.length) await data.people.saveAll(this.cache.user, new_matches);

      const people = [...my_matches, ...new_matches];
      return people;
    }

    return [];
  }

  async saveSearchCandidates(project: Project, people: Partial<Person>[], notify_empty = true, first_run = false): Promise<Project> {
    if (people.length) {
      // const people = await this.people.findById(load_ids);
      logging.infoFP(LOG_NAME, 'saveSearchCandidates', this.cache.user.profile, `Found ${people.length} people`);

      const users = await data.users.globals();
      const user_emails = users.map(u => u.email);
      const user_ids = people.filter(p => p.comms && arrayContains(p.comms, user_emails)).map(p => p.id);

      // make sure project is up to date and saved
      const new_candidates = await this.saveCandidates(project, people, ProjectCandidateState.FOUND, user_ids);
      logging.infoFP(LOG_NAME, 'saveSearchCandidates', this.cache.user.profile, `Saved ${new_candidates.length} new candidates`);
      try { await this.get(project); } catch (e) {
        logging.errorFP(LOG_NAME, 'saveSearchCandidates', this.cache.user.profile, `Invalid project ${project.id}`, e);
        return project;
      }
      this.checkProject(project, true);

      const found = new_candidates ? new_candidates.length : 0;

      // reminder for one day
      if (found > 0) {
        project.client.reminder = {
          when: DAYS(new Date(), 1),
          notification: NotificationType.Project_Reminder,
          template: TemplateType.Select,
        };
      } else if (project.candidates.length === 0) {
        project.client.reminder = {
          when: DAYS(new Date(), 1),
          notification: NotificationType.Project_Reminder,
          template: TemplateType.None,
        };
      }

      project.searching = false;
      project.last_update = new Date();

      await this.create(project);
      // if (this.cache.read_only) await this.cache.refreshCache(EntityType.Project);

      if (notify_empty || found > 0) {
        const to_email = await this.clientEmail(project);

        // if (this.cache.user.id !== project.client.askfora_id) await this.updateClient(project);

        const url = this.getUrl(project, to_email);
        const import_url = this.getImportUrl(project, to_email);

        const subject = found > 0 ?
          lang.project.FOUND_SUBJECT(project) :
          lang.project.NOT_FOUND_SUBJECT(project);

        const message = found > 0 ?
          lang.project.FOUND_MESSAGE(project, found, url, import_url) :
          lang.project.NOT_FOUND_MESSAGE(project, url, import_url);

        const email = {
          rcpts: [{ Name: project.client.displayName, Email: to_email }],
          subject,
          message,
        };

        let template = null;
        let vars = null;
        if (found > 0) {
          if (project.expert) template = TemplateType.Expert;
          else if (first_run) template = TemplateType.First;
        } else {
          if (project.expert) template = TemplateType.NoExpert;
          else {
            const intros = await this.people.findIntroductions(project.searched_skills, 3);
            if (intros && intros.length) {
              const group = this.getProjectGroup(project, this.cache.user.email);
              const url = mapURL('/profile', group);
              vars = { users_found: intros.length > 3 ? 3 : intros.length }
              switch (intros.length) {
                default:
                  vars['intro_link_3'] = `${url}/${intros[2].vanity}/connect`;
                  vars['user_profile_3'] = `${url}/${intros[2].vanity}/embed`;
                // no break
                case 2:
                  vars['intro_link_2'] = `${url}/${intros[1].vanity}/connect`;
                  vars['user_profile_2'] = `${url}/${intros[1].vanity}/embed`;
                // no break
                case 1:
                  vars['intro_link_1'] = `${url}/${intros[0].vanity}/connect`;
                  vars['user_profile_1'] = `${url}/${intros[0].vanity}/embed`;
                // no break
              }
              template = TemplateType.Suggested;
            } else template = TemplateType.None;
          }
        }

        await this.notify(project, this.cache.user, null, email, {
          group: this.cache.getGroup(),
          notification: NotificationType.Project_Found,
          template
        }, vars).catch(e => {
          logging.errorFP(LOG_NAME, 'saveSearchCandidates', this.cache.user.profile, 'error notifying user', e);
          throw e;
        });
      }

      logging.infoFP(LOG_NAME, 'saveSearchCandidates', this.cache.user.profile, `Returning with ${found} candidates`);
    } else {
      // didn't find anyone
      logging.infoFP(LOG_NAME, 'saveSearchCandidates', this.cache.user.profile, `No candidates for project ${project.id}`);
      const to_email = await this.clientEmail(project);
      const email = {
        rcpts: [{ Name: project.client.displayName, Email: to_email }],
        subject: lang.project.NOT_FOUND_SUBJECT(project),
        message: lang.project.NOT_FOUND_MESSAGE(project, this.getUrl(project, to_email), this.getImportUrl(project, to_email)),
      };

      project.searching = false;
      project.last_update = new Date();

      await this.updateClient(project);

      let template;
      let vars;
      if (project.expert) template = TemplateType.NoExpert;
      else {
        const intros = await this.people.findIntroductions(project.searched_skills, 3);
        if (intros && intros.length) {
          const group = this.getProjectGroup(project, this.cache.user.email);
          const url = mapURL('/profile', group);
          vars = { users_found: intros.length > 3 ? 3 : intros.length }
          switch (intros.length) {
            default:
              vars['intro_link_3'] = `${url}/${intros[2].vanity}/connect`;
              vars['user_profile_3'] = `${url}/${intros[2].vanity}/embed`;
            // no break
            case 2:
              vars['intro_link_2'] = `${url}/${intros[1].vanity}/connect`;
              vars['user_profile_2'] = `${url}/${intros[1].vanity}/embed`;
            // no break
            case 1:
              vars['intro_link_1'] = `${url}/${intros[0].vanity}/connect`;
              vars['user_profile_1'] = `${url}/${intros[0].vanity}/embed`;
          }
          template = TemplateType.Suggested;
        } else template = TemplateType.None;
      }

      const user_group = this.cache.getGroup();
      await this.notify(project, this.cache.user, null, email, {
        group: user_group,
        notification: NotificationType.Project_Found,
        template
      }, vars).catch(e => {
        logging.errorFP(LOG_NAME, 'saveSearchCandidates', this.cache.user.profile, 'error notifying user', e);
        throw e;
      });
    }

    logging.infoFP(LOG_NAME, 'saveSearchCandidates', this.cache.user.profile, 'Returning with 0 candidates');
    return project
  }

  async candidateReady(ready = true, service_fee = null): Promise<Project> {
    let active_project: Project;
    if (this.cache.projects) {
      for (const project of Object.values(this.cache.projects)) {
        if (project.candidates) {
          const candidate = project.candidates.find(c => c.askfora_id === this.cache.user.profile);

          try {
            let p = await this.get(project);
            if (p) {
              let updated = false;
              // update candidate and contractor to ready
              if (p.contractor && p.contractor.askfora_id === this.cache.user.profile) {
                updated = updated || ready !== p.contractor.ready;
                p.contractor.ready = ready;
                if (p.contract && this.cache.contracts[p.contract] &&
                  this.cache.contracts[p.contract].contractor_signed &&
                  this.cache.contracts[p.contract].client_signed && !active_project) active_project = p;
              }

              // find updated candidate to set ready
              for (const upcandidate of p.candidates) {
                if (upcandidate.askfora_id === this.cache.user.profile) {
                  updated = updated || ready !== upcandidate.ready;
                  upcandidate.ready = ready;
                  upcandidate.service_fee = service_fee;
                }
              }

              if (updated) {
                p.last_update = new Date();
                p.last_activity = p.last_update;

                p = await this.update(p, false);
                if (!p.client.self) await this.updateShared(p);

                const contract = p.contract ? await this.contracts.load(p.contract) : undefined;

                // check contract state
                await this.updateClient(p);

                if (p.contractor && p.contractor.askfora_id === this.cache.user.profile &&
                  contract && contract.client_signed && contract.contractor_signed && !p.escrow) {
                  // let the client know
                  const person = p.client;
                  const emails = parsers.findEmail(person.comms);
                  const to_email = emails && emails.length ? emails[0] : null;

                  const email = {
                    rcpts: [{ Name: person.displayName, Email: to_email }],
                    subject: lang.project.DEPOSIT_SUBJECT(candidate),
                    message: lang.project.DEPOSIT_MESSAGE(p, candidate, this.getUrl(p, to_email)),
                  };

                  await this.notify(p, new ForaUser(person.askfora_id), null, email, { group: this.cache.groupByEmail(to_email), notification: NotificationType.Project_Ready });
                }
              }
            }
          } catch (e) {
            logging.errorFP(LOG_NAME, 'candidateReady', this.cache.user.profile, `Error updating project ${project.id}`, e);
          }
        }
      }
    }

    return active_project;
  }

  async update(project: Project, update_shared = true): Promise<Project> {
    this.checkProject(project, true);

    if (!project || this.cache.user.profile === project.client.askfora_id || project.client.askfora_id === FORA_PROFILE) return this.create(project ? project : new Project(project), update_shared);
    else {
      if (update_shared) {
        throw new InternalError(500, `Candidate ${this.cache.user.profile} trying to update shared project ${project.id}`, project);
      }
      return this.create(project ? project : new Project(project), false);
    }
  }

  async updateShared(project: Project) {
    const update_project = new Project(project);
    if (!update_project.client.self && !project.admin) {
      const shared: Project = await data.projects.projectById(update_project.id);
      if (shared) update_project.client = shared.client;

      if (update_project.candidates) {
        update_project.candidates = update_project.candidates.map(c => projectPerson(update_project, c));
        const shared_candidate = shared && shared.candidates ? shared.candidates.find(c => c.askfora_id === this.cache.user.profile) : null;
        const update_candidate = update_project.candidates.find(c => c.askfora_id === this.cache.user.profile || c.comms.includes(this.cache.user.email));
        if (!update_candidate) throw new Error(`Error updating shared project ${update_project.id} because of missing candidate ${this.cache.user.profile} ${this.cache.user.email}`);

        if (!shared_candidate) {
          update_candidate.comms = update_candidate.comms.filter(c => c !== update_candidate.id);
          update_candidate.id = null;
          update_candidate.askfora_id = this.cache.user.profile;

          if (update_project.contractor && update_project.contractor.askfora_id === this.cache.user.profile) {
            update_project.contractor = projectPerson(update_project, update_candidate);
            slimEntity(update_project.contractor);
            if (update_candidate.id && !update_project.contractor.comms.includes(update_candidate.id)) update_project.contractor.comms.push(update_candidate.id);
          }
        } else {
          update_candidate.id = shared_candidate.id;
          update_candidate.comms = shared_candidate.comms;

          if (update_project.contractor && update_project.contractor.askfora_id === shared_candidate.askfora_id) {
            update_project.contractor = projectPerson(update_project, update_candidate);
            slimEntity(update_project.contractor);
            if (update_candidate.id && !update_project.contractor.comms.includes(update_candidate.id)) update_project.contractor.comms.push(update_candidate.id);
          }
        }

        if (update_project.contractor) delete update_project.contractor.self;
        for (const candidate of update_project.candidates) delete candidate.self;
      }
    }
    this.checkProject(update_project, false);
    await data.projects.saveShared(update_project);
  }

  async setupContract(project: Project): Promise<Partial<Contract>> {
    const now = new Date();
    let client_reviewed = false;
    let contractor_reviewed = false;
    // set a no contract flag if the client is a member of a group that has skip_contracting set
    if (project.group_settings && project.group_settings.skip_contracting) {
      project.contract = lang.project.SKIP_CONTRACT;
      project.last_update = now;
      project.last_activity = project.last_update;
      client_reviewed = true;
      contractor_reviewed = true;
    }

    if (project.group_settings && project.group_settings.skip_payment || project.fee === 0) {
      project.contractor.ready = true;
      project.escrow = lang.project.SKIP_ESCROW;
      project.last_update = now;
      project.last_activity = project.last_update;
    }

    if (project.contract && project.contract !== lang.project.SKIP_CONTRACT) {
      if (!this.cache.contracts[project.contract]) logging.warnFP(LOG_NAME, 'setupContract', this.cache.user.profile, `Project ${project.id} missing contract ${project.contract} from cache`);
      return this.cache.contracts[project.contract];
    }

    if (!project.contract) {
      // try to match contract with the primary comms, otherwise restore all comms
      for (const index in this.cache.contracts) {
        const contract = this.cache.contracts[index];
        if (project.contractor.comms.includes(contract.contractor_email) && !contract.client_declined && !contract.contractor_declined) {
          // make sure contract is saved
          if (project.group_settings && 'service_fee' in project.group_settings) project.service_fee = project.group_settings.service_fee;
          else if ('service_fee' in project.contractor) project.service_fee = project.contractor.service_fee;
          else {
            // second project with the same contractor gets a discount
            for (const proj of Object.values(this.cache.projects)) {
              if (proj.completed && proj.id !== project.id && proj.contract === contract.id) {
                project.service_fee = DISCOUNT_FEE;
                project.last_update = now;
                project.last_activity = project.last_update;
                break;
              }
            }
          }

          project.contract = contract.id;
          return contract;
        }
      }
    }

    const global_user = await data.users.globalById(project.contractor.askfora_id);

    return new Contract({
      id: project.contract,
      client_reviewed,
      contractor_reviewed,
      client_id: project.client.askfora_id,
      client_name: project.client.displayName,
      client_email: this.cache.user.email,
      contractor_id: project.contractor.askfora_id,
      contractor_name: project.contractor.displayName,
      contractor_email: global_user ? global_user.email : parsers.findEmail(project.contractor.comms)[0],
      context: project.skills,
    });
  }

  async notifySelected(project: Project, new_contract: Partial<Contract>) {
    // don't email the contractor until deposit if they're ready to go
    const person = project.contractor;
    const contractor_user = new ForaUser(person.askfora_id);
    await data.users.init(contractor_user, false);
    const emails = contractor_user.email ? [contractor_user.email] : parsers.findEmail(person.comms);
    const to_email = emails && emails.length ? emails[0] : null;
    let subject = lang.project.SELECTED_SUBJECT(project);
    const group_email = this.cache.getGroup(to_email ? this.cache.groupByEmail(to_email) : null);

    // only notify contractor if they have a contract or escrow is taken care of
    // or if an exsiting contract has been reviewed by the client but not signed by the contractor
    // otherwise they'll get notified after contract or escrow
    if ((!project.contractor.ready && !new_contract) ||
      (new_contract && new_contract.client_reviewed && !new_contract.contractor_signed)) {
      const message = lang.project.SELECTED_MESSAGE(project, person, stripe.stripeConnectUrl(group_email),
        new_contract && new_contract.client_reviewed);

      const notify_email = {
        rcpts: [{ Name: person.displayName, Email: to_email }],
        subject,
        message,
      };

      await this.notify(project, new ForaUser(person.askfora_id), person, notify_email, { group: this.cache.groupByEmail(to_email), notification: NotificationType.Project_Selected });
    } else if (project.escrow) {
      // group may not require payment
      const message = lang.project.SELECTED_MESSAGE(project, person, this.getUrl(project, to_email), new_contract !== undefined);

      const notify_email = {
        rcpts: [{ Name: person.displayName, Email: to_email }],
        subject,
        message,
      };

      await this.notify(project, new ForaUser(person.askfora_id), person, notify_email, { group: this.cache.groupByEmail(to_email), notification: NotificationType.Project_Selected, template: TemplateType.Ready });

      // create an archived project and notify every candidate who has looked at the job that isn't the contractor
      await this.archiveForCandidates(project);
    } else if(project.proposal) {
      const message = lang.project.ACCEPT_PROPOSAL_MESSAGE(project, person, stripe.stripeConnectUrl(group_email));
      subject = lang.project.ACCEPT_PROPOSAL_SUBJECT(project);

      const notify_email = {
        rcpts: [{ Name: person.displayName, Email: to_email }],
        subject,
        message,
      };

      await this.notify(project, new ForaUser(person.askfora_id), person, notify_email, { group: this.cache.groupByEmail(to_email), notification: NotificationType.Project_Proposal, template: TemplateType.Accept });
    }
  }

  referCandidate(project: Project, candidate: Candidate): Promise<Referral> {
    return data.projects.saveReferral(this.cache.user, project.id, projectPerson(project, candidate));
  }

  projectReferrals(project: Project): Promise<Referral[]> {
    return data.projects.findReferral(this.cache.user, project.id);
  }

  myReferrals(): Promise<Referral[]> {
    return data.projects.findReferral(this.cache.user);
  }
}
