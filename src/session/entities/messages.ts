import data from '../../data';
import * as itemTypes from '../../types/items';
import logging from '../../utils/logging';
import DataCache from '../data_cache';

const LOG_NAME = 'data.entities.messages';

type Message = itemTypes.Message;
type Person = itemTypes.Person;

export default class Messages {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  _removeFromCache(message: Message) {
    const len_before = this.cache.messages.length;
    this.cache.messages = this.cache.messages.filter(m => m.id !== message.id);
    if (this.cache.messages.length !== len_before && !this.cache.read_only) this.cache.cacheDirty('messages');

    /*for (const index in this.cache.people_messages) {
      const pm = this.cache.people_messages[index];
      this.cache.people_messages[index] = pm.filter(id => id !== message.id);
      if (pm.length !== this.cache.people_messages[index].length && !this.cache.read_only) this.cache.cacheDirty('people_messages');
    }*/
  }

  async archive(message: Message) {
    await data.messages.archive(this.cache.user, message);
    this._removeFromCache(message);
  }

  async create(sender: Person, _rcpt: Partial<Person> | Partial<Person>[], subject: string, message: string, cc: Person[] = []): Promise<Message> {
    let rcpt = _rcpt;
    if (!(rcpt instanceof Array)) rcpt = [rcpt];

    const draft = await data.messages.draft(this.cache.user, rcpt, sender, subject, message, cc);

    this.cache.messages.push(draft);

    /*for (const person of draft.recipient) {
      if (!(person.id in this.cache.people_messages)) this.cache.people_messages[person.id] = [draft.id];
      else this.cache.people_messages[person.id].push(draft.id);
    }*/

    this.cache.cacheDirty(['messages', /*'people_messages'*/]);

    return draft;
  }

  async read(message: Message) {
    await data.messages.read(this.cache.user, message);
    for (const index in this.cache.messages) {
      if (this.cache.messages[index].id === message.id) {
        this.cache.messages[index] = message;
        this.cache.cacheDirty('messages');
        break;
      }
    }
  }

  async reload(message: Message): Promise<Message> {
    let rmessage = null;
    try { rmessage = await data.messages.reload(this.cache.user, message); } 
    catch (e) { logging.errorFP(LOG_NAME, 'reload', this.cache.user.profile, `Error reloading message ${message.id} from source`, e); }

    if (!rmessage) this._removeFromCache(message);
    return rmessage;
  }

  async send(draft: itemTypes.Message): Promise<Message> {
    /*for (const person of draft.recipient) {
      if (!(person.id in this.cache.people_messages)) this.cache.people_messages[person.id] = [draft.id];
      else if (!this.cache.people_messages[person.id].includes(draft.id)) this.cache.people_messages[person.id].push(draft.id);
    }*/
    this.cache.cacheDirty(['messages', /*'people_messages'*/]);
    return data.messages.send(this.cache.user, draft);
  }
}
