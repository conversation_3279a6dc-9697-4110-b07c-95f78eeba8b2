import { v4 as uuid } from 'uuid';

import data from '../../data';
import DataCache from '../data_cache';

import { Goal } from '../../types/items';
import { EducationLevel, JobTags, Uid, findTypeValues } from '../../types/shared';

import { combineWords, flatten } from '../../utils/funcs';
import logging from '../../utils/logging';
import parsers from '../../utils/parsers';

import { generateCategories, isCategorySkill, scoreCandidateCategories } from '../../skills';

const LOG_NAME = 'entities.goals';

export default class Goals {
  cache: DataCache = null;

  constructor(cache: DataCache) {
    this.cache = cache;
  }

  async load(): Promise<Goal[]> {
    return data.goals.load(this.cache.user);
  }

  async get(id: Uid): Promise<Goal> {
    return data.goals.get(this.cache.user, id);
  }

  async recommend(goals: Goal[], skip_course_ids: Uid[] = [], sources?: string[]) {
    return data.goals.recommend(goals, skip_course_ids, sources);
  }

  async delete(goal: Goal)  {
    await data.goals.delete(this.cache.user, goal);
  }

  async deleteAll(goals: Goal[]) {
    await data.goals.deleteAll(this.cache.user, goals);
  }

  async uncompleteCourse(goal: Goal, id: Uid): Promise<Goal> {
    const course = goal.courses.find(c => c.id === id);
    if(course && course.completed) {
      course.completed = undefined;
      data.goals.save(this.cache.user, goal);
    }
    return goal;
  }

  async completeCourse(goal: Goal, id: Uid): Promise<Goal> {
    const course = goal.courses.find(c => c.id === id);
    if(course && !course.completed) {
      course.completed = new Date();
      data.goals.save(this.cache.user, goal);
    }
    return goal;
  }

  async notify(goal: Goal): Promise<Goal> {
    goal.last_notify = new Date();
    return data.goals.update(this.cache.user, goal);
  }

  async save(goal: Goal): Promise<Goal> {
    return data.goals.save(this.cache.user, goal);
  }

  async saveAll(goals: Goal[]): Promise<Goal[]> {
    return data.goals.saveGoals(this.cache.user, goals);
  }

  async init(group_ids: Uid[], global: boolean, ) {
    let user_goals;

    const lower_names = this.cache.me && this.cache.me.names ? this.cache.me.names.map(n => n.toLowerCase()) : this.cache.user.name.toLowerCase().split(' ');
    const jobs = findTypeValues(this.cache.me.tags, JobTags).map(j => j.toLowerCase());
    const ts = this.cache.me.topSkills;
    const ws = this.cache.me.skills;
    const skills = ts.length < 10 ? ws.slice(0,10) : ts.slice(0,10);
    const single_skills = flatten(skills.map(s => s.split(' ')
      .filter(s => !ts.includes(s) && !parsers.ignore(s) && !parsers.name(s) && !parsers.mask(s) && 
        group_ids.find(g => isCategorySkill(g, s)))));
    const cw = combineWords([...skills.filter(s => s.split(' ').length === 1), ...single_skills]);
    const joined_words = cw;
    const search_skills = _.uniq([...ts, ...joined_words].map(s => s.toLowerCase()));
    logging.infoFP(LOG_NAME, 'getGoals', this.cache.user.profile, `Generating categories for ${skills.length} skills`);
    const categories = await generateCategories(group_ids, global, skills, search_skills, [...lower_names, ...jobs, ...EducationLevel], 10);
    logging.infoFP(LOG_NAME, 'getGoals', this.cache.user.profile, `Found ${categories.length} categories`);
    const matches = await scoreCandidateCategories(categories, this.cache.me);
    logging.infoFP(LOG_NAME, 'getGoals', this.cache.user.profile, `Mapped ${matches ? matches.length : 0} goals`);

    user_goals = _.uniqBy(matches.filter(m => m.score >= 0.75), 'label').map(category => {
      return new Goal({
        id: uuid(),
        category,
      });
    });

    if (user_goals && user_goals.length) await this.saveAll(user_goals);


  }
}