import 'express-session';
import session from "express-session";
import path from 'path';

import config from '../config';
import logging from '../utils/logging';

export default function setupSession() {
  let store = null;

  if (config.isEnvOffline()) {
    const SFS = require('session-file-store') as any;
    const filestore = SFS(session);

    const options = {
      path: path.resolve(__dirname, '../cache/express'),
    };

    store = new filestore(options);
  } else {
    const CMJ = require('connect-memjs') as any;
    const memcached = CMJ(session);
    // const memcached = require('connect-memcached')(session);

    // let memCacheUrl = config.get('MEMCACHE_URL');
    // if (config.get('MEMCACHE_USERNAME')) memCacheUrl = `${config.get('MEMCACHE_USERNAME')}:${config.get('MEMCACHE_PASSWORD')}@${memCacheUrl}`;

    const memconfig = {
      servers: config.get('MEMCACHE_URL').split(','),
      // hosts: [memCacheUrl],
      retries: 3,
      logger: logging,
      username: undefined,
      password: undefined,
      timeout: 1.5,
      // conntimeout: 30000,
      // reconnect: 1000,
      // retry: 300,
      retry_delay: 1,
      failoverTime: 30,
      // idle: 30000,
      keepAlive: true,
    };

    if (config.get('MEMCACHE_USERNAME')) {
      memconfig.username = config.get('MEMCACHE_USERNAME');
      memconfig.password = config.get('MEMCACHE_PASSWORD');
    }

    store = new memcached(memconfig);
  }

  const cookie = config.get('COOKIE');
  const session_config = {
    secret: config.get('SECRET'),
    resave: true,
    saveUninitialized: true,
    store,
    rolling: true,
    // proxy: true,
    name: cookie ? cookie : 'Fora',
    // logger: logging.logger,
    cookie: {
      path: '/',
      maxAge: 5 * 24 * 3600 * 1000,
      httpOnly: false,
      secure: config.isEnvProduction(), // when running https
    },
  };

  if (config.isRunningOnGoogle()) session_config.cookie['domain'] = '.askfora.com';
  else if (config.get('DOMAIN')) session_config.cookie['domain'] = config.get('DOMAIN');

  return session_config;
}