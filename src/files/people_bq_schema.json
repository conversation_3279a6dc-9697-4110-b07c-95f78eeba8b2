[{"description": "displayName", "mode": "NULLABLE", "name": "displayName", "type": "STRING"}, {"description": "comms", "mode": "REPEATED", "name": "comms", "type": "STRING"}, {"description": "tags", "fields": [{"description": "start", "mode": "NULLABLE", "name": "start", "type": "TIMESTAMP"}, {"description": "bias", "mode": "NULLABLE", "name": "bias", "type": "INTEGER"}, {"description": "type", "mode": "NULLABLE", "name": "type", "type": "STRING"}, {"description": "value", "mode": "NULLABLE", "name": "value", "type": "STRING"}, {"description": "index", "mode": "NULLABLE", "name": "index", "type": "INTEGER"}, {"fields": [{"mode": "NULLABLE", "name": "namespace", "type": "STRING"}, {"mode": "NULLABLE", "name": "app", "type": "STRING"}, {"mode": "NULLABLE", "name": "path", "type": "STRING"}, {"mode": "NULLABLE", "name": "kind", "type": "STRING"}, {"mode": "NULLABLE", "name": "name", "type": "STRING"}, {"mode": "NULLABLE", "name": "id", "type": "INTEGER"}], "mode": "NULLABLE", "name": "__key__", "type": "RECORD"}], "mode": "REPEATED", "name": "tags", "type": "RECORD"}, {"description": "photos", "mode": "REPEATED", "name": "photos", "type": "STRING"}, {"description": "names", "mode": "REPEATED", "name": "names", "type": "STRING"}, {"description": "urls", "mode": "REPEATED", "name": "urls", "type": "STRING"}, {"description": "learned", "mode": "REPEATED", "name": "learned", "type": "TIMESTAMP"}, {"description": "nick<PERSON><PERSON>", "mode": "NULLABLE", "name": "nick<PERSON><PERSON>", "type": "STRING"}, {"description": "id", "mode": "NULLABLE", "name": "id", "type": "STRING"}, {"fields": [{"mode": "NULLABLE", "name": "namespace", "type": "STRING"}, {"mode": "NULLABLE", "name": "app", "type": "STRING"}, {"mode": "NULLABLE", "name": "path", "type": "STRING"}, {"mode": "NULLABLE", "name": "kind", "type": "STRING"}, {"mode": "NULLABLE", "name": "name", "type": "STRING"}, {"mode": "NULLABLE", "name": "id", "type": "INTEGER"}], "mode": "NULLABLE", "name": "__key__", "type": "RECORD"}, {"mode": "REPEATED", "name": "__error__", "type": "STRING"}, {"mode": "NULLABLE", "name": "__has_error__", "type": "BOOLEAN"}, {"mode": "NULLABLE", "name": "network", "type": "BOOLEAN"}]