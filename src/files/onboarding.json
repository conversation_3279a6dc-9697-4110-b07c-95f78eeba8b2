[{"id": "contractor_survey", "onboarding": "contractor_survey", "template": "survey", "next_step": "contractor", "minutes": 30}, {"id": "contractor", "onboarding": "contractor", "template": "contractor", "next_step": "promote", "hours": 1}, {"id": "promote", "onboarding": "promote", "template": "promote", "next_step": "available", "days": 2}, {"id": "available", "onboarding": "available", "template": "available", "next_step": "jobs", "days": 2}, {"id": "jobs", "onboarding": "jobs", "template": "jobs", "next_step": "welcome_contractor", "days": 2}, {"id": "welcome_contractor", "onboarding": "welcome_contractor", "template": "welcome", "days": 2}, {"id": "client_survey", "onboarding": "client_survey", "template": "survey", "next_step": "client", "minutes": 30}, {"id": "client", "onboarding": "client", "template": "client", "next_step": "start", "hours": 1}, {"id": "start", "onboarding": "start", "template": "start", "next_step": "welcome", "hours": 1}, {"id": "welcome", "onboarding": "welcome", "template": "welcome", "next_step": "find", "days": 2}, {"id": "find", "onboarding": "find", "template": "find", "days": 2}, {"id": "search", "onboarding": "asks", "url": "/app/ask", "template": "asks", "next_step": "expert", "days": 2}, {"id": "expert", "onboarding": "expert", "url": "/app/ask", "template": "expert", "next_step": "analyses", "days": 2}, {"id": "analyses", "onboarding": "analyses", "url": "/app/ask", "template": "analyses", "days": 2}]