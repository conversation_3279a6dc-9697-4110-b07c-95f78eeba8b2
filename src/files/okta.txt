## SSO Login via Ok<PERSON> and AskFora
AskFora users can configure a default Identity Provider that supports Single Sign On (SSO). The following instructions explain how to configure <PERSON><PERSON> as the primary Identity Provider to support SSO with your AskFora Group.


### Supported Features
Service Provider (SP)-Initiated Authentication (SSO) Flow - This authentication flow occurs when the user attempts to log in to the application from AskFora.


### Requirements
In order to proceed with configuring login with SSO through Okta, you must:

* Have access to an Okta AskFora group

* Be an Okta administrator in your AskFora group


### Configuration Steps
You will need to provide your Okta credentials to AskFora via secure email. 

1. In the Okta dashboard, navigate to Applications and then select the Applications sub-menu.

2. Click on Browse App Catalog and search for ‘<PERSON><PERSON><PERSON>’ then add the application.

3. In the Sign On tab find your Client ID, Okta secret, and the issuer URL linked from your OpenID Provider Metadata.

4. Send this information along with your AskFora group id in a secure <NAME_EMAIL>


### SP-initiated SSO
Go to your domain URL at AskFora.

Use the login option for your Group name or <PERSON><PERSON>. If you are prompted for your Okta username and password, enter them. You will be logged into AskFora if your credentials have perimissions.


### Notes
If you encounter any issues or have any questions, please do not hesitate to reach <NAME_EMAIL>.
