{"CONTRACT_URL": "http://localhost:8000/contract", "DEFAULT_HOST": "group.askfora.com", "GOOGLE_OAUTH2_CALLBACK": "http://localhost:8000/goauth", "GOOGLE_OAUTH2_CLIENT_ID": "google_client_id", "GOOGLE_OAUTH2_CLIENT_SECRET": "google_client_secret", "MEMCACHE_URL": "localhost:6570", "MICROSOFT_OAUTH2_CALLBACK": "http://localhost:8000/goauth", "MICROSOFT_OAUTH2_CLIENT_ID": "microsoft_client_id", "MICROSOFT_OAUTH2_CLIENT_SECRET": "microsoft_client_secret", "MSAL_OAUTH2_CALLBACK": "http://localhost:8000/goauth", "MSAL_OAUTH2_CLIENT_ID": "msal_client_id", "MSAL_OAUTH2_CLIENT_SECRET": "msal_client_secret", "MSAL_OAUTH2_ISSUER": "https://localhost:8000/goauth", "OKTA_OAUTH2_CALLBACK": "https://localhost:8000/goauth", "OKTA_OAUTH2_CLIENT_ID": "okta_client_id", "OKTA_OAUTH2_CLIENT_SECRET": "okta_client_secret", "OKTA_OAUTH2_ISSUER": "https://localhost:8000/goauth", "NOTIFICATION_URL": "http://localhost:8000/", "PROJECT_URL": "http://localhost:8000/project", "SECRET": "8f001edf-cede-4765-9337-80869f65c82d", "SLACK_OAUTH2_CLIENT_ID": "slack_client_id", "SLACK_OAUTH2_CLIENT_SECRET": "slack_client_secret", "SLACK_OAUTH2_CALLBACK": "http://localhost:8000/goauth", "STRIPE_CALLBACK": "https://localhost:8443/gopay", "STRIPE_WISE_ACCOUNT": "acct_offline_wise", "VAPID_PRIV": "enAmBSK_CNZcmkJ3bQ-6W-QcrAK2SavHafn8vC0GSfM", "VAPID_PUB": "BAly2aHVfYpSFBbrVSp_HVk-EFbgzAAptYDQ3Ra7cmUShbbS4PaaxuE8xs23WsxlE4yR8wTz-l__TYKHVXu03fk", "ENABLE_PLANS": true, "ENABLE_TEAMS": true, "name": "offline", "type": "config"}