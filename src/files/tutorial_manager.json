{"id": "a0c0bb68-296f-4ba0-942a-2ee4ec9e0fc5", "public": true, "skills": ["leading teams", "self user manual", "help employees adjust", "move employees on", "five whys", "teaching to ask why", "communicating with employees", "employee performance review", "removing employees"], "title": "Leading Teams with 'Why'", "preassessment": {"prompts": [{"question": "When a team member struggles with a task, which approach feels most natural to you?", "answers": [{"answer": "Offer direct guidance", "skills": ["task delegation", "giving instructions", "problem solving"]}, {"answer": "Discuss root causes", "skills": ["investigative questioning", "analytical thinking", "active listening"]}, {"answer": "Reassign to a better fit", "skills": ["talent assessment", "resource allocation", "team organization"]}]}, {"question": "If an individual's work isn't meeting expectations, what would be your first step?", "answers": [{"answer": "Share specific observations", "skills": ["providing feedback", "documentation", "goal setting"]}, {"answer": "Explore personal drivers", "skills": ["empathy", "motivational understanding", "relationship building"]}, {"answer": "Initiate transition planning", "skills": ["performance management", "succession planning", "change management"]}]}, {"question": "How do you usually approach employee development discussions?", "answers": [{"answer": "Provide training opportunities", "skills": ["learning and development", "skill gap analysis", "resource identification"]}, {"answer": "Discuss personal aspirations", "skills": ["career coaching", "mentoring", "individual development plans"]}, {"answer": "Determine next best role", "skills": ["career mapping", "organizational structure", "talent placement"]}]}, {"question": "During a team meeting, what is your usual method for sparking deeper insight?", "answers": [{"answer": "Give a framework for thinking", "skills": ["process definition", "structured thinking", "knowledge transfer"]}, {"answer": "Pose exploratory questions", "skills": ["facilitation", "critical thinking", "communication skills"]}, {"answer": "Outline clear alternatives", "skills": ["decision making", "strategic planning", "option evaluation"]}]}, {"question": "When facing team resistance to a change, where do you begin?", "answers": [{"answer": "Explain the rationale thoroughly", "skills": ["transparency", "justification", "persuasion"]}, {"answer": "Focus on individual needs", "skills": ["emotional intelligence", "relationship management", "conflict resolution"]}, {"answer": "Implement with clear process", "skills": ["project management", "workflow design", "process implementation"]}]}]}, "lesson_set": [{"outline": {"duration": "12 minutes", "level": "Intermediate", "title": "Part 1:  Deep Dive into 'Asking Why' - The Foundation", "text": "This section focuses on understanding *why* teaching employees to ask 'why' is so powerful. We'll explore the benefits, including fostering a culture of critical thinking and problem-solving. We'll discuss real-world examples of how this skill can improve performance, address potential issues proactively, and increase innovation."}, "lessons": [{"title": "1. Understanding Why 'Why' Matters", "text": "In the modern workplace, teaching employees to ask 'why' is not just about encouraging curiosity – it's about developing a fundamental skill that drives innovation and continuous improvement. When employees understand the importance of questioning existing processes and assumptions, they become active participants in organizational growth rather than passive followers of established procedures.\n\nConsider a software development team that consistently faces deployment delays. Instead of accepting these delays as inevitable, an employee who has learned to ask 'why' might inquire: 'Why do our deployments always happen on Fridays when our support team is transitioning to weekend coverage?' This simple question could lead to a significant process improvement that benefits the entire organization.", "info": [{"title": "Understanding Root Causes", "text": "Learn to identify underlying issues by consistently asking 'why' to uncover deeper connections and patterns.", "image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgZmlsbD0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIyIj4KICAgIDxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNzAiIHN0cm9rZT0iIzRDQUY1MCIgLz4KICAgIDxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iNTAiIHN0cm9rZT0iIzJGOTZFQiIgLz4KICAgIDxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iMzAiIHN0cm9rZT0iI0Y0NDMzNiIgLz4KICAgIDx0ZXh0IHg9Ijk1IiB5PSIxMDUiIGZpbGw9IiMzMzMiIHN0cm9rZT0ibm9uZSIgZm9udC1zaXplPSIyNCIgZm9udC13ZWlnaHQ9ImJvbGQiPj88L3RleHQ+CiAgPC9nPgo8L3N2Zz4="}, {"title": "Fostering Innovation", "text": "Encourage questioning of established processes to spark creative solutions and improvements.", "image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgZmlsbD0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIyIj4KICAgIDxwYXRoIGQ9Ik01MCA4MEw5MCAxMjBMMTMwIDgwTDE3MCAxMjAiIHN0cm9rZT0iIzY3M0FCNyIvPgogICAgPHBvbHlnb24gcG9pbnRzPSIxMDAsNDAgNzAsODAgMTMwLDgwIiBmaWxsPSIjRkY5ODAwIi8+CiAgICA8Y2lyY2xlIGN4PSIxMDAiIGN5PSIxNDAiIHI9IjIwIiBzdHJva2U9IiNFOTFFNjMiLz4KICA8L2c+Cjwvc3ZnPg=="}, {"title": "Driving Improvement", "text": "Transform passive followers into active participants by developing critical thinking skills.", "image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgZmlsbD0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIyIj4KICAgIDxyZWN0IHg9IjQwIiB5PSI4MCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjgwIiBzdHJva2U9IiM4QkMzNEEiLz4KICAgIDxyZWN0IHg9IjgwIiB5PSI2MCIgd2lkdGg9IjQwIiBoZWlnaHQ9IjEwMCIgc3Ryb2tlPSIjMDNBOUY0Ii8+CiAgICA8cmVjdCB4PSIxMjAiIHk9IjQwIiB3aWR0aD0iNDAiIGhlaWdodD0iMTIwIiBzdHJva2U9IiNGRjU3MjIiLz4KICAgIDxsaW5lIHgxPSI0MCIgeTE9IjEyMCIgeDI9IjE2MCIgeTI9IjQwIiBzdHJva2U9IiM5QzI3QjAiIHN0cm9rZS1kYXNoYXJyYXk9IjUgNSIvPgogIDwvZz4KPC9zdmc+"}]}, {"title": "2. The Impact on Team Performance", "text": "When team members learn to ask 'why,' it transforms the dynamics of problem-solving and decision-making. Instead of merely executing tasks, they begin to understand the purpose behind their work and can make more informed decisions. This deeper understanding leads to better performance outcomes and more engaged employees.\n\nFor example, a customer service representative who asks 'Why are we seeing an increase in complaints about our new feature?' might uncover valuable insights about user experience that could prevent future issues. This proactive approach not only improves customer satisfaction but also saves resources that would otherwise be spent on addressing complaints after the fact.", "info": [{"title": "Understanding the Root Cause", "text": "Team members who ask 'why' develop deeper insights into problems and their underlying causes", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjgwIiBmaWxsPSIjZjVhNjIzIiBvcGFjaXR5PSIwLjgiLz48cGF0aCBkPSJNNzAgMTIwYzIwLTMwIDQwLTMwIDYwIDAiIHN0cm9rZT0iIzJjM2U1MCIgc3Ryb2tlLXdpZHRoPSI0IiBmaWxsPSJub25lIi8+PGNpcmNsZSBjeD0iNzAiIGN5PSI4MCIgcj0iMTAiIGZpbGw9IiMyYzNlNTAiLz48Y2lyY2xlIGN4PSIxMzAiIGN5PSI4MCIgcj0iMTAiIGZpbGw9IiMyYzNlNTAiLz48L3N2Zz4="}, {"title": "Improved Decision Making", "text": "Critical questioning leads to better informed decisions and more strategic problem-solving approaches", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cmVjdCB4PSI0MCIgeT0iNDAiIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjMzQ5OGRiIiBvcGFjaXR5PSIwLjgiLz48cG9seWdvbiBwb2ludHM9IjEwMCw0MCA0MCwxNjAgMTYwLDE2MCIgZmlsbD0iIzI3YWU2MCIgb3BhY2l0eT0iMC44Ii8+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIyMCIgZmlsbD0iI2U3NGMzYyIvPjwvc3ZnPg=="}, {"title": "Enhanced Customer Experience", "text": "Proactive problem-solving through questioning leads to better customer satisfaction and resource efficiency", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cGF0aCBkPSJNNDAgMTAwYzAtMzAgNDAtNjAgNjAtNjBzNjAgMzAgNjAgNjBzLTQwIDYwLTYwIDYwcy02MC0zMC02MC02MHoiIGZpbGw9IiM5YjU5YjYiIG9wYWNpdHk9IjAuOCIvPjxwYXRoIGQ9Ik03MCAxMDBjMC0yMCAzMC00MCA0MC00MHM0MCAyMCA0MCA0MHMtMzAgNDAtNDAgNDBzLTQwLTIwLTQwLTQweiIgZmlsbD0iI2U2N2UyMiIvPjwvc3ZnPg=="}]}, {"title": "3. Building a Culture of Critical Thinking", "text": "Creating an environment where asking 'why' is encouraged requires deliberate effort from leadership. Leaders must model this behavior and create psychological safety where questioning is viewed as valuable rather than challenging authority. When employees feel safe to ask 'why,' they're more likely to identify potential problems before they become critical issues.\n\nA manufacturing team leader who encourages their team to question processes might discover that a particular safety protocol, while well-intentioned, is actually creating inefficiencies without adding meaningful protection. By fostering an environment where team members feel comfortable asking 'why we do it this way,' the leader enables continuous improvement and innovation.", "info": [{"title": "Model Questioning Behavior", "text": "Leaders must demonstrate openness to questions and critical thinking by regularly asking 'why' themselves", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iIzRDQUY1MCIvPjxwYXRoIGQ9Ik0zMCA3MGwyMC0yMCAyMCAyMCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSI1IiBmaWxsPSJub25lIi8+PC9zdmc+"}, {"title": "Create Psychological Safety", "text": "Foster an environment where questioning processes is seen as valuable rather than threatening", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB4PSIyMCIgeT0iMjAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iI0ZGOTgwMCIgcng9IjEwIi8+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iMjAiIGZpbGw9IiNGRkYiLz48L3N2Zz4="}, {"title": "Enable Continuous Improvement", "text": "Turn critical questions into opportunities for process optimization and innovation", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBkPSJNMTAgNTBjMC0yMiAxOC00MCA0MC00MHM0MCAxOCA0MCA0MC0xOCA0MC00MCA0MFMxMCA3MiAxMCA1MHoiIGZpbGw9IiNFOTFFNjMiLz48cGF0aCBkPSJNNTAgMjB2NjAiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iNSIvPjxwYXRoIGQ9Ik0yMCA1MGg2MCIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSI1Ii8+PC9zdmc+"}]}, {"title": "4. From Questions to Solutions", "text": "The power of asking 'why' extends beyond initial inquiry into actual problem-solving. By teaching employees to follow the thread of questioning, they develop better analytical skills and become more effective problem solvers. This approach is particularly valuable during performance reviews and when helping employees adjust to new roles or responsibilities.\n\nConsider a marketing team member who asks 'Why aren't our email campaigns performing as well as last quarter?' This question might lead to a series of investigations revealing that the timing of emails needs adjustment for different time zones, ultimately improving campaign performance across all regions.", "info": [{"title": "Identify the Question", "text": "Start by clearly stating the problem or challenge you observe, like 'Why aren't our campaigns performing well?'", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjgwIiBmaWxsPSIjZjVmNWY1IiBzdHJva2U9IiM0YTkwZTIiIHN0cm9rZS13aWR0aD0iMyIvPjx0ZXh0IHg9IjEwMCIgeT0iMTIwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmb250LXNpemU9IjgwIiBmaWxsPSIjNGE5MGUyIj4/PC90ZXh0Pjwvc3ZnPg=="}, {"title": "Analyze Through Questions", "text": "Follow the thread of questioning to uncover root causes and potential solutions through systematic investigation", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cGF0aCBkPSJNMjAgMTAwYzAtNDQuMSAzNS45LTgwIDgwLTgwczgwIDM1LjkgODAgODBzLTM1LjkgODAtODAgODBTMjAgMTQ0LjEgMjAgMTAweiIgZmlsbD0iI2ZmZjBmNSIgc3Ryb2tlPSIjZmY2YjZiIiBzdHJva2Utd2lkdGg9IjMiLz48cGF0aCBkPSJNOTAgOTBjMC0xNi41IDEzLjUtMzAgMzAtMzBzMzAgMTMuNSAzMCAzMC0xMy41IDMwLTMwIDMwUzkwIDEwNi41IDkwIDkweiIgZmlsbD0iI2ZmYzFjMSIvPjxwYXRoIGQ9Ik03MCA3MGMwLTExIDktMjAgMjAtMjBzMjAgOSAyMCAyMC05IDIwLTIwIDIwUzcwIDgxIDcwIDcweiIgZmlsbD0iI2ZmZTRlNCIvPjwvc3ZnPg=="}, {"title": "Implement Solutions", "text": "Take action based on your findings, such as adjusting email timing for different time zones to improve campaign performance", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cGF0aCBkPSJNMjAgMTAwTDgwIDIwbDEwMCA2MC00MCAxMDB6IiBmaWxsPSIjYjRlY2I0IiBzdHJva2U9IiM0Y2FmNTAiIHN0cm9rZS13aWR0aD0iMyIvPjxjaXJjbGUgY3g9IjEyMCIgY3k9IjgwIiByPSIyMCIgZmlsbD0iIzRjYWY1MCIvPjxwYXRoIGQ9Ik0xMTAgOTBsLTEwIDIwIDMwLTEweiIgZmlsbD0iI2ZmZiIvPjwvc3ZnPg=="}]}, {"title": "5. Implementation and Resistance", "text": "Introducing a culture of questioning can meet resistance, particularly in organizations with deeply entrenched processes. Leaders must be prepared to address this resistance and demonstrate the value of asking 'why' through concrete examples and clear communication. This might involve creating structured opportunities for questioning during team meetings or project reviews.\n\nFor instance, a manager implementing this approach might start each project review with 'Why are we pursuing this particular strategy?' This simple practice helps team members understand the broader context of their work and encourages them to think more critically about their contributions to organizational goals.", "info": [{"title": "Initial Resistance", "text": "Expect pushback when introducing questioning culture in organizations with established processes", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjgwIiBmaWxsPSIjZmY3ZjUwIi8+PGNpcmNsZSBjeD0iNzAiIGN5PSI4MCIgcj0iMjAiIGZpbGw9IiM0ZGI2YWMiLz48Y2lyY2xlIGN4PSIxMzAiIGN5PSI4MCIgcj0iMjAiIGZpbGw9IiM0ZGI2YWMiLz48cGF0aCBkPSJNNjAgMTMwIHE0MCAtNDAgODAgMCIgc3Ryb2tlPSIjMzM0NDU1IiBzdHJva2Utd2lkdGg9IjYiIGZpbGw9Im5vbmUiLz48L3N2Zz4="}, {"title": "Leadership Support", "text": "Leaders must actively demonstrate the value through examples and clear communication", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cmVjdCB4PSI0MCIgeT0iNDAiIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjNWZhZGU2IiByeD0iMTAiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSI4MCIgcj0iMjUiIGZpbGw9IiNmZmQ3MDAiLz48cGF0aCBkPSJNNzAgMTIwIHE0MCAtMjAgNjAgMCIgc3Ryb2tlPSIjZmZmZmZmIiBzdHJva2Utd2lkdGg9IjQiIGZpbGw9Im5vbmUiLz48L3N2Zz4="}, {"title": "Structured Implementation", "text": "Create dedicated opportunities for questioning during meetings and reviews", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cmVjdCB4PSI0MCIgeT0iNDAiIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjOWI1OWI2IiByeD0iMTAiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjQwIiBmaWxsPSIjZWNmMGYxIi8+PHRleHQgeD0iODUiIHk9IjExNSIgZm9udC1zaXplPSI0MCIgZmlsbD0iIzM0NDk1ZSI+Pz88L3RleHQ+PC9zdmc+"}]}], "assessment": {"prompts": [{"question": "When a project fails, which response is most beneficial?", "answers": [{"answer": "Examine root causes deeply", "skills": ["Analytical thinking", "Problem solving", "Process Improvement"]}, {"answer": "Assign blame to team", "skills": ["Avoids accountability", "Poor team dynamics"]}, {"answer": "Move on to the next", "skills": ["Ignores learning opportunities", "Lack of progress"]}]}, {"question": "During onboarding, it's helpful to encourage new hires to:", "answers": [{"answer": "Understand system's purposes", "skills": ["Strategic thinking", "Big picture thinking", "Purpose driven work"]}, {"answer": "Follow all set rules", "skills": ["Blind obedience", "Lack of initiative"]}, {"answer": "Accept current processes only", "skills": ["Resists change", "Stagnant thinking"]}]}, {"question": "When an employee's work quality drops, the first action should be to:", "answers": [{"answer": "Identify the underlying issue", "skills": ["Root cause analysis", "Problem identification", "Supportive management"]}, {"answer": "Issue a warning immediately", "skills": ["Punitive actions", "Negative reinforcement"]}, {"answer": "Ignore until it escalates", "skills": ["Neglectful leadership", "Reactive management"]}]}, {"question": "A team member proposes a new method. The best response is to:", "answers": [{"answer": "Explore its potential benefits", "skills": ["Openness to change", "Innovation focused", "Progressive thinking"]}, {"answer": "Reject if change is scary", "skills": ["Resistance to change", "Fear based leadership"]}, {"answer": "Dismiss it out of hand", "skills": ["Closed mindedness", "Suppresses new ideas"]}]}]}}, {"outline": {"duration": "10 minutes", "level": "Intermediate", "title": "Part 2: Practical Techniques for Teaching 'Why'", "text": "We move into specific techniques you can use to teach employees to ask 'why'. This will include methods such as using the 5 Whys technique, active questioning during meetings, and leading by example with your own questions. We will also discuss how to provide constructive feedback on questions asked by employees, encouraging them to refine their approach."}, "lessons": [{"title": "1. Mastering the Art of Teaching 'Why' Questions", "text": "The foundation of developing a questioning mindset in your team begins with understanding how to effectively teach the art of asking 'why.' As a leader, your role is to create an environment where questioning is not just permitted, but actively encouraged and refined. Start by modeling the behavior yourself: when discussing projects or challenges, verbally walk through your thought process, demonstrating how asking 'why' leads to better solutions. For example, if a team member suggests changing a process, respond with 'That's interesting - why do you think this change would be beneficial?' This shows both how to phrase questions and demonstrates the value of deeper inquiry.", "info": [{"title": "Cultivate Curiosity", "text": "Create an environment where questioning is actively encouraged and refined.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI2Y5YTgyNSIvPjxwYXRoIGQ9Ik01MCA3MGwyMC0yMEg3MEw1MCAzMCAzMCA1MGgwbDIwIDIweiIgZmlsbD0iIzRjYWY1MCIvPjxjaXJjbGUgY3g9IjUwIiBjeT0iNDAiIHI9IjgiIGZpbGw9IiNmZmYiLz48L3N2Zz4="}, {"title": "Model the Behavior", "text": "Walk through your thought process openly, showing how asking 'why' leads to better solutions.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB4PSIyMCIgeT0iMjAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgcng9IjEwIiBmaWxsPSIjMjE5NmYzIi8+PHBhdGggZD0iTTM1IDUwaDMwTTUwIDM1djMwIiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iNCIvPjwvc3ZnPg=="}, {"title": "Guide Through Example", "text": "Use real scenarios to demonstrate effective questioning techniques.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBkPSJNMjAgODBoNjBsMTAtNDBIMTBsMTAgNDB6IiBmaWxsPSIjZTkxZTYzIi8+PHBhdGggZD0iTTM1IDMwaDMwbDUgMTBIMzBsNS0xMHoiIGZpbGw9IiM5YzI3YjAiLz48Y2lyY2xlIGN4PSI1MCIgY3k9IjYwIiByPSI4IiBmaWxsPSIjZmZmIi8+PC9zdmc+"}]}, {"title": "2. Implementing the 5 Whys Technique", "text": "The 5 Whys technique is a powerful tool originally developed by Toyota, but its application extends far beyond manufacturing. When an issue arises, guide your team through the process of asking 'why' five times to reach the root cause. For instance, if a project is delayed, the sequence might go: Why is it delayed? (Missing data) Why is the data missing? (Team B hasn't provided it) Why hasn't Team B provided it? (They didn't know it was needed) Why didn't they know? (Communication breakdown) Why was there a communication breakdown? (No clear process for data requests). This exercise teaches systematic questioning and shows how each 'why' reveals a deeper understanding.", "info": [{"title": "Start with the Problem", "text": "Begin by clearly stating the initial problem or issue that needs to be addressed. Make sure it's specific and observable.", "image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGZpbGw9IiNGRjU3MjIiIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjgwIi8+PHRleHQgZmlsbD0iI0ZGRiIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjYwIiB4PSI4NSIgeT0iMTIwIj4/PC90ZXh0PjwvZz48L3N2Zz4="}, {"title": "Ask Why Five Times", "text": "For each answer you receive, probe deeper by asking 'why' again. Continue this process five times to move beyond surface-level symptoms.", "image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48Y2lyY2xlIGZpbGw9IiM0Q0FGNTAiIGN4PSIxMDAiIGN5PSI2MCIgcj0iNDAiLz48Y2lyY2xlIGZpbGw9IiMyMTk2RjMiIGN4PSI2MCIgY3k9IjE0MCIgcj0iNDAiLz48Y2lyY2xlIGZpbGw9IiNGNDQzMzYiIGN4PSIxNDAiIGN5PSIxNDAiIHI9IjQwIi8+PHRleHQgZmlsbD0iI0ZGRiIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiB4PSI5MCIgeT0iNzAiPldIWTwvdGV4dD48L2c+PC9zdmc+"}, {"title": "Identify Root Cause", "text": "The final 'why' should reveal the root cause. This is where you'll find the fundamental issue that needs to be addressed to prevent recurrence.", "image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGcgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj48cGF0aCBmaWxsPSIjOEU0NENEIiBkPSJNNjAgNDBoODB2MTIwSDYweiIvPjxjaXJjbGUgZmlsbD0iIzAwQkNENSIgY3g9IjEwMCIgY3k9IjQwIiByPSIzMCIvPjx0ZXh0IGZpbGw9IiNGRkYiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgeD0iODAiIHk9IjQ4Ij4hPC90ZXh0PjwvZz48L3N2Zz4="}]}, {"title": "3. Building Question-Friendly Meeting Structures", "text": "Transform your regular meetings into learning opportunities by incorporating structured question time. Begin each meeting by stating 'There are no wrong questions here.' When someone presents an idea or update, explicitly pause for questions, but don't just ask 'Any questions?' Instead, lead with specific prompts like 'What assumptions are we making?' or 'What would make this fail?' This creates a safe space for questioning while teaching employees how to frame effective questions. Remember to acknowledge and appreciate good questions: 'That's an excellent question because it helps us consider the long-term implications.'", "info": [{"title": "Set a Question-Friendly Tone", "text": "Begin meetings by declaring 'There are no wrong questions here' to establish psychological safety and encourage participation", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI2ZmZjBmNSIgc3Ryb2tlPSIjZmY2YjZiIiBzdHJva2Utd2lkdGg9IjIiLz48dGV4dCB4PSI1MCIgeT0iNTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyNCIgZmlsbD0iIzMzMyI+Pz88L3RleHQ+PC9zdmc+"}, {"title": "Use Specific Question Prompts", "text": "Instead of asking 'Any questions?', use targeted prompts like 'What assumptions are we making?' or 'What would make this fail?'", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB4PSIxMCIgeT0iMTAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgZmlsbD0iI2U2ZjNmZiIgc3Ryb2tlPSIjNDI4MGZmIiBzdHJva2Utd2lkdGg9IjIiLz48dGV4dCB4PSI1MCIgeT0iNTUiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIzMCIgZmlsbD0iIzMzMyI+PzwhLS0gLS0+PC90ZXh0Pjwvc3ZnPg=="}, {"title": "Acknowledge Good Questions", "text": "Reinforce questioning behavior by appreciating insightful questions and explaining their value to the discussion", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cG9seWdvbiBwb2ludHM9IjUwLDEwIDkwLDkwIDEwLDkwIiBmaWxsPSIjZTVmZmU2IiBzdHJva2U9IiM0Y2FmNTAiIHN0cm9rZS13aWR0aD0iMiIvPjx0ZXh0IHg9IjUwIiB5PSI2MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmaWxsPSIjMzMzIj7inIU8L3RleHQ+PC9zdmc+"}]}, {"title": "4. Providing Constructive Feedback on Questions", "text": "Feedback on questioning technique is crucial for development. When an employee asks a question, take time to discuss not just the answer, but the quality of the question itself. For instance, if an employee asks 'Will this project succeed?' help them refine it to 'What are the key factors that will determine this project's success?' Explain how the refined version is more specific and actionable. Create a feedback framework that examines question depth, relevance, and potential impact. Share this framework with your team so they understand how to evaluate and improve their own questions.", "info": [{"title": "Analyze Question Quality", "text": "Take time to evaluate both the content and structure of questions asked. Help employees understand what makes a strong versus weak question.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI2U2ZjNmZiIvPjxwYXRoIGQ9Ik01MCA3MGMtMTEgMC0yMC05LTIwLTIwczktMjAgMjAtMjBjMTEgMCAyMCA5IDIwIDIwcy05IDIwLTIwIDIweiIgZmlsbD0iIzRjYWY1MCIvPjx0ZXh0IHg9IjQ1IiB5PSI1NSIgZm9udC1zaXplPSIyMCIgZmlsbD0iI2ZmZiI+Pz88L3RleHQ+PC9zdmc+"}, {"title": "Guide Question Refinement", "text": "Help transform vague questions into specific, actionable ones that lead to meaningful insights and solutions.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBkPSJNMTAgOTBoODBNMjAgODBMNDAgNDBNNDAgNDBMNjAgNjBNNjAgNjBMODAgMjAiIHN0cm9rZT0iIzJjOThmMCIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+PGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iNSIgZmlsbD0iI2Y0NDMzNiIvPjxjaXJjbGUgY3g9IjYwIiBjeT0iNjAiIHI9IjUiIGZpbGw9IiM0Y2FmNTAiLz48L3N2Zz4="}, {"title": "Establish Feedback Framework", "text": "Create and share a structured approach for evaluating questions based on depth, relevance, and potential impact.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB4PSIxMCIgeT0iMjAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI2MCIgZmlsbD0iI2ZmZWNiMyIgc3Ryb2tlPSIjZmZhMDAwIiBzdHJva2Utd2lkdGg9IjIiLz48bGluZSB4MT0iMjAiIHkxPSI0MCIgeDI9IjgwIiB5Mj0iNDAiIHN0cm9rZT0iI2ZmYTAwMCIgc3Ryb2tlLXdpZHRoPSIyIi8+PGxpbmUgeDE9IjIwIiB5MT0iNjAiIHgyPSI4MCIgeTI9IjYwIiBzdHJva2U9IiNmZmEwMDAiIHN0cm9rZS13aWR0aD0iMiIvPjwvc3ZnPg=="}]}, {"title": "5. Establishing Question-Based Problem-Solving Sessions", "text": "Dedicate specific sessions to practicing question-based problem solving. Present a real business challenge and guide the team through a structured questioning process. For example, if addressing customer churn, start with broad questions ('Why are customers leaving?') and progressively narrow down to specific, actionable queries ('Why do customers in the enterprise segment show higher satisfaction than those in small business?'). Document these sessions and the insights gained to demonstrate the value of systematic questioning. This practical application helps employees understand how strategic questioning leads to better solutions and decisions.", "info": [{"title": "Present the Challenge", "text": "Begin with a real business challenge and set up a structured questioning session with your team", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZyBmaWxsPSJub25lIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiPjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjQwIiBzdHJva2U9IiM0Q0FGNTAiIHN0cm9rZS13aWR0aD0iMiIvPjx0ZXh0IHg9IjUwIiB5PSI1MCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjQwIiBmaWxsPSIjMjE5NkYzIj4/PC90ZXh0PjwvZz48L3N2Zz4="}, {"title": "Guide the Questioning Process", "text": "Lead the team from broad questions to specific, actionable queries through systematic investigation", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZyBmaWxsPSJub25lIiBzdHJva2U9IiNGRjU3MjIiIHN0cm9rZS13aWR0aD0iMiI+PHBhdGggZD0iTTIwIDgwTDUwIDIwTDgwIDgwIi8+PHBhdGggZD0iTTIwIDYwTDgwIDYwIi8+PHBhdGggZD0iTTM1IDQwTDY1IDQwIi8+PC9nPjwvc3ZnPg=="}, {"title": "Document and Demonstrate Value", "text": "Record insights and outcomes to show how strategic questioning leads to better solutions", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZyBmaWxsPSJub25lIiBzdHJva2U9IiM5QzI3QjAiIHN0cm9rZS13aWR0aD0iMiI+PHJlY3QgeD0iMjAiIHk9IjIwIiB3aWR0aD0iNjAiIGhlaWdodD0iNjAiLz48bGluZSB4MT0iMzAiIHkxPSI0MCIgeDI9IjcwIiB5Mj0iNDAiLz48bGluZSB4MT0iMzAiIHkxPSI1MCIgeDI9IjcwIiB5Mj0iNTAiLz48bGluZSB4MT0iMzAiIHkxPSI2MCIgeDI9IjcwIiB5Mj0iNjAiLz48L2c+PC9zdmc+"}]}], "assessment": {"prompts": [{"question": "During a project review, a team member identifies a possible issue. What should you do?", "answers": [{"answer": "Ask what makes them think so", "skills": ["fostering inquiry", "critical thinking"]}, {"answer": "Tell them to find a solution", "skills": ["delegation", "problem solving"]}, {"answer": "Ignore it, keep going", "skills": ["none"]}]}, {"question": "An employee makes a statement. How can you encourage deeper thought?", "answers": [{"answer": "Propose alternatives and discuss", "skills": ["critical thinking", "creative problem solving"]}, {"answer": "Immediately provide the answer", "skills": ["subject matter expertise"]}, {"answer": "Say that's not the best way", "skills": ["none"]}]}, {"question": "A process isn't working. How do you explore the root cause?", "answers": [{"answer": "Use iterative probing questions", "skills": ["problem analysis", "structured investigation"]}, {"answer": "Implement a new process", "skills": ["process improvement"]}, {"answer": "Accept it, move on", "skills": ["none"]}]}, {"question": "How should feedback on employee questions be given?", "answers": [{"answer": "Assess the question's relevance", "skills": ["performance evaluation", "communication skills"]}, {"answer": "Just give the answer", "skills": ["none"]}, {"answer": "Ignore and say good job", "skills": ["none"]}]}, {"question": "How can team meetings improve employee questioning skills?", "answers": [{"answer": "Use prompts for questions", "skills": ["meeting facilitation", "guided discussion"]}, {"answer": "Tell them not to ask", "skills": ["none"]}, {"answer": "Skip it for faster meetings", "skills": ["none"]}]}]}}, {"outline": {"duration": "15 minutes", "level": "Intermediate", "title": "Part 3:  Integrating 'Why' into Performance Reviews", "text": "This part explores incorporating 'why' into employee performance reviews. We'll discuss how to assess employees' ability to ask 'why' in an effective way, and how to provide feedback and coaching on this skill. We will focus on integrating this into performance goals and objectives, emphasizing the importance of continuous improvement."}, "lessons": [{"title": "1. Understanding the Role of 'Why' in Performance Assessment", "text": "When conducting performance reviews, managers often focus on what employees have achieved and how they've accomplished their goals. However, integrating the 'why' dimension adds crucial depth to these evaluations. The ability to question and understand underlying reasons is a fundamental skill that drives innovation and improvement.\n\nConsider a software developer who consistently meets deadlines but never questions requirements. While they're technically proficient, their lack of curiosity might result in missed opportunities for process improvement or innovative solutions. During performance reviews, assess how often employees ask clarifying questions, challenge assumptions constructively, and seek to understand the broader context of their work.", "info": [{"title": "Uncovering Purpose", "text": "Move beyond surface-level assessments to explore the deeper motivations and reasoning behind employee performance", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMjAwIDIwMCI+PHBhdGggZD0iTTEwMCAyMGM0NC4xODMgMCA4MCAzNS44MTcgODAgODBzLTM1LjgxNyA4MC04MCA4MFMyMCAxNDQuMTgzIDIwIDEwMHMzNS44MTcgLTgwIDgwIC04MHoiIGZpbGw9IiM2MkIxRTYiLz48cGF0aCBkPSJNMTAwIDQwYzMzLjEzNyAwIDYwIDI2Ljg2MyA2MCA2MHMtMjYuODYzIDYwLTYwIDYwUzQwIDEzMy4xMzcgNDAgMTAwczI2Ljg2MyAtNjAgNjAgLTYweiIgZmlsbD0iI0ZGOTk0RiIvPjx0ZXh0IHg9IjkwIiB5PSIxMTAiIGZvbnQtc2l6ZT0iNjAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZmlsbD0iI0ZGRiI+Pz88L3RleHQ+PC9zdmc+"}, {"title": "Cultivating Curiosity", "text": "Encourage employees to question assumptions and seek deeper understanding of processes and requirements", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMjAwIDIwMCI+PHBhdGggZD0iTTUwIDUwaDEwMHYxMDBINTB6IiBmaWxsPSIjRjA2MjkyIi8+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI0MCIgZmlsbD0iIzRDQUY1MCIvPjxwYXRoIGQ9Ik04NSA4NWgzMHYzMEg4NXoiIGZpbGw9IiNGRkMxMDciLz48L3N2Zz4="}, {"title": "Driving Innovation", "text": "Transform standard performance metrics by evaluating how employees contribute to process improvement and creative problem-solving", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiB2aWV3Qm94PSIwIDAgMjAwIDIwMCI+PHBhdGggZD0iTTIwIDEwMGg0MGw0MCAtNDBoNDBsNDAgNDBoLTQwbC00MCA0MEg2MEwyMCAxMDB6IiBmaWxsPSIjOUMyN0IwIi8+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIyMCIgZmlsbD0iI0U5MUU2MyIvPjwvc3ZnPg=="}]}, {"title": "2. Developing Assessment Criteria for 'Why' Skills", "text": "To effectively evaluate an employee's ability to ask 'why,' we need clear assessment criteria. This includes examining how they approach problem-solving, their contribution to team discussions, and their impact on process improvement.\n\nFor example, when evaluating a project manager, consider instances where they've questioned standard procedures and the outcomes of those inquiries. Did their questions about the traditional weekly meeting schedule lead to more efficient team communications? Did their investigation into recurring client complaints result in meaningful process changes? Document specific examples where their curiosity led to tangible improvements.", "info": [{"title": "Define Assessment Parameters", "text": "Establish clear metrics to measure how employees question standard procedures and approach problem-solving", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImEiIHgxPSIwIiB5MT0iMCIgeDI9IjEiIHkyPSIxIj48c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNDI4N2Y1Ii8+PHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMmI2YWQyIi8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iNDAiIGZpbGw9InVybCgjYSkiLz48cGF0aCBkPSJNMzAgNzBsNDAtNDBNMzAgMzBsNDAgNDAiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSI0Ii8+PC9zdmc+"}, {"title": "Monitor Team Contributions", "text": "Track how employees participate in discussions and contribute to collaborative problem-solving efforts", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjMwIiByPSIyMCIgZmlsbD0iI2ZmOTgwMCIvPjxjaXJjbGUgY3g9IjIwIiBjeT0iNzAiIHI9IjIwIiBmaWxsPSIjZjQ0MzM2Ii8+PGNpcmNsZSBjeD0iODAiIGN5PSI3MCIgcj0iMjAiIGZpbGw9IiM0Y2FmNTAiLz48L3N2Zz4="}, {"title": "Measure Impact", "text": "Document specific examples where questioning and curiosity led to tangible process improvements", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cG9seWxpbmUgcG9pbnRzPSIxMCA3MCA0MCA0MCA2MCA2MCA5MCAyMCIgc3Ryb2tlPSIjMmI2YWQyIiBzdHJva2Utd2lkdGg9IjQiIGZpbGw9Im5vbmUiLz48Y2lyY2xlIGN4PSI5MCIgY3k9IjIwIiByPSI4IiBmaWxsPSIjNGNhZjUwIi8+PC9zdmc+"}]}, {"title": "3. Providing Constructive Feedback on Questioning Skills", "text": "When discussing an employee's questioning skills during performance reviews, focus on both the frequency and quality of their inquiries. The goal is to encourage thoughtful, purposeful questions rather than superficial or disruptive ones.\n\nFor instance, if an employee tends to accept tasks without understanding their purpose, guide them toward asking strategic questions like 'How does this project align with our department's goals?' or 'What impact will this have on our end users?' Share specific scenarios where better questioning could have led to improved outcomes. If an employee recently completed a project that encountered unexpected challenges, discuss how proactive questioning early on might have identified and addressed these issues sooner.", "info": [{"title": "Recognize Questioning Patterns", "text": "Observe and document both the frequency and quality of employee questions during meetings and tasks.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjgwIiBmaWxsPSIjZTBlNGZmIi8+PHBhdGggZD0iTTgwIDYwYzIwLTEwIDQwLTEwIDYwIDBzMjAgMzAgMCAzMHMtNDAgMTAtNjAgMHMtMjAtMzAgMC0zMHoiIGZpbGw9IiM2Nzc1ZTQiLz48dGV4dCB4PSI5MCIgeT0iMTIwIiBmb250LXNpemU9IjQwIiBmaWxsPSIjMzQ0MmE4Ij4/PC90ZXh0Pjwvc3ZnPg=="}, {"title": "Guide Strategic Inquiry", "text": "Encourage employees to ask purposeful questions that align projects with organizational goals and user needs.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cmVjdCB4PSI0MCIgeT0iNDAiIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiBmaWxsPSIjZmZlZGU3Ii8+PHBhdGggZD0iTTYwIDgwbDQwIDQwbDQwLTQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNmZjk2NzEiIHN0cm9rZS13aWR0aD0iOCIvPjxjaXJjbGUgY3g9IjEwMCIgY3k9IjEwMCIgcj0iMjAiIGZpbGw9IiNmZjc2NGQiLz48L3N2Zz4="}, {"title": "Review & Improve", "text": "Analyze past scenarios where better questioning could have prevented challenges and led to better outcomes.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cGF0aCBkPSJNNDAgMTAwYzAtMzMuMTM3IDI2Ljg2My02MCA2MC02MHM2MCAyNi44NjMgNjAgNjBzLTI2Ljg2MyA2MC02MCA2MHMtNjAtMjYuODYzLTYwLTYweiIgZmlsbD0iI2UzZjVlYiIvPjxwYXRoIGQ9Ik04MCA4MGw0MCA0MG00MC00MGwtNDAgNDAiIHN0cm9rZT0iIzRjYWY1MCIgc3Ryb2tlLXdpZHRoPSI4IiBmaWxsPSJub25lIi8+PC9zdmc+"}]}, {"title": "4. Setting Goals for Improvement", "text": "The performance review should include specific, measurable goals for developing questioning skills. These goals should align with both individual development and organizational objectives.\n\nA practical example might be setting a goal for a team leader to implement a 'Five Whys' analysis in their monthly team meetings, documenting the insights gained and improvements made. Another goal might be for an individual contributor to develop and present three process improvement proposals over the next quarter, based on their investigation into current workflows. These goals should be challenging yet achievable, with clear metrics for success.", "info": [{"title": "Setting Clear Targets", "text": "Define specific, measurable goals that align with both personal development and organizational needs.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI2Y1YjA0MiIgc3Ryb2tlPSIjZWM4YTBjIiBzdHJva2Utd2lkdGg9IjIiLz48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSIzMCIgZmlsbD0iI2ZmZDcwMCIvPjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjIwIiBmaWxsPSIjZmY5NjAwIi8+PGxpbmUgeDE9IjUwIiB5MT0iMTAiIHgyPSI1MCIgeTI9IjkwIiBzdHJva2U9IiMzMzMiIHN0cm9rZS13aWR0aD0iMiIvPjxsaW5lIHgxPSIxMCIgeTE9IjUwIiB4Mj0iOTAiIHkyPSI1MCIgc3Ryb2tlPSIjMzMzIiBzdHJva2Utd2lkdGg9IjIiLz48L3N2Zz4="}, {"title": "Implement 'Five Whys' Analysis", "text": "Team leaders should incorporate structured problem-solving techniques in monthly meetings to gain deeper insights.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB4PSIxMCIgeT0iMTAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgZmlsbD0iIzY0OTVlZCIgcng9IjEwIi8+PHRleHQgeD0iMjUiIHk9IjQ1IiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSI+PzwvdGV4dD48dGV4dCB4PSI0NSIgeT0iNDUiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IndoaXRlIj4/PC90ZXh0Pjx0ZXh0IHg9IjY1IiB5PSI0NSIgZm9udC1zaXplPSIyNCIgZmlsbD0id2hpdGUiPj88L3RleHQ+PHRleHQgeD0iMzUiIHk9IjcwIiBmb250LXNpemU9IjI0IiBmaWxsPSJ3aGl0ZSI+Pz88L3RleHQ+PC9zdmc+"}, {"title": "Track Progress", "text": "Develop and present process improvement proposals based on workflow investigations with clear success metrics.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBkPSJNMTAgOTAgTDMwIDcwIEw1MCA4MCBMIDA4MCA0MCBMOTAgNjAiIHN0cm9rZT0iIzRjYWY1MCIgc3Ryb2tlLXdpZHRoPSIzIiBmaWxsPSJub25lIi8+PGNpcmNsZSBjeD0iMzAiIGN5PSI3MCIgcj0iNCIgZmlsbD0iIzRjYWY1MCIvPjxjaXJjbGUgY3g9IjUwIiBjeT0iODAiIHI9IjQiIGZpbGw9IiM0Y2FmNTAiLz48Y2lyY2xlIGN4PSI4MCIgY3k9IjQwIiByPSI0IiBmaWxsPSIjNGNhZjUwIi8+PGNpcmNsZSBjeD0iOTAiIGN5PSI2MCIgcj0iNCIgZmlsbD0iIzRjYWY1MCIvPjwvc3ZnPg=="}]}], "assessment": {"prompts": [{"question": "When a project hits a snag, what's your first action?", "answers": [{"answer": "Assign blame immediately.", "skills": ["Directing", "Accountability", "Consequence application"]}, {"answer": "Gather the team for discussion.", "skills": ["Facilitation", "Problem-solving", "Team engagement"]}, {"answer": "Decide the solution myself.", "skills": ["Decisiveness", "Task orientation", "Efficiency"]}]}, {"question": "An employee's performance dips, how do you start?", "answers": [{"answer": "Document the deficiency.", "skills": ["Record keeping", "Policy adherence", "Formal procedures"]}, {"answer": "Schedule a one-on-one.", "skills": ["Active listening", "Coaching", "Relationship building"]}, {"answer": "Monitor their next projects.", "skills": ["Observation", "Data tracking", "Performance analysis"]}]}, {"question": "A staff member wants to try a new approach, what do you say?", "answers": [{"answer": "Follow the set processes.", "skills": ["Consistency", "Process adherence", "Risk mitigation"]}, {"answer": "Explore the potential impacts.", "skills": ["Innovation", "Critical thinking", "Strategic planning"]}, {"answer": "Refocus on current tasks.", "skills": ["Prioritization", "Time management", "Project focus"]}]}, {"question": "When an individual is leaving, what is your priority?", "answers": [{"answer": "Ensure tasks are completed.", "skills": ["Project management", "Resource allocation", "Continuity"]}, {"answer": "Document their exit interview.", "skills": ["Legal compliance", "HR processes", "Record keeping"]}, {"answer": "Capture lessons learned from them.", "skills": ["Knowledge transfer", "Process Improvement", "Organizational learning"]}]}]}}, {"outline": {"duration": "10 minutes", "level": "Intermediate", "title": "Part 4:  Adjusting to Change with 'Why'", "text": "This section focuses on using the power of asking 'why' to help employees adjust to changes. How can you use questioning techniques to get to the root of employee resistance or discomfort. The goal is to empower employees to actively participate in the process and take ownership of change, rather than feeling passively affected."}, "lessons": [{"title": "1. Understanding the Power of 'Why' in Change Management", "text": "When organizations undergo change, the natural human response is often resistance. As a leader, your role isn't to suppress this resistance but to understand and work through it. The power of asking 'why' lies in its ability to dig beneath surface-level complaints and reveal deeper concerns. For instance, if an employee says, 'I don't like the new software system,' instead of dismissing their concern, ask 'Why do you feel that way?' Their response might reveal that they fear looking incompetent while learning the new system, or they worry about maintaining their productivity during the transition period.", "info": [{"title": "Start with Listening", "text": "Create a safe space for employees to express their concerns about change without judgment.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI2U4YjVjZSIvPjxwYXRoIGQ9Ik0zMCA0MGg0MHY0MEgzMHoiIGZpbGw9IiM4ZWM2ZTYiLz48cGF0aCBkPSJNMjUgMzBoNTB2NUgyNXoiIGZpbGw9IiM5ZmQ4YWIiLz48L3N2Zz4="}, {"title": "Ask 'Why' Effectively", "text": "Use open-ended questions to uncover the root causes of resistance to change.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB4PSIyMCIgeT0iMjAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iI2ZmYjk4MCIvPjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjIwIiBmaWxsPSIjODA4MGZmIi8+PHBhdGggZD0iTTQwIDYwbDIwLTIwIiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMiIvPjwvc3ZnPg=="}, {"title": "Address Root Concerns", "text": "Once underlying fears and worries are identified, develop targeted solutions to address them.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cG9seWdvbiBwb2ludHM9IjUwLDIwIDgwLDgwIDIwLDgwIiBmaWxsPSIjYjVlOGQ0Ii8+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iMTUiIGZpbGw9IiNmZjk2ODAiLz48bGluZSB4MT0iMzAiIHkxPSI3MCIgeDI9IjcwIiB5Mj0iNzAiIHN0cm9rZT0iIzgwODBmZiIgc3Ryb2tlLXdpZHRoPSIzIi8+PC9zdmc+"}]}, {"title": "2. The Five Whys Technique in Practice", "text": "Originally developed by Toyota, the Five Whys technique involves asking 'why' multiple times to get to the root cause of resistance. Consider this real-world example: An employee expresses frustration about a new reporting structure. First why: 'Why does this change bother you?' - 'Because I have to send reports to someone new.' Second why: 'Why is sending reports to someone new concerning?' - 'Because they might not understand my work context.' Third why: 'Why is that understanding important?' - 'Because my projects are complex and need contextual knowledge.' By the fifth why, you might discover the real issue is fear of being misunderstood or undervalued, which can then be directly addressed through proper knowledge transfer and relationship building.", "info": [{"title": "Understanding the Problem", "text": "Start by identifying the initial resistance and asking the first 'why' to understand the surface-level concern. Listen carefully to the response without judgment.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI2U3NGMzYyIvPjx0ZXh0IHg9IjUwIiB5PSI1NSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMjAiPj8/PC90ZXh0Pjwvc3ZnPg=="}, {"title": "Digging Deeper", "text": "Continue asking 'why' to each answer, moving past surface issues to reveal underlying concerns. Each response opens a new layer of understanding.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB4PSIxMCIgeT0iMTAiIHdpZHRoPSI4MCIgaGVpZ2h0PSI4MCIgZmlsbD0iIzNhYWY4NSIvPjxjaXJjbGUgY3g9IjUwIiBjeT0iNTAiIHI9IjIwIiBmaWxsPSIjZjFjNDBmIi8+PC9zdmc+"}, {"title": "Finding the Root Cause", "text": "By the fifth why, you should uncover the fundamental issue - often related to deeper emotional or organizational concerns that can be effectively addressed.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBkPSJNMTAgOTBMNTAgMTBMOTAgOTAiIGZpbGw9IiMyOTgwYjkiLz48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSIxMCIgZmlsbD0iI2U3NGMzYyIvPjwvc3ZnPg=="}]}, {"title": "3. Creating Psychological Safety Through Questioning", "text": "The manner in which you ask 'why' is crucial. The tone should be curious and supportive, never accusatory. Create an environment where employees feel safe expressing their concerns. For example, instead of asking 'Why aren't you on board with this change?' try 'What aspects of this change feel challenging to you?' This approach shows respect for their perspective and encourages honest dialogue. Share your own experiences with change and vulnerability - perhaps a time when you initially resisted a change that ultimately proved beneficial. This transparency helps normalize the adjustment process.", "info": [{"title": "Ask with Curiosity", "text": "Use a supportive, curious tone when asking questions. Replace 'Why aren't you...' with 'What aspects feel...' to show respect.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI2ZmYjRiNCIvPjxwYXRoIGQ9Ik0zMCA2MGMxMCAxMCAzMCAxMCA0MCAwIiBzdHJva2U9IiM2NjY2NjYiIGZpbGw9Im5vbmUiIHN0cm9rZS13aWR0aD0iMyIvPjxjaXJjbGUgY3g9IjM1IiBjeT0iNDAiIHI9IjUiIGZpbGw9IiMzMzMzMzMiLz48Y2lyY2xlIGN4PSI2NSIgY3k9IjQwIiByPSI1IiBmaWxsPSIjMzMzMzMzIi8+PHRleHQgeD0iNTAiIHk9IjgwIiBmb250LXNpemU9IjEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjY2NjY2Ij4/PC90ZXh0Pjwvc3ZnPg=="}, {"title": "Create Safe Spaces", "text": "Foster an environment where employees feel secure sharing their thoughts and concerns openly.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cmVjdCB4PSIyMCIgeT0iMjAiIHdpZHRoPSI2MCIgaGVpZ2h0PSI2MCIgZmlsbD0iIzkwY2FmOSIgcng9IjEwIi8+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iMjAiIGZpbGw9IiNmZmNjODAiLz48cGF0aCBkPSJNMzUgNTBhMTUgMTUgMCAwIDAgMzAgMCIgc3Ryb2tlPSIjZmZmZmZmIiBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjMiLz48L3N2Zz4="}, {"title": "Share Personal Experiences", "text": "Be transparent about your own journey with change to help normalize the adjustment process.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBkPSJNMTAgOTBjMjAtNDAgNjAtNDAgODAgMCIgc3Ryb2tlPSIjYWVkNTgxIiBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjMiLz48Y2lyY2xlIGN4PSIzMCIgY3k9IjMwIiByPSIyMCIgZmlsbD0iI2ZmYTcyNiIvPjxjaXJjbGUgY3g9IjcwIiBjeT0iMzAiIHI9IjIwIiBmaWxsPSIjZmY3MDQzIi8+PHBhdGggZD0iTTQwIDMwYTEwIDEwIDAgMCAwIDIwIDAiIHN0cm9rZT0iI2ZmZmZmZiIgZmlsbD0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIyIi8+PC9zdmc+"}]}, {"title": "4. Empowering Through Collaborative Problem-Solving", "text": "Once you understand the root causes of resistance, involve employees in developing solutions. If an employee expresses concern about new procedures slowing down their work, engage them with questions like 'What modifications would make this process work better for you while still achieving our goals?' This collaborative approach transforms them from passive recipients of change to active participants in shaping implementation. Document these discussions and solutions in performance reviews, acknowledging their contributions to the change process.", "info": [{"title": "Identify Root Causes", "text": "Listen carefully to understand the true sources of employee resistance to change", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48c3R5bGU+LmF7ZmlsbDojNjQ5NWVkO30uYntmaWxsOiNmZjk4MDA7fS5je2ZpbGw6IzRjYWY1MDt9PC9zdHlsZT48Y2lyY2xlIGNsYXNzPSJhIiBjeD0iNTAiIGN5PSI1MCIgcj0iMjAiLz48cGF0aCBjbGFzcz0iYiIgZD0iTTMwIDUwYzAgMCAxMC0xNSA0MC0xNXYzMGMtMzAgMC00MC0xNS00MC0xNXoiLz48cGF0aCBjbGFzcz0iYyIgZD0iTTU1IDY1YzAgMCAxNS01IDI1LTIwbC0xMC01Yy01IDEwLTE1IDE1LTE1IDE1eiIvPjwvc3ZnPg=="}, {"title": "Collaborate on Solutions", "text": "Engage employees in developing and modifying implementation plans to address their concerns", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48c3R5bGU+LmF7ZmlsbDojZTY0YTE5O30uYntmaWxsOiMyMTk2ZjM7fS5je2ZpbGw6IzRjYWY1MDt9PC9zdHlsZT48Y2lyY2xlIGNsYXNzPSJhIiBjeD0iMzAiIGN5PSI1MCIgcj0iMTUiLz48Y2lyY2xlIGNsYXNzPSJiIiBjeD0iNzAiIGN5PSI1MCIgcj0iMTUiLz48cGF0aCBjbGFzcz0iYyIgZD0iTTM1IDQwaDMwdjIwSDM1eiIvPjwvc3ZnPg=="}, {"title": "Document Progress", "text": "Record discussions, solutions and employee contributions in performance reviews", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48c3R5bGU+LmF7ZmlsbDojOWMyN2IwO30uYntmaWxsOiMwMDk2ODg7fS5je2ZpbGw6I2ZmYzEwNzt9PC9zdHlsZT48cmVjdCBjbGFzcz0iYSIgeD0iMjAiIHk9IjIwIiB3aWR0aD0iNjAiIGhlaWdodD0iNjAiLz48cGF0aCBjbGFzcz0iYiIgZD0iTTMwIDQwaDQwdjVIMzB6TTMwIDUwaDQwdjVIMzB6TTMwIDYwaDQwdjVIMzB6Ii8+PHBhdGggY2xhc3M9ImMiIGQ9Ik03MCAyMGwxMCAxMGgtMTB6Ii8+PC9zdmc+"}]}, {"title": "5. Identifying When 'Why' Reveals Deeper Issues", "text": "Sometimes, the 'why' process reveals that an employee's resistance stems from fundamental misalignment with organizational values or direction. In such cases, the conversation might need to shift toward career development or transition planning. For example, if repeated 'why' discussions reveal that an employee fundamentally disagrees with the company's digital transformation strategy, it might be time for honest discussions about whether their long-term career goals align with the organization's future. Handle these situations with empathy while maintaining clear expectations about the non-negotiable aspects of change.", "info": [{"title": "Uncovering Root Causes", "text": "Use the 'why' technique to dig beneath surface-level resistance and identify deeper underlying concerns", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjgwIiBmaWxsPSIjZjVmNWY1Ii8+PGcgc3Ryb2tlPSIjMzQ0OTVlIiBzdHJva2Utd2lkdGg9IjIiIGZpbGw9Im5vbmUiPjxwYXRoIGQ9Ik02MCA4MGg4MG0tODAgMjBoODBtLTgwIDIwaDgwIi8+PC9nPjxwYXRoIGQ9Ik0xMDAgNDBsNDAgNDBoLTgweiIgZmlsbD0iIzNlOTZmMCIvPjwvc3ZnPg=="}, {"title": "Values Alignment Check", "text": "Assess whether resistance indicates a fundamental misalignment between employee values and organizational direction", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48Y2lyY2xlIGN4PSI3MCIgY3k9IjEwMCIgcj0iNDAiIGZpbGw9IiNlNzRjM2MiLz48Y2lyY2xlIGN4PSIxMzAiIGN5PSIxMDAiIHI9IjQwIiBmaWxsPSIjMmVjYzcxIi8+PHBhdGggZD0iTTEwMCA2MHY4MCIgc3Ryb2tlPSIjMzQ0OTVlIiBzdHJva2Utd2lkdGg9IjIiLz48L3N2Zz4="}, {"title": "Empathetic Transition", "text": "When misalignment is identified, facilitate honest career development discussions while maintaining clear expectations", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cGF0aCBkPSJNNDAgMTAwYzAtMzMgMjctNjAgNjAtNjBzNjAgMjcgNjAgNjBjMCAyMC0xMCA0MC0zMCA1MGwtMzAgMjBtMCAwbDMwLTIwIiBzdHJva2U9IiMzNDQ5NWUiIHN0cm9rZS13aWR0aD0iMiIgZmlsbD0ibm9uZSIvPjxjaXJjbGUgY3g9IjEwMCIgY3k9IjgwIiByPSIyMCIgZmlsbD0iI2Y0ZDAwMyIvPjwvc3ZnPg=="}]}], "assessment": {"prompts": [{"question": "An employee is frustrated with a new policy, what do you do?", "answers": [{"answer": "Explore reasons for concerns", "skills": ["problem identification", "active listening", "empathy"]}, {"answer": "Explain the policy rationale", "skills": ["policy knowledge", "persuasion", "clear delivery"]}, {"answer": "State that the policy is final", "skills": ["assertiveness", "decision making", "authority"]}]}, {"question": "An employee says the new process is inefficient. What should be your initial response?", "answers": [{"answer": "Ask them to elaborate further", "skills": ["investigation", "root cause analysis", "observation"]}, {"answer": "Show your support for the process", "skills": ["process knowledge", "positive reinforcement", "encouragement"]}, {"answer": "Note the concern for later", "skills": ["time management", "documentation", "prioritization"]}]}, {"question": "After several conversations it's clear an employee can't accept a new direction, what is the next step?", "answers": [{"answer": "Discuss future opportunities", "skills": ["career coaching", "strategic planning", "talent management"]}, {"answer": "Emphasize current company goals", "skills": ["vision sharing", "goal setting", "accountability"]}, {"answer": "Focus on immediate tasks", "skills": ["task assignment", "workload management", "operational focus"]}]}, {"question": "To fully understand an employee's resistance to change you should:", "answers": [{"answer": "Ask questions repeatedly", "skills": ["inquiry", "persistence", "critical thinking"]}, {"answer": "Explain the need for change", "skills": ["rationale building", "change advocacy", "persuasion"]}, {"answer": "Detail company values", "skills": ["value communication", "culture awareness", "organizational alignment"]}]}]}}, {"outline": {"duration": "12 minutes", "level": "Intermediate", "title": "Part 5: 'Why' and Difficult Conversations: Removing and Moving Employees", "text": "This section explores the importance of using 'why' in difficult situations with employees. We'll look at how asking 'why' can lead to more constructive discussions around performance issues, both for removing employees and moving them into new roles. We will also cover how this approach can be used in exit interviews."}, "lessons": [{"title": "1. Understanding the Power of 'Why' in Employee Discussions", "text": "When faced with difficult conversations about employee performance or transitions, the most powerful tool in a manager's arsenal is often the simple question 'why.' This questioning technique goes beyond surface-level issues to uncover root causes and deeper understanding. For instance, if an employee is consistently missing deadlines, rather than immediately moving to disciplinary action, asking 'Why are you finding it challenging to meet these deadlines?' might reveal underlying issues such as insufficient training, unclear expectations, or personal challenges that can be addressed constructively.", "info": [{"title": "The Power of Asking Why", "text": "Start conversations by asking open-ended questions to understand root causes", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgMjQwIj48Y2lyY2xlIGN4PSIxMjAiIGN5PSIxMjAiIHI9IjkwIiBmaWxsPSIjZjVmNWY1Ii8+PHBhdGggZD0iTTExMCAxNjBjMC0zMCAyMC01MCA1MC01MHM1MCAyMCA1MCA1MC01MCAzMC01MCAzMHoiIGZpbGw9IiM2NmJiNmEiLz48cGF0aCBkPSJNOTAgMTYwYzAtMzAgMjAtNTAgNTAtNTBzNTAgMjAgNTAgNTAtNTAgMzAtNTAgMzB6IiBmaWxsPSIjNDJhNWY1Ii8+PHRleHQgeD0iMTIwIiB5PSIxMDAiIGZvbnQtc2l6ZT0iNjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGZpbGw9IiMzMzMiPj88L3RleHQ+PC9zdmc+"}, {"title": "Deeper Understanding", "text": "Look beyond surface-level issues to identify underlying challenges", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgMjQwIj48cGF0aCBkPSJNNDAgODBjMC0yMiAxOC00MCA0MC00MGg4MGMyMiAwIDQwIDE4IDQwIDQwdjgwYzAgMjItMTggNDAtNDAgNDBoLTgwYy0yMiAwLTQwLTE4LTQwLTQweiIgZmlsbD0iI2ZmYjc0ZCIvPjxjaXJjbGUgY3g9IjEyMCIgY3k9IjEyMCIgcj0iNDAiIGZpbGw9IiNmZmYiLz48Y2lyY2xlIGN4PSIxMjAiIGN5PSIxMjAiIHI9IjIwIiBmaWxsPSIjNDI4NWY0Ii8+PC9zdmc+"}, {"title": "Constructive Solutions", "text": "Address root causes with targeted support and resources", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgMjQwIj48cGF0aCBkPSJNNDAgMTIwbDgwLTgwIDgwIDgwLTgwIDgweiIgZmlsbD0iIzRjYWY1MCIvPjxjaXJjbGUgY3g9IjEyMCIgY3k9IjEyMCIgcj0iMzAiIGZpbGw9IiNmZmYiLz48cGF0aCBkPSJNMTEwIDEyMGwxMC0xMCAxMCAxMC0xMCAxMHoiIGZpbGw9IiMzMzMiLz48L3N2Zz4="}]}, {"title": "2. Applying 'Why' in Performance Reviews", "text": "Performance reviews become more meaningful when structured around 'why' questions. Instead of simply stating 'Your performance isn't meeting expectations,' engage in a dialogue: 'Why do you think there's a gap between expectations and current performance?' This approach often reveals insights that neither party had previously considered. For example, an employee might share that they feel overwhelmed because processes aren't well documented, leading to a solution-focused discussion about improving documentation rather than just focusing on performance metrics.", "info": [{"title": "Ask Deep Questions", "text": "Replace judgmental statements with curious 'why' questions to uncover root causes", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjgwIiBmaWxsPSIjZjVmNWY1Ii8+PHBhdGggZD0iTTcwIDEzMGMwLTMwIDYwLTMwIDYwIDAiIGZpbGw9Im5vbmUiIHN0cm9rZT0iIzY3M2FiNyIgc3Ryb2tlLXdpZHRoPSI2Ii8+PGNpcmNsZSBjeD0iNzAiIGN5PSI4MCIgcj0iMTIiIGZpbGw9IiM2NzNhYjciLz48Y2lyY2xlIGN4PSIxMzAiIGN5PSI4MCIgcj0iMTIiIGZpbGw9IiM2NzNhYjciLz48dGV4dCB4PSI5MCIgeT0iMTYwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iNDAiIGZpbGw9IiM2NzNhYjciPj88L3RleHQ+PC9zdmc+"}, {"title": "Foster Open Dialogue", "text": "Create a safe space for employees to share challenges and obstacles honestly", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cmVjdCB4PSIyMCIgeT0iNDAiIHdpZHRoPSI3MCIgaGVpZ2h0PSI2MCIgcng9IjEwIiBmaWxsPSIjNDJhNWY1Ii8+PHJlY3QgeD0iMTEwIiB5PSIxMDAiIHdpZHRoPSI3MCIgaGVpZ2h0PSI2MCIgcng9IjEwIiBmaWxsPSIjZWM0MDdhIi8+PGNpcmNsZSBjeD0iNTUiIGN5PSIxMjAiIHI9IjIwIiBmaWxsPSIjZmZjMTA3Ii8+PGNpcmNsZSBjeD0iMTQ1IiBjeT0iODAiIHI9IjIwIiBmaWxsPSIjNGNhZjUwIi8+PC9zdmc+"}, {"title": "Focus on Solutions", "text": "Transform insights into actionable improvements and process changes", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48cGF0aCBkPSJNMjAgMTgwTDgwIDQwbDYwIDE0MHoiIGZpbGw9IiM0Y2FmNTAiLz48cGF0aCBkPSJNODAgNDBsMTAwIDYwbC00MCA4MHoiIGZpbGw9IiM4YmMzNGEiLz48Y2lyY2xlIGN4PSI4MCIgY3k9IjQwIiByPSIyMCIgZmlsbD0iI2ZmYzEwNyIvPjxjaXJjbGUgY3g9IjE4MCIgY3k9IjEwMCIgcj0iMjAiIGZpbGw9IiNmZmMxMDciLz48Y2lyY2xlIGN4PSIyMCIgY3k9IjE4MCIgcj0iMjAiIGZpbGw9IiNmZmMxMDciLz48L3N2Zz4="}]}, {"title": "3. Using 'Why' in Role Transitions", "text": "When considering moving employees to new roles, the 'why' framework helps ensure decisions benefit both the organization and the individual. Start with 'Why would this role be a better fit?' and 'Why does this transition make sense now?' These questions help validate the decision and create buy-in. For example, if moving a technical expert into a management role, discussing why they're interested in leadership and why they believe they'd be effective can highlight both opportunities and potential challenges before the transition occurs.", "info": [{"title": "<PERSON><PERSON><PERSON>", "text": "Begin by evaluating why the new role would be a better match for the employee's skills and aspirations", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImEiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM2MkIxRDAiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiMzMzg1RkYiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSIzMCIgZmlsbD0idXJsKCNhKSIvPjxwYXRoIGQ9Ik0zNSA1MGgzME0zNSA0MGgzME0zNSA2MGgzMCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjMiLz48L3N2Zz4="}, {"title": "Consider Timing", "text": "Evaluate why this is the optimal moment for a role transition", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImIiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNGRjk5NjYiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNGRjVFNjIiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSIzMCIgZmlsbD0idXJsKCNiKSIvPjxwYXRoIGQ9Ik01MCAyNXYyNWwxNSAxNSIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjMiIGZpbGw9Im5vbmUiLz48L3N2Zz4="}, {"title": "Validate Decision", "text": "Discuss motivations and potential challenges to ensure alignment and readiness", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImMiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM3QkQ4OEYiLz48c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM1MEIzNjIiLz48L2xpbmVhckdyYWRpZW50PjwvZGVmcz48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSIzMCIgZmlsbD0idXJsKCNjKSIvPjxwYXRoIGQ9Ik0zNSA1MGwxMCAxMGwyMC0yMCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjMiIGZpbGw9Im5vbmUiLz48L3N2Zz4="}]}, {"title": "4. Exit Interviews and the Power of Why", "text": "Exit interviews become invaluable learning opportunities when centered around 'why' questions. 'Why did you start looking for other opportunities?' often reveals organizational blind spots that might not surface in regular feedback channels. A departing employee might explain that they felt their career growth was stagnating, highlighting the need for better career development programs. These insights can help prevent similar departures in the future and improve retention strategies.", "info": [{"title": "Ask the Right Questions", "text": "Focus on 'why' questions during exit interviews to uncover deeper insights into employee decisions", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSIjNjc4OGQxIiBkPSJNNTAgOTBjMjIuMSAwIDQwLTE3LjkgNDAtNDBTNzIuMSAxMCA1MCAxMCAxMCAyNy45IDEwIDUwczE3LjkgNDAgNDAgNDB6Ii8+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTUwIDI1YzMuMyAwIDYgMi43IDYgNnMtMi43IDYtNiA2LTYtMi43LTYtNiAyLjctNiA2LTZ6TTQyIDQ1aDE2djMwSDQyeiIvPjwvc3ZnPg=="}, {"title": "Gather Valuable Insights", "text": "Departing employees often provide honest feedback about organizational blind spots and areas for improvement", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSIjZjViYjQyIiBkPSJNODUgNTBjMCAxOS4zLTE1LjcgMzUtMzUgMzVTMTUgNjkuMyAxNSA1MHMxNS43LTM1IDM1LTM1IDM1IDE1LjcgMzUgMzV6Ii8+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTQ1IDMwaDMwdjQwSDQ1eiIvPjxwYXRoIGZpbGw9IiM0NDQiIGQ9Ik01MCAzNWgyMHY1SDUwek01MCA0NWgyMHY1SDUwek01MCA1NWgyMHY1SDUweiIvPjwvc3ZnPg=="}, {"title": "Improve Retention Strategies", "text": "Use exit interview feedback to enhance career development programs and prevent future departures", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBmaWxsPSIjNGNhZjUwIiBkPSJNMTAgOTBoODBWMTBIMTB6Ii8+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTIwIDcwaDYwTDUwIDMweiIvPjxjaXJjbGUgZmlsbD0iI2ZmZiIgY3g9IjUwIiBjeT0iNDAiIHI9IjUiLz48L3N2Zz4="}]}, {"title": "5. Difficult Conversations and Employee Removal", "text": "When removal becomes necessary, 'why' questions help ensure the decision is well-founded and properly documented. 'Why isn't this working?' and 'Why haven't improvement plans been successful?' create a clear narrative that supports the decision while potentially revealing any overlooked solutions. For instance, if an employee is being removed for poor performance, a thorough exploration of 'why' might reveal systemic issues that need addressing to prevent similar situations with future employees.", "info": [{"title": "Evaluate the Situation", "text": "Ask probing 'why' questions to ensure decisions are well-founded and properly documented", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48ZyBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjIiPjxjaXJjbGUgY3g9IjEwMCIgY3k9IjgwIiByPSI0MCIgZmlsbD0iI2ZmZjBlNiIgc3Ryb2tlPSIjZmY5ZjAwIi8+PHBhdGggZD0iTTgwIDcwaDQwTTgwIDkwaDQwIiBzdHJva2U9IiNmZjlmMDAiLz48dGV4dCB4PSI5MCIgeT0iODUiIGZpbGw9IiNmZjlmMDAiIGZvbnQtc2l6ZT0iMjAiPj88L3RleHQ+PC9nPjwvc3ZnPg=="}, {"title": "Document & Review", "text": "Analyze why improvement plans weren't successful and create clear narrative", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48ZyBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjIiPjxwYXRoIGQ9Ik01MCA0MGgxMDB2MTIwSDUweiIgZmlsbD0iI2U2ZjNmZiIgc3Ryb2tlPSIjMDA3YWZmIi8+PHBhdGggZD0iTTYwIDYwaDgwTTYwIDgwaDgwTTYwIDEwMGg0MCIgc3Ryb2tlPSIjMDA3YWZmIi8+PC9nPjwvc3ZnPg=="}, {"title": "Address Root Causes", "text": "Identify any systemic issues that could prevent similar situations in the future", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMDAgMjAwIj48ZyBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjIiPjxwYXRoIGQ9Ik01MCA4MHY4MGgxMDBWODAiIGZpbGw9IiNlNmZmZjAiIHN0cm9rZT0iIzAwZmY3ZiIvPjxwYXRoIGQ9Ik03MCA0MGw2MCA0MCIgc3Ryb2tlPSIjMDBmZjdmIi8+PHBhdGggZD0iTTEzMCA0MGwtNjAgNDAiIHN0cm9rZT0iIzAwZmY3ZiIvPjwvZz48L3N2Zz4="}]}], "assessment": {"prompts": [{"question": "When an employee is underperforming, what is a useful initial step?", "answers": [{"answer": "Investigate contributing circumstances", "skills": ["performance review", "help employees adjust", "teaching to ask why"]}, {"answer": "Begin formal warnings", "skills": ["removing employees"]}, {"answer": "Redesign their role", "skills": ["move employees on"]}]}, {"question": "During exit discussions, focusing on 'why' helps uncover:", "answers": [{"answer": "Employee's next job plan", "skills": []}, {"answer": "Organizational areas for improvement", "skills": ["removing employees", "teaching to ask why"]}, {"answer": "Their performance successes", "skills": ["performance review"]}]}, {"question": "When deciding on a role change, what crucial question is important to address?", "answers": [{"answer": "What is their career ambition?", "skills": []}, {"answer": "Does this align for them?", "skills": ["move employees on", "teaching to ask why"]}, {"answer": "What are our expectations?", "skills": ["performance review"]}]}, {"question": "When performance issues arise, understanding the root of the problem through questioning allows you to:", "answers": [{"answer": "Adjust the workload", "skills": ["help employees adjust"]}, {"answer": "Develop effective resolutions", "skills": ["teaching to ask why", "performance review", "help employees adjust"]}, {"answer": "Consider if removal is needed", "skills": ["removing employees"]}]}]}}, {"outline": {"duration": "10 minutes", "level": "Intermediate", "title": "Part 6: The Manager's 'Why' -  Leading with <PERSON><PERSON><PERSON>ity", "text": "Finally, we will focus on you, the manager. How can you use curiosity in your own daily work? We'll explore how to use 'why' to become a better leader by understanding your teams’ needs and challenges, driving problem-solving, and fostering a collaborative environment built on trust and active questioning. "}, "lessons": [{"title": "1. Understanding Your Leadership Through Why", "text": "As managers, our effectiveness stems not just from what we do, but from understanding why we do it. This introspective approach begins with creating what's known as a 'self user manual' - a document that explains your leadership style, preferences, and decision-making process. For example, you might document that you prefer direct communication over hints, or that you process information better through written reports rather than verbal updates. When you share this manual with your team, it creates transparency and sets clear expectations for interactions.", "info": [{"title": "Define Your Leadership Philosophy", "text": "Start by examining your core values and beliefs about leadership. What drives your decision-making and approach to management?", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQxIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj48c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNDA4OGYyIi8+PHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMmE1OWE0Ii8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iNDAiIGZpbGw9InVybCgjZ3JhZDEpIi8+PGNpcmNsZSBjeD0iNTAiIGN5PSIzNSIgcj0iMTUiIGZpbGw9IiNmZmYiLz48cGF0aCBkPSJNMjUgNjVjMC0yMCAyNS0xNSA1MCAwIiBmaWxsPSJub25lIiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMyIvPjwvc3ZnPg=="}, {"title": "Document Your Style", "text": "Create your 'self user manual' by documenting your communication preferences, decision-making process, and work style.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQyIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj48c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjI3MTQxIi8+PHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZDQ1NzJjIi8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PHJlY3QgeD0iMjAiIHk9IjIwIiB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIGZpbGw9InVybCgjZ3JhZDIpIi8+PGxpbmUgeDE9IjMwIiB5MT0iMzUiIHgyPSI3MCIgeTI9IjM1IiBzdHJva2U9IiNmZmYiIHN0cm9rZS13aWR0aD0iMyIvPjxsaW5lIHgxPSIzMCIgeTE9IjUwIiB4Mj0iNzAiIHkyPSI1MCIgc3Ryb2tlPSIjZmZmIiBzdHJva2Utd2lkdGg9IjMiLz48bGluZSB4MT0iMzAiIHkxPSI2NSIgeDI9IjcwIiB5Mj0iNjUiIHN0cm9rZT0iI2ZmZiIgc3Ryb2tlLXdpZHRoPSIzIi8+PC9zdmc+"}, {"title": "Share and Iterate", "text": "Share your manual with your team to foster understanding and gather feedback for continuous improvement.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48bGluZWFyR3JhZGllbnQgaWQ9ImdyYWQzIiB4MT0iMCUiIHkxPSIwJSIgeDI9IjEwMCUiIHkyPSIxMDAlIj48c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojNDFmMjcxIi8+PHN0b3Agb2Zmc2V0PSIxMDAlIiBzdHlsZT0ic3RvcC1jb2xvcjojMmNkNDU3Ii8+PC9saW5lYXJHcmFkaWVudD48L2RlZnM+PGNpcmNsZSBjeD0iMzAiIGN5PSI1MCIgcj0iMjAiIGZpbGw9InVybCgjZ3JhZDMpIi8+PGNpcmNsZSBjeD0iNzAiIGN5PSI1MCIgcj0iMjAiIGZpbGw9InVybCgjZ3JhZDMpIi8+PHBhdGggZD0iTTUwIDMwYzAgMjAgMCAyMCAwIDQwIiBmaWxsPSJub25lIiBzdHJva2U9InVybCgjZ3JhZDMpIiBzdHJva2Utd2lkdGg9IjMiLz48L3N2Zz4="}]}, {"title": "2. Applying Curiosity to Team Development", "text": "The power of 'why' becomes particularly evident in team development. Instead of immediately jumping to solutions when problems arise, start by asking 'Why is this happening?' Consider a scenario where team productivity is declining. Rather than immediately implementing new processes, ask: Why are deadlines being missed? Why do team members seem less engaged? These questions often reveal root causes like resource constraints or communication gaps that wouldn't be apparent from surface-level observations.", "info": [{"title": "Ask Deeper Questions", "text": "Begin with curiosity by asking 'Why is this happening?' rather than jumping to quick solutions", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48ZyBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjMiPjxjaXJjbGUgY3g9IjEyOCIgY3k9IjEyOCIgcj0iNjAiIHN0cm9rZT0iIzY3NzdlZSIvPjx0ZXh0IHg9IjEyOCIgeT0iMTQwIiBmb250LXNpemU9IjgwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNjc3N2VlIj4/PC90ZXh0PjwvZz48L3N2Zz4="}, {"title": "Examine Root Causes", "text": "Look beyond surface-level issues to understand underlying factors affecting team performance", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48ZyBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjIiPjxwYXRoIGQ9Ik0xMjggNDAgdjE3NiIgc3Ryb2tlPSIjNDRhYTk5Ii8+PHBhdGggZD0iTTEyOCAyMTYgbDIwIC0yMCBNMTI4IDIxNiBsLTIwIC0yMCIgc3Ryb2tlPSIjNDRhYTk5Ii8+PGNpcmNsZSBjeD0iMTI4IiBjeT0iNDAiIHI9IjIwIiBzdHJva2U9IiM0NGFhOTkiIGZpbGw9IiNmZmYiLz48L2c+PC9zdmc+"}, {"title": "Identify Solutions", "text": "Address discovered issues like resource constraints and communication gaps with targeted solutions", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48ZyBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjMiPjxwYXRoIGQ9Ik02NCA5NiBsMTI4IDY0IiBzdHJva2U9IiNmZjg4NDQiLz48Y2lyY2xlIGN4PSI2NCIgY3k9Ijk2IiByPSIyMCIgc3Ryb2tlPSIjZmY4ODQ0IiBmaWxsPSIjZmZmIi8+PGNpcmNsZSBjeD0iMTkyIiBjeT0iMTYwIiByPSIyMCIgc3Ryb2tlPSIjZmY4ODQ0IiBmaWxsPSIjZmZmIi8+PC9nPjwvc3ZnPg=="}]}, {"title": "3. Managing Performance Through Curiosity", "text": "Performance reviews become more effective when driven by curiosity rather than judgment. Instead of stating 'Your project was late,' ask 'What challenges did you encounter that led to the delay?' This approach transforms the conversation from criticism to collaborative problem-solving. When an employee struggles with a task, asking 'Why do you find this challenging?' helps identify whether the issue is related to skills, resources, or other factors that you can address together.", "info": [{"title": "Ask Open Questions", "text": "Replace judgmental statements with curious questions to understand root causes", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSI1MCIgY3k9IjUwIiByPSI0MCIgZmlsbD0iI2U2YjhmMSIvPjxwYXRoIGQ9Ik00MCA2NWMxMC0xNSAyMC0xNSAzMCAwIiBzdHJva2U9IiM2MzM5OTEiIGZpbGw9Im5vbmUiIHN0cm9rZS13aWR0aD0iMyIvPjxjaXJjbGUgY3g9IjM1IiBjeT0iNDAiIHI9IjUiIGZpbGw9IiM2MzM5OTEiLz48Y2lyY2xlIGN4PSI2NSIgY3k9IjQwIiByPSI1IiBmaWxsPSIjNjMzOTkxIi8+PHRleHQgeD0iNTAiIHk9IjgwIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMjAiIHRleHQtYW5jaG9yPSJtaWRkbGUiPj88L3RleHQ+PC9zdmc+"}, {"title": "Foster Collaboration", "text": "Transform criticism into collaborative problem-solving discussions", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48Y2lyY2xlIGN4PSIzMCIgY3k9IjUwIiByPSIyMCIgZmlsbD0iIzkyZDNmNSIvPjxjaXJjbGUgY3g9IjcwIiBjeT0iNTAiIHI9IjIwIiBmaWxsPSIjZjQ5YjkxIi8+PHBhdGggZD0iTTQwIDUwaDIwIiBzdHJva2U9IiM0YTRhNGEiIHN0cm9rZS13aWR0aD0iMyIvPjx0ZXh0IHg9IjUwIiB5PSI4MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjE1IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj5TeW5lcmd5PC90ZXh0Pjwvc3ZnPg=="}, {"title": "Identify Root Causes", "text": "Explore whether challenges stem from skills gaps, resource limitations, or other factors", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48cGF0aCBkPSJNMTAgOTBjMjAtNDAgNjAtNDAgODAgMCIgZmlsbD0iI2E4ZTZjZSIvPjxwYXRoIGQ9Ik0zMCA0MGwyMC0yMCAyMCAyME01MCAyMHY0MCIgc3Ryb2tlPSIjMmI4YTNlIiBmaWxsPSJub25lIiBzdHJva2Utd2lkdGg9IjMiLz48dGV4dCB4PSI1MCIgeT0iOTUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxMCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+Um9vdCBDYXVzZTwvdGV4dD48L3N2Zz4="}]}, {"title": "4. Facilitating Career Transitions", "text": "Sometimes, the most important 'why' questions arise when considering an employee's future. If someone is struggling in their role, asking 'Why isn't this position a good fit?' can lead to productive discussions about career paths. This might reveal that an employee's skills and interests align better with a different role within the organization. For instance, a developer might realize they're more passionate about project management after exploring why certain aspects of their current role feel unfulfilling.", "info": [{"title": "Identify Misalignment", "text": "Recognize when an employee's current role may not be the best fit by observing performance and engagement levels", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48ZyBmaWxsPSJub25lIiBzdHJva2UtbGluZWNhcD0icm91bmQiPjxwYXRoIGQ9Ik0xMjggNjBjMTUgMCAyNyAxMiAyNyAyN3MtMTIgMjctMjcgMjctMjctMTItMjctMjcgMTItMjcgMjctMjd6IiBmaWxsPSIjZmY5ZWFhIiBzdHJva2U9IiNlYjZlN2QiIHN0cm9rZS13aWR0aD0iNCIvPjxwYXRoIGQ9Ik0xMjggMTQwYzMwIDAgNTQgMjQgNTQgNTRIMTI4di01NHoiIGZpbGw9IiM5MGNhZjkiIHN0cm9rZT0iIzY0YjVmNiIgc3Ryb2tlLXdpZHRoPSI0Ii8+PHBhdGggZD0iTTEyOCAxNDBjLTMwIDAtNTQgMjQtNTQgNTRoNTR2LTU0eiIgZmlsbD0iI2ZmYjc0ZCIgc3Ryb2tlPSIjZmY5ODAwIiBzdHJva2Utd2lkdGg9IjQiLz48L2c+PC9zdmc+"}, {"title": "Explore Career Interests", "text": "Have open discussions about career aspirations and areas where the employee feels most energized", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48ZyBmaWxsPSJub25lIiBzdHJva2UtbGluZWNhcD0icm91bmQiPjxwYXRoIGQ9Ik02MCA2MGgxMzZ2MTM2SDYweiIgZmlsbD0iI2E1ZDZhNyIgc3Ryb2tlPSIjODFjNzg0IiBzdHJva2Utd2lkdGg9IjQiLz48cGF0aCBkPSJNOTAgOTBsMzAgMzBtMC0zMGwtMzAgMzAiIHN0cm9rZT0iIzRjYWY1MCIgc3Ryb2tlLXdpZHRoPSI4Ii8+PHBhdGggZD0iTTE0MCAxNDBsMzAgMzBtMC0zMGwtMzAgMzAiIHN0cm9rZT0iIzRjYWY1MCIgc3Ryb2tlLXdpZHRoPSI4Ii8+PC9nPjwvc3ZnPg=="}, {"title": "Facilitate Transition", "text": "Support the move to a better-aligned role while ensuring skills and experience transfer effectively", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNTYgMjU2Ij48ZyBmaWxsPSJub25lIiBzdHJva2UtbGluZWNhcD0icm91bmQiPjxwYXRoIGQ9Ik00MCA4MGgxNzZ2OTZINDBjLTggMC0xNi04LTE2LTE2Vjk2YzAtOCA4LTE2IDE2LTE2eiIgZmlsbD0iI2JiZGVmYiIgc3Ryb2tlPSIjNjRiNWY2IiBzdHJva2Utd2lkdGg9IjQiLz48cGF0aCBkPSJNODAgMTEybDMyIDMyIDY0LTY0IiBzdHJva2U9IiMyMTk2ZjMiIHN0cm9rZS13aWR0aD0iOCIvPjwvZz48L3N2Zz4="}]}, {"title": "5. Building a Culture of Inquiry", "text": "Teaching your team to embrace 'why' questions creates a more innovative and solution-focused environment. Model this behavior by regularly asking questions like 'Why do we follow this process?' or 'Why might our customers feel this way?' Encourage team members to question assumptions and explore alternatives. This culture of inquiry leads to better problem-solving and more engaged employees. For example, a team meeting might evolve from a status update into a brainstorming session when someone asks 'Why are we approaching this project this way?'", "info": [{"title": "<PERSON>", "text": "Create an environment where asking 'why' is encouraged and valued. Model curiosity by regularly questioning processes and assumptions.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZyBmaWxsPSJub25lIiBzdHJva2U9IiM0NDQ0NDQiIHN0cm9rZS13aWR0aD0iMiI+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iMzAiIGZpbGw9IiM2N0IyRTEiLz48dGV4dCB4PSI0NSIgeT0iNTUiIGZvbnQtc2l6ZT0iMjQiIGZpbGw9IiNGRkZGRkYiPj88L3RleHQ+PC9nPjwvc3ZnPg=="}, {"title": "Challenge Assumptions", "text": "Empower team members to question established practices and explore alternative solutions through open dialogue.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZyBmaWxsPSJub25lIiBzdHJva2U9IiM0NDQ0NDQiIHN0cm9rZS13aWR0aD0iMiI+PGNpcmNsZSBjeD0iMzAiIGN5PSI1MCIgcj0iMjAiIGZpbGw9IiNGRjk5MDAiLz48Y2lyY2xlIGN4PSI3MCIgY3k9IjUwIiByPSIyMCIgZmlsbD0iIzY3QjJFMSIvPjxsaW5lIHgxPSI0MCIgeTE9IjUwIiB4Mj0iNjAiIHkyPSI1MCIgc3Ryb2tlPSIjNDQ0NDQ0Ii8+PC9nPjwvc3ZnPg=="}, {"title": "Drive Innovation", "text": "Transform routine discussions into creative problem-solving sessions by encouraging thoughtful questioning and exploration.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZyBmaWxsPSJub25lIiBzdHJva2U9IiM0NDQ0NDQiIHN0cm9rZS13aWR0aD0iMiI+PGNpcmNsZSBjeD0iNTAiIGN5PSI1MCIgcj0iMjUiIGZpbGw9IiM2N0IyRTEiLz48cGF0aCBkPSJNNTAgMjVMMzUgNDVINjVMNTAgMjUiIGZpbGw9IiNGRjk5MDAiLz48cGF0aCBkPSJNNTAgNzVMMzUgNTVINjVMNTAgNzUiIGZpbGw9IiNGRjk5MDAiLz48L2c+PC9zdmc+"}]}, {"title": "6. Making Difficult Decisions", "text": "When facing the challenging decision of removing an employee, curiosity remains crucial. Ask yourself: Why isn't this working? Why have previous interventions failed? These questions ensure you've explored all options and can articulate clear reasons for your decision. They also help you learn from the situation to prevent similar issues in the future. For instance, understanding why a hire didn't work out might reveal gaps in your onboarding process or misalignments in job descriptions that you can address going forward.", "info": [{"title": "Ask Key Questions", "text": "Begin by examining the situation with curiosity. Ask 'Why isn't this working?' and explore root causes.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgMjQwIj48cGF0aCBmaWxsPSIjNDA0MEZGIiBkPSJNMTIwLDIwYzU1LjIsMCwxMDAsNDQuOCwxMDAsMTAwcy00NC44LDEwMC0xMDAsMTAwUzIwLDE3NS4yLDIwLDEyMFM2NC44LDIwLDEyMCwyMHoiLz48cGF0aCBmaWxsPSIjRkZGIiBkPSJNMTIwLDE4MGMtMzMuMSwwLTYwLTI2LjktNjAtNjBzMjYuOS02MCw2MC02MHM2MCwyNi45LDYwLDYwUzE1My4xLDE4MCwxMjAsMTgweiIvPjx0ZXh0IHg9IjEyMCIgeT0iMTMwIiBmb250LXNpemU9IjYwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjNDA0MEZGIj4/PC90ZXh0Pjwvc3ZnPg=="}, {"title": "Review Past Interventions", "text": "Analyze why previous attempts to address the situation have not succeeded.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgMjQwIj48cGF0aCBmaWxsPSIjRkY0MDQwIiBkPSJNNDAsMjAwVjQwaDQwdjE2MEg0MHogTTEwMCwyMDBWODBoNDB2MTIwSDEwMHogTTE2MCwyMDBWMTIwaDQwdjgwSDE2MHoiLz48cGF0aCBmaWxsPSIjRkZGRkZGIiBkPSJNMjAsMjIwVjIwMGgyMDB2MjBIMjB6Ii8+PC9zdmc+"}, {"title": "Learn for the Future", "text": "Use insights gained to improve processes like onboarding and job descriptions going forward.", "image": "data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNDAgMjQwIj48cGF0aCBmaWxsPSIjNDBGRjQwIiBkPSJNMjAsOTBjMC0zOC43LDMxLjMtNzAsNzAtNzBzNzAsMzEuMyw3MCw3MEg4MHoiLz48cGF0aCBmaWxsPSIjNDA0MEZGIiBkPSJNODAsMTUwYzAtMzguNywzMS4zLTcwLDcwLTcwczcwLDMxLjMsNzAsNzBIMTQweiIvPjxjaXJjbGUgZmlsbD0iI0ZGRiIgY3g9IjEyMCIgY3k9IjEyMCIgcj0iMjAiLz48L3N2Zz4="}]}], "assessment": {"prompts": [{"question": "What establishes clear expectations with your team?", "answers": [{"answer": "Documented style and preferences", "skills": ["creating self user manual", "transparency"]}, {"answer": "Immediate solution implementation", "skills": ["skips root cause analysis"]}, {"answer": "Direct criticism of mistakes", "skills": ["uses judgement", "not collaborative"]}]}, {"question": "When a team's output drops, what's a good first action?", "answers": [{"answer": "Investigate root causes first", "skills": ["asking why", "problem identification"]}, {"answer": "New process implementation", "skills": ["skips root cause analysis", "solution focused"]}, {"answer": "More frequent status updates", "skills": ["monitors activity", "does not explore why"]}]}, {"question": "What's a helpful approach during performance reviews?", "answers": [{"answer": "Discuss obstacles encountered", "skills": ["collaborative problem-solving", "performance review", "asking why"]}, {"answer": "Focus on past failures", "skills": ["judgement focused", "non-collaborative"]}, {"answer": "Assign tasks to improve", "skills": ["solution focused", "does not explore root cause"]}]}, {"question": "When an employee is struggling what is a key consideration?", "answers": [{"answer": "Explore position fit first", "skills": ["consider future", "asking why", "career paths"]}, {"answer": "Mandate specific training", "skills": ["solution focused", "skips investigation"]}, {"answer": "Focus on skill deficiencies", "skills": ["judgement focused", "non-collaborative"]}]}, {"question": "If termination seems likely, what should you do?", "answers": [{"answer": "Analyze all intervention failures", "skills": ["asking why", "lessons learned", "prevent future issues"]}, {"answer": "Document clear expectations", "skills": ["not collaborative", "focus on past failures"]}, {"answer": "Implement immediate replacement", "skills": ["not collaborative", "skips analysis"]}]}]}}]}