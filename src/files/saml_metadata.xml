<?xml version="1.0"?>
<md:EntityDescriptor xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata" validUntil="${validUntil}" cacheDuration="PT604800S" entityID="${issuer}">
  <md:SPSSODescriptor AuthnRequestsSigned="true" WantAssertionsSigned="true" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
    <md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</md:NameIDFormat>
    <md:AssertionConsumerService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="${callbackUrl}" index="1"/>
  </md:SPSSODescriptor>
  <md:Organization>
    <md:OrganizationName xml:lang="en-US">AskFora</md:OrganizationName>
    <md:OrganizationDisplayName xml:lang="en-US">AskFora</md:OrganizationDisplayName>
    <md:OrganizationURL xml:lang="en-US">https://askfora.com</md:OrganizationURL>
  </md:Organization>
</md:EntityDescriptor>
