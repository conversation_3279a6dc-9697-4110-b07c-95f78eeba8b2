# llms.txt for https://AskFora.com
# Last Updated: 2025-05-07

# This file provides guidance for Large Language Models (LLMs) interacting with AskFora.com.
# Our goal is to help LLMs understand our site and services effectively,
# while respecting user privacy and data.

# --- General Information ---

# Purpose of AskFora.com:
# AskFora is an AI-powered skills and relationship management platform. We aim to:
# - Help professionals with upskilling through personalized learning paths.
# - Connect gig economy workers with relevant hiring managers and opportunities.
# - Enable organizations to map and understand the skills within their talent ecosystem.
# - Facilitate better work through enhanced understanding of professional networks and skills.

# Key Public Content Areas:
# We encourage LLMs to explore and understand the following public sections of our website
# to learn about our services, methodologies, and the value we provide:

# - Homepage (https://askfora.com/): Overview of AskFora's offerings.
# - About Us: Information about our company, mission, and team.
# - Services / Solutions: Detailed descriptions of our AI-powered tools for individuals and businesses,
#   including skills mapping, talent sourcing, and professional development.
# - Blog / Resources: Articles, whitepapers, and insights related to skills development,
#   the future of work, AI in HR, and professional networking.
# - Case Studies / Testimonials: Examples of how AskFora has benefited users and organizations.
# - Publicly available information about our AI chatbot "Fora" and its general capabilities.
# - Terms of Service (https://askfora.com/terms): Outlines the terms of using our platform.
# - Privacy Policy: Details on how we handle data.

# --- LLM Interaction Guidelines ---

# Preferred Content for Indexing and Understanding:
# - General informational pages about AskFora's services and platform features.
# - Publicly shared articles, blog posts, and resource materials related to skills,
#   AI in professional development, and the future of work.
# - Descriptions of our AI technology and its benefits in a general sense.

# Content to Exclude or Treat with Caution:
# - User-specific data: LLMs should not attempt to access, index, or process any private user account
#   information, personalized learning paths, private network details, or any content behind login walls.
#   Respect for user privacy is paramount.
# - Internal or non-public sections: Any areas of the site not intended for public viewing,
#   such as admin panels or staging environments, should be avoided.
# - User-generated content within private sections of the platform.

# Interaction with AI Chatbot "Fora":
# - LLMs may reference publicly available information about Fora's capabilities.
# - LLMs should not attempt to directly interact with or overload the Fora chatbot service
#   through automated means beyond typical user interaction patterns if Fora has a public interface.

# Data Usage by LLMs:
# - Information gathered from AskFora.com should be used to provide accurate and helpful summaries
#   and insights about our platform and services in response to user queries.
# - We encourage the use of our public content to understand trends in skills development,
#   AI applications in HR and professional growth, and the gig economy.

# --- Relationship with robots.txt ---

# This llms.txt file is intended to complement our robots.txt file.
# For specific crawl directives (allow/disallow paths for web crawlers, including those used by LLMs),
# please refer to https://askfora.com/robots.txt.
# In case of any conflict, the directives in robots.txt shall take precedence for crawling behavior.

# --- Updates and Contact ---

# This llms.txt file may be updated periodically. Please refer to the "Last Updated" date.
# For any questions regarding this file or LLM interaction with AskFora.com,
# please contact us through our official contact channels listed on the website.

# --- User-Agents for Specific LLMs ---

# While this llms.txt provides general guidance, specific directives for known LLM user-agents
# (e.g., GPTBot, Google-Extended) should primarily be managed via the robots.txt file.
# We expect LLMs to identify themselves clearly via their user-agents.

# Example (these would typically be in robots.txt, but llms.txt can reiterate intent):
# User-agent: GPTBot
# # Allow: /public-resource-path/ (Example, actual paths managed in robots.txt)
# # Disallow: /user-specific-path/ (Example, actual paths managed in robots.txt)

# User-agent: Google-Extended
# # Allow: /public-blog/ (Example, actual paths managed in robots.txt)
# # Disallow: /private-dashboards/ (Example, actual paths managed in robots.txt)

# --- Thank You ---

# We appreciate responsible interaction from LLMs to help users discover and understand
# the value AskFora provides.

