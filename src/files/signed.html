<html>
  <head>
    <title>AskFora Contract Signed</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, minimal-ui, user-scalable=no viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="mobile-web-app-capable" content="yes"/>
    <style type="text/css">
      body { 
        font: 14px "Lucida Grande", Helvetica, Arial, sans-serif;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.87);
        background-color: rgb(249, 253, 255);
        line-height: 1.2;
        overflow:hidden;
        -webkit-overflow-scrolling:touch; 
      }
      p {
        border-radius:16px;
        border:1px solid rgba(34, 36, 38, 0.15);
        padding:14px;
        text-align:left;
        width:350px;
      }
      @keyframes lds-rolling {
        0% {
          -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          -webkit-transform: translate(-50%, -50%) rotate(360deg);
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
      @-webkit-keyframes lds-rolling {
        0% {
          -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          -webkit-transform: translate(-50%, -50%) rotate(360deg);
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
      .lds-rolling {
        position: relative;
      }
      .lds-rolling div,
      .lds-rolling div:after {
        position: absolute;
        width: 54px;
        height: 54px;
        border: 6px solid #7b7b7b;
        border-top-color: #efefef;
        border-radius: 50%;
      }
      .lds-rolling div {
        -webkit-animation: lds-rolling 0.6s linear infinite;
        animation: lds-rolling 0.6s linear infinite;
        top: 100px;
        left: 100px;
      }
      .lds-rolling div:after {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
      }
      .lds-rolling {
        width: 100px !important;
        height: 100px !important;
        -webkit-transform: translate(-50px, -50px) scale(0.50) translate(50px, 50px);
        transform: translate(-50px, -50px) scale(0.50) translate(50px, 50px);
      }

    </style>
  </head>
  <body>
    <center>
      <div style='height:100px'>&nbsp;</div>
      <img src='/images/about/fora.jpg' style='height:100px;width:100px'/>
      <p style="width: fit-content; background-color: white; margin: unset; min-width: 150px; text-align: center" id='label'>&hellip;</p>
      <div class="lds-css ng-scope"><div style="width:'calc(100%)';height:'calc(100%)'" class="lds-rolling"><div></div></div></div>
    </center>
  </body>
</html>

