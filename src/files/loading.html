<html>
  <head>
    <script src="https://bundle.run/buffer@6.0.3"></script>
    <script>
    globalThis.user = {email: '${email}'};
    let attempt = 0;

    // <!-- Google Tag Manager -->
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-53WKMNR');
    // <!-- End Google Tag Manager -->

    // <!-- Google Tag Manager -->
    (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
    j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
    'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','G-V6WD08Y4KV');

    // <!-- End Google Tag Manager -->
    function safeObj(o) {
      return buffer.Buffer.from(encodeURIComponent(JSON.stringify(o)).replace(/%([0-9A-F]{2})/g, (m, p) => {
        return String.fromCharCode(parseInt(p, 16)); })).toString('base64');
    }

    function checkLoad() {
      attempt++;
      console.log(`checking settings ${settings} ${attempt}`);
      if ('${settings}'.length) {
        fetch('${settings}', {
          method:'GET',
          mode: 'cors',
          credentials:'include',
        }).then(r => {
          if (r.ok) {
            console.log('got settings');
            return r.json();
          }
          throw new Error('Error loading settings');
        }).then(settings => {
          if (localStorage) {
            console.log('saving settings', settings);
            try {
              localStorage.setItem('settings', safeObj(settings));
              console.log(`auth ${settings.authenticated}`);
              if (settings.authenticated) {
                localStorage.setItem('login', 'true');
                console.log(`login set`);
              } else {
                localStorge.removeItem('login');
                console.log(`login removed`);
              }
            } catch(e) {
              console.log('error saving settings', e);
            }
            console.log('success ${redirect}');
            setTimeout(() => window.location.href = '${redirect}', 1000);
          } else {
            console.log('no local storage');
          }
        }).catch(e => {
          console.log('error loading settings', e);
          // wait max 60s
          if (attempt === 60) {
            console.log('timeout ${redirect}');
            setTimeout(() => window.location.href = '${redirect}', 100);
          } else setTimeout(checkLoad, 1000);
        });
      }
      else {
        console.log('no settings ${redirect}');
        setTimeout(() => window.location.href = '${redirect}', 100);
      }
    }

    setTimeout(checkLoad, 5000);

    let msg = ' I\'m verifying your account. This may take a minute... ';
    if ('${settings}'.length) msg = ' Getting everything ready for you. Give me a minute.'

    function showMsg(e = null, index = 0) {
      const label = document.getElementById('label');
      if (label) label.innerText = msg;
    }

    window.onload = showMsg;

    </script>
    <!-- Event snippet for Sign-up conversion page -->
    <!--script async src="https://www.googletagmanager.com/gtag/js?id=AW-*********"></script-->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-V6WD08Y4KV"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      // gtag('config', 'AW-*********');
      // gtag('config', 'UA-*********-1');
      gtag('config', 'G-V6WD08Y4KV');
      // gtag('event', 'conversion', {'send_to': 'AW-*********/0t1nCNOD-4ACEIfIocMB'}); // Sign-up
      gtag('event', 'conversion', {'send_to': 'AW-*********/jbL3CLuCouwCEIfIocMB'}); // Login
    </script>

    <!-- script>
      // !function(q,e,v,n,t,s){if(q.qp) return; n=q.qp=function(){n.qp?n.qp.apply(n,arguments):n.queue.push(arguments);}; n.queue=[];t=document.createElement(e);t.async=!0;t.src=v; s=document.getElementsByTagName(e)[0]; s.parentNode.insertBefore(t,s);}(window, 'script', 'https://a.quora.com/qevents.js');
      // qp('init', 'c21428a0be854b9b8a2d308b7a899c39');
      // qp('track', 'CompleteRegistration');
    </script-->

    <title>AskFora Loading</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, minimal-ui, user-scalable=no viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes"/>
    <meta name="mobile-web-app-capable" content="yes"/>
    <style type="text/css">
      body { 
        font: 14px "Lucida Grande", Helvetica, Arial, sans-serif;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.87);
        background-color: rgb(249, 253, 255);
        line-height: 1.2;
        overflow:hidden;
        -webkit-overflow-scrolling:touch; 
      }
      p {
        border-radius:16px;
        border:1px solid rgba(34, 36, 38, 0.15);
        padding:14px;
        text-align:left;
        width:350px;
      }
      @keyframes lds-rolling {
        0% {
          -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          -webkit-transform: translate(-50%, -50%) rotate(360deg);
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
      @-webkit-keyframes lds-rolling {
        0% {
          -webkit-transform: translate(-50%, -50%) rotate(0deg);
          transform: translate(-50%, -50%) rotate(0deg);
        }
        100% {
          -webkit-transform: translate(-50%, -50%) rotate(360deg);
          transform: translate(-50%, -50%) rotate(360deg);
        }
      }
      .lds-rolling {
        position: relative;
      }
      .lds-rolling div,
      .lds-rolling div:after {
        position: absolute;
        width: 54px;
        height: 54px;
        border: 6px solid #7b7b7b;
        border-top-color: #efefef;
        border-radius: 50%;
      }
      .lds-rolling div {
        -webkit-animation: lds-rolling 0.6s linear infinite;
        animation: lds-rolling 0.6s linear infinite;
        top: 100px;
        left: 100px;
      }
      .lds-rolling div:after {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
      }
      .lds-rolling {
        width: 100px !important;
        height: 100px !important;
        -webkit-transform: translate(-50px, -50px) scale(0.50) translate(50px, 50px);
        transform: translate(-50px, -50px) scale(0.50) translate(50px, 50px);
      }

    </style>
  </head>
  <body>
    <center>
      <div style='height:100px'>&nbsp;</div>
      <img src='/images/about/fora.jpg' style='height:100px;width:100px'/>
      <p style="width: fit-content; background-color: white; margin: unset; min-width: 150px; text-align: center" id='label'>&hellip;</p>
      <div class="lds-css ng-scope"><div style="width:'calc(100%)';height:'calc(100%)'" class="lds-rolling"><div></div></div></div>
    </center>
  </body>
</html>
