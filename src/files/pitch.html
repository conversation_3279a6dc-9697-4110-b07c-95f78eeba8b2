<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>AskFora Pitch Deck</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            overflow: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        .slide-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 40px;
            position: relative;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
        }

        .slide h1 {
            font-size: 3.5rem;
            color: white;
            margin-bottom: 20px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .slide h2 {
            font-size: 2.8rem;
            color: white;
            margin-bottom: 30px;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .slide h3 {
            font-size: 2rem;
            color: white;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .slide p {
            font-size: 1.3rem;
            color: white;
            margin-bottom: 20px;
            max-width: 800px;
            line-height: 1.6;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            background: linear-gradient(45deg, #4A90E2, #7B68EE);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }

        .logo img {
            width: 80px;
            height: 80px;
            border-radius: 16px;
        }

        .phone-container {
            width: 375px;
            height: 667px;
            background: #000;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            margin: 20px auto;
        }

        .screen {
            width: 100%;
            height: 100%;
            background: #f8f9fa;
            border-radius: 20px;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: #007AFF;
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .back-arrow {
            font-size: 18px;
        }

        .group-info h3 {
            font-size: 16px;
            font-weight: 600;
        }

        .group-info p {
            font-size: 12px;
            opacity: 0.8;
        }

        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #fff;
        }

        .message {
            margin-bottom: 15px;
            max-width: 80%;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .message.you {
            margin-left: auto;
        }

        .message.you .bubble {
            background: #007AFF;
            color: white;
            border-bottom-right-radius: 8px;
        }

        .message.fora .bubble,
        .message.jan .bubble,
        .message.lou .bubble {
            background: #E5E5EA;
            color: #000;
        }

        .message:not(.you) {
            display: flex;
            align-items: flex-start;
            gap: 8px;
        }

        .avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #007AFF;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            flex-shrink: 0;
            margin-top: 18px;
        }

        .avatar.fora { background: #FF3B30; }
        .avatar.jan { background: #34C759; }
        .avatar.lou { background: #FF9500; }

        .message-content {
            flex: 1;
            max-width: calc(100% - 38px);
        }

        .message:not(.you) .bubble {
            border-bottom-left-radius: 8px;
        }

        .message.you .message-content {
            max-width: 80%;
        }

        .sender-name {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            color: #8E8E93;
        }

        .bubble {
            padding: 12px 16px;
            border-radius: 20px;
            font-size: 16px;
            line-height: 1.4;
            word-wrap: break-word;
        }

        .timestamp {
            font-size: 11px;
            color: #8E8E93;
            margin-top: 4px;
            text-align: center;
        }

        .message.you .timestamp {
            text-align: right;
        }

        .you .sender-name {
            display: none;
        }

        .message:not(.you) .timestamp {
            text-align: left;
        }

        .input-area {
            padding: 15px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e5e5ea;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .input-field {
            flex: 1;
            background: white;
            border: 1px solid #e5e5ea;
            border-radius: 20px;
            padding: 10px 15px;
            font-size: 16px;
            outline: none;
        }

        .send-btn {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin: 40px 0;
            max-width: 1000px;
        }

        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: bold;
            color: #FFD700;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 1.1rem;
            color: white;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
            max-width: 900px;
        }

        .skill-card {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            text-align: center;
        }

        .skill-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .skill-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            margin-bottom: 8px;
        }

        .navigation {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-2px);
        }

        .nav-btn:active {
            background: rgba(255,255,255,0.4);
            transform: translateY(0);
        }

        .slide-counter {
            position: fixed;
            top: 30px;
            right: 30px;
            color: white;
            font-size: 1.1rem;
            background: rgba(0,0,0,0.3);
            padding: 8px 16px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .bullet-points {
            text-align: left;
            max-width: 700px;
            margin: 0 auto;
        }

        .bullet-points li {
            font-size: 1.2rem;
            color: white;
            margin-bottom: 15px;
            line-height: 1.5;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .learning-loop {
            display: flex;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
            gap: 30px;
            margin: 40px 0;
        }

        .loop-step {
            background: rgba(255,255,255,0.15);
            padding: 25px;
            border-radius: 50%;
            width: 150px;
            height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
            position: relative;
        }

        .loop-step::after {
            content: '→';
            position: absolute;
            right: -40px;
            font-size: 2rem;
            color: white;
        }

        .loop-step:last-child::after {
            content: '';
        }

        .contact-info {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            margin-top: 30px;
            max-width: 500px;
        }

        .contact-info h4 {
            color: #FFD700;
            font-size: 1.4rem;
            margin-bottom: 15px;
        }

        .contact-info p {
            font-size: 1.1rem;
            margin-bottom: 8px;
        }

        /* Mobile Styles */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .slide {
                padding: 20px 15px;
                min-height: 100vh;
                justify-content: flex-start;
                padding-top: 60px;
            }

            .slide h1 {
                font-size: 2.2rem;
                margin-bottom: 15px;
                line-height: 1.2;
            }

            .slide h2 {
                font-size: 1.6rem;
                margin-bottom: 15px;
                line-height: 1.3;
            }

            .slide p {
                font-size: 1rem;
                margin-bottom: 15px;
                line-height: 1.5;
            }

            .logo {
                width: 80px;
                height: 80px;
                margin: 0 auto 20px;
            }

            .logo img {
                width: 60px;
                height: 60px;
            }

            .phone-container {
                width: 280px;
                height: 500px;
                margin: 15px auto;
            }

            .stats-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                margin: 20px 0;
            }

            .stat-card {
                padding: 20px;
            }

            .stat-number {
                font-size: 2.5rem;
            }

            .stat-label {
                font-size: 1rem;
            }

            .skills-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                margin: 20px 0;
            }

            .skill-card {
                padding: 20px;
            }

            .skill-icon {
                font-size: 2.5rem;
            }

            .skill-title {
                font-size: 1.1rem;
            }

            .bullet-points {
                padding: 0 10px;
            }

            .bullet-points li {
                font-size: 1rem;
                margin-bottom: 12px;
            }

            .learning-loop {
                flex-direction: column;
                gap: 20px;
                margin: 20px 0;
            }

            .loop-step {
                width: 120px;
                height: 120px;
                padding: 15px;
            }

            .loop-step::after {
                content: '↓';
                right: auto;
                bottom: -30px;
                left: 50%;
                transform: translateX(-50%);
            }

            .loop-step:last-child::after {
                content: '';
            }

            .contact-info {
                padding: 20px;
                margin-top: 20px;
            }

            .contact-info h4 {
                font-size: 1.2rem;
            }

            .contact-info p {
                font-size: 1rem;
            }

            .navigation {
                bottom: 15px;
                gap: 10px;
            }

            .nav-btn {
                padding: 10px 16px;
                font-size: 14px;
            }

            .slide-counter {
                top: 15px;
                right: 15px;
                font-size: 1rem;
                padding: 6px 12px;
            }

            /* Slide 5 specific mobile layout */
            .slide:nth-child(5) > div {
                flex-direction: column !important;
                gap: 20px !important;
            }

            .slide:nth-child(5) .phone-container {
                order: 1;
            }

            .slide:nth-child(5) .bullet-points {
                order: 2;
                margin-top: 0;
            }
        }

        /* Extra small mobile devices */
        @media (max-width: 480px) {
            .slide {
                padding: 15px 10px;
                padding-top: 50px;
            }

            .slide h1 {
                font-size: 1.8rem;
            }

            .slide h2 {
                font-size: 1.4rem;
            }

            .slide p {
                font-size: 0.9rem;
            }

            .phone-container {
                width: 250px;
                height: 450px;
            }

            .chat-container {
                padding: 15px;
            }

            .bubble {
                font-size: 14px;
                padding: 10px 14px;
            }

            .avatar {
                width: 25px;
                height: 25px;
                font-size: 11px;
            }

            .sender-name {
                font-size: 11px;
            }

            .timestamp {
                font-size: 10px;
            }

            .input-field {
                font-size: 14px;
                padding: 8px 12px;
            }

            .send-btn {
                width: 35px;
                height: 35px;
            }

            .header {
                padding: 12px 15px;
            }

            .group-info h3 {
                font-size: 14px;
            }

            .group-info p {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">11</span>
    </div>

    <div class="slide-container">
        <!-- Slide 1: Title -->
        <div class="slide active">
            <div class="logo">
                <img src="src/web/icons/icon-640m.png" alt="AskFora Logo">
            </div>
            <h1>AskFora</h1>
            <p style="font-size: 1.8rem; margin-bottom: 30px;">Your AI-powered career co-pilot</p>
            <div class="phone-container">
                <div class="screen">
                    <div class="header">
                        <div class="header-left">
                            <span class="back-arrow">‹</span>
                            <div class="group-info">
                                <h3>Work Squad</h3>
                                <p>Fora, Jan, Lou, You</p>
                            </div>
                        </div>
                        <div>📞 📹</div>
                    </div>

                    <div class="chat-container">
                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">ughhhhh guys I cant anymore</div>
                                <div class="timestamp">2:14 PM</div>
                            </div>
                        </div>

                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">martin is literally the WORST</div>
                                <div class="timestamp">2:14 PM</div>
                            </div>
                        </div>

                        <div class="message fora">
                            <div class="avatar fora">F</div>
                            <div class="message-content">
                                <div class="sender-name">Fora</div>
                                <div class="bubble">oh no what did he do now??</div>
                                <div class="timestamp">2:15 PM</div>
                            </div>
                        </div>

                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">dude interrupts me EVERY time I talk in meetings</div>
                                <div class="timestamp">2:15 PM</div>
                            </div>
                        </div>

                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">and then acts like my ideas are his??? im so done</div>
                                <div class="timestamp">2:15 PM</div>
                            </div>
                        </div>

                        <div class="message jan">
                            <div class="avatar jan">J</div>
                            <div class="message-content">
                                <div class="sender-name">Jan</div>
                                <div class="bubble">ugh hate ppl like that</div>
                                <div class="timestamp">2:16 PM</div>
                            </div>
                        </div>

                        <div class="message lou">
                            <div class="avatar lou">L</div>
                            <div class="message-content">
                                <div class="sender-name">Lou</div>
                                <div class="bubble">wait is this the same guy who mansplained ur own project to u last month lol</div>
                                <div class="timestamp">2:16 PM</div>
                            </div>
                        </div>

                        <div class="message you">
                            <div class="message-content">
                                <div class="bubble">YES omg that guy 😤😤😤</div>
                                <div class="timestamp">2:16 PM</div>
                            </div>
                        </div>
                    </div>

                    <div class="input-area">
                        <input type="text" class="input-field" placeholder="Message">
                        <button class="send-btn">➤</button>
                    </div>
                </div>
            </div>
            <p style="font-size: 1.4rem; color: #FFD700;">Practice for your profession.</p>
        </div>

        <!-- Slide 2: The Problem -->
        <div class="slide">
            <h2>The professional world has unwritten rules.</h2>
            <h2 style="color: #FFD700;">How do you practice for a game you've never played?</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">75%</div>
                    <div class="stat-label">of new graduates feel unprepared for workplace dynamics</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">68%</div>
                    <div class="stat-label">experience imposter syndrome in their first job</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">42%</div>
                    <div class="stat-label">struggle with office communication</div>
                </div>
            </div>
            <p>Anxiety, imposter syndrome, and lack of real-world experience leave talented people feeling lost in their early careers.</p>
        </div>

        <!-- Slide 3: The Gap -->
        <div class="slide">
            <h2>Classrooms teach theory.</h2>
            <h2>The real world demands experience.</h2>
            <h2 style="color: #FFD700;">Where do you go to practice?</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">📚</div>
                    <div class="stat-label">Textbooks are static and theoretical</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">👥</div>
                    <div class="stat-label">Mentorship is hard to find and expensive</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">⚠️</div>
                    <div class="stat-label">Early career mistakes can be costly</div>
                </div>
            </div>
            <p>Current solutions don't bridge the gap between academic learning and professional reality.</p>
        </div>

        <!-- Slide 4: Our Solution -->
        <div class="slide">
            <h2 style="color: #FFD700;">Introducing AskFora:</h2>
            <h2>A safe place to scrimmage for your career.</h2>
            <p style="font-size: 1.5rem; margin: 40px 0;">AskFora is an app that gives you a personal, AI-powered support group to help you master the crucial interpersonal skills needed for the professional world.</p>
            <p style="font-size: 1.3rem;">In a private group chat, you can safely practice navigating real-world scenarios, get advice from your AI team, and build confidence for your career.</p>
        </div>

        <!-- Slide 5: How It Works - Experience -->
        <div class="slide">
            <h2>It's not an app.</h2>
            <h2 style="color: #FFD700;">It's your personal support group.</h2>
            <div style="display: flex; align-items: center; justify-content: center; gap: 50px; flex-wrap: wrap;">
                <div class="phone-container">
                    <div class="screen">
                        <div class="header">
                            <div class="header-left">
                                <span class="back-arrow">‹</span>
                                <div class="group-info">
                                    <h3>Work Squad</h3>
                                    <p>Your AI Mentors</p>
                                </div>
                            </div>
                            <div>📞 📹</div>
                        </div>

                        <div class="chat-container">
                            <div class="message fora">
                                <div class="avatar fora">F</div>
                                <div class="message-content">
                                    <div class="sender-name">Fora</div>
                                    <div class="bubble">wait jan didnt u tell me about ur friend who dealt w/ something like this?</div>
                                    <div class="timestamp">2:17 PM</div>
                                </div>
                            </div>

                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">omgggg yes sarah!!!</div>
                                    <div class="timestamp">2:17 PM</div>
                                </div>
                            </div>

                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">she had this coworker david who was such a dick</div>
                                    <div class="timestamp">2:17 PM</div>
                                </div>
                            </div>

                            <div class="message jan">
                                <div class="avatar jan">J</div>
                                <div class="message-content">
                                    <div class="sender-name">Jan</div>
                                    <div class="bubble">would literally take her presentations n present them as his own work 🙄</div>
                                    <div class="timestamp">2:18 PM</div>
                                </div>
                            </div>

                            <div class="message lou">
                                <div class="avatar lou">L</div>
                                <div class="message-content">
                                    <div class="sender-name">Lou</div>
                                    <div class="bubble">ok but what did she do tho?? bc this sounds awful</div>
                                    <div class="timestamp">2:18 PM</div>
                                </div>
                            </div>
                        </div>

                        <div class="input-area">
                            <input type="text" class="input-field" placeholder="Message">
                            <button class="send-btn">➤</button>
                        </div>
                    </div>
                </div>
                <ul class="bullet-points">
                    <li><strong>Bring your real-world problems</strong> - Share what's actually happening at work or school</li>
                    <li><strong>Get instant, empathetic advice</strong> - Three AI personalities provide different perspectives</li>
                    <li><strong>Learn through AI storytelling & guided reflection</strong> - Practice scenarios in a safe environment</li>
                </ul>
            </div>
        </div>

        <!-- Slide 6: Learning Loop -->
        <div class="slide">
            <h2>Learn by doing.</h2>
            <h2 style="color: #FFD700;">Solidify by teaching.</h2>
            <div class="learning-loop">
                <div class="loop-step">
                    <div>
                        <div style="font-size: 2rem;">🗣️</div>
                        <div>User presents problem</div>
                    </div>
                </div>
                <div class="loop-step">
                    <div>
                        <div style="font-size: 2rem;">🤖</div>
                        <div>AIs provide support & strategies</div>
                    </div>
                </div>
                <div class="loop-step">
                    <div>
                        <div style="font-size: 2rem;">❓</div>
                        <div>AI presents problem to user</div>
                    </div>
                </div>
                <div class="loop-step">
                    <div>
                        <div style="font-size: 2rem;">💡</div>
                        <div>User applies learning by giving advice</div>
                    </div>
                </div>
            </div>
            <p>This unique reciprocal learning dynamic reinforces skills and builds confidence through practice.</p>
        </div>

        <!-- Slide 7: The Skills -->
        <div class="slide">
            <h2>Mastering the 'soft skills' that drive success</h2>
            <div class="skills-grid">
                <div class="skill-card">
                    <div class="skill-icon">🤝</div>
                    <div class="skill-title">Conflict Resolution</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">🌐</div>
                    <div class="skill-title">Professional Networking</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">💼</div>
                    <div class="skill-title">Interviewing</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">📋</div>
                    <div class="skill-title">Meeting Preparation</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">🤲</div>
                    <div class="skill-title">Supporting Colleagues</div>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">💬</div>
                    <div class="skill-title">Workplace Communication</div>
                </div>
            </div>
        </div>

        <!-- Slide 8: Target Market -->
        <div class="slide">
            <h2>For the next generation of professionals,</h2>
            <h2 style="color: #FFD700;">at the moment of transition.</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">🎓</div>
                    <div class="stat-label"><strong>High School Students</strong><br>16M students preparing for college and careers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">🔧</div>
                    <div class="stat-label"><strong>Vocational & Trade</strong><br>12M students entering skilled professions</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">🎯</div>
                    <div class="stat-label"><strong>College Students & Recent Grads</strong><br>20M transitioning to professional careers</div>
                </div>
            </div>
            <p>Total addressable market: 48M students and new professionals seeking career confidence.</p>
        </div>

        <!-- Slide 9: Why Now -->
        <div class="slide">
            <h2>The future of work is collaborative</h2>
            <h2 style="color: #FFD700;">and requires high emotional intelligence.</h2>
            <ul class="bullet-points">
                <li><strong>Remote/hybrid work</strong> makes interpersonal skills even more critical and harder to observe naturally</li>
                <li><strong>Gen Z values authenticity</strong> and seeks supportive digital environments for growth</li>
                <li><strong>AI advancement</strong> makes human skills more valuable, not less</li>
                <li><strong>Workplace mental health</strong> is now a priority for organizations</li>
            </ul>
            <p style="margin-top: 30px;">The convergence of these trends creates the perfect moment for AskFora.</p>
        </div>

        <!-- Slide 10: The Vision -->
        <div class="slide">
            <h2 style="color: #FFD700;">Our vision is to ensure every student</h2>
            <h2>enters the workforce with the confidence</h2>
            <h2>and competence to succeed.</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">🏫</div>
                    <div class="stat-label">Partnerships with schools and universities</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">🏢</div>
                    <div class="stat-label">Corporate training modules for new hires</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">🏆</div>
                    <div class="stat-label">Advanced skill certifications</div>
                </div>
            </div>
            <p>We're building the infrastructure for professional development in the age of AI.</p>
        </div>

        <!-- Slide 11: The Ask -->
        <div class="slide">
            <h2 style="color: #FFD700;">Join us in building the future</h2>
            <h2>of professional development.</h2>
            <p style="font-size: 1.4rem; margin: 40px 0;">We're seeking customer development with Gen Z students entering the workforce.</p>
            <div class="contact-info">
                <h4>Ready to get involved?</h4>
                <p><strong>Founder:</strong> Omer Trajman</p>
                <p><strong>LinkedIn:</strong> linkedin.com/in/omert</p>
                <p><strong>Email:</strong> <EMAIL></p>
            </div>
            <p style="margin-top: 30px; font-size: 1.3rem;">Let's give the next generation the tools they need to thrive in their careers from day one.</p>
        </div>
    </div>

    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← Previous</button>
        <button class="nav-btn" onclick="nextSlide()">Next →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            }
        });

        // Enhanced touch/swipe support
        let startX = 0;
        let startY = 0;
        let endX = 0;
        let endY = 0;
        let startTime = 0;

        document.addEventListener('touchstart', function(e) {
            startX = e.changedTouches[0].screenX;
            startY = e.changedTouches[0].screenY;
            startTime = Date.now();
        }, { passive: true });

        document.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].screenX;
            endY = e.changedTouches[0].screenY;

            const deltaX = startX - endX;
            const deltaY = startY - endY;
            const deltaTime = Date.now() - startTime;

            // Only trigger if it's a horizontal swipe (not vertical scroll)
            // and the swipe is fast enough and long enough
            if (Math.abs(deltaX) > Math.abs(deltaY) &&
                Math.abs(deltaX) > 30 &&
                deltaTime < 500) {

                if (deltaX > 0) {
                    nextSlide();
                } else {
                    previousSlide();
                }
            }
        }, { passive: true });

        // Prevent default touch behaviors on the slide container
        document.querySelector('.slide-container').addEventListener('touchmove', function(e) {
            e.preventDefault();
        }, { passive: false });
    </script>
</body>
</html>