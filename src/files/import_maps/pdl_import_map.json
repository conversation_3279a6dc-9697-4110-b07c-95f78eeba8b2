{"87f97f73-b14e-4566-833c-117399a7f349": {"ext": "json", "users": true, "name": "PDL", "mapping": [{"path": ["id"], "attribute": "tags.PDL_ID", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["full_name"], "attribute": "displayName", "resolve_fn": "merge_name", "map_fn": "camel_case"}, {"path": ["first_name"], "attribute": "names", "resovle_fn": "save_one", "map_fn": "camel_case"}, {"path": ["middle_name"], "attribute": "names", "resovle_fn": "save_one", "map_fn": "camel_case"}, {"path": ["last_name"], "attribute": "names", "resovle_fn": "save_one", "map_fn": "camel_case"}, {"path": ["photo"], "attribute": "photos", "resovle_fn": "save_one"}, {"path": ["linkedin_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["facebook_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["twitter_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["github_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["profiles", "url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["profiles", "username"], "attribute": "names", "resolve_fn": "save_one"}, {"path": ["possible_profiles", "url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["possible_profiles", "username"], "attribute": "names", "resolve_fn": "save_one"}, {"path": ["company_website"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["job_company_website"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["job_company_linkedin_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["job_company_facebook_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["job_company_twitter_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["personal_emails"], "attribute": "comms", "map_fn": "email", "resolve_fn": "save_one"}, {"path": ["work_email"], "attribute": "comms", "map_fn": "email", "resolve_fn": "save_one"}, {"path": ["mobile_phone"], "attribute": "comms", "map_fn": "phone", "resolve_fn": "save_one"}, {"path": ["phone_numbers"], "attribute": "comms", "map_fn": "phone", "resolve_fn": "save_one"}, {"path": ["emails", "address"], "attribute": "comms", "map_fn": "email", "resolve_fn": "save_one"}, {"path": ["!", "industry(job_start_date)"], "attribute": "tags.industry", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!", "job_title(job_start_date)"], "attribute": "tags.jobTitle", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!", "job_title_role(job_start_date)"], "attribute": "tags.department", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!", "job_title_sub_role(job_start_date)"], "attribute": "tags.jobDescription", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!", "company_name(job_start_date)"], "attribute": "tags.organization", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!", "job_company_name(job_start_date)"], "attribute": "tags.organization", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!", "company_industry(job_start_date)"], "attribute": "tags.industry", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["job_summary(job_start_date)"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["summary"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["interests"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["skills"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["industry"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["company_industry"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["job_title"], "attribute": "tags.jobTitle", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["job_title_role"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["job_title_sub_role"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["company_name"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["job_title_levels"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["job_company_name"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["experience", "company", "industry"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["!experience", "company(start_date,end_date)", "name"], "attribute": "tags.organization", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["experience", "company(start_date)", "name"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["experience", "company(start_date)", "raw"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["experience", "summary(start_date)"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["company", "website"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["experience", "company", "linkedin_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["experience", "company", "facebook_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["experience", "company", "twitter_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["!experience", "title(start_date, end_date)", "name"], "attribute": "tags.jobTitle", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!experience", "title(start_date, end_date)", "role"], "attribute": "tags.department", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!experience", "title(start_date, end_date)", "raw"], "attribute": "tags.department", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["!experience", "title(start_date, end_date)", "summary"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["!experience", "title(start_date, end_date)", "sub_role"], "attribute": "tags.jobDescription", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["experience", "title(start_date, end_date)", "levels"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["!education", "school(start_date,end_date)", "name"], "attribute": "tags.school", "map_fn": "tag", "resolve_fn": "save_one_tag"}, {"path": ["education", "school", "name"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["education", "school", "type"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["education", "school", "website"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["education", "school", "linkedin_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["education", "school", "facebook_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["education", "school", "twitter_url"], "attribute": "urls", "map_fn": "url", "resolve_fn": "save_one"}, {"path": ["education", "degrees(start_date)"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["education", "majors(start_date)"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["education", "minors(start_date)"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["education", "raw(start_date)"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["education", "summary(start_date)"], "attribute": "tags", "map_fn": "skills", "resolve_fn": "save_one_tag"}, {"path": ["job_history", "company_name"], "attribute": "tags.organization", "map_fn": "tag", "resolve_fn": "save_one_tag"}]}}