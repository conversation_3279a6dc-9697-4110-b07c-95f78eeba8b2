{"id": "c95d64a4-f8ac-4297-814c-01331bccab8d", "path": "designer", "title": "Checklist for Hiring a Designer", "subtitle": "What do I need to know before I hire a designer?", "content": [{"heading": "How do I plan for working with a designer?", "subheading": "You want to prepare in advance of hiring a designer in order to ensure a smooth process.", "description": ["Before hiring a designer, it's important to know what type of design work you need done and what your budget is. You should also have a clear idea of your target audience and the overall aesthetic you are looking for. It's also helpful to review the designer's portfolio and ensure that their style and experience align with your project's needs. Additionally, you should discuss the project's goals, timelines, and communication expectations with the designer before starting the work."]}, {"heading": "What's the value of searching my network?", "subheading": "The best designers are often referrals from people you know.", "description": ["There are several benefits to hiring a designer from your professional network. For one, you may already be familiar with the person’s work, which can give you a good idea of what to expect from them.", "Additionally, because you know the person personally, you can communicate with them more easily and build a good working relationship. This can make it easier to manage the project and ensure that it is completed to your satisfaction.", "Because you already have a professional relationship with the person, they may be more inclined to go the extra mile to ensure that the project is a success."]}, {"heading": "What specificiations do I provide for a design project?", "subheading": "Scoping a project involves outlining the project's objectives, deliverables, and requirements in order to define the work that needs to be done.", "description": [" Here are some steps you can take to scope your project:", "1. Define the project's objectives: Clearly state what you hope to achieve with the project.", "2. Identify the project's deliverables: Determine what specific items or outcomes you expect to receive from the designer.", "3. Outline the project's requirements: Identify any specific needs or constraints that the designer should be aware of.", "4. Identify target audience: Understand who your intended audience is, and what they want or need."]}, {"heading": "How do I keep my project on track and on budget?", "subheading": "Having a clear and detailed project scope will help ensure that the designer understands your expectations and can deliver the work that you need.", "description": ["1. Establish a timeline: Determine when the project should be completed and any important deadlines.", "2. Set a budget: Establish how much you are willing to spend on the project.", "3. Define project scope : Summarize all the above points into a document that can be shared with the designer.", "4. Review and agreement: Review the project scope with the designer and agree on it before starting the work"]}], "project": "7ZNpga50XqKrOYagg7W8-fwjMvWWIyUxW8Ss8gMTO7Y", "skills": ["design", "UX", "UI", "layout", "editing"], "link_to": "tutor", "link_text": "Tutoring"}