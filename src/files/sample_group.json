[{"type": "group", "id": "GUID", "email_domain": ["@acme.com", "@askfora.com"], "host": "test.askfora.com", "redirect": "test.askfora.com", "name": "Test Integration", "company_name": "Acme", "logo": "iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAAWtQTFRFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA///c9AAAAHl0Uk5TADdAPBv+/yh5jd7qAkyWrAXv+hBVa7TLFfxyitcwSZCpA02oZxLyx7WfIWCHyg3lAZ1GiUem7fcLtptlYUXFDGSahMREgreYo+RiQrjCCPNe6IG9lUF7oDm/Gld+PnSAF+70M5ldkmxDvFDAfK9qPzttvs1oWouOuYMl8rEAAAIjSURBVHic5ZblW8MwEMaDLMCQ4W4Dhstw9+Hu7u4ufz59BvQu7aVN9hHuU3N37y97+1y6MPaXIyr6N2Ii0sd6uBlxkQDiQc8TIgF4ESAxSV8fxXEk6wNSBIBPW5+aJgDSM3QBmVyMLF1AtgWQo6nPzbMAdEch36rnBXqAQhtAbxSKbHrOi3UAJQSgVEPvTyQAOqNQBrJyeKxQB/hAFag0H6uU9Rnppqia1QCsVhWQBZo6VgyLelVAgynJa2R+8NAUVNPHwZ7VxrIZli1qgFbsgLE2WLYr6YMwBIYDYyg6zHVnlwoA7dgdTvRAolcF0A79feFEPyQGFPSDnaIDwYPKKAxZHTA2DKkRd8Co1QFjIUi5j0ItNP84ED2E3ABj0DtuJicgOemiD05B77SZnYGk2yhEEw4MbAeFJQMd3lmURv9zc476+QV6K/TDnEdhETkIoHxwCQrLToAV2gFjq1BwGgV8q5HHmhywrqLnG3KA113NnUZhU0n//Z0iY8JdG44tiT51wV0bDs82DdhR1HO+SwO6lQFeUm+/1chjjwLsowbfgT3wK26mAOhW4zkk6v4jaDiet9dP0Ab0kT1FHWf28jkqX5CAS9RxZaviqy3pwDjTyIN9FK4R/obUi/fvW2vxDhXvJYAH1GMdBXSr4Z4AKRe/S/xRrD2h0rNEL77oHrH0gkqvUsAj6hJHAd1q5A4sHt5w5R0VVqR6xj5Q36dD3z+LL78FUQEuVPCgAAAAAElFTkSuQmCC", "shared_key": "secret_key", "signing_secret": "signature", "service_fee": 0.05, "url": "https://acme.com", "auth_ids": ["slack_XYZ"], "accounts": {"stripe": {"id": "stripe_id"}}, "disallowed": ["file", "mail"], "import_maps": {"import_id": {"ext": "csv", "name": "Custom Map", "mappng": [{"attribute": "displayName", "fuzzy": true, "map_fn": "decode", "path": ["name"], "resolve_fn": "merge_name"}]}}, "notifications": [{"type": "project.accepted", "email": "never", "background": false, "chat": false}], "provider": "google", "provider_settings": [{"provider": "google", "oauth": {"client_id": "CLIENT_ID", "client_secret": "CLIENT_SECRET"}}], "roles": {"Admin": {"notify": ["project.accepted "], "admin": true}}, "search_mandatory_tags": [{"type": "import_type", "value": "import_id"}], "auto_add": true, "debug": false, "search_groups_contacts": true, "block_groups_contacts": true, "skip_contracting": false, "skip_payment": false, "no_referrals": false, "simple_invite": false, "sourcing": true, "mail_as_group": false}]