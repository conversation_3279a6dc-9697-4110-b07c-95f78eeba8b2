----------------------------

I'm working on learning some new skills.
Create 3-5 multiple choice questions to assess my current level of knowledge with these skills.
Phrase each question in a way so that the answer selected is what I actually feel confident in implementing.
Keep the answers short (ideally five words or less). Don't repeat the skills in the questions or answers.
Include a list of skills that each answer indicates I know.

The output is JSON that looks like this:
[{
 "question": "example question",
 "answers": [ {"answer": "example answer 1", "skills": ["skills related to example answer 1"]} ] 
}]

The skills are: optimization, Amazon storefront design, Landing page design, Email marketing, Email automation, wordpress Elementor, Ticket booking website, wordpress, Dropservicing website, shopify store, websites
Don't use these words in the answers: optimization, Amazon storefront design, Landing page design, Email marketing, Email automation, wordpress Elementor, Ticket booking website, wordpress, Dropservicing website, shopify store, websites
----------------------------

I'm working on learning some new skills.
Create an outline for a six part tutorial.  Each part should take no more than 10-5 minutes.

The output is JSON that looks like this:
[{
 "duration": "10 minutes",
 "level": "beginner",
 "text": "Example tutorial contents",
 "title": "Part 1: the first title",
}]

The skills are: optimization, Amazon storefront design, Landing page design, Email marketing, Email automation, wordpress Elementor, Ticket booking website, wordpress, Dropservicing website, shopify store, websites
I'm already good at: website design, email marketing, social media, logistics
----------------------------

Claude:
Create a course that takes 10 minutes and is for a Beginner level. The title is Part 1: Improving Online Efficiency and the summary is:
Introduction to the key principles of making your online presence efficient and effective. We will cover basic strategies for improving speed and user experience.
The skills to focus on are: optimization, Amazon storefront design, Landing page design, Email marketing, Email automation, wordpress Elementor, Ticket booking website, wordpress, Dropservicing website, shopify store, websites
fill out the content and details of the course
make it readable, like a textbook or online tutorial

The output is JSON that looks like this:
[{
 "title": "1. the first section",
 "text": "The detailed text of the tutorial in this section"
}]


-------------------------

I just finished a tutorial and want to assess want to objectively assess what I've learned.
Create 3-5 multiple choice questions, each with 3-5 answers which, based on how I answer, suggest a knowledge of specific skills.
Phrase each question such the answer I select implies knowledge in a related set of skills. 
Don't use self assessment style questions.
Don't ask me to rank my skills, confidence, or expertise.
Don't ask me what I'm able to do.
Don't ask how familiar I am with a skill or tool.
Don't ask me what skills or tools I'm comfortable with.
Keep the answers short (ideally five words or less). Don't repeat the skills in the questions or answers.
Include a list of skills that each answer indicates I know.

Here is the tutorial:
