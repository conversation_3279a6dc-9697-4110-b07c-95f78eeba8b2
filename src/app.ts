import bodyParser from 'body-parser';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import express from 'express';
// import fileParser from 'express-multipart-file-parser';
import session from 'express-session';
import path from 'path';
import favicon from 'serve-favicon';

import config from './config';

import headers from './utils/headers';
import logging from './utils/logging';
import httpsRedirect from './utils/redirect';

import rest_analyses from './rest/controllers/analyses_controller';
import rest_asks from './rest/controllers/asks_controller';
import rest_contacts from './rest/controllers/contacts_controller';
import rest_content from './rest/controllers/content_controller';
import rest_contracts from './rest/controllers/contracts_controller';
// import rest_courses from './rest/controllers/courses_controller';
import rest_categories from './rest/controllers/categories_controller';
import rest_goals from './rest/controllers/goals_controller';
import rest_groups from './rest/controllers/groups_controller';
import rest_imports from './rest/controllers/imports_controller';
import rest_notes from './rest/controllers/notes_controller';
import rest_plans from './rest/controllers/plans_controller';
import rest_projects from './rest/controllers/projects_controller';
import rest_skills from './rest/controllers/skills_controller';
import rest_users from './rest/controllers/users_controller';

import analytics from './routes/analytics';
import chat from './routes/chat';
import commands from './routes/commands';
import connect from './routes/connect';
import contract from './routes/contract';
import feedback from './routes/feedback';
import goauth from './routes/goauth';
import gopay from './routes/gopay';
import goprofile from './routes/goprofile';
import gosub from './routes/gosub';
import health from './routes/health';
import imports from './routes/imports';
import invoice from './routes/invoice';
import redirect from './routes/redirect';
import rindex from './routes/rindex';
// import learn from './routes/learn';
import expert from './routes/expert';
import gethired from './routes/gethired';
import logout from './routes/logout';
import network from './routes/network';
import notify from './routes/notify';
import people from './routes/people';
import ping from './routes/ping';
import profile from './routes/profile';
import project from './routes/project';
import rcache from './routes/rcache';
import rdialog from './routes/rdialog';
import reset from './routes/reset';
import rlog from './routes/rlog';
import slack from './routes/slack';
// import rtime from './routes/rtime';
import email from './routes/email';
import saml from './routes/saml';
import settings from './routes/settings';
import shortcuts from './routes/shortcuts';
// import update from './routes/update';
import responseTime from 'response-time';
import intro from './routes/intro';
import cache from './routes/itemcache';
import sitemap from './routes/sitemap';
import track from './routes/track';
import { tutorRun } from './routes/tutor';
import vanity from './routes/vanity';
import version from './routes/version';

export async function loadApp(): Promise<any> {
  config.checkNodeEnv(true);

  await config.loadConfig();

  // logging.app('Initializing...');

  /**
   * Notes on things that I have learned
   *  - Don't upgrade to ts-node 7.0.0!
   *    - https://github.com/TypeStrong/ts-node/issues/615
   *  - if you want req.session to work in files like src/contract, you have to type the req everywhere. Otherwise it
   *    misses the merged declaration from @types/express-session
   */

  const app = express();
  app.use(compression());
  app.use('/', httpsRedirect());
  app.use(responseTime());

  if (config.isRunningOnGoogle()) {
    logging.log('Running on Google');
    app.set('trust proxy', ['loopback', 'linklocal', 'uniquelocal']);
  } else logging.log('Running Locally');

  // Log all the requests
  app.use(logging.requestLogger());

  let store = null;

  if (config.isEnvOffline()) {
    const SFS = require('session-file-store') as any;
    const filestore = SFS(session);

    const options = {
      path: path.resolve(__dirname, '../cache/express'),
    };

    store = new filestore(options);
  } else {
    const CMJ = require('connect-memjs') as any;
    const memcached = CMJ(session);
    // const memcached = require('connect-memcached')(session);

    // let memCacheUrl = config.get('MEMCACHE_URL');
    // if (config.get('MEMCACHE_USERNAME')) memCacheUrl = `${config.get('MEMCACHE_USERNAME')}:${config.get('MEMCACHE_PASSWORD')}@${memCacheUrl}`;

    const memconfig = {
      servers: config.get('MEMCACHE_URL').split(','),
      // hosts: [memCacheUrl],
      retries: 3,
      logger: logging,
      username: undefined,
      password: undefined,
      timeout: 1.5,
      // conntimeout: 30000,
      // reconnect: 1000,
      // retry: 300,
      retry_delay: 1,
      failoverTime: 30,
      // idle: 30000,
      keepAlive: true,
    };

    if (config.get('MEMCACHE_USERNAME')) {
      memconfig.username = config.get('MEMCACHE_USERNAME');
      memconfig.password = config.get('MEMCACHE_PASSWORD');
    }

    store = new memcached(memconfig);
  }

  const cookie = config.get('COOKIE');

  const session_config = {
    secret: config.get('SECRET'),
    resave: true,
    saveUninitialized: true,
    store,
    rolling: true,
    // proxy: true,
    name: cookie ? cookie : 'Fora',
    // logger: logging.logger,
    cookie: {
      path: '/',
      maxAge: 5 * 24 * 3600 * 1000,
      httpOnly: false,
      secure: config.isEnvProduction(), // when running https
    },
  };

  if (config.isRunningOnGoogle()) session_config.cookie['domain'] = '.askfora.com';
  else if (config.get('DOMAIN')) session_config.cookie['domain'] = config.get('DOMAIN');

  app.use(session(session_config));

  app.disable('x-powered-by');
  app.use((req: express.Request, res: express.Response, next: any) => {
    let offline_host = 'localhost';
    if (config.isEnvOffline() && process.env.OFFLINE_HOST) offline_host = process.env.OFFLINE_HOST;
    let default_host = config.get('DEFAULT_HOST', req.headers.origin ? req.headers.origin[0] : offline_host);
    const proto_index = default_host.indexOf('://');
    let stated_proto = 'http';
    if (proto_index !== -1) {
      stated_proto = default_host.slice(0, proto_index);
      default_host = default_host.slice(proto_index + 3);
    }

    const proto = config.isRunningOnGoogle() || default_host.endsWith('8443') || default_host.endsWith('9000') ? 'https' : stated_proto;

    const host = `${proto}://${default_host}`;

    const send_headers = headers(host, 'GET,PUT,POST,DELETE', config.isRunningOnGoogle());
    for (const h of send_headers) res.header(h[0], h[1]);

    next();
  });

  // Copy a few values automatically from the request to the session (so we always have them)
  app.use((req: express.Request, res: express.Response, next: any) => {
    if (req.session) {
      if (config.isEnvOffline() && process.env.OFFLINE_HOST) {
        // console.log(`Hostname is ${process.env.OFFLINE_HOST}`);
        req.session['hostname'] = process.env.OFFLINE_HOST;
      }
      else if (req.hostname) req.session['hostname'] = req.hostname;
      if (!config.isEnvOffline()) logging.log(`Requested  ${req.ip} ${req.ips} ${req.subdomains} ${req.path}`);
      if (req.ip) req.session['ip'] = req.ip;
      if (req.ips) req.session['ips'] = req.ips;
      if (req.subdomains) req.session['subdomains'] = req.subdomains;
    }
    next();
  });

  const base_path = config.isRunningOnGoogle() ? path.resolve(__dirname) : path.resolve(__dirname, '..', 'lib');

  // map url paths
  app.use(favicon(path.join(base_path, 'public', 'favicon.ico')));
  app.use(bodyParser.json({ 
    limit: '20mb',
    verify: (req, res, buf) => req['raw_body'] = buf 
  })); // /\/((?!slack).)*/, 
  app.use(bodyParser.urlencoded({ limit: '20mb', extended: false }));
  // app.use(fileParser);
  app.use(cookieParser());

  app.use('/', (req: express.Request, res: express.Response, next: express.NextFunction) => {
    if(req.hostname === 'askfora.ngrok.io') {
      if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
      if(['/', '/about', '/terms', '/faq', '/data', '/privacy', '/security', '/data'].includes(req.path)) {
        const index = config.isRunningOnGoogle() ?  path.resolve(__dirname, 'homepage', 'index.html') :
          path.resolve(__dirname, '..',  'lib', 'homepage', 'index.html');
        res.type('html');
        res.status(200).sendFile(index);
      } else if(['/pitch.html', '/group_text_thread.html'].includes(req.path)) {
        const index = config.isRunningOnGoogle() ?  path.resolve(__dirname, 'homepage', req.path.replace(/\//g, '')) :
          path.resolve(__dirname, '..',  'lib', 'homepage', req.path.replace(/\//g, ''));
        res.type('html');
        res.status(200).sendFile(index);
      } else {
        express.static(path.join(base_path, 'homepage'))(req, res, next);
      }
    } else next();
  });

  app.use('/LICENSE', express.static(path.join(base_path, 'public', 'askfora.js.LICENSE.txt')));

  app.use('/skills', rindex);
  app.use('/professional', rindex);
  app.use('/network', rindex);
  app.use('/talent', rindex);
  app.use('/pricing', rindex);
  app.use('/signup', rindex);
  app.use('/login',  rindex);
  app.use('/data', rindex);
  app.use('/about', rindex);
  app.use('/security', rindex);
  app.use('/privacy', rindex);
  app.use('/terms', rindex);
  app.use('/okta', rindex);
  app.use('/webinar', rindex);
  app.use('/list', rindex);
  app.use('/apply', rindex);
  app.use('/info', rindex);
  app.use('/t', (req: express.Request, res: express.Response, next: express.NextFunction) => {
    if (req.path === '/') return rindex(req, res, next);
    else return tutorRun(req, res);
  });
  app.use('/p', rindex);
  app.use('/c', rindex);

  /*app.use('/t', express.static(path.resolve(base_path, 'public', 't')));
  app.use('/t/', express.static(path.resolve(base_path, 'public', 't')));
  app.use('/tutor.html', express.static(path.resolve(base_path, 'public', 't')));*/

  app.use('/app/:page?/:content?', redirect, express.static(path.resolve(base_path, 'public', 'app.html')));
  app.use('/app/', express.static(path.resolve(base_path, 'public', 'app.html')));

  app.use('/settings', rindex);
  app.use('/people', rindex);
  app.use('/jobs', rindex);
  app.use('/experts', rindex);
  app.use('/job', project);
  app.use('/jab', rindex);
  app.use('/notes', rindex);
  app.use('/note', rindex);
  app.use('/group', rindex);
  app.use('/premier', rindex);
  app.use('/ready', rindex);
  app.use('/offerings', rindex);
  app.use('/start', rindex);
  app.use('/teams', rindex);
  app.use('/learn', rindex);
  app.use('/breadwinners', rindex);
  app.use('/faq', rindex);
  app.use('/clients', rindex);
  app.use('/freelancers', rindex);
  app.use('/gethired', gethired);
  app.use('/expert', expert);
  app.use('/network', network);

  app.use('/share', express.static(path.resolve(base_path, 'public', 'share.html')));
  app.use('/skillsmap', express.static(path.resolve(base_path, 'public', 'skills.html')));
  app.use('/fonts', express.static(path.resolve(base_path, 'public', 'fonts')));
  app.use('/robots.txt', express.static(path.resolve(base_path, 'files', 'robots.txt')));
  app.use('/mask.json', express.static(path.resolve(base_path, 'files', 'mask.json')));
  app.use('/ignore.json', express.static(path.resolve(base_path, 'files', 'ignore.json')));
  app.use('/remap.json', express.static(path.resolve(base_path, 'files', 'remap.json')));

  app.use('/signed.html', express.static(path.resolve(base_path, 'files', 'signed.html')));

  // app.use('/_ah/health', health);
  app.use('/_ah/warmup', health);
  app.use('/msg', chat);
  app.use('/ping', ping);
  app.use('/goauth', goauth);
  app.use('/gopay', gopay);
  app.use('/gosub', gosub);
  app.use('/contract', contract);
  app.use('/project', project);
  app.use('/newjob', project);
  app.use('/track', track);
  app.use('/invoice', invoice);
  app.use('/shortcuts', shortcuts);
  app.use('/commands', commands);
  app.use('/connect', connect);
  app.use('/feedback', feedback);
  // app.use('/update', update);
  app.use('/notify', notify);
  // app.use('/learn', learn);
  // app.use('/time', rtime);
  app.use('/analytics', analytics);
  app.use('/import', imports);
  app.use('/email', email);
  app.use('/saml', saml);
  app.use('/logout', logout);
  app.use('/reset', reset);
  app.use('/vanity', vanity);
  app.use('/version', version);
  app.use('/profile', profile);
  app.use('/intro', intro);
  app.use('/cache', cache);
  app.use(/\/sitemap([0-9])*\.xml$/, sitemap);

  app.use('/tutor', tutorRun);

  app.use('/slack', slack);

  // local development only
  if (!config.isRunningOnGoogle()) {
    // for debugging, associate a session with a profile
    if (process.env.AUTH_PROFILE) app.use('/goprofile', goprofile);
  }

  // REST API. Needs to have swagger definitions at some point
  /*app.use('/api/v1/contacts', rest_contacts);
  app.use('/api/v1/contracts', rest_contracts);
  app.use('/api/v1/projects', rest_projects);
  app.use('/api/v1/users', rest_users); */

  app.use('/api/content', rest_content);
  app.use('/api/contacts', rest_contacts);
  app.use('/api/contracts', rest_contracts);
  app.use('/api/goals', rest_goals);
  app.use('/api/job', project);
  app.use('/api/projects', rest_projects);
  app.use('/api/people', people);
  app.use('/api/users', rest_users);
  app.use('/api/skills', rest_skills);
  app.use('/api/settings', settings);
  app.use('/api/group', rest_groups);
  app.use('/api/notes', rest_notes);
  app.use('/api/analyses', rest_analyses);
  app.use('/api/asks', rest_asks);
  app.use('/api/imports', rest_imports);
  app.use('/api/categories', rest_categories);
  app.use('/api/plans', rest_plans);
  // app.use('/api/courses', rest_courses);

  app.use(express.static(path.join(base_path, 'public'), { setHeaders: (r,p,s) => r.set({acceptRanges:false})}));

  app.use('/', rindex);

  // Diagnostics and logging
  if (!config.isEnvProduction()) app.use('/log', rlog);
  if (!config.isEnvProduction()) app.use('/dialog', rdialog);
  if (!config.isEnvProduction()) app.use('/cache', rcache);

  if (config.isRunningOnGoogle()) app.use(logging.errorLogger());

  // catch 404 and forward to error handler
  app.use((req: express.Request, res: express.Response, next: any) => {
    const ip = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
    const err = new Error(`${req.path} Not Found requsted from ${ip}`);
    (err as any).status = 404;
    next(err);
  });

  // error handler
  app.use((err, req: express.Request, res: express.Response, _next: any) => {
    // set locals, only providing error in development
    res.locals.message = err.message;
    res.locals.error = config.isEnvProduction() ? {} : err;

    console.log(err.stack);

    const base_path = config.isRunningOnGoogle() ? path.resolve(__dirname) : path.resolve(__dirname, '..', 'lib');

    // render the error page
    res.status(err.status || 500);
    if (err.status === 404) res.sendFile(path.resolve(base_path, 'public', '404.html'));
    else {
      res.json({
        message: err.message,
        error: err,
      }).end();
    }
  });

  return app;
}
