/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import 'express-session';

import { AuthHelper } from '../auth/auth_helper';
import { AuthProvider } from '../auth/auth_provider';
import Dialog from '../session/dialog';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

const LOG_NAME = 'routes.EmailController';

class EmailController {
  constructor() {
    this.login = this.login.bind(this);
  }

  async login(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const emails = parsers.findEmail(req.body.email);
    if (!emails || !emails.length) {
      res.sendStatus(500);
      return;
    }

    const dialog: Dialog = await Dialog.loadSession('email', req.session, req.cookies, {create_new: true, stack:Error().stack});
    if (!dialog) return AuthHelper.responseNoDialog(req, res);

    try {
      const email = emails[0].toLowerCase();
      await AuthProvider.email.generateCode(dialog, email, dialog.session);
      await dialog.saveSession();
      res.sendStatus(204);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'login', dialog.user.profile, 'Error creating code', err);
      await dialog.saveSession();
      res.sendStatus(500);
    }
  }
}

const router = express.Router();
const controller = new EmailController();

router.route('/login').get(async (req: express.Request, res: express.Response, _next: express.NextFunction) => { res.redirect('/')} );
router.route('/login').post(controller.login);

export default router;