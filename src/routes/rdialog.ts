import express from 'express';
import 'express-session';
import config from '../config';
import Dialog from '../session/dialog';
import { Person } from '../types/items';
import * as funcs from '../utils/funcs';

const router = express.Router();

const PRETTY_PRINT = '<script src="https://cdn.rawgit.com/google/code-prettify/master/loader/run_prettify.js"></script>';

function fieldReplacer(key, value) {
  if (key === 'nonIndexedFields') return undefined;
  else if (key === 'schema') return undefined;
  else if (key === 'client_secret') return undefined;
  else return value;
}

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

  const dialog: Dialog = await Dialog.loadSession('rdialog', req.session, req.cookies, {read_only: !req.query.reload, check_tokens: true, stack:Error().stack});
  if (dialog && (!config.isEnvProduction() || dialog.user.debug)) {
    await dialog.saveSession(true).catch(e => dialog.asyncError(e));
    res.status(200);

    if (req.query.full) {
      const dobj = JSON.parse(JSON.stringify(dialog));
      dobj.user = dialog.user;
      dobj.me = dialog.me;
      res.json(dobj).end();
      return;
    }

    if (req.query.dialog) {
      const dobj = JSON.stringify(dialog.history, null, 2);
      if (req.query.pretty) {
        res.send(`<html>${PRETTY_PRINT}<pre class="prettyprint">
          <code language="json">${dobj}</code>
          </pre></html>`).end();
      }
      else res.json(JSON.parse(dobj)).end();
      return;
    }

    const sdialog = {
      settings: null,
      me: null,
      user: {},
      group_set: [],
      group_host: {},
      cache_stats: dialog.cache.cacheStats(),
      loaded_groups: [],
      schema:{},
      nonIndexedFields:{},
      data_sources: null,
      accounts: null,
    };

    Object.assign(sdialog, dialog.serializeSession());
    sdialog.me = new Person(dialog.me);
    delete sdialog.schema;
    delete sdialog.nonIndexedFields;

    /* sdialog.accounts = JSON.parse(Buffer.from(sdialog.accounts, 'base64').toString());
    sdialog.data_sources = JSON.parse(Buffer.from(sdialog.data_sources, 'base64').toString());
    sdialog.settings = JSON.parse(Buffer.from(sdialog.settings, 'base64').toString()); */

    if (sdialog.me) funcs.slimEntity(sdialog.me);

    if (sdialog.loaded_groups) {
      for (const group of sdialog.loaded_groups) {
        delete group.schema;
        delete group.nonIndexedFields;
      }
    }

    const user = { schema: null, nonIndexedFields: null, settings: null, accounts: null };
    Object.assign(user, dialog.user.save(false, false));
    // user.settings = JSON.parse(Buffer.from(user.settings, 'base64').toString());
    // user.accounts = JSON.parse(Buffer.from(user.accounts, 'base64').toString());

    const update = { schema: null, nonIndexedFields: null, data_sources: null };
    Object.assign(update, dialog.user.saveUpdate(false));
    // update.data_sources = JSON.parse(Buffer.from(update.data_sources.toString(), 'base64').toString());

    if (sdialog.me) Object.assign(sdialog.me, JSON.parse(JSON.stringify(sdialog.me, fieldReplacer)));
    if (user) Object.assign(sdialog.user, JSON.parse(JSON.stringify(user, fieldReplacer)));
    if (update) Object.assign(sdialog.user, JSON.parse(JSON.stringify(update, fieldReplacer)));
    if (dialog.group_host) Object.assign(sdialog.group_host, JSON.parse(JSON.stringify(dialog.group_host, fieldReplacer)));

    if (req.query.pretty) {
      res.send(`<html>${PRETTY_PRINT}<pre class="prettyprint">
        <code language="json">${JSON.stringify(sdialog, null, 2)}</code>
        </pre></html>`).end();
    } else res.json(sdialog).end();
    res.end();
    return;
  } else if(dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));

  res.status(404).end();
});

export default router;
