import express from 'express';
import 'express-session';
import path from 'path';

import config from '../config';

import Dialog, { Topics } from '../session/dialog';

import { Tracking } from '../types/globals';
import { EntityType } from '../types/shared';

const router = express.Router();

/* GET home page. */
router.get('/', async (req: express.Request, res: express.Response, next: any) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

  const dialog = await Dialog.loadSession('gethired', req.session, req.cookies, {create_new:true, read_only: false, no_cache: true, check_tokens:true, stack:Error().stack});
  if (dialog) {
    await dialog.createGuest(true);
    dialog.user.track(req.query as Partial<Tracking>);
    dialog.user.settings.active = EntityType.Person;
    if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;
    if (dialog.user.settings.people) dialog.user.settings.people.active = null;
    if (dialog.user.settings.projects) dialog.user.settings.projects.active = null;
    dialog.user.settings.info = { enabled: false, profile: true, accounts: false, archives: false, notify: false, contracts: false};
    await dialog.saveSettings();
    dialog.setTopic(Topics.INIT_CONTRACTOR);
    await dialog.saveSession(true);
  }

  const index = config.isRunningOnGoogle() ?  path.resolve(__dirname, '..', 'public', 'index.html') :
    path.resolve(__dirname, '..', '..', 'lib', 'public', 'index.html');

  res.status(200).sendFile(index);
});

export default router;