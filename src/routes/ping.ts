import express from 'express';
import 'express-session';


import Dialog /*, { DEFAULT_PING, ping }*/ from '../session/dialog';

import { Tracking } from '../types/globals';

import { promptInfo } from '../utils/info';
import logging from '../utils/logging';

const router = express.Router();

const LOG_NAME = 'routes.ping';

router.get('/', async (req: express.Request, res: express.Response, _next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog: Dialog;
  try {
    // kick off loading a session, getting session id
    const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
    const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
    dialog = await Dialog.loadSession('ping', req.session, req.cookies, {offset, create_new:true, read_only:false, check_tokens:true, stack:Error().stack, max_wait: 3000}); // .then(dialog => {
    // const default_ping = parseInt(config.get('DEFAULT_PING'), 10);
    // const do_ping = default_ping && !isNaN(default_ping) ? ping(default_ping) : DEFAULT_PING;
    if (dialog) {
      dialog.user.track(req.query as Partial<Tracking>);
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      res.status(200).json({ping: 100}).end();
      return;
    }
  } catch (err) {
    logging.errorF(LOG_NAME, 'get', 'Ping error', err);
    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.status(200).json({ id: new Date().getTime(), reply: [promptInfo('I ran into an error')], answers: ['Send feedback'] }).end();
  }
  res.status(304).json({redirect: '/app'}).end();
});

export default router;
