import express from 'express';
import 'express-session';
import fs from 'fs';
import multer from 'multer';
import path from 'path';

import config from '../config';
import data from '../data';
import lang from '../lang';

import Dialog, { Topics } from '../session/dialog';
import ForaUser from '../session/user';

import { InternalError, Tracking } from '../types/globals';
import { Project } from '../types/items';
import { StripePluginState } from '../types/plugins';
import { AuthProviders, BasicInfo, EntityType, Info, PaymentInfo, ServerChatResponse, StripeConfig } from '../types/shared';

import { hash } from '../utils/funcs';
import { promptInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import stripe from '../sources/stripe_controller';

const LOG_NAME = 'routes.gopay';

const upload = multer();
const router = express.Router();
export const SERVICE_FEE = 0.1;
export const DISCOUNT_FEE = 0.05;

const escrow_handlers = [];

export function onEscrow(f) {
  escrow_handlers.push(f);
}

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  logging.infoF(LOG_NAME, 'get', `Auth Stripe: ${req.session.id}`);
  let dialog: Dialog = null;

  const context = req.query.state;
  const mobile = req.query.mobile;

  if (!context) {
    dialog = await Dialog.loadSession('gopay', req.session, req.cookies, {read_only: true, create_new: true, no_cache: true, stack:Error().stack});
    res.json({ key: config.get('STRIPE_PUBLIC_KEY'), fee: SERVICE_FEE } as StripeConfig).end();
    return;
  }

  try {
    dialog = await Dialog.loadSession('gopay', req.session, req.cookies, {check_tokens:true, stack:Error().stack});

    if (dialog) dialog.user.track(req.query as Partial<Tracking>);

    if (dialog && context === 'dashboard' && dialog.user.hasAccount(AuthProviders.Stripe)) {
      let stripe_id = dialog.user.paymentAccount();
      if (req.query.group && dialog.user.loaded_groups) {
        const group = dialog.user.loaded_groups[req.query.group as string];
        if (group) stripe_id = dialog.user.groupPaymentAccount(group.id);
      }
      const url = await stripe.login(stripe_id).catch(e => dialog.asyncError(e));
      await dialog.saveSession();
      res.status(200).json({url}).end();
    } else if (dialog && dialog.context.stripe && dialog.context.stripe.context === context) {
      let token = null;
      if (config.isEnvOffline()) token = lang.stripe.TOKEN_STUB;
      else token = await stripe.stripeAuth(req.query.code);

      if (token && token['stripe_user_id']) {
        dialog.user.updateAccount(AuthProviders.Stripe, token['stripe_user_id'], token);

        const info = await stripe.accountInfo(token['stripe_user_id']);
        let name = 'Stripe Express';
        if(info) {
          if (info.business_profile && info.business_profile.name && info.business_profile.name.length) {
            name = info.business_profile.name;
            if (info.type) name = `${name} (${info.type})`;
          } else if(info.email) {
            name = info.email;
            if (info.type) name = `${name} (${info.type})`;
          } else name = info.type;
        }

        dialog.user.nameAccount(AuthProviders.Stripe, token['stripe_user_id'], name);

        let active_project: Project = await dialog.projects.candidateReady();
        if (active_project) {
          if (!dialog.context.stripe) dialog.context.stripe = new StripePluginState();
          dialog.context.stripe.project = active_project;
          dialog.setTopic(Topics.STRIPE_AUTH_PROJECT);
        }
        else dialog.setTopic(Topics.STRIPE_AUTH_SUCCESS);
        await data.users.save(dialog.user);
        await dialog.safeSaveSession('projects');

      } else {
        dialog.setTopic(Topics.STRIPE_REG_ERROR);
        await dialog.safeSaveSession('stripe');
      }

      if (mobile) res.redirect('/');
      else {
        const redirect_page = fs.readFileSync(path.resolve(__dirname, '..', 'files', 'redirect.html'), 'utf-8');
        res.send(redirect_page).end();
      }
    } else {
      if (!dialog) throw new Error('No session found');
      else if (!dialog.context.stripe) throw new Error(`Missing stripe context for session ${dialog.session.id} ${dialog.user.profile}`);
      else throw new Error(`Mismatched stripe context for session ${dialog.session.id} ${dialog.user.profile}: ${dialog.context.stripe.context} vs ${context}`);
    }
  } catch (err) {
    logging.errorF(LOG_NAME, 'get', `Error in Stripe Auth for ${req.session.id}`, err);
    if (dialog) {
      dialog.setTopic(Topics.STRIPE_REG_ERROR);
      await dialog.safeSaveSession('stripe');
    }

    if (mobile) res.redirect('/');
    else {
      const redirect_page = fs.readFileSync(path.resolve(__dirname, '..', 'files', 'redirect.html'), 'utf-8');
      res.status(403).send(redirect_page).end();
    }
  }
});

router.post('/', upload.array(), async (req: express.Request, res: express.Response) => {
  let dialog: Dialog = null;
  let stripe_id;
  let project;
  let contractor_user;

  try {
    dialog = await Dialog.loadSession('gopay', req.session, req.cookies, {offset:req.body.offset, check_tokens:true, stack:Error().stack});
    if (!dialog) {
      res.sendStatus(403);
      return;
    }

    if (!dialog.isAuthenticated()) {
      res.sendStatus(404);
      return;
    }
  
    dialog.user.track(req.query as Partial<Tracking>);

    let pid = null;
    if (req.body.entities && req.body.entities.length) {
      pid = req.body.entities[0].id;
    }

    if (req.body.error) throw new Error(req.body.error);

    let token = req.body.data;

    if (token && token.startsWith('stripe')) {
      const p = token.split(';');
      if (p.length > 1) {
        if(dialog.user.loaded_groups && dialog.user.loaded_groups[p[1]]) {
          const group = dialog.user.loaded_groups[p[1]];
          token = group ? dialog.user.groupPaymentAccount(group.id) : dialog.user.paymentAccount(p[1]); 
        } else token = dialog.user.paymentAccount(p[1]);
      } else token = dialog.user.paymentAccount();
    }

    if (!token) throw new Error(lang.stripe.ESCROW_ERROR);

    // find project
    project = dialog.cache.projects[pid];
    if (!project) {
      logging.errorF(LOG_NAME, 'post', `Error taking payment for project ${pid} not found`, null);
      throw new InternalError(500, `Error taking payment for project ${pid} not found`);
    }

    project = await dialog.projects.get(project);
    if (!project) {
      logging.errorF(LOG_NAME, 'post', `Error taking payment for project ${pid} cached but not loaded`, null);
      throw new InternalError(500, `Error taking payment for project ${pid} cached but not loaded`);
    }

    logging.infoF(LOG_NAME, 'post', `Stripe payment token ${token} for project ${pid}`);

    // find contractor stripe id
    if (!project.contractor) {
      logging.errorF(LOG_NAME, 'post', `Error taking payment for project ${project.id} with no contractor`, null);
      throw new InternalError(500, `Error taking payment for project ${project.id} with no contractor`, project);
    }

    const cid = project.contractor.askfora_id;
    if (!cid) {
      logging.errorF(LOG_NAME, 'post', `Error taking payment for project ${project.id}, contractor ${project.contractor.id} has no askfora_id`, null);
      throw new InternalError(500, `Error taking payment for project ${project.id} contractor ${project.contractor.id} has no askfora_id`, project);
    }

    contractor_user = new ForaUser(cid);
    if (!(await data.users.init(contractor_user, false))) {
      logging.errorF(LOG_NAME, 'post', `Error taking payment for project ${project.id}, contractor init failed`, null);
      throw new InternalError(500, `Error taking payment for project ${project.id} contractor init failed`, project);
    }

    // contractor charge amount
    let amount = project.fee;
    if (project.rate === 'fixed') project.progress = project.duration;
    else amount *= project.duration;

    const emails = parsers.findEmail(project.client.comms);
    const to_email = emails && emails.length ? emails[0] : null;
    logging.infoFP(LOG_NAME, 'post', dialog.user.profile, `Looking for email for client in ${JSON.stringify(project.client.comms)} found ${JSON.stringify(to_email)}`);

    // issue charge
    stripe_id = contractor_user.hasAccount(AuthProviders.Stripe) ? contractor_user.paymentAccount() : config.get('STRIPE_WISE_ACCOUNT');
    if (project.contractor.as_group) {
      await contractor_user.loadGroupsFromDatastore();
      const group = contractor_user.loaded_groups[project.contractor.as_group];
      if (group) stripe_id = contractor_user.groupPaymentAccount(group.id);
    }

    let info: Info[] = [];
    const reply: BasicInfo[] = [];

    const project_url = dialog.projects.getUrl(project);

    const charge = await stripe.charge(project.skills, token, stripe_id, amount, isNaN(project.service_fee) ? SERVICE_FEE : project.service_fee, to_email, project.id, project_url);
    if (charge.requires_action) {
      info.push({
        type: EntityType.Payment,
        requires_action: charge.requires_action,
        client_secret: charge.client_secret,
        key: config.get('STRIPE_PUBLIC_KEY'), 
        fee: isNaN(project.service_fee) ? SERVICE_FEE : project.service_fee,
        account: stripe_id,
      } as PaymentInfo);
    } else {
      for (const f of escrow_handlers) {
        const prompt = await f(dialog, project, charge);
        if (prompt) {
          if (prompt.info) info = info.concat(prompt.info);
          if (prompt.reply) {
            for (const pr of prompt.reply) {
              if (pr.label) reply.push(pr);
              else reply.push(promptInfo(pr));
            }
          }
        }
      }
    }

    const response = {
      id: hash(new Date().getTime().toString()),
      reply,
      info,
      answers: [],
      page: 0,
      hint: '',
      suggestion: '',
    } as ServerChatResponse;

    await dialog.saveSession(true);
    res.json(response).end();
  } catch (err) {
    logging.errorFP(LOG_NAME, 'post', dialog ? dialog.user.profile : null, `Error in Stripe Escrow for ${req.session.id}`, err);
    if (dialog) {
      dialog.sendInternalMessage(lang.stripe.CHARGE_FAIL_RETRY_PROMPT).catch(err => {
        dialog.asyncError(err);
      });
      await dialog.saveSession().catch(e => dialog.asyncError(e));
    }

    const message = await stripe.handleError(err, stripe_id, project && project.contractor ? project.contractor.displayName : 'The freelancer', contractor_user);

    const response = {
      id: hash(new Date().getTime().toString()),
      reply:[{
        id:hash(new Date().getTime().toString()),
        label: message ? message : err.message,
      }],
      info:[],
      answers: [],
      ping: -1,
      hint: '',
      suggestion: '',
    } as ServerChatResponse;

    res.json(response).end();
  }
});

export default router;
