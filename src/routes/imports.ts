import vision from '@google-cloud/vision';
import axios from 'axios';
import express from 'express';
import 'express-session';
import { GoogleAuth } from 'google-auth-library';
import exif from "jpeg-exif";
import _ from 'lodash';
import multer from 'multer';
import { setTimeout } from "timers/promises";

import lang from '../lang';

import Dialog, { HANDOFF_PING, Topics } from '../session/dialog';
import ForaUser from '../session/user';

import { FileType, TemplateType, Tracking, WebPush } from '../types/globals';
import { Document, ImportType, Person, personRef } from '../types/items';
import { ConnectPluginState, ImportsPluginState } from '../types/plugins';
import { EntityType, Imports, NotificationType, Uid } from '../types/shared';

import { MINUTES } from '../utils/datetime';
import { hash, mapURL } from '../utils/funcs';
import * as imports from '../utils/imports';
import logging from '../utils/logging';
import notify, { NotifyType } from '../utils/notify';
import parsers from '../utils/parsers';

import { LinkedInPeopleImportPlugin } from '../sources/people/linkedin/linkedin_people_import_plugin';
import { SourceController } from '../sources/source_controller';

import { internalLearn } from '../routes/learn';

import config from '../config';
import data from '../data';

import { doExport } from './update';

const router = express.Router();

const LOG_NAME = 'routes.imports';

async function saveTemp(user: ForaUser, file) {
  return data.imports
    .save(
      user,
      new Document({
        body: file.buffer,
        mime: file.mimetype,
        created: new Date(),
        props: {
          originalname: file.originalname,
          encoding: file.encoding,
          size: file.size ? file.size.toString() : file.buffer.length.toString(),
        },
      }),
    )
    .catch(err => logging.errorFP(LOG_NAME, 'saveTemp', user.profile, 'Error saving import', err));
}

async function tempUrl(user: ForaUser, file): Promise<Document> {
  return data.imports.upload(user, new Document({
    mime: file.mimetype,
    created: new Date(),
    props: {
      originalname: file.originalname,
      encoding: file.encoding,
      size: file.size ? file.size.toString() : 0,
    }
  }));
}

export async function notifyImport(user: ForaUser, doc_id: Uid, import_type: string, count: number) {
  await user.loadGroupsFromDatastore();
  let user_group = user.loaded_groups && user.loaded_groups.length ? user.loaded_groups[0] : null;

  let import_label = '';
  switch (import_type) {
    case Imports.Facebook: import_label = 'Facebook'; break;
    case Imports.iCloud: import_label = 'iCloud'; break;
    case Imports.LinkedIn: import_label = 'LinkedIn'; break;
    default:
      if (user.loaded_groups) {
        for (const group of Object.values(user.loaded_groups)) {
          if (group.import_maps && group.import_maps[import_type]) {
           import_label =  group.import_maps[import_type].name;
           user_group = group;
           break;
          }
        }
      }
  }

  const url = mapURL(`${config.get('NOTIFICATION_URL')}/app/import`, user_group);
  const subject = count === 0 ?  `Nothing found in your ${import_label} data` : `Your ${import_label} data is ready`;
  const template = count === 0 ? TemplateType.Unsuccessful : TemplateType.Successful;

  const webpush: WebPush = {
    notification: {
      tag: `${doc_id}_${new Date().getTime()}`,
      title: 'AskFora Import',
      body: subject,
      click_action: url,
    },
  };

  const email = {
    rcpts: [{Name: user.name, Email: user.email}],
    subject,
    message: count === 0 ?  lang.imports.IMPORT_EMPTY(user.name.split(' ')[0], import_label)
      : lang.imports.IMPORT_MESSAGE(user.name.split(' ')[0], import_label, url),
  }

  const variables = {
    first_name: user.name.split(' ')[0],
    originating_site: url,
  }

  notify(user, { type: NotificationType.Import, webpush, email, template, variables }, NotifyType.PushAndEmail, user_group).catch(err => {
    logging.errorFP(LOG_NAME, 'notifyImport', user.profile, `Error notifying for import ${doc_id} type ${import_type}`, err);
  });
}

export async function internalImport(user: ForaUser, full_self: Partial<Person>, file_id: Uid, import_type: ImportType, import_id?: Uid, count?: number, refresh_set?: EntityType[]) {
  logging.infoFP(LOG_NAME, 'internalImport', user.profile, `Sending import command for file ${file_id} type ${import_type} id ${import_id} count ${count} refresh_set ${refresh_set ? JSON.stringify(refresh_set) : 'undefined'}`);
  const self = personRef(full_self);
  const message = {
    profile: user.profile,
    refresh: new Date(),
    self,
    file_id,
    import_type,
    import_id,
    count,
    refresh_set
  }

  if (config.isRunningOnGoogle()) {
    const url = `https://us-central1-${config.get('GOOGLE_CLOUD_PROJECT')}.cloudfunctions.net/import`;

    logging.infoFP(LOG_NAME, 'internalImport', user.profile, `Calling import ${url} with ${JSON.stringify(message)}`);

    while(true) {
      try {
        const auth = new GoogleAuth();
        const client = await auth.getIdTokenClient(url);
        await client.request({
          url, 
          timeout: 3600 * 1000,
          method: 'POST', 
          body: JSON.stringify(message), 
          headers: { "content-type": "application/json"}
        });
        logging.infoFP(LOG_NAME, 'internalImport', user.profile, `Posted import ${file_id}`);
        break;
      } catch(e) {
        if (e.code === 'ECONNRESET' || e.message === 'network timeout') logging.errorFP(LOG_NAME, 'internalImport', message.profile, `Error posting import - retrying`, e);
        else {
          logging.errorFP(LOG_NAME, 'internalImport', user.profile, `Error posting import ${file_id}`, e);
          break;
        }
      }
    }
  } else {
    await importFunction({data: Buffer.from(JSON.stringify(message)).toString('base64')}, null);
  }

  // const did_pub = await internalPub('import', message);
  // if (!did_pub) await importFunction({data: Buffer.from(JSON.stringify(message)).toString('base64')}, null);
  
}

export async function importRun(req: express.Request, res: express.Response) {
  logging.infoFP(LOG_NAME, 'importRun', req.body.profile, `Received ${JSON.stringify(req.body)}`);

  await data.dataCheck();

  if (!req.body || !req.body.profile || !req.body.self || !(req.body.file_id || req.body.import_id) || !req.body.import_type) {
    logging.warnFP(LOG_NAME, 'importRun', req.body.profile, `Not running malformed import ${JSON.stringify(req.body)}`);
    res.sendStatus(204).end();
    return;
  }

  const profile = req.body.profile as Uid;
  const self = personRef(req.body.self as Partial<Person>);
  const file_id = req.body.file_id as string;
  const import_type = req.body.import_type as ImportType;
  const import_id = req.body.import_id as Uid
  const count = req.body.count as number;
  const refresh_set = req.body.refresh_set as EntityType[];

  const message = {
    profile,
    refresh: new Date(),
    self,
    file_id,
    import_type,
    import_id,
    count,
    refresh_set,
  }

  try {
    await importFunction({data: Buffer.from(JSON.stringify(message)).toString('base64')}, null);
    logging.infoF(LOG_NAME, 'importRun', `Import run finished ${file_id} ${import_type} ${import_id} ${count} ${JSON.stringify(refresh_set)}`);
    res.sendStatus(204).end();
    return;
  } catch(e) {
    logging.errorF(LOG_NAME, 'importRun', 'Error running import', e);
    res.status(500).end();
  }
}

export async function finishImport(profile: Uid, self: Partial<Person>, import_type: ImportType, import_id: Uid, count: number, refresh_set: EntityType[]) {
  const user = new ForaUser(profile);
  if (!(await data.users.init(user, false))) {
    logging.warnFP(LOG_NAME, 'finishImport', profile, 'Issue initializing user');
    await data.users.message(user, lang.imports.IMPORTS_PROMPT_ERROR);
    return false;
  }

  const import_logs = await data.imports.importLogs(import_id);
  if (import_logs && import_logs.length) {
    const open_logs = import_logs.filter(log => !log.complete);

    if (open_logs.length) {
      // re-run logs older than 15 min
      const old_time = MINUTES(new Date(), -15);
      const old_logs = open_logs.filter(log => new Date(log.created) < old_time);
      if (old_logs.length) {
        const learners = _.uniq(old_logs.map(ol => ol.learn));
        // re-run these
        logging.warnFP(LOG_NAME, 'finishImport', user.profile, `${old_logs.length} logs older than ${old_time} remaining from ${learners.length} threads in import ${import_id} type ${import_type} count ${count}`);
        while(learners.length) {
          await Promise.all(learners.splice(0,50).map(async learn_id => {
            return internalLearn({learn_id});
          }));
          await setTimeout(60000);
        }
        internalImport(user, self, null, import_type, import_id, count, refresh_set);
      } else {
        logging.infoFP(LOG_NAME, 'finishImport', user.profile, `Waiting on ${open_logs.length} logs in import ${import_id} type ${import_type} count ${count}`);
        // just wait to finish
        await setTimeout(60000);
        internalImport(user, self, null, import_type, import_id, count, refresh_set);
      }
    } else {
      logging.infoFP(LOG_NAME, 'finishImport', user.profile, 'Finished loading, starting export');
      await doExport(profile, {import: {id: import_id, type: import_type, date: new Date(), count}, refresh_set, force_internal: true});
  
      logging.infoFP(LOG_NAME, 'finishImport', user.profile, 'Finished exporting, importing users');
      const [ucount, urefresh_set] = await SourceController.importUsers(user, import_id, import_type);
      logging.infoFP(LOG_NAME, 'finishImport', user.profile, 'Finished importing users');

      user.refreshing = false;
      if (!user.imports) user.imports = [];
      user.imports.push({id: import_id, type: import_type, date: new Date()});

      notifyImport(user, import_id, import_type, count).catch(err => logging.errorFP(LOG_NAME, 'finishImport', user.profile, 'Error notifying user', err));
      logging.infoFP(LOG_NAME, 'finishImport', user.profile, `Notified import ${import_id} type ${import_type} count ${count}`);

      await data.imports.removeImportLogs(import_logs);
    }
  } else {
    logging.warnFP(LOG_NAME, 'finishImport', user.profile, `No logs found for import ${import_id} type ${import_type} count ${count}`);
  }
}

export async function doImport(profile: Uid, self: Partial<Person>, file_id: Uid, import_type: ImportType): Promise<boolean> {
  if (!config.configLoaded()) await config.loadConfig();
  logging.infoFP(LOG_NAME, 'doImport', profile, `Importing file ${file_id} type ${import_type}`);
  const user = new ForaUser(profile);
  if (!(await data.users.init(user, false))) {
    logging.warnFP(LOG_NAME, 'doImport', profile, 'Issue initializing user');
    await data.users.message(user, lang.imports.IMPORTS_PROMPT_ERROR);
    return false;
  }

  if (user.isAuthenticated()) {
    if (!self) {
      self = await data.people.getUserPerson(user, profile);
      if (self) await data.people.save(user, self as Person);
    } else {
      // no need to save
      await data.people.getUserPerson(user, profile);
    }

    if (user.refreshing && new Date(user.last_refresh) > MINUTES(new Date(), -10) ) {
      logging.infoFP(LOG_NAME, 'doImport', profile, `Waiting on refresh to import ${file_id}, ${import_type}`);
      await setTimeout(30000);
      internalImport(user, self, file_id, import_type);
      return true;
    }

    logging.infoFP(LOG_NAME, 'doImport', profile, `Starting import ${file_id}, ${import_type}`);

    user.refreshing = true;
    await data.users.save(user, true, false, false);

    try { 
      const doc: Document = await data.imports.byFileId(user, file_id);
      const [count, refresh_set] = await SourceController.importData(user, self, doc, import_type);

      if (count > 0 && refresh_set.includes(EntityType.Person)) {
        await internalImport(user, self, null, import_type, doc.id, count, refresh_set);
        /*logging.infoFP(LOG_NAME, 'importData', user.profile, 'finished loading, starting export');
        await doExport(profile, {import: {id: doc.id, type: import_type, date: new Date(), count}, refresh_set, force_internal: true});
 
        const [ucount, urefresh_set] = await SourceController.importUsers(user, doc.id, import_type);*/
      } else {
        user.refreshing = false;
        if (!user.imports) user.imports = [];
        user.imports.push({id: doc.id, type: import_type, date: new Date()});

        notifyImport(user, doc.id, import_type, count).catch(err => logging.errorFP(LOG_NAME, 'importData', user.profile, 'Error notifying user', err));
        logging.infoFP(LOG_NAME, 'importData', user.profile, `Notified import ${doc.id} type ${import_type} count ${count}`);
      }
    } catch(err) {
      logging.errorFP(LOG_NAME, 'doImport', user.profile, 'Error importing', err);
      user.refreshing = false;
      data.users.save(user, true, false, false).catch(e => {
        logging.errorFP(LOG_NAME, 'doImport', profile, 'Error saving user after error in import', e);
      });
    }
  } else {
    logging.infoFP(LOG_NAME, 'doImport', profile, `Skipping import not authenticated with file ${file_id} type ${import_type}`);
  }

  return true;
}

export async function importFunction(ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig();

  let message;
  try { 
    message = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : {};
  } catch(e) {
    logging.errorF(LOG_NAME, 'importFunction', `Error parsing incoming event ${JSON.stringify(ps_event.data)}`, e);
    return;
  }

  logging.infoFP(LOG_NAME, 'importFunction', message.profile, `Function called with ${JSON.stringify(message)}`);

  if (message.profile && message.file_id) {
    await doImport(message.profile, message.self ? new Person(message.self) : null, message.file_id, message.import_type).catch(e => {
      logging.errorF(LOG_NAME, 'importFunction', `Error processing import ${message.profile} file_id ${message.file_id} type ${message.import_type}`, e);
      throw e;
    });
  } else if(message.profile && message.import_id && message.import_type && message.count && message.refresh_set) {
    await finishImport(message.profile, message.self ? new Person(message.self): null, message.import_type, message.import_id, message.count, message.refresh_set).catch(e => {
      logging.errorF(LOG_NAME, 'importFunction', `Error finishing import ${message.profile} import_id ${message.import_id} type ${message.import_type}`, e);
      throw e;
    });
  } else logging.warnF(LOG_NAME, 'importFunction', `Invalid message ${JSON.stringify(message)}`);
}

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog: Dialog;

  try {
    dialog = await Dialog.loadSession('import', req.session, req.cookies, {offset:req.body.offset, read_only: true, no_cache: true, create_new:true, check_tokens:true, stack:Error().stack});
    if (dialog) {
      dialog.user.track(req.query as Partial<Tracking>);
      if (dialog.isAuthenticatedNonGuest()) {
        if (req.query.upload) {
          const doc = await tempUrl(dialog.user, {
            mimetype: req.query.mimetype,
            originalname: req.query.originalname,
            encoding: req.query.encoding,
            size: req.query.size,
          });
          await dialog.saveSession();
          res.json({
            id: doc.id,
            url: doc.url,
            mimetype: doc.mime,
            size: req.query.size,
            created: doc.created,
          }).end();
        } else {
          await dialog.saveSession();
          res.json(dialog.user.imports).end();
        }
      } else {
        await dialog.saveSession();
        res.status(401).end();
      }
    }
  } catch (e) {
    logging.errorFP(LOG_NAME, 'get', dialog && dialog.user ? dialog.user.profile : null, 'Error returning imports', e);
    if (dialog) await dialog.saveSession();
    res.status(500).end();
  }
});

const storage = multer.memoryStorage();
const upload = multer({storage});
const import_upload = upload.fields([ { name: 'card' }, { name: 'import' } ]);

router.put('/', import_upload, async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog: Dialog;

  try {
    dialog = await Dialog.loadSession('import', req.session, req.cookies, {offset:req.body.offset, create_new:true, check_tokens:true, stack:Error().stack});
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      dialog.user.track(req.query as Partial<Tracking>);
      const files = (req as any).files;
      if (files && files['import'] && files['import'].length && 
        req.body && req.body.url && req.body.url.length) {
        const file = files['import'][0];
        const url = req.body.url;
        const start = req.body.start;
        const end = req.body.end;
        const total = req.body.total;
        logging.infoFP(LOG_NAME, 'put', dialog.user.profile, `Appending to file at ${url}'}`);
        const data = file.buffer;
        const r = await axios.put(url, data, {
          headers: {
            "content-length": data.length,
            "content-range": `bytes ${start}-${end}/${total}`
          },
          validateStatus: s => { return s < 400 },
        });
        logging.infoFP(LOG_NAME, 'put', dialog.user.profile, `Imported ${data.length} bytes to ${url}`);
      }
    }
    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(204).end();
  } catch(e) {
    logging.errorFP(LOG_NAME, 'put', dialog.user.profile, 'Error receiving upload', e);
    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.sendStatus(500).end();
  }
});

router.post('/', import_upload, async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog: Dialog;

  try {
    dialog = await Dialog.loadSession('import', req.session, req.cookies, {offset:req.body.offset, create_new:true, check_tokens:true, stack:Error().stack});
    dialog.user.track(req.query as Partial<Tracking>);
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      const files = (req as any).files;
      let doc = null;
      let import_type: ImportType = null;

      // if (files && files.length) file = files[0];

      if (files && files['card'] && files['card'].length) {
        const file = files['card'][0];
        logging.infoFP(LOG_NAME, 'post', dialog.user.profile, `Importing card from ${file.originalname ? file.originalname : 'unnamed file'}`);
        doc = await saveTemp(dialog.user, file);
      }

      if (files && files['import'] && files['import'].length) {
        const file = files['import'][0];
        logging.infoFP(LOG_NAME, 'post', dialog.user.profile, `Importing import from ${file.originalname ? file.originalname : 'unnamed file'}`);
        doc = await saveTemp(dialog.user, file);
      }

      if (req.body && req.body.import) {
        logging.infoFP(LOG_NAME, 'post', dialog.user.profile, 'Importing import from body');
        doc = await saveTemp(dialog.user, {
          buffer: req.body.import,
          mime: req.body.import[0] === '{' ? 'application/json' : 'text/csv',
          encoding: '7bit',
          size: req.body.import.length,
        });
      }

      if (req.body && req.body.id) {
        doc = req.body as Document;
        doc = await data.imports.byFileId(dialog.user, req.body.id);
      }

      if (doc) {
        // try to figure out the file type
        // tslint:disable-next-line:prefer-const
        let [file_type, file_data] = await imports.fileType(doc, null, false);

        logging.infoFP(LOG_NAME, 'post', dialog.user.profile, `Processing file ${file_type}`);

        let set_topic = Topics.IMPORTS_ERROR;
        doc.props['file_type'] = file_type;

        switch (file_type) {
          case FileType.JPG:
          case FileType.JPEG:
          case FileType.PNG:
          case FileType.RAW:
          case FileType.GIF:
            {
              if (config.isEnvOffline()) {
                const cdoc = exif.fromBuffer(Buffer.from(file_data, 'base64'));
                if (cdoc) file_data = cdoc.ImageDescription;
              } else {
                const visionClient = new vision.ImageAnnotatorClient();
                const cdoc = await visionClient.documentTextDetection({ image: { content: file_data } });
                if (cdoc) file_data = cdoc.length && cdoc[0].fullTextAnnotation ? cdoc[0].fullTextAnnotation.text.replace(/\n/g, ' ') : null;
              }

              if (file_data) set_topic = Topics.CONNECT;
              data.imports.delete(dialog.user, doc).catch(err => dialog.asyncError(err));
              break;
            }
          case FileType.HTML:
            // Soup returns circular structure
            file_data = null;
          // no break
          case FileType.JSON:
            set_topic = Topics.IMPORTS_PROMPT;
            if (doc.props.originalname) {
              if (parsers.checkKeyword(doc.props.originalname, lang.imports.IMPORTS_FACEBOOK_FILENAMES)) {
                set_topic = Topics.IMPORTS;
                import_type = Imports.Facebook;
              }
            }
            break;
          case FileType.VCF:
            set_topic = Topics.IMPORTS;
            import_type = Imports.iCloud;
            break;
          case FileType.CSV:
            set_topic = Topics.IMPORTS;
            if ((new LinkedInPeopleImportPlugin(dialog.user, doc)).checkImport()) import_type = Imports.LinkedIn;
            else set_topic = Topics.IMPORTS_PROMPT;
            break;
          case FileType.ZIP:
            [file_type, file_data] = await imports.fileType(doc, null);
            // faecbook requires about_you/your_address_books.html
            // icloud requires vCard/*
            // linkedin requires Connections.csv
            if (file_data && file_data.length) {
              if (Array.isArray(file_data)) import_type = SourceController.importTypeFromZipData(file_data);
              else  import_type = SourceController.importTypeFromZipData([file_data]);
            }
            if (import_type) set_topic = Topics.IMPORTS;
            break;
          case FileType.XLSX:
            set_topic = Topics.IMPORTS_PROMPT;
            break;
          default:
            break;
        }

        dialog.setTopic(set_topic);

        switch (set_topic) {
          case Topics.CONNECT:
            if (!dialog.context.connect) {
              dialog.context.connect = new ConnectPluginState();
              dialog.context.connect.message = file_data;
            }
            break;
          case Topics.IMPORTS:
          case Topics.IMPORTS_PROMPT:
          case Topics.IMPORTS_ERROR:
            if (!dialog.context.imports) {
              dialog.context.imports = {
                file_id: doc.file_id,
                import_type,
                docs: [doc],
                // data: file_data,
              } as ImportsPluginState;
            }
            break;
        }
      }

      await dialog.saveSession(true);

      res.json({ id: hash(new Date().getTime().toString()), ping: HANDOFF_PING, post_url: 'msg'}).end();
    } else {
      if (dialog) await dialog.saveSession();
      res.status(401).end();
    }
  } catch (e) {
    logging.errorFP(LOG_NAME, 'post', dialog.user.profile, 'Error receiving upload', e);
    if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    res.status(500).end();
  }
});

export default router;
