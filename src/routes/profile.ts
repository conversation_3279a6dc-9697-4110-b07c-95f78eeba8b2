import express from 'express';
import 'express-session';
import path from 'path';

import config from '../config';

import Dialog, { Topics } from '../session/dialog';
import ForaUser from '../session/user';

import { Tracking } from '../types/globals';
import { Person } from '../types/items';
import { InitPluginState } from '../types/plugins';
import { EntityType } from '../types/shared';

import logging from '../utils/logging';
import peopleUtils from '../utils/people';

import data from '../data';
import lang from '../lang';

const router = express.Router();
const LOG_NAME = 'routes.profile';

router.get('/:profile?/:action?', async (req: express.Request, res: express.Response, _next: express.NextFunction) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let profile_id = req.params.profile;
  let action = req.params.action;

  if (profile_id && action === 'embed') {
    try { 
      let user = await data.users.globalByVanity(profile_id);
      if (!user) {
        profile_id = encodeURI(profile_id);
        user = await data.users.globalByVanity(profile_id);
      }
      if (!user) {
        profile_id = decodeURI(profile_id);
        user = await data.users.globalByVanity(profile_id);
      }
      if (user) {
        const image = await data.profiles.get(new ForaUser(user.id), profile_id);
        if (image) {
          res.setHeader('content-type', 'image/png');
          res.send(image).end();
          return;
        }
      }
    } catch(err) {
      logging.errorF(LOG_NAME, 'get', `Error getting embedded profile card for ${profile_id}`, err);
      res.sendStatus(500);
      return;
    }

    res.sendStatus(404);
    return;
  }

  const dialog = await Dialog.loadSession('profile', req.session, req.cookies, {create_new:true, check_tokens:true, stack:Error().stack});

  if (dialog) {
    try {
      dialog.user.track(req.query as Partial<Tracking>);
      const ids = Object.keys(req.query);
      if (!profile_id && ids.length) profile_id = ids[0];

      if (profile_id) {
        if (profile_id === dialog.user.vanity || encodeURI(profile_id) == dialog.user.vanity) {
          dialog.user.settings.active = EntityType.Settings;
          if (dialog.user.settings.people) dialog.user.settings.people.active = null;
          if (dialog.user.settings.projects) dialog.user.settings.projects.active = null;
          if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;
          dialog.setTopic(Topics.SHARE);
          await dialog.saveSettings(dialog.user.settings);
          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          res.redirect('/app/profile');
          return;
        }

        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Looking for profile ${profile_id}`);
        let show_person: Partial<Person> = lang.about.ABOUT_INFO.find(a => a.vanity === profile_id);
        if (!show_person) {
          let vanity = await data.users.vanity(profile_id);
          if (!vanity) vanity = await data.users.vanity(encodeURI(profile_id));
          if (vanity) {
            show_person = peopleUtils.personFromVanity(vanity);
            // map Global Vanity on to Person
            const people = dialog.isAuthenticatedNonGuest() ? await dialog.people.findByComms([vanity.email]) : null;
            if (people && people.filter(p => !p.id || !p.id.startsWith('people/f')).length) {
              peopleUtils.mergePeople(people[0], show_person, true);
              show_person = people[0];
            }
            else if(show_person.id) dialog.cache.cachePerson(show_person);
          }
        }

        if (show_person) {
          const index = config.isRunningOnGoogle() ?  path.resolve(__dirname, '..', 'public', 'profile.html') :
            path.resolve(__dirname, '..', '..', 'lib', 'public', 'profile.html');
          let redirect;
          let topic = Topics.INIT_PROFILE;

          if (action === 'connect') {
            dialog.context.make_intro = {
              names: [dialog.me ? dialog.me.displayName : 'Fora User', show_person.displayName],
              resolve: null,
              ids: [dialog.me.id, `profile/${show_person.vanity}`],
              people: null, // links to people
              context: ['Fora recommended we connect'],
              draft: null,
              connection: true,
              project_id: null,
            };

            topic = dialog.isAuthenticatedNonGuest() ? Topics.PROMPT_CONNECTION : Topics.AUTH_CONNECTION;
            redirect = '/app/connect';
          }

          if (dialog.isAuthenticatedNonGuest()) {
            dialog.user.settings.info = { enabled: false, profile: false, accounts: false, notify: false, contracts: false};
            dialog.user.settings.active = EntityType.Person;
            if (show_person.self) dialog.user.settings.people.active = dialog.getPersonInfo(show_person, null, [], [], [], []);
            else dialog.user.settings.people.active = dialog.getPersonInfo(show_person);
            dialog.user.settings.people.active.vanity = show_person.vanity;

            //close open projects and notes
            if (dialog.user.settings.projects) dialog.user.settings.projects.active = null;
            if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;

            await dialog.saveSettings(dialog.user.settings);
          } else if (!dialog.checkTopic(Topics.AUTH_CONNECTION)) {
            dialog.clearContext();
            await dialog.createGuest(true);
          }

          if (dialog.context.init) dialog.context.init.profile = show_person; // temp_person;
          else dialog.context.init = {profile: show_person } as InitPluginState;
          dialog.context.init.vanity = show_person.vanity;

          dialog.setTopic(topic);
          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          if (redirect) res.redirect(redirect);
          else res.status(200).sendFile(index);
          return;
        }
      }
    } catch(err) {
      logging.errorFP(LOG_NAME, 'get', dialog.user.profile, `Error loading profile`, err); 
    }
    await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  }
  res.sendStatus(404).end();
});

export default router;