import express from 'express';
import 'express-session';

import logging from '../utils/logging';

import { Tracking } from '../types/globals';

import Dialog from '../session/dialog';

const LOG_NAME = 'routes.redirect';

export default async function redirect(req: express.Request, res: express.Response, next: express.NextFunction) {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

  if (req.query && (req.query.utm_id || req.query.utm_source || req.query.utm_medium ||
      req.query.utm_campaign || req.query.utm_term || req.query.utm_content)) {

    let dialog: Dialog;
    try {
      const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
      const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
      dialog = await Dialog.loadSession('redirect', req.session, req.cookies, {offset, create_new:true, read_only:false, check_tokens:false, stack:Error().stack, max_wait: 1000});
      if (dialog) {
        if (!dialog.isAuthenticatedNonGuest()) dialog.session.redirect = req.originalUrl;
        dialog.user.track(req.query as Partial<Tracking>);
        await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      }
    } catch(err) {
      logging.errorF(LOG_NAME, 'get', 'Ping error', err);
      if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
    }
  }

  if (next) return next();
}
