import express from 'express';
import 'express-session';

import Dialog from '../session/dialog';

import { Tracking } from '../types/globals';
import { ProjectRate } from '../types/shared';

import { localeBasic, localeDowMonthDay } from '../utils/format';
import { createInvoice } from '../utils/invoice';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

const router = express.Router();
const LOG_NAME = 'routes.project';

router.get('/', async (req: express.Request, res: express.Response, next: express.NextFunction) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  const dialog = await Dialog.loadSession('invoice', req.session, req.cookies, {read_only:true, check_tokens:true, stack:Error().stack });

  const ids = Object.keys(req.query);
  let project_id = null;
  if (ids.length) project_id = ids[0];

  if (dialog && project_id) {
    dialog.user.track(req.query as Partial<Tracking>);

    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Looking for project ${project_id}`);
    let project = dialog.cache.projects[project_id];
    const contract = project && project.contract ? dialog.cache.contracts[project.contract] : null;
    if (project && !contract) {
      logging.warnFP(LOG_NAME, 'get', dialog.user.profile, `No contract found ${project.contract}`);
    }

    if (project) {
      //reload
      project = await dialog.projects.get(project);
    }

    if (project && /* contract && */ project.payment) {
      const total = project.rate === ProjectRate.fixed ? project.fee : project.fee * project.progress;
      const deposit = project.rate === ProjectRate.fixed ? project.fee : project.fee * project.duration;
      const escrow = project.escrow.amount / 100;
      const fee = escrow - deposit;

      const all_comm = project.contractor.comms.join(' ');
      const phone = parsers.findPhone(all_comm, dialog.user.locale);
      const email = parsers.findEmail(all_comm);

      const comms = phone ? phone.concat(email ? email : []) : email;

      const invoice_details = {
        name: contract ? contract.contractor_name : project.contractor.displayName,
        date: localeDowMonthDay(project.payment.created * 1000, dialog.user.locale, dialog.user.timeZone),
        comms,
        terms:true,
        items: [
          {
            details: `Job: ${project.skills}`,
            rate: project.rate,
            quantity: project.rate === ProjectRate.fixed ? 1 : project.progress,
            unit_fee: project.fee,
            total,
            start: localeBasic(project.start, dialog.user.locale, dialog.user.timeZone),
            end: localeBasic(project.end, dialog.user.locale, dialog.user.timeZone),
          },
          {
            details:'AskFora Fee',
            rate:'fixed',
            quantity: 1,
            unit_fee:fee,
            total:fee,
            start: localeBasic(project.start, dialog.user.locale, dialog.user.timeZone),
            end: localeBasic(project.end, dialog.user.locale, dialog.user.timeZone),
          },
        ],
      };

      const invoice = await createInvoice(invoice_details);

      res.status(200);
      res.contentType('application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename=AskFora_${project.skills.replace(' ', '_')}_Invoice.pdf`);
      res.setHeader('Content-Length', invoice.length);
      res.send(invoice);
      res.end();
      return;
    }
  }
  res.status(404).end();
});

export default router;
