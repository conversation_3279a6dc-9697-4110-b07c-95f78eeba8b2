import express from 'express';
import 'express-session';

import { NormalizedProviderSettings } from '../types/auth';
import { Tracking } from '../types/globals';
import { AuthClientNameInfo, AuthContext } from '../types/shared';

import { AuthProvider } from '../auth/auth_provider';
import Dialog, { Topics } from '../session/dialog';

const router = express.Router();

const LOG_NAME = 'routes.connect';

// translates plain text to {message:,entities:[{name:,type:}]}
router.route('/:provider/:profile?').get(async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  const dialog = await Dialog.loadSession('connect', req.session, req.cookies, {create_new:true, check_tokens:true, stack:Error().stack});

  dialog.user.track(req.query as Partial<Tracking>);

  let redirect = '/app';

  if (dialog.group_host && dialog.group_host.provider && 
    !dialog.isAuthenticatedNonGuest() && dialog.group_host.provider !== req.params.provider) {
    dialog.setTopic(Topics.REG_ERROR_CANCELLED);
  } else {
    const providers: AuthClientNameInfo[] = AuthProvider.clientAuthNameSet(null, dialog.group_host);
    for (const provider of providers) {
      if (req.params.provider === provider.name.toLowerCase()) {
        let provider_settings: NormalizedProviderSettings = AuthProvider.getDefaultProviderSettings(provider.provider);
        let auth_context = AuthProvider.getAuthContext(dialog.user, provider.provider, dialog.user.profile, dialog.group_host);
        if (dialog.context.init && dialog.context.init.provider) {
          const default_settings = AuthProvider.getDefaultProviderSettings(dialog.context.init.provider);
          provider_settings = dialog.group_host ? dialog.group_host.providerSettings(dialog.context.init.provider) : default_settings;
          if (!provider_settings) provider_settings = default_settings;
          if (dialog.context.init.auth_context) auth_context = dialog.context.init.auth_context;
          else auth_context = AuthProvider.getAuthContext(dialog.user, provider_settings.provider, dialog.context.init.account, dialog.group_host); 
        } else if (dialog.context.init) {
          dialog.context.init.topic = dialog.topic;
          if (dialog.context.init.auth_context) auth_context = dialog.context.init.auth_context;
        }
  
        redirect = AuthProvider.urlRedirect(auth_context ? auth_context : AuthContext.App, dialog.group_host, provider_settings, dialog.user);

        /* if (!dialog.context.init) {
          dialog.context.init = { provider: provider.provider, auth_context } as InitPluginState;
          dialog.setTopic(Topics.REGISTER);
        } else {
          dialog.context.init.provider = provider.provider;
          dialog.context.init.auth_context = auth_context;
          if (dialog.context.init.topic) dialog.setTopic(dialog.context.init.topic);
          else dialog.setTopic(Topics.REGISTER);
        }*/
        break;
      }
    }
  }

  await dialog.saveSession();
  res.redirect(redirect);
});

export default router;
