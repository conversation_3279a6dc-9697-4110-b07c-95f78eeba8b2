import { PubSub } from '@google-cloud/pubsub';
import config from '../config';
import logging from '../utils/logging';

const LOG_NAME = 'routes.pub';

let proc_pub;

async function checkConfig(silent: boolean) {
  if (proc_pub) return;

  if (config.isRunningOnGoogle() || config.get('FORCE_PUB')) {
    // Instantiate a pubsub client
    const projectId = config.get('GOOGLE_CLOUD_PROJECT');
    if (!silent) logging.infoF(LOG_NAME, 'checkConfig', `Publishing requests to ${projectId}`);
    const ps = new PubSub({ projectId });
    proc_pub = ps.topic(`process-production`, {messageOrdering: true, flowControlOptions: {maxOutstandingMessages: 1}}); 
  }
}

config.onLoad('pub', checkConfig, false);

export async function internalPub(action: string, payload: any) {
  await checkConfig(true);
  if (proc_pub) {
    const data = Buffer.from(JSON.stringify({action, payload}));
    try { 
      logging.infoF(LOG_NAME, 'internalPub', `Publishing ${action} ${JSON.stringify(payload)}`);
      await proc_pub.publishMessage({data, /*orderingKey: new Date().toISOString()*/});
      logging.infoF(LOG_NAME, 'internalPub', `Published ${action} ${JSON.stringify(payload)}`);
      return true;
    } catch(e) {
      logging.errorF(LOG_NAME, 'internalPub', `Error publishing ${action} ${JSON.stringify(payload)}`, e);
    }
  }
  return false;
}