import express from 'express';
import 'express-session';
import fs from 'fs';
import path from 'path';

import lang from '../lang';

import Dialog, { Topics } from '../session/dialog';
import { FORA_PROFILE } from '../types/user';

import { Tracking } from '../types/globals';
import { Person, Project } from '../types/items';
import { ProjectPluginState } from '../types/plugins';
import { EntityType, ProjectCandidateState, ProjectInfo, ProjectRate, ViewProjects } from '../types/shared';

import { getEndDate, getStartDate } from '../utils/datetime';
import { slimEntity } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import { SERVICE_FEE } from '../routes/gopay';

const router = express.Router();
const LOG_NAME = 'routes.project';

const CREATE_PARAMS = [
  'jobspec',
  'skills',
  'title',
  'rate',
  'fee',
  'duration',
  'description',
  'search',
  'utm_id',
  'utm_source',
  'utm_medium',
  'utm_campaign',
  'utm_term',
  'utm_content'
];

router.get('/:project_id?', async (req: express.Request, res: express.Response, _next: express.NextFunction) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  const dialog = await Dialog.loadSession('project', req.session, req.cookies, {create_new:true, check_tokens:true, stack:Error().stack});

  if (dialog) {
    try {
      dialog.user.track(req.query as Partial<Tracking>);

      const ids = Object.keys(req.query);
      let project_id = null;
      if (ids.filter(i => !CREATE_PARAMS.includes(i)).length) project_id = ids[0];
      else project_id = req.params.project_id;

      if (project_id) {
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Looking for project ${project_id}`);
        let project = await dialog.projects.get(new Project({id: project_id}), true); //dialog.cache.projects[project_id];

        let set_active = true; //project !== null && project !== undefined;

        let next_topic = Topics.PROJECT_MISSING;
        if (!project) {
          // get latest
          //if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Reloading shared project ${project_id}`);
          //project = await dialog.projects.get(project);
        //} else {
          await dialog.createGuest(true);
          // check for shared project
          if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Loading new shared project ${project_id}`);
          project = await dialog.projects.get(new Project({id: project_id}), true);

          if (!project) {
            // if (logging.isDebug(dialog.user.profile)) 
            logging.warnFP(LOG_NAME, 'get', dialog.user.profile, `No shared project ${project_id}`);
            project = null;
            next_topic = Topics.PROJECT_EXPIRED;
          } else if (project.archived || project.escrow || project.progress) {
            const flags = `${project.archived ? ' archived' : ''}${project.escrow ? ' escrow' : ''}${project.progress ? ' progress' : ''}`;
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Shared project in progress ${project_id}${flags}`);
            dialog.context.project = new ProjectPluginState();
            dialog.context.project.load = true;
            dialog.context.project.open = true;
            dialog.context.project.project = project;
            if (dialog.user.isAuthenticatedNonGuest()) {
              if (dialog.user.profile !== project.client.askfora_id &&
                project.contractor && dialog.user.profile !== project.contractor.askfora_id) {
                next_topic = Topics.PROJECT_EXPIRED;
                project = null;
              }
            } else {
              next_topic = Topics.PROJECT_AUTH; //Topics.PROJECT_EXPIRED;
              project = null;
            }
          }
        }

        dialog.newDialog();

        if (project) {
          //check for jab template
          if (project.client && project.client.askfora_id === lang.init.FORA_PROFILE &&
              project.contractor && project.contractor.id === `people/${FORA_PROFILE}`) {
            project_id = null;
            next_topic = Topics.PROJECT;

            req.query.skills = project.skills;
            req.query.description = project.notes;
            req.query.requirements = project.requirements;
            req.query.deliverables = project.deliverables;
            req.query.background = project.background;
            req.query.rate = project.rate;
            req.query.fee = `${project.fee}`;
            req.query.duration = `${project.duration}`;
            req.query.sourcing_url = project.sourcing_url;
            req.query.sourcing_type = project.sourcing_type;
          } else {
            if (project.contract && project.contract !== lang.project.SKIP_CONTRACT) await dialog.contracts.load(project.contract);

            const as_client = project.client.self ? ' as client' : '';
            const as_candidate = project.me_candidate ?  ' as candidate' : ' as new candidate';
            if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Initializing project state ${project_id}${as_client}${as_candidate}`);
            dialog.context.project = new ProjectPluginState();
            dialog.context.project.project = project;
            dialog.context.project.load = true;
            dialog.context.project.open = true;
            if (project.client.self) {
              if (project.proposal) project.viewed = true;
              next_topic = Topics.PROJECT_CMD;
            } else {
              if (project.me_candidate) dialog.context.project.state = project.me_candidate.state;
              else {
                set_active = dialog.isAuthenticated();
                dialog.context.project.state = ProjectCandidateState.OPENED;
              }

              if (project.contractor && project.contractor.askfora_id !== dialog.user.profile) {
                if (project.proposal) {
                  if (project.client && project.client.askfora_id === FORA_PROFILE) {
                    /* project  = await dialog.projects.copyTemplate(project);
                    dialog.context.project.project = project;
                    dialog.context.project.added = true;

                    next_topic = dialog.isGuestAccount() ? Topics.PROJECT_STEPS: Topics.PROJECT; */
                    set_active = false;
                    next_topic = Topics.PROJECT_VIEW_TEMPLATE;
                  } else next_topic = Topics.PROJECT_AUTH;
                } else if (!(project.archived || project.escrow || project.progress)) next_topic = Topics.PROJECT_CANDIDATE
                else next_topic = Topics.PROJECT_EXPIRED;
              } else next_topic = Topics.PROJECT_CANDIDATE;
            }

            if (set_active) {
              if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Setting project active ${project_id}`);
              if (!dialog.user.settings.projects) dialog.user.settings.projects = { view: project.client.self ? ViewProjects.Manage : ViewProjects.Contract};
              dialog.user.settings.active = project.expert ? EntityType.Expert : EntityType.Project;
              const project_info  = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self, Object.keys(dialog.user.groups), dialog.user.locale);
              project_info.client = { id: project.client.id, name: project.client.displayName, self: project.client.self, type: EntityType.Person };
              project_info.candidates = [];
              project_info.me_candidate = project.client.self || dialog.isAnonymousAccount() || dialog.isGuestAccount() ? null : 
                  { id: dialog.me.id, name: dialog.me.displayName, self: true, type: EntityType.Person };
              dialog.user.settings.projects.active = {id: project.id, type: project.expert ? EntityType.Expert : EntityType.Project}; //project_info;
              dialog.user.settings.info = { enabled: false, profile: false, accounts: false, archives: false, notify: false, contracts: false};
              if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;
              if (dialog.user.settings.people) dialog.user.settings.people.active = null;
            }
            await dialog.saveSettings();
          }
        } else logging.warnFP(LOG_NAME, 'get', dialog.user.profile, `No project found ${project_id}`);
        dialog.setTopic(next_topic);

        if (dialog) await dialog.saveSession(true, 'project');
        res.redirect(`/app/job/${project_id}`);
        return;
      } 
     
      // check again - jab template may have cleared project_id
      if (!project_id) {
        await dialog.createGuest(true);

        // new job
        dialog.setTopic(Topics.PROJECT);
        dialog.context.project = new ProjectPluginState();

        const client = new Person(dialog.me);
        client.self = true;
        client.askfora_id = dialog.user.profile;
        slimEntity(client);

        let project = new Project({ client, service_fee: SERVICE_FEE });
        dialog.context.project.project = project;

        dialog.mapGroupProjectSettings(project);

        if (project.group_settings && project.group_settings.service_fee !== undefined && project.group_settings.service_fee !== null) project.fee = project.group_settings.service_fee;

        if (req.query.jobspec) {
          const jobspec = new URLSearchParams(Buffer.from(req.query.jobspec as string, 'base64').toString());
          req.query.skills = jobspec.get('skills');
          req.query.description = jobspec.get('description');
          req.query.requirements = jobspec.get('requirements');
          req.query.deliverables = jobspec.get('deliverables');
          req.query.background = jobspec.get('background');
          req.query.rate = jobspec.get('rate');
          req.query.fee = jobspec.get('fee');
          req.query.duration = jobspec.get('duration');
          req.query.sourcing_type = jobspec.get('sourcing_type');
          req.query.sourcing_url = jobspec.get('sourcing_url');
        }

        if (req.query.skills) project.skills = req.query.skills as string;
        else if(req.query.title) project.skills = req.query.title as string;

        if (req.query.title) project.title = req.query.skills as string;
        else project.title = project.skills;

        if (req.query.rate && (req.query.rate as ProjectRate) in ProjectRate){
          project.rate = req.query.rate as ProjectRate;
        }

        const sourcing =  project.group_settings && project.group_settings.sourcing;

        if (project.group_settings && project.group_settings.skip_payment) project.fee = 0;
        else if(project.rate === ProjectRate.sourcing && sourcing) {
          project.fee = 0;
          project.payment = lang.project.SKIP_PAYMENT(new Date());
          project.group_settings = { skip_payment: true, skip_contracting: true };
          project.escrow = lang.project.SKIP_ESCROW;
          if (req.query.sourcing_url !== undefined && req.query.sourcing_url !== null) project.sourcing_url = req.query.sourcing_url as string;
        } else if (req.query.fee) {
          const fee =  parsers.findFee(req.query.fee as string);
          const num = parsers.findNum(req.query.fee as string);
          if (fee && fee.length) project.fee = Math.min(parsers.parseNumberRounded(fee[0].replace(/,/g, '')), Number.MAX_SAFE_INTEGER);
          else if(num && num.length) project.fee = Math.min(parsers.parseNumberRounded(num[0].replace(/,/g, '')), Number.MAX_SAFE_INTEGER);
        }

        if (req.query.duration) {
          let max_duration;
          switch (project.rate) {
            case ProjectRate.hourly: max_duration = 100000; // hours
            case ProjectRate.daily: max_duration = 4000; // days
            case ProjectRate.fixed: max_duration = 1; // hours
            case ProjectRate.sourcing: max_duration = 1;
          }

          const duration = parsers.findDuration(req.query.duration as string, project.rate);
          const num = parsers.findNum(req.query.duration as string);

          if (duration && duration.length) project.duration = Math.min(parsers.parseNumberRounded(duration[0]), max_duration);
          else if(num && num.length) project.duration = Math.min(parsers.parseNumberRounded(num[0].replace(/,/g, '')), Number.MAX_SAFE_INTEGER);
        } else if([ProjectRate.fixed, ProjectRate.sourcing].includes(project.rate)) project.duration = 1;

        if (req.query.description !== undefined && req.query.description !== null) project.notes = req.query.description as string;
        if (req.query.requirements !== undefined && req.query.requirements !== null) project.requirements = req.query.requirements as string;
        if (req.query.deliverables !== undefined && req.query.deliverables !== null) project.deliverables = req.query.deliverables as string;
        if (req.query.background !== undefined && req.query.background !== null) project.background = req.query.background as string;

        if (project.rate !== undefined && project.rate !== null &&
            project.duration !== undefined && project.duration !== null) {
          project.start = getStartDate(project.duration, project.rate);
          project.end = getEndDate(project.duration, project.rate, new Date(project.start));
        }

        if (project.skills !== undefined && project.skills !== null && project.skills.length &&
            project.title !== undefined && project.title !== null && project.title.length &&
            project.fee !== undefined && project.fee !== null &&
            project.rate !== undefined && project.rate !== null &&
            project.duration !== undefined && project.duration !== null &&
            project.notes !== undefined && project.notes !== null) {
            
          project = await dialog.projects.create(project);
          dialog.context.project.project = project;

          dialog.setTopic(Topics.PROJECT_STEPS);

          /* if (req.query.search) {
            dialog.setTopic(Topics.PROJECT_MATCHING);
          } else { */
            const reply_project: ProjectInfo = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client && project.client.askfora_id == dialog.user.profile);
            dialog.addReplies(reply_project);

            if(dialog.isAuthenticatedNonGuest()) {
              dialog.context.project.save = true;
              if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, `Setting project active ${project_id}`);
              if (!dialog.user.settings.projects) dialog.user.settings.projects = { view: project.client.self ?  ViewProjects.Manage : ViewProjects.Contract };
              dialog.user.settings.active = project.expert ? EntityType.Expert : EntityType.Project;
              const project_info  = peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self);
              project_info.client = { id: project.client.id, name: project.client.displayName, self: project.client.self, type: EntityType.Person };
              project_info.me_candidate = project.client.self || dialog.isAnonymousAccount() || dialog.isGuestAccount() ? null : 
                  { id: dialog.me.id, name: dialog.me.displayName, self: true, type: EntityType.Person };
              dialog.user.settings.projects.active = {id: project.id, type: project.expert ? EntityType.Expert : EntityType.Project}; //project_info;
              dialog.user.settings.info = { enabled: false, profile: false, accounts: false, archives: false, notify: false, contracts: false};
              if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;
              if (dialog.user.settings.people) dialog.user.settings.people.active = null;

            } else dialog.setTopic(Topics.CREATE_PROJECT);
          //}
        }
      }
    } catch(e) {
      if (dialog) dialog.asyncError(e);
      logging.errorFP(LOG_NAME, 'get', dialog ? dialog.user.profile : null, `Error loading project ${JSON.stringify(req.query)}`, e);
    }
  }

  if (dialog) await dialog.saveSession(true, 'project');


  const redirect_page = fs.readFileSync(path.resolve(__dirname, '..', 'files', 'redirect.html'), 'utf-8');
  res.send(redirect_page).end();
});

export default router;
