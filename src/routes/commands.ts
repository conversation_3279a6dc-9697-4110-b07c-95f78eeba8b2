import express from 'express';
import session from 'express-session';
import { reserved } from '../plugins';
const router = express.Router();

// translates plain text to {message:,entities:[{name:,type:}]}
router.get('/', async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  res.status(200);
  res.json(Array.from(new Set(reserved))).end();
});

export default router;
