import express from 'express';
import 'express-session';
import Dialog, { Topics } from '../session/dialog';
import logging from '../utils/logging';

const router = express.Router();

const LOG_NAME = 'routes.reset';

router.get('/:app?', async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog;
  try {
    // kick off loading a session, getting session id
    const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
    const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
    const soft = !!req.query.soft;
    const dialog = await Dialog.loadSession('reset', req.session, req.cookies, {offset, create_new:true, read_only:false, check_tokens:true, stack:Error().stack});
    if (dialog) {
      dialog.clearCommand();
      let next_topic = Topics.DEFAULT;

      if (!dialog.isAuthenticated() || dialog.isGuestAccount()) {
        next_topic = Topics.INIT;
        if (soft) dialog.resetContext(dialog.isGuestAccount() ? ['init'] : []);
        else {
          await dialog.deleteSession(true);
          await dialog.cache.saveCache(dialog.cacheExpires());
        }
      } else {
        dialog.clearContext();
        dialog.clearActions();
        // dialog.clearSessionWait();
        /*const settings = dialog.userSettings();
        settings.active = EntityType.User;
        if (settings.notes) settings.notes.active = null;
        if (settings.people) settings.people.active = null;
        if (settings.projects) settings.projects.active = null;
        await dialog.saveSettings(settings);*/
      }

      dialog.clearFilters();

      dialog.setTopic(next_topic);
      dialog.newDialog({ next_topic });

      await dialog.saveSession(true);
    }

    if (req.params.app === 'app') res.redirect('/app');
    else res.redirect('/');
  } catch (err) {
    logging.errorF(LOG_NAME, 'get', 'Reset error', err);
    if (dialog) await dialog.saveSession(true);
    res.status(500).end();
  }
});

router.post('/',  async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog;
  try {
    // kick off loading a session, getting session id
    const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
    const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
    const soft = !!req.body.soft;
    dialog = await Dialog.loadSession('reset', req.session, req.cookies, {offset, create_new:true, read_only:false, check_tokens:true, stack:Error().stack});
    if (dialog) {
      logging.infoFP(LOG_NAME, 'reset', dialog.user.profile, `Running ${soft? 'soft ' : ''}reset`);
      dialog.clearCommand();
      let next_topic = Topics.DEFAULT;

      if (!dialog.isAuthenticated() || dialog.isGuestAccount()) {
        next_topic = Topics.INIT;
        if (soft) dialog.resetContext(dialog.isGuestAccount() ? ['init'] : []);
        else {
          logging.infoFP(LOG_NAME, 'reset', dialog.user.profile, `Deleting session`);
          await dialog.deleteSession(true);
          await dialog.cache.saveCache(dialog.cacheExpires());
          logging.infoFP(LOG_NAME, 'reset', dialog.user.profile, `Deleted session`);
        }
      } else {
        dialog.clearContext();
        dialog.clearActions();
        // dialog.clearSessionWait();
        /*const settings = dialog.userSettings();
        settings.active = EntityType.User;
        if (settings.notes) settings.notes.active = null;
        if (settings.people) settings.people.active = null;
        if (settings.projects) settings.projects.active = null;
        await dialog.saveSettings(settings);*/
      }

      dialog.setTopic(next_topic);
      dialog.newDialog({ next_topic });

      await dialog.saveSession(true);
      logging.infoFP(LOG_NAME, 'reset', dialog.user.profile, 'Session reset');
    } else logging.warnF(LOG_NAME, 'reset', 'Reset did not get dialog');

    res.status(204).end();
  } catch (err) {
    logging.errorF(LOG_NAME, 'get', 'Reset error', err);
    if (dialog) await dialog.saveSession(true);
    res.status(500).end();
  }
});


export default router;
