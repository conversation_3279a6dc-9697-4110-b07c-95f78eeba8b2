import express from 'express';
import session from 'express-session';
import logging from '../utils/logging';

const LOG_NAME = 'route.rlog';
const router = express.Router();

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  logging.infoF(LOG_NAME, 'get', `REMOTE: ${decodeURI(Object.keys(req.query).join(' '))}`);
  res.status(204).end();
});

export default router;
