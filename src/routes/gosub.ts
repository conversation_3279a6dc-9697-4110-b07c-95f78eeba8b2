import crypto from 'crypto';
import express from 'express';
import 'express-session';
import multer from 'multer';

import config from '../config';
import data from '../data';

import Dialog from '../session/dialog';

import { Notification, TemplateType } from '../types/globals';
import { NotificationType, Subscription } from '../types/shared';

import { subscriptionInfo } from '../utils/info';
import logging from '../utils/logging';
import notify, { NotifyType } from '../utils/notify';

import { cancel, checkSubscription, getPlan, getPlans, HyperlineSubscription, lookupCustomer, lookupUser, pause, PlanType, subscribed, subscriptions, subscriptionToken, update } from '../sources/hyperline_controller';

const LOG_NAME = 'routes.gosub';

const upload = multer();
const router = express.Router();

// events
// subscription.created, subscription.activated, subscription.paused, subscription.cancelled, 
// subscription.voided, subscription.errored, subscription.charged
router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  logging.infoF(LOG_NAME, 'get', `Subscription Token: ${req.session.id}`);
  let dialog: Dialog = null;

  try {
    dialog = await Dialog.loadSession('gosub', req.session, req.cookies, {create_new: false, read_only: true, no_cache: true, stack:Error().stack});
    if (dialog) {
      const seats = dialog.group_host ? (await data.users.globalByGroup(dialog.group_host.id)).length : undefined;

      let sub: Partial<Subscription> = {
        mode: config.isEnvProduction() ? 'production' : 'sandbox', 
        seats,
      };
      if (dialog.isAuthenticatedNonGuest()) {
        const group_admin = dialog.group_host && dialog.user.isAdmin(dialog.group_host.id);
        const customer = group_admin ?  await lookupCustomer(dialog.group_host) : await lookupCustomer(dialog.user);
        if (customer) {
          const sub_info = await checkSubscription(dialog.user);
          if (sub_info) {
            sub = subscriptionInfo(customer, sub_info, group_admin);
            sub.seats = seats;
          }
          sub.token = await subscriptionToken(customer.id);
        }
      }
      await dialog.saveSession();
      res.json(sub).end();
      return;
    }
  } catch(e) {
    if (dialog) await dialog.saveSession();
    logging.errorFP(LOG_NAME, 'gosub', dialog?.user.profile, `Error getting subscription`, e);
  }

  res.sendStatus(404).end();
});

router.post('/', upload.array(), async (req: express.Request, res: express.Response) => {
  const webhook_id = req.headers['webhook-id'];
  logging.infoF(LOG_NAME, 'post', `Hyperline Sub ${webhook_id ? 'webhook' : ''}`);
  if (webhook_id) {
    const webhook_timestamp = req.headers['webhook-timestamp'];
    const webhook_signature = req.headers['webhook-signature'] as string;

    const secret = config.get('HYPERLINE_SECRET');
    if (!secret) {
      res.status(204).end();
      return;
    }

    const content = `${webhook_id}.${webhook_timestamp}.${req['raw_body']}`;

    const secret_bytes = Buffer.from(secret.split('_')[1], "base64");

    const signature = crypto.createHmac('sha256', secret_bytes).update(content).digest('base64');

    const signature_match = webhook_signature.split(',')[1] === signature;

    const event: {
        event_type: string; // "<group>.<event>",
        data: {
          id: string;
          customer_id: string;
          status: string;
          name?: string[];
          billing_plan_id?: string[];
          invoice_id?: string;
          invoice_schedule?: string;
          payment_method?: {
            id: string;
            type: string;
          };
          tranaction?: {
            id: string;
          };
          activated_at?: Date|string;
          completed_at?: Date|string;
          cancelled_at?: Date|string;
          errored_at?: Date|string;
          paused_at?: Date|string;
          charged_at?: Date|string;
          next_payment_at?: Date|string;
          voided_at?: Date|string;
          deleted_at?: Date|string;
          recovered_at?: Date|string;
        };
      } = req.body;


    if (event?.data?.customer_id) {
      const user = await lookupUser(event.data.customer_id);
      await user.loadGroupsFromDatastore();
      const sub = user ? await checkSubscription(user) : undefined;
      if (!sub) logging.warnF(LOG_NAME, 'goSub', `Unknown customer ${event.data.customer_id}`) 
    }
    res.status(204).end();
    return;
  } else {
    let dialog: Dialog;
    try {
      dialog = await Dialog.loadSession('gosub', req.session, req.cookies, {create_new: false, read_only: true, no_cache: true, stack:Error().stack});
      if(dialog) {
        const seats = dialog.group_host ? (await data.users.globalByGroup(dialog.group_host.id)).length : undefined;
        let sub_info: Partial<Subscription> = {
          mode: config.isEnvProduction() ? 'production' : 'sandbox', 
          seats,
        };
  
        if(dialog.isAuthenticatedNonGuest()) {
          let group_sub = false;
          const group_admin = dialog.group_host && dialog.user.isAdmin(dialog.group_host.id);
          let customer;
          if(group_admin) {
            customer =  await lookupCustomer(dialog.group_host);
            if(customer) group_sub = true;
          } else customer = await lookupCustomer(dialog.user);

          if (customer) {
            let notification_type: NotificationType;
            let template: TemplateType;
            let billing: 'monthly' | 'annually' | undefined;

            const current_subs = await subscriptions(customer);
            const current_plans = current_subs ? current_subs.map(cs => cs.plan?.id).filter(x => x) : [];
            const is_subscribed = current_subs ? current_subs.map(cs => subscribed(cs)).reduce((a,b) => a || b, false) : false;

            let sub: Partial<HyperlineSubscription>;
            let changed_plan = false;
            if (req.body.subscribe) {
              const plans = await getPlans();
              if (req.body.commitment) {
                if (req.body.commitment === 'year') {
                  // check current subscriptions
                  const year_plan = plans?.find(p => p.commitment_interval?.period === 'years' &&
                      ( (!group_admin && p.products.find(r => r.type === 'flat_fee')) || (group_admin && p.products.find(r => r.type === 'seat'))));
                  if (year_plan && !current_plans.includes(year_plan.id)) { // (!current_sub || !current_sub.plan || current_sub.plan.id !== year_plan.id)) 
                    logging.infoFP(LOG_NAME, 'post', dialog.user.profile, `${customer.id} Enable yearly subscription`);
                    sub = await checkSubscription(dialog.user, [year_plan], year_plan);
                    changed_plan = true;
                    billing = 'annually';
                  }
                } else {
                  const month_plan = plans?.find(p => p.commitment_interval?.period === 'months' &&
                      ( (!group_admin && p.products.find(r => r.type === 'flat_fee')) || (group_admin && p.products.find(r => r.type === 'seat'))));
                  if (month_plan && !current_plans.includes(month_plan.id)) { // (!current_sub || !current_sub.plan || current_sub.plan.id !== month_plan.id)) 
                    logging.infoFP(LOG_NAME, 'post', dialog.user.profile, `${customer.id} Enable monthly subscription`);
                    sub = await checkSubscription(dialog.user, [month_plan], month_plan);
                    changed_plan = true;
                    billing = 'monthly';
                  }
                }

                if (!sub) {
                  logging.infoFP(LOG_NAME, 'post', dialog.user.profile, `${customer.id} Enable default subscription`);
                  const plan = await getPlan(group_sub? PlanType.Teams : PlanType.Professaionl, 'months');
                  sub = await checkSubscription(dialog.user, [plan], plan);
                  if (subscribed(sub)) {
                    notification_type = NotificationType.Subscription;
                    // notify subscription (5923638)
                    // billing: 'monthly' or 'annually'
                    const plan = plans?.find(p => p.id === sub.plan?.id);
                    billing = 'monthly';
                    if(plan?.commitment_interval?.period === 'years') billing = 'annually';
                  }
                } else if(changed_plan && (is_subscribed || (sub && subscribed(sub)))) {
                  notification_type = NotificationType.Subscription;
                  template = TemplateType.Changed;
                }

                // if subscribed
                // notify subscription.changed (5923948) 
              }  else  {
                const plan = await getPlan(group_sub? PlanType.Teams : PlanType.Professaionl, 'months');
                sub = await checkSubscription(dialog.user, [plan], plan);
                if (sub && subscribed(sub)) {
                  // notify subscription (5923638)
                  // billing: 'monthly' or 'annually'
                  notification_type = NotificationType.Subscription;
                  const plan = plans?.find(p => p.id === sub.plan?.id);
                  billing = 'monthly';
                  if(plan?.commitment_interval?.period === 'years') billing = 'annually';
                }
              }

              if (group_admin && sub) {
                const users = await data.users.globalByGroup(dialog.group_host.id);
                const r = await update(customer, sub, users.length);
                await dialog.groupsLoad(dialog.group_host.host, true);
              }
            } else if (req.body.pause) {
              sub = await pause(customer);
              // notify subscription.canceled (5924064)
              notification_type = NotificationType.Subscription;
              template = TemplateType.Canceled;
            } else if (req.body.cancel) {
              sub = await cancel(customer);
              // notify subscription.canceled (5924064)
              notification_type = NotificationType.Subscription;
              template = TemplateType.Canceled;
            }

            sub_info = subscriptionInfo(customer, sub, group_admin);
            sub_info.token = await subscriptionToken(customer.id);
            sub_info.seats = seats;

            if(notification_type) {
              const global_user = await data.users.globalById(dialog.user.profile);
              const notification: Notification = {
                email: { rcpts: [{Name: dialog.me.displayName, Email: global_user.email}] },
                type: notification_type,
                template,
                variables: {
                  firstname: global_user.name,
                  billing
                }
              }

              await notify(dialog.user, notification, NotifyType.EmailOnly, dialog.group_host, false);
            }
          }
        }

        await dialog.saveSession();
        res.json(sub_info).end();
        return;
      }
    } catch(e) {
      if (dialog) await dialog.saveSession();
      logging.errorFP(LOG_NAME, 'gosub', dialog?.user.profile, `Error updating subscription`, e);
    }
  }

  res.status(400).end();

});

export default router;