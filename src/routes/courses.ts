import express from 'express';
import { GoogleAuth } from 'google-auth-library';

import logging from '../utils/logging';

import { retryError } from '../sources/helpers/google/google_retry_helper';

import { SourceController } from '../sources/source_controller';

import config from '../config';
import data from '../data';

const router = express.Router();

const LOG_NAME = 'routes.courses';

export async function internalCourse(file_id: string, remote = false) {
  logging.infoF(LOG_NAME, 'internalCourse', `Sending course command for file ${file_id}`);
  const message = {
    file_id,
  }

  if (config.isRunningOnGoogle() || remote) {
    const url = `https://us-central1-${config.get('GOOGLE_CLOUD_PROJECT')}.cloudfunctions.net/course`;

    logging.infoF(LOG_NAME, 'internalCourse', `Calling course ${url} with ${JSON.stringify(message)}`);

    while(true) {
      try {
        const auth = new GoogleAuth();
        const client = await auth.getIdTokenClient(url);
        await client.request({
          url, 
          timeout: 3600 * 1000,
          method: 'POST', 
          body: JSON.stringify(message), 
          headers: { "content-type": "application/json"}
        });
        logging.infoF(LOG_NAME, 'internalCourse', `Posted course ${file_id}`);
        break;
      } catch(e) {
        if (retryError(e)) logging.errorF(LOG_NAME, 'internalCourse', `Error posting course - retrying`, e);
        else {
          logging.errorF(LOG_NAME, 'internalCourse', `Error posting course ${file_id}`, e);
          break;
        }
      }
    }
  } else {
    await courseFunction({data: Buffer.from(JSON.stringify(message)).toString('base64')}, null);
  }
}

export async function loadCourse(file_id: string) {
  const course_doc = await data.courses.loadCourse(file_id);
  await SourceController.loadCourse(course_doc);
}

export async function courseRun(req: express.Request, res: express.Response) {
  logging.infoFP(LOG_NAME, 'coursetRun', req.body.profile, `Received ${JSON.stringify(req.body)}`);

  await data.dataCheck();

  if (!req.body || !req.body.file_id) {
    logging.warnFP(LOG_NAME, 'courseRun', req.body.profile, `Not running malformed course ${JSON.stringify(req.body)}`);
    res.sendStatus(204).end();
    return;
  }

  const file_id = req.body.file_id as string;

  const message = {
    file_id,
  }

  try {
    await courseFunction({data: Buffer.from(JSON.stringify(message)).toString('base64')}, null);
    logging.infoF(LOG_NAME, 'courseRun', 'Course run finished');
    res.sendStatus(204).end();
    return;
  } catch(e) {
    logging.errorF(LOG_NAME, 'courseRun', 'Error running course', e);
    res.status(500).end();
  }
}

export async function courseFunction(ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig();

  let message;
  try { 
    message = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : {};
  } catch(e) {
    logging.errorF(LOG_NAME, 'courseFunction', `Error parsing incoming event ${JSON.stringify(ps_event.data)}`, e);
    return;
  }

  logging.infoF(LOG_NAME, 'courseFunction', `Function called with ${JSON.stringify(message)}`);

  if (message.file_id) {
    await loadCourse(message.file_id).catch(e => {
      logging.errorF(LOG_NAME, 'courseFunction', `Error processing course file_id ${message.file_id}`, e);
      throw e;
    });
  } else logging.warnF(LOG_NAME, 'courseFunction', `Invalid message ${message}`);
}

export default router;