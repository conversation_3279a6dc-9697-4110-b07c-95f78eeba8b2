import express from 'express';

import ForaUser from '../session/user';
import { checkSubscription, customerGroup, subscriptionEnabled } from '../sources/hyperline_controller';
import { Notification, TemplateType } from '../types/globals';
import { NotificationType } from '../types/shared';
import { DAYS, sameDay } from '../utils/datetime';
import { localeDowMonthDay } from '../utils/format';
import { mapURL } from '../utils/funcs';
import logging from '../utils/logging';
import notify, { NotifyType } from '../utils/notify';

import config from '../config';
import data from '../data';

export const subscriptionRouter = express.Router();
subscriptionRouter.get('/', subscriptionRun); 

export async function subscriptionRun(req: express.Request, res: express.Response) {
  logging.infoF(LOG_NAME, 'subscription', 'Running subscription'); 
  const start = new Date();
  try { 
    await subscriptionFunction(null, null);
    logging.infoF(LOG_NAME, 'subscription', `Subscription finished in ${new Date().getTime() - start.getTime()}`); 
    res.status(204).end();
  } catch(e) {
    logging.errorF(LOG_NAME, 'subscription', 'Error running subscription', e);
    res.status(500).end();
  }
}

const LOG_NAME = 'routes.subscription';

export async function subscriptionFunction(ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig();

  const ps_data = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : null;
  logging.infoF(LOG_NAME, 'subscriptionFunction', `Data: ${JSON.stringify(ps_data)}`);

  const now = new Date();
  const first = DAYS(now, 21);
  const second = DAYS(now, 14);
  const third = DAYS(now, 7);
  const ended = DAYS(now, -1);

  // notify users 
  const gusers = await data.users.globalIds(DAYS(now, -30));
  logging.infoF(LOG_NAME, 'subscriptionFunction', `Checking ${gusers.length} recent users`);
  for(const guser of gusers) {
    const user = new ForaUser(guser.profile);
    await data.users.init(user, false);
    const { group, customer } = await customerGroup(user);
    const sub_info = await checkSubscription(user);
    const subscribed = sub_info ? subscriptionEnabled(sub_info, group) : false;
    if (sub_info?.trial_ends_at && !subscribed) {
      logging.infoFP(LOG_NAME, 'subscriptionFunction', guser.profile, `trial ends at ${sub_info?.trial_ends_at}`);
      let template: TemplateType;
      if (sameDay(first, new Date(sub_info.trial_ends_at))) {
        // One week after sign up that highlights the learning module and the search widgets
        // Email on mailjet: “Free Trial AF Pro - Welcome (1 of 3)” (template id: 5714957)
        // subscription.welcome
        template = TemplateType.Welcome;
      } else if (sameDay(second, new Date(sub_info.trial_ends_at))) {
        // Two weeks after sign up that says their trial is half over
        // Email on mailjet: “Free Trial AF Pro - Halfway (2 of 3)” (template id: 5946484)
        // Note: I created a NEW variable: {{var:trialexpiration}} = date of expiration: one month from sign up
        // subscription.update
        template = TemplateType.Update;
      } else if (sameDay(third, new Date(sub_info.trial_ends_at))) {
        // Three weeks after sign up that says their trial is about to expire and how to add payment info to continue
        // Email on mailjet: “Free Trial AF Pro - Final Week (3 of 3)” (template id: 5946612)
        // subscription.final
        template = TemplateType.Final;
      } else if(sameDay(ended, new Date(sub_info.trial_ends_at))) {
        template = TemplateType.Subscribe;
      }

      if (template) {
        logging.infoFP(LOG_NAME, 'subscriptionFunction', guser.profile, `Notfying ${template}`);
        const url = mapURL('/app');
        const notification: Notification = {
          type: NotificationType.Subscription,
          template,
          email: {
            rcpts: [{Email: guser.email, Name: guser.name}]
          },
          variables: {
            firstname: guser.name,
            url,
            trialexpiration: localeDowMonthDay(sub_info.trial_ends_at, user.locale, user.timeZone),
          }
        } 

        await notify(user, notification, NotifyType.EmailOnly, null, false, false, true);
      }
    }
  }

}