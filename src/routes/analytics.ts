import express from 'express';
import config from '../config';
import Dialog from '../session/dialog';
import { AnalyticsConfig } from '../types/shared';
const router = express.Router();

router.get('/', async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
  const dialog = await Dialog.loadSession('analytics', req.session, req.cookies, {offset, create_new:false, read_only:true, no_cache: true, check_tokens:false, stack:Error().stack}); // .then(dialog => {
  res.status(200);
  res.json({ trackid: config.get('GA4_TRACK_ID'), user_id: dialog && dialog.user ? dialog.user.profile : undefined, date: new Date() } as AnalyticsConfig).end();
});

export default router;
