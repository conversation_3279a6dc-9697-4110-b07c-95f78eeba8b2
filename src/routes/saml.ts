/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import KMS, { KeyManagementServiceClient } from '@google-cloud/kms';
import * as crypto from 'crypto';
import express from 'express';
import 'express-session';
import * as forge from 'node-forge';
import passport from 'passport';
import { MultiSamlStrategy, MultiStrategyConfig, Profile, VerifiedCallback } from 'passport-saml';
import * as querystring from 'querystring';
import util from 'util';

import config from '../config';
import data from '../data';
import Dialog from '../session/dialog';

import { AuthContext, AuthProviders } from '../types/shared';

import { DAYS } from '../utils/datetime';
import { positiveHex } from '../utils/funcs';
import logging from '../utils/logging';

import { AuthHelper } from '../auth/auth_helper';

import fs from 'fs';
import path from 'path';

const DEBUG = (require('debug') as any)('fora:routes:saml');
const LOG_NAME = 'routes.SamlController';

/**
 * SAML Routes Controller
 *
 * NOTES:
 * - Azure does not honor the signature - https://docs.microsoft.com/en-us/azure/active-directory/develop/single-sign-on-saml-protocol#signature
 *
 * Todo:
 * - Cache provider in memcache - https://github.com/bergie/passport-saml/blob/master/README.md#cache-provider
 */
class SamlController {
  async callback(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    if (logging.isDebug()) {
      DEBUG('Callback - user', req.user);
      DEBUG('Callback - assertion %j', (req.user as Profile).getAssertion());
      DEBUG('Callback - assertion xml', (req.user as Profile).getAssertionXml());
    }

    // USER
    // { issuer: 'https://sts.windows.net/6d15f921-dfbf-4dc5-a5cc-15886c91fb6f/',
    //   sessionIndex: '_75a94ba7-76ac-4e0a-9710-d86237607c91',
    //   nameID: '5YTlaVCE7mc6ZcAuv3J6UGbuG5fEmX5fQWCGl9In2jY',
    //   nameIDFormat: 'urn:oasis:names:tc:SAML:2.0:nameid-format:persistent',
    //   nameQualifier: undefined,
    //   spNameQualifier: undefined,
    //   'http://schemas.microsoft.com/identity/claims/tenantid': '6d15f921-dfbf-4dc5-a5cc-15886c91fb6f',
    //   'http://schemas.microsoft.com/identity/claims/objectidentifier': '1227f90c-07a9-471b-8c5a-ae024059dbc1',
    //   'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': '<EMAIL>',
    //   'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname': 'Demo',
    //   'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname': 'Dani',
    //   'http://schemas.microsoft.com/identity/claims/displayname': 'Dani Demo',
    //   'http://schemas.microsoft.com/identity/claims/identityprovider': 'https://sts.windows.net/6d15f921-dfbf-4dc5-a5cc-15886c91fb6f/',
    //   'http://schemas.microsoft.com/claims/authnmethodsreferences': 'http://schemas.microsoft.com/ws/2008/06/identity/authenticationmethod/password',
    //   getAssertionXml: [Function],
    //   getAssertion: [Function] }

    // getAssertion()

    let reload_ok = null;
    const wait_full_reload = new Promise<void>(c => reload_ok = c);
    const dialog: Dialog = await Dialog.loadSession('saml', req.session, req.cookies, {check_tokens:true, wait_full_reload, stack:Error().stack});
    if (!dialog) return AuthHelper.responseNoDialog(req, res);

    try {
      //
      const group = await dialog.groupByIdOrHost(null, req.session['hostname']);
      await AuthHelper.responseHandler(dialog, {provider: AuthProviders.Saml, context: AuthContext.App, group}, reload_ok, req, res);
    } catch (err) {
      await AuthHelper.responseError(dialog, {provider: AuthProviders.Saml, context: AuthContext.App, group: undefined}, reload_ok, res, err);
    }
  }

  async login(req: express.Request, res: express.Response, _next: express.NextFunction): Promise<void> {
    const dialog: Dialog = await Dialog.loadSession('saml', req.session, req.cookies, {read_only:true, check_tokens:true, stack:Error().stack});
    if (!dialog) return AuthHelper.responseNoDialog(req, res);

    try {
      // const group = 
      await dialog.groupByIdOrHost(null, req.session['hostname']);
      res.redirect('/');
    } catch (err) {
      res.redirect('/');
    }
  }
}

export function getSamlOptions(req: express.Request, done) {
  data.groups.byHost(req.session['hostname']).then(group => {
    if (group) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'getSamlOptions', `Retrieved GROUP configuration for host '${req.session['hostname']}' - ${logging.formatEntity(group)}`);
      const group_provider_settings = group.providerSettings(AuthProviders.Saml);
      const saml = group_provider_settings ? group_provider_settings.saml : null;
      const s_config: MultiStrategyConfig = Object.assign({
        disableRequestedAuthnContext: true,
        acceptedClockSkewMs: 1000,
        // forceAuthn: true,
      }, saml);

      // Load any of the certs that are file references
      const certs: string[] = [];
      if (s_config.cert) {
        if (Array.isArray(s_config.cert)) {
          for (const cert of s_config.cert) {
            if (cert.startsWith('./')) certs.push(fs.readFileSync(path.resolve(__dirname, '..', '..', cert), 'utf-8'));
            else certs.push(cert);
          }
        } else {
          const cert: string = s_config.cert as string;
          if (cert.startsWith('./')) certs.push(fs.readFileSync(path.resolve(__dirname, '..', '..', cert), 'utf-8'));
          else certs.push(cert);
        }
      }
      s_config.cert = certs;

      // Load any of the private certs that are file references
      if (s_config.privateCert && typeof s_config.privateCert === 'string') {
        if ((s_config.privateCert as string).startsWith('./')) {
          // Path to a file - nice for local testing
          s_config.privateCert = fs.readFileSync(path.resolve(__dirname, '..', '..', s_config.privateCert), 'utf-8').toString();
          // } else if (config.privateCert.startsWith('GCS::')) {
          //   // Stored in GCS
          //   config.privateCert = data.plugins.storagePlugin().fileContentsByName(config.privateCert.substring(5));
        } else {
          // Assume KMS otherwise
          const key_path = s_config.privateCert;

          s_config.privateCert = async function kmsSign(samlMessage) {
            const kms_client = new KMS.v1.KeyManagementServiceClient(config.get('GOOGLE_CLOUD_PROJECT'));

            // We are using sha256 for our keys
            samlMessage.SigAlg = 'http://www.w3.org/2001/04/xmldsig-more#rsa-sha256';

            // Make an object that we can digest for signing
            const samlMessageToSign: { [key: string]: any } = {};
            if (samlMessage.SAMLRequest) samlMessageToSign.SAMLRequest = samlMessage.SAMLRequest;
            if (samlMessage.SAMLResponse) samlMessageToSign.SAMLResponse = samlMessage.SAMLResponse;
            if (samlMessage.RelayState) samlMessageToSign.RelayState = samlMessage.RelayState;
            if (samlMessage.SigAlg) samlMessageToSign.SigAlg = samlMessage.SigAlg;

            const digest = {
              sha256: crypto
                .createHash('sha256')
                .update(querystring.stringify(samlMessageToSign))
                .digest(), // 'base64'),
            };

            logging.infoF(LOG_NAME, 'private_cert_sign', `key path = ${key_path}`);
            logging.infoF(LOG_NAME, 'private_cert_sign', `digest = ${util.format(digest)}`);

            return new Promise((c,r) => {
              kms_client.asymmetricSign({ name: key_path, digest }, (err, responses) => {
                if (logging.isDebug()) DEBUG('asymmetricSign', err, responses);
                if (err) r(err);
                else {
                  samlMessage.Signature = responses.signature.toString('base64');

                  if (logging.isDebug()) DEBUG('asymmetricSign message = ', samlMessage);
                  c(null);
                }
              });
            });
          }
        }
      }

      if (logging.isDebug()) logging.debugF(LOG_NAME, 'getSamlOptions', `Final SAML configuration for host '${req.session['hostname']}' - ${util.format(s_config)}`);

      done(null, s_config);
    } else {
      const err = new Error(`Group configuration not found for ${req.session['hostname']}`);
      logging.errorF(LOG_NAME, 'getSamlOptions', `Unable to get SAML configuration for host '${req.session['hostname']}'`, err);
      done(err, null);
    }
  }).catch(err => {
    logging.errorF(LOG_NAME, 'getSamlOptions', `Unable to get SAML configuration for host '${req.session['hostname']}'`, err);
    done(err, null);
  });
}

export function getStrategy() {
  return new MultiSamlStrategy(
    { getSamlOptions },
    // Verify Request
    (profile: Profile, done: VerifiedCallback) => done(null, profile as any),
    // Logout verify
    (profile: Profile, done: VerifiedCallback) => done(null, profile as any),
  );
}

passport.use(
  // Name of the authenticator
  'saml',

  // The actual strategy used by the authenticator
  getStrategy(),
);

passport.serializeUser((user, done) => {
  if (logging.isDebug()) DEBUG(`Serializing user ${util.format(user)}`);
  done(null, user);
});

passport.deserializeUser((user, done) => {
  if (logging.isDebug()) DEBUG(`De-serializing user ${util.format(user)}`);
  done(null, user);
});

export async function _sign(kms_client: KeyManagementServiceClient, kms_path: string, cert) {
  cert.md = forge.md.sha256.create();

  // const algorithmOid = forge.pki.oids[cert.md.algorithm + 'WithRSAEncryption'];
  const algorithmOid = forge.pki.oids[cert.md.algorithm];
  if (!algorithmOid) {
    const error = new Error('Could not compute certificate digest. Unknown message digest algorithm OID.');
    error['algorithm'] = cert.md.algorithm;
    throw error;
  }
  cert.signatureOid = cert.siginfo.algorithmOid = algorithmOid;

  // get TBSCertificate, convert to DER
  cert.tbsCertificate = forge.pki.getTBSCertificate(cert);
  const bytes = forge.asn1.toDer(cert.tbsCertificate);

  // digest
  cert.md.update(bytes.getBytes());
  const digest = { sha256: forge.util.encode64(cert.md.digest().getBytes()) };

  // Sign
  cert.signature = await kms_client.asymmetricSign({ name: kms_path, digest });
}

export async function generateCert(key_ring: string, key_name: string, key_version: string) {
  const attrs = [
    { name: 'commonName', value: 'askfora.com' },
    { name: 'countryName', value: 'US' },
    { shortName: 'ST', value: 'Vermont' },
    { name: 'localityName', value:'Norwich' },
    { name: 'organizationName', value: 'AskFora' },
    { shortName: 'OU', value: 'AskFora' },
  ];

  const extensions = [
    { name: 'basicConstraints', cA: true },
    {
      name: 'keyUsage',
      keyCertSign: true,
      digitalSignature: true,
      nonRepudiation: true,
      keyEncipherment: true,
      dataEncipherment: true,
    },
    {
      name: 'subjectAltName',
      altNames: [
        {
          type: 6, // URI
          value: 'https://askfora.com',
        },
      ],
    },
  ];

  const kms_client = new KMS.v1.KeyManagementServiceClient({ projectId: process.env.GOOGLE_CLOUD_PROJECT });
  const kms_path = kms_client.cryptoKeyVersionPath(process.env.GOOGLE_CLOUD_PROJECT, 'global', key_ring, key_name, key_version);

  const kms_public_key = await kms_client.getPublicKey({ name: kms_path });

  const cert = forge.pki.createCertificate();
  cert.serialNumber = positiveHex(forge.util.bytesToHex(forge.random.getBytesSync(9))); // the serial number can be decimal or hex (if preceded by 0x)
  cert.validity.notBefore = new Date();
  cert.validity.notAfter = new Date();
  cert.validity.notAfter.setDate(cert.validity.notBefore.getDate() + 365);

  cert.setSubject(attrs);
  cert.setIssuer(attrs);

  cert.publicKey = forge.pki.publicKeyFromPem(kms_public_key[0].pem);

  cert.setExtensions(extensions);

  await _sign(kms_client, kms_path, cert);

  const fingerprint = forge.md.sha1
    .create()
    .update(forge.asn1.toDer(forge.pki.certificateToAsn1(cert)).getBytes())
    .digest()
    .toHex()
    .match(/.{2}/g)
    .join(':');

  return {
    public: kms_public_key[0].pem,
    cert: forge.pki.certificateToPem(cert),
    fingerprint,
  };
}

// Build our router/controller and map them
const router = express.Router();
const controller = new SamlController();

router.use(passport.initialize());
router.use(passport.session());

// router.route('/login').get(passport.authenticate('saml', { failureRedirect: '/saml_failed', failureFlash: true }), (req, res) => res.redirect('/'));
router.route('/login').get(passport.authenticate('saml', { failureRedirect: '/saml_failed', failureFlash: true}), controller.login);
router.route('/callback').post(passport.authenticate('saml', { failureRedirect: '/saml_failed', failureFlash: true}), controller.callback);

router.get('/metadata', async(req: express.Request, res: express.Response, _next) => {
  // only provide the metadata if the host matches a valid group
  const group = await data.groups.byHost(req.session['hostname']);
  const group_provider_settings = group && group.provider === AuthProviders.Saml ? group.defaultProviderSettings : null;
  const saml = group_provider_settings ? group_provider_settings.saml : null;  
  if (saml) {
    const key_ring = config.get('SAML_KEY_RING', 'saml');
    const key_name = config.get('SAML_KEY_NAME', 'askfora');
    const key_version = config.get('SAML_KEY_VERSION', '1');

    const safe_name = group.company_name.replace(/ /g, '_');

    let cert = '';
    let template = 'saml_metadata.xml';
    if (req.query.cert) {
      const pems = await generateCert(key_ring, key_name, key_version);
      cert = pems['cert'].replace(/\r\n/g, '\\n');
      template = 'saml_metadata_cert.xml';
    }

    const metadata = fs
      .readFileSync(path.resolve(__dirname, '..', 'files', template))
      .toString()
      .replace(/\$\{validUntil\}/g, DAYS(new Date(), 365).toISOString())
      .replace(/\$\{issuer\}/g, saml.issuer)
      .replace(/\$\{callbackUrl\}/g, saml.callbackUrl)
      .replace(/\$\{cert\}/g, cert);

    res.status(200);
    res.contentType('application/xml');
    res.setHeader('Content-Disposition', `attachment; filename=AskFora_${safe_name}_Metadata.xml`);
    res.end(metadata);
  } else res.sendStatus(404);
});

router.get('/cert', async(req: express.Request, res: express.Response, _next) => {
  // only provide the cert if the host matches a valid group
  const group = await data.groups.byHost(req.session['hostname']);
  if (group) {
    const key_ring = config.get('SAML_KEY_RING', 'saml');
    const key_name = config.get('SAML_KEY_NAME', 'askfora');
    const key_version = config.get('SAML_KEY_VERSION', '1');

    const pems = await generateCert(key_ring, key_name, key_version);

    res.status(200);
    res.setHeader('Content-Disposition', 'attachment; filename=AskFora_SAML.cer');
    res.send(pems['cert']);
    res.end();
  } else res.sendStatus(404);
});

router.get('/logo', async(req: express.Request, res: express.Response, _next) => {
  let image = null;
  const group = await data.groups.byHost(req.session['hostname']);
  if (group && group.logo) image = Buffer.from(group.logo, 'base64');
  else image = fs.readFileSync(path.resolve(__dirname, '..', 'files', 'saml_logo.png'));

  res.status(200);
  res.contentType('image/png');
  res.end(image, 'binary');
});

export default router;
