import express from 'express';
import { setTimeout } from "timers/promises";

//import { GlobalType } from '../types/items';

import logging from '../utils/logging';

import config from '../config';
import data from '../data';
import { buildCategories } from '../skills';

export const reloadRouter = express.Router();
reloadRouter.get('/', reloadRun); 

export async function reloadRun(req: express.Request, res: express.Response) {
  logging.infoF(LOG_NAME, 'reload', 'Running reload'); 
  const start = new Date();
  try { 
    await reloadFunction(null, null);
    logging.infoF(LOG_NAME, 'reload', `Reload finished in ${new Date().getTime() - start.getTime()}`); 
    res.status(204).end();
  } catch(e) {
    logging.errorF(LOG_NAME, 'reload', 'Error running reload', e);
    res.status(500).end();
  }
}

const LOG_NAME = 'routes.reload';

export async function reloadFunction(ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig();
  const ps_data = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : null;
  logging.infoF(LOG_NAME, 'reloadFunction', `Data: ${JSON.stringify(ps_data)}`);

  // start export of vanities
  const user_export = await data.people.export();
  const reload_categories = 
    new Date().getHours() === 0 ?  buildCategories() : new Promise<void>(c => c());
  //const reload_courses = data.plugins.bigQueryPlugin().exportGlobal(GlobalType.Course)

  if (user_export) {
    let source;
    while(!source) {
      source = await data.people.checkExport(null, user_export);
      await setTimeout(60000);
    }
    await data.people.finishExport(null, source);
  } else logging.warnF(LOG_NAME, 'reloadFunction', 'Users not exported');

  await reload_categories;
  //await reload_courses;
}