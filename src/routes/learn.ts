import express from 'express';
import 'express-session';
import { GoogleAuth } from 'google-auth-library';
import _ from 'lodash';

import lang from '../lang';

import DataCache from '../session/data_cache';
import ForaUser from '../session/user';

import { TemplateType, Update } from '../types/globals';
import { Person } from '../types/items';
import { EntityType, NotificationType, Uid } from '../types/shared';

import { SECONDS } from '../utils/datetime';
import { mapURL } from '../utils/funcs';
import logging from '../utils/logging';
import { mailTemplate } from '../utils/mail';
import notify, { NotifyType } from '../utils/notify';
import peopleUtils from '../utils/people';

import config from '../config';
import data from '../data';
import Learn from '../skills/learn';
import { internalPub } from './pub';
import { doExport } from './update';

const DEBUG = (require('debug') as any)('fora:routes:learn');

const LOG_NAME = 'routes.learn';

export interface LearnArgs {
  profile: Uid;
  person: Uid;
  age: number;
  timeout: number;
  max_per_profile: number;
  no_export?: boolean;
  force_local?: boolean;
  learn_id?: Uid;
}

export async function internalLearn(message: Partial<LearnArgs>) {
  logging.infoFP(LOG_NAME, 'internalLearn', message.profile, `internal learn ${JSON.stringify(message)}`);

  if (!message.person && !message.age && !message.max_per_profile) {
    if (config.isRunningOnGoogle()) {
      const url = `https://us-central1-${config.get('GOOGLE_CLOUD_PROJECT')}.cloudfunctions.net/learn`;

      logging.infoFP(LOG_NAME, 'internalLearn', message.profile, `Learning all available people at ${url} with ${JSON.stringify(message)}`);

      try {
        const auth = new GoogleAuth();
        const client = await auth.getIdTokenClient(url);
        await client.request({
          url, 
          method: 'POST', 
          timeout: 3600 * 1000,
          body: JSON.stringify(message), 
          headers: { "content-type": "application/json"}
        });
        logging.infoFP(LOG_NAME, 'internalLearn', message.profile, `Posted learn`);
      } catch(e) {
        logging.errorFP(LOG_NAME, 'internalLearn', message.profile, `Error posting learn`, e);
      }
    } else if(message.learn_id) {
      await learnImport(message.learn_id);
    } else if(message.profile) {
      await learnProfile(message.profile);
    }
  } else {
    const did_pub = message.force_local ? false : await internalPub('learn', message);
    if (!did_pub) await learnFunction({data: Buffer.from(JSON.stringify(message)).toString('base64')}, null);
  }
}

export async function learnRun(req: express.Request, res: express.Response) {
  logging.infoFP(LOG_NAME, 'learnRun', req.body.profile, `Received ${JSON.stringify(req.body)}`);

  await data.dataCheck();

  const profile = req.body.profile as Uid;
  const learn_id = req.body.learn_id as Uid;
  try {
    if (learn_id) await learnImport(learn_id);
    else await learnProfile(profile);
    logging.infoF(LOG_NAME, 'learnRun', 'Learn run finished');
    res.sendStatus(204).end();
    return;
  } catch(e) {
    logging.errorFP(LOG_NAME, 'learnRun', profile, `Error learning people`, e);
    res.sendStatus(500).end();
  }
}

async function learnProfile(profile: Uid) {
  const user = new ForaUser(profile);
  if (await data.users.init(user, false)) {
    const self = await data.people.getUserPerson(user, user.profile);

    const people = await data.people.load(user, undefined, true, false);
    const learned = people && people.length ? await Learn.learnPeople(user, people) : null;
    if (learned && learned.length) {
      await data.people.saveAll(user, learned);

      await doExport(user.profile, { force_internal: true });
    }

    await data.users.message(user, lang.init.DONE_LEARNING);

    if (!self) logging.warnFP(LOG_NAME, 'learnProfile', user.profile, `User has no self`);
    else if (!self.learned || !self.learned.length || self.learned.reduce((a,b) => a > b ? a : b, new Date(0)).getTime() === 0) {
      const url = mapURL(`/app/ask`, user.auth_group);
      await mailTemplate([{ Email: user.email, Name: user.name, }], [],
        NotificationType.Onboarding,
        TemplateType.Search_Network,
        { firstname: user.name, url  }
      );

      await data.users.onboardingSet(user, TemplateType.Search_Network);
    }
  }
}

async function learnImport(learn_id: Uid) {
  const import_logs = (await data.imports.importLogs(null, learn_id)).filter(log => !log.complete);
  const profiles = _.uniq(import_logs.map(l => l.user));
  const users = await Promise.all(profiles.map(async profile => {
    const user = new ForaUser(profile);
    await data.users.init(user, false);
    return user;
  }));

  for (const user of users) {
    const people = await data.people.byAttributeId(user, import_logs.filter(log => log.user === user.profile).map(log => log.person));
    const learned = people && people.length ? await Learn.learnPeople(user, people as Person[]) : [];
    if (learned && learned.length) {
      await data.people.saveAll(user, learned);
    }
  }

  await data.imports.completeImportLogs(import_logs);
}

async function learnUsers(profile: Uid = null, person_id: Uid = null, age = 30, timeout = 300, max_per_profile = 10, no_export = false, silent = false) {
  if (!config.configLoaded()) await config.loadConfig();
  // look for un-learned persons of users created since
  let users = []; // = profile ? [new ForaUser(profile)] : _.shuffle(await data.users.globals());

  const now = new Date();
  const deadline = SECONDS(now, timeout ? timeout : 300);

  let count = 0;
  let save_sets: {[key:string]: Person[]} = {};

  let fora_user = profile ? new ForaUser(profile) : null;

  let found_people: {[key: string]: ForaUser} = {};
  let user_people = person_id ? {} : await data.people.byAttributeLearned(fora_user, 5, age !== undefined && age !== null ? age : 30, max_per_profile !== null ? max_per_profile : 10);

  let learn_people = null; 
  let user_self = fora_user ? await data.people.getUserPerson(fora_user, profile) : null;

  if (profile) users = [fora_user];
  else if(user_people && Object.keys(user_people).length) users = Object.keys(user_people).map(profile => new ForaUser(profile));

  logging.infoF(LOG_NAME, 'learnUsers', `Learn people for ${profile ? profile : users.length}${profile ? '' : ' users'} starting at ${now} until ${deadline}`);

  while (new Date() < deadline) {
    while (!learn_people || !learn_people.length) {
      if (users.length) {
        fora_user = new ForaUser(users.pop().profile);
        learn_people = user_people[fora_user.profile];
        if (learn_people || profile) {
          if (await data.users.init(fora_user, false)) {
            fora_user.refreshing = true;
            await data.users.save(fora_user, true, false, false);
            if (profile && person_id) {
              const learn_person = fora_user && person_id ?  user_self && person_id === user_self.id ? user_self : await data.people.byId(fora_user, person_id) : null;
              if (learn_person) {
                learn_people = [learn_person];
                logging.infoFP(LOG_NAME, 'learnUsers', fora_user ? fora_user.profile : null, `::${learn_person.id} Learning ${learn_person.self ? 'self' : 'person'}`);
              }
            }
            else if (learn_people) logging.infoFP(LOG_NAME, 'learnUsers', fora_user ? fora_user.profile : null, `Learning up to ${learn_people.length} people`);
            break;
          } else {
            logging.warnFP(LOG_NAME, 'learnUsers', fora_user.profile, `Skipping learning ${learn_people.length} because init failed`);
            learn_people = null;
          }
        }
      } else {
        logging.infoFP(LOG_NAME, 'learnUsers', fora_user ? fora_user.profile : null, 'Done learning');
        break;
      }
    }

    if (!learn_people || !learn_people.length) break;

    let learn_self = null;
    const people = [];
    for (const p of learn_people.splice(0, 10)) {
      const person = profile && person_id ? p : await data.people.byId(fora_user, p.id);
      if (person) {
        if(person.self || (user_self && user_self.id && person.id && person.id === user_self.id) ) {
          if (person.urls && person.urls.length && 
            (!person.learned || person.learned.reduce((a,b) => a > b ? a : b, new Date(0)).getTime() === 0 || person.id === person_id)) {
            logging.warnFP(LOG_NAME, 'learnUsers', fora_user ? fora_user.profile : null, `::${person.id} Learning self ${user_self ? user_self.id : '"no self id"'} ${person.self ? '': 'not'} self ${fora_user.email} ${JSON.stringify(person.self.comms)} because learned ${JSON.stringify(person.learned)}`);
            learn_self = person;
            learn_self.self = true;
          }
        } else if(!person.comms.includes(fora_user.email)) people.push(person);
      }
    }

    if (learn_self) {
      let learn = new Learn(fora_user);
      learn_self = await learn.learnPerson(learn_self, true, true);
      if (learn_self) {
        logging.infoFP(LOG_NAME, 'learnUsers', fora_user.profile, `Saving learned self ${learn_self.id}`);
        learn_self = await data.people.savePerson(fora_user, learn_self);
        if (learn_self) {
          const cache = new DataCache(fora_user, learn_self, fora_user.loaded_groups ? Object.values(fora_user.loaded_groups) : [], `learn_${fora_user.profile}`);
          // await cache.loadCache();
          cache.cachePerson(learn_self, false);
 
          await data.users.refreshSet(fora_user, EntityType.Person).catch(err => { logging.errorFP(LOG_NAME, 'learnUsers', fora_user.profile, 'Error setting refresh after learn', err); });

          fora_user.refreshing = false;
          await data.users.save(fora_user, true, false, false);

          if (!silent) {
            const url = mapURL('/app/profile');
            await notify(fora_user, {
                type: NotificationType.Learn,
                email: {
                  rcpts: [{Name: learn_self.displayName, Email: fora_user.email}],
                  subject: `I've updated your AskFora Pofile`,
                  message: `I found new information about you from public sources. You can edit your profile here: ${url}`,
                },
                webpush: {
                  notification: {
                    tag: learn_self.id,
                    click_action: url,
                    title: `I've updated your AskFora Pofile`,
                    body: `I found new information about you from public sources.`,
                  },
                },
                info: [peopleUtils.getPersonInfo(fora_user.profile, learn_self)],
                variables: { firstname: learn_self.displayName.split(' ')[0], url },
              },
              NotifyType.PushAndEmail,
            null, false);
          }
          logging.infoFP(LOG_NAME, 'learnUsers', fora_user.profile, 'Finished learning self');
        } else logging.infoFP(LOG_NAME, 'learnUsers', fora_user.profile, 'Learning self returned empty');
      }
      learn = null;
    }

    if (!people.length) continue;

    logging.infoFP(LOG_NAME, 'learnUsers', fora_user.profile, `Learning ${people.length} ${people.length === 1 ? 'person' : 'people'}`);

    const learned = await Learn.learnPeople(fora_user, people, person_id !== null && person_id !== undefined);

    if (learned.length) {
      if (!save_sets[fora_user.profile]) save_sets[fora_user.profile] = learned;
      else save_sets[fora_user.profile] = save_sets[fora_user.profile].concat(learned);
    }

    logging.infoFP(LOG_NAME, 'learnUsers', fora_user.profile, `Learned ${learned.length} person/people`);
    if (learned.length > 0) found_people[fora_user.profile] = fora_user;

    logging.infoFP(LOG_NAME, 'learnUsers', fora_user.profile, `Learning, counting ${count} deadline ${deadline}`);

    count++;
  }

  const savers: Promise<void>[] = [];
  for (const profile in save_sets) {
    if (logging.isVerbose(profile)) logging.verboseFP(LOG_NAME, 'learnPerson', profile, `Saving learned people ${JSON.stringify(save_sets[profile])}`);
    const save_people = data.people.saveAll(found_people[profile], save_sets[profile])
      .catch(e => logging.errorFP(LOG_NAME, 'learnPerson', profile, `Error saving learned people`, e));
    savers.push(save_people);
    if (no_export) {
      await save_people;
      const cache = new DataCache(fora_user, user_self, fora_user.loaded_groups ? Object.values(fora_user.loaded_groups) : [], `learn_${fora_user.profile}`);
      for (const person of save_sets[profile]) {
        savers.push(cache.cachePerson(person, false));
      }
    }
  }

  await Promise.all(savers);

  // Now that we have no people returned, only do the export if previous calls returned us people
  const exporters: Promise<void>[] = [];
  for (const fora_user of Object.values(found_people)) {
    data.users.refreshSet(fora_user, EntityType.Person).catch(err => {
      logging.errorFP(LOG_NAME, 'learnUsers', fora_user.profile, 'Error setting refresh after learn', err);
    });
    logging.infoFP(LOG_NAME, 'learnUsers', fora_user.profile, 'Finished learning people, exporting');
    const opts: Partial<Update> = {};
    if (!config.isRunningOnGoogle()) opts.force_internal = true;
    if (!no_export) {
      exporters.push(doExport(fora_user.profile, opts).
        then(() => logging.infoFP(LOG_NAME, 'learnUsers', fora_user.profile, `Exported people for ${fora_user.profile} after learn`)).
        catch(err => logging.errorFP(LOG_NAME, 'learnUsers', fora_user.profile, 'Error exporting people after learn', err))
      );
    }
  }

  await Promise.all(exporters);

  fora_user = null;
  users = null;
  user_people = null;
  found_people = null;
  learn_people  = null;
  save_sets = null;

  logging.infoF(LOG_NAME, 'learnUsers', `Learned ${count} people ending at ${deadline}`);
}

export async function learnFunction(ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig(true);
  const data = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : null;

  logging.infoFP(LOG_NAME, 'learnFunction', data?.profile, `Function called with ${data ? JSON.stringify(data) : 'null'}`);

  const age = data?.age !== undefined ? parseInt(data.age, 10) : null;
  const timeout = data?.timeout !== undefined ? parseInt(data.timeout, 10) : null;
  const max_per_profile = data?.max_per_profile !== undefined ? parseInt(data.max_per_profile, 10) : null;
  const no_export = data?.no_export === true;
  await learnUsers(data?.profile, data?.person, isNaN(age) ? null : age, isNaN(timeout) ? null : timeout, isNaN(max_per_profile) ? null : max_per_profile, no_export, data?.silent).catch(err => {
    logging.errorF(LOG_NAME, 'learnFunction', `Error learning users ${data && data.profile ? data.profile : ''}`, err); 
  });
}
