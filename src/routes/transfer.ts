import config from '../config';
import data from '../data';

import { InternalError } from '../types/globals';
import { MIDNIGHT } from '../utils/datetime';
import logging from '../utils/logging';

import wise from '../sources/wise_controller';

const LOG_NAME = 'routes.transfer';

export async function transferFunction(ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig(true);

  let transfers = await data.plugins.storagePlugin().transfers();

  let client = wise();
  let profile = (await client.getProfiles({})).find(p => p.type === 'business');

  logging.infoF(LOG_NAME, 'transferFunction', `Checking ${transfers.length} transfers for profile ${profile.id}`);

  let b;
  let balance = await client.getBorderlessAccounts({profileId: profile.id});
  if (balance && balance.length && balance[0].balances  && balance[0].balances.length) {
    b = balance[0].balances.find(b => b.amount && b.amount.currency === 'USD');
  }

  balance = null;

  let start = new Date();
  if(b) {
    let account_balance = b.amount.value; 
    logging.infoF(LOG_NAME, 'transferFunction', `Starting balance: ${account_balance}`);

    for (const trf of transfers.filter(t => !t.fund).sort((a,b) => a.created.getTime() - b.created.getTime())) {
      if (trf.created < start) start = trf.created;
      const quote = await client.getQuote({quoteId: trf.quoteUuid});
      if (quote && (account_balance > quote.sourceAmount || config.isEnvDevelopment())) {
        if (!config.isEnvProduction() && !config.isEnvOffline()) {
          logging.infoF(LOG_NAME, 'transferFunction', `Topping up profile ${profile.id} by ${quote.sourceAmount}`);
          await client.testTopUp({profileId: profile.id, balanceId: b.id, currency: 'USD', amount: quote.sourceAmount});
        } 
        
        const fund = await client.fundTransfer({profile: profile.id, transferId: trf.wise_id});
        if (fund.error) {
          logging.errorF(LOG_NAME, 'transferFunction', `Error funding trasfer ${trf.id}`, new InternalError(fund.statusCode, fund.message));
        } else {
          logging.infoF(LOG_NAME, 'transferFunction', `Funded transfer ${trf.id}`);
          trf.fund = fund;
          await data.plugins.storagePlugin().transferSave(trf);
          account_balance -= quote.sourceAmount;
        }
      }
    }
  
    logging.infoF(LOG_NAME, 'transferFunction', `Ending balance: ${account_balance}`);
  } else logging.warnF(LOG_NAME, 'transferFunction', `No balances found`);

  //reconcile
  let wise_transfers = await client.getTransfers({profile: profile.id, createdDateStart: MIDNIGHT(start)});
  if (transfers.length !== wise_transfers.length) {
    logging.warnF(LOG_NAME, 'transferFunction', `Mismatch in transaction records local ${transfers.length} remote ${wise_transfers.length}`);
  } 

  for (const trf of transfers) {
    // wise keeps ids as numbers, we use strings
    const wt = wise_transfers.find(w => w.id === trf.wise_id);
    if (!wt) logging.warnF(LOG_NAME, 'transferFunction', `Transfer ${trf.id} not found at wise`);
    if (wt && trf.status !== wt.status) {
      logging.infoF(LOG_NAME, 'transferFunction', `Updating transfer ${trf.id} status from ${trf.status} to ${wt.status}`);
      trf.status = wt.status;
      await data.plugins.storagePlugin().transferSave(trf);
    }
  }

  transfers = null;
  wise_transfers = null;
  client = null;
  profile = null;
}