import express from 'express';
import 'express-session';
import multer from 'multer';
import { setTimeout } from "timers/promises";

import Dialog, { /*INTERNAL_PING,*/ Topics } from '../session/dialog';

import { InternalError, Message, Tracking } from '../types/globals';
import { GlobalType } from '../types/items';
import { ServerChatBody, ServerChatResponse } from '../types/shared';

import { hash } from '../utils/funcs';
import { promptInfo } from '../utils/info';
import logging from '../utils/logging';
import parsers from '../utils/parsers';


import config from '../config';
import { reserved } from '../plugins';

import procBool from '../proc/bool';
import procEntity from '../proc/entity';
import procEvents from '../proc/events';
import procLocations from '../proc/locations';
import procNumber from '../proc/number';
import procPlaces from '../proc/places';
import procQuery from '../proc/query_v1';
import procSkills from '../proc/skills';
import procTime from '../proc/time';
import procTypes from '../proc/types';
import procUrls from '../proc/urls';

const router = express.Router();
const upload = multer(); // for parsing multipart/form-data
const LOG_NAME = 'routes.chat';

async function sendReply(req: express.Request, res: express.Response, dialog: Dialog) {
  let profile = dialog.user.profile;

  // process actions and prompts
  if (dialog.message.message) {
    procBool(dialog);
    procNumber(dialog);
    procTime(dialog);
    procTypes(dialog);
    procEvents(dialog);
    procPlaces(dialog);
    procLocations(dialog);
    procUrls(dialog);
    procSkills(dialog);
  } else dialog.message.message = '';

  if (dialog.message.message.length && dialog.cache.people_loaded) {
    if (!parsers.checkKeyword(dialog.message.message.toLowerCase(), reserved, true)) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'sendReply', profile, `Waiting for people names to parse ${dialog.message.message}`);
      await dialog.cache.people_loaded;
    }
  }

  await procEntity(dialog);
  dialog.makeFilters();
  procQuery(dialog);

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'sendReply', profile, `actions: ${JSON.stringify(dialog.actions)}`);

  dialog.reRun();

  // let ping = INTERNAL_PING.ping;
  // let max_ping = 10;
  while (dialog.isProcessing(false) || dialog.doReRun) { // ping === INTERNAL_PING.ping) {
    dialog.clearRun();
    dialog.clearCommand();
    // by default ping every 30s
    // dialog.defaultPing();
    logging.infoFP(LOG_NAME, 'sendReply', profile, `Running action with Topic ${dialog.topic}`);
    dialog.currentPlugin().runAction(dialog);
    logging.infoFP(LOG_NAME, 'sendReply', profile, `Setting prompt with Topic ${dialog.topic}`);
    dialog.currentPlugin().setPrompt(dialog);
    logging.infoFP(LOG_NAME, 'sendReply', profile, `Exit Topic is ${dialog.topic}`);

    // ping = dialog.getPing();
    // max_ping -= 1;

    // let async jobs complete
    while (dialog.isProcessing(false)) { // ping === INTERNAL_PING.ping) {
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      await setTimeout(1000);
      await dialog.runSession();
    }
    // if (max_ping <= 0) throw Error('Internal ping timeout');
  }


  // if (dialog.isProcessing()) dialog.quickPing();

  const curr_dialog = dialog.currentDialog();

  const response = {
    id: curr_dialog  && (curr_dialog.info.length || curr_dialog.replies.length) ? curr_dialog.id : hash(new Date().getTime().toString()),
    reply: undefined,
    info: undefined,
    answers: [],
    page: 0,
    hint: null,
    suggestion: null,
  } as ServerChatResponse;

  if (dialog.topic !== Topics.NOOP) {
    if (curr_dialog !== null && !curr_dialog.sent) {
      if (curr_dialog.prompt.length) {
        response.reply = curr_dialog.prompt;
        curr_dialog.sent = true;
      }
      if (curr_dialog.info.length) {
        response.info = curr_dialog.info;
        const info_ids = [response.id];
        for (const info of response.info) info_ids.push(info.id);
        response.id = hash(info_ids.join('-'));
        curr_dialog.sent = true;
      }
      if (curr_dialog.answers.length) {
        response.answers = curr_dialog.answers;
        curr_dialog.sent = true;
      }
      if (curr_dialog.page) {
        response.page = curr_dialog.page;
        curr_dialog.sent = true;
      }
      if (curr_dialog.hint) {
        response.hint = curr_dialog.hint;
        curr_dialog.sent = true;
      }
      if (curr_dialog.suggestion) {
        response.suggestion = curr_dialog.suggestion;
        curr_dialog.sent = true;
      }
      if (curr_dialog.quick_replies) {
        response.quick_replies = curr_dialog.quick_replies;
        curr_dialog.sent = true;
      }
    }
  }

  const command = dialog.getCommand();
  if (command !== null) {
    Object.assign(response, command);

    // used for feedback
    if (curr_dialog) {
      curr_dialog.last_command = {};
      Object.assign(curr_dialog.last_command, command);
    }

    logging.infoFP(LOG_NAME, 'sendReply', profile, `Command ${JSON.stringify(command)}`);
  }

  dialog.restoreTopic();

  if (req.session) { if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'sendReply', req.session['profile'], `saving session ${req.session.id}`); } 

  if (config.isEnvOffline()) {
    logging.infoFP(LOG_NAME, 'reply', profile, JSON.stringify(response));
  } else if (dialog.user.debug || config.isEnvDevelopment()) {
    logging.infoFP(LOG_NAME, 'reply', profile, JSON.stringify(response));
  }

  await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  res.json(response).end();
}

router.get('/', async (req: express.Request, res: express.Response, _next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  // console.log('GOT COOKIES', req.cookies);
  let dialog: Dialog = null;
  try {
    // check the cache and then the db for a session
    const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
    const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
    dialog = await Dialog.loadSession('gmsg', req.session, req.cookies, {offset, create_new:true, check_tokens:true, stack:Error().stack});

    if (!dialog) throw new InternalError(500, "No dialog returned for get");

    dialog.user.track(req.query as Partial<Tracking>);
    dialog.user.setOffset(req.query.offset as string);
    dialog.user.setLocale(req.header('accept-language'));
    dialog.user.setTimeZone(req.query.timeZone as string);

    let msg = req.query.message;
    if (!msg) msg = '';

    dialog.message = {
      type: GlobalType.Message,
      message: msg,
      entities: [],
      ping: 0,
      offset: dialog.user.offset,
      locale: dialog.user.locale,
      timeZone: dialog.user.timeZone,
      link: true,
      time: new Date(),
    } as Message;

    if (dialog.user.debug) logging.infoFP(LOG_NAME, 'get', dialog.user.profile, JSON.stringify(dialog.message));

    await sendReply(req, res, dialog);
    await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  } catch (err) {
    logging.errorFP(LOG_NAME, 'get', dialog ? dialog.user.profile : null, 'Chat get error', err);
    if (dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));
    res.json({ id: new Date().getTime(), reply: [promptInfo('I ran into an error')], answers: ['Send feedback'] });
  }
});

router.post('/', upload.array(), async (req: express.Request, res: express.Response, _next) => {
  // console.log('GOT COOKIES', req.cookies, req.session);
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  // res.set('X-Accel-Buffering', 'no');
  let dialog: Dialog;
  try {
    // check the cache and then the db for a session
    dialog = await Dialog.loadSession('pmsg', req.session, req.cookies, {offset:req.body.offset, create_new:true, check_tokens: true, stack:Error().stack});
    if (dialog) {
      dialog.user.track(req.query as Partial<Tracking>);
      dialog.user.setOffset(req.query.offset as string);
      dialog.user.setLocale(req.header('accept-language'));
      dialog.user.setTimeZone(req.query.timeZone as string);
      
      const message: ServerChatBody = req.body;
      const ping = parseInt(message.ping as any);
      const max = parseInt(message.max as any);
      dialog.message = {
        type: GlobalType.Message,
        message: typeof message.message === 'string' ? message.message : '',
        entities: Array.isArray(message.entities) ? message.entities : [],
        ping: isNaN(ping) ? 0 : ping,
        offset: dialog.user.offset,
        locale: dialog.user.locale,
        timeZone: dialog.user.timeZone,
        command: message.command === true,
        link: false,
        time: new Date(),
        data: message.data,
        max: isNaN(max) ? null : max,
      } as Message;

      /*if (config.isEnvOffline()) {
        logging.infoFP(LOG_NAME, 'post', dialog.user.profile, JSON.stringify(dialog.message));
        logging.infoFP(LOG_NAME, 'context', dialog.user.profile, JSON.stringify(dialog.context));
      } else if (dialog.user.debug || config.isEnvDevelopment()) {
        logging.infoFP(LOG_NAME, 'post', dialog.user.profile, JSON.stringify(dialog.message));
        logging.infoFP(LOG_NAME, 'context', dialog.user.profile, JSON.stringify(Object.keys(dialog.context)));
        if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'context', dialog.user.profile, JSON.stringify(dialog.context));
      }*/

      if (dialog.context.error) throw dialog.context.error;
      await sendReply(req, res, dialog);
    } else {
      res.json({ ping: 100 });
    }
  } catch (err) {
    // TODO:(all) - We should send the error to the user (hidden) so that it can come back to us in the feedback
    if (err.stack) logging.errorFP(LOG_NAME, 'post', dialog && dialog.user ? dialog.user.profile : null, 'Chat error', err);
    // reset
    if (dialog) {
      // await dialog.releaseSessionHold();
      dialog.last_error = err;
      dialog.newDialog({ next_topic: Topics.DEFAULT });
      dialog.clearContext();
      await dialog.saveSession(true, 'error');
    }
    res.json({ id: new Date().getTime(), reply: [promptInfo('I ran into an error')], answers: ['Send feedback'] });
  }
});

export default router;
