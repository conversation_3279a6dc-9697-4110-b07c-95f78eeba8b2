import express from 'express';
import 'express-session';
import { GoogleAuth } from 'google-auth-library';
import { setTimeout } from "timers/promises";
import util from 'util';

import Dialog from '../session/dialog';
import * as SessionCache from '../session/session_cache';
import ForaUser from '../session/user';

import { InternalError, Update } from '../types/globals';
import { IEntity, Person, Tag, personRef } from '../types/items';
import { EntityType, TagType, Uid, findTypeTags } from '../types/shared';

import { DAYS, HOURS, MINUTES, sameDay } from '../utils/datetime';
import { arraysIntersect, flatten, hash } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import { AuthProvider } from '../auth/auth_provider';
import config from '../config';
import data from '../data';
import { createCustomer, lookupCustomer } from '../sources/hyperline_controller';
import { SourceController } from '../sources/source_controller';

import { internalLearn } from './learn';
import { internalPub } from './pub';

const DEBUG = (require('debug') as any)('fora:routes:update');

const LOG_NAME = 'routes.update';

export async function internalUpdate(message: Partial<Update>) {
  logging.infoFP(LOG_NAME, 'internalUpdate', message.profile, `Internal update ${JSON.stringify(message)}`);

  if((message.first_run || message.do_search) && config.isRunningOnGoogle()) {
    const url = `https://us-central1-${config.get('GOOGLE_CLOUD_PROJECT')}.cloudfunctions.net/update`;

    logging.infoFP(LOG_NAME, 'internalUpdate', message.profile, `Calling update ${url} with ${JSON.stringify(message)}`);

    while(true) {
      try {
        const auth = new GoogleAuth();
        const client = await auth.getIdTokenClient(url);
        await client.request({
          url, 
          method: 'POST', 
          timeout: 3600 * 1000,
          body: JSON.stringify(message), 
          headers: { "content-type": "application/json"}
        });
        logging.infoFP(LOG_NAME, 'internalUpdate', message.profile, `Posted update`);
        break;
      } catch(e) {
        if (e.code === 'ECONNRESET' || e.message === 'network timeout') logging.errorFP(LOG_NAME, 'internalUpdate', message.profile, `Error posting update - retrying`, e);
        else {
          logging.errorFP(LOG_NAME, 'internalUpdate', message.profile, `Error posting update`, e);
          break;
        }
      }
    }
 
  } else {
    const did_pub = message.force_internal ? null : await internalPub('update', message);
    if (!did_pub) await updateFunction({data: Buffer.from(JSON.stringify(message)).toString('base64')}, null);
  }
}

export async function updateRun(req: express.Request, res: express.Response) {
  logging.infoFP(LOG_NAME, 'updateRun', req.body.profile, `Received ${JSON.stringify(req.body)}`);

  await data.dataCheck();

  const message = req.body as Partial<Update>;
  try { 
    await updateFunction({data: Buffer.from(JSON.stringify(message)).toString('base64')}, null);
    logging.infoF(LOG_NAME, 'updateRun', 'Update run finished');
    res.sendStatus(204).end();
    return;
  } catch(e) {
    logging.errorF(LOG_NAME, 'updateRun', 'Error running update', e);
    res.status(500).end();
  }
}

export async function doExport(profile: string, opts: Partial<Update> = null): Promise<boolean> {
  logging.infoFP(LOG_NAME, 'doExport', profile, `Export options ${JSON.stringify(opts)}`);
  const name = await data.people.export(profile);
  if (name) {
    const message = {profile, name};
    if (opts) Object.assign(message, opts);
    await internalUpdate(message);
  } else logging.errorFP(LOG_NAME, 'doExport', profile, `Export failed to start ${JSON.stringify(opts)}`, new InternalError(500, `Export failed to start for ${profile} ${JSON.stringify(opts)}`));
  return false;
}

export async function userCheck(user: ForaUser, do_export = false): Promise<Person> {
  let self = await data.people.getUserPerson(user, user.profile);
  let group;
  let updated = false;
  if (!self || !self.id) {
    updated = true;
    logging.infoFP(LOG_NAME, 'userCheck', user.profile, `Restoring self - ${user.provider} `);
    group = user.auth_group;
    const provider_settings = group && group.provider_settings ? group.provider_settings : AuthProvider.getDefaultProviderSettings(user.provider);
    const n_user = await AuthProvider.getProviderUser(user.tokens, provider_settings, null, null);
    if (n_user) {
      const provider_self = await AuthProvider.getProviderUserSelf(user.tokens, n_user, provider_settings, null);
      if (self) {
        peopleUtils.mergePeople(self, provider_self, true);
        self.id = provider_self.id;
        self.self = true;
      } else self = provider_self;
    } else if (self) {
      logging.warnFP(LOG_NAME, 'userCheck', user.profile, `No provider user, generating temp id`);
      self.tempId();
    } else {
      logging.warnFP(LOG_NAME, 'userCheck', user.profile, `No provider user, failed to find self`);
      self = null;
      return;
    }
  }

  if (!self.learned || self.learned.length === 0) {
    self.learned = [new Date(Number.MAX_SAFE_INTEGER / 1000)];
    updated = true;
  }

  if (!self.photos || self.photos.length === 0) {
    const initials = parsers.findInitials(self.displayName);
    self.photos = [`/api/contacts/default/${initials}/photo`]
    updated = true;
  }

  let save_vanity = false;
  if (!self.urls || self.urls.length === 0) {
    const host = config.get('DEFAULT_HOST');
    if (!user.vanity) {
      save_vanity = true;
      await data.users.generateVanity(user, self, group);
    }
    const vanity_url = `https://${host}/profile/${user.vanity}`;
    self.urls = [vanity_url];
    updated = true;
  }

  // fix missing vanity
  if (self.urls) {
    const ulen = self.urls.length;
    self.urls = self.urls.filter(u => u && !u.match(/\/undefined$/));
    updated = updated || self.urls.length !== ulen;
  }

  const self_tag = new Tag(TagType.skill, 'self');
  if (!self.tags || self.tags.length === 0) {
    self.tags = [self_tag];
    updated = true;
  } else {
    const skills = findTypeTags(self.tags, TagType.skill);
    const has_self_tag = skills.find(t => t.value === 'self'); 
    if (!has_self_tag) {
      const empty = skills.find(t => t.value === null || t.value === undefined);
      if (empty) empty.value = 'self';
      else self.tags.push(self_tag);
      updated = true;
    }
  }

  const sr = await data.people.resolveSavePerson(user, new Person(self));
  if (sr.remove && sr.remove.id !== self.id && (!sr.save || sr.remove.id !== sr.save.id)) {
    await data.people.delete(user, sr.remove);
  }

  if (sr.save) {
    self = sr.save;
    updated = true;
    self.self = true;
  }

  if (save_vanity) {
    await data.users.saveVanity(user, self);
    await data.users.save(user, false, true, false);
  }

  if (updated) {
    logging.infoFP(LOG_NAME, 'userCheck', user.profile, `Saving user updated ${updated}`);
    await data.people.save(user, self);

    if (do_export)  await doExport(user.profile, { force_internal: true }).catch(err => logging.errorFP(LOG_NAME, 'userCheck', user.profile, 'Error exporting people', err));
    // TODO: bigquery
    /*else {
      try {
        await data.people.bigQueryUpdate(user.profile, self);
      } catch (e) {
        logging.warnFP(LOG_NAME, 'userCheck', user.profile, `No people found in reload ${do_export ? 're-exporting' : 'skipping'}^`);
      }
    }*/
  }

  return self;
}

export async function doRefresh(profile: string, reset_types: EntityType[] = [], force_refresh = false): Promise<boolean> {
  const now: Date = new Date();
  if (!config.configLoaded()) await config.loadConfig();

  logging.infoFP(LOG_NAME, 'doRefresh', profile, `Updating ${force_refresh ? 'force' :''} ${JSON.stringify(reset_types)}`);

  const user = new ForaUser(profile);
  if (!(await data.users.init(user, false, false, null, reset_types))) {
    logging.warnFP(LOG_NAME, 'doRefresh', user.profile, 'Skipping update due to init failure. Update will run after next auth');
    SessionCache.logoutSessions(user).catch(e => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error logging out user after init failure in update thread', e));
    // do an export if this was recent
    if (user.last > MINUTES(now, -30)) {
      await data.people.getUserPerson(user);
      await doExport(profile).catch(err => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error exporting people with no refresh', err));
      return false;
    }
    return true;
  }

  logging.infoFP(LOG_NAME, 'doRefresh', user.profile, `User restored - ${user.provider}`);

  // check for first run
  if (!user.refreshing && !SourceController.firstRunDone(user)) {
    logging.warnFP(LOG_NAME, 'doRefresh', profile, `User hasn't first run, restarting`);
    let self = await userCheck(user);

    let projects = [];
    const project_cache = await data.users.itemInfoCache(user, [EntityType.Project]);
    if (project_cache && project_cache.length && project_cache[0].cache) projects = project_cache[0].cache;
    else projects = await data.projects.load(user);
    await firstRun(profile, self, projects.length > 0, projects.length > 0 ? projects.sort((a,b) => new Date(b.last_update).getTime() - new Date(a.last_update).getTime())[0].id : undefined);

    return;
  } else if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'doRefresh', user.profile, `User provider = ${user.provider}, tokens = ${util.format(user.tokens)}`);

  if (user.isAuthenticated() && ((!user.refreshing && (new Date(user.last_refresh) < HOURS(now, -1) || force_refresh)) || config.isEnvDevelopment())) {
    user.refreshing = true;
    if (new Date(user.last) > new Date(user.last_refresh)) user.refreshed = [];
    await data.users.save(user, true, false, false);

    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'doRefresh', user.profile, `User calling source controller - ${user.provider} `);
    let self = await userCheck(user);
    
    if (!self) {
      logging.warnFP(LOG_NAME, 'doRefresh', profile, `User has no self, not refreshing`);
      return;
    }

    // subscription
    const customer = await lookupCustomer(this.user);
    if(!customer) await createCustomer(this.user);

    const items: IEntity[] = flatten<IEntity>(await Promise.all<IEntity[]>([
      data.analyses.load(user),
      data.asks.load(user),
      data.goals.load(user),
      data.projects.load(user)]));

    await data.users.cacheItemInfo(user, items, false);

    // Run refresh on any plugins that are enabled.
    let refresh_set: EntityType[] = [];
    try { 
      if (force_refresh) await data.plugins.bigQueryPlugin().endOp(profile)
      if (!SourceController.firstRunDone(user)) {
        logging.warnFP(LOG_NAME, 'doRefresh', profile, `User hasn't first run, doing that instead of refresh`);

        user.refreshing = false;
        await data.users.save(user, true, false, false).catch(e => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error saving user after error in refresh', e));

        let projects = [];
        const project_cache = await data.users.itemInfoCache(user, [EntityType.Project]);
        if (project_cache && project_cache.length && project_cache[0].cache) projects = project_cache[0].cache;
        else projects = await data.projects.load(user);
 
        await firstRun(profile, self, projects.length > 0, projects.length > 0 ? projects.sort((a,b) => new Date(b.last_update).getTime() - new Date(a.last_update).getTime())[0].id : undefined);
      }
      else refresh_set = await SourceController.refresh(user, self, reset_types);
      await data.users.save(user, true, true, false).catch(err => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error saving user after refresh', err));
    } catch(err) {
      logging.errorFP(LOG_NAME, 'doRefresh', user.profile, 'Error refreshing', err);
      user.refreshing = false;
      await data.users.save(user, true, false, false).catch(e => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error saving user after error in refresh', e));
    }

    // We need to trigger a `people.bigQueryExport` so the people/people_* records will get created
    if ((refresh_set && refresh_set.includes(EntityType.Person)) || force_refresh) {
      logging.infoFP(LOG_NAME, 'doRefresh', user.profile, 'Finished loading, starting export');
      await userCheck(user);
      await doExport(profile, {refresh_set}).catch(err => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error exporting people', err));
      self = null;
      return false;
    } else {
      logging.infoFP(LOG_NAME, 'doRefresh', user.profile, 'Finished loading, skipping export');
      await SourceController.finishRefresh(user, 'refresh').catch(err => {
        logging.errorFP(LOG_NAME, 'doRefresh', user.profile, 'Error refreshing', err);
        // user.refreshing = false;
        // data.users.save(user, true, false, false).catch(e => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error saving user after error in update', e));
      });

      if (refresh_set)  await data.users.refreshSet(user, refresh_set);
    }
    self = null;
  } else {
    if (!user.isAuthenticated()) logging.warnFP(LOG_NAME, 'update', user.profile, 'Skipping update due to missing tokens. Update will run after next auth');
    else logging.warnFP(LOG_NAME, 'doRefresh', user.profile, `Skipping update due to refresh ${user.refreshing} last ${user.last_refresh}`);
  }

  return true;
}

export function firstRun(profile: Uid, internal_self: Partial<Person>, do_search: boolean, project: Uid) {
  let self: Partial<Person> = internal_self && internal_self.id ? new Person({id: internal_self.id}) : null;
  if (self) self = personRef(self);
  return internalUpdate({
    profile,
    first_run: true,
    self,
    do_search,
    project,
  });
}

export function searchCandidates(profile: Uid, internal_self: Partial<Person>, project: Uid, search_ids: Uid[] = []) {
  let self: Partial<Person> = internal_self && internal_self.id ? new Person({id: internal_self.id}) : null;
  if (self) self = personRef(self);
  return internalUpdate({
    profile,
    do_search: true,
    self,
    project,
    search_ids,
  });
}

export async function updateFunction(ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig();

  let message;
  try { 
    message = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : {};
  } catch(e) {
    logging.errorF(LOG_NAME, 'updateFunction', `Error parsing incoming event ${JSON.stringify(ps_event.data)}`, e);
    return;
  }

  logging.infoFP(LOG_NAME, 'updateFunction', message.profile, `Function called with ${JSON.stringify(message)}`);

  let done = false;

  if (message.profile) {
    if (message.name) {
      // check on export
      if (config.isEnvOffline()) done = true;
      else {
        const source = await data.people.checkExport(message.profile, message.name);
        if (source) {
          await data.people.finishExport(message.profile, source);
          done = true;
        } else {
          // check on export status again
          logging.infoFP(LOG_NAME, 'updateFunction', message.profile, `Checking on export in 60s`);
          await setTimeout(60000);
          await internalUpdate(message); // {profile: message.profile, name: message.name});
        }
      }
    } else if(message.first_run) {
      let user = new ForaUser(message.profile);
      if (!(await data.users.init(user))) {
        logging.warnFP(LOG_NAME, 'firstRun', user.profile, 'Skipping first run due to init failure.');
        SessionCache.logoutSessions(user).catch(e => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error logging out user after init failure in update thread', e));
        user = null;
        return true;
      }

      try {
        // Re-enable any plugins that were disabled due to error (most likely token issues)
        SourceController.enableErrorDisabledPlugins(user);

        // Run firstRun on any plugins that are enabled
        message.refresh_set = await SourceController.firstRun(user, message.self, message.do_search, message.project);

        // save user
        await data.users.save(user, true, true, false).catch(err => logging.errorFP(LOG_NAME, 'firstRun', user.profile, 'Error saving user after firstRun', err));

        await userCheck(user);
        
        // We need to trigger a `exportPeople` so the people/people_* records will get created
        logging.infoFP(LOG_NAME, 'firstRun', user.profile, 'finished loading, starting export');
        done = await doExport(user.profile, message);
        if (config.isEnvOffline()) return true;
      } catch(e) {
        logging.errorFP(LOG_NAME, 'firstRun', user.profile, `Error in first update run for user`, e);
        // will need to do firstRun again
        user.refreshing = false;
        user.data_sources = {};
        await data.users.save(user, true, true, false).catch(err => logging.errorFP(LOG_NAME, 'firstRun', user.profile, 'Error saving user after firstRun error', err));
        const profile = user.profile;
        user = null;
        return firstRun(profile, message.self, message.do_search, message.project);
      }
    } else if(message.export) {
      logging.infoFP(LOG_NAME, 'updateFunction', message.profile, 'Running just export');
      const user = new ForaUser(message.profile);
      if (!(await data.users.init(user, false))) {
        logging.warnFP(LOG_NAME, 'updateFunction', message.profile, 'Skipping export due to init failure.');
      } else {
        await userCheck(user);
        done = await doExport(message.profile, message);
      }
    } else {
      /// regular refresh
      try {
        done = await doRefresh(message.profile, config.isEnvDevelopment() ? message.reset : null, message.force_refresh); //, when);
      } catch(err) {
        logging.errorF(LOG_NAME, 'updateFunction', `Error updating ${message.profile}`, err);
      }
    }

    if (done || (message.do_search && !message.first_run)) {
      const update_type = message.first_run ? 'firstRun' : 
        message.import ? 'importData' : 'refresh';

      let user = new ForaUser(message.profile);
      if (!(await data.users.init(user, false))) {
        logging.warnFP(LOG_NAME, update_type, user.profile, 'Skipping update done run due to init failure.');
        SessionCache.logoutSessions(user).catch(e => logging.errorFP(LOG_NAME, update_type, user.profile, 'Error logging out user after init failure in update thread', e));
        return true;
      }

      if (message.import) {
        if (!user.imports) user.imports = [];
        user.imports.push({id: message.import.id, type: message.import.type, date: new Date()});
      }

      if (done) {
        await SourceController.finishRefresh(user, update_type, message.refresh_set).catch(err => {
          logging.errorFP(LOG_NAME, update_type, user.profile, 'Error refreshing', err);
        });
      }

      const user_exists = await data.users.globalById(user.profile); 
      if (message.first_run || message.do_search) {
        if (message.first_run && !config.isEnvOffline() && user_exists) {
          logging.infoFP(LOG_NAME, update_type, message.profile, 'Processing is done for all plugins');
          // kick off learn for this user
          await internalLearn({profile: user.profile});
        }

        if (message.project) {
          const dialog: Dialog = await Dialog.loadSession('update', { id: `first_run_${message.profile}_${new Date().getTime()}`, profile: message.profile }, {}, {ephemeral: true, create_new: true, check_tokens:true, stack:Error().stack});
          if (dialog) {
            // const cache_project = dialog.cache.projects[message.project];
            // if (cache_project) {
            const project = await dialog.projects.get(message.project);
              // First run is done, circle back with the active project
            if (project) {
              if (message.do_search) {
                const people = await dialog.projects.searchCandidates(project, message.search_ids).catch(e => dialog.asyncError(e));

                if (!project.expert) {
                  const connections = await dialog.people.findIntroductions(project.searched_skills ? project.searched_skills : project.skills.split(' '), 20);
                  const candidate_comms = [
                    ...project.candidates ? flatten(project.candidates.map(c => c.comms)) : [], 
                    ... people ? flatten(people.map(c => c.comms)) : []
                  ];
                  project.suggested = connections.filter(c => !arraysIntersect(candidate_comms, c.comms)).map(c => `profile/${c.vanity}`);
                }

                if ((people && (people.length || message.first_run))) await dialog.projects.saveSearchCandidates(project, people, true, true);
                else if(project.suggested && project.suggested.length) await dialog.projects.update(project);
              } else await dialog.projects.resolveCandidates(project).catch(e => dialog.asyncError(e));
            } else logging.warnFP(LOG_NAME, 'updateFunction', dialog.user.profile, `No project found for ${message.project}`);
            // }
            await dialog.saveSession();
          }
        }
      }

      /*if (message.import) {
        notifyImport(user, message.import.id, message.import.type, message.import.count).catch(err => logging.errorFP(LOG_NAME, 'importData', user.profile, 'Error notifying user', err));
        logging.infoFP(LOG_NAME, 'importData', user.profile, `notified import ${message.import.id} type ${message.import.type} count ${message.import.count}`);
      }*/

      // corner case - handled an account that was deleted while we were loading
      if (!user_exists) {
        logging.warnFP(LOG_NAME, 'firstRun', user.profile, 'User deleted during update');
        await data.users.deleteAccount(user.profile, user.provider);
      } else {
        await data.users.save(user, true, true, false).catch(e => logging.errorFP(LOG_NAME, 'update', user.profile, 'Error saving user after error in update', e));
      }
    }
  } else {
    const now = new Date();
    // const week_ago = DAYS(now, -3);
    // const hour_ago = HOURS(now, -1);
    // const users = await data.users.globals();
    const since = DAYS(now, -5);
    let users = await data.users.globalIds(since);
    if (users && users.length) {
      logging.infoF(LOG_NAME, 'doRefresh', `Updating ${users.length} users`);
      for (const user of users) {
        const profile_mod = (Buffer.from(hash(user.profile)).reduce((a, b) => a + b) % 12);
        const start = new Date(user.start);
        const last = new Date(user.last);
        const yesterday = DAYS(now, -1);
        if (/*(new Date(user.last_refresh) < hour_ago &&*/ 
          (sameDay(last, yesterday) && !sameDay(start, yesterday)) ||
          (sameDay(start, now) && !user.onboarding) ||
          now.getHours() % 12 === profile_mod || 
          config.isEnvDevelopment()) {
        // const profile_mod = config.isRunningOnGoogle() ? (Buffer.from(funcs.hash(user.profile)).reduce((a, b) => a + b) % 600) + 10 : 0;
        // const refresh = funcs.SECONDS(now, profile_mod);
          await internalUpdate({ profile: user.profile });
        } else {
          logging.infoFP(LOG_NAME, 'doRefresh', user.profile, `Skipping last ${user.last} ${now.getHours()} mod ${profile_mod}`);
        }
        // await update(user.profile, new Date()).catch(err => logging.errorF(LOG_NAME, 'updateFunction', `Error updating ${user.profile}`, err));
      }
    } else logging.warnF(LOG_NAME, 'doRefresh', `No users to refresh since ${since}` );
  }
}