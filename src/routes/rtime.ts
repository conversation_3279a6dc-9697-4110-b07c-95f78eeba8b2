// import chrono from 'chrono-node';
import express from 'express';
import session from 'express-session';
import * as funcs from '../utils/funcs';

const router = express.Router();

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  /* 
  let val = decodeURI(req.query.time as string);
  // const locale = req.query.locale ? req.query.locale : 'en-US';
  // const timeZone = req.query.timeZone ? req.query.timeZone : 'Etc/Zulu';
  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;

  const now: Date = new Date();
  const net_offset = now.getTimezoneOffset() - offset;
  const reference = funcs.MINUTES(now, net_offset);
  const dt = chrono.parse(val, reference);
  let index_offset = 0;
  if (dt.length) {
    for (const dtm of dt) {
      let sub = [];
      if (dtm.start.knownValues.year) sub.push(`${dtm.start.knownValues.year}`);
      if (dtm.start.knownValues.month) {
        const prefixed = `0${dtm.start.knownValues.month}`.slice(-2);
        sub.push(`-${prefixed}`);
      }
      if (dtm.start.knownValues.day) {
        const prefixed = `0${dtm.start.knownValues.day}`.slice(-2);
        sub.push(`-${prefixed}`);
      } else if (dtm.start.knownValues.weekday) {
        sub = [
          dtm.start
            .date()
            .toISOString()
            .slice(0, 10),
        ];
             }

      if (sub.length) {
        val = val.slice(0, dtm.index + index_offset) + sub.join('') + val.slice(dtm.index + index_offset + dtm.text.length);
        index_offset += sub.join('').length - dtm.text.length;
      }
    }
  }

  res.status(200).json({ time: val }); */
  res.status(404).end();
});

export default router;
