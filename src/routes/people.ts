import express from 'express';
import 'express-session';
import _ from 'lodash';
import multer from 'multer';

import config from '../config';
import data from '../data';
import lang from '../lang';

import { Message, Person } from '../types/items';
import { EntityType, ProfileStatus, ProjectCandidateState, ServerChatResponse, Uid } from '../types/shared';

import { hash } from '../utils/funcs';
import { promptInfo } from '../utils/info';
import logging from '../utils/logging';
import peopleUtils from '../utils/people';

import procEntity from '../proc/entity';
import procPeople from '../proc/people';
import procTime from '../proc/time';
import Dialog, { Topics } from '../session/dialog';

const LOG_NAME = 'routes.people';
const DEBUG = (require('debug') as any)('fora:routes:people');
// DEBUG.log = logging.log;
const router = express.Router();
const upload = multer(); // for parsing multipart/form-data

const  include_states = [
  ProjectCandidateState.SELECTED,
  ProjectCandidateState.SUBMITTED,
  ProjectCandidateState.PAYMENT_REQUESTED,
];

router.get('/:contact_id?', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog: Dialog = null;
  try {
    dialog = await Dialog.loadSession('people', req.session, req.cookies, {read_only: true, create_new:true, check_tokens:true, stack:Error().stack});

    if (!dialog) {
      logging.warnF(LOG_NAME, 'get', 'Get people with no session created');
      res.json([]);
      return;
    }

    dialog.clearCommand();

    if (req.query.about) {
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, 'Returning about');
      res.json(lang.about.ABOUT_INFO.slice(1).map(p => dialog.getPersonInfo(p))).end();
      await dialog.saveSession();
      dialog = null;
      return;
    }

    if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'get', dialog.user.profile, 'Loading people');

    dialog.user.setOffset(req.query.offset as string);
    dialog.user.setLocale(req.header('accept-language'));
    dialog.user.setTimeZone(req.query.timeZone as string);

    let people: Partial<Person>[] = [];
    let page = parseInt(req.query.page as string, 10);
    let groups: Uid[] = null;

    if (isNaN(page)) page = undefined;
    if (req.params.contact_id) people = await dialog.people.byId(`people/${req.params.contact_id}`);
    else if(req.query.first && lang.bool.AFFIRMATIVES.includes(req.query.first as string)) {
      if (dialog.isAuthenticatedNonGuest()) people = await dialog.people.allPeople(false);
    } else if (req.query.group && req.query.group.length) {
      if (dialog.isAuthenticatedNonGuest()) {
        people = await dialog.people.groupPeople(false, lang.bool.AFFIRMATIVES.includes(req.query.network as string), 
          lang.bool.AFFIRMATIVES.includes(req.query.group as string) ? undefined : [req.query.group as string], page);
        groups = [req.query.group as Uid];
      }
    } else if (req.query.network && lang.bool.AFFIRMATIVES.includes(req.query.network as string)) {
      if (dialog.isAuthenticatedNonGuest()) people = await dialog.people.allPeople(true);
    } else if (req.query.host && lang.bool.AFFIRMATIVES.includes(req.query.host as string)) {
      const group_host = dialog.getGroup();
      people = group_host ? await dialog.people.groupPromote([group_host.id]) : [];
      groups = group_host ? [group_host.id] : null;
    } else if(req.query.ready && lang.bool.AFFIRMATIVES.includes(req.query.ready as string)) {
      const vanities = await data.users.vanitiesByStatus([ProfileStatus.Ready]);
      if (vanities && vanities.length) {
        const vanity_people = await data.users.vanities(vanities);
        if (vanity_people) people = vanity_people.sort().map(v => peopleUtils.personFromVanity(v));
      }
    } else if(req.query.premier && lang.bool.AFFIRMATIVES.includes(req.query.premier as string)) {
      const vanities = await data.users.vanitiesByStatus([ProfileStatus.Premier]);
      if (vanities && vanities.length) {
        const vanity_people = await data.users.vanities(vanities);
        if (vanity_people) people = vanity_people.sort().map(v => peopleUtils.personFromVanity(v));
      }
    } else if(req.query.users && lang.bool.AFFIRMATIVES.includes(req.query.users as string)) {
      people = await dialog.people.findConnectedUserPeople();
    } else {
      if (dialog.me) people.push(dialog.me);

      // show people meeting today then people from tasks, notes, today's emails
      const now = new Date();
      let people_ids = [];
      const message_people: Partial<Person>[] = [];
      const whats_next = dialog.activitySummary(
        [EntityType.Event, EntityType.Task, EntityType.Message],
        dialog.cache.events,
        Object.values(dialog.cache.tasks),
        dialog.cache.messages,
        now,
        null,
        false,
      );

      for (const item of whats_next.next) {
        const message = item as Message;

        switch (item.type) {
          case EntityType.Task:
          case EntityType.Event:
          case EntityType.Note:
            for (const pindex in item['people']) {
              const person = item['people'][pindex];
              if (person.id && !person.self && !people_ids.includes(person.id)) {
                people.push(person);
                people_ids.push(person.id);
              }
            }
            break;
          case EntityType.Message:
            if (message.sender && !message.sender.self && !people_ids.includes(message.sender.id)) people.push(message.sender);
            if (message.recipient) {
              for (const pindex in message.recipient) {
                const person = message.recipient[pindex];
                if (person.id && !person.self && !people_ids.includes(person.id)) {
                  message_people.push(person);
                  people_ids.push(person.id);
                }
              }
            }
            break;
        }
      }

      for (const project of Object.values(dialog.cache.projects)) {
        //if (!project.completed && !project.archived) {
          if (project.client.self) {
            if(project.candidates) {
              const filter_candidates = project.candidates.filter(c => include_states.includes(c.state) && !people_ids.includes(c.id));
              people = people.concat(filter_candidates);
              people_ids = people_ids.concat(filter_candidates.map(p => p.id));
            }
          } else if(!people_ids.includes(project.client.id)) {
            people_ids.push(project.client.id);
            people.push(project.client);
          }
        //}
      }

      people = people.concat(message_people);
      people = people.slice(0,50);
    }

    if (config.isEnvOffline()) {
      // wait for cache to load
      people = people.concat(await data.people.load(dialog.user)); // people.concat(people_ids.map(pi => { return new Person({ id: Object.keys(pi)[0], displayName: Object.values(pi)[0] } ) } ));
    }

    const uniq_people = people ? _.uniqWith(people.filter(p => p.id || p.vanity), (a,b) => {
      if (a.id && a.id === b.id) return true;
      if (a.vanity && a.vanity === b.vanity) return true;
      return false;
    }) : [];

    await dialog.saveSession();
    let info_people;
    if (!isNaN(page)) {
      info_people = uniq_people.slice(1000 * page, 1000 * (page + 1)).map(p => dialog.getPersonInfo(p))
    } else {
      info_people = uniq_people.map(p => dialog.getPersonInfo(p));
    }

    if (groups && groups.length) info_people.forEach(p => p.groups = groups);
    res.json(info_people);
  } catch (err) {
    if (dialog) dialog.safeSaveSession();
    logging.errorFP(LOG_NAME, 'get', dialog && dialog.user ? dialog.user.profile : null, 'People get error', err);
    // res.json({ id: new Date().getTime(), reply: [funcs.promptInfo('I ran into an error')], answers: ['Send feedback'] });
    res.json([]);
  }
});

router.post('/', upload.array(), async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  if (logging.isDebug()) logging.debugF(LOG_NAME, 'post', `People: ${req.session.id}`);
  let dialog: Dialog = null;
  try {
    dialog = await Dialog.loadSession('people', req.session, req.cookies, {offset:req.body.offset, create_new:true, check_tokens:true, stack:Error().stack});
    if (dialog) {
      dialog.clearCommand();
      dialog.message = req.body;

      dialog.user.setOffset(req.query.offset as string);
      dialog.user.setLocale(req.header('accept-language'));
      dialog.user.setTimeZone(req.query.timeZone as string);

      if (dialog.context.error) throw dialog.context.error;

      if (dialog.message.message && dialog.message.message.length) await dialog.cache.people_loaded;

      procTime(dialog);
      await procEntity(dialog);
      await procPeople(dialog);

      const response = {
        id: hash(new Date().getTime().toString()),
        ping: -1,
        reply: undefined,
        info: [],
      } as ServerChatResponse;
      const curr_dialog = dialog.currentDialog();

      if (dialog.topic !== Topics.NOOP) {
        if (curr_dialog !== null && !curr_dialog.sent) {
          if (curr_dialog.prompt.length) {
            response.reply = curr_dialog.prompt;
            curr_dialog.sent = true;
          }
          if (curr_dialog.info.length) {
            response.info = curr_dialog.info;
            curr_dialog.sent = true;
          }
        }
      }

      const command = dialog.getCommand();
      if (command !== null) {
        Object.assign(response, command);
        // logging.infoF(LOG_NAME, 'post', `Command ${JSON.stringify(command)}`);
      }

      // dialog.session.profile = dialog.user.profile;
      // dialog.session.guest = dialog.user.isGuestAccount();
      if (dialog.isMaskedTopic()) await dialog.saveSession();
      else await dialog.safeSaveSession();

      res.json(response).end();
    } else res.json({ id: new Date().getTime(), reply: [promptInfo('Please connect an account to login')], answers: ['Connect'] }).end();
  } catch (err) {
    logging.errorFP(LOG_NAME, 'post', dialog && dialog.user ? dialog.user.profile : null, 'People error', err);
    if (dialog) await dialog.safeSaveSession();
    res.json({ id: new Date().getTime(), reply: [promptInfo('I ran into an error')], answers: ['Send feedback'] }).end();
  }
});

export default router;
