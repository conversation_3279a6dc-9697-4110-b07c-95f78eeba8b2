import express from 'express';
import data from '../data';
import { ProfileStatus } from '../types/shared';
import * as funcs from '../utils/funcs';

const router = express.Router();

const LOG_NAME='routes.sitemap';

router.get('/', async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

  const sitemap_xml = [
    '<?xml version="1.0" encoding="UTF-8"?>',
    '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">',
  ];

  const special = ['about', 'teams', 'learn', 'info', 'webinar', 'apply', 'signup', 'professional', 'pricing', 'skills', 'teams'];
  const content = await data.content.contentSet();
  const vanities = await data.users.vanitiesByStatus([ProfileStatus.Ready, ProfileStatus.Premier]);

  for (const s of special) sitemap_xml.push(`<url><loc>${funcs.mapURL(`/${s}`)}</loc></url>`);

  const curls = content.filter(c => !special.includes(c)).map(c => funcs.mapURL(`/start/${c}`));
  for (const c of curls) sitemap_xml.push(`<url><loc>${c}</loc></url>`);

  const vurls = vanities.map(vanity => funcs.mapURL(`/profile/${vanity}`));
  for (const v of vurls) sitemap_xml.push(`<url><loc>${v}</loc></url>`);

  sitemap_xml.push('</urlset>');

  res.status(200);
  res.type('application/xml');
  res.send(sitemap_xml.join('\n')).end();
});

export default router;