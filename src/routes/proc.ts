import config from '../config';
import data from '../data';

import { MINUTES } from '../utils/datetime';
import logging from '../utils/logging';

import { importFunction } from './imports';
import { learnFunction } from './learn';
import { notifyFunction } from './notify';
import { internalPub } from './pub';
import { reloadFunction } from './reload';
import { syncMailFunction } from './syncMail';
import { transferFunction } from './transfer';
import { updateFunction } from './update';

const LOG_NAME = 'routes.proc';

export async function procFunction(ps_event, context) {
  let event_data;
  try {
    console.log(`Received event ${JSON.stringify(ps_event)} context ${JSON.stringify(context)}`);
    logging.infoF(LOG_NAME, 'procFunction', `Received event ${JSON.stringify(ps_event)} context ${JSON.stringify(context)}`);

    event_data = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : ps_event;
    const event_time = context && context.timestamp ? new Date(context.timestamp): 
      ps_event && ps_event.publish_time ? new Date(ps_event.publish_time) : new Date();
      new Date();
    if (MINUTES(event_time, 15) < new Date()) {
      if (ps_event.retry) {
        console.log(`Allowing old retry event from ${event_time}: ${JSON.stringify(event_data)}`);
        logging.warnF(LOG_NAME, 'procFunction', `Allowing old retry event from ${event_time}: ${JSON.stringify(event_data)} and retrying`);
      } else {
        console.log(`Skipping event from ${event_time}: ${JSON.stringify(event_data)}`);
        logging.warnF(LOG_NAME, 'procFunction', `Skipping event from ${event_time}: ${JSON.stringify(event_data)} and retrying`);

        await internalPub(event_data.action, event_data.payload);
        return;
      }
    }

    try {
      await data.dataCheck();
    } catch(e) {
      logging.warnF(LOG_NAME, 'procFunction', `Data did not init, re-queuing ${event_data.action}`);
      await internalPub(event_data.action, event_data.payload);
    }
   
    return processFunction(event_data);
  } catch(e) {
    console.log(`Error running ${JSON.stringify(event_data)}: ${JSON.stringify(e)}`);
    logging.errorF(LOG_NAME, 'procFunction', `Error running ${JSON.stringify(event_data)}`, e);
  }
}

export async function processFunction(event_data) {
  if (!config.configLoaded()) await config.loadConfig();
  // force data to load
  // console.log(`Received ${JSON.stringify(event_data)}`);
  logging.infoF(LOG_NAME, 'processFunction', `Received ${JSON.stringify(event_data)}`);
  if (event_data) {
    // console.log(`Running ${event_data.action}`);
    logging.infoF(LOG_NAME, 'processFunction', `Running ${event_data.action}`);
    switch(event_data.action) {
      case 'update':
        await updateFunction(event_data.payload ? {data: Buffer.from(JSON.stringify(event_data.payload)).toString('base64')} : null, null);
        break;
      case 'import':
        await importFunction(event_data.payload ? {data: Buffer.from(JSON.stringify(event_data.payload)).toString('base64')} : null, null);
        break;
      case 'learn':
        await learnFunction(event_data.payload ? {data: Buffer.from(JSON.stringify(event_data.payload)).toString('base64')} : null, null);
        break;
      case 'notify':
        await notifyFunction(event_data.payload ? {data: Buffer.from(JSON.stringify(event_data.payload)).toString('base64')} : null, null);
        break;
      case 'reload':
        await reloadFunction(event_data.payload ? {data: Buffer.from(JSON.stringify(event_data.payload)).toString('base64')} : null, null);
        break;
      case 'sync_mail':
        await syncMailFunction(event_data.payload ? {data: Buffer.from(JSON.stringify(event_data.payload)).toString('base64')} : null, null);
        break;
      case 'transfer':
        await transferFunction(event_data.payload ? {data: Buffer.from(JSON.stringify(event_data.payload)).toString('base64')} : null, null);
        break;
      default:
        logging.warnF(LOG_NAME, 'processFunction', `Unknown action ${event_data.action}`);
    }
  } else logging.warnF(LOG_NAME, 'processFunction', 'No data');
}