import express from 'express';
import 'express-session';
import Dialog from '../session/dialog';
import logging from '../utils/logging';
const router = express.Router();

const LOG_NAME = 'routes.logout';

router.get('/', async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  try {
    // logout
    const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
    const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
    const dialog = await Dialog.loadSession('logout', req.session, req.cookies, {offset, create_new:true, read_only:false, check_tokens:true, stack:Error().stack}); // .then(dialog => {
    if (dialog) {
      await dialog.deleteSession(true);
      await dialog.cache.saveCache(dialog.cacheExpires());
    }
    res.redirect('/');
  } catch (err) {
    logging.errorF(LOG_NAME, 'get', 'Logout error', err);
    res.status(500).end();
  }
});

export default router;
