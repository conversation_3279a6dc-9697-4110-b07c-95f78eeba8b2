
import config from '../config';
import data from '../data';

import ForaUser from '../session/user';

import { MailingList } from '../types/globals';
import { Person } from '../types/items';

import { DAYS } from '../utils/datetime';
import logging from '../utils/logging';
import { registerMailingList } from '../utils/mail';

const LOG_NAME = 'routes.syncMail';

export async function syncMailFunction(_ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig();
  // const ps_data = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : null;

  // fetch users
  const since = DAYS(new Date(), -1);
  const gusers = await data.users.globals(since);
  if (!gusers) {
    logging.infoF(LOG_NAME, 'syncMailFunction', `No users to sync since ${since}`);
    return;
  }

  const people = await data.users.vanityPeopleByEmail(gusers.map(g => g.email));
  const people_vanity: {[key:string]: Partial<Person>} = {};

  if (people) {
    for (const person of people) {
      if (person.vanity) people_vanity[person.vanity] = person;
    }
  }

  logging.infoF(LOG_NAME, 'syncMailFunction', `Syncing up to ${gusers.length} users`);
  const reg_ml: MailingList[] = [];
  for (const user of gusers) {
    if (!user.locale) {
      const fora_user = new ForaUser(user.profile);
      await data.users.init(fora_user, false);
      user.locale = fora_user.locale;
      await data.plugins.storagePlugin().userGlobalSave(user);
    }

    const ml_user: MailingList = {
      Email: user.email,
      IsExcludedFromCampaigns: user.context === null || user.context === undefined,
      Name: user.name,
      Properties: {
        context: user.context,
        country: user.locale,
        name: user.name,
        firstname: user.name,
        lastname: '',
        vanity: user.vanity,
        newsletter_sub: 'true',
        start: user.start,
        last: user.last,
      }
    }

    const person = people_vanity[user.vanity]; //]await data.people.getUserPerson(user, user.profile);
    if (person) {
      const parts = person.displayName.split(' ');
      ml_user.Name = person.displayName;
      ml_user.Properties.name = person.displayName;
      ml_user.Properties.firstname = parts[0];
      if (parts.length > 1) ml_user.Properties.lastname = parts[parts.length - 1];
    }

    if (!!ml_user.Email && ml_user.Email.length > 4 && 
      !!ml_user.Name && ml_user.Name.length) reg_ml.push(ml_user);
    else logging.warnF(LOG_NAME, 'syncMailFunction', `Not syncing ${user.profile} ${JSON.stringify(ml_user)}`);
  }

  await registerMailingList(reg_ml);
  logging.infoF(LOG_NAME, 'syncMailFunction', `Finished syncing ${reg_ml.length} users`);
}