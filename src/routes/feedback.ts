import express from 'express';
import 'express-session';
import multer from 'multer';
import Dialog from '../session/dialog';
import logging from '../utils/logging';
import mail from '../utils/mail';

const router = express.Router();
const upload = multer(); // for parsing multipart/form-data

const LOG_NAME = 'routes.feedback';

router.post('/', upload.array(), async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
let dialog: Dialog = null;
  try {
    const feedback = req.body as {context: string, name: string, email: string, message: string};

    if (!req.headers.referer) {
      logging.warnF(LOG_NAME, 'post', `Not sending feedback with no referer ${JSON.stringify(feedback)}`,);
      res.status(204).end();
      return;
    }

    const referer = req.headers.referer as string;

    if (!referer.match(/askfora\.com/) && !referer.match(/askfora\.ngrok\.io/)) {
      logging.warnF(LOG_NAME, 'post', `Not sending feedback with invalid referrer ${referer} ${JSON.stringify(feedback)}`,);
      res.status(204).end();
      return;
    }


    dialog = await Dialog.loadSession('feedback', req.session, req.cookies, {offset:req.body.offset, read_only: false, no_cache: true, create_new:false, check_tokens:true, stack:Error().stack});
    if (dialog && !dialog.isAnonymousAccount()) {
      await mail([{ Name: 'Fora', Email: '<EMAIL>' }], [], feedback.context, `From ${feedback.name} <${feedback.email}>\n${feedback.message}`);
      if (dialog) await dialog.saveSession();
      res.status(204).end();
      return;
    } else {
      logging.warnF(LOG_NAME, 'post', `Not sending anonymous feedback for session ${dialog.session.id} ${JSON.stringify(feedback)}`,);
    }
  } catch(err) {
    logging.errorFP(LOG_NAME, 'post', dialog && dialog.user ? dialog.user.profile : null, 'Feedback error', err);
    if (dialog) await dialog.saveSession();
  }
  res.status(204).end();
});

export default router;