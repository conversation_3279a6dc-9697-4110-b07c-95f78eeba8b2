import express from 'express';
import data from '../data';
import Dialog from '../session/dialog';
const router = express.Router();

router.get('/', async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
  const dialog = await Dialog.loadSession('analytics', req.session, req.cookies, {offset, create_new:false, read_only:true, no_cache: true, check_tokens:false, stack:Error().stack});
  if (dialog && dialog.isAuthenticatedNonGuest()) {
    const cache = await data.users.itemInfoCache(dialog.user);
    await dialog.saveSession();
    res.status(200);
    res.json(cache ? cache.map(c => c.cache.filter(i => !i.deleted).map(i => {
        return {
          type: c.itype,
          id: i.id,
          title: i.title,
          score: i.score,
          expert: i.expert,
          rate: i.rate,
          update_date: new Date(i.last_update),
          template: i.template,
          archvied: i.archived,
          cache: true,
        }
      })).reduce((a,b) => a.concat(b), []) : []);

    return;
  }

  res.sendStatus(500);
});

export default router;

