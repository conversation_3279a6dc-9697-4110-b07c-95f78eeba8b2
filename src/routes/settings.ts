import express from 'express';
import 'express-session';
import _ from 'lodash';
import multer from 'multer';

import config from '../config';

import { AuthLevel, AuthProviders, EntityType, ForaUserSettings } from '../types/shared';

import logging from '../utils/logging';

import { AuthProvider } from '../auth/auth_provider';
import data from '../data/index';
import Dialog from '../session/dialog';

const router = express.Router();
const upload = multer(); // for parsing multipart/form-data

const LOG_NAME = 'routes.settings';

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let settings: ForaUserSettings = {} as ForaUserSettings;

  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
  const group = req.query.group ? true : false;
  const accounts = req.query.accounts ? true : false;

  const dialog = await Dialog.loadSession('settings', req.session, req.cookies, {offset, create_new: true, read_only: true, no_cache: true, check_tokens: true, stack: Error().stack});
  if (dialog) {
    dialog.user.setOffset(req.query.offset as string);
    dialog.user.setLocale(req.header('accept-language'));
    dialog.user.setTimeZone(req.query.timeZone as string);

    if (group) {
      await dialog.user.loadGroupsFromDatastore(true);
      const group_settings = dialog.groupSettings();
      await dialog.saveSession();
      res.status(200);
      res.json(group_settings);
      res.end();
      return;
    } else if(accounts) {
      const account_details:{ [key: string]: any } = {};
      for (const provider in dialog.user.accounts) {
        account_details[provider] = {};
        for (const profile in dialog.user.accounts[provider]) {
          const provider_name = AuthProvider.clientAuthNameSet(provider as AuthProviders);
          const details = dialog.user.accounts[provider][profile];
          account_details[provider][profile] = {
            name: details.name ? details.name : provider_name && provider_name.length ? provider_name[0].name : null,
            default: details.default || (provider == dialog.user.provider && profile == dialog.user.profile)  ? true : false,
            email: details.email ? details.email : dialog.user.email,
            permissions: details.permissions,
            group: details.group,
          }
        }
      }
    
      await dialog.saveSession();
      res.status(200);
      res.json(account_details);
      res.end();
      return;
    } else if(dialog.isAuthenticated() || dialog.isAuthenticated(AuthLevel.Demo)) {
      settings = dialog.userSettings();
      // const saved_settings = await data.users.userSettings(dialog.user);
      // if (saved_settings) Object.assign(settings, saved_settings);
      settings.authenticated = dialog.isAuthenticated() && !dialog.isGuestAccount();
      if(!settings.templates || !settings.templates.length) settings.templates = await dialog.projects.getTemplates();

      // global overrides
      if(config.get("LEARNING")) {
        if (settings.learning) settings.learning.enabled = true;
        else  settings.learning = { disallowed: false, enabled: true }
      }

      settings.widgets = _.uniq([...settings.widgets ? settings.widgets : [], ...config.get('WIDGETS', [])])
    } else {
      settings.authenticated = false;

      if (dialog.user.settings.active === EntityType.Project) {
        settings.projects = dialog.user.settings.projects;
        settings.active = EntityType.Project;
      } else if (dialog.user.settings.active === EntityType.Expert) {
        settings.projects = dialog.user.settings.projects;
        settings.active = EntityType.Expert;
      } else if (dialog.user.settings.active === EntityType.Person) {
        settings.people = dialog.user.settings.people;
        settings.active = EntityType.Person;
      }
    }

    await dialog.saveSession();
  }

  res.status(200);
  res.json(settings);
  res.end();
});

router.post('/', upload.array(), async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  const dialog = await Dialog.loadSession('settings', req.session, req.cookies, {offset:req.body.offset, check_tokens:true, stack:Error().stack});

  const settings = req.body.settings;
  const accounts = req.body.accounts;

  if (dialog) {
    if (dialog.isAuthenticatedNonGuest() && accounts) {
      for (const provider in accounts) {
        for (const profile in accounts[provider]) {
          if (accounts[provider][profile]) {
            if (accounts[provider][profile].name) dialog.user.nameAccount(provider as AuthProviders, profile, accounts[provider][profile].name);
            if (accounts[provider][profile].default) dialog.user.defaultAccount(provider as AuthProviders, profile);
          }
        }
      }
    }

    if (settings) {
      try {
        await dialog.saveSettings(settings).catch(err => {
          logging.errorFP(LOG_NAME, 'post.settings', dialog.user.profile, 'Settings error', err);
          data.users.message(dialog.user, 'I ran into an error saving your settings').catch(err => dialog.asyncError(err));
        });

        const res_settings = dialog.userSettings();
        await dialog.saveSession();
        res.status(200);
        res.json(res_settings);
        res.end();
        return;
      } catch (err) {
        logging.errorFP(LOG_NAME, 'post.settings', dialog.user.profile, 'Uncaught settings error', err);
      }
    } 

    await dialog.saveSession();
  }

  res.status(412).end();
});

export default router;
