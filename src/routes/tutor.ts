import express from 'express';
import 'express-session';
import session from "express-session";
import { v4 as uuid } from 'uuid';

import Dialog from '../session/dialog';
import ForaUser from '../session/user';
import { Group } from '../types/group';

import { AuthProvider } from '../auth/auth_provider';
import GroupAuthValidation from '../rest/auth/group_auth_validation';

import lang from '../lang';
import setupSession from '../session';
import headers from '../utils/headers';

import { Person, Tag, Tutorial } from '../types/items';
import { AuthProviders, Extra, NotificationType, Subscription, TagType, TutorParams, TutorRegister, Uid } from '../types/shared';

import { MONTHS } from '../utils/datetime';
import { hash } from '../utils/funcs';
import { certificateInfo, subscriptionInfo, tutorialInfo } from '../utils/info';
import logging from '../utils/logging';
import mail from '../utils/mail';
import notifyUser, { NotifyType } from '../utils/notify';
import parsers from '../utils/parsers';
import peopleUtils from '../utils/people';

import { cancel, checkSubscription, createCustomer, customerCredit, customerCreditBalance, customerDebit, getCoupons, getPlan, getPlans, getProducts, lookupCustomer, pause, PlanType, subscribe, subscriptionEnabled, subscriptions, subscriptionToken } from '../sources/hyperline_controller';

import config from '../config';
import data from '../data';

const DEBUG = (require('debug') as any)('fora:routes:learn');

const LOG_NAME = 'routes.tutor';

// Base64 encoded 1x1 transparent PNG
const TRANSPARENT_PIXEL_BASE64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
const TRANSPARENT_PIXEL_BUFFER = Buffer.from(TRANSPARENT_PIXEL_BASE64, 'base64');

const PRACTICE_COMPLETE = 'Congratulations, you successfully completed this practice!';

let use_session;

config.onLoad('tutorRun', async (silent: boolean) => {
  if(config.isRunningOnGoogle()) {
    const session_config = setupSession();
    use_session = session(session_config);
  } // else logging.infoF(LOG_NAME, 'onLoad', JSON.stringify(process.env));
});

async function registerUser(dialog: Dialog, group: Group, msg: Partial<TutorParams>, url: URL, res: express.Response) {
  const emails = parsers.findEmail(msg.register);
  const email = emails && emails.length ? emails[0] : undefined;
  if(email) {
    if(msg.code) {
      let tokens;
      if(msg.code.length > 10) {
        logging.infoFP(LOG_NAME, 'registerUser', group?.id, `Received signed tokens`);

        if(group?.signing_secret) {
          logging.infoFP(LOG_NAME, 'registerUser', group.id, `Checking signed tokens`);
          tokens = AuthProvider.email.tokensFromSignedRequest(msg.code, msg.register, group);
        }

        const default_group = config.isRunningOnGoogle() ? 
          config.isEnvDevelopment() ? 'test.askfora.com' : 'tutor.askfora.com' : 'tutor.askfora.ngrok.io'

        if(!tokens) { // try tutor host
          logging.infoF(LOG_NAME, 'registerUser', `Falling back to ${default_group}`);
          const tgroup = await data.groups.byHost(default_group);
          tokens = AuthProvider.email.tokensFromSignedRequest(msg.code, msg.register, tgroup);
        }

        logging.infoFP(LOG_NAME, 'registerUser', group?.id, `Signed tokens for ${email} ${msg.code}: ${tokens}`);
      } else {
        tokens = AuthProvider.email.tokensFromCode(parseInt(msg.code, 10), msg.register, dialog.session.seed, dialog.session.factor, group);
        logging.infoF(LOG_NAME, 'registerUser', `Verified tokens for ${email} ${msg.code}: ${tokens}`);
      }

      if (tokens !== null) {
        if (dialog.session.seed)  delete dialog.session.seed;
        if (dialog.session.factor) delete dialog.session.factor;

        let credit_referral = undefined;

        // lookup account
        const global_users = await data.users.globalByEmail(email);
        const global_user = global_users && global_users.length ? global_users[0] : undefined;
        if (global_user) {
          const auth_id = global_user.auth_ids.find(a => a.endsWith(`_${global_user.profile}`));
          const provider = auth_id ? auth_id.split('_')[0] as AuthProviders : AuthProviders.Email;
          if(msg.referred_by && !global_user.referred_by) {
            global_user.referred_by = msg.referred_by;
            credit_referral = msg.referred_by;
          }
          dialog.user = new ForaUser(global_user.id, provider);
          await data.users.init(dialog.user, true, true, tokens);
          await dialog.setSelf();
          await dialog.initUser(dialog.me, AuthProviders.Email, 'tutor', group);

          const user_info = `${dialog.user.name} <${dialog.user.email}> ${dialog.user.profile} signed in ${dialog.user.groups ? JSON.stringify(dialog.user.groups) : ''}`;
          mail([{
            Email: '<EMAIL>',
            Name: 'Fora',
          }], [], `${dialog.user.name} signd in`, user_info);
  
        } else {
          const profile = `e_${hash(email).toLowerCase()}`;
          logging.warnFP(LOG_NAME, 'registerUser', profile, `Creating new user ${profile} for user`);

          dialog.user = new ForaUser(profile, AuthProviders.Email);
          dialog.user.tokens = tokens;
          const displayName = parsers.emailName(email);
          const names = parsers.findNames(displayName);
          dialog.user.name = names[0];
          dialog.user.email = email;

          await data.users.init(dialog.user, false);
          await data.users.save(dialog.user, true, true, false);
          const guser = await data.users.globalRegister(dialog.user, 'group', msg.referred_by);
          if(guser.referred_by === msg.referred_by) credit_referral = msg.referred_by;
          dialog.user.affiliate = guser.affiliate;

          const self = new Person({comms: [email], displayName, nickName: names[0]});
          await data.users.generateVanity(dialog.user, self, undefined);
          const host = config.get('DEFAULT_HOST');
          const vanity_url = `https://${host}/profile/${dialog.user.vanity}`;
          const save = self.checkSelf(dialog.user.profile, dialog.user.vanity, vanity_url);
          await data.users.saveVanity(dialog.user, self);
    
          await dialog.initUser(self, AuthProviders.Email, 'tutor', group);

          const user_info = `${dialog.user.name} <${dialog.user.email}> ${dialog.user.profile} signed up ${dialog.user.groups ? JSON.stringify(dialog.user.groups) : ''}`;
          mail([{
            Email: '<EMAIL>',
            Name: 'Fora',
          }], [], `${dialog.user.name} signd up`, user_info);
        }

        if(credit_referral) {
          const referral_user = await data.users.globalByAffiliate(credit_referral);
          if(referral_user) await data.tutorials.credit(referral_user, dialog.user, url);
        }

        if(msg.tutorial) await dialog.tutorials.update({id: msg.tutorial, status:{preassessed: true}});

        await sendRegistration(dialog, res);
      } else {
        await dialog.saveSession();
        res.status(401).end();
      }
    } else if(dialog.isAuthenticatedNonGuest()) {
      if(msg.tutorial) {
        await dialog.tutorials.update({id: msg.tutorial, status:{preassessed: true}});
        await sendRegistration(dialog, res);
      }
      else await userLink(dialog, msg, url, res);
    } else {
      await dialog.createGuest(true);

      const email = emails[0];
      await AuthProvider.email.generateCode(dialog, email, dialog.session,
        `https://${url.host}/t`, lang.tutorial.TUTORIAL_SUBJECT, lang.tutorial.TUTORIAL_MESSAGE, NotificationType.Tutorial
      );

      await dialog.saveSession();
      res.json({email: dialog.user.email, guest: !dialog.user.isAuthenticatedNonGuest(), subscription: undefined}).end();
    }
  } else {
    await dialog.saveSession();
    res.status(500).end();
  }
}

async function userLink(dialog: Dialog, msg: Partial<TutorParams>, url: URL, res: express.Response) {
  const emails = parsers.findEmail(msg.register);
  const email = emails && emails.length ? emails[0] : undefined;
  if(email) {
    if(dialog.isAuthenticatedNonGuest() && dialog.user.email === email) {
      if(msg.tutorial) await dialog.tutorials.update({id: msg.tutorial, status:{preassessed: true}});
      const turl = msg.tutorial ? `https://${url.host}/t?t=${msg.tutorial}` : `https://${url.host}/t`;
      const notify = {
        email: { 
          message: lang.tutorial.TUTORIAL_MESSAGE(turl),
          subject: lang.tutorial.TUTORIAL_SUBJECT,
          rcpts: [{Email: dialog.user.email, Name: dialog.user.name}]
        },
        type: NotificationType.Tutorial,
        variables: { url: turl, code: '' }
      }

      await notifyUser(dialog.user, notify, NotifyType.EmailOnly);

      await dialog.saveSession();
      res.status(204).end();
    } else {
      // create a new user to record interest
      logging.warnF(LOG_NAME, 'userLink', `Tracking user interest ${email}`);
      const global_users = await data.users.globalByEmail(email);
      const global_user = global_users && global_users.length ? global_users[0] : undefined;
      if (!global_user) {
        const profile = `e_${hash(email).toLowerCase()}`;
        logging.warnFP(LOG_NAME, 'userLink', profile, `Creating new user ${profile} for user`);

        const user = new ForaUser(profile, AuthProviders.Email);
        const displayName = parsers.emailName(email);
        const names = parsers.findNames(displayName);
        user.name = names[0];
        user.email = email;

        await data.users.init(user, false);
        await data.users.save(user, true, true, false);
        const guser = await data.users.globalRegister(user, 'group', msg.referred_by);
        if(guser.referred_by === msg.referred_by) {
          const referral_user = await data.users.globalByAffiliate(msg.referred_by);
          if(referral_user) await data.tutorials.credit(referral_user, user, url);
        }

        const self = new Person({comms: [email], displayName, nickName: names[0]});
        await data.users.generateVanity(user, self, undefined);
        const host = config.get('DEFAULT_HOST');
        const vanity_url = `https://${host}/profile/${user.vanity}`;
        const save = self.checkSelf(user.profile, user.vanity, vanity_url);
        await data.users.saveVanity(user, self);

        const user_info = `${dialog.user.name} <${dialog.user.email}> ${dialog.user.profile} signed up via link ${dialog.user.groups ? JSON.stringify(dialog.user.groups) : ''}`;
        mail([{
          Email: '<EMAIL>',
          Name: 'Fora',
        }], [], `${dialog.user.name} signd up via link`, user_info);

      }
      await dialog.saveSession();
      res.status(204).end();
    }
  } else {
    await dialog.saveSession();
    res.status(404).end();
  }
}

async function procTutorial(dialog: Dialog, tutorial: Tutorial, res: express.Response, shared?: boolean) {
  logging.infoFP(LOG_NAME, 'tutorial', dialog.user.profile, `Procssing tutorial ${tutorial.id}`);
  try {
    if(!tutorial.free) {
      // check if user is subscribed or has credits
      const tutorial_plans = (await getPlans()).filter(p => p.name.includes(PlanType.Learning));
      const sub = dialog.isAuthenticatedNonGuest() ? await checkSubscription(dialog.user, tutorial_plans) : undefined;
      if(!subscriptionEnabled(sub)) {
        // no subscription, check for credits
        const products = await getProducts();
        const tutorial_product = products.find(x => x.id === config.getPricing('TUTORIAL'));
        const credits = tutorial_product ? await customerCreditBalance(dialog.user, tutorial_product) : undefined;

        let unlocked = tutorial.status && tutorial.status.unlocked ? tutorial.status.unlocked : [];
        const had_unlocked = unlocked.length;
        let usage_retained = 0;

        if(dialog.isAuthenticatedNonGuest()) {
          if(!unlocked?.length && !credits?.current_balance) {
            if(shared) {
              // unlock the first lesson and create credits
              await customerCredit(dialog.user, tutorial_product, 0);
              unlocked.push(0);
            }
          } else if(unlocked.length < tutorial.lesson_set.length) {
            while(credits?.current_balance >= 10 && unlocked.length < tutorial.lesson_set.length) {
              credits.current_balance -= 10;
              usage_retained += 10;
              for(let i = 0; i < tutorial.lesson_set.length; i++) {
                if(!unlocked.includes(i)) {
                  unlocked.push(i);
                  break;
                }
              }
            }

            if(usage_retained) await customerDebit(dialog.user, tutorial_product, usage_retained);
          }
        }

        tutorial.lesson_set.forEach((ls, i) => {
          if(!unlocked.includes(i) && (!tutorial.status?.assessed || !tutorial.status.assessed.includes(i))) {
            if(ls.assessment) delete ls.assessment;
            ls.lessons.forEach(l => {
              if(l.text) delete l.text;
              if(l.info) delete l.info;
            });
          }
        });

        if(had_unlocked !== unlocked.length) {
          if(tutorial.status) tutorial.status.unlocked = unlocked;
          else tutorial.status = {unlocked};
          await dialog.tutorials.update(tutorial);
        }
      }
    }
  } catch(e) {
    logging.errorFP(LOG_NAME, 'procTutorial', dialog.user.profile, `Error processing tutorial ${tutorial.id}`, e);
  }

  await dialog.saveSession();
  res.json(tutorialInfo(tutorial)).end();
}

export async function subscribeUser(dialog: Dialog, msg: Partial<TutorParams>, res: express.Response) {
  const plan = await getPlan(PlanType.Learning, 'months');
 
  let sub = await await checkSubscription(dialog.user, [plan]);
  // if(!sub) {
    const customer = await lookupCustomer(dialog.user);
    if(customer) {
      let sub_msg  = undefined;  

      const promo = msg.code ? await data.tutorials.getPromo(msg.code) : undefined;
      if(promo?.credits) {
        const products = await getProducts();
        const tutorial_product = products.find(x => x.id === config.getPricing('TUTORIAL'));
        await customerCredit(dialog.user, tutorial_product, promo.credits);
        sub_msg = `${dialog.user.name} subscribed with coupon ${msg.code} for ${promo.credits} credits!`;
      } else if(promo?.months) {
        const coupons = await getCoupons(PlanType.Learning);
        sub = await subscribe(customer, plan, new Date(0), coupons && coupons.length ? coupons[0] : undefined, MONTHS(new Date(), promo.months));
        sub_msg = `${dialog.user.name} subscribed with coupon ${msg.code} for ${promo.months} months!`;
      } else if(!msg.code) {
        sub = await subscribe(customer, plan, new Date(0));
        sub_msg = `${dialog.user.name} subscribed!`;
      }

      const user_info = `${dialog.user.name} <${dialog.user.email}> ${dialog.user.profile} subscribed ${msg.code ? msg.code : ''} ${dialog.user.groups ? JSON.stringify(dialog.user.groups) : ''}`;
      if(sub_msg) {
        mail([{
          Email: '<EMAIL>',
          Name: 'Fora',
        }], [], sub_msg, user_info);
      } else {
        mail([{
          Email: '<EMAIL>',
          Name: 'Fora',
        }], [], `${dialog.user.name} subscribe failed! ${msg && msg.code ? 'Code: ' : ''} ${msg && msg.code ? msg.code : ''}`, user_info);
      }
    }
  // }
  await sendRegistration(dialog, res);
}

export async function unsubscribeUser(dialog: Dialog, res: express.Response) {
  const customer = await lookupCustomer(dialog.user);
  if(customer) {
    const plan = await getPlan(PlanType.Learning, 'months');
    const sub = await subscriptions(customer, [plan]);
    await pause(customer, sub && sub.length ? sub[0] : undefined);

    const user_info = `${dialog.user.name} <${dialog.user.email}> ${dialog.user.profile} unsubscribed ${dialog.user.groups ? JSON.stringify(dialog.user.groups) : ''}`;
    mail([{
      Email: '<EMAIL>',
      Name: 'Fora',
    }], [], `${dialog.user.name} unsubscribed!`, user_info);
}
  await sendRegistration(dialog, res);
}

export async function tutorial(dialog: Dialog, group: Group, msg: Partial<TutorParams>, tutorial_id: Uid, url: URL, res: express.Response) {
  const tutorial_plans = (await getPlans()).filter(p => p.name.includes(PlanType.Learning));
  const sub = dialog.isAuthenticatedNonGuest() ? await checkSubscription(dialog.user, tutorial_plans) : undefined;
  if (msg.message) {
    if(!subscriptionEnabled(sub)) {
      res.json({reply: lang.tutorial.CHAT_NOT_SUBSCRIBED}).end();
      await dialog.saveSession();
      return;
    }

    const reply = msg.practice ? !config.isRunningOnGoogle() && msg.done ? PRACTICE_COMPLETE :
      await dialog.tutorials.practice(tutorial_id, msg.message) : 
      await dialog.tutorials.chat(tutorial_id, msg.message);

    if(reply?.includes(PRACTICE_COMPLETE)) await dialog.tutorials.complete(tutorial_id);

    await dialog.saveSession();
    if (reply) res.json({reply}).end();
    else res.status(404).end();
  } else if(msg.practice) {
    if(!subscriptionEnabled(sub)) {
      res.json({reply: lang.tutorial.CHAT_NOT_SUBSCRIBED}).end();
      await dialog.saveSession();
      return;
    }

    const reply = await dialog.tutorials.practice(tutorial_id);

    await dialog.saveSession();
    if (reply) res.json({reply}).end();
    else res.status(404).end();
  } else if(msg.register) {
    if(msg.interest) await userLink(dialog, msg, url, res);
    else await registerUser(dialog, group, msg, url, res);
  } else if(msg.status) {
    const tutorial = await dialog.tutorials.update({id: tutorial_id, status: msg.status});

    if(tutorial) await procTutorial(dialog, tutorial, res, msg.shared);
    else {
      await dialog.saveSession();
      res.status(404).end();
    }
  } else {
    logging.infoFP(LOG_NAME, 'tutorial', dialog.user.profile, `Fetching tutorial ${tutorial_id}`);
    const tutorial = await dialog.tutorials.get(tutorial_id);
    if(tutorial) await procTutorial(dialog, tutorial, res, msg.shared);
    else {
      await dialog.saveSession();
      logging.warnF(LOG_NAME, 'tutorRun', `Tutoral not found ${JSON.stringify(msg)}`);
      res.status(404).end();
    }
  }
}

export async function catalog(dialog: Dialog, group: Group, res: express.Response) {
  let tutorial_info = [];
  try {
    const private_tutorials = group?.tutorials?.private;
    const host = group?.host?.split('.')[0];
    logging.infoFP(LOG_NAME, 'catalog', dialog.user.profile, `Loading all ${private_tutorials ? host : ''} ${!group?.tutorials?.public ? 'host_only' : ''}`);
    const tutorials = await dialog.tutorials.load(true, private_tutorials ? host : undefined, !group?.tutorials?.public);
    tutorial_info = tutorials.map(tutorialInfo);
    // don't include  lessons
    tutorial_info.forEach(ti => delete ti.lesson_set);
  } catch(e) {
    logging.errorFP(LOG_NAME, 'catalog', dialog.user.profile, `Error getting catalog`, e);
  }
  await dialog.saveSession();
  res.json(tutorial_info).end();
}

export async function skills(dialog: Dialog, msg: Partial<TutorParams>, res: express.Response) {
  const me = new Person(dialog.me);

  const non_skills = me.tags.filter(t => t.type !== TagType.skill);
  me.tags = [...non_skills, ...msg.skills.map(({value, weight}) => new Tag(TagType.skill, value, weight, new Date(), weight))];
  if(dialog.isAuthenticatedNonGuest()) {
    await dialog.people.save(me);
    await data.users.saveVanity(dialog.user, me);
  } else dialog.me = me;
  await sendRegistration(dialog, res);
}

export async function logout(dialog: Dialog, res: express.Response) {
  await dialog.deleteSession(false).catch(e => dialog.asyncError(e));
  await dialog.saveSession();
  res.status(204).end();
}

export async function deleteAccount(dialog: Dialog, res: express.Response) {
  if(dialog.isAuthenticatedNonGuest()) {
    const customer = await lookupCustomer(dialog.user);
    if(customer) await cancel(customer);

    const provider = dialog.user.provider;
    const account = dialog.user.account;
    data.users.deleteAccount(dialog.user.profile, provider, account).catch(err => dialog.asyncError(err));

    const user_info = `${dialog.user.name} <${dialog.user.email}> ${dialog.user.profile} deleted account ${provider ? provider : ''}.${account ? account : ''} ${dialog.user.groups ? JSON.stringify(dialog.user.groups) : ''}`;
    mail([{
      Email: '<EMAIL>',
      Name: 'Fora',
    }], [], `${dialog.user.name} deleted ${account ? 'an' : 'their'} account!`, user_info);
  }

  return logout(dialog, res);
}

export async function saveAssessment(dialog: Dialog, tutorial_id: Uid, lesson: number, assessment: { role: string; parts: { text: string; }[] }[], res: express.Response) {
  let tutorial;
  if(dialog.isAuthenticatedNonGuest()) tutorial = await dialog.tutorials.assess(tutorial_id, lesson, assessment);

  if(tutorial) await procTutorial(dialog, tutorial, res);
  else {
    await dialog.saveSession();
    res.status(404).end();
  }
}

async function sendRegistration(dialog: Dialog, res: express.Response) {
  let subscription: Subscription;
  await dialog.createGuest(true);
  if(dialog.user.isAuthenticatedNonGuest()) {
    let customer = await lookupCustomer(dialog.user);
    if (customer) {
      const tutorial_plans = (await getPlans()).filter(p => p.name.includes(PlanType.Learning));
      const sub_info = await checkSubscription(dialog.user, tutorial_plans);
      subscription = subscriptionInfo(customer, sub_info);
      subscription.token = await subscriptionToken(customer.id);
    } else customer = await createCustomer(dialog.user);
  }
  const register: TutorRegister = {
    email: dialog.user.email, 
    guest: !dialog.user.isAuthenticatedNonGuest(), 
    subscription, 
    affiliate: dialog.user.affiliate,
    user_id: hash(dialog.user.profile),
    me: dialog.getPersonInfo(),
  }
  // TODO: temp initials
  register.me.image = register.me.name ? `https://askfora.com/initials/${parsers.findInitials(register.me.name)}.png` : `https://askfora.com/initials/default.png`;
  await dialog.saveSession();
  res.json(register).end();
}


export async function certificate(dialog: Dialog, cert_id: Uid, res: express.Response) {
  const cert = await data.tutorials.getCertificate(cert_id);
  if(cert) {
    const cert_info = certificateInfo(cert.person, cert.certificate);
    await dialog.saveSession();
    res.json(cert_info).end();
    return;
  }
  
  await dialog.saveSession();
  res.status(404).end();
}

export async function getProfile(dialog: Dialog, vanity: string, res: express.Response) {
  let info;

  if(vanity) {
    const profile = await data.users.vanity(vanity);
    if(profile) {
      // return person with certs
      const person = peopleUtils.personFromVanity(profile);
      info = dialog.getPersonInfo(person);

      const certificates = await data.tutorials.getCertificatesByUser(profile.profile);
      info.certificates = certificates.map(c => certificateInfo(person, c));
    } else {
      await dialog.saveSession();
      res.status(404).end();
    }
  } else info = dialog.getPersonInfo();

  // TODO: temp initials
  info.image = info.name ? `https://askfora.com/initials/${parsers.findInitials(info.name)}.png` :`https://askfora.com/initials/default.png`;

  await dialog.saveSession();
  res.json(info).end();
}

export async function saveProfile(dialog: Dialog, name: string, links: Extra[], res: express.Response) {
  if(dialog.isAuthenticatedNonGuest()) {
    const me = new Person(dialog.me);
    me.displayName = name;
    me.urls = links.map(l => l.link);
    await dialog.people.save(me);
    const info = dialog.getPersonInfo(); 
    // TODO: temp initials
    info.image = info.name ? `https://askfora.com/initials/${parsers.findInitials(info.name)}.png` :`https://askfora.com/initials/default.png`;
    await dialog.saveSession();
    res.json(info).end();
    return;
  }

  await dialog.saveSession();
  res.status(404).end();
}

export async function tutorRun(req: express.Request, res: express.Response) {
  logging.infoF(LOG_NAME, 'tutorRun', `Received ${req.originalUrl} ${req.path} ${JSON.stringify(req.body)}`);
  if (!config.configLoaded()) await config.loadConfig();

  const referrer = req.get('Referrer');
  const url = referrer ? new URL(referrer) : new URL("https://askfora.com")

  if(url.host.endsWith('askfora.com') || url.host === 'askfora.ngrok.io' || url.host === 'localhost') {
    const send_headers = headers(url.host, 'GET,PUT,POST,DELETE', config.isRunningOnGoogle());
    for (const h of send_headers) res.header(h[0], h[1]);
  }

  if(use_session) {
    logging.infoF(LOG_NAME, 'tutorRun', `Setting up session`);
    await new Promise(c => use_session(req, res, (q,s, n) => c([q,s,n])));
  } //else logging.infoF(LOG_NAME, 'tutorRun', JSON.stringify(process.env));

  if(!req.session) {
    logging.warnF(LOG_NAME, 'tutorRun', `Error loading sessions`);
    res.status(501).end();
    return;
  }

  if(url.host) req.session['hostname'] = url.host;

  await data.dataCheck();

  await new GroupAuthValidation().route(req, res);
  const group = req['group'] ? new Group(req['group']) : undefined;

  const msg = req.body as Partial<TutorParams>;

  const offset = msg.offset && !isNaN(msg.offset) ? msg.offset : 0;
  const locale = msg.locale ? msg.locale : req.header('accept-language');
  const timeZone = msg.timeZone;

  const dialog = await Dialog.loadSession(req.originalUrl, req.session, req.cookies, {offset, locale, timeZone, create_new:true, check_tokens:false, ephemeral: true, stack:Error().stack});

  const path_url = new URL(`https://${req.hostname}${req.originalUrl ? req.originalUrl : req.path}`)
  const paths = path_url.pathname.split('/');
  try {
    if(paths.length > 1) {
      switch(paths[2]) {
        case 'warmup': // Add this case
          res.setHeader('Content-Type', 'image/png');
          res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
          res.setHeader('Pragma', 'no-cache');
          res.setHeader('Expires', '0');
          res.send(TRANSPARENT_PIXEL_BUFFER).end();
          return;
        case 'subscribe': return subscribeUser(dialog, msg,res);
        case 'unsubscribe': return unsubscribeUser(dialog, res);
        case 'tutorial': return tutorial(dialog, group, msg, paths.length > 3 ? paths[3] : msg.tutorial, url, res);
        case 'assessment': return saveAssessment(dialog, paths.length > 3 ? paths[3] : msg.tutorial, msg.lesson, msg.assessment, res);
        case 'register':
          if(msg.interest) return userLink(dialog, msg, url, res);
          else return registerUser(dialog, group, msg, url, res);
        case 'catalog': return catalog(dialog, group, res);
        case 'skills': return skills(dialog, msg, res);
        case 'logout': return logout(dialog, res);
        case 'delete': return deleteAccount(dialog, res);
        case 'cert': return certificate(dialog, paths.length > 3 ? paths[3] : undefined, res);
        case 'profile': 
          if(msg.name) return saveProfile(dialog, msg.name, msg.links, res);
          return getProfile(dialog, paths.length > 3 ? paths[3] : undefined, res);
        default: 
        if(paths[1] == 't') return tutorial(dialog, group, msg, paths.length > 3 ? paths[3] : msg.tutorial, url, res);
      }
    }

    if(msg.subscribe) await subscribeUser(dialog, msg, res);
    else if(msg.unsubscribe) await unsubscribeUser(dialog, res);
    else if(msg.skills) await skills(dialog, msg, res);
    else if(msg.tutorial) await tutorial(dialog, group, msg, msg.tutorial, url, res);
    else if(msg.register) {
      if(msg.interest) await  userLink(dialog, msg, url, res);
      else await registerUser(dialog, group, msg, url, res);
    } else if(msg.catalog) await catalog(dialog, group, res);
    else if(msg.logout) await logout(dialog, res);
    else if(msg.message) {
      let label = `I can't help with that yet`;
      try {
        label = await dialog.chatAbout(msg.message);
      } catch(e) {
        logging.warnFP(LOG_NAME, 'tutorRun', dialog.user.profile, `Error asking about ${msg.message}`, e);
      }

      await dialog.saveSession();
      res.json({
        id: uuid(),
        ping: -1,
        reply: [{ label }],
        info: [],
        answers: dialog.isGuestAccount() || dialog.isAnonymousAccount() ? lang.tutorial.WELCOME_OPTIONS : undefined,
        // hide: 10000,
      }).end();
    } else await sendRegistration(dialog, res);
  } catch(e) {
    logging.errorFP(LOG_NAME, 'tutorRun', dialog.user.profile, `Error running ${paths}`, e);
    await dialog.saveSession();
    res.status(500).end();
  }
}

