import express from 'express';
import path from 'path';

import config from '../config';

const router = express.Router();

/* GET home page. */
router.get('/:page?/?:x?/?:y?/?', (req: express.Request, res: express.Response, next: any) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  const index = config.isRunningOnGoogle() ?  path.resolve(__dirname, '..', 'public', 'index.html') :
    path.resolve(__dirname, '..', '..', 'lib', 'public', 'index.html');
  res.type('html');
  res.status(200).sendFile(index);
});

export default router;
