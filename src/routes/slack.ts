import { App, ExpressReceiver, LogLevel } from '@slack/bolt';
import express from 'express';
import _ from 'lodash';
import { setTimeout } from "timers/promises";

import { AuthProvider } from '../auth/auth_provider';
import config from '../config';
import data from '../data';

import Dialog, { /*INTERNAL_PING, MIN_BG_PING,*/ Topics } from '../session/dialog';
import { Message as GlobalMessage } from '../types/globals';
import { GlobalType, Message, Person } from '../types/items';
import { AuthProviders, EntityType, PersonInfo, ProjectCandidateState, ServerChatResponse, TaskInfo } from '../types/shared';

import { sameDay } from '../utils/datetime';
import { localeDowMonthDay } from '../utils/format';
import { flatten, hash, mapURL } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import procBool from '../proc/bool';
import procEntity from '../proc/entity';
import procEvents from '../proc/events';
import procLocations from '../proc/locations';
import procNumber from '../proc/number';
import procPlaces from '../proc/places';
import procQuery from '../proc/query_v1';
import procSkills from '../proc/skills';
import procTime from '../proc/time';
import procTypes from '../proc/types';
import procUrls from '../proc/urls';

const router = express.Router();
const LOG_NAME = 'routes.slack';

let slackRouter: SlackRouter;

const  include_states = [
  ProjectCandidateState.SELECTED,
  ProjectCandidateState.SUBMITTED,
  ProjectCandidateState.PAYMENT_REQUESTED,
];

const DIVIDER = { "type": "divider" };

config.onLoad('slack', async (silent: boolean) => {
  const auth = AuthProvider.getDefaultProviderSettings(AuthProviders.Slack);
  const receiver = new ExpressReceiver({ 
    signingSecret: config.get('SLACK_SIGNING_SECRET'),
    clientId: auth.oauth.client_id,
    clientSecret: auth.oauth.client_secret, 
  });

  slackRouter = new SlackRouter(receiver);
});

/* Slack Account data:
  "access_token": "*****************************************************",
  "token_type": "bot",
  "scope": "commands,incoming-webhook",
  "bot_user_id": "U0KRQLJ9H",
  "app_id": "A0KRD7HC3",
  "team": {
    "name": "Slack Softball Team",
    "id": "T9TK3CUKW"
  },
  "enterprise": {
    "name": "slack-sports",
    "id": "E12345678"
  },
*/

class SlackRouter {
  private receiver: ExpressReceiver;
  private app: App;

  constructor(receiver: ExpressReceiver) {
    this.authorize = this.authorize.bind(this);

    this.receiver = receiver;
    this.app = new App({
      receiver, 
      logLevel: config.isDebug() ? LogLevel.DEBUG : LogLevel.INFO, 
      authorize: this.authorize,
    });

    this.app.event('app_home_opened', this.appHomeOpened.bind(this)); // 	User clicked into your App Home
    this.app.event('app_mention', this.appMention.bind(this)); // Message mentioned us
    this.app.event('app_uninstalled', this.appUninstalled.bind(this)); // App was uninstalled
    this.app.event('email_domain_changed', this.emailDomainChanged.bind(this)); // Workspace has new email domain
    this.app.event('link_shared', this.linkShared.bind(this)); // AskFora link shared
    // this.app.event('message', this.message.bind(this)); // Message in a channel
    this.app.message(this.message.bind(this)); // Message in a channel
    this.app.event('team_join', this.teamJoin.bind(this)); // New member
    this.app.event('tokens_revoked', this.tokensRevoked.bind(this)); // App token revoked
    this.app.event('user_change', this.userChange.bind(this)); // User data changed

    this.app.shortcut({callback_id: 'find'}, this.find.bind(this));
    this.app.shortcut({callback_id: 'job'}, this.job.bind(this));
    this.app.shortcut({callback_id: 'proposal'}, this.proposal.bind(this));
    this.app.shortcut({callback_id: 'note'}, this.note.bind(this));

    this.app.command('/find', this.find.bind(this));
    this.app.command('/job', this.job.bind(this));
    this.app.command('/note', this.note.bind(this));
    this.app.command('/proposal', this.proposal.bind(this));

    this.app.action({type: 'block_actions'}, this.action.bind(this));
  }

  async authorize({ teamId, enterpriseId }) {
    const enterprise_group = await data.groups.byAccount(AuthProviders.Slack, enterpriseId);
    const team_group = await data.groups.byAccount(AuthProviders.Slack, teamId);

    const group = enterprise_group ? enterprise_group : team_group;
    if (group && group.accounts && group.accounts[AuthProviders.Slack]) {
      const account = enterpriseId && group.accounts[AuthProviders.Slack][enterpriseId] ? group.accounts[AuthProviders.Slack][enterpriseId] : group.accounts[AuthProviders.Slack][teamId];
      if (account) {
        return {
          botToken: account.access_token,
          botId: account.bot_id,
          botUserId: account.bot_user_id,
        }
      }
    }
  }

  async route(req: express.Request, res: express.Response) {
    return this.receiver.requestHandler(req as any, res as any);
  }

  //////////////// Response handlers /////////////////////////

  async defaultEventHandler(event) {
    logging.infoF(LOG_NAME, 'defaultEventHandler', JSON.stringify(event));
  }

  async appHomeOpened({body, payload, event, say, context, client, logger}) {
    logging.infoF(LOG_NAME, 'appHomeOpened', JSON.stringify(event));
    const dialog = await this.slackAuth(client.enterpriseId ? client.enterpriseId: client.teamId, event.user);
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      // this.sendMessage('I heard that', event.channel, event.client_msg_id);
      const now = new Date();
      const me = dialog.getPersonInfo();
      const title = { 
        type: 'section', 
        text: { type: "mrkdwn", text: `*${localeDowMonthDay(now, dialog.user.locale, dialog.user.timeZone)}*` },
        accessory: {
          type: "button",
          text: { type: "plain_text", text: "Go To AskFora" },
          value: "settings"
				},
			};

      const today = me.description ? me.description.filter(d => sameDay(d.date, now, dialog.user.offset)).slice(0,50) : [];

      // show people meeting today then people from tasks, notes, today's emails
      let people: Partial<Person>[] = [];
      let people_ids = [];
      const message_people: Partial<Person>[] = [];
      const whats_next = dialog.activitySummary(
        [EntityType.Event, EntityType.Task, EntityType.Message],
        dialog.cache.events,
        Object.values(dialog.cache.tasks),
        dialog.cache.messages,
        now,
        null,
        false,
      );

      for (const item of whats_next.next) {
        const message = item as Message;

        switch (item.type) {
          case EntityType.Task:
          case EntityType.Event:
          case EntityType.Note:
            for (const pindex in item['people']) {
              const person = item['people'][pindex];
              if (person.id && !person.self && !people_ids.includes(person.id)) {
                people.push(person);
                people_ids.push(person.id);
              }
            }
            break;
          case EntityType.Message:
            if (message.sender && !message.sender.self && !people_ids.includes(message.sender.id)) people.push(message.sender);
            if (message.recipient) {
              for (const pindex in message.recipient) {
                const person = message.recipient[pindex];
                if (person.id && !person.self && !people_ids.includes(person.id)) {
                  message_people.push(person);
                  people_ids.push(person.id);
                }
              }
            }
            break;
        }
      }

      for (const project of Object.values(dialog.cache.projects)) {
        //if (!project.completed && !project.archived) {
          if (project.client.self) {
            if(project.candidates) {
              const filter_candidates = project.candidates.filter(c => include_states.includes(c.state) && !people_ids.includes(c.id));
              people = people.concat(filter_candidates);
              people_ids = people_ids.concat(filter_candidates.map(p => p.id));
            }
          } else if(!people_ids.includes(project.client.id)) {
            people_ids.push(project.client.id);
            people.push(project.client);
          }
        //}
      }

      people = people.concat(message_people);
      people = people.slice(0,50);

      const uniq_people = people ? _.uniqWith(people.filter(p => p.id || p.vanity), (a,b) => {
        if (a.id && a.id === b.id) return true;
        if (a.vanity && a.vanity === b.vanity) return true;
        return false;
      }) : [];

      const people_info = uniq_people.map(p => dialog.getPersonInfo(p)).filter(p => !p.self);

      const home = {
        context: context.botToken,
        user_id: event.user,
        view: {
          type: 'home',
          blocks: 
            [
              title, 
              ...( today.length ? flatten(today.map(d => this.descToBlock(dialog, d))) 
                : [{ type: 'section', text: { type: 'plain_text', text: 'Nothing going on today' }}]),
              DIVIDER,
              { type: 'header', text: { type: 'plain_text', text: 'Recent Contacts' }},
              ...flatten(people_info.map(p => [DIVIDER, this.infoToBlock(dialog, p)]))
            ]
        }
      }
      logging.infoFP(LOG_NAME, 'appHomeOpened', dialog.user.profile, JSON.stringify(home, null, 2));
      await client.views.publish(home);
    } else {
      const user_info = await client.users.info({user:event.user});
      await say(await this.loginMessage(dialog, user_info));
    }

    if (dialog) {
      dialog.reset();
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));
    }
  }

  async appMention({body, payload, event, say, context, client, logger}) {
    // {"client_msg_id":"c55ebc62-3c49-48ab-87c8-f28cc9339126",
    //  "type":"app_mention",
    //  "text":"<@U01FPLP5XJ6> nope",
    //  "user":"UA45A2M9A",
    //  "ts":"1606613734.004800",
    //  "team":"T9A6NNFDW",
    //  "blocks":[
    //    {"type":"rich_text",
    //     "block_id":"gm6L+",
    //     "elements":[
    //       {"type":"rich_text_section",
    //        "elements":[
    //          {"type":"user", "user_id":"U01FPLP5XJ6"},
    //          {"type":"text","text":" nope"}]
    //       }]
    //    }],
    //  "channel":"C01FQEQANJW",
    //  "event_ts":"1606613734.004800"}
    logging.infoF(LOG_NAME, 'appMention', JSON.stringify(event));
    const dialog = await this.slackAuth(event.team, event.user);
    if (dialog && dialog.isAuthenticatedNonGuest()) {
      await this.processMessage(dialog, event.blocks[0].elements[0].elements.filter(e => e.text).map(e => e.text).join(' ').trim(), say)
      // await say('I heard that');
    }
    else {
      const user_info = await client.users.info({user:event.user});
      await say(await this.loginMessage(dialog, user_info));
    }
    if (dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  }

  async appUninstalled(event) {
    logging.infoF(LOG_NAME, 'appUninstalled', JSON.stringify(event));
    // lookup group from event.team_id
    // remove tokens from all userso
    const group = await data.groups.byAccount(AuthProviders.Slack, event.team_id);
    const users = [];
    for (const user of users) {
      const tokens = user.getTokens(AuthProviders.Slack);
      if (tokens) {
        await AuthProvider.revokeToken(AuthProviders.Slack, user.profile, tokens, AuthProvider.getDefaultProviderSettings(AuthProviders.Slack));
        user.deleteAccount(AuthProviders.Slack);
        user.removeAccountSetting(AuthProviders.Slack);
        await data.users.globalRegister(user, 'slack');
      }
    }

    delete group.accounts[AuthProviders.Slack];
    group.auth_ids = group.auth_ids.filter(a => a !== `${AuthProviders.Slack}_${event.teamd_id}`);

    logging.infoF(LOG_NAME, 'appUninstall', JSON.stringify(event));
  }

  async emailDomainChanged(event) {
    logging.infoF(LOG_NAME, 'emailDomainChanged', JSON.stringify(event));
  }

  async linkShared(event) {
    logging.infoF(LOG_NAME, 'linkShared', JSON.stringify(event));
  }

  async message({body, payload, event, say, context, client, logger}) {
    // {"client_msg_id":"61d28448-4bf8-49d6-a4c6-ff9c9dd25a9b",
    //  "type":"message",
    //  "text":"yo",
    //  "user":"UA45A2M9A",
    //  "ts":"**********.003100",
    //  "team":"T9A6NNFDW",
    //  "blocks":[
    //    {"type":"rich_text",
    //     "block_id":"GkhyQ",
    //     "elements":[
    //        {"type":"rich_text_section",
    //         "elements":[
    //           {"type":"text","text":"yo"}]
    //        }]
    //    }],
    //  "channel":"D01FPMSRJRG",
    //  "event_ts":"**********.003100",
    //  "channel_type":"im"}
    logging.infoF(LOG_NAME, 'message', JSON.stringify(event));

    if (event.subtype === 'message_changed') {
      if (event.message.subtype === 'bot_message') {
        if (logging.isDebug()) logging.debugF(LOG_NAME, 'message', `Ignoring message changed from self by subtype`);
        return;
      } else event = event.message;
    }

    if (event.subtype === 'bot_message') {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'message', `Ignoring message from self by subtype`);
      return;
    }

    if (event.bot_id) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'message', `Ignoring message from self by subtype`);
      return;
    }

    const dialog = await this.slackAuth(event.team, event.user);

    /*if (event.bot_id === this.bot_id) {
      if (logging.isDebug(dialog ? dialog.user.profile : null))logging.debugFP(LOG_NAME, 'message', dialog ? dialog.user.profile : null, `Ignoring message from self`);
    } else*/ if(dialog) {
      if (dialog.isAuthenticatedNonGuest()) {
        await this.processMessage(dialog, event.blocks[0].elements[0].elements.filter(e => e.text).map(e => e.text).join(' ').trim(), say);
      } else {
        const user_info = await client.users.info({user:event.user});
        await say(await this.loginMessage(dialog, user_info));
      }
    }
    if (dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  }

  async teamJoin(event) {
    logging.infoF(LOG_NAME, 'teamJoin', JSON.stringify(event));
  }

  async tokensRevoked(event) {
    logging.infoF(LOG_NAME, 'tokensRevoked', JSON.stringify(event));
    // lookup group from event.team_id
    // remove tokens from all userso
    const group = await data.groups.byAccount(AuthProviders.Slack, event.team_id);
    const users = [];
    for (const user of users) {
      const tokens = user.getTokens(AuthProviders.Slack);
      if (tokens) {
        await AuthProvider.revokeToken(AuthProviders.Slack, user.profile, tokens, AuthProvider.getDefaultProviderSettings(AuthProviders.Slack));
        user.deleteAccount(AuthProviders.Slack);
        user.removeAccountSetting(AuthProviders.Slack);
        await data.users.globalRegister(user, 'slack');
      }
    }
  }

  async userChange(event) {
    logging.infoF(LOG_NAME, 'userChange', JSON.stringify(event));
  }

  async action({ack, body, payload, event, say, context, client, logger}) {
    logging.infoF(LOG_NAME, 'action', JSON.stringify(payload));
    await ack();
    if (payload.message) await say(payload.message);
    else await say({text: 'Loading...'});
  }

  async find({ack, body, payload, event, say, context, client, logger}) {
    // {"type":"shortcut",
    //  "token":"wcVjjQD667Jsb6OXRybR2qx0",
    //  "action_ts":"**********.851435",
    //  "team":{"id":"T9A6NNFDW","domain":"askfora"},
    //  or "team_id": "T9A6NNFDW",
    //  "user":{"id":"UA45A2M9A","username":"otrajman","team_id":"T9A6NNFDW"},
    //  or "user_id": "UA45A2M9A",
    //  "callback_id":"find",
    //  "trigger_id":"*************.************.7a6642815ea24a84464982795ec3ccb4"
    //  "text":"foo"} // if from slash command

    logging.infoF(LOG_NAME, 'find', JSON.stringify(payload));
    await ack();
    const dialog = await this.slackAuth(payload.team ? payload.team.id : payload.team_id, payload.user ? payload.user.id : payload.user_id);
    let timeout = this.timer(dialog, 'find', payload, say);

    if (dialog && dialog.isAuthenticatedNonGuest()) {
      let msg;
      if (payload.text) msg = `find ${payload.text}`; // `I'll find ${payload.text} for you`;
      else msg = 'find'; // 'Who are what are you looking for?'
      await this.processMessage(dialog, msg, say);
    } else {
      const user_info = await client.users.info({user:event.user});
      const msg = await this.loginMessage(dialog, user_info);
      await this.reply(dialog, 'find', msg, timeout, say);
    }

    if (dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  }

  async job({ack, body, payload, event, say, context, client, logger}) {
    // {"type":"shortcut",
    //  "token":"wcVjjQD667Jsb6OXRybR2qx0",
    //  "action_ts":"**********.851435",
    //  "team":{"id":"T9A6NNFDW","domain":"askfora"},
    //  or "team_id": "T9A6NNFDW",
    //  "user":{"id":"UA45A2M9A","username":"otrajman","team_id":"T9A6NNFDW"},
    //  or "user_id": "UA45A2M9A",
    //  "callback_id":"job",
    //  "trigger_id":"*************.************.7a6642815ea24a84464982795ec3ccb4"
    //  "text":"foo"} // if from slash command

    logging.infoF(LOG_NAME, 'job', JSON.stringify(payload));
    await ack();
    const dialog = await this.slackAuth(payload.team ? payload.team.id : payload.team_id, payload.user ? payload.user.id : payload.user_id);
    let timeout = this.timer(dialog, 'job', payload, say);

    if (dialog && dialog.isAuthenticatedNonGuest()) {
      let msg;
      if (payload.text) msg = `start job ${payload.text}`; //`I'll start a ${payload.text} job for you`;
      else msg = 'start job'; // 'What skills do you need help with?';
      await this.processMessage(dialog, msg, say);
    } else {
      const user_info = await client.users.info({user:event.user});
      const msg = await this.loginMessage(dialog, user_info);
      await this.reply(dialog, 'job', msg, timeout, say);
    }

    if (dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  }

  async proposal({ack, body, payload, event, say, context, client, logger}) {
    // {"type":"shortcut",
    //  "token":"wcVjjQD667Jsb6OXRybR2qx0",
    //  "action_ts":"**********.851435",
    //  "team":{"id":"T9A6NNFDW","domain":"askfora"},
    //  or "team_id": "T9A6NNFDW",
    //  "user":{"id":"UA45A2M9A","username":"otrajman","team_id":"T9A6NNFDW"},
    //  or "user_id": "UA45A2M9A",
    //  "callback_id":"proposal",
    //  "trigger_id":"*************.************.7a6642815ea24a84464982795ec3ccb4"
    //  "text":"foo"} // if from slash command

    logging.infoF(LOG_NAME, 'proposal', JSON.stringify(payload));
    await ack();
    const dialog = await this.slackAuth(payload.team ? payload.team.id : payload.team_id, payload.user ? payload.user.id : payload.user_id);
    let timeout = this.timer(dialog, 'proposal', payload, say);

    if (dialog && dialog.isAuthenticatedNonGuest()) {
      let msg;
      if (payload.text) msg = `propose project ${payload.text}`; //`I'll start a proposal for ${payload.text} for you`;
      else msg = 'propose project'; // 'Who is the proposal for?';
      await this.processMessage(dialog, msg, say);
    } else {
      const user_info = await client.users.info({user:event.user});
      const msg = await this.loginMessage(dialog, user_info);
      await this.reply(dialog, 'proposal', msg, timeout, say);
    }


    if (dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  }

  async note({ack, body, payload, event, say, context, client, logger}) {
    // {"type":"shortcut",
    //  "token":"wcVjjQD667Jsb6OXRybR2qx0",
    //  "action_ts":"**********.851435",
    //  "team":{"id":"T9A6NNFDW","domain":"askfora"},
    //  or "team_id": "T9A6NNFDW",
    //  "user":{"id":"UA45A2M9A","username":"otrajman","team_id":"T9A6NNFDW"},
    //  or "user_id": "UA45A2M9A",
    //  "callback_id":"note",
    //  "trigger_id":"*************.************.7a6642815ea24a84464982795ec3ccb4"
    //  "text":"foo"} // if from slash command

    logging.infoF(LOG_NAME, 'note', JSON.stringify(payload));
    await ack();
    const dialog = await this.slackAuth(payload.team ? payload.team.id : payload.team_id, payload.user ? payload.user.id : payload.user_id);
    let timeout = this.timer(dialog, 'note', payload, say);

    if (dialog && dialog.isAuthenticatedNonGuest()) {
      let msg;
      if (payload.text) msg = `note ${payload.text}`; // `I'll rememner "${payload.text}" for you`;
      else msg = 'take notes'; // 'What do you need to remember?';
      await this.processMessage(dialog, msg, say);
    } else {
      const user_info = await client.users.info({user:event.user});
      const msg = await this.loginMessage(dialog, user_info);
      await this.reply(dialog, 'note', msg, timeout, say);
    }


    if(dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));
  }

  ////////////////////// Utility functions ////////////////////////////////

  timer(dialog: Dialog, name, payload, say) {
    const timer_set = {
      fallback: false,
      complete: false,
    }

    let ts = payload.ts ? Math.floor(parseInt(payload.ts.replace(/\./, ''))/1000) :
      payload.action_ts ? Math.floor(parseInt(payload.action_ts.replace(/\./, ''))/1000) : new Date().getTime();

    const now = new Date().getTime();
    const timeout = 2500 - (now - ts);

    logging.infoF(LOG_NAME, 'timer', `ts ${ts} ${now} ${timeout} `);
    setTimeout(timeout).then(() => {
      timer_set.fallback = true;
      if (!timer_set.complete) {
        logging.infoFP(LOG_NAME, name, dialog ? dialog.user.profile : null, 'Status');
        say({text: 'Sorry, I got lost. Can you repeat that?', type: 'ephemeral'});
      }
    });
  }

  reply(dialog: Dialog, name, msg, timeout, say) {
    if(say && !timeout.fallback) {
      logging.infoFP(LOG_NAME, name, dialog ? dialog.user.profile : null, 'Direct reply');
      timeout.complete = true;
      say.json({text: msg, type: 'ephemeral'}).end();
    }
    else {
      logging.infoFP(LOG_NAME, name, dialog ? dialog.user.profile : null, 'Send message');
      return say(msg);
    }
  }

  async processMessage(dialog: Dialog ,message, say) {
    logging.infoFP(LOG_NAME, 'processMessage', dialog ? dialog.user.profile : null, `Processing "${message}"`);
    // let ping = INTERNAL_PING.ping;
    let max_ping = 10;
    let wait = true;
    let response: ServerChatResponse;
    const profile = dialog.user.profile;
    let respond = false;

    while (/*(ping < MIN_BG_PING && ping > 0) || ping === INTERNAL_PING.ping ||*/ dialog.isProcessing(false) && respond) {
      respond = false;
      dialog.message = {
        type: GlobalType.Message,
        message: message ? message : '',
        entities: [],
        // ping,
        offset: dialog.user.offset,
        locale: dialog.user.locale,
        timeZone: dialog.user.timeZone,
        link: false,
      } as GlobalMessage;

      message = '';
      // ping = INTERNAL_PING.ping;

      procBool(dialog);
      procNumber(dialog);
      procTime(dialog);
      procTypes(dialog);
      procEvents(dialog);
      procPlaces(dialog);
      procLocations(dialog);
      procUrls(dialog);
      procSkills(dialog);

      await procEntity(dialog);
      procQuery(dialog);

      while (dialog.isProcessing(false)) { // ping === INTERNAL_PING.ping) {
        // if (ping === INTERNAL_PING.ping) ping = 0;
        // if (dialog.message.ping === INTERNAL_PING.ping) delete dialog.message.ping;

        dialog.clearCommand();
        // by default ping every 30s
        // dialog.defaultPing();
        logging.infoFP(LOG_NAME, 'sendReply', profile, `Running action with Topic ${dialog.topic}`);
        dialog.currentPlugin().runAction(dialog);
        logging.infoFP(LOG_NAME, 'sendReply', profile, `Setting prompt with Topic ${dialog.topic}`);
        dialog.currentPlugin().setPrompt(dialog);
        logging.infoFP(LOG_NAME, 'sendReply', profile, `Exit Topic is ${dialog.topic}`);

        // ping = dialog.getPing();
        max_ping -= 1;

        // let async jobs complete
        if (dialog.isProcessing(false)) { // ping === INTERNAL_PING.ping) {
          await dialog.saveSession(true).catch(e => dialog.asyncError(e));
          await setTimeout(1000);
          await dialog.runSession();
        }
        if (max_ping <= 0) throw Error('Internal ping timeout');
      }

      const curr_dialog = dialog.currentDialog();

      response = {
        id: curr_dialog  && (curr_dialog.info.length || curr_dialog.replies.length) ? curr_dialog.id : hash(new Date().getTime().toString()),
        reply: undefined,
        info: undefined,
        answers: [],
        page: 0,
        hint: null,
        suggestion: null,
      } as ServerChatResponse;

      if (dialog.topic !== Topics.NOOP) {
        if (curr_dialog !== null && !curr_dialog.sent) {
          if (curr_dialog.prompt.length) {
            response.reply = curr_dialog.prompt;
            curr_dialog.sent = true;
          }
          if (curr_dialog.info.length) {
            response.info = curr_dialog.info;
            const info_ids = [response.id];
            for (const info of response.info) info_ids.push(info.id);
            response.id = hash(info_ids.join('-'));
            curr_dialog.sent = true;
          }
          if (curr_dialog.answers.length) {
            response.answers = curr_dialog.answers;
            curr_dialog.sent = true;
          }
          if (curr_dialog.page) {
            response.page = curr_dialog.page;
            curr_dialog.sent = true;
          }
          if (curr_dialog.hint) {
            response.hint = curr_dialog.hint;
            curr_dialog.sent = true;
          }
          if (curr_dialog.suggestion) {
            response.suggestion = curr_dialog.suggestion;
            curr_dialog.sent = true;
          }
          if (curr_dialog.quick_replies) {
            response.quick_replies = curr_dialog.quick_replies;
            curr_dialog.sent = true;
          }
        }
      }

      const command = dialog.getCommand();
      if (command !== null) {
        Object.assign(response, command);

        // used for feedback
        if (curr_dialog) {
          curr_dialog.last_command = {};
          Object.assign(curr_dialog.last_command, command);
        }

        logging.infoFP(LOG_NAME, 'sendReply', profile, `Command ${JSON.stringify(command)}`);
      }

      dialog.restoreTopic();
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));

      // ping = response.ping;

      if (dialog.isProcessing(false)) { //ping <= MIN_BG_PING && ping > 0) {
        if (!wait) {
          wait = true;
          await say('Working on it...sit tight');
        }
        // free up the session then get a new one
        await setTimeout(1000);
        dialog = await Dialog.loadSession('slack', { id: dialog.session.id, profile: dialog.user.profile, hostname: dialog.group_host.name, save:(c) => { if (c) c(); } }, {}, {create_new:true, stack:Error().stack, group: dialog.group_host});
      }

      if (response && response.answers && response.answers.includes('List')) {
        message = 'list';
        respond = true;
      }
    }

    if (response) {
      let blocks = [];
      let text = [];
      if (response.reply) {
        for(const r of response.reply) {
          if (r.label && r.label.length) text.push(r.label);
          const block = this.replyToBlock(dialog, r);
          if (block) blocks.push(block); 
        }
      }

      if (response.info) {
        for (let item of response.info.slice(0,10)) {
          if (item.type === EntityType.Person) {
            const [person] = await dialog.people.byId(item.id);
            if (person) item = dialog.getPersonInfo(person);
          }
          const block = this.infoToBlock(dialog, item);
          if (block) blocks.push(block); 
          if (item.people) {
            for (const p of item.people.filter(p => !p.self)) {
              const [person] = await dialog.people.byId(p.id);
              if (person) item = dialog.getPersonInfo(person);
              blocks.push(this.infoToBlock(dialog, item));
            } 
          }
        }
      }

      if (blocks && blocks.length) {
        const reply = { text: text.length ? text.join('\n') : 'Here you go', blocks: blocks.slice(0,50)}
        logging.infoFP(LOG_NAME, 'message', dialog.user.profile, JSON.stringify(reply, null, 2));
        await say(reply);
      } else if(response.ping && response.ping > 0) {
        say("Ok");
        setTimeout(response.ping).then(() => this.processMessage(dialog, undefined, say));
      }

      if (!dialog.isProcessing() || dialog.isDoneProcessing()) dialog.reset(); // response.ping >  MIN_BG_PING) await dialog.reset();
    } else await say('I got nothing');
  }

  descToBlock(dialog, desc) {
    const url = desc.link && desc.link.length ? mapURL(desc.link, dialog.group_host) : null;
    // const start = desc.date ? `\n${localwDowMonthDay(desc.date, dialog.user.locale, dialog.user.timeZone)}` : '';
    return [DIVIDER,
      {
        type: 'section', 
        text: {
          type: 'mrkdwn', 
          text: url ? `*<${url}|${desc.text}>*\n${desc.label}` : `*${desc.text}*\n${desc.label}`
        }, 
      },
    ].concat(desc.people ? desc.people.filter(p => !p.self).map(p => this.infoToBlock(dialog, p)): []);
  }

  replyToBlock(dialog, r) {
    if (r.link && r.link.length) {
      const url = mapURL(r.link, dialog.group_host);
      // const formatted = `${r.label} <a href="${r.link}">${r.text ? r.text : r.link}</a>`;
      // const formatted = `${r.label} <a href="${r.link}">${r.text ? r.text : r.link}</a>`;
      return {
        type: 'section', 
        text: { 
          type: 'mrkdwn', 
          text: r.label ? `${r.label} <${url}|${r.text}>` : `<${url}|${r.text}>`
        },
      };
    } else {
      return {
        type: 'section', 
        text: { 
          type: 'plain_text', 
          text: r.label ? r.label : ' '
        }
      }
    }
  }

  infoToBlock(dialog: Dialog, item) {
    let type_path;
    let name;
    switch(item.type) {
      case EntityType.Person: 
        {
          const person = item as PersonInfo;
          name = person.name;
          const desc = person.description ? `\n${person.description.filter(d => d.link).slice(0,3).map(d => {
              const url = d.link && d.link.length ? mapURL(d.link, dialog.group_host) : null;
              return url ? `<${url}|${d.text}>\n${d.label}` : `${d.text}\n${d.label}`;
            }).join('\n')}` : '';
          const skills = person.skills && person.skills.length ? `\nKeywords: ${person.skills.filter(s => !parsers.ignore(s.value)).slice(0,5).join(', ')}` : '';
          const block = { 
            type: 'section', 
            text: {
              type: 'mrkdwn',
              text: `*<${mapURL(`people/${person.id}`, dialog.group_host)}|${name}>*${desc}${skills}`
            }
          };

          if (person.image) {
            block['accessory'] = { type: 'image', image_url: mapURL(`${person.image}/88`, dialog.group_host, dialog.user.profile), alt_text: 'Image' };
          }

          return block;
        }
      case EntityType.Note:
      case EntityType.Task: 
        {
          const note = item as TaskInfo;
          const due = note.due && new Date(note.due).getTime() ? `\nDue: ${localeDowMonthDay(note.due, dialog.user.locale, dialog.user.timeZone)}` : '';
          const project = note.project ? `\n<${mapURL(`/job/${note.project}`, dialog.group_host)}|Link to Job>` : '';
          const notes = note.notes ? note.notes : '';
          return {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `*<${mapURL(`/note/${note.id}`, dialog.group_host)}|${item.title}>*${due}${project}${notes}` 
            }
          }
        }
      case EntityType.Contract: type_path = '/contract'; break;
      case EntityType.Project: type_path = '/job'; break;
      case EntityType.User:
      case EntityType.Event:
      case EntityType.Message:
      case EntityType.Location:
      case EntityType.None:
      case EntityType.Organization:
      case EntityType.Calendar:
      case EntityType.UnresolvedPerson:
      case EntityType.Document:
      case EntityType.Payment:
      case EntityType.Recommendation:
        item = null;
        break;
    }

    if (item) {
      const url = mapURL(`${type_path ? type_path : ''}/${item.id}`, dialog.group_host);
      return {
        type: 'section', 
        text: {
          type: 'mrkdwn', 
          text: `<${url}|${name}>`
        }
      };
    }
  }

  async loginMessage(dialog, user_info) {
    let login = `Hey there, I'm Fora. It's great to meet you. I need to learn a bit more about you in order to help. Go ahead and login at https://askfora.com`;
    if (user_info) {
      const name_space = user_info ? ` ${user_info.user.real_name.split(' ')[0]}` : '';
      const url = mapURL(`${config.get('NOTIFICATION_URL')}/connect/slack`, dialog.group_host);
      login = `Hey there${name_space}, I'm Fora. It's great to meet you. I need to learn a bit more about you in order to help. Go ahead and login here: ${url}`;
    }
    return login;
  }

  async slackAuth(team_id: string, user_id: string): Promise<Dialog> {
    const global_user = await data.users.globalByAccount(AuthProviders.Slack, `${team_id}_${user_id}`);
    /*if (this.team_id) this.group = await data.groups.byAccount(AuthProviders.Slack, this.team_id);
    else this.group = await data.groups.byHost(this.hostname);

    const tokens = this.group && this.group.accounts && this.group.accounts.slack ? this.group.accounts.slack[this.team_id] : null;
    this.bot_id = tokens ? tokens.bot_id : null;*/

    let dialog: Dialog;

    const group = await data.groups.byAccount(AuthProviders.Slack, team_id);

    if (global_user) {
      const session_id = `slack_${global_user.id}_${new Date().getTime()}`;
      dialog = await Dialog.loadSession('slack', { id: session_id, profile: global_user.id, hostname: group ? group.host : null, save:(c) => { if (c) c(); } }, {}, {create_new:true, stack:Error().stack, group});
    } else {
      const session_id = `slack_anonymous_${new Date().getTime()}`;
      dialog = await Dialog.loadSession('slack', { id: session_id, hostname: group ? group.host : null, save:(c) => { if (c) c(); } }, {}, {create_new:true, stack:Error().stack, group});
    }
    
    logging.infoFP(LOG_NAME, 'slackAuth', dialog.user.profile, `Team ${team_id} User ${user_id} Group ${group ? group.id : 'none'}`);

    return dialog;
  }

}

router.route('/:func?').get(async (req: express.Request, res: express.Response, _next: express.NextFunction) => {
  res.redirect('/slack/connect');
});

router.route('/:func?').post(async (req: express.Request, res: express.Response, _next: express.NextFunction) => {
  try {
    await slackRouter.route(req, res);
  } catch(e) {
    logging.errorF(LOG_NAME, 'post', JSON.stringify(req.params), e);
  }
});

export async function slackFunction(req: express.Request, res: express.Response) {
  logging.infoF(LOG_NAME, 'slackFunction', `Request ${req.path} ${JSON.stringify(req.headers)}`);
  if (!config.configLoaded()) await config.loadConfig(true);
  return slackRouter.route(req, res);
}

export default router;