import express from 'express';
import 'express-session';
import data from '../data';
import Dialog from '../session/dialog';
import { Tracking } from '../types/globals';
import logging from '../utils/logging';

const router = express.Router();

const LOG_NAME='routes.vanity';

router.get('/', async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
  const dialog: Dialog = await Dialog.loadSession('vanity', req.session, req.cookies, {offset, read_only: 'check' in req.query, no_cache: true, create_new:false, check_tokens:true, stack:Error().stack});

  if (dialog) {
    try {
      dialog.user.track(req.query as Partial<Tracking>);
      let status = 'ok';
      if (req.query.check) {
        const vanity = encodeURI((req.query.check as string).replace(/[,./\\#@!? ]/g, '').toLowerCase());
        const user = await data.users.globalByVanity(vanity);
        if (user) {
          if (user.profile !== dialog.user.profile) status = 'invalid';
        }
      } else if (req.query.update) {
        const vanity = encodeURI((req.query.update as string).replace(/[,./\\#@!? ]/g, '').toLowerCase());
        const user = await data.users.globalByVanity(vanity);
        if (!user || user.profile === dialog.user.profile) {
          dialog.user.vanity = vanity;
          // await data.users.globalRegister(dialog.user);
          await data.users.save(dialog.user);
          await data.users.saveVanity(dialog.user, dialog.me);
        } else status = 'invalid';
      }

      await dialog.saveSession();
      res.json({ status });
      res.end();
      return;
    } catch(e) {
      await dialog.saveSession().catch(e => dialog.asyncError(e));
      logging.errorFP(LOG_NAME, 'get', dialog.user.profile, `Error processing vanity`, e);
      res.status(500).end();
      return;
    }
  }

  res.status(404).end();
});

export default router;
