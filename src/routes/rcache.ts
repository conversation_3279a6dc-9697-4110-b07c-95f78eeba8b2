import express from 'express';
import 'express-session';
import config from '../config';
import data from '../data/index';
import Dialog from '../session/dialog';

const router = express.Router();

const PRETTY_PRINT = '<script src="https://cdn.rawgit.com/google/code-prettify/master/loader/run_prettify.js"></script>';

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  if (!config.isEnvProduction()) {
    const dialog: Dialog = await Dialog.loadSession('rcache', req.session, req.cookies, {read_only:true, check_tokens:true, stack:Error().stack});
    if (dialog) {
      res.status(200);

      const caches = {
        instructions  : 'To get the keys/data in memcache, run `./tools/memcached-tool.pl localhost dump`. memjs won\'t let us :/',
        memcache_stats: undefined,
        data          : JSON.parse(JSON.stringify(dialog.cache)),
      };

      if (req.query.stats) {
        // https://stackoverflow.com/questions/19560150/get-all-keys-set-in-memcached
        // 1 - `stats items`
        // 2 - `stats cachedump 3 100` - 3 is an example slab from the previous command. 100 is the number of keys
        caches.memcache_stats = await new Promise<Record<string, string>>(async (c, r) => {
          // const slabs = new Set();
          data.plugins.cachePlugin().statsWithKey('items', (err: Error, server: string, stats: Record<string, string>) => {
            if (err) return r(err);

            // We can't get to the keys within a slab,; silly memjs
            // Can't figure out how to send the `cachedump ${slab} 10` command through to the server

            // // Key will look like items:1:number_cold
            // Object.keys(stats).map(key => {
            //   const parts = key.split(':');
            //   slabs.add(parts[1]);
            // });
            //
            // for (const slab of slabs) {
            //   data.plugins.cachePlugin().statsWithKey(`cachedump`, (err: Error, server: string, stats: Record<string, string>) => {
            //     if (err) return r(err);
            //
            //     console.log('XXX', stats);
            //     c();
            //   });
            // }

            c(stats);
          });
        });
      }

      if (req.query.pretty) {
        res.send(`<html>${PRETTY_PRINT}<pre class="prettyprint"><code language="json">${JSON.stringify(caches, null, 2)}</code></pre></html>`).end();
      } else res.json(caches).end();
      res.end();
      return;
    }
  }

  res.status(404).end();
});

export default router;
