import express from 'express';
import 'express-session';
import fs from 'fs';
import path from 'path';
import data from '../data';
import Dialog, { Topics } from '../session/dialog';
import { Tracking } from '../types/globals';
import { EntityType, NotificationType } from '../types/shared';
import logging from '../utils/logging';
import peopleUtils from '../utils/people';

const router = express.Router();

const LOG_NAME='routes.intro';

router.get('/:intro_id', async (req: express.Request, res: express.Response, next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
  const dialog: Dialog = await Dialog.loadSession('intro', req.session, req.cookies, {offset, read_only: false, no_cache: true, create_new:true, check_tokens:true, stack:Error().stack});

  if (dialog) {
    try {
      dialog.user.track(req.query as Partial<Tracking>);
      if (req.params.intro_id) {
        const intro = await dialog.people.introductionById(req.params.intro_id);
        if (intro) {
          if (dialog.user.profile === intro.intro_to) {
            const people = (await data.people.vanityByIds([intro.requested_by, intro.intro_to])).sort((a,b) => a.profile === intro.requested_by ? -1 : 1).map(v => peopleUtils.personFromVanity(v));
            dialog.context.make_intro = {
              people,
              intro_id: intro.id,
            }
            await dialog.getInternalNotifications(NotificationType.Introduction);

            dialog.user.settings.info = { enabled: false, profile: false, accounts: false, notify: false, contracts: false};
            dialog.user.settings.active = EntityType.Person;
            dialog.user.settings.people.active = dialog.getPersonInfo(people[0]);

            //close open projects and notes
            if (dialog.user.settings.projects) dialog.user.settings.projects.active = null;
            if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;

            await dialog.saveSettings(dialog.user.settings);
            dialog.setTopic(Topics.ACCEPT_CONNECTION);
          } else if(dialog.user.profile === intro.requested_by) {
            const people = (await data.people.vanityByIds([intro.requested_by, intro.intro_to])).sort((a,b) => a.profile === intro.requested_by ? -1 : 1).map(v => peopleUtils.personFromVanity(v));
            dialog.context.make_intro = {
              people,
              intro_id: intro.id,
              requested: true,
              connection: true,
            }
            dialog.setTopic(Topics.MAKE_CONNECTION);
          } else dialog.setTopic(Topics.INVALID_INTRO);
        } else if(dialog.isAuthenticatedNonGuest()) {
          dialog.setTopic(Topics.INVALID_INTRO);
        } else {
          dialog.setTopic(Topics.AUTH_INTRO);
          dialog.context.make_intro = { intro_id: req.params.intro_id };
        }

        await dialog.saveSession();
        const redirect_page = fs.readFileSync(path.resolve(__dirname, '..', 'files', 'redirect.html'), 'utf-8');
        res.send(redirect_page).end();
        return;
      } 
      await dialog.saveSession();
    } catch(e) {
      await dialog.saveSession().catch(e => dialog.asyncError(e));
      logging.errorFP(LOG_NAME, 'get', dialog.user.profile, `Error processing intro`, e);
      res.status(500).end();
      return;
    }
  }

  res.status(404).end();
});

export default router;