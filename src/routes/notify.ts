import express from 'express';
import 'express-session';
import _ from 'lodash';
import multer from 'multer';

import { AuthProvider } from '../auth/auth_provider';

import { User as GUser, Notification, ONBOARDING_TEMPLATES, OnboardingStep, TemplateType, Tracking, WebPush } from '../types/globals';
import { Group } from '../types/group';
import { Event, Message, Task } from '../types/items';
import { EntityType, NotificationType, NotifyInfo, ProjectCandidateState, Reply, Uid } from '../types/shared';

import { DAYS, HOURS, MINUTES, MONTHS, priorityEvent, sameDay, sameSecond } from '../utils/datetime';
import { mapURL, sortTutorials } from '../utils/funcs';
import { dayInfo, eventInfo, messageInfo, taskInfo } from '../utils/info';
import logging from '../utils/logging';
import { mailTemplate } from '../utils/mail';
import notify, { NotifyType } from '../utils/notify';

import { SourceController } from '../sources/source_controller';
import { firstRun } from './update';

import config from '../config';
import data from '../data';
import lang from '../lang';

import Dialog from '../session/dialog';
import ForaUser from '../session/user';

import { internalPub } from './pub';

const DEBUG = (require('debug') as any)('fora:routes:notify');

const LOG_NAME = 'routes.notify';
const router = express.Router();
const upload = multer(); // for parsing multipart/form-data

interface NotifyRequest {
  force: boolean;
  users: Uid[];
  group?: Uid;
}

export async function doNotify(profile: string, refresh: Date, when: Date = null, force = false) {
  let now: Date = new Date();

  if (config.isEnvDevelopment() && when) now = when;
  if (!refresh || !(new Date(refresh).getTime()) || isNaN(new Date(refresh).getTime())) refresh = now;

  logging.infoFP(LOG_NAME, 'doNotify', profile, `Checking for notify at ${refresh.toUTCString()}`);

  // run a what's next and send a push notice depending on the use prefs:
  // Notfiy logic:
  // events - 10 minutes before every event
  // meetings - 10 minutes before every event with multiple attendees.
  // reminders - events plus if there are no events in the next 30 minutes
  //         but no more than three times a day and
  //         only between 8 and 11 and 1 and 5 local to the users last offset.
  let dialog: Dialog = await Dialog.loadSession('notify', { id: `notify_${profile}_${now.getTime()}`, profile }, {}, {ephemeral: true, read_only:true, create_new: true, check_tokens:false, stack:Error().stack});
  let has_notifications = dialog ? await dialog.getInternalNotifications(null, false) : null;

  if (dialog) {
    if (logging.isDebug(profile)) {
      logging.debugFP(LOG_NAME, 'doNotify', profile, `has_notification ${JSON.stringify(has_notifications)}`);
      logging.debugFP(LOG_NAME, 'doNotify', profile, `push settings ${dialog.user.settings.push}`);
    }

    // make sure self is loaded
    if (!dialog.me) {
      logging.warnFP(LOG_NAME, 'doNotify', profile, `Cannot notify. User has no self - tokens may have expired or account was not fully deleted`);
      return;
    }

    if (has_notifications && has_notifications.length) {
      // send notifications that have email and no webpush
      for (const notification of has_notifications) {
        if (!notification.webpush && notification.email) {
          await notify(dialog.user, notification, NotifyType.EmailOnly, dialog.getNotifyGroup(dialog.user.email), true).catch(e => dialog.asyncError(e));
          await dialog.clearInternalNotification(notification.id);
        }
      }
    } else if(dialog.user.settings.push && dialog.lastChat().getTime() > DAYS(now, -5).getTime()) {
      if (!dialog.user.settings.calendars) {
        dialog = null;
        has_notifications  = null;
        throw new Error(`Cannot notify ${profile} - user has no calendars`);
      }

      const { events, tasks, messages } = dialog.notificationItems();

      if (events.length || tasks.length || messages.length) {
        let next = null;

        let whats_next = dialog.activitySummary(
          [EntityType.Event, EntityType.Task, EntityType.Message],
          events,
          tasks,
          messages,
          now,
          null,
          false,
        );

        if (whats_next.next && whats_next.next.length) {
          let index = 0;

          // If it's between 7 and 11:30 or 1 and 5:30
          const local = MINUTES(now, now.getTimezoneOffset() - dialog.user.offset);
          const hour = local.getUTCHours();
          const minutes = local.getUTCMinutes();
          const thirty_min = MINUTES(dialog.lastChat(), 30);
          const can_notify = (config.isEnvDevelopment() && !config.isRunningOnGoogle()) || config.isEnvOffline() || thirty_min < now;

          logging.infoFP(LOG_NAME, 'doNotify', dialog.user.profile, `Can notify ${can_notify} ${dialog.lastChat()} + 30min = ${thirty_min} < ${now} ${JSON.stringify(has_notifications)}`);

          next = whats_next.next[index];
          while (next) {
            let ndata = 'today';
            // conditions for 'reminders'
            let reminders = false;
            if (tasks.length) {
              if ((hour >= 7 && hour <= 11) || (hour >= 13 && hour <= 17) || config.isEnvOffline()) {
                // if we haven't pinged in 30 min...
                if (can_notify) {
                  // and there are no events in the next 30 minutes
                  if (next.type !== EntityType.Event || MINUTES(new Date(next.start), -30) >= now) {
                    reminders = true;
                    let due = null;
                    if (next && next.type === EntityType.Task) {
                      due = new Date(next.due);
                      if (!due.getTime()) next = null;
                    }

                    // first thing in the morning show a prompt about today's schedule
                    if (hour < 8 && minutes < 10 && (!due || !sameDay(due, now, dialog.user.offset) || MINUTES(due, -30) >= now)) {
                      ndata = 'today';
                      next = null;
                      whats_next.full_day = true;
                    }
                  }
                }
              }
            }

            let start = null;
            let end = null;
            if (next && next.type === EntityType.Event) {
              start = new Date(next.start);
              end = new Date(next.end);
            }

            let info: NotifyInfo = null;
            let prompt: Reply[] = null;
            if (whats_next.full_day) info = dayInfo(whats_next);
            else if (next) {
              switch (next.type) {
                case EntityType.Event:
                  {
                    // make sure event exists
                    let event = next as Event;
                    try { 
                      event = await dialog.events.reload(next);
                    } catch(e) {
                      logging.warnFP(LOG_NAME, 'doNotify', dialog.user.profile, `Error reloading event ${event.id}`, e);
                    }
                    if (event && (can_notify || priorityEvent(event, now, true))) {
                      info = eventInfo(event, false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
                      ndata = 'event';
                    } else next = null;
                  }
                  break;
                case EntityType.Task: 
                  if (can_notify) {
                    // make sure task exists
                    let task = next as Task;
                    try {
                      task = await dialog.tasks.reload(next);
                    } catch(e) {
                      logging.warnFP(LOG_NAME, 'doNotify', dialog.user.profile, `Error reloading task ${task.id}`, e);
                    }
                    if (task) {
                      info = taskInfo(task, false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
                      ndata = 'task due';
                    } else next = null;
                  } else next = null;
                  break;
                case EntityType.Message:
                  if (can_notify) {
                    let message = next as Message;
                    try {
                      message = await dialog.messages.reload(next);
                    } catch(e) {
                      logging.warnFP(LOG_NAME, 'doNotify', dialog.user.profile, `Error reloading message ${message.id}`, e);
                    }
                    if (message && (message.draft || !message.read)) {
                      info = messageInfo(next, false, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
                      ndata = 'message';
                    } else next = null;
                  } else next = null;
                  break;
              }

              if (next) {
                logging.infoFP(LOG_NAME, 'doNotify', dialog.user.profile, `Notifying ${next.type} ${next.id}`);
                const format = dialog.formatNext(next, now, whats_next.full_day, dialog.user.offset, dialog.user.locale, dialog.user.timeZone);
                if (format) {
                  if (format.info) info = format.info;
                  if (format.prompt) {
                    if (Array.isArray(format.prompt)) prompt = format.prompt.slice(0, 1);
                    else prompt = [format.prompt];
                  }
                }
              } else {
                index++;
                if (index < whats_next.next.length) next = whats_next.next[index];
              }
            }

            if (info) {
              logging.infoFP(LOG_NAME, 'doNotify', dialog.user.profile, `Sending notifiation for ${info.type} ${info.id}`);
              if (config.isEnvDevelopment() && !config.isRunningOnGoogle() && when) ndata += ` ${when.toUTCString()}`;

              if ((reminders && (!next || next.type !== EntityType.Event)) || 
                  (start && MINUTES(start, -15) <= now && start >= now) || (end && MINUTES(end, 15) >= now && end <= now) ||
                  (config.isEnvDevelopment() && !config.isRunningOnGoogle() ) || config.isEnvOffline()) {
                const webpush: WebPush = {
                  notification: {
                    tag: info.id,
                    title: info.title,
                    body: info.description,
                    icon: info.image,

                    click_action: `${config.get('NOTIFICATION_URL')}/?what%20next%20${ndata}`,
                  },
                  data: {
                    message: `what next ${ndata}`,
                  },
                };

                let notify_dialog = null;
                try { 
                  notify_dialog = await Dialog.loadSession('notify', { id: `notify_${profile}_${now.getTime()}`, profile }, {}, {ephemeral: true, read_only:false, create_new: true, check_tokens:false, stack:Error().stack});
                } catch(err) {
                  logging.errorFP(LOG_NAME, 'doNotify', profile, `Error loading notify dialog`, err);
                }

                if (notify_dialog) {
                  try { 
                    await notify(notify_dialog.user, { webpush, prompt }, NotifyType.PushOnly, notify_dialog.getNotifyGroup(notify_dialog.user.email), true).catch(e => notify_dialog.asyncError(e));
                  } catch(e) {
                    logging.errorFP(LOG_NAME, 'doNotify', profile, `Error sending notification ${JSON.stringify(webpush)} ${JSON.stringify(prompt)}`, e);
                  }
                  await notify_dialog.saveSession();
                } else logging.warnFP(LOG_NAME, 'doNotify', profile, `No dialog to notify${whats_next.full_day ? ' full day' : ''} type: ${next ? next.type : 'none'}` );
              }
              next = null;
            } else logging.warnFP(LOG_NAME, 'doNotify', profile, `No info to notify${whats_next.full_day ? ' full day' : ''} type: ${next ? next.type : 'none'}` );
          }
        }

        whats_next = null;
      }
    }

    const global_user = await data.users.globalById(profile);
    const last_notify = global_user.last_notify ? new Date(global_user.last_notify) : new Date(0);

    let projects = await dialog.projects.load(); // Object.values(dialog.cache.projects);
    let sent_reminder = false;

    if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'doNotify', profile, `projects ${projects.length}`);
    if (projects.length) {
      for (let project of projects) {
        let notification;
        let template;

        if (project.client && project.client.self) {
          const project_id = project.id;
          project = await dialog.projects.get(project);
          if (project) {
            if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'doNotify', profile, `project reminder ${project.client.reminder}`);
            if (project.client.reminder && (config.isEnvOffline() || config.isEnvDevelopment() ||
              new Date(project.client.reminder.when) < now)) {
              notification = project.client.reminder.notification;
              template = project.client.reminder.template;
              switch(template) {
                case TemplateType.None:
                  project.client.reminder = {
                    when: DAYS(now, 1),
                    notification,
                    template: TemplateType.None_Second,
                  }
                  break;
                case TemplateType.Select:
                  project.client.reminder = {
                    when: DAYS(now, 3),
                    notification,
                    template: TemplateType.Select_Second,
                  }
                  break;
                default:
                  project.client.reminder = null;
                  break;
              }
              project.last_update = now;
              if (project.contractor) notification = null;
              await dialog.projects.update(project);
            }
          } else logging.warnFP(LOG_NAME, 'doNotify', profile, `Failed to load project ${project_id}`);
        }

        if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'doNotify', profile, `project notification ${notification}`);
        if (notification) {
          const email = { rcpts: [ { Name: dialog.me.displayName, Email: dialog.user.email }] }
          await dialog.projects.notify(project, dialog.user, dialog.me, email, { group: dialog.getNotifyGroup(dialog.user.email), notification, template });
          sent_reminder = true;
        }
      }
    }

    projects = null;

    if (!sent_reminder) {
      // check for intro reminders
      const intros = await dialog.people.findIntroductionsWaitingForMe();
      if (intros) {
        const intro_set = intros.filter(intro => !intro.accepted && !intro.reminder && 
            (config.isEnvDevelopment() || DAYS(intro.created, 2) < now)).sort((a,b) => a.created.getTime() - b.created.getTime());
        if (intro_set.length) {
          const intro = intro_set[0];
          const project = intro.project ? dialog.cache.projects[intro.project] : null;

          const from_person = await data.people.getUserPerson(dialog.user, intro.requested_by);
          if (from_person) {
            const url = mapURL(`${config.get('NOTIFICATION_URL')}/intro/${intro.id}`, dialog.getGroup());
            const subject = lang.introduce.CONNECTION_SUBJECT;
            const message = lang.introduce.CONNECTION_EMAIL([from_person, dialog.me], null, url);
            const template = intro.project ? null : TemplateType.Networking;

            const email = {
              rcpts: [{ Name: dialog.me.displayName, Email: dialog.user.email }],
              subject,
              message,
            };

            const variables = {
              to_first_name: dialog.me.displayName.split(' ')[0],
              from_first_name: from_person.displayName.split(' ')[0],
              from_full_name: from_person.displayName,
              job_name: project ? project.title: '',
              intro_link: url,
            };

            await notify(dialog.user, { email, type: NotificationType.Introduction, variables, template }, NotifyType.EmailOnly, dialog.getNotifyGroup(dialog.user.email));
            intro.reminder = now;
            sent_reminder = true
            await dialog.people.saveIntroduction(intro);
          } else logging.warnFP(LOG_NAME, 'doNotify', dialog.user.profile, `Intro without from person ${JSON.stringify(intro)}`);
        }
      }
    }

    const yesterday = DAYS(now, -1);
    if (!sent_reminder && (last_notify <= yesterday || force)) {
      // look for learning goals
      const goals = await dialog.goals.load();

      const notification: Notification = {
        email: { rcpts: [{Name: dialog.me.displayName, Email: global_user.email}] },
        type: NotificationType.Goal,
        variables: {
          firstname: global_user.name,
        }
      }

      // find elligable goals - < 100% not deleted with open courses
      const open_goals = goals.filter(g => g.score < 1 && !g.deleted && !g.courses || !g.courses.length || !!g.courses?.find(c => !c.completed && !c.ignore)).sort((a,b) => 
        new Date(b.last_update).getTime() - new Date(a.last_update).getTime()
      );
      const assigned_goals = open_goals.filter(g => g.assigned);
      const self_goals = open_goals.filter(g => !g.assigned);


      //////////////////////////////
      // In-app
      const assigned_yesterday = assigned_goals.find(g => sameDay(g.last_update, yesterday, dialog.user.offset) && sameDay(g.created, g.last_update, dialog.user.offset));
      const remind_assigned = assigned_goals.find(g => g.last_update <= DAYS(now, -3));

      const self_yesterday = self_goals.find(g => sameDay(g.last_update, yesterday, dialog.user.offset) && sameDay(g.created, g.last_update, dialog.user.offset))
      const remind_self = self_goals.find(g => g.last_update <= DAYS(now, -3));

      let in_app = false;
      if (assigned_yesterday) {
        const url = mapURL(`/app/learning/${assigned_yesterday.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        // in app
        notification.webpush = {
          notification: {
            tag: `${assigned_yesterday.id}_goal_reminder`,
            title: lang.goal.REMINDER(assigned_yesterday.title),
            body: lang.goal.CLICK,
            click_action: url,
          }
        }
        in_app = true;
      } else if(remind_assigned) {
        const url = mapURL(`/app/learning/${remind_assigned.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        // in app
        notification.webpush = {
          notification: {
            tag: `${remind_assigned.id}_goal_reminder`,
            title: lang.goal.REMINDER(remind_assigned.title),
            body: lang.goal.CLICK,
            click_action: url,
          }
        } 
        in_app = true;
      } else if(self_yesterday) {
        const url = mapURL(`/app/learning/${self_yesterday.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        // in app
        notification.webpush = {
          notification: {
            tag: `${self_yesterday.id}_goal_reminder`,
            title: lang.goal.REMINDER(self_yesterday.title),
            body: lang.goal.CLICK,
            click_action: url,
          }
        }
        in_app = true;
      } else if(remind_self) {
        const url = mapURL(`/app/learning/${remind_self.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        // in app
        notification.webpush = {
          notification: {
            tag: `${remind_self.id}_goal_reminder`,
            title: lang.goal.REMINDER(remind_self.title),
            body: lang.goal.CLICK,
            click_action: url,
          }
        }
        in_app = true;
      }

      //////////////////////////////
      // Email notifications
      // last updated on the assigned date
      const new_assigned_goal = assigned_goals.find(g => g.created <= yesterday && sameSecond(g.assigned, g.last_update, dialog.user.offset));

      // created and viewed yesterday
      const last_self_goal = self_goals.find(g => sameDay(g.created, yesterday, dialog.user.offset) && sameDay(g.last_update, yesterday, dialog.user.offset));
      const last_assigned_goal = assigned_goals.find(g => !g.courses || !g.courses.length); //sameDay(g.created, yesterday, dialog.user.offset) && sameDay(g.last_update, yesterday, dialog.user.offset));

      // older than 5 days
      const older_self_goal = self_goals.find(g => g.created <= DAYS(now, -5) && g.last_update <= yesterday);
      const older_assigned_goal = assigned_goals.find(g => g.created <= DAYS(now, -5) && g.last_update <= DAYS(now, -5) && 
        (!g.last_notify || g.last_notify <= DAYS(now, -5)) );

      if (new_assigned_goal) {
        const url = mapURL(`/app/learning/${new_assigned_goal.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        notification.variables.goalname = new_assigned_goal.title;
        notification.variables.learninggoal = url;
        if (new_assigned_goal.assigner) {
          const assigner = await data.users.globalById(new_assigned_goal.assigner);
          if (assigner) notification.variables.admin_firstname = assigner.name;
        }
        notification.template = TemplateType.AssignedNew; // 5928132
        await dialog.goals.notify(new_assigned_goal);
      } else if (last_assigned_goal) {
        const url = mapURL(`/app/learning/${last_assigned_goal.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        notification.variables.goalname = last_assigned_goal.title;
        notification.variables.learninggoal = url;
        if (last_assigned_goal.assigner) {
          const assigner = await data.users.globalById(last_assigned_goal.assigner);
          if (assigner) notification.variables.admin_firstname = assigner.name;
        }
        notification.template = TemplateType.AssignedReminder; // 5928165
        await dialog.goals.notify(last_assigned_goal);
      } else if(older_assigned_goal) {
        const url = mapURL(`/app/learning/${older_assigned_goal.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        notification.variables.goalname = older_assigned_goal.title;
        notification.variables.learninggoal = url;
        if (older_assigned_goal.assigner) {
          const assigner = await data.users.globalById(older_assigned_goal.assigner);
          if (assigner) notification.variables.admin_firstname = assigner.name;
        }
        notification.template = TemplateType.AssignedFinal; // 5933485
        await dialog.goals.notify(older_assigned_goal);
      } else if(last_self_goal) {
        const url = mapURL(`/app/learning/${last_self_goal.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        notification.variables.goalname = last_self_goal.title;
        notification.variables.learninggoal = url;
        notification.template = TemplateType.Reminder // 5934149
        await dialog.goals.notify(last_self_goal);
      } else if(older_self_goal) {
        const url = mapURL(`/app/learning/${older_self_goal.id}`, dialog.getNotifyGroup(dialog.user.email), dialog.user.profile);
        notification.variables.goalname = older_self_goal.title;
        notification.variables.learninggoal = url;
        notification.webpush = {
          notification: {
            tag: `${older_self_goal.id}_goal_reminder`,
            title: _.sample(lang.goal.PROMOTE(notification.variables.goalname)),
            body: lang.goal.CLICK,
            click_action: url,
          }
        }
        notification.template = TemplateType.Final; // 5934158
        await dialog.goals.notify(older_self_goal);
      } else delete notification.email;

      if (notification.template || in_app) {
        await notify(dialog.user, notification, notification.webpush ? NotifyType.PushAndEmail : NotifyType.EmailOnly, dialog.getNotifyGroup(dialog.user.email));
        sent_reminder = true
      }
    }

    dialog = null;
    has_notifications  = null;
  }
}

export async function notifyStep(user: ForaUser): Promise<OnboardingStep> {
  const global_user = await data.users.globalById(user.profile);
  if (global_user && global_user.onboarding) {
    await data.users.init(user, false);
    const last_notify = global_user.last_notify ? new Date(global_user.last_notify) : new Date(0);
    const last = user.last ? new Date(user.last) : new Date(0);
    const last_date = last_notify > last ? last_notify : last;
    // const start = new Date(user.start);
    const now = new Date();

    logging.infoFP(LOG_NAME, 'notifyStep', user.profile, `Last notify: ${last_notify} Last: ${last} Last date: ${last_date} Now: ${now} ${global_user.onboarding}`);

    if (last_date && last_date.getTime() > 0 || config.isEnvDevelopment() || config.isEnvOffline()) {
      const onboarding = await data.templates.onboarding(global_user.onboarding);
      if (onboarding) {
        if (config.isEnvDevelopment() || config.isEnvOffline()) return onboarding;
        if (onboarding.minutes && MINUTES(last_date, onboarding.minutes) < now) return onboarding;
        if (onboarding.hours && HOURS(last_date, onboarding.hours) < now) return onboarding;
        if (onboarding.days && DAYS(last_date, onboarding.days) < now) return onboarding;
      }
    }
  }
}

export async function onboardUser(user: ForaUser): Promise<boolean> {
  const onboarding = await notifyStep(user);
  if (onboarding) {
    logging.infoFP(LOG_NAME, 'onboardUser', user.profile, `Onboarding steps ${JSON.stringify(onboarding)}`);
    if(onboarding.template || onboarding.next_step) await data.users.onboardingSet(user, onboarding.next_step);

    if (onboarding.template) {
      if (user.email) {
        const groups = await data.groups.groups();
        const group = groups.find(g => g.email_domain && 
          Array.isArray(g.email_domain) ? g.email_domain.find(ed => user.email.endsWith(ed))
          : user.email.endsWith(g.email_domain as any as string));
        const profile_url = mapURL(`/profile/${user.vanity}`, group);
        const person = await data.people.getUserPerson(user);
        const name = person ? person.displayName : user.name;

        let job = '';
        let job_name = '';
        const projects = onboarding.template === TemplateType.Start ? await data.projects.openProjects(user) : [];
        if (projects.length && projects[0].candidates.filter(c => c.state !== ProjectCandidateState.FOUND).length === 0) {
          const project = projects[0];
          job_name = project ? project.title : null;
          const project_group = project.group_settings && project.group_settings.group ? groups.find(g => g.id == project.group_settings.group) : group;
          job = project ?  mapURL(`${config.get('PROJECT_URL')}/${project.id}`, project_group) : null;
        }

        await mailTemplate([{ Email: user.email, Name: name, }], [],
          NotificationType.Onboarding,
          onboarding.template,  
          {
            firstname: name.split(' ')[0],
            url: onboarding.url ? mapURL(onboarding.url, user.auth_group) : undefined,
            profile: profile_url,
            job_name,
            job,
          }
        );

        if (typeof user.offline_notify === 'function') user.offline_notify({type: NotificationType.Onboarding}, NotificationType.Onboarding);
      } else logging.warnFP(LOG_NAME, 'onboardUser', user.profile, `User has no email`);
    }
    return true;
  }
  return false;
}

export async function notifyFunction(ps_event, _context) {
  if (!config.configLoaded()) await config.loadConfig();

  const message = ps_event && ps_event.data ? JSON.parse(Buffer.from(ps_event.data, 'base64').toString()) : {};
  logging.infoFP(LOG_NAME, 'notifyFunction', message.profile, `Function called with ${JSON.stringify(message)}`);

  if (message.profile !== undefined && message.profile !== null) {
    let did_init = false;
    let did_onboard = false;
    let user;
    try {
      user = new ForaUser(message.profile as string);
      did_init = await data.users.init(user, false);
      if (did_init) {
        // check for first run
        if (!user.refreshing && !SourceController.firstRunDone(user)) {
          logging.warnFP(LOG_NAME, 'doRefresh', user.profile, `User hasn't first run, restarting`);
          let projects = [];
          const project_cache = await data.users.itemInfoCache(user, [EntityType.Project]);
          if (project_cache && project_cache.length && project_cache[0].cache) projects = project_cache[0].cache;
          else projects = await data.projects.load(user);
          const self = await data.people.getUserPerson(user, user.profile);
          await firstRun(user.profile, self, projects.length > 0, projects.length > 0 ? projects.sort((a,b) => new Date(b.last_update).getTime() - new Date(a.last_update).getTime())[0].id : undefined);
        }

        if ( config.isEnvDevelopment() && (message.notification || message.when)) {
          if(message.notification ) await notify(user, message.notification, NotifyType.PushAndEmail);
          else if ( message.when) await doNotify(user.profile, new Date(), new Date(message.when as string), !!message.force);
        } else {
          did_onboard = await onboardUser(user);
          if (!did_onboard) await doNotify(user.profile, new Date(message.refresh), undefined, !!message.force);
        }
      } else logging.warnFP(LOG_NAME, 'notifyFunction', message.profile as string, `Error sending notification, could not init user`);
    } catch(e) {
      logging.errorFP(LOG_NAME, 'notifyFunction', message.profile as string, `Error sending notification ${JSON.stringify(message)} ${did_init ? 'did_init': ''} ${did_onboard ? 'did_onboard' : ''}`, e);
    }
    user = null;
  } else {
    try {
      const now = new Date();
      let users;
      if (config.isEnvDevelopment() && message.profile) users = [{profile: message.profile as string}];
      else {
        const onboard_users = await data.users.onboarding(ONBOARDING_TEMPLATES);

        const onboard_ids = [];
        logging.infoF(LOG_NAME, 'notifyFunction', `Onboarding ${JSON.stringify(onboard_users.map(o => o.profile))}`);

        for (const ouser of onboard_users) {
          const user = new ForaUser(ouser.profile);
          const did_init = await data.users.init(user, false);
          if (did_init) {
            const did_onboard = await onboardUser(user);
            if (did_onboard) {
              logging.infoF(LOG_NAME, 'notifyFunction', `Onboarded ${user.profile}`);
              onboard_ids.push(user.profile);
            }
          }
        }

        users = await data.users.globalIds(DAYS(now, -30));
        users = users ? users.filter(u => !onboard_ids.includes(u.profile)) : [];
      }

      if (users) {
        for (const user of _.shuffle(users)) {
          const send_notify = {
            profile: user.profile,
            refresh: now.toUTCString(),
            when: config.isEnvDevelopment() ? message.when : undefined,
          };

          logging.infoF(LOG_NAME, 'notifyFunction', `Sending notify command for ${user.profile}`); // at ${next_update}`);

          const did_pub = await internalPub('notify', send_notify);
          if (!did_pub) await notifyFunction({data: Buffer.from(JSON.stringify(send_notify)).toString('base64')}, null);
        }
        users = null;
      }
    } catch (err) {
      logging.errorF(LOG_NAME, 'notifyFunction', 'Error queuing notifications', err);
    }
  }
}

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

  if (req.query.get_keys) {
    // res.set('X-Accel-Buffering', 'no');
    res.json({ 
      vapidPublicKey: config.get('VAPID_PUB'), 
      messagingSenderId: config.get('FIREBASE_SENDER_ID') ,
      projectId: config.get('GOOGLE_CLOUD_PROJECT'),
      apiKey: config.get('WEB_API_KEY'),
      appId: config.get('APP_ID'),
    }).end();
    return;
  }

  if (req.query.sw) {
    res.type('.js');
    res.send(`firebase.initializeApp({
      messagingSenderId:'${config.get('FIREBASE_SENDER_ID')}', 
      projectId:'${config.get('GOOGLE_CLOUD_PROJECT')}',
      apiKey:'${config.get('WEB_API_KEY')}',
      appId:'${config.get('APP_ID')}',
      measurementId: '${config.get('MEASUREMENT_ID')}',
    }); var messaging = firebase.messaging();`).end();
    return;
  }

  if (req.query.messages) {
    const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
    const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
    const dialog: Dialog = await Dialog.loadSession('notify', req.session, req.cookies, { offset, no_cache: true, create_new: false, check_tokens:true, stack:Error().stack});
    if (dialog) {
      try {
        dialog.user.track(req.query as Partial<Tracking>);
        const int_notify: Notification[] = dialog.isAuthenticatedNonGuest() ? await dialog.getInternalNotifications() : [];
        await dialog.saveSession(true);
        res.status(200);
        if (int_notify)  res.send(int_notify);
        else res.send([]);
        res.end();
      } catch(e) {
        logging.errorFP(LOG_NAME, 'get', dialog.user.profile, 'Error checking notifications', e);
        await dialog.saveSession(true).catch(e => dialog.asyncError(e));
        res.status(500).end();
      }
    }
    return;
  }
});

router.post('/', upload.array(), async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  if (req.session && req.cookies && req.body.offset) {
    let push_settings = {};
    let dialog;
    try {
      dialog = await Dialog.loadSession('notify', req.session, req.cookies, {offset:req.body.offset, no_cache: true, create_new: false, check_tokens:true, stack:Error().stack});

      if (dialog) {
        dialog.user.track(req.query as Partial<Tracking>);
        if(dialog.isAuthenticatedNonGuest()) {
          if (req.body.offset) dialog.user.offset = req.body.offset;
          let reg = null;
          if (req.body.data) {
            dialog.user.settings.push = req.body.data.push;
            reg = req.body.data.token;
          }

          if (reg) {
            if (!dialog.user.notify.includes(reg)) dialog.user.notify.push(reg);
            dialog.user.notify = dialog.user.notify.filter(n => !n.keys);
            await data.users.save(dialog.user, false, false, false);
          }

          try { 
            await dialog.saveSettings();
          } catch(err) {
            // TODO(all) - We need to do something to notify the user
            logging.errorF(LOG_NAME, 'post', 'Settings error: %s', err);
            dialog.asyncError(err);
          }

          push_settings = { push: dialog.user.settings.push };
          await dialog.saveSession(true);
        } else await dialog.saveSession();
      } 
    } catch (err) {
      if (dialog) await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      logging.errorF(LOG_NAME, 'post', 'Notify registration error', err);
    }
    res.json(push_settings).end();
  }
});

export async function tutorialNotify(force = false, user_set: Partial<GUser>[] = null, group?: Group): Promise<void> {
  const hours = HOURS(new Date(), -23);

  const all_users = user_set ? user_set : await data.users.globalIds(MONTHS(new Date(), -1));
  const users = force ? all_users : all_users.filter(u => !u.last_notify || new Date(u.last_notify) < hours);

  logging.infoF(LOG_NAME, 'notifyRun', `Checking ${users.length}/${all_users.length}`);

  const guest_user = new ForaUser('tutorial');
  const all_tutorials = await data.tutorials.load(guest_user);

  const default_group = config.isRunningOnGoogle() ? 
    config.isEnvDevelopment() ? 'test.askfora.com' : 'tutor.askfora.com' : 'tutor.askfora.ngrok.io'

  const tgroup = await data.groups.byHost( default_group);
  logging.infoF(LOG_NAME, 'notifyRun', `Default group ${default_group} ${tgroup ? tgroup.id : 'none'}`);

  while(users.length) {
    await Promise.all(users.splice(0, 100).map(async (guser) => {
      const fuser = guser.profile ? new ForaUser(guser.profile) : undefined;
      const did_init = fuser ? await data.users.init(fuser, false) : false;
      if (did_init) {
        await fuser.loadGroupsFromDatastore();
        let ugroup = group;
        if(!ugroup) ugroup = fuser.emailGroup();
        if(!ugroup || !ugroup.signing_secret) {
          ugroup = tgroup;
        }

        if(!ugroup) logging.warnFP(LOG_NAME, 'notifyRun', guser.profile, `No group for user ${guser.profile}`);

        const email = btoa(JSON.stringify({email: guser.email}));
        const params = new URLSearchParams();
        params.set('e', email);

        const notification: Notification = {
          email: {
            rcpts: [{Name: guser.name, Email: guser.email}],
            subject: 'Your AskFora Tutorial',
          },
          type: NotificationType.Tutorial,
          variables: {
            name: guser.name,
            tutorial: 'Your tutorial',
            url: mapURL(`/t`, ugroup),
            question: '',
            question_hint: '',
            tutorial_preview: '',
            answer_1: '',
            answer_2: '',
            answer_3: '',
          }
        }

        const code = AuthProvider.email.signRequest(guser.email, ugroup);
        params.set('code', code);

        const tutorials = await data.tutorials.load(fuser);
        const sorted_tutorials = sortTutorials(tutorials.filter(t => !t.status?.practiced));
        if(sorted_tutorials.length) {
          const tutorial = sorted_tutorials[0];
          if(tutorial.status) {
            if(tutorial.status.assessed?.length === tutorial.lesson_set.length) {
              notification.email.subject = `Practice what you've learned`;
              notification.template = TemplateType.Practice;
              notification.variables.tutorial = tutorial.title;
              params.set('t', tutorial.id);
              if(tutorial.private && tutorial.hosts?.length) {
                const sgroup = await data.groups.byHost(`${tutorial.hosts[0]}.askfora.com`);
                if(sgroup?.signing_secret) {
                  ugroup = sgroup;
                  const signed = AuthProvider.email.signRequest(guser.email, ugroup)
                  params.set('code', signed);
                }
              }
              notification.variables.url = mapURL(`/t?${params.toString()}`, ugroup);
              logging.infoFP(LOG_NAME, 'notifyRun', guser.profile, `Practice tutorial ${tutorial.id} ${tutorial.title}`);
            } else if(!tutorial.status.assessed?.length) {
              notification.email.subject = tutorial.lesson_set[0].assessment.prompts[0].question; //`Start your tutorial`;
              notification.template = TemplateType.Preview; //Ready;
              notification.variables.tutorial = tutorial.title;

              notification.variables.question = tutorial.lesson_set[0].assessment.prompts[0].question;
              notification.variables.question_hint = tutorial.lesson_set[0].outline.title.match(/(?:Part \d+:\s?)?(.*)/)[1];
              notification.variables.tutorial_preview = tutorial.lesson_set[0].outline.text;
              notification.variables.answer_1 = tutorial.lesson_set[0].assessment.prompts[0].answers[0].answer;
              notification.variables.answer_2 = tutorial.lesson_set[0].assessment.prompts[0].answers[1].answer;
              notification.variables.answer_3 = tutorial.lesson_set[0].assessment.prompts[0].answers[2].answer;

              params.set('t', tutorial.id);
              if(tutorial.private && tutorial.hosts?.length) {
                const sgroup = await data.groups.byHost(`${tutorial.hosts[0]}.askfora.com`);
                if(sgroup?.signing_secret) {
                  ugroup = sgroup;
                  const signed = AuthProvider.email.signRequest(guser.email, ugroup)
                  params.set('code', signed);
                  // notification.variables.url = mapURL(`/t?${params.toString()}`, ugroup);
                }
              }
              notification.variables.url = mapURL(`/t?${params.toString()}`, ugroup);
              logging.infoFP(LOG_NAME, 'notifyRun', guser.profile, `Work on tutorial ${tutorial.id} ${tutorial.title}`);
            } else {
              notification.template = TemplateType.Preview; // Reminder;
              notification.variables.tutorial = tutorial.title;

              let index = 0;
              for(let i = 0; i < tutorial.lesson_set.length; i++) {
                if(!tutorial.status?.assessed?.includes(i)) {
                  index = i;
                  break;
                }
              }

              notification.email.subject = tutorial.lesson_set[index].assessment.prompts[0].question; //`Continue your tutorial`;

              notification.variables.question = tutorial.lesson_set[index].assessment.prompts[0].question;
              notification.variables.question_hint = tutorial.lesson_set[index].outline.title.match(/(?:Part \d+:\s?)?(.*)/)[1];
              notification.variables.tutorial_preview = tutorial.lesson_set[index].outline.text;
              notification.variables.answer_1 = tutorial.lesson_set[index].assessment.prompts[0].answers[0].answer;
              notification.variables.answer_2 = tutorial.lesson_set[index].assessment.prompts[0].answers[1].answer;
              notification.variables.answer_3 = tutorial.lesson_set[index].assessment.prompts[0].answers[2].answer;

              params.set('t', tutorial.id);
              if(tutorial.private && tutorial.hosts?.length) {
                const sgroup = await data.groups.byHost(`${tutorial.hosts[0]}.askfora.com`);
                if(sgroup?.signing_secret) {
                  ugroup = sgroup;
                  const signed = AuthProvider.email.signRequest(guser.email, ugroup)
                  params.set('code', signed);
                }
              }
              notification.variables.url = mapURL(`/t?${params.toString()}`, ugroup);
              logging.infoFP(LOG_NAME, 'notifyRun', guser.profile, `Continue tutorial ${tutorial.id} ${tutorial.title}`);
            }
        } else {
          notification.email.subject = tutorial.lesson_set[0].assessment.prompts[0].question; //`Start your tutorial`;
          notification.template = TemplateType.Preview; //Ready;
          notification.variables.tutorial = tutorial.title;

          notification.variables.question = tutorial.lesson_set[0].assessment.prompts[0].question;
          notification.variables.question_hint = tutorial.lesson_set[0].outline.title.match(/(?:Part \d+:\s?)?(.*)/)[1];
          notification.variables.tutorial_preview = tutorial.lesson_set[0].outline.text;
          notification.variables.answer_1 = tutorial.lesson_set[0].assessment.prompts[0].answers[0].answer;
          notification.variables.answer_2 = tutorial.lesson_set[0].assessment.prompts[0].answers[1].answer;
          notification.variables.answer_3 = tutorial.lesson_set[0].assessment.prompts[0].answers[2].answer;

          params.set('t', tutorial.id);
          if(tutorial.private && tutorial.hosts?.length) {
            const sgroup = await data.groups.byHost(`${tutorial.hosts[0]}.askfora.com`);
            if(sgroup?.signing_secret) {
              ugroup = sgroup;
              const signed = AuthProvider.email.signRequest(guser.email, ugroup)
              params.set('code', signed);
            }
          }
          notification.variables.url = mapURL(`/t?${params.toString()}`, ugroup);
          logging.infoFP(LOG_NAME, 'notifyRun', guser.profile, `Start tutorial ${tutorial.id} ${tutorial.title}`);
        }
      } else {
        const tutorial_ids = tutorials.map(t => t.id);
        const new_tutorial = _.shuffle(all_tutorials).find(t => !tutorial_ids.includes(t.id) &&
          (!t.hosts?.length || t.hosts.find(host => fuser.hasHostGroup(host)))
        );
        if(new_tutorial) {
          notification.email.subject = new_tutorial.lesson_set[0].assessment.prompts[0].question; //`Start a new AskFora tutorial`;
          notification.template = TemplateType.Preview; //Suggested;
          notification.variables.tutorial = new_tutorial.title;

          notification.variables.question = new_tutorial.lesson_set[0].assessment.prompts[0].question;
          notification.variables.question_hint = new_tutorial.lesson_set[0].outline.title.match(/(?:Part \d+:\s?)?(.*)/)[1];
          notification.variables.tutorial_preview = new_tutorial.lesson_set[0].outline.text;
          notification.variables.answer_1 = new_tutorial.lesson_set[0].assessment.prompts[0].answers[0].answer;
          notification.variables.answer_2 = new_tutorial.lesson_set[0].assessment.prompts[0].answers[1].answer;
          notification.variables.answer_3 = new_tutorial.lesson_set[0].assessment.prompts[0].answers[2].answer;


          params.set('t', new_tutorial.id);
          if(new_tutorial.private && new_tutorial.hosts?.length) {
            const sgroup = await data.groups.byHost(`${new_tutorial.hosts[0]}.askfora.com`);
            if(sgroup?.signing_secret) {
              ugroup = sgroup;
              const signed = AuthProvider.email.signRequest(guser.email, ugroup)
              params.set('code', signed);
            }
          }
          notification.variables.url = mapURL(`/t?${params.toString()}`, ugroup);
          logging.infoFP(LOG_NAME, 'notifyRun', guser.profile, `New tutorial ${new_tutorial.id} ${new_tutorial.title}`);
        }
      }

      await notify(fuser, notification, NotifyType.EmailOnly, ugroup);
    }
    }));
  }
  logging.infoF(LOG_NAME, 'notifyRun', `Finished notifying ${users.length}/${all_users.length}`);
}

export async function notifyRun(req: express.Request, res: express.Response) {
  logging.infoF(LOG_NAME, 'notifyRun', `Received ${req.originalUrl} ${req.path} ${JSON.stringify(req.body)}`);
  if (!config.configLoaded()) await config.loadConfig();

  await data.dataCheck();

  if(config.isEnvDevelopment() && req.body) {
    const nreq: NotifyRequest  = req.body as NotifyRequest;
    const users = nreq.users ? await data.users.globalByIds(nreq.users) : undefined;
    const group = nreq.group ? await data.groups.byId(nreq.group) : undefined;
    await tutorialNotify(nreq.force, users, group);
  } else await tutorialNotify();
  
  res.status(204).end();
}

export default router;