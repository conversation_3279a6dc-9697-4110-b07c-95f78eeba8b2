import express from 'express';
import 'express-session';

import Dialog, { Topics } from '../session/dialog';

import { Tracking } from '../types/globals';
import { AuthClientContext, AuthContext, AuthContextPriority, AuthProviders } from '../types/shared';

import logging from '../utils/logging';

import { AuthHelper } from '../auth/auth_helper';
import { AuthProvider } from '../auth/auth_provider';

const router = express.Router();
const LOG_NAME = 'routes.GoAuth';

router.post('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog: Dialog = null;
  let [ctx, prv, group_id] = req.query.state ? (req.query.state as string).split(' ') : [`${AuthContext.App}`, null, null];
  let context = ctx as AuthContext;
  let provider = prv as AuthProviders;

  let redirect = req.query.redirect as string;
  if (redirect) {
    if(!redirect.startsWith('/')) {
      try {
        const redirect_url = new URL(redirect);
        if (redirect_url.hostname.endsWith('askfora.com') || redirect_url.hostname === 'askfora.ngrok.io' || redirect_url.hostname === 'askfora-group.ngrok.io') {
          redirect = redirect_url.pathname;
        }
      } catch(e) {
        logging.errorF(LOG_NAME, 'get', `Bad redirect parameter ${redirect}`, e);
        redirect = null;
      } 
    }

    if (redirect) redirect = `/${redirect.split('/').filter(t => t).join('/')}`;
  }

  logging.infoF(LOG_NAME, 'post', `session = ${req.session.id}, context = ${context}, provider = ${provider}, group = ${group_id}, redirect = ${redirect}`);
    
  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
 
  let reload_ok = null;
  try {
    const wait_full_reload = new Promise<void>(c => reload_ok = c);
    dialog = await Dialog.loadSession('goauth', req.session, req.cookies, {
      offset, 
      create_new:true,
      read_only: false,
      check_tokens: false,
      stack:Error().stack,
      wait_full_reload,
    });

    // No dialog
    if (!dialog) return AuthHelper.responseNoDialog(req, res);

    dialog.user.track(req.query as Partial<Tracking>);

    // Get the group for the optionally provided id or URL that the request was made with
    const hostname = req.session ? req.session['hostname'] : req.hostname;
    await dialog.groupsLoad(hostname, true);
    const group = await dialog.groupByIdOrHost(group_id, hostname);

    if (group && !dialog.group_host) dialog.group_host = group;

    // Some errors come back as query.errors
    if (req.query.error) {
      await AuthHelper.responseError(dialog, {provider, context, group}, reload_ok, res, new Error(req.query.error as string));
      return;
    }

    // Handle the response
    if ((provider === null || provider == undefined) && 
      (group === null || group === undefined || group.provider_settings === null || group.provider_settings === undefined)) {
      if (dialog) {
        reload_ok();
        await dialog.saveSession();
      }
      return AuthHelper.responseNoProvider(req, res);
    }

    await AuthHelper.responseHandler(dialog, {provider, context, group}, reload_ok, req, res);
  } catch (err) {
    await AuthHelper.responseError(dialog, {provider, context, group: undefined}, reload_ok, res, err);
  }
});

router.get('/', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog: Dialog = null;
  let [ctx, prv, group_id] = req.query.state ? (req.query.state as string).split(' ') : [`${AuthContext.App}`, null, null];
  let context = ctx as AuthContext;
  let provider = prv as AuthProviders;
  let redirect = req.query.redirect as string;
  if (redirect) {
    if(!redirect.startsWith('/')) {
      try {
        const redirect_url = new URL(redirect);
        if (redirect_url.hostname.endsWith('askfora.com') || redirect_url.hostname === 'askfora.ngrok.io' || redirect_url.hostname === 'askfora-group.ngrok.io') {
          redirect = redirect_url.pathname;
        }
      } catch(e) {
        logging.errorF(LOG_NAME, 'get', `Bad redirect parameter ${redirect}`, e);
        redirect = null;
      } 
    }

    if (redirect) redirect = `/${redirect.split('/').filter(t => t).join('/')}`;
  }

  logging.infoF(LOG_NAME, 'get', `session = ${req.session.id}, context = ${context}, provider = ${provider}, group = ${group_id}, redirect = ${redirect}`);

  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
  const read_only = req.query.get_auth /*|| context == AuthContext.AuthMobile*/ ? true : false; 

  let reload_ok = null;
  try {
    const wait_full_reload = new Promise<void>(c => reload_ok = c);
    dialog = await Dialog.loadSession('goauth', req.session, req.cookies, {
      offset, 
      create_new:true,
      read_only,
      check_tokens:!req.query.code,
      stack:Error().stack,
      wait_full_reload,
    });

    // No dialog
    if (!dialog) return AuthHelper.responseNoDialog(req, res);

    dialog.user.track(req.query as Partial<Tracking>);

    // Get the group for the optionally provided id or URL that the request was made with
    const hostname = req.session ? req.session['hostname'] : req.hostname;
    await dialog.groupsLoad(hostname, true);
    const group = await dialog.groupByIdOrHost(group_id, hostname);

    if (group && !dialog.group_host) dialog.group_host = group;

    // Return available authentication methods (group aware)
    if (req.query.get_auth) {
      if (redirect) dialog.session.redirect = redirect;
      if (dialog.checkTopic(Topics.SETTINGS)) context = AuthContext.Settings;
      else if (dialog.checkTopic(Topics.PROJECT_AUTH)) context = AuthContext.Project;
      else if (dialog.checkTopic(Topics.CONTRACT_AUTH)) context = AuthContext.Contract;
      else if (dialog.checkTopic(Topics.PROJECT_STEPS)) {
        if (dialog.context.project && dialog.context.project.project) {
          if (dialog.context.project.project.expert) context = AuthContext.ProjectExpert;
          else context = AuthContext.ProjectCreate;
        }
      } 
      else if (dialog.checkTopic(Topics.AUTH_INTRO)) context = AuthContext.Intro;
      else if (dialog.checkTopic(Topics.AUTH_CONNECT)) context = AuthContext.Connect;
      // else if (dialog.checkTopic([Topics.PROJECT_EXPERT, Topics.PROJECT_EXPERT_SKILLS])) context = AuthContext.ProjectExpert;

      reload_ok();
      await dialog.saveSession();
      res.status(200);
      if (/*context === AuthContext.AuthMobile ||*/ !provider || !provider.length) {
        let auth_set: AuthClientContext[] = [];
        for (const c of context.split(',') as AuthContext[]) {
          let c_auth_set = AuthProvider.clientAuthSet(c, group)
          for (const ast of c_auth_set) {
            const has_auth = auth_set.find(f => f.provider === ast.provider);
            if (has_auth) {
              if (AuthContextPriority[has_auth.context] >= AuthContextPriority[ast.context]) {
                c_auth_set = c_auth_set.filter(f => f.provider !== ast.provider);    
              } else {
                auth_set = auth_set.filter(f => f.provider !== has_auth.provider);    
              }
            }
          }
          auth_set = auth_set.concat(c_auth_set);
        }
        res.json(auth_set);
      } else if (AuthProvider.isSupportedContext(provider, context.split(',')[0] as AuthContext)) res.json(AuthProvider.clientAuthContext(context, provider, group));
      else res.json();
      res.end();
      return;
    }

    // Some errors come back as query.errors
    if (req.query.error) {
      await AuthHelper.responseError(dialog, {provider, context, group}, reload_ok, res, new Error(req.query.error as string));
      return;
    }

    // Handle the response
    if ((provider === null || provider == undefined) && 
      (group === null || group === undefined || group.provider_settings === null || group.provider_settings === undefined)) {
      if (dialog) {
        reload_ok();
        await dialog.saveSession();
      }
      return AuthHelper.responseNoProvider(req, res);
    }

    await AuthHelper.responseHandler(dialog, {provider, context, group}, reload_ok, req, res);
  } catch (err) {
    await AuthHelper.responseError(dialog, {provider, context, group: undefined}, reload_ok, res, err);
  }
});

export default router;
