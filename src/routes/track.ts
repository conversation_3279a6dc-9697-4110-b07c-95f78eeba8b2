import express from 'express';
import 'express-session';
import Dialog from '../session/dialog';
import { Tracking } from '../types/globals';
import logging from '../utils/logging';

const router = express.Router();

const LOG_NAME = 'routes.track';

router.get('/', async (req: express.Request, res: express.Response, _next) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  let dialog: Dialog;
  try {
    const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
    const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
    dialog = await Dialog.loadSession('track', req.session, req.cookies, {offset, create_new:true, read_only:false, check_tokens:true, stack:Error().stack}); // .then(dialog => {
    dialog.user.track(req.query as Partial<Tracking>);
  } catch(err) {
    logging.errorF(LOG_NAME, 'get', 'Track error', err);
  }
  if (dialog) await dialog.saveSession().catch(e => dialog.asyncError(e));
  res.status(204).end();
});

export default router;