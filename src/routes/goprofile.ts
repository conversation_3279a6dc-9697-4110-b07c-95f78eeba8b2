import express from 'express';
import 'express-session';

import config from '../config';
import data from '../data';

import Dialog from '../session/dialog';
import ForaUser from '../session/user';

import { NormalizedProviderSettings } from '../types/auth';
import { OAuthConfig } from '../types/oauth';
import { AuthProviders } from '../types/shared';

import { DAYS } from '../utils/datetime';
import logging from '../utils/logging';

const router = express.Router();
const LOG_NAME = 'routes.goprofile';

router.get('/', async (req: express.Request, res: express.Response) => {
  let path;
  if (!config.isRunningOnGoogle() && process.env.AUTH_PROFILE) {
    const profile = req.query.profile as string;
    path = req.query.path as string;
    logging.warnFP(LOG_NAME, 'get', profile, `associating session ${req.session.id} with profile`);
    req.session['profile'] = profile;
    const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
    const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;

    const dialog: Dialog = await Dialog.loadSession('goprofile', req.session, req.cookies, {offset, create_new:true, check_tokens:true, stack:Error().stack});
    await dialog.groupsLoad(req.session['hostname']);
    // if (dialog.group_host) dialog.user.loadGroups([dialog.group_host]);

    if (req.query.init) {
      dialog.user = new ForaUser(profile);
      dialog.user.setTokens({ expires_at: DAYS(new Date(), 1)}, AuthProviders.Offline );
      if (!(await data.users.init(dialog.user, false, true))) throw new Error('Unable to initialize user');
      const self = await data.people.getUserPerson(dialog.user);
      const provider_settings = new NormalizedProviderSettings(AuthProviders.Offline, new OAuthConfig('OFFLINE_CLIENT_ID', 'OFFLINE_CLIENT_SECRET'));
      await dialog.initUser(self, provider_settings.provider, 'offline');
    }

    await dialog.saveSession();
  }
  res.redirect(`/${path ? path : 'app'}`);
});

export default router;
