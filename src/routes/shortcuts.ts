import express from 'express';
import 'express-session';
import * as plugins from '../plugins';
import Dialog from '../session/dialog';
import { Tracking } from '../types/globals';
import { Plugin } from '../types/plugins';
const router = express.Router();

// translates plain text to {message:,entities:[{name:,type:}]}
router.get('/', async (req: express.Request, res: express.Response, next: any) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));
  const q_offset = req.query.offset ? parseInt(req.query.offset as string, 10) : null; 
  const offset = q_offset && !isNaN(q_offset) ? q_offset : 0;
  const dialog: Dialog = await Dialog.loadSession('shortcuts', req.session, req.cookies, {offset, create_new:true, read_only:true, no_cache: true, check_tokens:true, stack:Error().stack});
  let auth = false;
  if (dialog) {
    dialog.user.track(req.query as Partial<Tracking>);
    if(dialog.isAuthenticated()) auth = true;
  }

  const shortcuts = [];

  for (const p of Object.values(plugins)) {
    const plugin = p as Plugin;
    if (plugin.shortcuts) {
      const s = plugin.shortcuts(auth);
      if (s) shortcuts.push(s);
    }
  }

  res.status(200);
  res.json(shortcuts);
});

export default router;
