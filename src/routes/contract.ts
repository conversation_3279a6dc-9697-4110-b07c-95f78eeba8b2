import express from 'express';
import 'express-session';
import fs from 'fs';
import path from 'path';
import Dialog, { Topics } from '../session/dialog';
import { Tracking } from '../types/globals';
import { ContractPluginState } from '../types/plugins';
import logging from '../utils/logging';

const router = express.Router();
const LOG_NAME = 'routes.contract';

router.get('/:contract_id?', async (req: express.Request, res: express.Response) => {
  if (!req.cookies.GOOGAPPUID) res.cookie('GOOGAPPUID', Math.floor(Math.random() * 1000));

  const ids = Object.keys(req.query);
  let contract_id = null;
  if (ids.length) contract_id = ids[0];
  else contract_id = req.params.contract_id;

  if (contract_id) {
    if(['error', 'signed', 'declined'].includes(contract_id)) res.status(204).end();
    else {
      let dialog: Dialog = null;

      try {
        dialog = await Dialog.loadSession('contract', req.session, req.cookies, {create_new:true, check_tokens: true, stack:Error().stack});
        const doc = await dialog.contracts.load(contract_id);

        dialog.user.track(req.query as Partial<Tracking>);

        dialog.newDialog();
        if (doc) {
          try {
            dialog.context.contract = {
              contract_id: doc.id,
              doc,
              loading: true,
            } as ContractPluginState;

            if (doc.client_signed && doc.contractor_signed) dialog.setTopic(Topics.CONTRACT_LOAD);
            else if (dialog.isAuthenticatedNonGuest()) dialog.setTopic(Topics.CONTRACT_SIGN);
            else dialog.setTopic(Topics.CONTRACT_AUTH);
          } catch (err) {
            logging.errorFP(LOG_NAME, 'get.contract', dialog.user.profile, 'Error setting up contract', err);
            dialog.setTopic(Topics.CONTRACT_ERROR);
          }
        } else dialog.setTopic(Topics.CONTRACT_MISMATCH);
        await dialog.saveSession();

        const redirect_page = fs.readFileSync(path.resolve(__dirname, '..', 'files', 'redirect.html'), 'utf-8');
        res.send(redirect_page).end();
      } catch(e) {
        logging.errorFP(LOG_NAME, 'get', dialog ? dialog.user.profile : null, `Error loading contract ${contract_id}`, e);
        if (dialog) await dialog.saveSession();
        res.status(500).end();
      }
    }
    return;
  }

  res.redirect('/');
});

export default router;
