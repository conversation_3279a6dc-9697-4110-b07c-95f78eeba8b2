/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import 'express-session';
import fs from 'fs';
import { Profile } from 'passport-saml';
import path from 'path';
import util from 'util';

import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../errors/error_handler';

import Dialog, { Topics } from '../session/dialog';
import * as SessionCache from '../session/session_cache';
import ForaUser from '../session/user';
import { FORA_GUEST_ID, FORA_PROFILE } from '../types/user';

import { LoginContext, NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { TemplateType } from '../types/globals';
import { Person, Project, projectPerson } from '../types/items';
import { OAuthConfig } from '../types/oauth';
import { InitPluginState } from '../types/plugins';
import { ANONYMOUS_ID, AuthContext, AuthLevel, AuthProviders, EntityType, ViewProjects } from '../types/shared';

import { DAYS, HOURS } from '../utils/datetime';
import { makeIdMap, mapURL, mergeTags } from '../utils/funcs';
import logging from '../utils/logging';
import mail from '../utils/mail';
import peopleUtils from '../utils/people';

import config from '../config';
import data from '../data';

import { onboardUser } from '../routes/notify';
import { firstRun, internalUpdate } from '../routes/update';

import { SourceController } from '../sources/source_controller';

import { AuthProvider } from './auth_provider';

const LOG_NAME = 'auth.AuthHelper';

export class AuthHelper {

  static async responseError(dialog: Dialog, lc: LoginContext, reload_ok: any, res: express.Response, err: Error) {
    logging.errorF(LOG_NAME, 'get', `Error during provider auth ${lc.provider}`, err);
    let path = "/";

    if (dialog) {
      if (dialog.session.seed)  delete dialog.session.seed;
      if (dialog.session.factor) delete dialog.session.factor;
      if (dialog.context.init) delete dialog.context.init;
      if (dialog.context.email_login) delete dialog.context.email_login;
    }

    let clear_user = false;
    if (err.message === 'Unable to initialize user') {
      // Go back to anonymous session
      if (dialog && dialog.user && dialog.user.profile !== FORA_PROFILE && (!dialog.user.provider || lc.provider === dialog.user.provider)) {
        SessionCache.logoutSessions(dialog.user).catch(err => logging.errorFP(LOG_NAME, 'get', dialog.user.profile, 'Error logging out sessions', err));
        clear_user = true;
      }
    }

    // Microsoft Error Example
    // http://localhost:8000/goauth?error=access_denied&error_description=The%20user%20has%20denied%20access%20to%20the%20scope%20requested%20by%20the%20client%20application.&state=chat%20microsoft
    if (reload_ok) reload_ok();

    if (dialog) {
      if (ErrorHandler.isOAuthDeniedError(err)) {
        dialog.context.init = { provider: lc.provider } as InitPluginState;
        switch (lc.context) {
          case AuthContext.Project:
          case AuthContext.ProjectExpert:
          case AuthContext.ProjectCreate:
            dialog.setTopic(Topics.PROJECT_AUTH_CANCELLED);
            break;
          case AuthContext.Contract:
            dialog.setTopic(Topics.CONTRACT_AUTH_CANCELLED);
            break;
          case AuthContext.Contractor:
            dialog.setTopic(Topics.CONTRACTOR_AUTH_CANCELLED);
            break;
          case AuthContext.AuthNoOrganizer:
            dialog.setTopic(Topics.REG_ERROR_NO_ORGANIZER_CANCELLED);
            break;
          case AuthContext.AuthNoEmail:
          case AuthContext.AuthSyncOrganizer:
            dialog.setTopic(Topics.REG_ERROR_NO_EMAIL_CANCELLED);
            break;
          case AuthContext.Learn:
          case AuthContext.Webinar:
          case AuthContext.Info:
          case AuthContext.Apply:
            break;
          case AuthContext.Signup:
          case AuthContext.Breadwinners:
          case AuthContext.App:
            dialog.setTopic(Topics.REG_ERROR_CANCELLED);
            path = '/app';
            break;
          case AuthContext.AuthChat:
            dialog.setTopic(Topics.REG_ERROR_CANCELLED);
            break;
          case AuthContext.Network:
            path = '/network';
            dialog.reset();
            break;
          default:
            dialog.setTopic(Topics.REG_ERROR);
            break;
        }
      } else if (ErrorHandler.isOAuthConfigurationError(err) || (dialog.user.provider && dialog.user.provider !== lc.provider)) {
        logging.errorFP(LOG_NAME, 'responseError', dialog.user ? dialog.user.profile : null, 'Error in auth, logging out', err);
        dialog.setTopic(Topics.REG_ERROR);
        if (dialog.user) {
          await SessionCache.releaseSession(dialog.user.profile, dialog.session.id, Error().stack);
          await SessionCache.logoutSessions(dialog.user);
        }
        await dialog.deleteSession (false);
        clear_user = true;
      } else {
        // TODO - Placeholder until we can find a list of possible error returns
        if (lc.context === AuthContext.AuthNoEmail) dialog.setTopic(Topics.REG_ERROR_NO_EMAIL);
        else dialog.setTopic(Topics.REG_ERROR);
        if (dialog.user) {
          await SessionCache.releaseSession(dialog.user.profile, dialog.session.id, Error().stack);
          await SessionCache.logoutSessions(dialog.user);
        }
        await dialog.deleteSession (false);
        clear_user = true;
      }
      
      if (clear_user) dialog.user = new ForaUser(FORA_PROFILE);
      await dialog.saveSession();
    }

    res.status(403);
    res.send(`<html lang="en"><head><script>setTimeout(() => { window.location = "${path}" }, 1000)</script><title>redirect</title></head></html>`).end();
  }

  static async responseHandler(dialog: Dialog, lc: LoginContext, reload_ok: any, req: express.Request, res: express.Response): Promise<void> {
    // Always update our init context
    dialog.context.init = { provider: lc.provider, auth_context: lc.context } as InitPluginState;

    const host = lc.group ? (lc.group.redirect ? `${lc.group.redirect}` : lc.group.host ? `${lc.group.host}` : '') : config.get('DEFAULT_HOST', 'localhost');
    lc.hostname = `https://${host}`;
    lc.redirect = `${lc.hostname}/`;
    lc.settings = `/api/settings`;

    let idToken: string = req.body ? req.body.credential : null;

    if (config.isEnvOffline()) {
      if (req.query.profile) {
        const id = req.query.profile as string;
        const user = new ForaUser(id);

        // force offline if offline
        // if(dialog.user.provider === AuthProviders.Offline) 
        lc.provider = AuthProviders.Offline;

        const global_user = await data.users.globalById(id);
        if (global_user) {
          user.name = global_user.name;
          user.email = global_user.email;
        } else if(req.query.name && req.query.email) {
          logging.warnFP(LOG_NAME, 'responseHandler', id, `Creating new user`);
          user.name = req.query.name as string;
          user.email = req.query.email as string;
        } else {
          logging.warnFP(LOG_NAME, 'responseHandler', id, `No global user found`);
        }

        if (!(await data.users.init(user, false))) throw new Error('Unable to initialize user');

        // We need to set a temporary token so the call below to `getAuthRequiredScope` will succeed
        user.tokens = { expires_at: DAYS(new Date(), 1) };

        lc.self = await data.people.byId(user, `people/t${user.id}`);
        if (lc.self) lc.self.self = true;
        else {
          lc.self = new Person({displayName: user.name, id: `people/t${user.id}`, comms: [user.email], self: true});
          logging.warnFP(LOG_NAME, 'responseHandler', id, `Creating new self ${lc.self.id}`);
          await data.people.save(user, lc.self);
        }

        lc.tokens = new NormalizedProviderToken(AuthProviders.Offline, 'offline', AuthLevel.Organizer);
        lc.tokens.expires_at = HOURS(new Date(), 1);
        lc.tokens.id_token = 'offline';
        lc.tokens.refresh_token = 'offline';
        lc.tokens.scope = AuthProvider.offline.getAuthRequiredScope(user, AuthProviders.Offline, id).join(' ');
        lc.tokens.token_type = 'Bearer';
        if(logging.isDebug()) logging.debugF(LOG_NAME, 'responseHandler', `tokens = ${logging.formatToken(lc.tokens)}`);

        lc.n_user = new NormalizedProviderUser(id, user.email, user.name);
        if(logging.isDebug()) logging.debugF(LOG_NAME, 'responseHandler', `offline results = ${util.format(lc.n_user)}`);

        lc.provider_settings = new NormalizedProviderSettings(AuthProviders.Offline, new OAuthConfig('OFFLINE_CLIENT_ID', 'OFFLINE_CLIENT_SECRET'));
        if(logging.isDebug()) logging.debugF(LOG_NAME, 'responseHandler', `offline provider_settings = ${util.format(lc.provider_settings)}`);
      }   
    } else {
      const default_settings = AuthProvider.getDefaultProviderSettings(lc.provider);
      lc.provider_settings = lc.group ? lc.group.providerSettings(lc.provider) : default_settings;
      if (!lc.provider_settings) lc.provider_settings = default_settings;
      const provider_set = AuthProvider.clientAuthSet(lc.context, lc.group).find(s => s.provider === lc.provider);
      switch (lc.provider) {
        case AuthProviders.Google:
          if (req.query.code) lc.tokens = await AuthProvider.tokensFromOauthCode(req.query.code as string, lc.provider_settings, provider_set && provider_set.scope ? provider_set.scope.split(' ') : undefined);
          else if(idToken) lc.n_user = await AuthProvider.getJWTProviderUser(idToken, lc.provider_settings);
          break;
        case AuthProviders.Microsoft:
        case AuthProviders.Msal:
        case AuthProviders.Okta:
        case AuthProviders.Offline:
        case AuthProviders.Slack:
          lc.tokens = await AuthProvider.tokensFromOauthCode(req.query.code as string, lc.provider_settings, provider_set && provider_set.scope ? provider_set.scope.split(' ') : undefined);
          break;
        case AuthProviders.Email:
          lc.tokens = AuthProvider.email.tokensFromCode(parseInt(req.query.code as string, 10), dialog.user.email ? dialog.user.email : req.query.email as string, dialog.session.seed, dialog.session.factor, lc.group);
          if (lc.tokens !== null) {
            if (dialog.session.seed)  delete dialog.session.seed;
            if (dialog.session.factor) delete dialog.session.factor;
          }
          break;
        case AuthProviders.Saml:
          lc.tokens = AuthProvider.saml.tokensFromUser(req.user as Profile);
          break;
      }

      if (lc.tokens) lc.n_user = await AuthProvider.getProviderUser(lc.tokens, lc.provider_settings, lc.group, req);
      if(logging.isDebug()) logging.debugFP(LOG_NAME, 'responseHandler', lc.n_user ? lc.n_user.id : null, `Auth provider ${lc.provider} tokens ${JSON.stringify(lc.tokens)}}`);
    }

    if (lc.n_user) AuthHelper.userPrepare(dialog, lc, req, res);
    else if(lc.tokens) {
      // if no user, put the tokens on the group if tokens are valid and user is an admin
      if (dialog.user.isAuthenticated() && lc.group && dialog.user.isAdmin(lc.group.id) && AuthProvider.isValid(lc.tokens, false)) {
        AuthProvider.setGroupTokens(lc.provider, lc.group, lc.tokens);
        await data.groups.save(lc.group);
        const topic = AuthProvider.connectedTopic(lc.provider, lc.context, lc.group, dialog.user);
        dialog.setTopic(topic);
      } else {
        logging.warnFP(LOG_NAME, 'responseHandler', dialog.user ? dialog.user.profile : null, `No user or group tkens`);
        dialog.setTopic(Topics.HELP_CONNECT);
      }

      if (dialog.session.seed)  delete dialog.session.seed;
      if (dialog.session.factor) delete dialog.session.factor;
      if (dialog.context.init) delete dialog.context.init;
      if (dialog.context.email_login) delete dialog.context.email_login;

      await dialog.saveSession(true);
      AuthHelper.responseSuccess(dialog, lc, req, res);
    } else {
      await dialog.saveSession(true);
      res.redirect('/');
    }

    if (reload_ok) reload_ok();
  }

  static async userPrepare(
    dialog: Dialog, 
    lc: LoginContext, 
    req: express.Request,
    res: express.Response) {

    let re_auth_needed = false;
    let user_provider_id;
    let user_provider_email;

    user_provider_id = lc.n_user.id;
    user_provider_email = lc.n_user.email;
    let updated_tokens = lc.tokens;

    // check that email is unique or id matches an account
    let global_user = await data.users.globalByAccount(lc.provider, user_provider_id);

    // upgrade microsoft accounts
    if (!global_user && lc.provider === AuthProviders.Msal) global_user = await data.users.globalByAccount(AuthProviders.Microsoft, user_provider_id);

    const email_users = await data.users.globalByEmail(user_provider_email);
    const email_user = email_users?.length ? email_users[0] : null;

    if (global_user && email_user && global_user.id !== email_user.id) {
      // global and email user don't match, can't connect them
      logging.warnFP(LOG_NAME, 'responseHandler', user_provider_id, `Global ${global_user.id} and email user ${email_user.id} don't match`);
      dialog.setTopic(Topics.HELP_MERGE);
      await dialog.saveSession();
      switch(lc.context) {
        case AuthContext.Signup: 
          /*lc.redirect = `${lc.hostname}/signup`;
          break;*/
        case AuthContext.Breadwinners:
          /*lc.redirect = `${lc.hostname}/breadwinners`;
          break;*/
        case AuthContext.App: 
          lc.redirect = `${lc.hostname}/app`;
          break;
        case AuthContext.Learn: 
          lc.redirect = `${lc.hostname}/learn`;
          break;
        case AuthContext.Webinar: 
          lc.redirect = `${lc.hostname}/webinar`;
          break;
        case AuthContext.Info: 
          lc.redirect = `${lc.hostname}/info`;
          break;
        case AuthContext.Apply: 
          lc.redirect = `${lc.hostname}/apply`;
          break;
      }
      AuthHelper.responseSuccess(dialog, lc, req, res);
    } else {
      let merge = false;
      if(dialog.isAuthenticatedNonGuest()) {
        // make sure authenticated user ids or emails match
        if (global_user && email_user && global_user.id === email_user.id) {
          if (global_user.id !== dialog.user.profile) {
            // TODO: can't currently merge two users
            logging.warnFP(LOG_NAME, 'responseHandler', user_provider_id, `Global ${global_user.id} and current user ${dialog.user.profile} don't match even though email user does`);
            merge = true;
          } else {
            logging.infoFP(LOG_NAME, 'responseHandler', user_provider_id, `Global ${global_user.id} and current user ${dialog.user.profile} match`);
            // everything matches, proceed
          }
        } else if(global_user && !email_user) {
          // global_user with a new email
          if (global_user.id !== dialog.user.profile) {
            // TODO: can't currently merge two users
            logging.warnFP(LOG_NAME, 'responseHandler', user_provider_id, `Global ${global_user.id} and current user ${dialog.user.profile} don't match with no email user`);
            merge = true;
          } else {
            // everything matches, proceed
            logging.infoFP(LOG_NAME, 'responseHandler', user_provider_id, `Global ${global_user.id} and current user ${dialog.user.profile} match with no email user`);
          }
        } else if(!global_user && email_user) {
          // email_user with a new id - ok to add to account
          if (email_user.id !== dialog.user.profile) {
              logging.warnFP(LOG_NAME, 'responseHandler', user_provider_id, `Email ${email_user.id} and current user ${dialog.user.profile} don't match with no global user`);
            // TODO: can't currently merge two users
            merge = true;
          } else {
            // everything matches, proceed
            logging.infoFP(LOG_NAME, 'responseHandler', user_provider_id, `Email ${email_user.id} and current user ${dialog.user.profile} match with no global user`);
          }
        }

        if (!merge) {
          // new user and/or new email, add to this account, 
          // secondary account
          logging.infoFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Updating additional tokens for ${lc.provider} primary ${dialog.user.provider}`);

          // if there's a new user id, it'll just get added to auth_ids
          if (lc.tokens) dialog.user.setTokens(lc.tokens, lc.provider, user_provider_id, lc.group);

          // if there's a new email, we need to map it
          if (user_provider_email !== dialog.user.email) await data.users.mapUserEmail(dialog.user, user_provider_email);

          updated_tokens = dialog.user.getTokens(lc.provider, user_provider_id);

          // merge self
          if (AuthProvider.isValid(updated_tokens)) {
            logging.warnFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Merging secondary account self`);
            let new_self = await AuthProvider.getProviderUserSelf(updated_tokens, lc.n_user, lc.provider_settings, req);
            if (dialog.me) {
              const self = new Person(dialog.me);
              if (new_self) peopleUtils.mergePeople(self, new_self, true);
              new_self = self;
            }
            dialog.setSelf(new_self, true);

            // make sure tokens are saved
            await data.users.saveBoth(dialog.user, true);
          } else {
            logging.warnFP(LOG_NAME, 'responseHandler', dialog.user.profile, `User has no self or tokens are invalid`);
          }
        }

        // don't init a new user
        lc.existing_user = true;
      } else if (!dialog.isAuthenticated() || dialog.isGuestAccount()) {
        /// New Login
        const tracking = dialog.user.tracking;
        if (!global_user && !email_user) {
          logging.infoFP(LOG_NAME, 'responseHandler', user_provider_id, `New user with provider ${lc.provider}`);
          // New user
          lc.new_login = true;
          // lookup global user by auth_id
          // fall back to provider id
          dialog.user = new ForaUser(user_provider_id, lc.provider);
          dialog.user.name = lc.n_user.given_name;
          dialog.user.email = user_provider_email;
          if (lc.tokens) {
            lc.tokens.default = true;
            await data.users.init(dialog.user, true, true, lc.tokens);
          }
        } else if (email_user && (
          (global_user && global_user.id === email_user.id) ||
          (!global_user && lc.n_user.id === email_user.id))) {
          logging.infoFP(LOG_NAME, 'responseHandler', user_provider_id, `Returning user with provider ${lc.provider}`);
          // Returning user with the same account
          const global_user_account = global_user && global_user.auth_ids ? global_user.auth_ids.find(a => a.endsWith(`_${global_user.id}`)) : null;
          const global_user_provider = global_user_account ? global_user_account.split('_')[0] as AuthProviders : null
          dialog.user = global_user_provider ? new ForaUser(global_user.id, global_user_provider ) : new ForaUser(email_user.id, lc.provider);
          dialog.user.name = lc.n_user.given_name;
          dialog.user.email = user_provider_email;
          const did_init = await data.users.init(dialog.user, true, true);

          // set tokens for provider, which might be secondary
          if (lc.tokens) dialog.user.setTokens(lc.tokens, lc.provider, user_provider_id, lc.group);

          // re-try init with updated tokens
          if (!did_init) await data.users.init(dialog.user, true, true);

          // remove old microsoft tokens
          if (lc.provider === AuthProviders.Msal && dialog.user.hasAccount(AuthProviders.Microsoft, user_provider_id)) {
            logging.warnFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Upgrading account to MSAL`);
            dialog.user.deleteAccount(AuthProviders.Microsoft, user_provider_id);
          }
        } else if (!global_user && email_user && lc.provider === AuthProviders.Email) {
          logging.infoFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Returning email user with no global user`);
          // if there's no global user but an email user, allow if it's an email provider login
          dialog.user = new ForaUser(email_user.id);
          dialog.user.name = email_user.name;
          dialog.user.email = user_provider_email;
          await data.users.init(dialog.user, true, true);

          // switch to the default provider
          lc.provider = dialog.user.provider;
          const default_settings = AuthProvider.getDefaultProviderSettings(lc.provider);
          lc.provider_settings = lc.group ? lc.group.providerSettings(lc.provider) : default_settings;
          if (!lc.provider_settings) lc.provider_settings = default_settings;

          // use default profile
          user_provider_id = dialog.user.profile;
          lc.n_user = new NormalizedProviderUser(dialog.user.profile, dialog.user.email, dialog.user.name);
        } else {
          // mismatched returning user
          // if there's a global user and no email user, the email has changed
          lc.redirect = `${lc.hostname}/app/login`
          logging.warnFP(LOG_NAME, 'responseHandler', user_provider_id, `Returning global user ${global_user ? global_user.id : undefined} and no email user or email use doesn't match`);
          merge = true;
        }

        if (!merge) {
          logging.infoFP(LOG_NAME, 'responseHandler', user_provider_id, `Setting tokens and initialing user with provider ${lc.provider}`);
          dialog.user.track(tracking, lc.context);
          updated_tokens = dialog.user.getTokens(lc.provider, user_provider_id);

          if (AuthProvider.isValid(updated_tokens)) {
            if (!config.isEnvOffline()) {
              lc.self = await data.people.getUserPerson(dialog.user);
              if (!lc.self) lc.self = await AuthProvider.getProviderUserSelf(updated_tokens, lc.n_user, lc.provider_settings, req);
            }
            lc.existing_user = await dialog.initUser(lc.self, lc.provider_settings.provider, lc.context, lc.group);
          } else logging.warnFP(LOG_NAME, 'responseHandler', user_provider_id, `Updated tokens are not valid`);
        }
      } else {
        throw new Error('How did I get here?');
      }

      if (!merge) {
        let scope_redirect = AuthProvider.checkScopesForReAuth(dialog.user, lc.provider, user_provider_id, updated_tokens ? updated_tokens.scope : '', lc.context, lc.group);
        if (scope_redirect) {
          logging.infoFP(LOG_NAME, 'responseHandler', user_provider_id, `Upgrade permissions, redirect scope: ${scope_redirect}`);
          lc.redirect = scope_redirect;
          lc.settings = ''; // don't fetch settings before redirecting
          re_auth_needed = true;
        } else if (!updated_tokens || !AuthProvider.isValid(updated_tokens)) {
          // const provider_settings: NormalizedProviderSettings = group && group.provider_settings ? group.provider_settings : AuthProvider.getDefaultProviderSettings(provider);
          lc.redirect =  AuthProvider.clientAuthUrl(lc.context, lc.group, lc.provider_settings, user_provider_email, true);
          logging.infoFP(LOG_NAME, 'responseHandler', user_provider_id, `Invalid tokens, redirect scope: ${lc.redirect}`);
          try { 
            await AuthProvider.revokeToken(lc.provider, user_provider_id, updated_tokens);
          } catch(e) {
            logging.errorFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Error revoking tokens, trying to log in again`, e);
          }
          lc.settings = ''; // don't fetch settings before redirecting
          re_auth_needed = true;
        }

        if (!re_auth_needed) {
          if (dialog.session.seed)  delete dialog.session.seed;
          if (dialog.session.factor) delete dialog.session.factor;
          if (dialog.context.init) delete dialog.context.init;
          if (dialog.context.email_login) delete dialog.context.email_login;

          await AuthHelper.responsePrepare( dialog, lc, req, res);
        } else {
          // force save
          await dialog.saveSession(true);
          AuthHelper.responseSuccess(dialog, lc, req, res);
        }
      } else {
        if (dialog.session.seed)  delete dialog.session.seed;
        if (dialog.session.factor) delete dialog.session.factor;
        if (dialog.context.email_login) delete dialog.context.email_login;

        if (dialog.isAuthenticatedNonGuest()) {
          if (dialog.context.init && dialog.context.init.provider) delete dialog.context.init.provider;
          dialog.setTopic(Topics.HELP_MERGE);
        } else {
          if(dialog.context.init) {
            dialog.context.init.provider = lc.provider;
            dialog.context.init.existing = email_user && email_user.auth_ids  && email_user.auth_ids.length ? email_user.auth_ids[0].split('_')[0] as AuthProviders : null;
          } else {
            dialog.context.init = {
              provider: lc.provider,
              existing: email_user && email_user.auth_ids  && email_user.auth_ids.length ? email_user.auth_ids[0].split('_')[0] as AuthProviders : null,
            }
          }
          dialog.setTopic(Topics.REG_ERROR_NO_PROVIDER);
        }
        await dialog.saveSession(true);
        AuthHelper.responseSuccess(dialog, lc, req, res);
      }
    }
  }

  static async responsePrepare( dialog: Dialog, lc: LoginContext, req: express.Request, res: express.Response) {
    let do_notify = false;
    let do_search = false;
    let project = null;
    let merge_context = null;

    // Need to keep the project and context
    const projects = Object.values(dialog.cache.projects).filter(p => p.id !== 'fora_internal_0');
    const project_context = dialog.context.project;
    const contractor_context = dialog.context.contractor;

    await dialog.cache.loadCache();

    // Show tutorial for new users
    if (!lc.existing_user || config.isEnvOffline()) {
      if (lc.existing_user) dialog.setTopic(Topics.DEFAULT);
      else {
        dialog.setTopic(Topics.TUTORIAL);
        // if (!config.isEnvOffline()) mailTemplate([{Name:dialog.me.displayName, Email: dialog.user.email}], [], NotificationType.Survey, null, {firstname: dialog.me.displayName.split(' ')[0]});
      }
    } else {
      // logging.infoFP(LOG_NAME, 'responseHandler', dialog.user.profile, 'Reloading data for existing user');
      // await dialog.reloadData(null, true).catch(err => dialog.asyncError(err));
      dialog.setTopic(Topics.DEFAULT);
    }

    const profile = mapURL(`/profile/${dialog.user.vanity}`, lc.group);

    if (!lc.existing_user || /* config.isEnvDevelopment() || */ config.isEnvOffline()) {
      const user_info = `${dialog.user.name} <${dialog.user.email}> ${profile} joined ${lc.group ? lc.group.name : ''} by ${lc.context}`
      mail([{
        Email: '<EMAIL>',
        Name: 'Fora',
      }], [], `${dialog.user.name} just Joined!`, user_info);
    }

    // check for a group that has redirect
    const group_redirect = dialog.user.groupRedirect();
    if (group_redirect) {
      lc.hostname = `https://${group_redirect }`;
      lc.redirect = `${lc.hostname}/`;
    }

    // Redirect topic and save session
    switch (lc.context) {
      case AuthContext.Learn:
        if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        else lc.redirect = `${lc.hostname}/learn`;
        break;
      case AuthContext.Webinar:
        if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        else lc.redirect = `${lc.hostname}/webinar`;
        break;
      case AuthContext.Info:
        if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        else lc.redirect = `${lc.hostname}/info`;
        break;
      case AuthContext.Apply:
        if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        else lc.redirect = `${lc.hostname}/apply`;
        break;

      case AuthContext.Signup:
        /*if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        else lc.redirect = `${lc.hostname}/signup`;
        break;*/
      case AuthContext.Breadwinners:
        /*if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        else lc.redirect = `${lc.hostname}/breadwinners`;
        break;*/
      case AuthContext.App:
        if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        else lc.redirect = `${lc.hostname}/app`;
        /*if (!existing_user || config.isEnvOffline()) {
          await data.users.onboardingSet(dialog.user, TemplateType.ContractorSurvey);
          do_notify = true;
        }*/

        if (!lc.existing_user && lc.new_login) dialog.setTopic(Topics.CONNECT_TUTORIAL);

        // make sure there's no project or contractor carry over if it's not in the context
        dialog.clearContext('contractor');
        dialog.clearContext('project');
        break;
 
      case AuthContext.Contract:
        dialog.setTopic(Topics.CONTRACT_MISMATCH);
        if (dialog.context.contract && dialog.context.contract.doc) {
          const doc = dialog.context.contract.doc;
          if (doc.client_id === dialog.user.profile || doc.contractor_id === dialog.user.profile) {
            if (doc.client_signed && doc.contractor_signed) dialog.setTopic(Topics.CONTRACT_LOAD);
            else dialog.setTopic(Topics.CONTRACT_SIGN);
          }
          await dialog.safeSaveSession('contract');
        }
        break;
      case AuthContext.Contractor:
        await dialog.cache.cleanCache();
        dialog.setTopic(Topics.CONTRACTOR_START);
        dialog.user.settings.active = EntityType.User;
        dialog.user.settings.info = {
          accounts: false,
          archives: false,
          contracts: false,
          enabled: true,
          notify: false,
          profile: true,
        }
        await dialog.saveSettings(dialog.user.settings);
        if (contractor_context && contractor_context.profile) {
          merge_context = 'contractor';
          mergeTags(dialog.me.tags, contractor_context.profile.tags);
          const saved_self = await dialog.people.save(dialog.me);
          dialog.me = saved_self;
        }

        if (!lc.existing_user /*|| config.isEnvDevelopment()*/ || config.isEnvOffline()) {
          await data.users.onboardingSet(dialog.user, TemplateType.ContractorSurvey);
          do_notify = true;
        }
        break;
      case AuthContext.Project:
        if (!project_context || !project_context.project) {
          logging.warnFP(LOG_NAME, 'responsePrepare', dialog.user.profile, `Missing project${project_context ? '' : ' context' }`);
          dialog.setTopic(Topics.PROJECT_MISSING);
        } else {
          if (project_context.project.client && project_context.project.client.askfora_id === dialog.user.profile) dialog.setTopic(Topics.PROJECT_CMD);
          else dialog.setTopic(Topics.PROJECT_CANDIDATE);
          project = project_context.project;
          merge_context = 'project';
          if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
          else if(project) lc.redirect = `${lc.hostname}/app/job/${project.id}`;
        }
        break;
      case AuthContext.ProjectExpert:
        dialog.setTopic(Topics.PROJECT_LOGIN);
        await dialog.cache.cleanCache();
        dialog.cache.projects = makeIdMap(projects) as { [key: string]: Project };
        dialog.context.project = project_context;
        merge_context = 'project';
        if (!project_context || !project_context.project) {
          logging.warnFP(LOG_NAME, 'responsePrepare', dialog.user.profile, `Missing expert project${project_context ? '' : ' context' }`);
          dialog.setTopic(Topics.PROJECT_MISSING);
        } else {
          project = project_context.project;
          project.client = projectPerson(project, dialog.me, project.client);
          project.client.comms = [dialog.me.id, dialog.user.email];
          project.client.self = true;
          project.client.askfora_id = dialog.user.profile;
          dialog.projects.projectRole(project);

          logging.infoFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Setting project client to ${JSON.stringify(project.client)}`);
          await dialog.projects.update(project).catch(e => dialog.asyncError(e));
          await dialog.reloadData([EntityType.Project], true);
          logging.infoFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Existing projects ${JSON.stringify(Object.keys(dialog.cache.projects))}`);
          // await data.users.onboardingSet(dialog.user, existing_user && !config.isEnvOffline() ? TemplateType.Client : TemplateType.ClientSurvey);
          if (!lc.existing_user || config.isEnvOffline()) {
            await data.users.onboardingSet(dialog.user, TemplateType.ClientSurvey);
            do_notify = true;
          }
        }
        break;
      case AuthContext.ProjectCreate:
        await dialog.cache.cleanCache();
        dialog.cache.projects = makeIdMap(projects) as { [key: string]: Project };
        dialog.context.project = project_context;
        merge_context = 'project';
        if (!project_context || !project_context.project) {
          logging.warnFP(LOG_NAME, 'responsePrepare', dialog.user.profile, `Missing project create${project_context ? '' : ' context' }`);
          dialog.setTopic(Topics.PROJECT_MISSING);
        } else {
          project = project_context.project;
          
          if (project.client && (!project.client.askfora_id || project.client.askfora_id === FORA_PROFILE 
              || project.client.id == FORA_GUEST_ID || project.client.id === ANONYMOUS_ID 
              || project.client.askfora_id === dialog.user.id)) {
            // turn template into new project
            project.client = projectPerson(project, dialog.me, project.client);
            project.client.comms = [dialog.me.id, dialog.user.email];
            project.client.self = true;
            project.client.askfora_id = dialog.user.profile;
            dialog.projects.projectRole(project);
            logging.infoFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Setting project client to ${JSON.stringify(project.client)}`);

            dialog.mapGroupProjectSettings(project);
            if (project.group_settings && 'service_fee' in project.group_settings) project.service_fee = project.group_settings.service_fee;
            else if (project.contractor && 'service_fee' in project.contractor) project.service_fee = project.contractor.service_fee;
              
            project = await dialog.projects.create(project).catch(e => dialog.asyncError(e));
            project_context.project = project;
            await dialog.reloadData([EntityType.Project], true);
            logging.infoFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Existing projects ${JSON.stringify(Object.keys(dialog.cache.projects))}`);
            if (Object.values(dialog.cache.projects).filter(p => p.client.self).length > 1 || (lc.existing_user && !config.isEnvOffline ())) dialog.setTopic(Topics.PROJECT_LOGIN);
            else {
              dialog.setTopic(Topics.PROJECT_FIRSTRUN);
              do_search = !project.candidates || !project.candidates.length;
              project.searching = do_search;
            }

            //await data.users.onboardingSet(dialog.user, existing_user && !config.isEnvOffline() ? TemplateType.Client : TemplateType.ClientSurvey);
            if (!lc.existing_user || config.isEnvOffline()) {
              await data.users.onboardingSet(dialog.user, TemplateType.ClientSurvey);
              do_notify = true;
            }
          } else dialog.setTopic(Topics.PROJECT_CANDIDATE);
        }
        break;
      case AuthContext.Network:
        lc.redirect = `${lc.hostname}/network`;
        dialog.user.settings.active = EntityType.Person;
        dialog.user.settings.info = { enabled: false, profile: false, archives: false, accounts: false, notify: false, contracts: false};
        if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;
        if (dialog.user.settings.projects) dialog.user.settings.projects.active = null;
        if (dialog.user.settings.people) {
          dialog.user.settings.people.active = null;
          dialog.user.settings.people.enabled = true;
        }
        await dialog.saveSettings(dialog.user.settings);

        if (!lc.existing_user /*|| config.isEnvDevelopment()*/ || config.isEnvOffline()) {
          await data.users.onboardingSet(dialog.user, TemplateType.ClientSurvey);
          do_notify = true;
        }

        if (!lc.existing_user && lc.new_login) dialog.setTopic(Topics.CONNECT_TUTORIAL);

        break;
      case AuthContext.Intro:
        dialog.setTopic(Topics.INVALID_INTRO);
        if (dialog.context.make_intro) {
          const intro = await dialog.people.introductionById(dialog.context.make_intro.intro_id);
          if (intro) {
            const people = (await data.people.vanityByIds([intro.requested_by, intro.intro_to])).map(v => peopleUtils.personFromVanity(v));
            for (const p of people) {
              p.id = null;
              p.network = false;
            }
            dialog.context.make_intro.people = people;
            dialog.setTopic(Topics.ACCEPT_CONNECTION);
          }
        }
        break;
      case AuthContext.Connect:
        dialog.setTopic(Topics.INVALID_CONNECTION);
        if (dialog.context.make_intro) {
          dialog.context.make_intro.names.splice(0, 0, dialog.me.displayName);
          dialog.context.make_intro.ids.splice(0, 0, dialog.me.id);
          dialog.context.make_intro.connection = true;
          dialog.setTopic(Topics.PROMPT_CONNECTION);
          merge_context = 'make_intro';
        }

        if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        else lc.redirect = `${lc.hostname}/app/connect`;
        break;
      case AuthContext.Settings:
        dialog.setTopic(Topics.SETTINGS);
        if (dialog.context.settings && dialog.context.settings.type) {
          dialog.user.settings.active = dialog.context.settings.type;
          if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;
          if (dialog.user.settings.projects) dialog.user.settings.projects.active = null;
          if (dialog.user.settings.people) dialog.user.settings.people.active = null;
          dialog.user.settings.info = { enabled: false, profile: false, archives: false, accounts: false, notify: false, contracts: false};

          switch(dialog.context.settings.type) {
            case EntityType.Settings:
            case EntityType.User:
              dialog.user.settings.info = { enabled: true, profile: true, archives: false, accounts: false, notify: false, contracts: false};
              lc.redirect = `${lc.hostname}/app/settings/profile`;
              break;
            case EntityType.Person:
            case EntityType.Task:
            case EntityType.Note:
            case EntityType.Project:
          }
          await dialog.saveSettings(dialog.user.settings);
        }
        break;
      case AuthContext.People:
        dialog.user.settings.active = EntityType.Person;
        if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;
        if (dialog.user.settings.projects) dialog.user.settings.projects.active = null;
        if (dialog.user.settings.people) dialog.user.settings.people.active = null;
        dialog.user.settings.info = { enabled: false, profile: false, archives: false, accounts: false, notify: false, contracts: false};
        lc.redirect = '/people/explore';
        await dialog.saveSettings(dialog.user.settings);
        break;
      case AuthContext.AuthConnect:
        dialog.setTopic(Topics.INIT_CONNECT);
        break;
      default:
        if (dialog.session.redirect) lc.redirect = `${lc.hostname}${dialog.session.redirect}`
        if (!lc.existing_user /*|| config.isEnvDevelopment()*/ || config.isEnvOffline()) {
          await data.users.onboardingSet(dialog.user, TemplateType.ContractorSurvey);
          do_notify = true;
        }

        if (!lc.existing_user && lc.new_login) dialog.setTopic(Topics.CONNECT_TUTORIAL);

        // make sure there's no project or contractor carry over if it's not in the context
        dialog.clearContext('contractor');
        dialog.clearContext('project');
        break;
    }

    if (project) {
      if (!dialog.user.settings.projects) dialog.user.settings.projects = { view: project.client.self ? ViewProjects.Manage : ViewProjects.Contract };
      dialog.user.settings.active = project.expert ? EntityType.Expert : EntityType.Project;
      dialog.user.settings.projects.active = {id: project.id, type: project.expert ? EntityType.Expert : EntityType.Project, update_date: project.last_update}; // peopleUtils.projectInfo(dialog.user.profile, dialog.me, project, project.client.self);
      dialog.user.settings.info = { enabled: false, profile: false, archives: false, accounts: false, notify: false, contracts: false};
      if (dialog.user.settings.notes) dialog.user.settings.notes.active = null;
      if (dialog.user.settings.people) dialog.user.settings.people.active = null;
      await dialog.saveSettings(dialog.user.settings);
      if (project.client) {
        if (project.client.self) await dialog.projects.resolveCandidates(project).catch(e => dialog.asyncError(e));
        else await dialog.projects.resolveClient(project).catch(e => dialog.asyncError(e));
      } else logging.warnFP(LOG_NAME, 'responseHandler', dialog.user.profile, `Project ${project.id} has no client`);
    }

    if(logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'responseHandler', dialog.user.profile, `user = ${util.inspect(dialog.user, false, 1)}`);

    if (dialog.session.redirect) delete dialog.session.redirect;
    // force save
    await dialog.saveSession(true, merge_context);
    if (do_notify) await onboardUser(dialog.user);
    AuthHelper.responseSuccess(dialog, lc, req, res);

    if (lc.existing_user) await internalUpdate({profile: dialog.user.profile});

      // give response a chance to finish
    if (!lc.existing_user || do_search || !SourceController.firstRunDone(dialog.user)) {
      setTimeout(() => {
        firstRun(dialog.user.profile, lc.self, do_search, project ? project.id : null);
      }, 100);
    }
  }

  static responseSuccess(dialog: Dialog, lc: LoginContext, req: express.Request, res: express.Response) {
    /*if (context === AuthContext.AuthMobile) {
      res.status(200);
      res.redirect(redirect);
    } else {*/
      const redirect_page = fs
        .readFileSync(path.resolve(__dirname, '..', 'files', 'loading.html'), 'utf-8')
        .replace(/\$\{redirect\}/g, lc.redirect)
        .replace(/\$\{settings\}/g, lc.settings)
        .replace(/\$\{email\}/g, dialog.user ? dialog.user.email : "''");
      res.header('Access-Control-Allow-Origin', `${lc.redirect}`);
      res.send(redirect_page).end();
    //}
  }

  static responseNoDialog(req: express.Request, res: express.Response) {
    logging.errorF(LOG_NAME, 'responseNoDialog', `No dialog found in session ${req.session.id} for provider authentication response`, null);
    res.status(403);
    res.redirect('/');
  }

  static responseNoProvider(req: express.Request, res: express.Response) {
    logging.errorF(LOG_NAME, 'responseNoProvider', `No provider found in session ${req.session.id} for authentication response`, null);
    res.status(403);
    res.redirect('/');
  }

}
