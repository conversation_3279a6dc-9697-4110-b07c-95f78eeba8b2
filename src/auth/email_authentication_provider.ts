/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import { v4 as uuid } from 'uuid';

import config from '../config';

import Dialog, { Topics } from '../session/dialog';
import ForaUser from '../session/user';

import lang from '../lang';

import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Topic } from '../types/globals';
import { Group } from '../types/group';
import { Person, Tag } from '../types/items';
import { AuthClientInfo, AuthContext, AuthLevel, AuthProviders, NotificationType, ReplyCommand, TagType } from '../types/shared';

import { MONTHS } from '../utils/datetime';
import { hash } from '../utils/funcs';
import notify, { NotifyType } from '../utils/notify';


const LOG_NAME = 'data.auth.EmailAuthenticationProvider';
const DEBUG = (require('debug') as any)('fora:auth:email_auth_provier');

const SUPPORTED_CONTEXT = [
  AuthContext.AuthNoOrganizer,
  AuthContext.Contract,
  AuthContext.Project,
  AuthContext.Settings,
  AuthContext.App,
  AuthContext.Breadwinners,
  AuthContext.Signup,
  AuthContext.Learn,
  AuthContext.Webinar,
  AuthContext.Apply,
  AuthContext.Info,
  // AuthContext.ProjectCreate,
  // AuthContext.ProjectExpert,
];

/**
 * Authentication Provider for Email
 */
export class EmailAuthenticationProvider {
  connectedTopic(provider: AuthProviders, context: AuthContext, group: Group, user?: ForaUser): Topic {
    if (user.isAuthenticated() && !user.isGuestAccount() && user.hasAccount(provider)) return Topics.TUTORIAL;
    else return Topics.REG_ERROR;
  }

  getAuthClientInfo(context: AuthContext, group: Group): AuthClientInfo {
    return {
      name: group && group.email_domain ? `${group.name} email` : 'Email',
      clientId: group ? group.id : null,
      scope: undefined,
      provider: AuthProviders.Email,
      context,
    };
  }

  getAuthContext(): AuthContext {
    return AuthContext.App;
  }

  getAuthUrl(group: Group): string {
    return null;
  }

  getAuthCommand(context: AuthContext, group: Group): ReplyCommand {
    return {
      id: 'email_login',
      cmd: `connect email ${context ? context : ''}`,
      wait: true,
    };
  }

  getDefaultProviderSettings(): NormalizedProviderSettings {
    return new NormalizedProviderSettings(AuthProviders.Email);
  }

  getGroupName(provider: AuthProviders, group: Group): string {
    return group ? group.name : 'Email';
  }

  getGroupId(provider: AuthProviders, group: Group): string {
    return group ? group.id : null;
  }

  getGroupTokens(provider: AuthProviders, group: Group): any {
    return null; 
  }

  setGroupTokens(provider: AuthProviders, group: Group, tokens: any): any {
    return null;
  }

  getAuthPermssions(user: ForaUser, provider?: AuthProviders, group?: Group): AuthLevel {
    return AuthLevel.Basic;
  }

  isSupportedContext(context: AuthContext) {
    return SUPPORTED_CONTEXT.includes(context);
  }

  async getProviderUserNormalized(tokens: NormalizedProviderToken, req: express.Request, group: Group): Promise<NormalizedProviderUser> {
    if (tokens.id_token) {
      // check session for user email
      return new NormalizedProviderUser(`e_${hash(tokens.id_token).toLowerCase()}`, tokens.id_token, tokens.id_token.split('@')[0]);
    }
  }

  loadSelfPerson(tokens: NormalizedProviderToken, user: NormalizedProviderUser): Person {
    const p = new Person({
      comms: [user.email],
      learned: [new Date(0)],
      tags: [new Tag(TagType.skill, 'self', 0, new Date() )],
    });
    p.tempId();
    return p;
  }
  
  tokensFromSignedRequest(signed_request: string, email: string, group: Group): NormalizedProviderToken {
    if(!signed_request || !email || !group?.signing_secret) return null;

    const sr = signed_request.split('.');
    if(sr.length !== 2) return null;
    const seed = sr[0];
    const check_code = sr[1];

    const code = hash(`${email}:${seed}:${group.signing_secret}`);

    if(code == check_code) return this.tokens(email);
    return null;
  }

  signRequest(email: string, group: Group): string {
    if(!email || !group?.signing_secret) return null;

    const seed = uuid();
    const code = hash(`${email}:${seed}:${group.signing_secret}`);

    return `${seed}.${code}`;
  }

  tokensFromCode(check_code: number, email: string, seed: string, factor: number, group: Group): NormalizedProviderToken {
    if (!check_code || !email || !seed || !factor) return null;

    let group_settings = group ? group.providerSettings(AuthProviders.Email) : null;
    const email_secret = group_settings && group_settings.oauth && group_settings.oauth.client_secret ? group_settings.oauth.client_secret : config.get('EMAIL_CLIENT_SECRET');

    const phrase = hash(`${email}:${seed}:${email_secret}`);
    const code = Buffer.from(phrase).reduce((a,b) => a+b*factor, 0)

    if (code === check_code) return this.tokens(email);
    return null;
  }

  tokens(email: string) {
    const tokens = new NormalizedProviderToken(AuthProviders.Email, 'Email', AuthLevel.Basic);
    tokens.id_token = email.toLowerCase();
    tokens.expires_at = MONTHS(new Date(), 1);
    tokens.email = email.toLowerCase();
    return tokens;
  }

  async generateCode(dialog: Dialog, email: string, session: any, base_url?: string, subject?: string, message_fn?: (url: string) => string, type?: NotificationType): Promise<void> {
      const group = dialog.groupByIdOrHost(null, dialog.session['hostname']);
      // generate a code and attach it 

      let group_settings = group ? group.providerSettings(AuthProviders.Email) : null;
      const email_secret = group_settings && group_settings.oauth && group_settings.oauth.client_secret ? group_settings.oauth.client_secret : config.get('EMAIL_CLIENT_SECRET');

      const seed = uuid();
      const factor = Math.ceil(Math.random()*10);
      const phrase = hash(`${email}:${seed}:${email_secret}`);

      const code = Buffer.from(phrase).reduce((a,b) => a+b*factor, 0);

      await dialog.createGuest(true);
      session['seed'] = seed;
      session['factor'] = factor;

      const params = new URLSearchParams();
      params.set('code', code.toString());
      params.set('e',btoa(JSON.stringify({email})));

      const url = base_url ? `${base_url}?${params.toString()}` : undefined;

      if (!subject) subject = lang.init.LOGIN_CODE_SUBJECT;
      const message = message_fn ? message_fn(url) : lang.init.LOGIN_CODE_MESSAGE(code);
      if (!type) type = NotificationType.Code;

      const notification = { 
        email: {
          rcpts: [{Name: email, Email: email}],
          subject,
          message,
        },
        type,
        variables: { code, url },
      }

      dialog.user.email = email.toLowerCase();
      await notify(dialog.user, notification, NotifyType.EmailOnly, group, false, false);
  }
}
 