/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { ConfidentialClientApplication } from '@azure/msal-node';
import { Client } from '@microsoft/microsoft-graph-client';
import { User } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import util from 'util';

import config from '../config';

import ForaUser from '../session/user';

import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Group } from '../types/group';
import { Person, Tag } from '../types/items';
import { OAuthConfig } from '../types/oauth';
import { AuthClientInfo, AuthContext, AuthLevel, AuthProviders, TagType, Uid } from '../types/shared';

import { MINUTES } from '../utils/datetime';
import { normalizeId, stringIncludesAll } from '../utils/funcs';
import logging from '../utils/logging';

import { MicrosoftGraphConverter } from '../sources/helpers/microsoft/microsoft_graph_conversions';
import { AbstractAuthenticationProvider } from './a_authentication_provider';

export enum MsalScopes {
  Offline_Access = 'offline_access',
  Calendar_Read = 'Calendars.Read',
  Calendar_ReadWrite = 'Calendars.ReadWrite',
  Contacts_Read = 'Contacts.Read',
  Contacts_ReadWrite = 'Contacts.ReadWrite',
  // Files_ReadWrite_AppFolder = 'Files.ReadWrite.AppFolder',
  Mail_Read = 'Mail.Read',
  Mail_ReadWrite = 'Mail.ReadWrite',
  Mail_Send = 'Mail.Send',
  // Notes_ReadWrite = 'Notes.ReadWrite',
  Tasks_Read = 'Tasks.Read',
  Tasks_ReadWrite = 'Tasks.ReadWrite',
  User_Read = 'User.Read',
  Directory = 'Directory.Read.All', // https://graph.microsoft.com/v1.0/users
}

/**
 * Authentication provider for Msal
 *
 * Development Hints:
 * - To get a token for building test cases or playing around
 *   - sign into https://developer.microsoft.com/en-us/graph/graph-explorer
 *   - open console
 *   - run tokenPlease()
 *
 * Notes:
 * - /me is equivalent to /users/{id = current user}
 *
 * Libraries Used:
 * - https://github.com/microsoftgraph/msgraph-sdk-javascript
 * - https://github.com/microsoftgraph/msgraph-typescript-typings/
 * - https://github.com/microsoftgraph/msgraph-sdk-javascript
 * - https://github.com/AzureAD/microsoft-authentication-library-for-js
 *
 * Consent Management Pages:
 * - https://account.live.com/consent/Manage
 * - https://portal.office.com/account/#apps

 * Tutorials:
 * - https://docs.microsoft.com/en-us/outlook/rest/node-tutorial
 * - https://developer.microsoft.com/en-us/graph/docs/concepts/permissions_reference
 * - https://github.com/microsoftgraph/nodejs-apponlytoken-rest-sample/blob/master/auth.js
 * - https://github.com/AzureAD/microsoft-authentication-library-for-js/tree/dev/samples/msal-node-samples/auth-code
 */
export class MsalAuthenticationProvider extends AbstractAuthenticationProvider {
  private static readonly REVOKE_ENABLED: boolean = false; // Msal does not support revoking tokens as of ******** - I know right?!?
  log_name = 'auth.MsalAuthenticationProvider';

  ensureTokenTyped(token: any): NormalizedProviderToken {
    if (token) {
      token['provider'] = AuthProviders.Msal; // Make sure that we have a proper provider
    }

    return super.ensureTokenTyped(token);
  }

  getAuthClient(tokens?: NormalizedProviderToken, settings?: NormalizedProviderSettings): ConfidentialClientApplication { //OAuthClient {
    // Use the settings from the ENV if the user didn't explicitly tell us not to
    const defaults = this.getDefaultProviderSettings();
    if (!settings) settings = defaults;

    const clientId = settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : defaults.oauth.client_id;
    //const client_callback = settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;
    const clientSecret = settings.oauth && settings.oauth.client_secret ? settings.oauth.client_secret : defaults.oauth.client_secret;

    const client = new ConfidentialClientApplication({auth: {clientId, clientSecret}});
    if (tokens && tokens.account) {
      const cache = client.getTokenCache();
      cache.deserialize(JSON.stringify(tokens.account));
    } else if(tokens) logging.warnF(this.log_name, 'getAuthClient', `No account on tokens for ${tokens.email}`);

    return client;
  }

  getAuthClientInfo(context: AuthContext, group: Group, settings?: NormalizedProviderSettings): AuthClientInfo {
    return {
      name: 'Microsoft',
      clientId: settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : config.get('MSAL_OAUTH2_CLIENT_ID'),
      scope: this.getAuthScopes(context, group),
      provider: AuthProviders.Msal,
      context,
    };
  }

  async getAuthTokenFromCode(code: string, settings?: NormalizedProviderSettings, scopes?: string[]): Promise<NormalizedProviderToken> {
    const redirectUri = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : config.get('MSAL_OAUTH2_CALLBACK');
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthTokenFromCode', `MSAL_OAUTH2_CALLBACK = :${redirectUri}`);
    const client = this.getAuthClient(null, settings);
    const tokens = await client.acquireTokenByCode({code, scopes, redirectUri})
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthTokenFromCode', `tokens = ${util.format(tokens)}`);

    const account = JSON.parse(client.getTokenCache().serialize());

    const scope = scopes.join(' ');
    const permissions = stringIncludesAll(scope, this.SCOPES_ORGANIZER_SYNC) ? AuthLevel.OrganizerSync:
      stringIncludesAll(scope, this.SCOPES_ORGANIZER) ? AuthLevel.Organizer: AuthLevel.Basic;

    const ntokens = new NormalizedProviderToken(AuthProviders.Msal, null, permissions);
    ntokens.id_token = tokens.idToken;
    ntokens.scope = scope;
    ntokens.token_type = 'Bearer';
    ntokens.access_token = tokens.accessToken;
    ntokens.account = account;
    ntokens.expires_at = new Date(tokens.expiresOn);

    // get access token
    if (!ntokens.access_token) ntokens.access_token = await this.getAuthAccessToken(ntokens, settings);
    
    const user = await this.getProviderUserNormalized(ntokens, settings);
    if (user) ntokens.email = user.email;

    return ntokens;
  }

  getAuthUrl(context: AuthContext, group: Group, email: string = null, scopes?: string[], force_consent = false, force_selection = true): string {
    const defaults = this.getDefaultProviderSettings();
    let settings: NormalizedProviderSettings = group ? group.providerSettings(AuthProviders.Msal) : defaults;
    if (!settings) settings = defaults;


    const issuer = settings && settings.oauth && settings.oauth.client_issuer ? settings.oauth.client_issuer : defaults.oauth.client_issuer;
    const client_id = settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : defaults.oauth.client_id;
    const redirect_uri = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;

    const state = `${context} ${AuthProviders.Msal} ${group ? group.id : ''}`;
    const scope = this.getAuthScopes(context, group, scopes);
    const access_type = 'offline';
    const response_type = 'code';

    const url_params = {
      client_id,
      scope,
      state,
      redirect_uri,
      access_type,
      response_type,
    }

    const params = new URLSearchParams();

    for (const att in url_params) params.append(att, url_params[att]);

    const url = new URL(`${issuer}/oauth2/v2.0/authorize`);
    url.search = params.toString();

    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthUrl', `Generated url = ${url}`);
    return url.toString();
  }

  getDefaultProviderSettings(): NormalizedProviderSettings {
    return new NormalizedProviderSettings(AuthProviders.Msal, new OAuthConfig(config.get('MSAL_OAUTH2_CLIENT_ID'), config.get('MSAL_OAUTH2_CLIENT_SECRET'), config.get('MSAL_OAUTH2_CALLBACK'), config.get('MSAL_OAUTH2_ISSUER')));
  }

  /**
   * Get a handle to a Microsoft Graph client
   *
   * @param token - Tokens for user that will interact with the Microsoft Graph
   * @param settings - Normalized provider settings
   * @async
   */
  async getGraphClient(user: ForaUser, profile: Uid, settings?: NormalizedProviderSettings): Promise<Client> {
    let tokens = user.getTokens(AuthProviders.Msal, profile);
    // Use the settings from the ENV if the user didn't explicitly tell us not to
    let access_token = tokens ? await this.getAuthAccessToken(tokens, settings) : null;
    if (logging.isDebug()) logging.debugF(this.log_name, 'getGraphClient', `access_token = ${logging.truncate(access_token, 10)}`);

    if (access_token) return Client.init({ authProvider: done => done(null, access_token), debugLogging: logging.isDebug() });
    else if (tokens) {
      const new_tokens = await this.reAuthenticate(tokens, settings);
      if (new_tokens) {
        access_token = await this.getAuthAccessToken(new_tokens, settings);
        if (access_token) {
          user.tokens = new_tokens;
          return Client.init({ authProvider: done => done(null, access_token), debugLogging: logging.isDebug() });
        }
      }
    }


    throw Error("access_token is either null, or expired and we can't refresh it");
  }

  async getProviderUserNormalized(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser> {
    const user: User = await this.getProviderUser(tokens, settings);
    if (user) {
      if (logging.isDebug()) logging.debugF(this.log_name, 'getProviderUserNormalized', `User = ${util.format(user)}`);
      return new NormalizedProviderUser(normalizeId(user.id), user.userPrincipalName, user.givenName);
    }
    return null;
  }

  isValid(token: NormalizedProviderToken, check_refresh = true): boolean {
    const is_valid = !!(token && token['access_token'] && (!check_refresh || token['id_token']));
    if(logging.isDebug()) logging.debugF(this.log_name, 'isValid', `${is_valid ? '' : 'NOT '}Valid ${JSON.stringify(token)}`);
    return is_valid;
  }

  async loadSelfPerson(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<Person> {
    const user = await this.getProviderUser(tokens, settings);
    // const qlimit = limit > 0 ? `limit ${limit}` : '';
    if (user) {
      const save_person = MicrosoftGraphConverter.convertPersonFromUser(user, tokens, settings);
      save_person.learned = [new Date(0)];
      save_person.tags.push(new Tag(TagType.skill, 'self', 0, new Date() ));
      save_person.self = true;
      return save_person;
    } 
    return null;
  }

  async reAuthenticate(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    if (logging.isDebug()) logging.debugF(this.log_name, 'reAuthenticate', `tokens = ${logging.formatToken(tokens)}`);

    await this.getAuthAccessToken(tokens, settings);
    return tokens;
  }

  async revokeToken(profile: Uid, provider: AuthProviders, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void> {
    logging.warnFP(this.log_name, 'revokeToken', provider, 'Not revoking token because revoke is not enabled');
  }

  protected setupScopes(): void {
    this.SCOPES_OFFLINE = [MsalScopes.Offline_Access];
    this.SCOPES_CALENDAR = [MsalScopes.Calendar_Read];
    this.SCOPES_CALENDAR_SYNC = [MsalScopes.Calendar_ReadWrite];
    this.SCOPES_CONTACT = [MsalScopes.Contacts_Read];
    this.SCOPES_CONTACT_SYNC = [MsalScopes.Contacts_ReadWrite];
    this.SCOPES_DIRECTORY = [MsalScopes.Directory];
    this.SCOPES_FILE = []; // MicrosoftScopes.Files_ReadWrite_AppFolder];
    this.SCOPES_MAIL = [MsalScopes.Mail_Read, MsalScopes.Mail_Send];
    this.SCOPES_MAIL_SYNC = [MsalScopes.Mail_ReadWrite, MsalScopes.Mail_Send];
    this.SCOPES_NOTE = []; // MicrosoftScopes.Notes_ReadWrite];
    this.SCOPES_TASK = [MsalScopes.Tasks_Read];
    this.SCOPES_TASK_SYNC = [MsalScopes.Tasks_ReadWrite];
    this.SCOPES_USER = [MsalScopes.User_Read];
  }

  /**
   * Get access token from given tokens. Will attempt to get a new set of tokens if the current has expired
   * and we have a refresh token. If we do not have a refresh_token, will return null.
   *
   * @param tokens - tokens
   * @param settings - Normalized provider settings
   * @async
   */
  private async getAuthAccessToken(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<string> {
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthAccessToken', `token = ${logging.formatToken(tokens)}`);
    logging.infoF(this.log_name, 'getAuthAccessToken', `token = ${logging.formatToken(tokens)}`);

    const access_token = tokens  ? tokens.access_token as string : null;
    const expires_at = tokens ? tokens.expires_at as any : null;

    if (access_token) {
      if (expires_at) {
        // We have a token, but is it expired?
        // Expire 5 minutes early to account for clock differences
        const expiration = new Date(expires_at); //.getTime() - 300000);
        if (expiration.getTime() > MINUTES(new Date(), 5).getTime()) {
          // Token is still good, just return it
          // return getAuthClient().accessToken.create({access_token}).token;
          return access_token;
        }
      } /* else {
        return access_token;
      }*/
    }

    // Either no token or it's expired, do we have a refresh token?
    if (tokens && tokens.id_token) {
      let client;
      try {
        client = this.getAuthClient(tokens, settings);
        let new_tokens;
        if (tokens.account) {
          new_tokens = await client.acquireTokenSilent({scopes: tokens.scope.split(' '), account: tokens.account})
        }
        else {
          new_tokens = await client.acquireTokenOnBehalfOf({scopes:tokens.scope.split(' '), oboAssertion: tokens.id_token})
        }

        tokens.scope = new_tokens.scopes.join(' ');
        tokens.id_token = new_tokens.idToken;
        tokens.access_token = new_tokens.accessToken;
        tokens.expires_at = new Date(new_tokens.expiresOn);

        return new_tokens.accessToken; 
     } catch(e) {
        logging.errorF(this.log_name, 'getAuthAccessToken', `Error refreshing auth token`, e);
      }
    }

    // Nothing in the stored tokens that helps, return empty
    return null;
  }

  /**
   * Get the current user from the provider
   *
   * Microsoft docs:
   * - https://developer.microsoft.com/en-us/graph/docs/api-reference/v1.0/api/user_get
   * - https://developer.microsoft.com/en-us/graph/docs/api-reference/v1.0/resources/user
   *
   * @param tokens - Tokens to communicate with provider
   * @param settings - Normalized provider settings
   * @async
   */
  private async getProviderUser(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<User> {
    let client: Client;
    let user: User;
    try {
      const temp_user = new ForaUser('temp', AuthProviders.Msal);
      temp_user.tokens = tokens;
      client = await this.getGraphClient(temp_user, 'temp', settings);
      user = client ? await client.api('/me').get() : null;
    } catch(e) {
      if (!client) logging.errorF(this.log_name, 'getProviderUser', 'Failed to get graph client', e);
      else if (!user) logging.errorF(this.log_name, 'getProviderUser', 'Failed to get user /me', e);
    }

    if (user) {
      if (logging.isDebug()) logging.debugF(this.log_name, 'getProviderUser', `user = ${util.format(user)}`);
      return user;
    } else {
      logging.warnF(this.log_name, 'getProviderUser', `Failed to get user from provider`);
      return null;
    }
  }
}
