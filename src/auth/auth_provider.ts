/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import util from 'util';

import config from '../config';
import lang from '../lang';

import { Topics } from '../session/dialog';
import ForaUser from '../session/user';

import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Topic } from '../types/globals';
import { Group } from '../types/group';
import { Person } from '../types/items';
import { AuthClientContext, AuthClientInfo, AuthClientNameInfo, AuthContext, AuthLevel, AuthProviders, BasicInfo, Uid } from '../types/shared';

import logging from '../utils/logging';

import { EmailAuthenticationProvider } from './email_authentication_provider';
import { GoogleAuthenticationProvider } from './google_authentication_provider';
import { MicrosoftAuthenticationProvider } from './microsoft_authentication_provider';
import { MsalAuthenticationProvider } from './msal_authentication_provider';
import { OfflineAuthenticationProvider } from './offline_authentication_provider';
import { OktaAuthenticationProvider } from './okta_authentication_provider';
import { SamlAuthenticationProvider } from './saml_authentication_provider';
import { SlackAuthenticationProvider } from './slack_authentication_provider';

const LOG_NAME = 'auth.AuthProvider';

export class AuthProvider {
  public static google = new GoogleAuthenticationProvider();
  public static microsoft = new MicrosoftAuthenticationProvider();
  public static msal = new MsalAuthenticationProvider();
  public static offline = new OfflineAuthenticationProvider();
  public static okta = new OktaAuthenticationProvider();
  public static saml = new SamlAuthenticationProvider(); 
  public static email = new EmailAuthenticationProvider(); 
  public static slack = new SlackAuthenticationProvider();

  static checkScopesForReAuth(user: ForaUser, provider: AuthProviders, profile: Uid, scope: string, context: AuthContext, group: Group): string {
    let required_scopes;
    switch (provider) {
      case AuthProviders.Google:
        required_scopes = AuthProvider.google.getAuthRequiredScope(user, provider, profile, group);
        if (logging.isDebug()) {
          logging.debugF(LOG_NAME, 'checkScopesForReAuth', `required scopes = ${util.format(required_scopes)}`);
          logging.debugF(LOG_NAME, 'checkScopesForReAuth', `supplied scopes = ${util.format(scope)}`);
        }

        for (const r_scope of required_scopes) {
          if (!scope.includes(r_scope)) {
            logging.warnF(LOG_NAME, 'checkScopesForReAuth', `missing scope = ${r_scope}, re-authenticating`);
            return AuthProvider.google.getAuthUrl(context, group, user.email, required_scopes.concat(scope.split(' ')), false, false);
          }
        }
        break;
      case AuthProviders.Microsoft:
        required_scopes = AuthProvider.microsoft.getAuthRequiredScope(user, provider, profile, group);
        if (logging.isDebug()) {
          logging.debugF(LOG_NAME, 'checkScopesForReAuth', `required scopes = ${util.format(required_scopes)}`);
          logging.debugF(LOG_NAME, 'checkScopesForReAuth', `supplied scopes = ${util.format(scope)}`);
        }

        for (const r_scope of required_scopes) {
          // Returned tokens scope does not include `offline_access` even though we request it. It all works,
          // its just not in the returned scope portion of the tokens
          if (r_scope !== 'offline_access' && !scope.includes(r_scope)) {
            logging.warnF(LOG_NAME, 'checkScopesForReAuth', `missing scope = ${r_scope}, re-authenticating`);
            return AuthProvider.microsoft.getAuthUrl(context, group, user.email, required_scopes.concat(scope.split(' ')), false, false);
          }
        }
        break;
      case AuthProviders.Msal:
        required_scopes = AuthProvider.msal.getAuthRequiredScope(user, provider, profile, group);
        if (logging.isDebug()) {
          logging.debugF(LOG_NAME, 'checkScopesForReAuth', `required scopes = ${util.format(required_scopes)}`);
          logging.debugF(LOG_NAME, 'checkScopesForReAuth', `supplied scopes = ${util.format(scope)}`);
        }

        for (const r_scope of required_scopes) {
          // Returned tokens scope does not include `offline_access` even though we request it. It all works,
          // its just not in the returned scope portion of the tokens
          if (r_scope !== 'offline_access' && !scope.includes(r_scope)) {
            logging.warnF(LOG_NAME, 'checkScopesForReAuth', `missing scope = ${r_scope}, re-authenticating`);
            return AuthProvider.msal.getAuthUrl(context, group, user.email, required_scopes.concat(scope.split(' ')), false, false);
          }
        }
        break;
      case AuthProviders.Okta:
      case AuthProviders.Email:
      case AuthProviders.Offline:
      case AuthProviders.Saml:
      case AuthProviders.Slack:
        return;
      default:
        throw new Error(`Unexpected provider type - ${user.provider}`);
    }

    return null;
  }

  static clientAuthContext(context: AuthContext, provider: AuthProviders, group: Group, email: string = null): AuthClientContext {
    let client_context: AuthClientContext = null;

    switch (provider) {
      case AuthProviders.Google:
        client_context = AuthProvider.google.getAuthClientInfo(context, group);
        client_context.name = 'Google';
        client_context.permissions = AuthProvider.contextPermissions(context);
        client_context.url = AuthProvider.google.getAuthUrl(context, group, email);
        break;
      case AuthProviders.Microsoft:
        client_context = AuthProvider.microsoft.getAuthClientInfo(context, group);
        client_context.name = 'Microsoft';
        client_context.permissions = AuthProvider.contextPermissions(context);
        client_context.url = AuthProvider.microsoft.getAuthUrl(context, group, email);
        break;
      case AuthProviders.Msal:
        client_context = AuthProvider.msal.getAuthClientInfo(context, group);
        client_context.name = 'Microsoft';
        client_context.permissions = AuthProvider.contextPermissions(context);
        client_context.url = AuthProvider.msal.getAuthUrl(context, group, email);
        break;
      case AuthProviders.Okta:
        client_context = AuthProvider.okta.getAuthClientInfo(context, group);
        client_context.name = 'Okta';
        client_context.permissions = AuthProvider.contextPermissions(context);
        client_context.url = AuthProvider.okta.getAuthUrl(context, group, email);
        break;
      case AuthProviders.Offline:
        client_context = AuthProvider.offline.getAuthClientInfo(context, group);
        client_context.name = 'Offline';
        client_context.permissions = AuthProvider.contextPermissions(context);
        client_context.url = AuthProvider.offline.getAuthUrl(context, group, email);
        break;
      case AuthProviders.Saml:
        client_context = AuthProvider.saml.getAuthClientInfo(context, group);
        client_context.name = group ? group.company_name : 'work or school';
        client_context.permissions = AuthProvider.contextPermissions(AuthContext.App);
        client_context.url = AuthProvider.saml.getAuthUrl(group);
        break;
      case AuthProviders.Email:
        client_context = AuthProvider.email.getAuthClientInfo(context, group);
        client_context.name = group ? group.company_name : 'work or school';
        client_context.permissions = AuthProvider.contextPermissions(AuthContext.App);
        client_context.url = AuthProvider.email.getAuthUrl(group);
        break;
      case AuthProviders.Slack:
        client_context = AuthProvider.slack.getAuthClientInfo(context, group);
        client_context.name = AuthProvider.slack.getGroupName(provider, group);
        client_context.permissions = AuthProvider.contextPermissions(AuthProvider.slack.getContext(group));
        client_context.url = AuthProvider.slack.getAuthUrl(context, group, email);
        break; 
      default:
        throw new Error(`Unexpected provider type - ${provider}`);
    }

    if (group) client_context.group = group.id;

    return client_context;
  }

  static clientAuthPermissions(user: ForaUser, provider?: AuthProviders, group?: Group): AuthLevel {
    switch(provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getAuthPermssions(user, provider, group);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getAuthPermssions(user, provider, group);
      case AuthProviders.Msal:
        return AuthProvider.msal.getAuthPermssions(user, provider, group);
      case AuthProviders.Okta:
        return AuthProvider.okta.getAuthPermssions(user, provider, group);
      case AuthProviders.Offline:
        return AuthProvider.offline.getAuthPermssions(user, provider, group);
      case AuthProviders.Saml:
        return AuthProvider.saml.getAuthPermssions(user, provider, group);
      case AuthProviders.Email:
        return AuthProvider.email.getAuthPermssions(user, provider, group);
      case AuthProviders.Slack:
        return AuthProvider.slack.getAuthPermssions(user, provider, group);
      default:
        throw new Error(`Unexpected provider type - ${provider}`);
    }
  }

  static clientAuthInfo(context: AuthContext, provider: AuthProviders, group: Group): AuthClientInfo {
    switch (provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getAuthClientInfo(context, group);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getAuthClientInfo(context, group);
      case AuthProviders.Msal:
        return AuthProvider.msal.getAuthClientInfo(context, group);
      case AuthProviders.Okta:
        return AuthProvider.okta.getAuthClientInfo(context, group);
      case AuthProviders.Offline:
        return AuthProvider.offline.getAuthClientInfo(context, group);
      case AuthProviders.Saml:
        return AuthProvider.saml.getAuthClientInfo(context, group);
      case AuthProviders.Email:
        return AuthProvider.email.getAuthClientInfo(context, group);
      case AuthProviders.Slack:
        return AuthProvider.slack.getAuthClientInfo(context, group);
      default:
        throw new Error(`Unexpected provider type - ${provider}`);
    }
  }

  static clientAuthRevokeUrl(user: ForaUser, group: Group = null): string { // provider: AuthProviders = null, group: Group = null): string {
    let provider: AuthProviders = user.provider;
    if (group && group.provider) provider = group.provider;

    if (provider) {
      switch(provider) {
        case AuthProviders.Google: return 'https://myaccount.google.com/permissions';
        case AuthProviders.Msal:
        case AuthProviders.Microsoft: 
          if (user.profile.includes('_')) return 'https://myapplications.microsoft.com/';
          else return 'https://account.live.com/consent/Manage'; // 'https://portal.office.com/account/#apps'; 
      }
    }

    return null;
  }

  static clientAuthNameSet(provider: AuthProviders = null, group: Group = null, admin = false): AuthClientNameInfo[] {
    if (!provider && group && group.provider) provider = group.provider;

    let data = [{ name: 'Google', provider: AuthProviders.Google }, { name: 'Microsoft', provider: AuthProviders.Msal }, { name: 'Email', provider: AuthProviders.Email }];

    if (provider) {
      switch (provider) {
        case AuthProviders.Google: 
          data = [{ name: group && group.name ? group.name : 'Google', provider: AuthProviders.Google }];
          break;
        case AuthProviders.Microsoft: 
          data = [{ name: group && group.name ? group.name : 'Microsoft', provider: AuthProviders.Microsoft }];
          break;
        case AuthProviders.Msal: 
          data = [{ name: group && group.name ? group.name : 'Microsoft', provider: AuthProviders.Msal }];
          break;
        case AuthProviders.Okta: 
          data = [{name: group && group.name ? group.name : 'Okta', provider: AuthProviders.Okta}];
          break;
        case AuthProviders.Email: 
          data = [{name: group && group.name ? group.name : 'Email', provider: AuthProviders.Email}];
          break;
        case AuthProviders.Saml: 
          data = [{ name: `${group ? group.company_name : 'work or school'}`, provider: AuthProviders.Saml }];
          break;
        case AuthProviders.Slack: 
          data = [{ name: group && group.name ? group.name : AuthProvider.slack.getGroupName(provider, group), provider: AuthProviders.Slack }];
          break;
        case AuthProviders.Stripe: 
          data = [{ name: group && group.name ? group.name : 'Stripe', provider: AuthProviders.Stripe }];
          break;
        case AuthProviders.Wise: 
          data = [{ name: group && group.name ? group.name : 'Wise', provider: AuthProviders.Slack }];
          break;
      }
    }

    if (group) {
      if (group.accounts) {
        const auth_set = AuthProvider.clientAuthSet(null, group);
        const providers = data.map(d => d.provider);
        for (const provider of Object.keys(group.accounts) as AuthProviders[]) {
          if (!providers.includes(provider)) {
            const provider_set = auth_set.find(a => a.provider === provider);
            if (provider_set) {
              providers.push(provider);
              data.push({name: provider_set.name, provider});
            }
          }
        }
      }

      if ((group.accounts && group.accounts['slack']) || admin) data.push({ name: 'Slack', provider: AuthProviders.Slack });
    }

    if (config.isEnvOffline()) data.push({ name: 'Offline', provider: AuthProviders.Offline });
    return data;
  }

  static clientAuthSet(context?: AuthContext, group?: Group): AuthClientContext[] {
    const group_provider = group ? group.provider : null;
    const group_provider_settings = group ? group.defaultProviderSettings : null;
    const group_oauth = group_provider_settings ? group_provider_settings.oauth : null;
    const group_client_id = group_oauth ? group_oauth.client_id : null;
    const google_auth: AuthClientInfo = AuthProvider.google.getAuthClientInfo(context, group);
    const google_set = {
      clientId: group_provider === AuthProviders.Google && group_client_id ? group_client_id : google_auth.clientId,
      scope: google_auth.scope,
      access_type: google_auth.access_type,
      context: google_auth.context,
      provider: google_auth.provider,
      name: 'Google',
      permissions: AuthProvider.contextPermissions(context),
      url: context ? AuthProvider.google.getAuthUrl(context, group, null, google_auth.scope.split(' ')) : null,
    };

    const microsoft_auth: AuthClientInfo = AuthProvider.microsoft.getAuthClientInfo(context, group);
    const microsoft_set = {
      clientId: group_provider === AuthProviders.Microsoft && group_client_id ? group_client_id : microsoft_auth.clientId,
      scope: microsoft_auth.scope,
      access_type: microsoft_auth.access_type,
      context: microsoft_auth.context,
      provider: microsoft_auth.provider,
      name: 'Microsoft',
      permissions: AuthProvider.contextPermissions(context),
      url: context ? AuthProvider.microsoft.getAuthUrl(context, group, null, microsoft_auth.scope.split(' ')) : null,
    };

    const msal_auth: AuthClientInfo = AuthProvider.msal.getAuthClientInfo(context, group);
    const msal_set = {
      clientId: group_provider === AuthProviders.Msal && group_client_id ? group_client_id : msal_auth.clientId,
      scope: msal_auth.scope,
      access_type: msal_auth.access_type,
      context: msal_auth.context,
      provider: msal_auth.provider,
      name: 'Microsoft',
      permissions: AuthProvider.contextPermissions(context),
      url: context ? AuthProvider.msal.getAuthUrl(context, group, null, msal_auth.scope.split(' ')) : null,
    };

    const slack_auth: AuthClientInfo = AuthProvider.slack.getAuthClientInfo(context, group);
    const slack_set = {
      clientId: group_provider === AuthProviders.Slack && group_client_id ? group_client_id : slack_auth.clientId,
      scope: slack_auth.scope,
      context: slack_auth.context,
      provider: slack_auth.provider,
      name: group_provider === AuthProviders.Slack ? AuthProvider.slack.getGroupName(AuthProviders.Slack, group) : 'Slack',
      permissions: AuthProvider.contextPermissions(context),
      url: context ? AuthProvider.slack.getAuthUrl(context, group, null, slack_auth.scope.split(' ')) : null,
    };

    const okta_auth: AuthClientInfo = AuthProvider.okta.getAuthClientInfo(context, group);
    const okta_set = {
      clientId: group_provider === AuthProviders.Okta && group_client_id ? group_client_id : okta_auth.clientId,
      scope: okta_auth.scope,
      context: okta_auth.context,
      provider: okta_auth.provider,
      name: group_provider === AuthProviders.Okta ? AuthProvider.okta.getGroupName(AuthProviders.Okta, group) : 'Okta',
      permissions: AuthProvider.contextPermissions(context),
      url: context ? AuthProvider.okta.getAuthUrl(context, group, null, okta_auth.scope.split(' ')) : null,
    };

    const email_auth: AuthClientInfo = AuthProvider.email.getAuthClientInfo(context, group);
    const email_set = {
      clientId: group_provider === AuthProviders.Email && group_client_id ? group_client_id : email_auth.clientId,
      scope: email_auth.scope,
      context: email_auth.context,
      provider: email_auth.provider,
      name: group_provider === AuthProviders.Email ? AuthProvider.email.getGroupName(AuthProviders.Email, group) : 'Email',
      permissions: AuthProvider.contextPermissions(context),
      command: AuthProvider.email.getAuthCommand(context, group),
    };

    const getProviderSet = provider =>  {
      switch (provider) {
        case AuthProviders.Google: return google_set;
        case AuthProviders.Microsoft: return microsoft_set;
        case AuthProviders.Msal: return msal_set;
        case AuthProviders.Offline: return google_set;
        case AuthProviders.Okta: return okta_set;
        case AuthProviders.Email: return email_set;
        case AuthProviders.Saml:
          {
            // We only do SAML if its an explicit requirement of a group
            const saml_auth: AuthClientInfo = AuthProvider.saml.getAuthClientInfo(context, group);
            const saml_set = {
              clientId: group.id,
              scope: saml_auth.scope,
              access_type: saml_auth.access_type,
              context: saml_auth.context,
              provider: saml_auth.provider,
              name: group ? group.company_name : 'work or school',
              permissions: AuthProvider.contextPermissions(AuthContext.App),
              url: context ? AuthProvider.saml.getAuthUrl(group) : null,
            };

            return saml_set;
          }
        case AuthProviders.Slack:
          return slack_set;
      }
      return null;
    }

    const auth_set = [];

    const providers = [];

    if (group) {
      if (group.provider) {
        const provider_set = getProviderSet(group.provider);
        if (provider_set) {
          providers.push(group.provider);
          auth_set.push(provider_set);
        }
      }

      if (group.accounts) {
        for (const provider of Object.keys(group.accounts)) {
          if (!providers.includes(provider)) {
            const provider_set = getProviderSet(provider);
            if (provider_set) {
              providers.push(provider);
              auth_set.push(provider_set);
            }
          }
        }
      }

      if (group.provider_settings) {
        for (const settings of group.provider_settings) {
          if (!providers.includes(settings.provider)) {
            const provider_set = getProviderSet(settings.provider);
            if (provider_set) {
              providers.push(settings.provider);
              auth_set.push(provider_set);
            }
          }
        }
      }
    } 
    
    if (!auth_set.length) auth_set.push(google_set, msal_set, email_set);

    return context ? auth_set.filter(s => this.isSupportedContext(s.provider, context)) : auth_set;
  }

  static clientAuthUrl(context: AuthContext, group: Group, settings: NormalizedProviderSettings, email?: string, force_consent = false): string {
    switch (settings.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getAuthUrl(context, group, email, null, force_consent);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getAuthUrl(context, group, email, null, force_consent);
      case AuthProviders.Msal:
        return AuthProvider.msal.getAuthUrl(context, group, email, null, force_consent);
      case AuthProviders.Okta:
        return AuthProvider.okta.getAuthUrl(context, group, email, null, force_consent);
      case AuthProviders.Offline:
        return AuthProvider.offline.getAuthUrl(context, group, email, null, force_consent);
      case AuthProviders.Saml:
        return AuthProvider.saml.getAuthUrl(group);
      case AuthProviders.Email:
        return AuthProvider.email.getAuthUrl(group);
      case AuthProviders.Slack:
        return AuthProvider.slack.getAuthUrl(context, group, email, null, force_consent);
      default:
        throw new Error(`Unexpected provider type - ${settings.provider}`);
    }
  }

  static contextPermissions(context: AuthContext): BasicInfo[] {
    switch (context) {
      case AuthContext.AuthNoOrganizer:
      case AuthContext.Contract:
      case AuthContext.Project:
      case AuthContext.Signup:
      case AuthContext.Learn:
      case AuthContext.Webinar:
      case AuthContext.Info:
      case AuthContext.Apply:
      case AuthContext.Breadwinners:
        return lang.init.PERMISSIONS_BASIC();
      case AuthContext.ProjectCreate:
      case AuthContext.ProjectExpert:
        return lang.init.PERMISSIONS_PROJECT();
      case AuthContext.AuthSyncOrganizer:
        return lang.init.PERMISSIONS_ORGANIZER_SYNC();
      case AuthContext.AuthNoEmail:
      case AuthContext.Contractor:
      case AuthContext.Network:
      case AuthContext.Intro:
      case AuthContext.Settings:
      case AuthContext.People:
      case AuthContext.Connect:
      case AuthContext.App:
        return lang.init.PERMISSIONS_ORGANIZER();
      case AuthContext.AuthAPI:
        return lang.init.PERMISSIONS_CHAT_API();
      case AuthContext.AuthConnect:
        return lang.init.PERMISSIONS_CONNECT();
      default:
        return lang.init.PERMISSIONS_FULL();
    }
  }

  static getAuthContext(user: ForaUser, provider?: AuthProviders, profile?: string, group?: Group): AuthContext {
    switch (provider ? provider : user.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getAuthContext(user, provider, profile, group);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getAuthContext(user, provider, profile, group);
      case AuthProviders.Msal:
        return AuthProvider.msal.getAuthContext(user, provider, profile, group);
      case AuthProviders.Okta:
        return AuthProvider.okta.getAuthContext(user, provider, profile, group);
      case AuthProviders.Offline:
        return AuthProvider.offline.getAuthContext(user, provider, profile, group);
      case AuthProviders.Saml:
        return AuthProvider.saml.getAuthContext();
      case AuthProviders.Email:
        return AuthProvider.email.getAuthContext();
      case AuthProviders.Slack:
        return AuthProvider.slack.getAuthContext(user, provider, profile, group);
      case AuthProviders.Stripe:
      case AuthProviders.Wise:
        return AuthContext.AuthConnect;
      default:
        throw new Error(`Unexpected provider type for ${profile ? profile : user.profile}: ${provider ? provider : user.provider}`);
    }
  }

  static getDefaultProviderSettings(provider: AuthProviders): NormalizedProviderSettings {
    switch (provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getDefaultProviderSettings();
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getDefaultProviderSettings();
      case AuthProviders.Msal:
        return AuthProvider.msal.getDefaultProviderSettings();
      case AuthProviders.Okta:
        return AuthProvider.okta.getDefaultProviderSettings();
      case AuthProviders.Offline:
        return AuthProvider.offline.getDefaultProviderSettings();
      case AuthProviders.Saml:
        return AuthProvider.saml.getDefaultProviderSettings();
      case AuthProviders.Email:
        return AuthProvider.email.getDefaultProviderSettings();
      case AuthProviders.Slack:
        return AuthProvider.slack.getDefaultProviderSettings();
      default:
        throw new Error(`Unexpected provider type - ${provider}`);
    }
  }

  static getProviderUser(tokens: NormalizedProviderToken, settings: NormalizedProviderSettings, group: Group, request: express.Request): Promise<NormalizedProviderUser> {
    switch (settings.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getProviderUserNormalized(tokens, settings);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getProviderUserNormalized(tokens, settings);
      case AuthProviders.Msal:
        return AuthProvider.msal.getProviderUserNormalized(tokens, settings);
      case AuthProviders.Okta:
        return AuthProvider.okta.getProviderUserNormalized(tokens, settings);
      case AuthProviders.Offline:
        return AuthProvider.offline.getProviderUserNormalized(tokens, settings);
      case AuthProviders.Saml:
        return AuthProvider.saml.getProviderUserNormalized(request, group);
      case AuthProviders.Email:
        return AuthProvider.email.getProviderUserNormalized(tokens, request, group);
      case AuthProviders.Slack:
        return AuthProvider.slack.getProviderUserNormalized(tokens, settings);
      default:
        throw new Error(`Unexpected provider type - ${settings.provider}`);
    }
  }

  static getJWTProviderUser(idToken: string, settings: NormalizedProviderSettings): Promise<NormalizedProviderUser> {
    switch(settings.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getJWTProviderUserNormalized(idToken, settings);
      default:
        throw new Error(`Unexpected provider type - ${settings.provider}`);
    }
  }

  static async getProviderUserSelf(tokens: NormalizedProviderToken, user: NormalizedProviderUser, settings: NormalizedProviderSettings, request: express.Request): Promise<Person> {
    let person;
    switch (settings.provider) {
      case AuthProviders.Google:
        person = AuthProvider.google.loadSelfPerson(tokens, settings);
        break;
      case AuthProviders.Microsoft:
        person = AuthProvider.microsoft.loadSelfPerson(tokens, settings);
        break;
      case AuthProviders.Msal:
        person = AuthProvider.msal.loadSelfPerson(tokens, settings);
        break;
      case AuthProviders.Okta:
        person = AuthProvider.okta.loadSelfPerson(tokens, settings);
        break;
      case AuthProviders.Offline:
        person = AuthProvider.offline.loadSelfPerson(tokens, settings);
        break;
      case AuthProviders.Saml:
        person = AuthProvider.saml.loadSelfPerson(request, user);
        break;
      case AuthProviders.Email:
        person = AuthProvider.email.loadSelfPerson(tokens, user);
        break;
      case AuthProviders.Slack:
        person = AuthProvider.slack.loadSelfPerson(tokens, settings);
        break;
      default:
        throw new Error(`Unexpected provider type - ${settings.provider}`);
    }
    if (person) person.id = `people/a${user.id}`;
    return person;
  }

  static getScopesForEvents(user: ForaUser, provider?: AuthProviders): string[][] {
    switch (provider ? provider : user.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getScopesForEvents();
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getScopesForEvents();
      case AuthProviders.Msal:
        return AuthProvider.msal.getScopesForEvents();
      case AuthProviders.Okta:
        return [];
      case AuthProviders.Offline:
        return AuthProvider.offline.getScopesForEvents();
      case AuthProviders.Saml:
      case AuthProviders.Email:
      case AuthProviders.Slack:
        return [];
      default:
        throw new Error(`Unexpected provider type - ${user.provider}`);
    }
  }

  static getScopesForFiles(user: ForaUser, provider?: AuthProviders): string[][] {
    switch (provider ? provider : user.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getScopesForFiles();
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getScopesForFiles();
      case AuthProviders.Msal:
        return AuthProvider.msal.getScopesForFiles();
      case AuthProviders.Offline:
        return AuthProvider.offline.getScopesForFiles();
      case AuthProviders.Okta:
      case AuthProviders.Saml:
      case AuthProviders.Email:
      case AuthProviders.Slack:
        return [];
      default:
        throw new Error(`Unexpected provider type - ${user.provider}`);
    }
  }

  static getScopesForMessages(user: ForaUser, provider?: AuthProviders): string[][] {
    switch (provider ? provider : user.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getScopesForMessages();
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getScopesForMessages();
      case AuthProviders.Msal:
        return AuthProvider.msal.getScopesForMessages();
      case AuthProviders.Offline:
        return AuthProvider.offline.getScopesForMessages();
      case AuthProviders.Okta:
      case AuthProviders.Saml:
      case AuthProviders.Email:
      case AuthProviders.Slack:
        return [];
      default:
        throw new Error(`Unexpected provider type - ${user.provider}`);
    }
  }

  static getScopesForNotes(user: ForaUser, provider?: AuthProviders): string[][] {
    switch (provider ? provider : user.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getScopesForNotes();
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getScopesForNotes();
      case AuthProviders.Msal:
        return AuthProvider.msal.getScopesForNotes();
      case AuthProviders.Offline:
        return AuthProvider.offline.getScopesForNotes();
      case AuthProviders.Okta:
      case AuthProviders.Saml:
      case AuthProviders.Email:
      case AuthProviders.Slack:
        return [];
      default:
        throw new Error(`Unexpected provider type - ${user.provider}`);
    }
  }

  static getScopesForPeople(user: ForaUser, provider?: AuthProviders): string[][] {
    switch (provider ? provider : user.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getScopesForPeople();
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getScopesForPeople();
      case AuthProviders.Msal:
        return AuthProvider.msal.getScopesForPeople();
      case AuthProviders.Offline:
        return AuthProvider.offline.getScopesForPeople();
      case AuthProviders.Okta:
      case AuthProviders.Saml:
      case AuthProviders.Email:
      case AuthProviders.Slack:
        return [];
      default:
        throw new Error(`Unexpected provider type - ${user.provider}`);
    }
  }

  static getScopesForTasks(user: ForaUser, provider?: AuthProviders): string[][] {
    switch (provider ? provider : user.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.getScopesForTasks();
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.getScopesForTasks();
      case AuthProviders.Msal:
        return AuthProvider.msal.getScopesForTasks();
      case AuthProviders.Offline:
        return AuthProvider.offline.getScopesForTasks();
      case AuthProviders.Okta:
      case AuthProviders.Saml:
      case AuthProviders.Email:
      case AuthProviders.Slack:
        return [];
      default:
        throw new Error(`Unexpected provider type - ${user.provider}`);
    }
  }

  static getGroupName(provider: AuthProviders, group: Group): string {
    switch(provider) {
      case AuthProviders.Google: return AuthProvider.google.getGroupName(provider, group);
      case AuthProviders.Microsoft: return AuthProvider.microsoft.getGroupName(provider, group);
      case AuthProviders.Msal: return AuthProvider.msal.getGroupName(provider, group);
      case AuthProviders.Okta: return AuthProvider.okta.getGroupName(provider, group);
      case AuthProviders.Offline: return AuthProvider.offline.getGroupName(provider, group);
      case AuthProviders.Saml: return AuthProvider.saml.getGroupName(provider, group);
      case AuthProviders.Email: return AuthProvider.email.getGroupName(provider, group);
      case AuthProviders.Slack: return AuthProvider.slack.getGroupName(provider, group);
    }
  }

  static getGroupId(provider: AuthProviders, group: Group): string {
    switch(provider) {
      case AuthProviders.Google: return AuthProvider.google.getGroupId(provider, group);
      case AuthProviders.Microsoft: return AuthProvider.microsoft.getGroupId(provider, group);
      case AuthProviders.Msal: return AuthProvider.msal.getGroupId(provider, group);
      case AuthProviders.Okta: return AuthProvider.okta.getGroupId(provider, group);
      case AuthProviders.Offline: return AuthProvider.offline.getGroupId(provider, group);
      case AuthProviders.Saml: return AuthProvider.saml.getGroupId(provider, group);
      case AuthProviders.Email: return AuthProvider.email.getGroupId(provider, group);
      case AuthProviders.Slack: return AuthProvider.slack.getGroupId(provider, group);
    }
  }

  static getGroupTokens(provider: AuthProviders, group: Group): any {
    switch(provider) {
      case AuthProviders.Google: return AuthProvider.google.getGroupTokens(provider, group);
      case AuthProviders.Microsoft: return AuthProvider.microsoft.getGroupTokens(provider, group);
      case AuthProviders.Msal: return AuthProvider.msal.getGroupTokens(provider, group);
      case AuthProviders.Okta: return AuthProvider.okta.getGroupTokens(provider, group);
      case AuthProviders.Offline: return AuthProvider.offline.getGroupTokens(provider, group);
      case AuthProviders.Saml: return AuthProvider.saml.getGroupTokens(provider, group);
      case AuthProviders.Email: return AuthProvider.email.getGroupTokens(provider, group);
      case AuthProviders.Slack: return AuthProvider.slack.getGroupTokens(provider, group);
    }
  }

  static setGroupTokens(provider: AuthProviders, group: Group, tokens: any): any {
    switch(provider) {
      case AuthProviders.Google: return AuthProvider.google.setGroupTokens(provider, group, tokens);
      case AuthProviders.Microsoft: return AuthProvider.microsoft.setGroupTokens(provider, group, tokens);
      case AuthProviders.Msal: return AuthProvider.msal.setGroupTokens(provider, group, tokens);
      case AuthProviders.Okta: return AuthProvider.okta.setGroupTokens(provider, group, tokens);
      case AuthProviders.Offline: return AuthProvider.offline.setGroupTokens(provider, group, tokens);
      case AuthProviders.Saml: return AuthProvider.saml.setGroupTokens(provider, group, tokens);
      case AuthProviders.Email: return AuthProvider.email.setGroupTokens(provider, group, tokens);
      case AuthProviders.Slack: return AuthProvider.slack.setGroupTokens(provider, group, tokens);
    }
  }

  static isAccessAndProviderOnly(tokens: NormalizedProviderToken): boolean {
    return !!(tokens.access_token && tokens.provider && !tokens.expires_at && !tokens.id_token && !tokens.refresh_token && !tokens.scope && !tokens.token_type);
  }

  static isExpired(profile: string, tokens: NormalizedProviderToken): boolean {
    switch (tokens.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.isExpired(profile, tokens);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.isExpired(profile, tokens);
      case AuthProviders.Msal:
        return AuthProvider.msal.isExpired(profile, tokens);
      case AuthProviders.Okta:
        return AuthProvider.okta.isExpired(profile, tokens);
      case AuthProviders.Offline:
        return AuthProvider.offline.isExpired(profile, tokens);
      case AuthProviders.Saml:
      case AuthProviders.Email:
      case AuthProviders.Slack:
        return false;
      default:
        throw new Error(`Unexpected provider type - ${tokens.provider}`);
    }
  }

  static isValid(tokens: NormalizedProviderToken, check_refresh = true) {
    if (!tokens || !tokens.provider) {
      logging.warnF(LOG_NAME, 'isValid', `No tokens or tokens without provider`);
      return false;
    }

    switch (tokens.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.isValid(tokens, check_refresh);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.isValid(tokens, check_refresh);
      case AuthProviders.Msal:
        return AuthProvider.msal.isValid(tokens, check_refresh);
      case AuthProviders.Okta:
        return AuthProvider.okta.isValid(tokens, check_refresh);
      case AuthProviders.Offline:
        return AuthProvider.offline.isValid(tokens, check_refresh);
      case AuthProviders.Saml:
      case AuthProviders.Email:
      case AuthProviders.Slack:
        return true;
      default:
        throw new Error(`Unexpected provider type - ${tokens.provider}`);
    }
  }

  static promptRegister(context: AuthContext, group: Group, settings?: NormalizedProviderSettings, user?: ForaUser): BasicInfo[] {
    const email = user ? user.email : null;
    const url = AuthProvider.clientAuthUrl(context, group, settings, user ? email : null, true);

    const name_set = AuthProvider.clientAuthNameSet(settings.provider, group)[0];
    const name = name_set ? name_set.name : '';

    if (!this.isSupportedContext(settings.provider, context)) {
      const auth_info = this.clientAuthInfo(context, settings.provider, group);
      context = auth_info.context;
    }

    return AuthProvider.contextPermissions(context).concat(lang.init.REGISTER(url, name));
  }

  static isSupportedContext(provider: AuthProviders, context: AuthContext) {
     switch(provider) {
      case AuthProviders.Google:
        return AuthProvider.google.isSupportedContext(context);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.isSupportedContext(context);
      case AuthProviders.Msal:
        return AuthProvider.msal.isSupportedContext(context);
      case AuthProviders.Okta:
        return AuthProvider.okta.isSupportedContext(context);
      case AuthProviders.Offline:
        return AuthProvider.offline.isSupportedContext(context);
      case AuthProviders.Saml:
        return AuthProvider.slack.isSupportedContext(context);
      case AuthProviders.Email:
        return AuthProvider.email.isSupportedContext(context);
      default:
        return Topics.REG_ERROR;
    }
  }

  static connectedTopic(provider: AuthProviders, context: AuthContext, group: Group, user?: ForaUser): Topic {
    switch(provider) {
      case AuthProviders.Google:
        return AuthProvider.google.connectedTopic(provider, context, group, user);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.connectedTopic(provider, context, group, user);
      case AuthProviders.Msal:
        return AuthProvider.msal.connectedTopic(provider, context, group, user);
      case AuthProviders.Okta:
        return AuthProvider.okta.connectedTopic(provider, context, group, user);
      case AuthProviders.Offline:
        return AuthProvider.offline.connectedTopic(provider, context, group, user);
      case AuthProviders.Saml:
        return AuthProvider.slack.connectedTopic(provider, context, group, user);
      case AuthProviders.Email:
        return AuthProvider.email.connectedTopic(provider, context, group, user);
      default:
        return Topics.REG_ERROR;
    }
  }

  static urlRedirect(context: AuthContext, group: Group, settings?: NormalizedProviderSettings, user?: ForaUser): string {
    const email = user ? user.email : null;
    const url = AuthProvider.clientAuthUrl(context, group, settings, user ? email : null, true);
    return url;
  }

  static reAuthenticate(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    if (config.isEnvOffline()) return AuthProvider.offline.reAuthenticate(tokens, settings);

    switch (tokens.provider) {
      case AuthProviders.Google:
        return AuthProvider.google.reAuthenticate(tokens, settings);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.reAuthenticate(tokens, settings);
      case AuthProviders.Msal:
        return AuthProvider.msal.reAuthenticate(tokens, settings);
      case AuthProviders.Okta:
        return AuthProvider.okta.reAuthenticate(tokens, settings);
      case AuthProviders.Offline:
        return AuthProvider.offline.reAuthenticate(tokens, settings);
      case AuthProviders.Slack:
        return AuthProvider.slack.reAuthenticate(tokens, settings);
      default:
        throw new Error(`Unexpected provider type - ${tokens.provider}`);
    }
  }

  static revokeToken(provider: AuthProviders, profile: Uid, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void> {
    switch (provider) {
      case AuthProviders.Google:
        return AuthProvider.google.revokeToken(profile, provider, tokens, settings);
      case AuthProviders.Microsoft:
        return AuthProvider.microsoft.revokeToken(profile, provider, tokens, settings);
      case AuthProviders.Msal:
        return AuthProvider.msal.revokeToken(profile, provider, tokens, settings);
      case AuthProviders.Okta:
        return AuthProvider.okta.revokeToken(profile, provider, tokens, settings);
      case AuthProviders.Offline:
        return AuthProvider.offline.revokeToken(profile, provider, tokens, settings);
      case AuthProviders.Slack:
        return AuthProvider.slack.revokeToken(profile, provider, tokens, settings);
      case AuthProviders.Email:
      case AuthProviders.Saml:
        return;
      default:
        throw new Error(`Unexpected provider type - ${provider}`);
    }
  }

  static async tokensFromOauthCode(code: string, settings: NormalizedProviderSettings, scopes?: string[]): Promise<NormalizedProviderToken> {
    let tokens: NormalizedProviderToken;

    if (logging.isDebug()) logging.debugF(LOG_NAME, 'tokensFromOauthCode', `code = ${util.format(code)}`);
    switch (settings.provider) {
      case AuthProviders.Google:
        tokens = await AuthProvider.google.getAuthTokenFromCode(code, settings);
        break;
      case AuthProviders.Microsoft:
        tokens = await AuthProvider.microsoft.getAuthTokenFromCode(code, settings);
        break;
      case AuthProviders.Msal:
        tokens = await AuthProvider.msal.getAuthTokenFromCode(code, settings, scopes);
        break;
      case AuthProviders.Okta:
        tokens = await AuthProvider.okta.getAuthTokenFromCode(code, settings);
        break;
      case AuthProviders.Offline:
        tokens = await AuthProvider.offline.getAuthTokenFromCode(code, settings);
        break;
      case AuthProviders.Slack:
        tokens = await AuthProvider.slack.getAuthTokenFromCode(code, settings);
        break;
      default:
        throw new Error(`Unexpected provider type - ${settings.provider}`);
    }

    if (logging.isDebug()) logging.debugF(LOG_NAME, 'tokensFromOauthCode', `tokens = ${logging.formatToken(tokens)}`);

    return tokens;
  }
}
