/*************************************************
 * Google Auth
 *
 * http://google.github.io/google-api-nodejs-client/modules/_apis_oauth2_v2_.html
 * https://github.com/google/google-api-nodejs-client/blob/master/src/apis/oauth2/v2.ts
 *************************************************/

import { OAuth2Client } from 'google-auth-library';

import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Group } from '../types/group';
import { Person } from '../types/items';
import { OAuthConfig } from '../types/oauth';
import { AuthClientInfo, Auth<PERSON>ontext, AuthLevel, AuthProviders, DisallowableAuth<PERSON>ontext, Uid } from '../types/shared';

import { HOURS } from '../utils/datetime';

import { AbstractAuthenticationProvider } from './a_authentication_provider';

export enum OfflineScopes {
  Calendar_ReadWrite = 'https://www.askfora.com/auth/calendar',
  Contacts_ReadWrite = 'https://www.askfora.com/auth/contacts',
  Email = 'email',
  Files_ReadWrite_AppFolder = 'https://www.askfora.com/auth/drive.appdata',
  Mail_ReadWrite = 'https://www.askfora.com/auth/mail.modify',
  Profile = 'profile',
  Tasks_ReadWrite = 'https://www.askfora.com/auth/tasks',
}

export class OfflineAuthenticationProvider extends AbstractAuthenticationProvider {
  log_name = 'data.auth.OfflineAuthenticationProvider';

  getAuthClient(tokens?: NormalizedProviderToken, settings?: NormalizedProviderSettings): OAuth2Client {
    return null;
  }

  getAuthClientInfo(context: AuthContext, group: Group, settings?: NormalizedProviderSettings): AuthClientInfo {
    return {
      name: 'Offline',
      clientId: settings ? settings.oauth.client_id : 'OFFLINE_CLIENT_ID',
      scope: this.getAuthScopes(context, group),
      access_type: this.groupContains(group, DisallowableAuthContext.Offline) ? 'online' : 'offline',
      context,
      provider: AuthProviders.Offline,
    };
  }

  async getAuthTokenFromCode(code: string, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    const token = new NormalizedProviderToken(AuthProviders.Offline, 'offline', AuthLevel.Organizer);
    token.expires_at = HOURS(new Date(), 1);
    token.id_token = 'offline';
    token.refresh_token = 'offline';
    token.scope = this.SCOPES_ORGANIZER.join(' ');
    token.token_type = 'Bearer';

    const user = await this.getProviderUserNormalized(token, settings);
    token.email = user.email;

    return token;
  }

  getAuthUrl(context: AuthContext, group: Group, email: string = null, scope: string[] = null, force_consent = false, force_selection = true): string {
    const state = `${context} offline ${group ? group.id : ''}`;
    return `https://askfora.com/oauth/offline?state=${encodeURIComponent(state)}`;
  }

  getDefaultProviderSettings(): NormalizedProviderSettings {
    return new NormalizedProviderSettings(AuthProviders.Offline, new OAuthConfig('OFFLINE_CLIENT_ID', 'OFFLINE_CLIENT_SECRET', 'https://localhost/goauth'));
  }

  async getProviderUserNormalized(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser> {
    return JSON.parse(process.env.OFFLINE_AUTH_PROVIDER_USER) as NormalizedProviderUser;
  }

  isValid(token: NormalizedProviderToken, check_refresh = true): boolean {
    if (process.env.OFFLINE_AUTH_PROVIDER_VALID_TOKEN) {
      // Unit test is overriding so it can control what a valid token is
      return token && token.access_token === process.env.OFFLINE_AUTH_PROVIDER_VALID_TOKEN;
    } else return super.isValid(token, check_refresh);
  }

  async loadSelfPerson(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<Person> {
    return null;
  }

  async reAuthenticate(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    const token = new NormalizedProviderToken(AuthProviders.Offline, 'offline', AuthLevel.Organizer);
    token.expires_at = HOURS(new Date(), 1);
    token.id_token = 'id_token';
    token.refresh_token = 'refresh_token';
    token.scope = this.SCOPES_ALL.join(' ');
    token.token_type = 'Bearer';

    return token;
  }

  async revokeToken(profile: Uid, provider: AuthProviders, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void> { 
    console.warn('Revoke token not implemented for offline auth');
  }

  protected setupScopes(): void {
    this.SCOPES_CALENDAR = [OfflineScopes.Calendar_ReadWrite];
    this.SCOPES_CALENDAR_SYNC = [OfflineScopes.Calendar_ReadWrite];
    this.SCOPES_CONTACT = [OfflineScopes.Contacts_ReadWrite];
    this.SCOPES_CONTACT_SYNC = [OfflineScopes.Contacts_ReadWrite];
    this.SCOPES_FILE = [OfflineScopes.Files_ReadWrite_AppFolder];
    this.SCOPES_MAIL = [OfflineScopes.Mail_ReadWrite];
    this.SCOPES_MAIL_SYNC = [OfflineScopes.Mail_ReadWrite];
    this.SCOPES_NOTE = [OfflineScopes.Files_ReadWrite_AppFolder];
    this.SCOPES_TASK = [OfflineScopes.Tasks_ReadWrite];
    this.SCOPES_TASK_SYNC = [OfflineScopes.Tasks_ReadWrite];
    this.SCOPES_USER = [OfflineScopes.Email, OfflineScopes.Profile];
  }
}
