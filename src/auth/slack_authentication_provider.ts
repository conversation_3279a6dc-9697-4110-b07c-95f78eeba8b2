import { InstallProvider } from '@slack/oauth';

import config from '../config';

import { Topics } from '../session/dialog';
import ForaUser from '../session/user';

import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Topic } from '../types/globals';
import { Group } from '../types/group';
import { Person } from '../types/items';
import { OAuthConfig } from '../types/oauth';
import { AuthClientInfo, AuthContext, AuthProviders, Uid } from '../types/shared';

import logging from '../utils/logging';

import { AbstractAuthenticationProvider } from './a_authentication_provider';

import { WebClient } from '@slack/web-api';

const DEBUG = (require('debug') as any)('fora:auth:slack');

/* Connect
{
  "access_token": "*****************************************************",
  "token_type": "bot",
  "scope": "commands,incoming-webhook",
  "bot_user_id": "U0KRQLJ9H",
  "app_id": "A0KRD7HC3",
  "team": {
      "name": "Slack Softball Team",
      "id": "T9TK3CUKW"
  },
  "enterprise": {
      "name": "slack-sports",
      "id": "E12345678"
  },
}
*/

/* Sign in, we move authed_user up
{
  "ok": true,
  "app_id": "A01FGTMPRPY",
  "authed_user": {
    "id": "UA45A2M9A",
    "scope": "identity.basic,identity.email",
    "access_token": "*****************************************************************************",
    "token_type": "user"
  },
  "team": {
    "id": "T9A6NNFDW"
  },
  "enterprise": null,
  "response_metadata": {}
}
*/

enum SlackBotScopes {
  AppMentions_Read = 'app_mentions:read',
  Channels_History = 'channels:history',
  Chat_Write = 'chat:write',
  Chat_Write_Customize = 'chat:write.customize',
  Commands = 'commands',
  Groups_History = 'groups:history',
  Im_History = 'im:history',
  Im_Read = 'im:read',
  Im_Write = 'im:write',
  Links_Read = 'links:read',
  Mpim_History = 'mpim:history',
  Reminders_Read = 'reminders:read',
  Reminders_Write = 'reminders:write',
  Team_Read = 'team:read',
  Users_Profile_Read = 'users.profile:read',
  Users_Read = 'users:read',
  Users_Read_Email = 'users:read.email',
}

enum SlackUserScopes {
  Identity = 'identity.basic',
  Identity_email = 'identity.email',
}

export class SlackAuthenticationProvider extends AbstractAuthenticationProvider {
  log_name = 'data.auth.SlackAuthenticationProvider';

  ensureTokenTyped(token: any): NormalizedProviderToken {
    if (token) {
      // doesn't expire
      token['expires_at'] = new Date(1000000 * 1000000 * 5);
    }

    return super.ensureTokenTyped(token);
  }

  expiresAt(token: NormalizedProviderToken): Date {
    if (token) {
      return new Date(1000000 * 1000000 * 5);
    } return new Date(0);
  }

  connectedTopic(provider: AuthProviders, context: AuthContext, group: Group, user?: ForaUser): Topic {
    if (user.isAuthenticated() && !user.isGuestAccount() && user.hasAccount(provider)) return Topics.SLACK_WELCOME;
    else if(group.accounts && group.accounts['slack']) return Topics.SLACK_ADDED;
    else return Topics.REG_ERROR;
  }

  getContext(group: Group): AuthContext {
    const slack = this.getGroupTokens(AuthProviders.Slack, group);
    if (slack) return AuthContext.App;
    return AuthContext.AuthAPI;
  }

  getGroupName(provider: AuthProviders, group: Group): string {
    const slack = this.getGroupTokens(AuthProviders.Slack, group);
    let name = 'Slack';
    if (slack) {
      if (slack['team']) name = slack['team'].name;
      else if(slack['enterprise']) name = slack['enterprise'].name;
    }
    return name;
  }

  getGroupId(provider: AuthProviders, group: Group): string {
    const slack = this.getGroupTokens(AuthProviders.Slack, group);
    let id = null;
    if (slack) {
      if (slack['team']) id = slack['team'].id;
      else if(slack['enterprise']) id = slack['enterprise'].id;
    }
    return id;
  }

  getGroupIsEnterprise(group: Group): boolean {
    const slack = this.getGroupTokens(AuthProviders.Slack, group); 
    return slack && slack['enterprise'] !== null && slack['enterprise'] !== undefined;
  }

  getGroupTokens(provider: AuthProviders, group: Group): any {
    return  group && group.accounts && group.accounts.slack && Object.values(group.accounts.slack).length ? Object.values(group.accounts.slack)[0] : null;
  }

  setGroupTokens(provider: AuthProviders, group: Group, tokens: any): any {
    const id = tokens['enterprise'] ? tokens['enterprise'].id : tokens['team'] ? tokens['team'].id : null;
    if (!id) {
      logging.warnF(this.log_name, 'setGroupTokens', `Invalid slack tokens - no enterprise or team: ${JSON.stringify(tokens)}`);
      throw new Error('Invalid slack tokens - no enterprise or team');
    }
    if (!group.accounts) group.accounts = {};
    if (!group.accounts[AuthProviders.Slack]) group.accounts[AuthProviders.Slack] = {}
    group.accounts[AuthProviders.Slack][id] = tokens;
    if (!group.auth_ids) group.auth_ids = [];
    group.auth_ids.push(`${AuthProviders.Slack}_${id}`);
  }

  getAuthClient(tokens?: NormalizedProviderToken, settings?: NormalizedProviderSettings): any {
    const installer = new InstallProvider({
      clientId: settings.oauth.client_id,
      clientSecret: settings.oauth.client_secret,
    });
    return installer;
  }

  getAuthClientInfo(context: AuthContext, group: Group, settings?: NormalizedProviderSettings): AuthClientInfo {
    return {
      name: 'Slack',
      clientId: settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : config.get('SLACK_OAUTH2_CLIENT_ID'),
      scope: this.getAuthScopes(context, group),
      context,
      provider: AuthProviders.Slack,

    }
  }

  getAuthContext(user: ForaUser, provider?: AuthProviders, profile?: string, group?: Group): AuthContext {
    // login if group is connected
    if (group && group.accounts && group.accounts['slack']) return AuthContext.App;

    // connect if not connected and admin
    if ((!user.accounts['slack'] || !user.accounts['slack'].length) && group && user.isAdmin(group.id)) return AuthContext.AuthAPI;

    return null;
  }

  async getAuthTokenFromCode(code: string, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    let web = new WebClient();
    const raw_tokens = await web.oauth.v2.access({code, client_id: settings.oauth.client_id, client_secret: settings.oauth.client_secret, redirect_uri: settings.oauth.client_callback});
    const tokens = raw_tokens.token_type === 'bot' ? raw_tokens : 
      {
        ok: raw_tokens.ok,
        app_id: raw_tokens.app_id,
        team: raw_tokens.team,
        enterprise: raw_tokens.enterprise,
        response_metadata: raw_tokens.response_metadata,
        id: raw_tokens.authed_user.id,
        scope: raw_tokens.authed_user.scope,
        access_token: raw_tokens.authed_user.access_token,
        token_type: raw_tokens.authed_user.token_type,
      };
    tokens.provider = AuthProviders.Slack;
    const teamId = tokens['enterprise'] ? tokens['enterprise'].id : tokens['team'] ? tokens['team'].id : null;
    web = new WebClient(tokens.access_token, { teamId });

    if (tokens.token_type === 'bot') {
      const info = await web.auth.test();
      if (info) {
        tokens.bot_id = info.bot_id;
      } else {
        logging.warnF(this.log_name, 'getAuthTokenFromCode', `No info returned from auth test`);
      }
    } else {
      const user = await this.getProviderUserNormalized(tokens as any, settings);
      if (user) tokens.email = user.email;
    }

    return tokens as any;
  }

  getAuthUrl(context: AuthContext, group: Group, email: string = null, scope: string[] = null, force_consent = false, force_selection = true): string {
    const defaults = this.getDefaultProviderSettings();
    let settings: NormalizedProviderSettings = group ? group.providerSettings(AuthProviders.Saml) : null;
    if (!settings) settings = defaults;

    const client_id = settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : defaults.oauth.client_id;
    const client_callback = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;
    const client_secret = settings && settings.oauth && settings.oauth.client_secret ? settings.oauth.client_secret : defaults.oauth.client_secret;

    // not using slack lib because it returns a promise
    const url = new URL('https://slack.com/oauth/v2/authorize');
    const params = new URLSearchParams();
    if (context === AuthContext.AuthAPI) params.append('scope', this.getAuthScopes(context, group, scope));
    else {
      params.append('user_scope', this.getAuthScopes(context, group, scope));
      params.append('team', this.getGroupId(AuthProviders.Slack, group));
    }
    params.append('client_id', client_id);
    params.append('state', `${context} slack ${group ? group.id : ''}`);
    params.append('redirect_uri', client_callback);
    
    url.search = params.toString();
    return url.toString();
  }

  getDefaultProviderSettings(): NormalizedProviderSettings {
    return new NormalizedProviderSettings(AuthProviders.Slack, new OAuthConfig(config.get('SLACK_OAUTH2_CLIENT_ID'), config.get('SLACK_OAUTH2_CLIENT_SECRET'), config.get('SLACK_OAUTH2_CALLBACK')));
  }

  async getProviderUserNormalized(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser> {
    if (tokens.token_type === 'bot') return null;

    const teamId = tokens['enterprise'] ? tokens['enterprise'].id : tokens['team'] ? tokens['team'].id : null;
    const web = new WebClient(tokens.access_token, { teamId });

    const user_info = (await web.users.identity() as any);
    return new NormalizedProviderUser(`${user_info.enterprise ? user_info.enterprise.id : user_info.team.id}_${user_info.user.id}`, user_info.user.email, user_info.user.name);
  }

  async loadSelfPerson(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<Person> {
    if (tokens.token_type === 'bot') return null;

    const teamId = tokens['enterprise'] ? tokens['enterprise'].id : tokens['team'] ? tokens['team'].id : null;
    const web = new WebClient(tokens.access_token, { teamId });
    
    const user_info = (await web.users.identity()) as any;
    return new Person({
      displayName: user_info.user.name,
      id: `${user_info.enterprise ? user_info.enterprise.id : user_info.team.id}_${user_info.user.id}`,
      comms: [user_info.user.email],
      learned: [new Date(0)],
    });
  }

  async reAuthenticate(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    return tokens;
  }

  async revokeToken(profile: Uid, provider: AuthProviders, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void> {
    const teamId = tokens['enterprise'] ? tokens['enterprise'].id : tokens['team'] ? tokens['team'].id : null;
    const web = new WebClient(tokens.access_token, { teamId });
    return (web.auth.revoke({token: tokens.access_token}) as any);
  }

  protected setupScopes(): void {
    this.SCOPES_CONNECT = Object.values(SlackBotScopes);
    this.SCOPES_USER = Object.values(SlackUserScopes);
  }
}