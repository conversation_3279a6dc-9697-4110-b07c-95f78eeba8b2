/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client, Issuer, TokenSet } from 'openid-client';
import config from '../config';
import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Group } from '../types/group';
import { Person, Tag } from '../types/items';
import { OAuthConfig } from '../types/oauth';
import { AuthClientInfo, AuthContext, AuthLevel, AuthProviders, DisallowableAuthContext, TagType, Uid } from '../types/shared';
import * as funcs from '../utils/funcs';
import logging from '../utils/logging';
import { AbstractAuthenticationProvider } from './a_authentication_provider';

const DEBUG = (require('debug') as any)('fora:auth:okta');

export enum OktaScopes {
  OpenId = 'openid',
  // Self = 'self',
  Email = 'email',
  Email_Read = 'okta.myAccount.email.read',
  Phone_Read ='okta.myAccount.phone.read',
  Profile_Read ='okta.myAccount.profile.read',
  Account_Read ='okta.myAccount.read',
  Profile ='profile',
  Address ='address',
  Phone ='phone',
  Offline_Access ='offline_access',
  Device_Sso = 'device_sso',
}

export class OktaAuthenticationProvider extends AbstractAuthenticationProvider {
  log_name = 'data.auth.OktaAuthenticationProvider';

  async getAuthClient(tokens?: NormalizedProviderToken, settings?: NormalizedProviderSettings, refresh = false): Promise<Client> {
    const defaults = this.getDefaultProviderSettings();
    if (!settings) settings = defaults;

    const issuer = settings && settings.oauth && settings.oauth.client_issuer ? settings.oauth.client_issuer : defaults.oauth.client_issuer;
    const client_id = settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : defaults.oauth.client_id;
    const redirect_uri = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;
    const client_secret = settings && settings.oauth && settings.oauth.client_secret ? settings.oauth.client_secret : defaults.oauth.client_secret;

    const okta_issuer = await Issuer.discover(issuer);
    const client = await new okta_issuer.Client({
      client_id,
      client_secret, 
      redirect_uris: [redirect_uri], 
      response_types: ['code']
    });

    return client;
  }

  getAuthClientInfo(context: AuthContext, group: Group, settings?: NormalizedProviderSettings): AuthClientInfo {
    return {
      name: 'Okta',
      clientId: settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : config.get('OKTA_OAUTH2_CLIENT_ID'),
      scope: this.getAuthScopes(context, group),
      access_type: this.groupContains(group, DisallowableAuthContext.Offline) ? 'online' : 'offline',
      context,
      provider: AuthProviders.Okta,
    }
  }


  async getAuthTokenFromCode(code: string, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    const client: Client = await this.getAuthClient(null, settings);
    const defaults = this.getDefaultProviderSettings();
    const redirect_uri = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;
    
    const token: TokenSet = await client.callback(redirect_uri, {code});

    const npt = new NormalizedProviderToken(AuthProviders.Okta, token.access_token, AuthLevel.Basic);
    npt.expires_at = new Date(token.expires_at * 1000)
    npt.id_token = token.id_token;
    // npt.refresh_token = token.refresh_token;
    npt.scope = token.scope;
    npt.token_type = token.token_type;

    const user = await client.userinfo(token.access_token);
    npt.email = user.email;
    return npt;
  }

  getAuthUrl(context: AuthContext, group: Group, email: string = null, scopes: string[] = null, force_consent = false, force_selection = true): string {
    const defaults = this.getDefaultProviderSettings();
    let settings: NormalizedProviderSettings = group ? group.providerSettings(AuthProviders.Okta) : defaults;
    if (!settings) settings = defaults;
    const issuer = settings && settings.oauth && settings.oauth.client_issuer ? settings.oauth.client_issuer : defaults.oauth.client_issuer;
    const client_id = settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : defaults.oauth.client_id;
    const redirect_uri = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;

    let auth_url = settings && settings.oauth && settings.oauth.auth_url ? settings.oauth.auth_url : defaults.oauth.auth_url;
    if (!auth_url) auth_url = issuer;

    const state = `${context} ${AuthProviders.Okta} ${group ? group.id : ''}`;
    const scope = this.getAuthScopes(context, group, scopes);
    const access_type = 'offline';
    const response_type = 'code';

    const url_params = {
      client_id,
      scope,
      state,
      redirect_uri,
      access_type,
      response_type,
    }

    const params = new URLSearchParams();

    for (const att in url_params) params.append(att, url_params[att]);

    const url = new URL(`${auth_url}/v1/authorize`);
    url.search = params.toString();

    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthUrl', `Generated url = ${url}`);
    return url.toString();
  }

  getDefaultProviderSettings(): NormalizedProviderSettings {
    return new NormalizedProviderSettings(AuthProviders.Okta, new OAuthConfig(config.get('OKTA_OAUTH2_CLIENT_ID'), config.get('OKTA_OAUTH2_CLIENT_SECRET'), config.get('OKTA_OAUTH2_CALLBACK'), config.get('OKTA_OAUTH2_ISSUER')));
  }

  getGroupName(provider: AuthProviders, group: Group): string {
    if (group) {
      if (group.company_name) return group.company_name;
      if (group.name) return group.name;
    }
    return 'Okta';
  }

  async getProviderUserNormalized(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser> {
    const client: Client = await this.getAuthClient(null, settings);

    try {
      const user = await client.userinfo(tokens.access_token);
      /*
      user.name
      user.locale,
      user.email,
      user.preferred_username,
      user.given_name,
      user.family_name,
      user.zoneinfo
      */
      return new NormalizedProviderUser(`o_${funcs.hash(user.preferred_username).toLowerCase()}`, user.email, user.given_name);
    } catch(err) {
      if (err.message && err.message.includes('No access, refresh token, API key or refresh handler callback is set.')) logging.warnF(this.log_name, 'getProviderUserNormalized', `Error getting provider user`, err);
      else logging.errorF(this.log_name, 'getProviderUserNormalized', `Error getting provider user`, err);
      return null;
    }
  }

  async loadSelfPerson(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<Person> {
    const client: Client = await this.getAuthClient(null, settings);

    const user = await client.userinfo(tokens.access_token);
    const person: Person = new Person({
      id: `people/o_${funcs.hash(user.preferred_username)}`,
      displayName: `${user.given_name} ${user.family_name}`,
      comms: [user.email],
      tags: [new Tag(TagType.skill, 'self', 0, new Date())],
      learned: [new Date(0)],
    });

    return person;
  }

  isValid(token: NormalizedProviderToken, check_refresh = true): boolean {
    if (logging.isDebug()) DEBUG('isValid: %o', !!(token && token['access_token']));
    const is_valid = !!(token && token['access_token']);
    if(logging.isDebug()) logging.debugF(this.log_name, 'isValid', `${is_valid ? '' : 'NOT '}Valid ${JSON.stringify(token)}`);
    return is_valid;
  }

  async reAuthenticate(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    return tokens;

    /*
    const client: Client = await this.getAuthClient(null, settings);

    if (tokens.access_token) {
      const token = await client.refresh(tokens.refresh_token);
      const npt = new NormalizedProviderToken(AuthProviders.Okta, token.access_token, AuthLevel.Basic);
      npt.expires_at = new Date(token.expires_at * 1000)
      npt.id_token = token.id_token;
      // npt.refresh_token = token.refresh_token;
      npt.scope = token.scope;
      npt.token_type = token.token_type;
      npt.email = tokens.email;
      return npt;
    }
    return null;*/
  }

  async revokeToken(profile: Uid, provider: AuthProviders, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void> {
    const client: Client = await this.getAuthClient(null, settings);

    if (tokens.access_token) {
      try {
        await client.revoke(tokens.access_token);
      } catch(err) {
        logging.warnFP(this.log_name, 'revokeToken', profile, 'Manual revoke of access token failed', err);
        throw err;
      }
    }

  }

  protected setupScopes(): void {
    this.SCOPES_CALENDAR = [];
    this.SCOPES_CONTACT = [];
    this.SCOPES_FILE = [];
    this.SCOPES_MAIL = [];
    this.SCOPES_NOTE = [];
    this.SCOPES_TASK = [];
    this.SCOPES_USER = [
      OktaScopes.OpenId,
      // OktaScopes.Self,
      OktaScopes.Email,
      OktaScopes.Email_Read,
      OktaScopes.Phone_Read,
      OktaScopes.Profile_Read,
      OktaScopes.Account_Read,
      OktaScopes.Profile,
      OktaScopes.Address,
      OktaScopes.Phone,
      OktaScopes.Offline_Access,
      // OktaScopes.Device_Sso,
   ];
  }
}
