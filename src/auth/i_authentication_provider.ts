/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import ForaUser from '../session/user';
import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Topic } from '../types/globals';
import { Group } from '../types/group';
import { Person } from '../types/items';
import { AuthClientInfo, AuthContext, AuthProviders, Uid } from '../types/shared';

export interface IAuthenticationProvider {
  /**
   * Ensure the given token is strongly typed
   *
   * @param merged_token - Tokens to check - should conform to NormalizedProviderToken
   */
  ensureTokenTyped(merged_token: any): NormalizedProviderToken;

  /**
   * Gets the expiration date of the provided token
   *
   * @param token - Tokens for the user we are authenticating as.
   */
  expiresAt(token: NormalizedProviderToken): Date;

  /** 
   * Gets the topic after logging in
   *
   * @param provider - the provider
   * @param context - the context
   * @param group - the group
   * @param settings - the settings
   * @param user - the user
   */
  connectedTopic(provider: AuthProviders, context: AuthContext, group: Group, user?: ForaUser): Topic;

  /**
   * Get our OAuth client (with token set if provided)
   *
   * @param token - Tokens for the user we are authenticating as.
   * @param settings - Normalized provider settings
   */
  getAuthClient(token?: NormalizedProviderToken, settings?: NormalizedProviderSettings): any;

  /**
   * Get all needed information for an external entity to build a client to this provider
   *
   * @param context - context requested (used to build scopes)
   * @param group - Group running for/with (host locked)
   * @param settings - Normalized provider settings
   */
  getAuthClientInfo(context: AuthContext, group: Group, settings?: NormalizedProviderSettings): AuthClientInfo;

  /**
   * Get the AuthContext that matches the scopes in the given Token set
   *
   * @param token - Existing token
   * @returns AuthContext
   */
  getAuthContext(user: ForaUser, provider?: AuthProviders, profile?: Uid, group?: Group): AuthContext;

  /**
   * Get the list of scopes that are required based upon how the given user is authenticated
   *
   * @param user - ForaUser to convert to required scopes
   * @param group - Group running for/with (host locked)
   * @returns Array of scope values for this provider
   */
  getAuthRequiredScope(user: ForaUser, provider: AuthProviders, profile: Uid, group: Group): string[];

  /**
   * Authenticate with Provider (Exchanges the given authentication code for token (access, refresh, etc)).
   *
   * @param code The authorization code.
   * @param settings - Normalized provider settings
   * @return NormalizedProviderToken.
   * @async
   */
  getAuthTokenFromCode(code: string, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken>;

  /**
   * Generates URL for consent page landing.
   *
   * @param context - The context/types of permissions that we are requesting on behalf of the user.
   * @param group - Group running for/with
   * @param settings - Optional - Normalized provider settings
   * @param email - Optional - Email of the user we are generating the URL for/about
   * @param scope - Optional - List of scopes to start with
   * @param force_consent - Optional - Do we need to ask the authorization server to force the user to re-consent
   * @param force_selection - Optional - Do we need to ask the authorization server to force the user to select their account again
   * @return URL to consent page
   */
  getAuthUrl(context: AuthContext, group: Group, email?: string, scope?: string[], force_consent?: boolean, force_selection?: boolean): string;

  /**
   * Get the default settings for this provider
   */
  getDefaultProviderSettings(): NormalizedProviderSettings;

  /**
   * Get information about the authenticated user from the Provider
   *
   * @param token - NormalizedProviderToken for the user we are authenticating as
   * @param settings - Normalized provider settings
   * @return Current user info
   * @async
   */
  getProviderUserNormalized(token: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser>;

  getScopesForEvents(): string[][];

  getScopesForMessages(): string[][];

  getScopesForNotes(): string[][];

  getScopesForPeople(): string[][];

  getScopesForTasks(): string[][];

  getScopesForUsers(): string[][];

  getGroupName(provider: AuthProviders, group: Group): string;

  getGroupId(provider: AuthProviders, group: Group): string;

  getGroupTokens(provider: AuthProviders, group: Group): any;

  setGroupTokens(provider: AuthProviders, group: Group, tokens: any): any;

  /**
   * Check if the given users token are expired (for this provider).
   *
   * @param profile - Profile the token belong to
   * @param token - token to check
   */
  isExpired(profile: string, token: NormalizedProviderToken): boolean;

  /**
   * Are the provided token valid? This check just checks for required token (`access_token`) and
   * optionally `refresh_token`
   *
   * @param token - token to validate
   * @param check_refresh - check if `refresh_token` is present and if not, return false
   */
  isValid(token: NormalizedProviderToken, check_refresh?: boolean): boolean;

  /**
   * Load the 'me' record from the Provider
   *
   * @param token
   * @param settings - Normalized provider settings
   * @return the person record we created
   * @async
   */
  loadSelfPerson(token: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<Person>;

  /**
   * Re-authenticate with Provider (Retrieves the access token using refresh token).
   *
   * @param token - NormalizedProviderToken for the user we are authenticating as.
   * @param settings - Normalized provider settings
   * @return NormalizedProviderToken.
   * @async
   */
  reAuthenticate(token: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken>;

  /**
   * Reconcile all token to determine which (or merged) is the correct one to use.
   *
   * @param settings - Normalized provider settings
   * @param a_side - dictionary of `A` side token
   * @param b_side - dictionary of `B` side token
   * @param revoke - Optional setting to revoke any bad or old token (defaults to true)
   */
  reconcileExistingTokens(a_side: { [key: string]: NormalizedProviderToken }, b_side: { [key: string]: NormalizedProviderToken }, settings?: NormalizedProviderSettings, revoke?: boolean): { [key: string]: NormalizedProviderToken };

  /**
   * Revoke the given users tokens within the provider
   *
   * @param user - User to revoke tokens for
   * @param settings - Normalized provider settings
   * @async
   */
  revokeToken(profile: Uid, provider: AuthProviders, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void>;
}
