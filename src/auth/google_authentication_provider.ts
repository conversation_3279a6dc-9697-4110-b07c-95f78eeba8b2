/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import axios from 'axios';
import bigInt from 'big-integer';
import { LoginTicket, OAuth2Client } from 'google-auth-library';
import { GenerateAuthUrlOpts, GetTokenResponse, RefreshAccessTokenResponse } from 'google-auth-library/build/src/auth/oauth2client';
import { google, oauth2_v2, people_v1 } from 'googleapis';
import { setTimeout } from "timers/promises";
import util from 'util';

import config from '../config';

import { GoogleConverter } from '../sources/helpers/google/google_conversions';

import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Group } from '../types/group';
import { Person, Tag } from '../types/items';
import { OAuthConfig } from '../types/oauth';
import { AuthClientInfo, AuthContext, AuthProviders, DisallowableAuthContext, TagType, Uid } from '../types/shared';

import logging from '../utils/logging';

import { AbstractAuthenticationProvider } from './a_authentication_provider';

const oauth2 = google.oauth2('v2');
const people = google.people('v1');

const DEBUG = (require('debug') as any)('fora:auth:google');

export enum GoogleScopes {
  Calendar_ReadWrite = 'https://www.googleapis.com/auth/calendar',
  Calendar_Read = 'https://www.googleapis.com/auth/calendar.readonly',
  // Directory = 'https://admin.googleapis.com/admin/directory/v1/users.readonly',
  Directory = 'https://www.googleapis.com/auth/admin.directory.user.readonly',
  Contacts_ReadWrite = 'https://www.googleapis.com/auth/contacts',
  Contacts_Read = 'https://www.googleapis.com/auth/contacts.readonly',
  Email = 'email',
  Files_ReadWrite_AppFolder = 'https://www.googleapis.com/auth/drive.appdata',
  Mail_ReadWrite = 'https://www.googleapis.com/auth/gmail.modify',
  Mail_Read = 'https://www.googleapis.com/auth/gmail.readonly',
  Profile = 'profile',
  Tasks_ReadWrite = 'https://www.googleapis.com/auth/tasks',
  Tasks_Read= 'https://www.googleapis.com/auth/tasks.readonly',
}

/*
 * Authentication provider for Google
 *
 * Notes:
 * - Refresh tokens are returned on the first exchange, you need to persist them permanently. The latter exchanges will only
 *   return an access token. In order to receive a new refresh token, revoke user's existing access token and go through the
 *   auth flow again. [https://github.com/google/google-api-nodejs-client/issues/52]
 *
 * Source Hints:
 * - http://google.github.io/google-api-nodejs-client/modules/_apis_oauth2_v2_.html
 * - https://github.com/google/google-api-nodejs-client/blob/master/src/apis/oauth2/v2.ts
 *
 * Interesting articles:
 * - https://blog.timekit.io/google-oauth-invalid-grant-nightmare-and-how-to-fix-it-9f4efaf1da35
 *
 * Consent Management Pages:
 * - https://myaccount.google.com/permissions
 *
 * Expired Token Reasons:
 * - The user has revoked your app's access
 * - The refresh token has not been used for six months
 * - The user changed passwords and the refresh token contains Gmail scopes
 * - The user account has exceeded a maximum number of granted (live) refresh tokens (50 is the limit as of 201801)
 * - There is also a larger limit on the total number of refresh tokens a user account or service account can have
 *   across all clients. Most normal users won't exceed this limit but a developer's test account might.
 * - Source https://developers.google.com/identity/protocols/OAuth2#expiration
 */
export class GoogleAuthenticationProvider extends AbstractAuthenticationProvider {
  // prettier-ignore
  private static readonly PERSON_FIELDS = [
    'names', 'nicknames', 'organizations', 'occupations', 'skills', 'interests', 'braggingRights',
    'emailAddresses', 'imClients', 'phoneNumbers', 'relations', 'events', 'urls', 'metadata', 'photos',
  ].join(',');
  private static readonly URL_REVOKE = 'https://accounts.google.com/o/oauth2/revoke?';
  // private static readonly URL_REVOKE = 'https://oauth2.google.com/revoke';
  log_name = 'data.auth.GoogleAuthenticationProvider';

  ensureTokenTyped(token: any): NormalizedProviderToken {
    if (token) {
      // Move the expiry_date from old records stored in DS over to the new field
      if (token['expiry_date']) {
        token['expires_at'] = token['expiry_date'];
        delete token['expiry_date'];
      }

      // Make sure that we have a proper provider
      token['provider'] = AuthProviders.Google;
    }

    return super.ensureTokenTyped(token);
  }

  expiresAt(token: NormalizedProviderToken): Date {
    if (token) {
      if (token['expiry_date']) {
        const expiry_date = (token as any).expiry_date;
        if (expiry_date) return new Date(expiry_date);
        else return new Date(0);
      } else return super.expiresAt(token);
    } else return new Date(0);
  }

  getAuthClient(tokens?: NormalizedProviderToken, settings?: NormalizedProviderSettings, refresh = false): OAuth2Client {
    // Use the settings from the ENV if the user didn't explicitly tell us not to
    const defaults = this.getDefaultProviderSettings();
    if (!settings) settings = defaults;

    const client_id = settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : defaults.oauth.client_id;
    const client_callback = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;
    const client_secret = settings && settings.oauth && settings.oauth.client_secret ? settings.oauth.client_secret : defaults.oauth.client_secret;

    if (logging.isDebug()) {
      logging.debugF(this.log_name, 'getAuthClient', `OAUTH2_CLIENT_ID = ${client_id}`);
      logging.debugF(this.log_name, 'getAuthClient', `OAUTH2_CALLBACK = ${client_callback}`);
      logging.debugF(this.log_name, 'getAuthClient', `tokens normalized = ${logging.formatToken(tokens)}`);
    }

    const oauth2Client: OAuth2Client = new OAuth2Client(client_id, client_secret, client_callback);
    if (tokens) {
      if (this.isValid(tokens, refresh)) oauth2Client.setCredentials(GoogleConverter.convertNormalizedProviderTokenToCredentials(tokens));
      else logging.warnF(this.log_name, 'getAuthClient', 'Ignorning invalid tokens');
    }
    return oauth2Client;
  }

  getAuthClientInfo(context: AuthContext, group: Group, settings?: NormalizedProviderSettings): AuthClientInfo {
    return {
      name: 'Google',
      clientId: settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : config.get('GOOGLE_OAUTH2_CLIENT_ID'),
      scope: this.getAuthScopes(context, group),
      access_type: this.groupContains(group, DisallowableAuthContext.Offline) ? 'online' : 'offline',
      context,
      provider: AuthProviders.Google,
    };
  }

  async getAuthTokenFromCode(code: string, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    const oauth2Client: OAuth2Client = this.getAuthClient(null, settings);
    let response: GetTokenResponse = null;

    let wait = 500;
    while(!response) {
      try {
        response = await oauth2Client.getToken(code);
      } catch(err) {
        if (err.code === 'ECONNRESET' && wait < 512000) {
          await setTimeout(wait);
          wait *= 2;
        } else {
          logging.errorF(this.log_name, 'getAuthTokenFromCode', `Error getting auth token for code ${code} ${wait}`, err);
          return;
        }
      }
    }

    if (response && response.tokens) {
      if (logging.isDebug()) DEBUG('getAuthTokenFromCode: Raw tokens = %o', response.tokens);
      oauth2Client.setCredentials(response.tokens);
      const tokens = GoogleConverter.convertNormalizedProviderTokenFromCredentials(response.tokens, this.scope_levels);
      if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthTokenFromCode', `tokens = ${logging.formatToken(tokens)}`);
      const user = await this.getProviderUserNormalized(tokens, settings);
      if (user) tokens.email = user.email;
      return tokens;
    } else {
      logging.errorF(this.log_name, 'getAuthTokenFromCode', `response = ${util.format(response)}`, null);
      throw new Error("Somehow tokens were not returned but we didn't get an exception");
    }
  }

  getAuthUrl(context: AuthContext, group: Group, email: string = null, scope: string[] = null, force_consent = false, force_selection = true): string {
    const defaults = this.getDefaultProviderSettings();
    let settings: NormalizedProviderSettings = group ? group.providerSettings(AuthProviders.Google) : defaults;
    if (!settings) settings = defaults;

    const oauth2Client: OAuth2Client = this.getAuthClient(null, settings);
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthUrl', `Force consent screen = ${force_consent}`);

    const options = {
      access_type: this.groupContains(group, DisallowableAuthContext.Offline) ? 'online' : 'offline',
      scope: this.getAuthScopes(context, group, scope),
      state: `${context} ${AuthProviders.Google} ${group ? group.id : ''}`,
      include_granted_scopes: true,
    } as GenerateAuthUrlOpts;

    // Figure out what options we want to send along
    if (email) options['login_hint'] = email;
    options['prompt'] = this.getPromptOptions(force_consent, force_selection);

    const generatedUrl = oauth2Client.generateAuthUrl(options);
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthUrl', `Generated url = ${generatedUrl}`);
    return generatedUrl;
  }

  getDefaultProviderSettings(): NormalizedProviderSettings {
    return new NormalizedProviderSettings(AuthProviders.Google, new OAuthConfig(config.get('GOOGLE_OAUTH2_CLIENT_ID'), config.get('GOOGLE_OAUTH2_CLIENT_SECRET'), config.get('GOOGLE_OAUTH2_CALLBACK')));
  }

  async getProviderUserNormalized(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser> {
    const oauth2Client: OAuth2Client = this.getAuthClient(tokens, settings, false);

    try {
      const params: oauth2_v2.Params$Resource$Userinfo$V2$Me$Get = { auth: oauth2Client as any};
      const result = await oauth2.userinfo.v2.me.get(params);
      if (logging.isDebug()) logging.debugF(this.log_name, 'getProviderUserNormalized', `response.data = ${util.format(result.data)}`);
      return new NormalizedProviderUser(result.data.id, result.data.email, result.data.given_name);
    } catch (err) {
      if (err.message && err.message.includes('No access, refresh token, API key or refresh handler callback is set.')) logging.warnF(this.log_name, 'getProviderUserNormalized', `Error getting provider user`, err);
      else logging.errorF(this.log_name, 'getProviderUserNormalized', `Error getting provider user`, err);
      return null;
    }
  }

  async getJWTProviderUserNormalized(idToken: string, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser> {
    const oauth2Client = this.getAuthClient(undefined, settings, false);

    try {
      const ticket: LoginTicket = await oauth2Client.verifyIdToken({audience: oauth2Client._clientId, idToken});
      const payload = ticket ? ticket.getPayload() : null;
      if (payload && payload.email_verified) return new NormalizedProviderUser(payload.sub, payload.email, payload.given_name);
    } catch(err) {
      logging.errorF(this.log_name, 'getJWTProviderUserNormalized', `Error getting provider user`, err);
      return null;
    }
  }

  async loadSelfPerson(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<Person> {
    const oauth2Client = this.getAuthClient(tokens, settings, false);

    const args = {
      auth: oauth2Client as any,
      resourceName: 'people/me',
      personFields: GoogleAuthenticationProvider.PERSON_FIELDS,
    } as people_v1.Params$Resource$People$Get;

    try {
      const result = await people.people.get(args);
      const google_person: people_v1.Schema$Person = result.data;
      let id = null;

      if (google_person.metadata && google_person.metadata.sources) {
        for (const source of google_person.metadata.sources) {
          if (source.type === 'CONTACT') {
            id = `people/c${bigInt(source.id, 16).toString()}`;
            break;
          }
          if (source.type === 'PROFILE') {
            // ignore, will set temp id
          }
        }
      } else throw new Error('No people/me');

      const save_person: Person = GoogleConverter.personFromGooglePerson(google_person);
      if (id) save_person.id = id;
      else save_person.tempId();
      save_person.learned = [new Date(0)];
      save_person.tags.push(new Tag(TagType.skill, 'self', 0, new Date() ));
      save_person.self = true;

      return save_person;
    } catch (err) {
      logging.errorF(this.log_name, 'loadSelfPerson', `Error loading self from google from ${logging.formatToken(tokens)}`, err);
      throw err;
    }
  }

  async reAuthenticate(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    if (logging.isDebug()) logging.debugF(this.log_name, 'reAuthenticate', `tokens = ${logging.formatToken(tokens)}`);
    const oauth2Client = this.getAuthClient(tokens, settings);
    let last_error;
    for (let i = 0; i < 10; i++) {
      try {
        const response: RefreshAccessTokenResponse = await oauth2Client.refreshAccessToken();

        if (response && response.credentials) {
          if (logging.isDebug()) logging.debugF(this.log_name, 'reAuthenticate', `credentials = ${response.credentials}`);
          oauth2Client.setCredentials(response.credentials);
          return GoogleConverter.convertNormalizedProviderTokenFromCredentials(response.credentials, this.scope_levels);
        } else {
          throw new Error("Somehow tokens were not returned but we didn't get an exception");
        }
      } catch(err) {
        last_error = err;
        if (err['code'] === 'ECONNRESET') {
          logging.warnF(this.log_name, 'reAuthenticate', `Connection reset on try ${i}/10. Retrying.`, err);
        } else throw err;
      }
      await setTimeout(100);
    }
    if (last_error) throw last_error;
    return null;
  }

  /*async reAuthenticate(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    if (logging.isDebug()) logging.debugF(this.log_name, 'reAuthenticate', `tokens = ${logging.formatToken(tokens)}`);
    const oauth2Client = this.getAuthClient(tokens, settings);
    // noinspection JSDeprecatedSymbols
    const response: Headers = await oauth2Client.getRequestHeaders();

    if (response && response['Authorization']) {
      if (logging.isDebug()) logging.debugF(this.log_name, 'reAuthenticate', `credentials = ${response['Authorization']}`);
      const creds = response['Authorization'].match(/Bearer (:?.*)/);

      const g_tokens:Credentials = tokens;

      if (creds) {
        g_tokens.access_token = creds[1];
        // g_tokens.expiry_date = funcs.MINUTES(new Date(), 59).getTime();
      }
      return GoogleConverter.convertNormalizedProviderTokenFromCredentials(g_tokens);
    } else {
      throw new Error(`Somehow tokens were not returned but we didn't get an exception ${JSON.stringify(response)}`);
    }
  }*/

  async revokeToken(profile: Uid, provider: AuthProviders, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void> {
    if (!tokens) return;

    const oauth2Client: OAuth2Client = this.getAuthClient(tokens, settings);

    // let cred_err = null;
    let refresh_err = null;
    let access_err = null;

    // This is broken as of 20181228. So we try a bunch of things
    try {
      await oauth2Client.revokeCredentials();
    } catch (err) {
      logging.warnF(this.log_name, 'revokeToken', 'revokeCredentials() Failed - expected bug');
      // cred_err = err;
    }

    if (tokens.refresh_token) {
      const post_data = `token=${tokens.access_token}`;
      try {
        await axios.post(GoogleAuthenticationProvider.URL_REVOKE, post_data, {
            headers: { 
              'Content-Type': 'application/x-www-form-urlencoded',
              'Content-Length': Buffer.byteLength(post_data),
            },
          });
      } catch (err) {
        logging.warnFP(this.log_name, 'revokeToken', profile, 'Manual revoke of refresh token failed', err);
        refresh_err = err;
      }
    }

    if (tokens.access_token) {
      try {
        await axios({
          url: GoogleAuthenticationProvider.URL_REVOKE,
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          params: {
            token: tokens.access_token,
          },
        });
      } catch (err) {
        logging.warnFP(this.log_name, 'revokeToken', profile, 'Manual revoke of access token failed', err);
        access_err = err;
      }
    }

    if (refresh_err) throw refresh_err;
    if (access_err) throw access_err;
  }

  protected setupScopes(): void {
    this.SCOPES_CALENDAR = [GoogleScopes.Calendar_Read];
    this.SCOPES_CALENDAR_SYNC = [GoogleScopes.Calendar_ReadWrite];
    this.SCOPES_CONTACT = [GoogleScopes.Contacts_Read];
    this.SCOPES_CONTACT_SYNC = [GoogleScopes.Contacts_ReadWrite];
    this.SCOPES_DIRECTORY = [GoogleScopes.Directory];
    this.SCOPES_FILE = [GoogleScopes.Files_ReadWrite_AppFolder];
    this.SCOPES_MAIL = [GoogleScopes.Mail_Read];
    this.SCOPES_MAIL_SYNC = [GoogleScopes.Mail_ReadWrite];
    this.SCOPES_NOTE = [GoogleScopes.Files_ReadWrite_AppFolder];
    this.SCOPES_TASK = [GoogleScopes.Tasks_Read];
    this.SCOPES_TASK_SYNC = [GoogleScopes.Tasks_ReadWrite];
    this.SCOPES_USER = [GoogleScopes.Email, GoogleScopes.Profile];
  }
}
