/**
 * 
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import _ from 'lodash';
import util from 'util';
import { Topics } from '../session/dialog';
import ForaUser from '../session/user';
import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Topic } from '../types/globals';
import { Group } from '../types/group';
import { Person } from '../types/items';
import { AuthClientInfo, AuthContext, AuthLevel, AuthLevelPriority, AuthProviders, DisallowableAuthContext, Uid } from '../types/shared';
import * as functions from '../utils/funcs';
import logging from '../utils/logging';
import { IAuthenticationProvider } from './i_authentication_provider';

const DEBUG = (require('debug') as any)('fora:auth');

export abstract class AbstractAuthenticationProvider implements IAuthenticationProvider {
  protected abstract log_name: string;
  protected SCOPES_ALL = [];
  protected SCOPES_CALENDAR = [];
  protected SCOPES_CALENDAR_SYNC= [];
  protected SCOPES_CONTACT = [];
  protected SCOPES_CONTACT_SYNC = [];
  protected SCOPES_DEFAULT = [];
  protected SCOPES_FILE = [];
  protected SCOPES_MAIL = [];
  protected SCOPES_MAIL_SYNC = [];
  protected SCOPES_NOTE = [];
  protected SCOPES_OFFLINE = [];
  protected SCOPES_ORGANIZER = []; // read only
  protected SCOPES_ORGANIZER_SYNC = []; // read write
  protected SCOPES_TASK = [];
  protected SCOPES_TASK_SYNC = [];
  protected SCOPES_USER = [];
  protected SCOPES_CONNECT = [];
  protected SCOPES_DIRECTORY = [];

  scope_levels: {[key:string]: string[]} = {};

  constructor() {
    this.setupScopes();

    this.SCOPES_DEFAULT = _.uniq([
      ...this.SCOPES_OFFLINE, // This is just Microsoft. Google does offline at another point
      // ...this.SCOPES_FILE,
      // ...this.SCOPES_NOTE,
      ...this.SCOPES_USER,
    ]);
    this.SCOPES_ORGANIZER = _.uniq([
      ...this.SCOPES_OFFLINE, // This is just Microsoft. Google does offline at another point
      ...this.SCOPES_CALENDAR,
      ...this.SCOPES_CONTACT,
      //...this.SCOPES_TASK,
    ]);
    this.SCOPES_ORGANIZER_SYNC = _.uniq([
      ...this.SCOPES_OFFLINE, // This is just Microsoft. Google does offline at another point
      ...this.SCOPES_CALENDAR_SYNC,
      ...this.SCOPES_CONTACT_SYNC,
      ...this.SCOPES_TASK_SYNC,
    ]);

    this.SCOPES_ALL = _.uniq([
      ...this.SCOPES_OFFLINE, // This is just Microsoft. Google does offline at another point
      ...this.SCOPES_CALENDAR_SYNC,
      ...this.SCOPES_CONTACT_SYNC,
      // ...this.SCOPES_FILE,
      // ...this.SCOPES_MAIL,
      // ...this.SCOPES_NOTE,
      ...this.SCOPES_TASK_SYNC,
      ...this.SCOPES_USER,
    ]);

    this.scope_levels[AuthLevel.Organizer] = this.SCOPES_ORGANIZER;
    this.scope_levels[AuthLevel.OrganizerSync] = this.SCOPES_ORGANIZER_SYNC;
  }

  ensureTokenTyped(token: any): NormalizedProviderToken {
    if (token) {
      if (token instanceof NormalizedProviderToken) return token;
      else return AbstractAuthenticationProvider.convertToNormalizedProviderToken(token);
    } else return null;
  }

  expiresAt(token: NormalizedProviderToken): Date {
    if (token && token.expires_at) return new Date(token.expires_at);
    else return new Date(0);
  }

  connectedTopic(provider: AuthProviders, context: AuthContext, group: Group, user?: ForaUser): Topic {
    if (user.isAuthenticated() && !user.isGuestAccount() && user.hasAccount(provider)) return Topics.TUTORIAL;
    else return Topics.REG_ERROR;
  }

  abstract getAuthClient(token?: NormalizedProviderToken, settings?: NormalizedProviderSettings): any;

  abstract getAuthClientInfo(context: AuthContext, group: Group, settings?: NormalizedProviderSettings): AuthClientInfo;

  getAuthContext(user: ForaUser, provider?: AuthProviders, profile?: Uid, group?: Group): AuthContext {
    if (profile === 'directory') return AuthContext.AuthConnect;

    let token;
    if (user) {
      if (provider) token = user.getTokens(provider, profile);
      else token = user.tokens;
    } 
    if (!token || !token.scope) return null;

    const scope: string = token.scope;
    //if (functions.stringIncludesAll(scope, this.SCOPES_MAIL)) return AuthContext.AuthChat;
    // else 
    if (functions.stringIncludesAll(scope, this.SCOPES_ORGANIZER_SYNC) && 
      (!group || !group.disallowed || 
        (!group.disallowed.includes(DisallowableAuthContext.Calendar) && !group.disallowed.includes(DisallowableAuthContext.Contact) )
      )) return AuthContext.AuthSyncOrganizer;
    else if (functions.stringIncludesAll(scope, this.SCOPES_ORGANIZER) && 
      (!group || !group.disallowed || 
        (!group.disallowed.includes(DisallowableAuthContext.Calendar) && !group.disallowed.includes(DisallowableAuthContext.Contact) )
      )) return AuthContext.AuthNoEmail;
    else return AuthContext.AuthNoOrganizer;
  }

  getAuthPermssions(user: ForaUser, provider?: AuthProviders, group?: Group): AuthLevel {
    let token;
    if (user) {
      if (provider) token = user.getTokens(provider);
      else token = user.tokens;
    } 
    if (!token || !token.scope) return null;

    const scope: string = token.scope;
    if (functions.stringIncludesAll(scope, this.SCOPES_ORGANIZER)) return AuthLevel.Organizer;
    else if (functions.stringIncludesAll(scope, this.SCOPES_ORGANIZER_SYNC)) return AuthLevel.OrganizerSync;
    else return AuthLevel.Basic;
  }

  getAuthRequiredScope(user: ForaUser, provider: AuthProviders, profile: Uid, group?: Group) {
    let scope = this.SCOPES_DEFAULT.slice();
    if (user.isAuthenticated(AuthLevel.Organizer, provider, profile)) scope = scope.concat(this.SCOPES_ORGANIZER);
    else if (user.isAuthenticated(AuthLevel.OrganizerSync, provider, profile)) scope = scope.concat(this.SCOPES_ORGANIZER_SYNC);
    // if (user.isAuthenticated(AuthLevel.Email)) scope = scope.concat(this.SCOPES_MAIL);
    if (logging.isDebug(user.profile)) DEBUG(`Scope from ${JSON.stringify(user.tokens)}\n\t${JSON.stringify(user.permissions)}\n\t${scope}`);

    return this.removeDisallowedScopes(group, scope);
  }

  abstract getAuthTokenFromCode(code: string, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken>;

  abstract getAuthUrl(context: AuthContext, group: Group, email?: string, scope?: string[], force_consent?: boolean): string;

  abstract getDefaultProviderSettings(): NormalizedProviderSettings;

  getPromptOptions(force_consent: boolean, force_selection: boolean): string {
    const options = [];
    if (force_consent) options.push('consent');
    else if (force_selection) options.push('select_account');

    return options.join(' ');
  }

  abstract getProviderUserNormalized(token: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser>;

  getScopesForEvents(): string[][] {
    return [this.SCOPES_CALENDAR, this.SCOPES_CALENDAR_SYNC];
  }

  getScopesForFiles(): string[][] {
    return [this.SCOPES_FILE];
  }

  getScopesForMessages(): string[][] {
    return [this.SCOPES_MAIL, this.SCOPES_MAIL_SYNC];
  }

  getScopesForNotes(): string[][] {
    return this.SCOPES_NOTE;
  }

  getScopesForPeople(): string[][] {
    return [this.SCOPES_CONTACT, this.SCOPES_CONTACT_SYNC];
  }

  getScopesForTasks(): string[][] {
    return [this.SCOPES_TASK, this.SCOPES_TASK_SYNC];
  }

  getScopesForUsers(): string[][] {
    return [this.SCOPES_DIRECTORY];
  }

  getGroupName(provider: AuthProviders, group: Group): string {
    return group.name;
  }

  getGroupId(provider: AuthProviders, group: Group): string {
    return group.id;
  }

  getGroupTokens(provider: AuthProviders, group: Group): any {
    if (group.provider === provider && group.provider_settings) {
      const group_provider_settings = group.providerSettings(provider);
      return group_provider_settings ? group_provider_settings.oauth : null;
    }

    return null;
  }

  setGroupTokens(provider: AuthProviders, group: Group, tokens: any): any {
    if (group.provider === provider && group.provider_settings) {
      const group_provider_settings = group.providerSettings(provider);
      if (group_provider_settings) group_provider_settings.oauth = tokens;
    }
  }

  isSupportedContext(context: AuthContext) {
    return true;
  }

  isExpired(profile: string, token: NormalizedProviderToken): boolean {
    const expiry_date = this.expiresAt(token);
    const expired = expiry_date <= new Date();

    if (expired) logging.warnFP(this.log_name, 'isExpired', profile, `User token expired at ${expiry_date}`);
    else if(logging.isDebug(profile)) logging.debugFP(this.log_name, 'isExpired', profile, `User tokens expire at ${expiry_date} - (NOT expired)`);

    return expired;
  }

  isValid(token: NormalizedProviderToken, check_refresh = true): boolean {
    if (logging.isDebug()) DEBUG('isValid: %o', !!(token && token['access_token'] && (!check_refresh || token['refresh_token'])));
    const is_valid = !!(token && token['access_token'] && (!check_refresh || token['refresh_token']));
    if(logging.isDebug()) logging.debugF(this.log_name, 'isValid', `${is_valid ? '' : 'NOT '}Valid ${JSON.stringify(token)}`);
    return is_valid;
  }

  abstract loadSelfPerson(token: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<Person>;

  abstract reAuthenticate(token: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken>;

  public reconcileExistingTokens(
    a_side: { [key: string]: NormalizedProviderToken },
    b_side: { [key: string]: NormalizedProviderToken },
    settings?: NormalizedProviderSettings,
    revoke = true,
  ): { [key: string]: NormalizedProviderToken } {
    const merged = {};

    // Loop through the A side
    if (logging.isDebug()) {
      DEBUG('reconcileExistingTokens: A_SIDE %o', a_side);
      DEBUG('reconcileExistingTokens: B_SIDE %o', b_side);
    }

    _.forOwn(a_side, (value, key) => {
      if (key && key !== 'null') {
        if (b_side && b_side[key]) {
          if (value) merged[key] = this.mergeTokens(this.ensureTokenTyped(value), this.ensureTokenTyped(b_side[key]), settings, revoke);
          else merged[key] = this.ensureTokenTyped(b_side[key]);
        } else merged[key] = this.ensureTokenTyped(value);
        if (logging.isDebug()) DEBUG('reconcileExistingTokens: working on A_SIDE key = %s, value = %o', key, merged[key]);
      }
    });

    // Loop through the B side
    _.forOwn(b_side, (value, key) => {
      if (key && key !== 'null' && !(key in merged)) {
        merged[key] = this.ensureTokenTyped(value);
        if (logging.isDebug()) DEBUG('reconcileExistingTokens: working on B_SIDE key = %s, value = %o', key, merged[key]);
      }
    });

    if (logging.isDebug()) DEBUG('reconcileExistingTokens: merged = %o', merged);
    return merged;
  }

  abstract revokeToken(profile: Uid, provider: AuthProviders, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void>;

  protected getAuthScopes(context: AuthContext, group: Group, scope?: string[]): string {
    if (!scope) scope = this.SCOPES_DEFAULT.slice();

    switch (context) {
      case AuthContext.AuthNoOrganizer:
      case AuthContext.Contract:
      case AuthContext.Project:
        break;
      case AuthContext.Contractor:
      case AuthContext.Connect:
      case AuthContext.Settings:
        break;
      case AuthContext.Learn:
      case AuthContext.Webinar:
      case AuthContext.Apply:
        break;
      case AuthContext.Signup:
      case AuthContext.Breadwinners:
      case AuthContext.App:
      case AuthContext.People:
      case AuthContext.Network:
      case AuthContext.Intro:
      case AuthContext.ProjectCreate:
      case AuthContext.ProjectExpert:
      case AuthContext.AuthNoEmail:
        scope = _.uniq(scope.concat(this.SCOPES_ORGANIZER.slice()));
        break;
      case AuthContext.AuthSyncOrganizer:
        scope = _.uniq(scope.concat(this.SCOPES_ORGANIZER_SYNC.slice()));
        break;
      case AuthContext.AuthConnect:
        scope = _.uniq(this.SCOPES_DIRECTORY.slice());
        break;
      case AuthContext.AuthAPI:
        scope = _.uniq(this.SCOPES_CONNECT.slice());
        break;
      default:
        scope = _.uniq(scope/*.concat(this.SCOPES_MAIL)*/.concat(this.SCOPES_ORGANIZER.slice()));
        break;
    }

    // Remove any scopes that are disallowed
    scope = this.removeDisallowedScopes(group, scope);

    const scope_str = _.uniq(scope).join(' ');
    if(logging.isDebug()) logging.debugF(this.log_name, 'getAuthScopes', `scope = ${scope_str}`);
    return scope_str;
  }

  protected groupContains(group: Group, context: DisallowableAuthContext): boolean {
    return group && group.disallowed && group.disallowed.includes(context);
  }

  protected mapDisallowedContextToScope(context: DisallowableAuthContext): string[] {
    switch (context) {
      case DisallowableAuthContext.Calendar:
        return [...this.SCOPES_CALENDAR, ...this.SCOPES_CALENDAR_SYNC];
      case DisallowableAuthContext.Contact:
        return [...this.SCOPES_CONTACT, ...this.SCOPES_CONTACT_SYNC];
      case DisallowableAuthContext.File:
        return this.SCOPES_FILE;
      case DisallowableAuthContext.Mail:
        return [...this.SCOPES_MAIL, ...this.SCOPES_MAIL_SYNC];
      case DisallowableAuthContext.Note:
        return this.SCOPES_NOTE;
      case DisallowableAuthContext.Offline:
        return [...this.SCOPES_OFFLINE, ...this.SCOPES_CALENDAR, ...this.SCOPES_CALENDAR_SYNC, ...this.SCOPES_CONTACT, ...this.SCOPES_CONTACT_SYNC, ...this.SCOPES_FILE, /*...this.SCOPES_MAIL,*/ ...this.SCOPES_NOTE, ...this.SCOPES_TASK, ...this.SCOPES_TASK_SYNC];
      case DisallowableAuthContext.Task:
        return [...this.SCOPES_TASK, ...this.SCOPES_TASK_SYNC];
    }
  }

  /**
   * Merge token 'intelligently'. In this case, we are doing our best to determine which side has correct and potentially newer token
   *
   * @param settings - Normalized provider settings
   * @param a_side - `A` side Token
   * @param b_side - 'B` side Token
   * @param revoke - Optional setting to revoke any bad or old token (defaults to true)
   * @private
   */
  public mergeTokens(a_side: NormalizedProviderToken, b_side: NormalizedProviderToken, settings?: NormalizedProviderSettings, _revoke = true) {
    if (logging.isDebug()) {
      DEBUG('mergeTokens: A_SIDE %o', a_side);
      DEBUG('mergeTokens: B_SIDE %o', b_side);
    }

    //
    // Rules decided upon as of 20181229 (Omer and Joe)
    //
    // Field importance (fall through logic)
    //  1 access_token - if there is no access token, remove it
    //  2 refresh_token - if they are not equal, always take the one that has a refresh
    //    token so we don't have to ask the user to fully re-auth
    //  3 scope - if they are not equal, parse the strings into a sets, go with the
    //    larger one IFF it is a super-set of the smaller one
    //  4 expires_at - go with the latest expiration
    //

    // need to retain
    // highest permisions
    let permissions: AuthLevel;
    if (a_side.permissions) {
      if (b_side.permissions) {
        permissions = AuthLevelPriority[a_side.permissions] > AuthLevelPriority[b_side.permissions] ? a_side.permissions : b_side.permissions;
      } else permissions = a_side.permissions;
    } else permissions = b_side.permissions;

    // default if true
    let default_tokens = a_side.default || b_side.default;

    // name
    let name = a_side.name && a_side.name.length ? a_side.name : b_side.name;

    // email
    let email = a_side.email && a_side.email.length ? a_side.email : b_side.email;

    // group
    let group = a_side.group && a_side.group.length ? a_side.group : b_side.group;

    // account - msal only
    let account = a_side.account ? a_side.account : b_side.account;

    const tokens = this.chooseTokenByAccess(a_side, b_side);

    if (tokens) {
      tokens.permissions = permissions;
      tokens.default = default_tokens;
      tokens.name = name;
      tokens.email = email;
      tokens.group = group;
      tokens.account = account;
    }

    return tokens;
  }

  protected removeDisallowedScopes(group: Group, scope: string[]): string[] {
    // Minus out any of the scopes that we can't use
    if (group && group.disallowed && group.disallowed.length) {
      if(logging.isDebug()) logging.debugF(this.log_name, 'removeDisallowedScopes', `scope before disallow = ${util.format(scope)}`);
      for (const disallowed of group.disallowed) scope = _.pullAll(scope, this.mapDisallowedContextToScope(disallowed));
      if(logging.isDebug()) logging.debugF(this.log_name, 'removeDisallowedScopes', `scope after disallow = ${util.format(scope)}`);
    }

    return scope;
  }

  protected abstract setupScopes(): void;

  private chooseTokenByAccess(a_side: NormalizedProviderToken, b_side: NormalizedProviderToken) {
    const a_side_valid = !!(a_side && a_side.access_token);
    const b_side_valid = !!(b_side && b_side.access_token);

    if (a_side_valid && b_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByAccess: both valid, falling through to chooseTokenByRefresh');
      return this.chooseTokenByRefresh(a_side, b_side);
    } else if (a_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByAccess: returning A side');
      return a_side;
    } else if (b_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByAccess: returning B side');
      return b_side;
    } else {
      if (logging.isDebug()) DEBUG('chooseTokenByAccess: returning NULL');
      return null;
    }
  }

  private chooseTokenByExpiration(a_side: NormalizedProviderToken, b_side: NormalizedProviderToken) {
    const a_side_expiration = this.expiresAt(a_side);
    const b_side_expiration = this.expiresAt(b_side);
  
    if (logging.isDebug()) {
      DEBUG('chooseTokenByExpiration: dates [%s][%s]', a_side_expiration, b_side_expiration);
      DEBUG('chooseTokenByExpiration: time [%s][%s]', a_side_expiration.getTime(), b_side_expiration.getTime());
    }

    if (a_side_expiration.getTime() >= b_side_expiration.getTime()) {
      if (logging.isDebug()) DEBUG('chooseTokenByExpiration: returning A side');
      return a_side;
    } else {
      if (logging.isDebug()) DEBUG('chooseTokenByExpiration: returning B side');
      return b_side;
    }
  }

  private chooseTokenByRefresh(a_side: NormalizedProviderToken, b_side: NormalizedProviderToken) {
    const a_side_valid = !!(a_side && a_side.refresh_token);
    const b_side_valid = !!(b_side && b_side.refresh_token);

    if (a_side_valid && b_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByRefresh: both valid, falling through to chooseTokenByScope');
      return this.chooseTokenById(a_side, b_side);
    } else if (a_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByRefresh: returning A side');
      const refresh_token = a_side.refresh_token;
      const scope_token = this.chooseTokenById(a_side, b_side);
      scope_token.refresh_token = refresh_token;
      return scope_token;
    } else if (b_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByRefresh: returning B side');
      const refresh_token = b_side.refresh_token;
      const scope_token = this.chooseTokenById(b_side, a_side);
      scope_token.refresh_token = refresh_token;
      return scope_token;
    } else {
      if (logging.isDebug()) DEBUG('chooseTokenByRefresh: both INVALID, falling through to chooseTokenByScope');
      return this.chooseTokenById(a_side, b_side);
    }
  }

  private chooseTokenById(a_side: NormalizedProviderToken, b_side: NormalizedProviderToken) {
    const a_side_valid = !!(a_side && a_side.id_token);
    const b_side_valid = !!(b_side && b_side.id_token);

    if (a_side_valid && b_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByRefresh: both valid, falling through to chooseTokenByScope');
      return this.chooseTokenByScope(a_side, b_side);
    } else if (a_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByRefresh: returning A side');
      const id_token = a_side.id_token;
      const scope_token = this.chooseTokenByScope(a_side, b_side);
      scope_token.id_token = id_token;
      return scope_token;
    } else if (b_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByRefresh: returning B side');
      const id_token = b_side.id_token;
      const scope_token = this.chooseTokenByScope(b_side, a_side);
      scope_token.id_token = id_token;
      return scope_token;
    } else {
      if (logging.isDebug()) DEBUG('chooseTokenByRefresh: both INVALID, falling through to chooseTokenByScope');
      return this.chooseTokenByScope(a_side, b_side);
    }
  }

  private chooseTokenByScope(a_side: NormalizedProviderToken, b_side: NormalizedProviderToken) {
    const a_side_scope = a_side ? a_side.scope : '';
    const b_side_scope = b_side ? b_side.scope : '';
    const a_side_valid = !!a_side_scope;
    const b_side_valid = !!b_side_scope;

    if (logging.isDebug()) {
      DEBUG('chooseTokenByScope: A side scopes string = %o', a_side_scope);
      DEBUG('chooseTokenByScope: B side scopes string= %o', b_side_scope);
    }

    const a_side_scopes = a_side_scope ? a_side_scope.split(' ').sort() : [];
    const b_side_scopes = b_side_scope ? b_side_scope.split(' ').sort() : [];

    if (logging.isDebug()) {
      DEBUG('chooseTokenByScope: A side scopes array = %o', a_side_scopes);
      DEBUG('chooseTokenByScope: B side scopes array = %o', b_side_scopes);
    }

    if (a_side_valid && b_side_valid) {
      if (_.isEqual(a_side_scopes, b_side_scopes)) {
        if (logging.isDebug()) DEBUG('chooseTokenByScope: both valid and equal, falling through to chooseTokenByExpiration');
        return this.chooseTokenByExpiration(a_side, b_side);
      } else if (b_side_scopes.every(scope => a_side_scopes.includes(scope))) {
        if (logging.isDebug()) DEBUG('chooseTokenByScope: return A side (B is a subset of A)');
        return a_side;
      } else if (a_side_scopes.every(scope => b_side_scopes.includes(scope))) {
        if (logging.isDebug()) DEBUG('chooseTokenByScope: return B side (A is a subset of B)');
        return b_side;
      } else {
        if (logging.isDebug()) DEBUG('chooseTokenByScope: both are different but not super-sets, falling through to chooseTokenByExpiration');
        return this.chooseTokenByExpiration(a_side, b_side);
      }
    } else if (a_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByScope: returning A side');
      return a_side;
    } else if (b_side_valid) {
      if (logging.isDebug()) DEBUG('chooseTokenByScope: returning B side');
      return b_side;
    } else {
      if (logging.isDebug()) DEBUG('chooseTokenByScope: both INVALID, falling through to chooseTokenByExpiration');
      return this.chooseTokenByExpiration(a_side, b_side);
    }
  }

  private static convertToNormalizedProviderToken(merged: any): NormalizedProviderToken {
    const token: NormalizedProviderToken = new NormalizedProviderToken(merged['provider'], merged['access_token'], merged['permissions']);
    token.expires_at = merged['expires_at'];
    token.id_token = merged['id_token'];
    token.refresh_token = merged['refresh_token'];
    token.scope = merged['scope'];
    token.token_type = merged['token_type'];
    token.default = merged['default'];
    token.name = merged['name'];
    token.email = merged['email'];
    token.group = merged['group'];
    token.account = merged['account'];

    return token;
  }
}
