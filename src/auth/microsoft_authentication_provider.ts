/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client } from '@microsoft/microsoft-graph-client';
import { User } from '@microsoft/microsoft-graph-types';
import 'isomorphic-fetch';
import { /* create, OAuthClient,*/ AccessToken, AuthorizationCode, Token } from 'simple-oauth2';
import util from 'util';

import config from '../config';

import ForaUser from '../session/user';

import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { Group } from '../types/group';
import { Person, Tag } from '../types/items';
import { OAuthConfig } from '../types/oauth';
import { AuthClientInfo, AuthContext, AuthProviders, TagType, Uid } from '../types/shared';

import { MINUTES } from '../utils/datetime';
import { normalizeId } from '../utils/funcs';
import logging from '../utils/logging';

import { MicrosoftGraphConverter } from '../sources/helpers/microsoft/microsoft_graph_conversions';
import { AbstractAuthenticationProvider } from './a_authentication_provider';

export enum MicrosoftScopes {
  Offline_Access = 'offline_access',
  Calendar_ReadWrite = 'Calendars.ReadWrite',
  Calendar_Read = 'Calendars.Read',
  Contacts_ReadWrite = 'Contacts.ReadWrite',
  Contacts_Read = 'Contacts.Read',
  // Files_ReadWrite_AppFolder = 'Files.ReadWrite.AppFolder',
  Mail_ReadWrite = 'Mail.ReadWrite',
  Mail_Read = 'Mail.Read',
  Mail_Send = 'Mail.Send',
  // Notes_ReadWrite = 'Notes.ReadWrite',
  Tasks_Read = 'Tasks.Read',
  Tasks_ReadWrite = 'Tasks.ReadWrite',
  User_Read = 'User.Read',
}

/**
 * Authentication provider for Microsoft
 *
 * Development Hints:
 * - To get a token for building test cases or playing around
 *   - sign into https://developer.microsoft.com/en-us/graph/graph-explorer
 *   - open console
 *   - run tokenPlease()
 *
 * Notes:
 * - /me is equivalent to /users/{id = current user}
 *
 * Libraries Used:
 * - https://github.com/microsoftgraph/msgraph-sdk-javascript
 * - https://github.com/microsoftgraph/msgraph-typescript-typings/
 *
 * Consent Management Pages:
 * - https://account.live.com/consent/Manage
 * - https://portal.office.com/account/#apps

 * Tutorials:
 * - https://docs.microsoft.com/en-us/outlook/rest/node-tutorial
 * - https://developer.microsoft.com/en-us/graph/docs/concepts/permissions_reference
 * - https://github.com/microsoftgraph/nodejs-apponlytoken-rest-sample/blob/master/auth.js
 */
export class MicrosoftAuthenticationProvider extends AbstractAuthenticationProvider {
  private static readonly REVOKE_ENABLED: boolean = false; // Microsoft does not support revoking tokens as of ******** - I know right?!?
  log_name = 'auth.MicrosoftAuthenticationProvider';

  ensureTokenTyped(token: any): NormalizedProviderToken {
    if (token) {
      token['provider'] = AuthProviders.Microsoft; // Make sure that we have a proper provider
    }

    return super.ensureTokenTyped(token);
  }

  getAuthClient(tokens?: NormalizedProviderToken, settings?: NormalizedProviderSettings): AuthorizationCode { //OAuthClient {
    // Use the settings from the ENV if the user didn't explicitly tell us not to
    const defaults = this.getDefaultProviderSettings();
    if (!settings) settings = defaults;

    const client_id = settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : defaults.oauth.client_id;
    const client_callback = settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;
    const client_secret = settings.oauth && settings.oauth.client_secret ? settings.oauth.client_secret : defaults.oauth.client_secret;

    if (logging.isDebug()) {
      logging.debugF(this.log_name, 'getAuthClient', `OAUTH2_CLIENT_ID = ${client_id}`);
      logging.debugF(this.log_name, 'getAuthClient', `OAUTH2_CALLBACK = ${client_callback}`);
    }

    const credentials = {
      client: {
        id: client_id,
        secret: client_secret,
      },
      auth: {
        tokenHost: 'https://login.microsoftonline.com',
        authorizePath: 'common/oauth2/v2.0/authorize',
        tokenPath: 'common/oauth2/v2.0/token',
      },
    };

    return new AuthorizationCode(credentials); // create(credentials);
  }

  getAuthClientInfo(context: AuthContext, group: Group, settings?: NormalizedProviderSettings): AuthClientInfo {
    return {
      name: 'Microsoft',
      clientId: settings && settings.oauth && settings.oauth.client_id ? settings.oauth.client_id : config.get('MICROSOFT_OAUTH2_CLIENT_ID'),
      scope: this.getAuthScopes(context, group),
      provider: AuthProviders.Microsoft,
      context,
    };
  }

  async getAuthTokenFromCode(code: string, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    const OAUTH2_CALLBACK = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : config.get('MICROSOFT_OAUTH2_CALLBACK');
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthTokenFromCode', `MICROSOFT_OAUTH2_CALLBACK = :${OAUTH2_CALLBACK}`);

    const oauthClient: AuthorizationCode /*OAuthClient*/ = this.getAuthClient(null, settings);
    const response: AccessToken = await oauthClient./*authorizationCode.*/getToken({ code, redirect_uri: OAUTH2_CALLBACK });

    const tokens = MicrosoftGraphConverter.convertNormalizedProviderTokenFromToken(/*oauthClient.accessToken.create(*/response.token, this.scope_levels);
    // refresh token indicates offline access, make sure it's in scope
    if (tokens.refresh_token) {
      if (!tokens.scope) tokens.scope = MicrosoftScopes.Offline_Access.slice(); // copy the constant
      else if(!tokens.scope.includes(MicrosoftScopes.Offline_Access)) tokens.scope = tokens.scope.concat(' ', MicrosoftScopes.Offline_Access);
    }
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthTokenFromCode', `tokens = ${util.format(tokens)}`);
    const user = await this.getProviderUserNormalized(tokens, settings);
    if (user) tokens.email = user.email;
    return tokens;
  }

  getAuthUrl(context: AuthContext, group: Group, email: string = null, scope?: string[], force_consent = false, force_selection = true): string {
    const defaults = this.getDefaultProviderSettings();
    let settings: NormalizedProviderSettings = group ? group.providerSettings(AuthProviders.Microsoft) : defaults;
    if (!settings) settings = defaults;

    const client_callback = settings && settings.oauth && settings.oauth.client_callback ? settings.oauth.client_callback : defaults.oauth.client_callback;

    if (logging.isDebug()) {
      logging.debugF(this.log_name, 'getAuthUrl', `Callback = ${client_callback}`);
      logging.debugF(this.log_name, 'getAuthUrl', `Force consent screen = ${force_consent}`);
    }

    const client: AuthorizationCode /*OAuthClient*/ = this.getAuthClient(null, settings);
    const url_params = {
      redirect_uri: client_callback,
      scope: this.getAuthScopes(context, group, scope),
      state: `${context.toString()} ${AuthProviders.Microsoft} ${group ? group.id : ''}`,
    };

    // Figure out what options we want to send along
    if (email) url_params['login_hint'] = email;
    url_params['prompt'] = this.getPromptOptions(force_consent, force_selection);

    const url_generated = client./*authorizationCode.*/authorizeURL(url_params);
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthUrl', `generated url = ${url_generated}`);
    return url_generated;
  }

  getDefaultProviderSettings(): NormalizedProviderSettings {
    return new NormalizedProviderSettings(AuthProviders.Microsoft, new OAuthConfig(config.get('MICROSOFT_OAUTH2_CLIENT_ID'), config.get('MICROSOFT_OAUTH2_CLIENT_SECRET'), config.get('MICROSOFT_OAUTH2_CALLBACK')));
  }

  /**
   * Get a handle to a Microsoft Graph client
   *
   * @param token - Tokens for user that will interact with the Microsoft Graph
   * @param settings - Normalized provider settings
   * @async
   */
  async getGraphClient(user: ForaUser, profile: Uid, settings?: NormalizedProviderSettings): Promise<Client> {
    let tokens = user.getTokens(AuthProviders.Microsoft, profile);
    // Use the settings from the ENV if the user didn't explicitly tell us not to
    let access_token = tokens ? await this.getAuthAccessToken(tokens, settings) : null;
    if (logging.isDebug()) logging.debugF(this.log_name, 'getGraphClient', `access_token = ${logging.truncate(access_token, 10)}`);

    if (access_token) return Client.init({ authProvider: done => done(null, access_token), debugLogging: logging.isDebug() });
    else if (tokens) {
      const new_tokens = await this.reAuthenticate(tokens, settings);
      if (new_tokens) {
        access_token = await this.getAuthAccessToken(new_tokens as any as Token, settings);
        if (access_token) {
          user.tokens = new_tokens;
          return Client.init({ authProvider: done => done(null, access_token), debugLogging: logging.isDebug() });
        }
      }
    }

    throw Error("access_token is either null, or expired and we can't refresh it");
  }

  async getProviderUserNormalized(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderUser> {
    const user: User = await this.getProviderUser(tokens, settings);
    if (user) {
      if (logging.isDebug()) logging.debugF(this.log_name, 'getProviderUserNormalized', `User = ${util.format(user)}`);
      return new NormalizedProviderUser(normalizeId(user.id), user.userPrincipalName, user.givenName);
    }
    return null;
  }

  async loadSelfPerson(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<Person> {
    const user = await this.getProviderUser(tokens, settings);
    // const qlimit = limit > 0 ? `limit ${limit}` : '';
    if (user) {
      const save_person = MicrosoftGraphConverter.convertPersonFromUser(user, tokens, settings);
      save_person.learned = [new Date(0)];
      save_person.tags.push(new Tag(TagType.skill, 'self', 0, new Date() ));
      save_person.self = true;
      return save_person;
    } 
    return null;
  }

  async reAuthenticate(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<NormalizedProviderToken> {
    if (logging.isDebug()) logging.debugF(this.log_name, 'reAuthenticate', `tokens = ${logging.formatToken(tokens)}`);

    // Either no token or it's expired, do we have a refresh token?
    const refresh_token = tokens.refresh_token;
    if (refresh_token) {
      const newToken = await (await this.getAuthClient(null, settings).createToken/*accessToken.create*/({ refresh_token })).refresh();
      const tokens = MicrosoftGraphConverter.convertNormalizedProviderTokenFromToken(newToken.token, this.scope_levels);
      if (tokens.refresh_token) {
        if (!tokens.scope) tokens.scope = MicrosoftScopes.Offline_Access.slice(); // copy the constant
        else if(!tokens.scope.includes(MicrosoftScopes.Offline_Access)) tokens.scope = tokens.scope.concat(' ', MicrosoftScopes.Offline_Access);
      }
      return tokens;
    }

    throw new Error('Cannot re-authenticate without a refresh_token');
  }

  async revokeToken(profile: Uid, provider: AuthProviders, tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<void> {
    if (MicrosoftAuthenticationProvider.REVOKE_ENABLED) {
      await (await this.getAuthClient(null, settings).createToken/*accessToken.create*/(tokens as any)).revoke('access_token')
        .catch(err => {
          logging.errorFP(this.log_name, 'revokeToken', provider, 'Error revoking token', err);
          throw err;
        });
    } else logging.warnFP(this.log_name, 'revokeToken', provider, 'Not revoking token because revoke is not enabled');
  }

  protected setupScopes(): void {
    this.SCOPES_OFFLINE = [MicrosoftScopes.Offline_Access];
    this.SCOPES_CALENDAR = [MicrosoftScopes.Calendar_Read];
    this.SCOPES_CONTACT = [MicrosoftScopes.Contacts_Read];
    this.SCOPES_CALENDAR_SYNC = [MicrosoftScopes.Calendar_ReadWrite];
    this.SCOPES_CONTACT_SYNC = [MicrosoftScopes.Contacts_ReadWrite];
    this.SCOPES_FILE = []; // MicrosoftScopes.Files_ReadWrite_AppFolder];
    this.SCOPES_MAIL_SYNC = [MicrosoftScopes.Mail_ReadWrite, MicrosoftScopes.Mail_Send];
    this.SCOPES_MAIL = [MicrosoftScopes.Mail_Read, MicrosoftScopes.Mail_Send];
    this.SCOPES_NOTE = []; // MicrosoftScopes.Notes_ReadWrite];
    this.SCOPES_TASK_SYNC = [MicrosoftScopes.Tasks_Read];
    this.SCOPES_TASK = [MicrosoftScopes.Tasks_ReadWrite];
    this.SCOPES_USER = [MicrosoftScopes.User_Read];
  }

  /**
   * Get access token from given tokens. Will attempt to get a new set of tokens if the current has expired
   * and we have a refresh token. If we do not have a refresh_token, will return null.
   *
   * @param tokens - tokens
   * @param settings - Normalized provider settings
   * @async
   */
  private async getAuthAccessToken(tokens: Token, settings?: NormalizedProviderSettings): Promise<string> {
    if (logging.isDebug()) logging.debugF(this.log_name, 'getAuthAccessToken', `token = ${logging.formatToken(tokens)}`);
    logging.infoF(this.log_name, 'getAuthAccessToken', `token = ${logging.formatToken(tokens)}`);

    const access_token = tokens  ? tokens.access_token as string : null;
    const expires_at = tokens ? tokens.expires_at as any : null;

    if (access_token) {
      if (expires_at) {
        // We have a token, but is it expired?
        // Expire 5 minutes early to account for clock differences
        const expiration = new Date(expires_at); //.getTime() - 300000);
        if (expiration.getTime() > MINUTES(new Date(), 5).getTime()) {
          // Token is still good, just return it
          // return getAuthClient().accessToken.create({access_token}).token;
          return access_token;
        }
      } else {
        return access_token;
      }
    }

    // Either no token or it's expired, do we have a refresh token?
    if (tokens && tokens.refresh_token) {
      try {
        const newToken = await (await this.getAuthClient(null, settings).createToken/*accessToken.create*/(tokens)).refresh();
        // We are not storing this new token but its not as bad as it sounds. data/index refreshes the token and saves
        // it before any processing goes on so this part of the code should not be run often
        return newToken.token.token as string;
      } catch(e) {
        logging.errorF(this.log_name, 'getAuthAccessToken', `Error refreshing auth token`, e);
      }
    }

    // Nothing in the stored tokens that helps, return empty
    return null;
  }

  /**
   * Get the current user from the provider
   *
   * Microsoft docs:
   * - https://developer.microsoft.com/en-us/graph/docs/api-reference/v1.0/api/user_get
   * - https://developer.microsoft.com/en-us/graph/docs/api-reference/v1.0/resources/user
   *
   * @param tokens - Tokens to communicate with provider
   * @param settings - Normalized provider settings
   * @async
   */
  private async getProviderUser(tokens: NormalizedProviderToken, settings?: NormalizedProviderSettings): Promise<User> {
    let client: Client;
    let user: User;
    try {
      const temp_user = new ForaUser('temp', AuthProviders.Microsoft);
      temp_user.tokens = tokens;
      client = await this.getGraphClient(temp_user, 'temp', settings);
      user = client ? await client.api('/me').get() : null;
    } catch(e) {
      if (!client) logging.errorF(this.log_name, 'getProviderUser', 'Failed to get graph client', e);
      else if (!user) logging.errorF(this.log_name, 'getProviderUser', 'Failed to get user /me', e);
    }

    if (user) {
      if (logging.isDebug()) logging.debugF(this.log_name, 'getProviderUser', `user = ${util.format(user)}`);
      return user;
    } else {
      logging.warnF(this.log_name, 'getProviderUser', `Failed to get user from provider`);
      return null;
    }
  }
}
