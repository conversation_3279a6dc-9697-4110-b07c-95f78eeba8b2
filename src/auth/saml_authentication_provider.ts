/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import express from 'express';
import _ from 'lodash';
import { MultiStrategyConfig, Profile } from 'passport-saml';
import util from 'util';
import config from '../config';
import { NormalizedProviderSettings, NormalizedProviderToken, NormalizedProviderUser } from '../types/auth';
import { InternalError, Topic } from '../types/globals';
import { Group } from '../types/group';
import { Person, Tag } from '../types/items';
// import { Profile } from '../types/saml';
import { Topics } from '../session/dialog';
import ForaUser from '../session/user';
import { AuthClientInfo, AuthContext, AuthLevel, AuthProviders, TagType } from '../types/shared';
import * as functions from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

const LOG_NAME = 'data.auth.SamlAuthenticationProvider';
const DEBUG = (require('debug') as any)('fora:auth:saml_auth_provier');

const SUPPORTED_CONTEXT = [
  AuthContext.AuthNoOrganizer,
  AuthContext.Contract,
  AuthContext.Project,
  AuthContext.Settings,
  AuthContext.ProjectCreate,
  AuthContext.ProjectExpert,
  AuthContext.App,
];

/**
 * Authentication Provider for SAML
 *
 * NOTES:
 * - AuthnContext values - Look for 'Corresponding URI on https://docs.microsoft.com/en-us/windows-server/identity/ad-fs/operations/create-a-rule-to-send-an-authentication-method-claim
 *
 * Examples:
 * - User
 *    issuer: 'https://sts.windows.net/6d15f921-dfbf-4dc5-a5cc-15886c91fb6f/',
 *    sessionIndex: '_75a94ba7-76ac-4e0a-9710-d86237607c91',
 *    nameID: '5YTlaVCE7mc6ZcAuv3J6UGbuG5fEmX5fQWCGl9In2jY',
 *    nameIDFormat: 'urn:oasis:names:tc:SAML:2.0:nameid-format:persistent',
 *    nameQualifier: undefined,
 *    spNameQualifier: undefined,
 *    'http://schemas.microsoft.com/identity/claims/tenantid': '6d15f921-dfbf-4dc5-a5cc-15886c91fb6f',
 *    'http://schemas.microsoft.com/identity/claims/nameidentifier': '1227f90c-07a9-471b-8c5a-ae024059dbc1',
 *    'http://schemas.microsoft.com/identity/claims/objectidentifier': '1227f90c-07a9-471b-8c5a-ae024059dbc1',
 *    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name': '<EMAIL>',
 *    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/surname': 'Demo',
 *    'http://schemas.xmlsoap.org/ws/2005/05/identity/claims/givenname': 'Dani',
 *    'http://schemas.microsoft.com/identity/claims/displayname': 'Dani Demo',
 *    'http://schemas.microsoft.com/identity/claims/identityprovider': 'https://sts.windows.net/6d15f921-dfbf-4dc5-a5cc-15886c91fb6f/',
 *    'http://schemas.microsoft.com/claims/authnmethodsreferences': 'http://schemas.microsoft.com/ws/2008/06/identity/authenticationmethod/password',
 *    getAssertionXml: [Function],
 *    getAssertion: [Function]
 */
export class SamlAuthenticationProvider {
  connectedTopic(provider: AuthProviders, context: AuthContext, group: Group, user?: ForaUser): Topic {
    if (user.isAuthenticated() && !user.isGuestAccount() && user.hasAccount(provider)) return Topics.TUTORIAL;
    else return Topics.REG_ERROR;
  }

  getAuthClientInfo(context: AuthContext, group: Group): AuthClientInfo {
    return {
      name: group.name,
      clientId: group ? group.id : null,
      scope: undefined,
      provider: AuthProviders.Saml,
      context,
    };
  }

  getAuthContext(): AuthContext {
    return AuthContext.App;
  }

  getAuthUrl(group: Group): string {
    // return group.saml_settings.entryPoint;

    const host = group ? (group.redirect ? `${group.redirect}` : group.host ? `${group.host}` : '') : config.get('DEFAULT_HOST', 'localhost');
    const proto = config.isRunningOnGoogle() || host.endsWith('8443') || host.endsWith('9000') ? 'https' : 'http';
    return `${proto}://${host}/saml/login`;
  }

  getDefaultProviderSettings(): NormalizedProviderSettings {
    return new NormalizedProviderSettings(AuthProviders.Saml, null, {} as MultiStrategyConfig);
  }

  getGroupName(provider: AuthProviders, group: Group): string {
    return group.name;
  }

  getGroupId(provider: AuthProviders, group: Group): string {
    return group.id;
  }

  getGroupTokens(provider: AuthProviders, group: Group): any {
    if (group.provider === AuthProviders.Saml && group.provider_settings) {
      const default_settings = group.defaultProviderSettings;
      return default_settings ? default_settings.saml : null;
    }
  }

  setGroupTokens(provider: AuthProviders, group: Group, tokens: any): any {
    if (group.provider === AuthProviders.Saml && group.provider_settings) {
      const default_settings = group.defaultProviderSettings;
      if (default_settings) default_settings.saml = tokens;
      else group.provider_settings.push({provider: AuthProviders.Saml, saml: tokens, oauth: undefined});
    } else if(group.provider === AuthProviders.Saml) group.provider_settings = [{provider: AuthProviders.Saml, saml: tokens, oauth: undefined}]
  }

  async getProviderUserNormalized(req: express.Request, group: Group): Promise<NormalizedProviderUser> {
    const profile: Profile = req.user as Profile;
    logging.infoF(LOG_NAME, 'getProviderUserNormalized', JSON.stringify(profile));

    let email, id, given_name;

    for (const claim in profile) {
      const lclaim = claim.toLowerCase();
      if (!email && lclaim.includes('email')) email = (profile[claim] as string).replace(/^"/, '').replace(/"$/, '');
      if (!email && lclaim.includes('/name')) email = (profile[claim] as string).replace(/^"/, '').replace(/"$/, '');

      if (!id && lclaim.includes('identifier')) id = (profile[claim] as string).replace(/^"/, '').replace(/"$/, '');
      if (!id && lclaim.includes('nameid')) id = (profile[claim] as string).replace(/^"/, '').replace(/"$/, '');

      if (!given_name && lclaim.includes('displayname') && profile[claim]) [given_name] = functions.permuteName(profile[claim] as string);
    }

    if (!id) throw new InternalError(500, 'Cannot get user without ID');

    const user =  new NormalizedProviderUser(
      functions.normalizeId(`${group.id}_${id}`),
      email,
      given_name,
    );
    logging.infoF(LOG_NAME, 'getProviderUserNormalized', JSON.stringify(user));
    if (!user.email) logging.errorF(LOG_NAME, 'loadSelfPerson',  `SAML User ${user.id} has no email claim`, new Error('SAML User has no email'));
    return user;
  }

  getAuthPermssions(user: ForaUser, provider?: AuthProviders, group?: Group): AuthLevel {
    return AuthLevel.Basic;
  }

  isSupportedContext(context: AuthContext) {
    return SUPPORTED_CONTEXT.includes(context);
  }

  loadSelfPerson(req: express.Request, user: NormalizedProviderUser): Person {
    const profile: Profile = req.user as Profile;
    logging.infoF(LOG_NAME, 'loadSelfPerson', `${JSON.stringify(user)} ${JSON.stringify(profile)}`);

    let display_name = user.given_name;
    let email = user.email;
    let saml_id = user.id;
    let givenname, surname;

    for (const claim in profile) {
      const lclaim = claim.toLowerCase();
      if (!display_name && lclaim.includes('displayname') && profile[claim]) [display_name] = functions.permuteName(profile[claim] as string);
      if (!givenname && lclaim.includes('givenname')) givenname = profile[claim] as string;
      if (!surname && lclaim.includes('surname')) surname = profile[claim] as string;

      if (!email && lclaim.includes('email')) email = profile[claim] as string;
      if (!email && lclaim.includes('/name')) email = profile[claim] as string;

      if (!saml_id && lclaim.includes('identifier')) saml_id = profile[claim] as string;
      if (!saml_id && lclaim.includes('nameid')) saml_id = profile[claim] as string;
    }

    const names = []; // all name permutations
    const comms = []; // emails, ims, phones
    const tags = []; // company, skills, etc. {type:,value:}

    //
    // Name information
    functions.saveOne(names, display_name);
    functions.saveOne(names, givenname);
    functions.saveOne(names, surname);

    // Add a `Dani D` and `Dani Demo` record for Dani Demo
    if (givenname && surname) {
      functions.saveOne(names, `${givenname} ${surname}`);
      functions.saveOne(names, `${givenname} ${surname[0]}`);
    }

    //
    // Email Addresses
    functions.saveOne(comms, profile.mail ? profile.mail.toLowerCase() : null);
    functions.saveOne(comms, profile.email ? profile.email.toLowerCase() : null);
    functions.saveOne(comms, email ? email.toLowerCase() : null);
    const emails = parsers.findEmail(comms);

    // need at least one tag for skill queries to work
    if (!tags.length) tags.push(new Tag(TagType.skill, 'self', 0, new Date() ));

    // use initials for image
    const initials = parsers.findInitials(display_name);
    const photos = [`/api/contacts/default/${initials}/photo`];

    const id = `people/s${functions.normalizeId(saml_id)}`;
    if (id) functions.saveOne(comms, id);
    const person = new Person({
      id,
      displayName: display_name,
      nickName: givenname,
      email: emails.length ? emails[0] : null,
      self: true,
      names,
      photos,
      comms,
      tags,
      learned: [new Date(0)],
      urls: [],
    });
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'loadSelfPerson', `person = ${util.inspect(person)}`);

    if (!person.email) logging.errorF(LOG_NAME, 'loadSelfPerson',  `SAML User ${person.id} has no email claim`, new Error('SAML User has no email'));

    return person;
  }

  tokensFromUser(user: Profile): NormalizedProviderToken {
    const tokens = new NormalizedProviderToken(AuthProviders.Saml, 'SAML', AuthLevel.Basic);
    tokens.expires_at = new Date(_.get(user.getAssertion(), 'Assertion.Conditions[0].$.NotOnOrAfter', 0) as number);
    tokens.email = user.email;
    return tokens;
  }
}
