/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { GlobalType } from '../../types/items';
import { EntityType } from '../../types/shared';

export interface ICachePlugin {
  cacheAttVal(profile: string, type: EntityType | GlobalType, att, val, result, expires?): void;

  clearCacheAttVal(profile: string, type: EntityType | GlobalType, att, val): void;

  delete(key: string): Promise<void>;

  get(key: string): Promise<any>;

  lookupCacheAttVal(profile: string, type: EntityType | GlobalType, att, val);

  quit(): void;

  reset(): void;

  set(key: string, value: any, options): Promise<any>;

  statsWithKey(key: string, callback: (err?: Error | null, server?: string, stats?: Record<string, string>) => void): void;
}
