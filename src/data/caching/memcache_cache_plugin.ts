/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Client } from 'memjs';
import config from '../../config';
import { GlobalType } from '../../types/items';
import { EntityType } from '../../types/shared';
import { compress, uncompress } from '../../utils/funcs';
import logging from '../../utils/logging';
import { ICachePlugin } from './i_cache_plugin';

const LOG_NAME = 'data.caching.memcache';

export class MemcacheCachePlugin implements ICachePlugin {
  private cache_client: Client;
  private readonly log_name = 'data.caching.MemcacheCachePlugin';

  constructor() {
    this.internalConstructor();
  }

  private trimKey(key: string) {
    return key.slice(0, 245) + key.slice(-2);
  }

  public cacheAttVal(profile: string, type: EntityType | GlobalType, att, val, result, expires = null): Promise<void> {
    const compressed_buffer = compress(result);

    return new Promise(c => {
      expires = expires ? expires : parseInt(config.get('ATTRIBUTE_CACHE_EXPIRES', 600), 10);
      const key = this.trimKey(`${profile}_${type}_${att}_${val}`);
      this.cache_client.set(key, compressed_buffer, { expires }, err => {
        if (err) {
          logging.errorFP(this.log_name, 'cacheAttVal', profile, `Error setting ${type}.${att}`, err);
          this.quit();
          this.internalConstructor();
        }
        c();
      });
    });
  }

  public clearCacheAttVal(profile: string, type: EntityType | GlobalType, att, val): Promise<void> {
    return new Promise(c => {
      const key = this.trimKey(`${profile}_${type}_${att}_${val}`);
      this.cache_client.delete(key, err => {
        if (err) {
          logging.errorFP(this.log_name, 'clearCacheAttVal', profile, `Error deleting ${type}.${att}}`, err);
          this.quit();
          this.internalConstructor();
        }
        c();
      });
    });
  }

  delete(key: string): Promise<void> {
    return new Promise(c => this.cache_client.delete(this.trimKey(key), () => c()));
  }

  get(key: string): Promise<any> {
    try {
      return new Promise((c,r) => {
        this.cache_client.get(this.trimKey(key), (err, value, flags) => {
          if (err) r(err);
          if (value) c({value: value, flags: flags});
          c(undefined);
        });
      });
    } catch(e) {
      logging.errorF(LOG_NAME, 'get', `Error getting key ${this.trimKey(key)}`, e);
      this.quit();
      this.internalConstructor();
      throw e;
    }
  }

  public async lookupCacheAttVal(profile: string, type: EntityType | GlobalType, att, val): Promise<any> {
    let compressed_buffer: any = await new Promise(c => {
      const key = this.trimKey(`${profile}_${type}_${att}_${val}`);
      this.cache_client.get(key, (e, r: any) => {
        if (e) {
          logging.errorFP(this.log_name, 'lookupCacheAttVal', profile, `Error getting ${type}.${att}`, e);
          this.quit();
          this.internalConstructor();
        }
        if (r) c(r);
        else c(null);
      });
    });

    if (compressed_buffer) {
      compressed_buffer = Buffer.from(compressed_buffer);
      if (compressed_buffer.data) compressed_buffer = compressed_buffer.data;

      return uncompress(compressed_buffer);
    }

    return null;
  }

  quit(): void {
    this.cache_client.quit();
  }

  public reset(): void {
    try {
      this.quit();
    } catch (err) {
      logging.warnF(this.log_name, 'reset', 'Issue calling quit', err);
      this.cache_client.close();
    }

    this.internalConstructor();
  }

  public set(key: string, value: any, options): Promise<any> {
    return new Promise(c => this.cache_client.set(this.trimKey(key), value, options, c)).catch(e => {
      this.quit();
      this.internalConstructor();
    });
  }

  statsWithKey(key: string, callback: (err?: Error | null, server?: string, stats?: Record<string, string>) => void): void {
    this.cache_client.statsWithKey(this.trimKey(key), callback);
  }

  private internalConstructor() {
    let memCacheUrl = config.get('MEMCACHE_URL');
    if (config.get('MEMCACHE_USERNAME')) memCacheUrl = `${config.get('MEMCACHE_USERNAME')}:${config.get('MEMCACHE_PASSWORD')}@${memCacheUrl}`;
    this.cache_client = Client.create(memCacheUrl, {
      retries: 5,
      failover: true,
      logger: logging.logger,
      timeout: 2,
      conntimeout: 5,
      // conntimeout: 30000,
      // reconnect: 1000,
      // retry: 300,
      // idle: 30000,
      retry_delay: 0.5,
      failoverTime: 30,
      keepAlive: true,
    });
  }
}
