/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import fs from 'fs';
import path from 'path';
import config from '../../config';
import { GlobalType } from '../../types/items';
import { EntityType } from '../../types/shared';
import * as funcs from '../../utils/funcs';
import logging from '../../utils/logging';
import { ICachePlugin } from './i_cache_plugin';


let CACHE_PATH = 'cache'; // '../../../cache/cache.mem';

config.onLoad('offline_cache', async () => {
  CACHE_PATH = `${config.get('CACHE_PATH', 'cache')}`;
});

export class OfflineCachePlugin implements ICachePlugin {
  private cache: { [key: string]: Buffer } = {};
  private readonly cache_filename = path.resolve(__dirname, '../../../', CACHE_PATH, 'cache.mem');
  private readonly log_name = 'data.caching.OfflineCachePlugin';

  constructor() {
    if (logging.isDebug()) logging.debugF(this.log_name, 'constructor', `Checking ${this.cache_filename} for offline memcache`);
    if (fs.existsSync(this.cache_filename)) {
      const cache_file = fs.readFileSync(this.cache_filename);
      if (logging.isDebug()) logging.debugF(this.log_name, 'constructor', `Reading ${cache_file.length} byte offline memcache from ${this.cache_filename}`);
      this.cache = funcs.uncompress(cache_file);
    }

    process.on('exit', this.quit.bind(this));
    process.on('SIGINT', this.quit.bind(this));
    process.on('SIGUSR1', this.quit.bind(this));
    process.on('SIGUSR2', this.quit.bind(this));
    process.on('uncaughtException', this.quit.bind(this));
  }

  cacheAttVal(profile: string, type: EntityType | GlobalType, att, val, result, expires = null): void {
    this.cache[`${profile}_${type}_${att}_${val}`] = Buffer.from(JSON.stringify(result));
  }

  clearCacheAttVal(profile: string, type: EntityType | GlobalType, att, val): void {
    delete this.cache[`${profile}_${type}_${att}_${val}`];
    // this.flush();
  }

  delete(key: string): Promise<void> {
    delete this.cache[key];
    return new Promise(c => c());
  }

  get(key: string): Promise<{ value: any }> {
    return new Promise(c => {
      c({ value: this.cache[key] });
    });
  }

  keys(): string[] {
    return Object.keys(this.cache);
  }

  lookupCacheAttVal(profile: string, type: EntityType | GlobalType, att, val) {
    const result = this.cache[`${profile}_${type}_${att}_${val}`];
    if (result) {
       if('data' in result) return JSON.parse(Buffer.from(result['data'] as Buffer).toString());
       else  return JSON.parse(Buffer.from(result).toString());
    }
    return null;
  }

  quit(): void {
    this.flush();
  }

  public reset(): void {
    this.cache = {};
  }

  set(key: string, value: any, options): Promise<any> {
    this.cache[key] = Buffer.from(value);
    // this.flush();
    return new Promise<void>(c => c());
  }

  statsWithKey(key: string, callback: (err?: Error | null, server?: string, stats?: Record<string, string>) => void): void {
    logging.warnF(this.log_name, 'statsWithKey', 'not implemented');
  }

  private flush() {
    const cache_file = funcs.compress(this.cache);
    if (logging.isDebug()) logging.debugF(this.log_name, 'flush', `Flushing ${cache_file.length} byte offline memcache to ${this.cache_filename}`);
    fs.writeFileSync(this.cache_filename, cache_file);
  }
}
