import { OnenotePage } from '@microsoft/microsoft-graph-types';
import cheerio from 'cheerio';
import util from 'util';
import config from '../../config';
import ForaUser from '../../session/user';
import { AbstractMicrosoftGraphHelper } from '../../sources/helpers/microsoft/a_microsoft_graph_helper';
import MicrosoftGraphOneNoteHelper from '../../sources/helpers/microsoft/microsoft_graph_one_note_helper';
import { Note, Person } from '../../types/items';
import { EntityType, Uid } from '../../types/shared';
import logging from '../../utils/logging';
import { ICachePlugin } from '../caching/i_cache_plugin';
import { INoteStore } from './i_note_store';

const LOG_NAME = 'data.storage.MicrosoftOneNoteNoteStore';

/**
 * Microsoft One Note Document Store Plugin
 * ----------------------------------------
 * Storage = Microsoft OneNote
 *
 * Plugin will ensure that there is an AskFora notebook within the users personal OneNote. Each Document will be stored as a page within.
 *
 * Notes:
 *  - Get all notebooks and sections in one call
 *    - /me/onenote/notebooks?$expand=sections,sectionGroups($expand=sections)
 *    - This doesn't seem to to work $expand=sections,sectionGroups($expand=sections,sectionGroups($levels=max;$expand=sections)) - too may layers deep
 *  - Default sorting is slow (lastModifiedDate), use something else
 *    - ~/sections/{id}/oneNotePages?$select=id,name,creationDate&$orderby=creationDate
 *
 * Links:
 *  - https://developer.microsoft.com/en-us/graph/graph-explorer
 *  - https://docs.microsoft.com/en-us/graph/api/resources/onenote-api-overview?view=graph-rest-1.0
 *  - https://github.com/OneNoteDev/MsGraph_OneNoteApiSampleNodejs
 *  - https://developer.microsoft.com/en-us/office/blogs/a-few-performance-tips-for-using-the-onenote-api/
 *  - https://blogs.msdn.microsoft.com/onenotedev/2014/06/12/new-beta-endpoint-create-page-in-any-location/
 */
export default class MicrosoftOneNoteNoteStore implements INoteStore {
  constructor(private readonly cache: ICachePlugin) {}

  async clearNotes(user: ForaUser, account: Uid, destroy_containers?: boolean): Promise<number> {
    return 0;
  }

  async deleteNote(user: ForaUser, account: Uid, f_note: Note): Promise<void> {
    try {
      const client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
      await MicrosoftGraphOneNoteHelper.oneNotePageDelete(user, account, f_note.file_id, client);
      if (!config.isEnvOffline()) await this.cache.clearCacheAttVal(user.profile, EntityType.Note, 'note', f_note.file_id);
    } catch (err) {
      if (err['statusCode'] && err['statusCode'] === 404) {
        // Note is already gone at the provider, we can safely delete on our end
        logging.warnFP(LOG_NAME, 'deleteNote', user.profile, `Note is not present at provider, continuing ${f_note.file_id} [404]`);
        if (!config.isEnvOffline()) await this.cache.clearCacheAttVal(user.profile, EntityType.Note, 'note', f_note.file_id);
      } else {
        // Unknown error, blast away
        throw err;
      }
    }
  }

  async getNote(user: ForaUser, account: Uid, file_id): Promise<Note> {
    if (!config.isEnvOffline()) {
      const cached_file = await this.cache.lookupCacheAttVal(user.profile, EntityType.Note, 'note', file_id);
      if (cached_file) return new Note(cached_file);
    }

    const client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

    // Get the content from OneNote
    const ms_page = await MicrosoftGraphOneNoteHelper.oneNotePageGet(user, account, file_id, client);
    const content = await MicrosoftGraphOneNoteHelper.oneNotePageGetContent(user, account, file_id, client);

    if (ms_page && content) {
      const note = MicrosoftOneNoteNoteStore.decodePage(user, ms_page, content);
      if (!config.isEnvOffline()) await this.cache.cacheAttVal(user.profile, EntityType.Note, 'note', file_id, note);
      return note;
    }
  }

  async loadNotes(user: ForaUser, account: Uid): Promise<Note[]> {
    const notes: Note[] = [];

    const client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
    const ms_pages = await MicrosoftGraphOneNoteHelper.oneNotePages(user, account, client);
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadNotes', user.profile, `Pages = ${util.format(ms_pages)}`);

    if (ms_pages.length) {
      const r_notes = await Promise.all<Note>(
        ms_pages.map(async ms_page => {
          if (!config.isEnvOffline()) {
            // If we already have the page/note cached, use it
            const cached_file = await this.cache.lookupCacheAttVal(user.profile, EntityType.Note, 'note', ms_page.id);
            if (cached_file) return new Note(cached_file);
          }

          // Get the content from OneNote
          const content = await MicrosoftGraphOneNoteHelper.oneNotePageGetContent(user, account, ms_page.id, client);

          if (content) {
            const note = MicrosoftOneNoteNoteStore.decodePage(user, ms_page, content);
            if (!config.isEnvOffline()) await this.cache.cacheAttVal(user.profile, EntityType.Note, 'note', ms_page.id, note);
            return note;
          }
        }),
      );

      // Notice that the above logic will only return a doc to docs if there is content. We are just being extra cautious here
      for (const r_note of r_notes) if (r_note) notes.push(r_note);
    } else logging.warnFP(LOG_NAME, 'loadNotes', user.profile, 'No pages in OneNote');

    return notes;
  }

  async newNote(user: ForaUser, account: Uid, f_note: Note): Promise<Note> {
    const contents = MicrosoftOneNoteNoteStore.encodePageFull(f_note);
    const ms_page = await MicrosoftGraphOneNoteHelper.oneNotePageCreate(user, account, contents);
    f_note.id = f_note.id ? f_note.id : ms_page.id;
    f_note.file_id = ms_page.id;
    f_note.created = new Date(ms_page.createdDateTime);

    if (!config.isEnvOffline()) await this.cache.cacheAttVal(user.profile, EntityType.Note, 'note', f_note.file_id, f_note);

    return f_note;
  }

  async saveNote(user: ForaUser, account: Uid, f_note: Note): Promise<void> {
    const contents = MicrosoftOneNoteNoteStore.encodePageBody(f_note);
    await MicrosoftGraphOneNoteHelper.oneNotePageUpdateBody(user, account, f_note.file_id, contents);
    if (!config.isEnvOffline()) await this.cache.cacheAttVal(user.profile, EntityType.Note, 'note', f_note.file_id, f_note);
  }

  private static decodePage(user: ForaUser, page: OnenotePage, contentHtml: string): Note {
    // 	<head>
    // 		<title>2019-01-07T16:30:56.1275798Z</title>
    // 		<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    // 	</head>
    // 	<body data-absolute-enabled="true" style="font-family:Calibri;font-size:11pt">
    // 		<div id="div:{b4ee2b46-571a-4a6c-bfef-f0d3840ac57a}{32}" data-id="_default" style="position:absolute;left:48px;top:120px;width:624px">
    // 			<h1 id="h1:{b4ee2b46-571a-4a6c-bfef-f0d3840ac57a}{40}" lang="en" data-tag="important" style="font-size:16pt;color:#1e4e79;margin-top:5.5pt;margin-bottom:5.5pt">Here are my notes</h1>
    // 			<p id="p:{b4ee2b46-571a-4a6c-bfef-f0d3840ac57a}{44}" lang="en" data-tag="contact" data-id="people/m0000000000001" style="margin-top:5.5pt;margin-bottom:5.5pt">Test Person #1</p>
    // 			<p id="p:{b4ee2b46-571a-4a6c-bfef-f0d3840ac57a}{47}" lang="en" data-tag="contact" data-id="people/m0000000000002" style="margin-top:5.5pt;margin-bottom:5.5pt">Test Person #2</p>
    // 		</div>
    // 	</body>
    //  </html>

    const $ = cheerio.load(contentHtml);

    // First `data-tag="important"` is our note text
    const content_string = $('*[data-id="note_body"]').text();
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'decodePage', user.profile, `Raw content = '${util.format(content_string)}'`);
    const content_array = content_string.split('\n').map(content => content.trim());
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'decodePage', user.profile, `Parsed content = '${util.format(content_array)}'`);

    // Grab all the `data-tag="contact"` for people entries
    const people: Partial<Person>[] = [];
    $('*[data-tag="contact"]')
      .each((i, el) => { people.push({ id: $(el).data('id'), displayName: $(el).text() } as Partial<Person>) })
      .get();
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'decodePage', user.profile, `Parsed people = '${util.format(people)}'`);

    // Grab the ID of the note (can be different the the page ID)
    const note_id = $('*[data-id="note_id"]').text();

    return new Note({
      id: note_id ? note_id : page.id,
      file_id: page.id,
      created: new Date(page.createdDateTime),
      notes: content_array,
      people,
    });
  }

  private static encodePageBody(note: Note): string {
    const contacts = [];

    for (const person of note.people) {
      contacts.push(`<p data-tag="contact" data-id="${person.id}">${person.displayName}</p>`);
    }

    return `
<h1 data-tag="important" data-id="note_body">${note.notes.join('<br />')}</h1>
<h1 data-tag="important" data-id="note_id">${note.id}</h1>
${contacts.join('\n')}
`;
  }

  private static encodePageFull(note: Note): string {
    const title = note.created ? note.created.toISOString() : 'Unset - will set after creation';
    const body = MicrosoftOneNoteNoteStore.encodePageBody(note);

    return `
<!DOCTYPE html>
<html lang="en">
  <head>
    <title>${title}</title>
  </head>
  <body>
${body}
  </body>
</html>`;
  }
}
