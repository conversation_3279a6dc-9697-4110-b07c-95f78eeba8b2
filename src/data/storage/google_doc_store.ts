/*************************************************
 * Google Notes
 *
 * http://google.github.io/google-api-nodejs-client/modules/_apis_drive_v3_.html
 * https://github.com/google/google-api-nodejs-client/blob/master/src/apis/drive/v3.ts
 *************************************************/

import { OAuth2Client } from 'google-auth-library';
import { drive_v3, google } from 'googleapis';
import _ from 'lodash';
import { Readable } from 'stream';
import util from 'util';

import ForaUser from '../../session/user';

import { InternalError } from '../../types/globals';
import { Document, Note, Person } from '../../types/items';
import { AuthLevel, AuthProviders, EntityType, Uid } from '../../types/shared';

import * as format from '../../utils/format';
import * as functions from '../../utils/funcs';
import logging from '../../utils/logging';

import { AuthProvider } from '../../auth/auth_provider';
import { GoogleRetryHelper } from '../../sources/helpers/google/google_retry_helper';
import { ICachePlugin } from '../caching/i_cache_plugin';
import { FileFilter, IFileStore } from './i_file_store';
import { INoteStore } from './i_note_store';

const drive = google.drive('v3');
const LOG_NAME = 'data.storage.GoogleDocStore';

class GDriveReadable extends Readable {
  constructor(public buff, options = {}) {
    super(options);
  }

  _read(size) {
    if (this.buff.length) {
      const len = this.buff.length < size ? this.buff.length : size;
      const read = this.buff.slice(0, len);
      this.buff = this.buff.slice(len);
      if (!this.push(read)) return;
    }
    let i = 0;
    while (this.push(null)) {
      // wait for read
      i++;
    }
  }
}

export default class GoogleDocStore implements IFileStore, INoteStore {
  constructor(private readonly cache: ICachePlugin) {}

  async clearFiles(user: ForaUser, account: Uid, filter: FileFilter, destroy_containers?: boolean): Promise<number> {
    // destroy_containers is a NO-OP because we did not create any containers in this implementation
    const query = _.toPairs(filter)
      .map(x => `appProperties has { key = '${x[0]}' and value = '${x[1]}' }`)
      .join(' and ');
    return this.driveClearFiles(user, account, query, false);
  }

  clearNotes(user: ForaUser, account: Uid, destroy_containers?: boolean): Promise<number> {
    // destroy_containers is a NO-OP because we did not create any containers in this implementation
    return this.driveClearFiles(user, account, "appProperties has { key = 'type' and value = 'note' }", true);
  }

  deleteFile(user: ForaUser, account: Uid, doc: Document): Promise<void> {
    return this.driveDeleteFile(user, account, doc, false);
  }

  deleteNote(user: ForaUser, account: Uid, note: Note): Promise<void> {
    return this.driveDeleteFile(user, account, new Document({ id: note.id, file_id: note.file_id }), true);
  }

  getFile(user: ForaUser, account: Uid, file_id: string): Promise<Document> {
    return this.driveGetFile(user, account, file_id, false);
  }

  async getNote(user: ForaUser, account: Uid, note_id: string): Promise<Note> {
    const doc = await this.driveGetFile(user, account, note_id, true);
    return GoogleDocStore.convertDocToNote(doc);
  }

  loadFiles(user: ForaUser, account: Uid, filter: FileFilter): Promise<Document[]> {
    const query = _.toPairs(filter)
      .map(x => `appProperties has { key = '${x[0]}' and value = '${x[1]}' }`)
      .join(' and ');
    return this.driveLoadFiles(user, account, query, false);
  }

  async loadNotes(user: ForaUser, account: Uid): Promise<Note[]> {
    await this.driveRepairType(user, account, true);
    const notes: Note[] = [];
    const docs = await this.driveLoadFiles(user, account, "appProperties has { key = 'type' and value = 'note' }", true);
    for (const doc of docs) notes.push(GoogleDocStore.convertDocToNote(doc));

    return notes;
  }

  newFile(user: ForaUser, account: Uid, doc: Document): Promise<Document> {
    return this.driveNewFile(user, account, doc, false);
  }

  streamFile(user: ForaUser, account: Uid, doc: Document): Promise<Document> {
    throw new InternalError(500, 'Stream file to Google Doc Store is not supported');
  }

  async newNote(user: ForaUser, account: Uid, note: Note): Promise<Note> {
    const doc_in = GoogleDocStore.convertNoteToDoc(user, note);
    const doc_out = await this.driveNewFile(user, account, doc_in, true);
    return GoogleDocStore.convertDocToNote(doc_out);
  }

  saveFile(user: ForaUser, account: Uid, doc: Document): Promise<void> {
    return this.driveSaveFile(user, account, doc, false);
  }

  saveNote(user: ForaUser, account: Uid, note: Note): Promise<void> {
    const doc_in = GoogleDocStore.convertNoteToDoc(user, note);
    return this.driveSaveFile(user, account, doc_in, true);
  }

  getAuthClient(user: ForaUser, account: Uid): OAuth2Client {
    return AuthProvider.google.getAuthClient(user.getTokens(AuthProviders.Google, account));
  }

  private async driveClearFiles(user: ForaUser, account: Uid, query: string, cache: boolean): Promise<number> {
    const oauth2Client = this.getAuthClient(user, account);

    let count = 0;
    const args = {
      auth: oauth2Client as any,
      spaces: 'appDataFolder',
      fields: 'nextPageToken, files(id,name,appProperties)',
      q: query,
    } as drive_v3.Params$Resource$Files$List;

    const result = await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.list, 'drive.files.list', drive, args);

    if (result && result.data && result.data.files) {
      for (const file_md of result.data.files) {
        await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.delete, 'drive.files.delete', drive, { auth: oauth2Client as any, fileId: file_md.id });
        count++;

        if (cache) await this.cache.clearCacheAttVal(user.profile, EntityType.Document, 'doc', file_md.id);
      }
    }

    return count;
  }

  private async driveDeleteFile(user: ForaUser, account: Uid, doc: Document, cache: boolean): Promise<void> {
    const args = {
      auth: this.getAuthClient(user, account) as any,
      spaces: 'appDataFolder',
      fileId: doc.file_id,
    } as drive_v3.Params$Resource$Files$Delete;

    await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.delete, 'drive.files.delete', drive, args);

    if (cache) await this.cache.clearCacheAttVal(user.profile, EntityType.Document, 'doc', doc.file_id);
    return;
  }

  private async driveGetFile(user: ForaUser, account: Uid, file_id: string, cache: boolean): Promise<Document> {
    if (cache) {
      const cached_file = await this.cache.lookupCacheAttVal(user.profile, EntityType.Document, 'doc', file_id);
      if (cached_file) return cached_file;
    }

    const md_args = {
      auth: this.getAuthClient(user, account) as any,
      fileId: file_id,
      // alt: 'media',
      fields: 'id,createdTime,name,appProperties',
    };

    const data_args = {
      auth: this.getAuthClient(user, account) as any,
      fileId: file_id,
      alt: 'media',
    };

    const file_md: drive_v3.Schema$File = await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.get, 'drive.files.get', drive, md_args);
    const file_data: drive_v3.Schema$File = await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.get, 'drive.files.get', drive, data_args);

    if (file_md && file_md['data'] && file_data && file_data['data']) {
      const doc = new Document({
        id: file_md['data'].appProperties.id,
        file_id: file_md['data'].id,
        created: file_md['data'].createdTime,
        props: file_md['data'].appProperties,
        body: functions.uncompress(file_data['data']), // from alt:'media'
      });

      if (cache) await this.cache.cacheAttVal(user.profile, EntityType.Document, 'doc', file_md['data'].id, doc);

      return doc;
    }

    return null;
  }

  private async driveLoadFiles(user: ForaUser, account: Uid, query: string, cache: boolean): Promise<Document[]> {
    if (!user.isAuthenticated(AuthLevel.Organizer)) return [];

    const docs: Document[] = [];
    const oauth2Client = this.getAuthClient(user, account);

    const args = {
      auth: oauth2Client as any,
      spaces: 'appDataFolder',
      fields: 'nextPageToken, files(id,createdTime,name,appProperties)',
      q: query,
    } as drive_v3.Params$Resource$Files$List;

    const result = await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.list, 'drive.files.list', drive, args);

    if (result) { if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'driveLoadFiles', user.profile, `Response = ${util.format(result.data)}`); }
    else logging.warnF(LOG_NAME, 'driveLoadFiles', `No result from GDrive files list for ${user.profile}`);

    if (result && result.data && result.data.files) {
      // don't do this in parallel, Google doesn't like us and then throttles us
      for (const file_md of result.data.files) {
        // docs = await Promise.all<Document>(result.data.files.map(async file_md => {
        if (cache) {
          const cached_file = await this.cache.lookupCacheAttVal(user.profile, EntityType.Document, 'doc', file_md.id);
          if (cached_file) docs.push(cached_file); // return cached_file;
        }

        const args = { auth: oauth2Client as any, fileId: file_md.id, alt: 'media' };
        let file: drive_v3.Schema$File = null;

        file = await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.get, 'drive.files.get', drive, args);

        if (file && 'data' in file) {
          const doc = new Document({
            id: file_md.appProperties.id,
            file_id: file_md.id,
            created: file_md.createdTime,
            body: file['data'] as Buffer, // from alt:'media'
            props: file_md.appProperties,
          });

          if (cache) await this.cache.cacheAttVal(user.profile, EntityType.Document, 'doc', file_md.id, doc);
          // return doc;
          docs.push(doc);
        }
      } // ));
    }

    return docs.filter(d => d);
  }

  private async driveNewFile(user: ForaUser, account: Uid, doc: Document, cache: boolean): Promise<Document> {
    if (!doc.id) {
      doc.id = doc.props && doc.props.id ? doc.props.id : functions.hash(JSON.stringify(doc));
      if (!doc.props) doc.props = { id: doc.id };
      else doc.props.id = doc.id;
    }

    const body = new GDriveReadable(functions.compress(doc.body));

    const args = {
      auth: this.getAuthClient(user, account) as any,
      requestBody: { name: doc.id, parents: ['appDataFolder'], appProperties: doc.props } as drive_v3.Schema$File,
      media: { mimeType: doc.mime, body },
      fields: 'id,createdTime',
    } as drive_v3.Params$Resource$Files$Create;

    const new_doc = await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.create, 'drive.files.create', drive, args);

    if (new_doc) {
      doc.file_id = new_doc.data.id;
      doc.created = new_doc.data.createdTime;
    }

    if (cache) await this.cache.cacheAttVal(user.profile, EntityType.Document, 'doc', doc.file_id, doc);
    return doc;
  }

  private async driveRepairType(user: ForaUser, account: Uid, cache: boolean) {
    const docs = await this.driveLoadFiles(user, account, '', cache);
    for (const doc of docs) {
      let updated = false;
      if (!doc.props) {
        doc.props = { type: 'note' };
        updated = true;
      } else if (!doc.props.type) {
        doc.props.type = 'note';
        updated = true;
      }

      if (updated) {
        logging.infoFP(LOG_NAME, 'driveRepairType', user.profile, `Repairing type to 'note' on ${doc.file_id}`);
        await this.driveSaveFile(user, account, doc, cache);
      }
    }
  }

  private async driveSaveFile(user: ForaUser, account: Uid, doc: Document, cache: boolean): Promise<void> {
    const args = {
      auth: this.getAuthClient(user, account) as any,
      fileId: doc.file_id,
      media: { mimeType: doc.mime, body: doc.body },
      requestBody: { appProperties: doc.props } as drive_v3.Schema$File,
    } as drive_v3.Params$Resource$Files$Update;

    await GoogleRetryHelper.repeatableCallWithBackOff(drive.files.update, 'drive.files.update', drive, args);
    if (cache) await this.cache.cacheAttVal(user.profile, EntityType.Document, 'doc', doc.file_id, doc);
  }

  private static convertDocToNote(new_doc: Document) {
    const body = new_doc.body ? format.stringFromBody(new_doc.body) : '';

    const note = new Note({
      id: new_doc.id,
      file_id: new_doc.file_id,
      created: new_doc.created,
      notes: body.split('\n'),
    });

    const people: { [key: string]: Partial<Person> } = {};

    if (new_doc.props) {
      for (const index in new_doc.props) {
        const id = index.slice(1);
        const value = new_doc.props[index];
        switch (index[0]) {
          case 'p':
            if (!people[id]) people[id] = { id: value };
            else people[id].id = value;
            break;
          case 'd':
            if (!people[id]) people[id] = { displayName: value };
            else people[id].displayName = value;
            break;
        }
      }
    }

    note.people = Object.values(people);

    return note;
  }

  private static convertNoteToDoc(user: ForaUser, note: Note) {
    const props = { id: note.id, type: 'note' };

    if (note.people) {
      for (const index in note.people) {
        const person = note.people[index];
        if (person.id) {
          props[`p${index}`] = person.id;
          props[`d${index}`] = person.displayName;
        }
      }
    }

    const made_doc = new Document({
      id: note.id,
      file_id: note.file_id,
      body: note.notes.join('\n'),
      mime: 'text/plain',
      props,
    });

    // if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'convertNoteToDoc', user.profile, `User = ${util.format(user)}`);
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'convertNoteToDoc', user.profile, `Doc = ${util.format(made_doc)}`);

    return made_doc;
  }
}
