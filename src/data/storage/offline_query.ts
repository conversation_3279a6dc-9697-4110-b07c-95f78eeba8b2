import { Query, QueryResultsOptions, SimpleQueryRowsResponse } from '@google-cloud/bigquery';
import fs from 'fs';
import _ from 'lodash';
import path from 'path';

import { InternalError } from '../../types/globals';
import { Person } from '../../types/items';
import { EntityType, TagType, Uid, findTypeTags, findTypeValues } from '../../types/shared';
import { User } from '../../types/user';

import { compress, uncompress } from '../../utils/funcs';
import logging from '../../utils/logging';
import peopleUtils from '../../utils/people';

import OfflineDatastore from './offline_datastore';

const DEBUG = (require('debug') as any)('fora:data:storage:offlinequery');
const LOG_NAME = 'data:storage.OfflineQuery';

const CACHE_PATH = '../../../cache/';
const STORE_FILENAME = 'query.ds';
const COMMS_FILENAME = 'comms.ds';
const GLOBAL_PEOPLE = 'people_query.ds';

export default class OfflineQuery { // extends BigQuery {
  private readonly cache_path = path.resolve(__dirname, CACHE_PATH);
  private global_store: { [key: string]: { [key: string]: Person } } = {}; // globally {id:person}
  private readonly known_ids: string[] = [];
  private readonly ods: OfflineDatastore;
  private readonly tag_store: { [key: string]: { [key: string]: Uid[] } } = {}; // {profile:{tag_value:[ids]}}
  private readonly comm_store: { [key: string]: { [key: string]: Uid[] } } = {}; // {profile:{comm_value:[ids]}}

  constructor(ods: OfflineDatastore) {
    // super();

    this.ods = ods;

    if (fs.existsSync(this.cache_path)) {
      this.known_ids = fs.readdirSync(this.cache_path).filter(i => i.match(/\d*/)[0].length);
      this.loadStore();
    } else fs.mkdirSync(this.cache_path, 0o744);

    process.on('exit', this.quit.bind(this));
    process.on('SIGINT', this.quit.bind(this));
    process.on('SIGUSR1', this.quit.bind(this));
    process.on('SIGUSR2', this.quit.bind(this));
    process.on('uncaughtException', this.quit.bind(this));
  }

  async createJob(options: any): Promise<any> {
    if (options.configuration && options.configuration.query) return this.query(options.configuration.query);

    logging.infoFP(LOG_NAME, 'createJob', options.id, '');
    // copy data from data store
    const id = options.id;
    if (!this.global_store[id]) this.global_store[id] = {};
    if (!this.tag_store[id]) this.tag_store[id] = {};
    if (!this.comm_store[id]) this.comm_store[id] = {};

    const [results] = await this.ods.runQuery({ namespace: id, kinds: [EntityType.Person.split('.').pop()], entityFilters: [], filters: [] });
    if (results) {
      for (const person of results as Person[]) {
        const person_id = person.id; //.replace('people/t', '');
        this.global_store[id][person_id] = person;

        for (const tag of person.tags) {
            const val = tag.value.toLowerCase();
            if (this.tag_store[id][val]) {
              if (!this.tag_store[id][val].includes(person_id)) this.tag_store[id][val].push(person_id);
            }
            else this.tag_store[id][val] = [person_id];
          // }
        }

        for (const comm of person.comms) {
          if (this.comm_store[id][comm]) {
            if (!this.comm_store[id][comm].includes(person_id)) this.comm_store[id][comm].push(person_id);
          }
          else this.comm_store[id][comm] = [person_id];
        }
      }
    }
    this.flushStore(id);
    this.flushGlobal();
    return {status:{state:'DONE'}};
  }

  /* async query(query: string, options?: QueryResultsOptions): Promise<QueryRowsResponse> {
    const [r, s] = await this.query({ query } as Query, options);
    return [r, null, s];
  } */

  dataset(d) {
    return {
      table: t => {
        return {
          insert: (p,o) => {
            logging.test(`Insert ${d}.${t} ${JSON.stringify(p)}`);
            return new Promise<void>(c => c());
          }
        }
      }
    }
  }

  async query(query: Query, _options?: QueryResultsOptions): Promise<SimpleQueryRowsResponse> {
    const lquery = query.query.toLowerCase();
    const pr = lquery.match(/people\.people_(\d*)/);
    const profile = pr ? pr[1] : null;

    if (!query.params) {
      logging.warnF(LOG_NAME, 'query', `No query parameters ${query.query}`, new Error());
      return;
    }

    if (query.params['tag_types']) return this.getUserTags(query.params['tag_types'], query.params['vanity'], query.params['user_comms']);
    else if (query.params && query.params['user_comms']) return this.connections(query.params['profile'], query.params['user_comms'], query.params['conn_email']);
    else if (lquery.startsWith('insert') || lquery.startsWith('update')) return this.storePerson(profile, query.query, query.params as any);
    else if (lquery.startsWith('delete')) return this.deletePerson(profile, query.params);
    else if (lquery.startsWith('select distinct(profile)')) return this.queryProfiles(query.params);
    else if (lquery.startsWith('select')) return this.queryPerson(query.params, lquery.includes('from people.users'));
    else if(lquery.startsWith('drop')) return this.deleteUser(lquery.split(' ')[2]);
  }

  async getUserTags(tag_types: TagType[], vanity: string, user_comms: string[]): Promise<SimpleQueryRowsResponse> {
    const tag_data: {[key:string]: {num: number, weight: number}} = {};

    // count tagtypes from everyone who doesn't have the vanity or user_comms
    for (const profile in this.global_store) {
      const profile_people = this.global_store[profile];
      for (const person of Object.values(profile_people)) {
        if (person.id === `people/t${profile}`) {
          if (person.vanity !== vanity && (!user_comms || _.intersection(person.comms, user_comms).length === 0)) {
            const tags = findTypeTags(person.tags, tag_types);
            for (const tag of tags) {
              if (tag_data[tag.value]) {
                tag_data[tag.value].num++;
                tag_data[tag.value].weight += tag.index;
              } else tag_data[tag.value] = {num: 1, weight: tag.index}
            }
          }
          break;
        }
      }
    }
    
    const results: {num: number, weight: number, value: string}[] = Object.keys(tag_data).map(value => {
      return {
        value,
        num: tag_data[value].num,
        weight: tag_data[value].weight,
      }
    });

    return [results, null]

  }

  async connections(profile: Uid, conn_emails: string[], user_comm: string): Promise<SimpleQueryRowsResponse> {
    const ids: Uid[] = [];
    const users = this.ods.namespaces();
    const emails = conn_emails.slice();
    if (user_comm) emails.push(user_comm);
    for (const namespace of users.filter(u => u !== profile)) {
      const [people] = this.ods.runQuery({namespace, kinds:[EntityType.Person.split('.').pop()], filters: [], entityFilters: [{op:'=', name: 'comms', val:emails}]});
      if (people && people.find(c => _.intersection(c.comms, conn_emails).length > 0)) {
        if (!user_comm || people.find(c => c.comms.includes(user_comm))) ids.push(namespace);
      }
    }
    const results: Partial<User>[] = ids.map(profile => { return { profile }});
    return [results, null]
  }

  public quit(): void {
    this.flush();
  }

  private async deleteUser(profile: Uid): Promise<SimpleQueryRowsResponse> {
    if (this.global_store[profile]) delete this.global_store[profile];
    if (this.tag_store[profile]) delete this.tag_store[profile];
    if (this.comm_store[profile]) delete this.comm_store[profile];
    return [null, null];
  }

  private async deletePerson(profile: Uid, qp: any): Promise<SimpleQueryRowsResponse> {
    if (this.global_store[profile] && this.global_store[profile][qp.id]) {
      const del = this.global_store[profile][qp.id];
      if (del && del.comms && this.comm_store[profile]) {
        for (const comm of del.comms) {
          if (this.comm_store[profile][comm]) {
            this.comm_store[profile][comm] = this.comm_store[profile][comm].filter(i => i !== qp.id);
          }
        }
      }
      delete this.global_store[profile][qp.id];
    }

    if (this.comm_store[profile] && qp.aliases_filter) {
      for (const q of qp.aliases_filter) {
        if (this.comm_store[profile][q]) {
          this.comm_store[profile][q] = this.comm_store[profile][q].filter(i => i !== qp.id);
        }
      }
    }

    return [null, null];
  }

  private flush(id = null) {
    if (id) this.flushStore(id);
    else {
      for (const i of Object.keys(this.tag_store)) this.flushStore(i);
      this.flushGlobal();
    }
  }

  private flushGlobal() {
    const store_filename = path.resolve(this.cache_path, GLOBAL_PEOPLE);
    const store_file = compress(this.global_store);
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'flushStore', `Flushing ${store_file.length} byte query storage of global to ${store_filename}`);
    fs.writeFileSync(store_filename, store_file);
  }

  private flushStore(id) {
    if (id && id.startsWith('g-')) throw new InternalError(500, `Invalid id ${id}`);
    logging.infoFP(LOG_NAME, 'flushStore', id, '');
    if (this.tag_store[id]) {
      const id_path = path.resolve(this.cache_path, id);
      if (!fs.existsSync(id_path)) fs.mkdirSync(id_path);
      const store_filename = path.resolve(id_path, STORE_FILENAME);

      const store_file = compress(this.tag_store[id]);
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'flushStore', `Flushing ${store_file.length} byte query storage of ${id} to ${store_filename}`);
      fs.writeFileSync(store_filename, store_file);
    }

    if (this.comm_store[id]) {
      const id_path = path.resolve(this.cache_path, id);
      if (!fs.existsSync(id_path)) fs.mkdirSync(id_path);
      const store_filename = path.resolve(id_path, COMMS_FILENAME);

      const store_file = compress(this.comm_store[id]);
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'flushStore', `Flushing ${store_file.length} byte query storage of ${id} to ${store_filename}`);
      fs.writeFileSync(store_filename, store_file);

    }
  }

  /* private loadPeople(u_id: Uid, id_set: Uid[]): {[key: string]: Person} {
    // load people
    const people_set: {[key: string]: Person} = {};
    for (const q_id of id_set) {
      const ods_results = this.ods.get({
        namespace: u_id,
        kind: EntityType.Person.split('.').pop(),
        name: q_id.startsWith('people') ? q_id : `people/t${q_id}`,
      });
      DEBUG('loadPeople: ods_results %o', ods_results);
      if (ods_results) {
        const person = new Person(ods_results[0]);
        if (!person.network) {
          person['profile'] = u_id; // { namespace: u_id };
          people_set[person.id] = person;
        }
      }
    }
    return people_set;
  }*/

  private loadStore() {
    for (const id of this.known_ids) {
      const id_path = path.resolve(this.cache_path, id);
      const stat = fs.statSync(id_path);
      if (stat.isDirectory()) {
        this.tag_store[id] = {};
        try {
          const store_filename = path.resolve(id_path, STORE_FILENAME);
          const store_file = fs.readFileSync(store_filename);
          if (logging.isDebug()) logging.debugF(LOG_NAME, 'loadStore', `reading ${store_file.length} query storage of ${id} from ${store_filename}`);
          this.tag_store[id] = uncompress(store_file);
        } catch (err) {
          if (err.code === 'ENOENT') logging.warnF(LOG_NAME, 'loadStore', `No query file for ${id}`);
          else logging.errorF(LOG_NAME, 'loadStore', `OfflineQuery: Error loading query file for ${id}`, err);
        }

        this.comm_store[id] = {};
        try {
          const store_filename = path.resolve(id_path, COMMS_FILENAME);
          const store_file = fs.readFileSync(store_filename);
          if (logging.isDebug()) logging.debugF(LOG_NAME, 'loadStore', `reading ${store_file.length} query storage of ${id} from ${store_filename}`);
          this.comm_store[id] = uncompress(store_file);
        } catch (err) {
          if (err.code === 'ENOENT') logging.warnF(LOG_NAME, 'loadStore', `No query file for ${id}`);
          else logging.errorF(LOG_NAME, 'loadStore', `OfflineQuery: Error loading query file for ${id}`, err);
        }
      }
    }

    try {
      const store_filename = path.resolve(this.cache_path, GLOBAL_PEOPLE);
      if (fs.existsSync(store_filename)) {
        const store_file = fs.readFileSync(store_filename);
        logging.infoF(LOG_NAME, 'loadStore', `reading ${store_file.length} query storage of global from ${store_filename}`);
        this.global_store = uncompress(store_file);
      } else {
        logging.infoF(LOG_NAME, 'loadStore', `reading query storage of global from ods`);
        this.global_store = {};
        for (const id of this.known_ids) {
          this.global_store[id] = {};
          const people = this.ods.runQuery({namespace: id, kinds: [EntityType.Person.split('.').pop()]})
          for (const person of people) this.global_store[id][person.id] = person;
        }
        this.flushGlobal();

      }
    } catch (err) {
      if (err.code === 'ENOENT') logging.warnF(LOG_NAME, 'loadStore', 'No global query file');
      else logging.errorF(LOG_NAME, 'loadStore', 'OfflineQuery: Error loading global query file', err);
    }
  }

  private async queryProfiles(qp: any): Promise<SimpleQueryRowsResponse> {
    const email_filter = [];
    const profiles = [];
    if (qp['aliases_filter']) {
      for (const comm of qp['aliases_filter']) email_filter.push(comm);
      for(const profile of Object.keys(this.global_store)) {
        for(const person of Object.values(this.global_store[profile])) {
          if (_.intersection(person.comms, email_filter).length > 0) {
            profiles.push({profile});
            break;
          }
        }
      }
    }
    return [profiles, null];
  }

  private async queryPerson(qp: any, user_person: boolean): Promise<SimpleQueryRowsResponse> {
    const id_set: Uid[] = [];
    const ids = qp['profiles'] ? qp['profiles'] as Uid[] : this.known_ids;
    const force_network = !qp['profiles'];
    const self_id = qp['profile'];
    const people_set: { [key: string]: Person } = {};

    if (logging.isDebug()) DEBUG('queryPerson %o ids %o', qp, ids);

    const id_filter = {};
    const tag_filter = [];
    let tag_types = [];
    const name_filter = [];
    const email_filter = [];

    for (const key in qp) {
      if (key.startsWith('ids_')) {
        const id = key.slice(4);
        if (id_filter[id]) id_filter[id] = _.uniq(id_filter[id].concat(qp[key]));
        else id_filter[id] = qp[key].slice();
        if (!ids.includes(id)) ids.push(id);
      } 

      if (key === 'tag_values') {
        for (const t of qp[key]) tag_filter.push(t.replace(/%/g,''));
      } else if (key.startsWith('tag_value')) tag_filter.push(qp[key].replace(/%/g,''));

      if (key === 'tag_types') tag_types = qp[key];

      if (key.startsWith('email')) email_filter.push(qp[key].replace(/%/g,''));

      if (key === 'aliases_filter') {
        for (const comm of qp[key]) email_filter.push(comm);
      }

      if (key.startsWith('name_value')) name_filter.push(qp[key].replace(/%/g,''));
    }

    if (logging.isDebug()) DEBUG('queryPerson filters tag %o name %o email %o', tag_filter, name_filter, email_filter);

    if (ids && ids.length) {
      for (const u_id of _.uniq(ids)) {
        let q_id_set = id_filter[u_id] ? id_filter[u_id].slice() : [];
        if (this.tag_store[u_id]) {
          for (const tag_value of tag_filter) {
            // TODO - this needs to do a partial match. If tag_value is `design`, it needs to match `this.tagstore[id]['designer']`
            const t_id_set = this.tag_store[u_id][tag_value];
            if (t_id_set) q_id_set = q_id_set.concat(t_id_set);
          }

          if (this.comm_store[u_id]) {
            for (const email of email_filter) {
              const e_id_set = this.comm_store[u_id][email];
              if (e_id_set) q_id_set = q_id_set.concat(e_id_set.filter(id => !user_person || id === `people/t${u_id}`));
            }
          }
  
          if (logging.isDebug()) DEBUG('queryPerson: q_id_set %o', q_id_set);

          if (q_id_set.length) {
            // add to set
            const f_id_set: string[] = _.uniq(q_id_set.filter(i => !id_set.includes(i)));
            const people = {};
            if (this.global_store[u_id]) {
              for (const id of f_id_set) {
                const person = this.global_store[u_id][id]; // loadPeople(u_id, f_id_set);
                if (person) {
                  const ptag_values = tag_types.length ? findTypeValues(person.tags, tag_types).map(v => v.toLowerCase()) : person.tags.map(t => t.value.toLowerCase());
                  if (tag_filter.length === 0 || _.intersection(ptag_values, tag_filter).length > 0) {
                    id_set.push(id);
                    let [up] = user_person ? await this.ods.runQuery({kinds: ['vanity'], filters: [{name: 'profile', op: '=', val: u_id}] }) : [null];
                    people[person.id] = new Person(up && up.length ? peopleUtils.personFromVanity(up[0]) : person);
                    people[person.id].id = person.id;
                    people[person.id].profile = u_id;
                    people[person.id].vanity = up && up.length ? up[0].vanity : null;
                    if (force_network || u_id !== self_id) people[person.id].network = true;
                  }
                } else logging.warnFP(LOG_NAME, 'queryPerson', u_id, `Couldn't find person ${id}`);
              }
            }
            const loaded_keys = Object.keys(people);
            if (loaded_keys.length !== f_id_set.length) {
              const missing = f_id_set.filter(i => !loaded_keys.includes(`people/t${i}`));
              logging.warnFP(LOG_NAME, 'queryPerson', u_id, `Couldn't load all users: ${missing}`);
            }
            Object.assign(people_set, people);
          }
        } else {
          if (logging.isDebug()) DEBUG('queryPerson: tag store not loaded for %s', u_id);
        }
     }

      if (id_set && id_set.length) {
        if (logging.isDebug()) DEBUG('queryPerson: id_set %o', id_set);

        // this is correct - client looks for an array in result[0]
        // if (ids_only) return [id_set, null]

        const results = id_set.map(id => people_set[id.startsWith('people') ? id : `people/t${id}`]);
        if (logging.isDebug()) DEBUG('queryPerson: results %o', results);
        return [results, null];
      }
    } /* else if (qp.aliases_filter) {
      const id_set: Uid[] = [];
      for (const u_id in this.comm_store) {
        for (const comm of qp.aliases_filter) {
          if (this.comm_store[u_id][`people/t${comm}`]) {
            if (!id_set.includes(u_id)) id_set.push(u_id);
            break;
          }
        }
      }

      if (ids_only) return [id_set, null];

      const namespaces = this.ods.namespaces();
      for (const ns of namespaces) {
        const people = {};
        if (this.global_store[ns]) {
          for (const id of id_set) {
            const person = this.global_store[ns][id]; // loadPeople(u_id, f_id_set);
            if (person) people[person.id] = person;
          }
        }
        Object.assign(people_set, people); //this.loadPeople(ns, id_set));
      }
      const results = id_set.map(id => people_set[id.startsWith('people') ? id : `people/t${id}`]).filter(p => p);
      if (logging.isDebug()) DEBUG('queryPerson: results %o', results);
      return [results, null];
    }*/
    if (logging.isDebug()) DEBUG('queryPerson: no results');
    return [null, null];
  }

  private async storePerson(id: Uid, query: string, qp: any[]): Promise<SimpleQueryRowsResponse> {
    if (!id) {
      logging.infoF(LOG_NAME, 'storePerson', 'Skipping global query table');
      return;
    }

    // this is hard coded to the query in data/index:insertPerson
    let person;
    if (Array.isArray(qp) && qp.length === 8) {
      person = new Person({
        displayName: qp[0],
        comms: qp[1],
        tags: qp[2],
        photos: qp[3],
        names: qp[4],
        urls: qp[5],
        nickName: qp[6],
        id: qp[7],
      });
    } else {
      const raw_tags = query.match(/tags = \[([^\]]*)\],/)[1];
      const all_tags = raw_tags ? raw_tags.split('STRUCT') : null;
      const tags = [];
      if (all_tags) {
        for (const t of all_tags) {
          let type, value, index, start;
          for (const tl of t.split('\n')) {
            if (tl.includes('as')) {
              const [val, tag_type] = tl.split('as');
              switch(tag_type.replace(/[ ,]/g,'')) {
                case 'type':
                  type = val.match(/\s*'([^']*)'/)[1];
                  break;
                case 'value':
                  value = val.match(/\s*'([^']*)'/)[1];
                  break;
                case 'index':
                  index = parseInt(val.match(/\s*(\d*)/)[1], 10);
                  break;
                case 'start':
                  start = new Date(parseInt(val.match(/\s*PARSE_TIMESTAMP\('%s', '([^']*)'/)[1], 10));
                  break;
              }
            }
          }
          if (type !== undefined && value !== undefined && index !== undefined && start !== undefined) {
            tags.push({
              type,
              value,
              index,
              start,
            });
          }
        }
      }

      const params = qp as any;
      const pid = params['id'];
      params['tags'] = tags;
      if (this.global_store[id] && this.global_store[id][pid]) {
        person = this.global_store[id][pid];
        Object.assign(person, params);
        if (tags.length) person['tags' ] = tags;
      } else {
        person = new Person(params);
        logging.infoFP(LOG_NAME, 'storePerson', id, 'Creating global store');
        if (!this.global_store[id]) this.global_store[id] = {};
        this.global_store[id][pid] = person;
      }
    }

    if (person.comms) {
      if (!this.comm_store[id]) this.comm_store[id] = {};
      for (const c of person.comms) this.comm_store[id][c] = [person.id];
    }

    if (!this.tag_store[id]) this.tag_store[id] = {};

    for (const tag of person.tags) {
      const val = tag.value.toLowerCase();
      if (this.tag_store[id][val]) {
        if (!this.tag_store[id][val].includes(person.id)) this.tag_store[id][val].push(person.id);
      }
      else this.tag_store[id][val] = [person.id];
    }

    this.flushStore(id);
  
    return [null, null];
  }
}
