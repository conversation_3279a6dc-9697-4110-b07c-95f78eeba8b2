import ForaUser from '../../session/user';
import { Document } from '../../types/items';
import { Uid } from '../../types/shared';

export enum FileUsage {
  Import = 'import',
  Photo = 'photo',
  Card = 'card',
  Course = 'course',
}

export interface FileFilter {
  type: FileUsage;
}

export interface IFileStore {
  /**
   * Clear all Files/Documents from the storage location that match the given filter
   *
   * @param user - User to clear items for
   * @param filter - Filter the files that we will clear. If destroy_containers is true, all files will be cleared
   * @param destroy_containers - Should we also delete any containers that we created?
   */
  clearFiles(user: ForaUser, account: Uid, filter: FileFilter, destroy_containers?: boolean): Promise<number>;

  /**
   * Delete a File/Document from the storage location
   *
   * @param user - User to delete item for
   * @param doc - Document to delete
   */
  deleteFile(user: ForaUser, account: Uid, doc: Partial<Document>): Promise<void>;

  /**
   * Get a File/Document from the storage location
   *
   * @param user - User to get item for
   * @param file_id - ID of the item to get
   */
  getFile(user: ForaUser, account: Uid, file_id: string): Promise<Document>;

  /**
   * Load all Files/Documents from the storage location
   *
   * @param user - User to load items for
   * @param filter - Filter the files that we will load
   */
  loadFiles(user: ForaUser, account: Uid, filter: FileFilter): Promise<Document[]>;

  /**
   * Create a new File/Document in the storage location
   *
   * @param user - User to create/insert item for
   * @param doc - Document to create/store
   */
  newFile(user: ForaUser, account: Uid, doc: Document): Promise<Document>;

  /**
   * Create a new File/Document in the storage location, return url to stream in contents
   *
   * @param user - User to create/insert item for
   * @param doc - Document to create/store
   */
  streamFile(user: ForaUser, account: Uid, doc: Document): Promise<Document>;

  /**
   * Save an existing File/Document in the storage location
   *
   * @param user - User to save/update item for
   * @param doc - Document to update/store
   */
  saveFile(user: ForaUser, account: Uid, doc: Document): Promise<void>;
}
