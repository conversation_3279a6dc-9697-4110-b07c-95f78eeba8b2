import ForaUser from '../../session/user';
import { Note } from '../../types/items';
import { Uid } from '../../types/shared';

export interface INoteStore {
  /**
   * Clear all Notes from the storage location
   *
   * @param user - User to clear notes for
   * @param destroy_containers - Should we also delete any containers that we created?
   */
  clearNotes(user: ForaUser, account: Uid, destroy_containers?: boolean): Promise<number>;

  /**
   * Delete a Note from the storage location
   *
   * @param user - User to delete note for
   * @param note - Note to delete
   */
  deleteNote(user: ForaUser, account: Uid, note: Note): Promise<void>;

  /**
   * Get a Note from the storage location
   *
   * @param user - User to get note for
   * @param file_id - ID of the note at the storage location to get (not our ID)
   */
  getNote(user: ForaUser, account: Uid, file_id: string): Promise<Note>;

  /**
   * Load all Notes from the storage location
   *
   * @param user - User to load notes for
   */
  loadNotes(user: ForaUser, account: Uid): Promise<Note[]>;

  /**
   * Create a new Note in the storage location
   *
   * @param user - User to create/insert note for
   * @param note - Note to create/store
   */
  newNote(user: ForaUser, account: Uid, note: Note): Promise<Note>;

  /**
   * Save an existing Note in the storage location
   *
   * @param user - User to save/update note for
   * @param note - Note to update/store
   */
  saveNote(user: ForaUser, account: Uid, note: Note): Promise<void>;
}
