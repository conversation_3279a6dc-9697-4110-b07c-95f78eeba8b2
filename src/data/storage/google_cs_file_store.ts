/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Storage } from '@google-cloud/storage';
import util from 'util';

import ForaUser from '../../session/user';

import { GoogleCloudStorageHelper } from '../../sources/helpers/google/google_cloud_storage_helper';
import { GoogleConverter } from '../../sources/helpers/google/google_conversions';
import { retryError } from '../../sources/helpers/google/google_retry_helper';

import { InternalError } from '../../types/globals';
import { Document } from '../../types/items';
import { Uid } from '../../types/shared';

import * as functions from '../../utils/funcs';
import logging from '../../utils/logging';

import { FileFilter, IFileStore } from './i_file_store';

const LOG_NAME = 'data.storage.GoogleCloudStorageFileStore';

export class GoogleCloudStorageFileStore implements IFileStore {
  constructor(private readonly storage: Storage) {}

  async clearFiles(user: ForaUser, account: Uid, filter: FileFilter, destroy_containers?: boolean): Promise<number> {
    let count = 0;
    const files = await GoogleCloudStorageHelper.files(user, account, this.storage);

    if (files && files.length) {
      for (const file of files) {
        const metadata = await GoogleCloudStorageHelper.fileMetadata(user, account, file);

        if (filter) {
          if (filter.type === metadata.metadata.type) {
            await GoogleCloudStorageHelper.fileDelete(user, account, this.storage, file.id);
            count += 1;
          }
        } else {
          await GoogleCloudStorageHelper.fileDelete(user, account, this.storage, file.id);
          count += 1;
        }
      }
    }

    if (destroy_containers) await GoogleCloudStorageHelper.bucketDelete(user, account, this.storage);
    return count;
  }

  async deleteFile(user: ForaUser, account: Uid, doc: Partial<Document>): Promise<void> {
    await GoogleCloudStorageHelper.fileDelete(user, account, this.storage, doc.file_id);
  }

  async getFile(user: ForaUser, account: Uid, file_id: string): Promise<Document> {
    let retry = 100;
    while(retry <= 3200) {
      try { 
        const file = await GoogleCloudStorageHelper.file(user, account, this.storage, file_id);
        if (file) {
          const metadata = await GoogleCloudStorageHelper.fileMetadata(user, account, file);
          const contents = await GoogleCloudStorageHelper.fileContents(user, file);
          if (metadata && contents) return GoogleConverter.convertCloudStorageFileToDoc(user, file, metadata, contents);
          break;
        } else throw new InternalError(404, `File ${file_id} not found`);
      } catch(e) {
        if (!retryError(e)) {
          logging.warnFP(LOG_NAME, 'getFile', user ? user.profile : null,  `Error getting file ${file_id}`, e);
          throw e;
        }
      }
    }
  }

  async loadFiles(user: ForaUser, account: Uid, filter: FileFilter): Promise<Document[]> {
    const docs: Document[] = [];
    const files = await GoogleCloudStorageHelper.files(user, account, this.storage);

    if (files) { if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'loadFiles', user.profile, `loadFiles: length ${files.length}`); }
    else logging.warnFP(LOG_NAME, 'loadFiles', user.profile, 'loadFiles: No result from cloud storage files list');

    if (files && files.length) {
      for (const file of files) {
        const metadata = await GoogleCloudStorageHelper.fileMetadata(user, account, file);
        if (filter && filter.type === metadata.metadata.type) {
          const contents = await GoogleCloudStorageHelper.fileContents(user, file);
          docs.push(GoogleConverter.convertCloudStorageFileToDoc(user, file, metadata, contents));
        }
      }
    }

    return docs;
  }

  async newFile(user: ForaUser, account: Uid, doc: Document): Promise<Document> {
    if (!doc.id) {
      doc.id = doc.props && doc.props.id ? doc.props.id : functions.hash(JSON.stringify(doc));
      if (!doc.props) doc.props = { id: doc.id };
      else doc.props.id = doc.id;
    }

    const file = await GoogleCloudStorageHelper.fileCreate(user, account, this.storage, doc.id, doc.props, doc.body);
    const metadata = await GoogleCloudStorageHelper.fileMetadata(user, account, file);

    doc.file_id = file.name;
    doc.created = new Date(metadata.timeCreated);

    if (logging.isDebug(user ? user.profile : null)) logging.debugFP(LOG_NAME, 'newFile', user ? user.profile : null, `Returned doc = ${util.format(doc)}`);
    return doc;
  }

  async streamFile(user: ForaUser, account: Uid, doc: Document): Promise<Document> {
    if (!doc.id) {
      doc.id = doc.props && doc.props.id ? doc.props.id : functions.hash(JSON.stringify(doc));
      if (!doc.props) doc.props = { id: doc.id };
      else doc.props.id = doc.id;
    }

    const {file, url} = await GoogleCloudStorageHelper.fileStream(user, account, this.storage, doc.id, doc.props);
    // const metadata = await GoogleCloudStorageHelper.fileMetadata(user, account, file);

    doc.file_id = file.name;
    doc.created = new Date(); // metadata.timeCreated);
    doc.url = url;

    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'newFile', user.profile, `Returned doc = ${util.format(doc)}`);
    return doc;
 
  }

  async saveFile(user: ForaUser, account: Uid, doc: Document): Promise<void> {
    await GoogleCloudStorageHelper.fileUpdate(user, account, this.storage, doc.file_id, doc.props, doc.body);
  }
}
