/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Datastore } from '@google-cloud/datastore';
// import { DatastoreKey, OneOrMany } from '@google-cloud/datastore/entity';
// import { QueryCallback } from '@google-cloud/datastore/query';
// import { CommitCallback, CommitResult, GetCallback } from '@google-cloud/datastore/request';
import fs from 'fs';
import _ from 'lodash';
import path from 'path';
import util from 'util';

import config from '../../config';

import { FORA_PROFILE } from '../../types/user';

import * as globalTypes from '../../types/globals';
import { Group } from '../../types/group';
import * as itemTypes from '../../types/items';
import { Analysis, Ask, Contract, Event, GlobalType, Goal, Location, Message, Note, Person, Project, Task } from '../../types/items';
import { EntityType, Uid } from '../../types/shared';
import { User } from '../../types/user';

import { startCase } from '../../utils/format';
import { compress, uncompress } from '../../utils/funcs';
import logging from '../../utils/logging';

const DEBUG = (require('debug') as any)('fora:data:storage.OfflineDatastore');
const LOG_NAME = 'data:storage.OfflineDatastore';

let CACHE_PATH = 'cache'; // ); // '../../../cache/';
const CONFIG_FILES = [
  GlobalType.Contract, 
  GlobalType.Project, 
  GlobalType.Session, 
  GlobalType.User, 
  GlobalType.Vanity,
  GlobalType.Message,
  GlobalType.Group,
  GlobalType.Skill,
  GlobalType.SkillCategory,
  GlobalType.Recommendation,
  GlobalType.Transfer,
  GlobalType.Referral,
  GlobalType.Template,
  GlobalType.Introduction,
  GlobalType.Content,
  GlobalType.Jab,
  GlobalType.OnboardingStep,
  GlobalType.Search,
  GlobalType.Course,
  GlobalType.Plan,
];


interface DatastoreKey {
  namespace?: string;
  id?: string;
  name?: string;
  kind: string;
  parent?: DatastoreKey;
  path: Array<string | number>;
  includes?: any;
  length?: number;
}

interface QueryCallback {
  (err: Error | null, entities?: any[], info?: any): void;
}

interface CommitCallback {
  (err?: Error | null, resp?: any): void;
}

interface GetCallback {
  (err?: Error | null, entity?: any): void;
}

const GLOBAL_TYPES = {
  contract: globalTypes.Contract,
  group: Group,
  message: globalTypes.Message,
  project: globalTypes.Project,
  session: globalTypes.Session,
  user: globalTypes.User,
  skill: globalTypes.Skill,
  skillcategory: globalTypes.SkillCategory,
  vanity: globalTypes.Vanity,
  recommendation: globalTypes.Recommendation,
  transfer: globalTypes.Transfer,
  referral: globalTypes.Referral,
  template: globalTypes.Template,
  introduction: globalTypes.Introduction,
  jab: globalTypes.Jab,
  content: globalTypes.Content,
  onboardingstep: globalTypes.OnboardingStep,
  search: globalTypes.Search, 
  plan: globalTypes.Plan,
};

const ITEM_TYPES = {
  Person: Person,
  Note: Note,
  Task: Task,
  User: User,
  Event: Event,
  Message: Message,
  Location: Location,
  Contract: Contract,
  Project: Project,
  Analysis: Analysis,
  Ask: Ask,
  Goal: Goal,
};

config.onLoad('offline_datastore', async () => {
  CACHE_PATH = config.get('CACHE_PATH', 'cache');
},            true, 10);

export default class OfflineDatastore extends Datastore {
  // readonly KEY: typeof Datastore.KEY;
  private known_ids: string[] = [];
  private readonly configstore = {}; // {type:{id:obj}}
  private readonly memstore = {}; // {profile:{type:{id:obj}}}
  private readonly cache_path = null;

  private flush_store = {}; // {id:[type]}
  private flush_config = []; // [type]

  constructor() {
    super();
    this.cache_path = path.resolve(__dirname, '../../../', CACHE_PATH);
    logging.infoF(LOG_NAME, 'constructor', `cache path: ${this.cache_path}`);

    this.reload();   
    
    process.on('exit', this.quit.bind(this));
    process.on('SIGINT', this.quit.bind(this));
    process.on('SIGUSR1', this.quit.bind(this));
    process.on('SIGUSR2', this.quit.bind(this));
    process.on('uncaughtException', this.quit.bind(this));
  }

  checkFlush() {
    // return;
    for (const id in this.flush_store) {
      for (const mt of this.flush_store[id]) this._flushType(id, mt);
    }
    for (const config of this.flush_config) this._flushConfig(config);

    this.flush_store = {};
    this.flush_config = [];
  }

  delete(keyOrKeys: DatastoreKey | ReadonlyArray<DatastoreKey>): Promise<any> { //, _callback?: CommitCallback): Promise<any> {
    let del_keys: any = keyOrKeys;
    if (!(del_keys instanceof Array)) del_keys = [del_keys];

    // const flush_store = {}; // {id:[type]}
    // const flush_config = []; // [type]

    for (const key of del_keys) {
      if (key.namespace) {
        const ns = this.memstore[key.namespace];
        if (ns && ns[key.kind]) {
          if (logging.isDebug(key.namespace)) logging.debugFP(LOG_NAME, 'delete', key.namespace, `deleting ${key.kind} ${key.name}`);
          delete ns[key.kind][key.name];
          if (!this.flush_store[key.namespace]) this.flush_store[key.namespace] = [key.kind];
          else if (!this.flush_store[key.namespace].includes(key.kind)) this.flush_store[key.namespace].push(key.kind);
        }
      } else if (this.configstore[key.kind]) {
        delete this.configstore[key.kind][key.name];
        if (!this.flush_config.includes(key.kind)) this.flush_config.push(key.kind);
      }
    }

    this.checkFlush();

    return new Promise<void>(c => { c(); });
  }

  get(keys: any, optionsOrCallback?: any): any { //, callback?: GetCallback<any>): any {
    let result = this._get(keys, optionsOrCallback); //, callback);
    if (!result || !result.length) {
      if (keys.namespace) this._loadStore(keys.namespace, keys.kind);
      else this._loadConfig(keys.kind);
      result = this._get(keys, optionsOrCallback); //, callback);
    }
    return result;
  }

  public quit(): void {
    this._flushConfig();
    this._flush();
    process.exit();
  }

  runQuery(query: any, optionsOrCallback?: any, callback?: QueryCallback): any {
    const result = this._runQuery(query, optionsOrCallback, callback);
    /* if (!result || !result.length) {
      if (query.namespace) this._loadStore(query.namespace, query.kinds[0]);
      this._loadConfig(query.kinds[0]);
      result = this._runQuery(query, optionsOrCallback, callback);
    }*/
    return result;
  }

  save(entities: any | any[]): Promise<any> {
    let save_ents: any = entities;
    if (!(save_ents instanceof Array)) save_ents = [save_ents];

    for (const entity of save_ents) {
      const key = entity.key;
      if (key.namespace) {
        if (key.namespace.startsWith(FORA_PROFILE)) throw new globalTypes.InternalError(500, 'Cannot persist anonymous user');
        if (key.namespace.startsWith('g-')) throw new globalTypes.InternalError(500, 'Cannot persist guest user');
        let ns = this.memstore[key.namespace];
        if (!ns) {
          ns = {};
          this.memstore[key.namespace] = ns;
        }

        if (!ns[key.kind]) ns[key.kind] = {};

        if (logging.isDebug(key.namespace)) logging.debugFP(LOG_NAME, 'save', key.namespace, `Saving ${key.kind} of type ${EntityType[key.kind]}`);
        ns[key.kind][key.name] = this.deserialize(EntityType[key.kind] as EntityType, entity);
        if (!this.flush_store[key.namespace]) this.flush_store[key.namespace] = [key.kind];
        else if (!this.flush_store[key.namespace].includes(key.kind)) this.flush_store[key.namespace].push(key.kind);
      } else {
        if (!this.configstore[key.kind]) this.configstore[key.kind] = {};

        const gkind = startCase(key.kind);
        if (!GlobalType[gkind]) {
          logging.warnF(LOG_NAME, 'save', `Unknown global type ${key.kind}`);
          if (logging.isDebug()) logging.debugF(LOG_NAME, 'save', util.format(entity));
        }
        if (logging.isDebug()) logging.debugF(LOG_NAME, 'save', `Saving config ${key.kind} of type ${GlobalType[gkind]}`);
        this.configstore[key.kind][key.name] = this.deserialize(key.kind as GlobalType, entity);
        if (!this.flush_config.includes(key.kind)) this.flush_config.push(key.kind, key.name);
      }
    }

    this.checkFlush();

    return new Promise<void>(c => { c(); });
  }

  namespaces(): Uid[] {
    if (!this.memstore || Object.keys(this.memstore).length === 0) this._loadStore();
    return Object.keys(this.memstore);
  }

  reload() {
    if (fs.existsSync(this.cache_path)) {
      this._loadConfig();
      const users = this.configstore['user'];
      this.known_ids = users ? Object.keys(users) : [];
      this._loadStore();
    } else fs.mkdirSync(this.cache_path, 0o744);
  }

  private _flush(id = null) {
    if (id) for (const mt of Object.keys(this.memstore[id])) this._flushType(id, mt);
    else for (const i of Object.keys(this.memstore)) this._flush(i);
  }

  private _flushConfig(config = null, fid = null) {
    if (config) {
      if (config.startsWith('g-')) throw new globalTypes.InternalError(500, `Invalid config ${config}`);
      if (this.configstore[config]) {
        logging.infoF(LOG_NAME, '_flushConfig', `Saving offline global data for ${config}`);
        const config_path = path.resolve(this.cache_path, config);
        if (!fs.existsSync(config_path)) fs.mkdirSync(config_path);
        if (fid) {
          const id_path = path.resolve(config_path, fid);
          try {
            const data = JSON.stringify(this.configstore[config][fid]);
            fs.writeFileSync(id_path, data);
          } catch (err) {
            logging.errorF(LOG_NAME, '_flushConfig', `Error saving offline global ${config}.${fid} to ${id_path}`, err);
          }
        } else {
          for (const id in this.configstore[config]) {
            const id_path = path.resolve(config_path, id);
            try {
              const data = JSON.stringify(this.configstore[config][id]);
              fs.writeFileSync(id_path, data);
            } catch (err) {
              logging.errorF(LOG_NAME, '_flushConfig', `Error saving offline global ${config}.${id} to ${id_path}`, err);
            }
          }
        }
      }
    } else for (const c of Object.keys(this.configstore)) this._flushConfig(c);
  }

  private _flushType(id, mt) {
    if (id.startsWith('g-')) throw new globalTypes.InternalError(500, `Invalid id ${id}`);
    if (this.memstore[id] && this.memstore[id][mt]) {
      const id_path = path.resolve(this.cache_path, id);
      if (!fs.existsSync(id_path)) fs.mkdirSync(id_path);
      const store_filename = path.resolve(id_path, `${mt}.ds`);

      const store_file = compress(this.memstore[id][mt]);
      if (logging.isDebug()) logging.debugF(LOG_NAME, '_flushType', `Flushing ${store_file.length} byte ${mt} storage of ${id} to ${store_filename}`);
      fs.writeFileSync(store_filename, store_file);
    }
  }

  private _get(keys: any, _optionsOrCallback?: any): any { //, _callback?: GetCallback<any>): any {
    if (keys instanceof Array) { // throw new Error('offlineDS: get array not supported');
      let vals = [];
      for(const key of keys) {
        const val = this._get(key);
        if (val) vals.push(val);
      }

      return vals;
    }

    const key = keys;
    if (key.namespace) {
      const id = key.namespace;
      if (this.memstore[id] && this.memstore[id][key.kind]) {
        const value = this.memstore[id][key.kind][key.name];
        return value ? [value] : null;
      }
    } else {
      if (this.configstore[key.kind]) return [this.configstore[key.kind][key.name]];
    }
    return [];
  }

  private _loadConfig(config = null) {
    for (const config of this.flush_config) this._flushConfig(config);
    this.flush_config = [];

    if (config) {
      const config_path = path.resolve(this.cache_path, config);
      if (fs.existsSync(config_path)) {
        logging.infoF(LOG_NAME, '_loadConfig', `Loading offline global data for ${config}`);
        const obj_type = startCase(config) === 'Group' ? Group : globalTypes[startCase(config)];
        const stat = fs.statSync(config_path);
        if (stat.isDirectory()) {
          this.configstore[config] = {};
          const ids = fs.readdirSync(config_path);
          for (const id of ids) {
            try {
              const data = fs.readFileSync(path.resolve(config_path, id), 'utf-8');
              this.configstore[config][id] = new obj_type(JSON.parse(data));
            } catch (err) {
              logging.errorF(LOG_NAME, '_loadConfig', `Error loading ${id} from ${config_path}`, err);
              throw err;
            }
          }
        } else throw new Error(`Cannot load offline global data for ${config}: not a directory`);
      }
    } else for (const config of CONFIG_FILES) this._loadConfig(config);
  }

  private _loadStore(id = null, kind = null) {
    for (const id in this.flush_store) {
      for (const mt of this.flush_store[id]) this._flushType(id, mt);
    }
    this.flush_store = {};

    if (id) {
      if (id.startsWith('g-')) throw new globalTypes.InternalError(500, `Invalid id ${id}`);
      const id_path = path.resolve(this.cache_path, id);
      if (!fs.existsSync(id_path)) fs.mkdirSync(id_path);
      const stat = fs.statSync(id_path);
      if (stat.isDirectory()) {
        if (!this.memstore[id]) this.memstore[id] = {};
        // load type.ds files
        const mts = fs.readdirSync(id_path).filter(f => f.slice(-3) === '.ds');
        for (const store_filename of mts) {
          if (!kind || store_filename.startsWith(kind)) {
            const mt = store_filename.slice(0, -3);
            const store_file = fs.readFileSync(path.resolve(id_path, store_filename));
            if (logging.isDebug()) logging.debugF(LOG_NAME, '_loadStore', `Reading ${store_file.length} byte ${mt} storage of ${id} from ${store_filename}`);
            this.memstore[id][mt] = uncompress(store_file);
          }
        }
      }
    } else for (const id of this.known_ids) this._loadStore(id);
  }

  private _runQuery(query: any, _optionsOrCallback?: any, _callback?: QueryCallback): any {
    let objs = {};
    const schema = {};

    if (query.namespace) {
      if (this.memstore[query.namespace]) objs = this.memstore[query.namespace][query.kinds[0]];
      const t = ITEM_TYPES[query.kinds[0]];
      if (t) {
        const fields = new t({ type: null }).schema.fields;
        for (const field of fields) schema[field.name] = field;
      }
    } else {
      // Globals
      objs = this.configstore[query.kinds[0]];
      const t = GLOBAL_TYPES[query.kinds[0]];
      if (t) {
        const fields = new t({ type: null }).schema.fields;
        for (const field of fields) schema[field.name] = field;
      } else logging.errorF(LOG_NAME, '_runQuery', `Cannot find GLOBAL_TYPE ${query.kinds[0]}`, null);
    }

    const results = [];
    if (objs) {
      let entities = Object.values(objs);
      let filters = query.filters.slice();
      if (query.entityFilters) filters = filters.concat(query.entityFilters);
      for (const filter of filters) {
        if (logging.isDebug()) DEBUG('_runQuery: filter = %O', filter);
        if (!schema[filter.name]) throw new Error(`offlineDS cannot filter ${query.kinds[0]}: no schema ${filter.name}`);
        const ftype = Array.isArray(schema[filter.name].type) ? schema[filter.name].type.find(t => t !== "null") : schema[filter.name].type;
        const is_array = schema[filter.name].isArray;

        entities.map(e => (e[this.KEY] = { namespace: query.namespace, name: e['id'], kind: query.kinds[0], path: [query.kinds[0], e['id']] }));

        entities = entities.filter(e => e[filter.name] !== undefined && e[filter.name] !== null).filter(e => {
          if (logging.isDebug()) DEBUG('_runQuery: entity = %O', e);
          switch (filter.op) {
            case '=':
              switch (ftype) {
                case 'string':
                  return e[filter.name] === filter.val;
                case 'float':
                  return parseFloat(e[filter.name]) === parseFloat(filter.val);
                case 'long':
                  if (!schema[filter.name].logicalType || !['date', 'timestamp-millis'].includes(schema[filter.name].logicalType)) return parseInt(e[filter.name], 10) === parseInt(filter.val, 10);
                case 'date':
                  return new Date(e[filter.name]) === new Date(filter.val);
                case 'boolean':
                  return e[filter.name] === filter.val;
                case 'bytes':
                  if (!is_array) throw new Error(`offlineDS cannot filter ${query.kinds[0]}: attribute ${filter.name} is bytes but not array`);
                  if (filter.val instanceof Array) {
                    for (const val of filter.val) if (e[filter.name].includes(val)) return true;
                  } else return e[filter.name].includes(filter.val);
              }
              break;
            case '<':
              switch (ftype) {
                case 'string':
                  return e[filter.name] < filter.val;
                case 'float':
                  return parseFloat(e[filter.name]) < parseFloat(filter.val);
                case 'long':
                  if (!schema[filter.name].logicalType || !['date', 'timestamp-millis'].includes(schema[filter.name].logicalType)) return parseInt(e[filter.name], 10) < parseInt(filter.val, 10);
                case 'date':
                  return new Date(e[filter.name]) < new Date(filter.val);
                case 'bytes':
                  if (!is_array) throw new Error(`offlineDS cannot filter ${query.kinds[0]}: attribute ${filter.name} is bytes but not array`);
                  if (filter.val instanceof Array) {
                    for (const val of filter.val) {
                      for (const ev of e[filter.name]) if (ev < val) return true;
                    }
                  } else {
                    for (const ev of e[filter.name]) if (ev < filter.val) return true;
                  }
              }
              break;
            case '<=':
              switch (ftype) {
                case 'string':
                  return e[filter.name] <= filter.val;
                case 'float':
                  return parseFloat(e[filter.name]) <= parseFloat(filter.val);
                case 'long':
                  if (!schema[filter.name].logicalType || !['date', 'timestamp-millis'].includes(schema[filter.name].logicalType)) return parseInt(e[filter.name], 10) <= parseInt(filter.val, 10);
                case 'date':
                  return new Date(e[filter.name]) <= new Date(filter.val);
                case 'bytes':
                  if (!is_array) throw new Error(`offlineDS cannot filter ${query.kinds[0]}: attribute ${filter.name} is bytes but not array`);
                  if (filter.val instanceof Array) {
                    for (const val of filter.val) {
                      for (const ev of e[filter.name]) if (ev <= val) return true;
                    }
                  } else {
                    for (const ev of e[filter.name]) if (ev <= filter.val) return true;
                  }
              }
              break;
            case '>':
              switch (ftype) {
                case 'string':
                  return e[filter.name] > filter.val;
                case 'float':
                  return parseFloat(e[filter.name]) > parseFloat(filter.val);
                case 'long':
                  if (!schema[filter.name].logicalType || !['date', 'timestamp-millis'].includes(schema[filter.name].logicalType)) return parseInt(e[filter.name], 10) > parseInt(filter.val, 10);
                case 'date':
                  return new Date(e[filter.name]) > new Date(filter.val);
                case 'bytes':
                  if (!is_array) throw new Error(`offlineDS cannot filter ${query.kinds[0]}: attribute ${filter.name} is bytes but not array`);
                  if (filter.val instanceof Array) {
                    for (const val of filter.val) {
                      for (const ev of e[filter.name]) if (ev > val) return true;
                    }
                  } else {
                    for (const ev of e[filter.name]) if (ev > filter.val) return true;
                  }
              }
              break;
            case '>=':
              switch (ftype) {
                case 'string':
                  return e[filter.name] >= filter.val;
                case 'float':
                  return parseFloat(e[filter.name]) >= parseFloat(filter.val);
                case 'long':
                  if (!schema[filter.name].logicalType || !['date', 'timestamp-millis'].includes(schema[filter.name].logicalType)) return parseInt(e[filter.name], 10) >= parseInt(filter.val, 10);
                case 'date':
                  return new Date(e[filter.name]) >= new Date(filter.val);
                case 'bytes':
                  if (!is_array) throw new Error(`offlineDS cannot filter ${query.kinds[0]}: attribute ${filter.name} is bytes but not array`);
                  if (filter.val instanceof Array) {
                    for (const val of filter.val) {
                      for (const ev of e[filter.name]) if (ev >= val) return true;
                    }
                  } else {
                    for (const ev of e[filter.name]) if (ev >= filter.val) return true;
                  }
              }
              break;
            
            case 'IN':
              if (filter.val && Array.isArray(filter.val)) return filter.val.includes(e[filter.name])
              break;

            default:
              throw new Error(`offlineDS filter op ${filter.op} not supported`);
          }

          return false;
        });
      }

      results.push(entities);
    }

    return results; // array of at most size 1, first object is array of results
  }

  private deserialize(type: EntityType | itemTypes.GlobalType, data) {
    const obj_type = type.includes('.') ? type.slice(type.lastIndexOf('.') + 1) : startCase(type);
    const obj = Object.values(EntityType).includes(type as EntityType) ? 
      obj_type === 'User' ? new User({ id: data.key.name }) : 
      new itemTypes[obj_type]({ id: data.key.name }) : 
      obj_type === 'Group' ? new Group({ id: data.key.name }) : 
      new globalTypes[obj_type]({ id: data.key.name });
    for (const index in data.data) {
      if (data.data[index] !== null && data.data[index] !== undefined) {
        if (data.data[index].name) obj[data.data[index].name] = data.data[index].value;
        else obj[index] = data.data[index];
      }
    }

    if (obj.schema) {
      for (const field of obj.schema.fields) {
        if (!obj[field.name] && field.isArray) obj[field.name] = [];
      }
    }

    const type_name = type.split('.').pop();
    const type_name_sc = _.startCase(_.toLower(type_name));
    if (type_name[0].toUpperCase() === type_name[0]) {
      return type_name_sc === 'User' ? new User(obj) : new itemTypes[type_name_sc](obj);
    } else {
      return type_name_sc === 'Group' ? new Group(obj) : new globalTypes[type_name_sc](obj);
    }
  }

  //////////////////////////////////
  // These functions are used by AskFora GoogleDataStorePlugin
  //////////////////////////////////

  //////////////////////////////////
  // Nothing else is implemented
  //////////////////////////////////
  /*
   allocateIds(incompleteKey: DatastoreKey, n: number): Promise<AllocateIdsResult> {
   throw new Error('Not Implemented');
   }

   createQuery(namespaceOrKind?: string, kind?: string): DatastoreQuery {
   throw new Error('Not Implemented');
   }

   createReadStream(keys: DatastoreKey | ReadonlyArray<DatastoreKey>, options: QueryOptions): NodeJS.ReadableStream {
   throw new Error('Not Implemented');
   }

   determineBaseUrl_(customApiEndpoint?: string): void {
   throw new Error('Not Implemented');
   }

   double(value: string | number): DatastoreDouble {
   throw new Error('Not Implemented');
   }

   geoPoint(coordinates: DatastoreCoords): DatastoreGeopoint {
   throw new Error('Not Implemented');
   }

   get(key: DatastoreKey, options?: QueryOptions): Promise<[object | undefined]> {
   get(key: DatastoreKey, options: QueryOptions, callback: GetCallback<object>): void;
   get(keys: ReadonlyArray<DatastoreKey>, options: QueryOptions, callback: GetCallback<object[]>): void;
   get(key: DatastoreKey, callback: GetCallback<object>): void;
   get(keys: ReadonlyArray<DatastoreKey>, callback: GetCallback<object[]>): void;
   get(keys: ReadonlyArray<DatastoreKey>, options?: QueryOptions): Promise<[object[]]> {
   throw new Error('Not Implemented');
   }


   insert(entities: OneOrMany<object>): Promise<CommitResult> {
   throw new Error('Not Implemented');
   }

   int(value: string | number): DatastoreInt {
   throw new Error('Not Implemented');
   }

   isDouble(value: any): value is DatastoreDouble {
   throw new Error('Not Implemented');
   }

   isGeoPoint(value: any): value is DatastoreGeopoint {
   throw new Error('Not Implemented');
   }

   isInt(value: any): value is DatastoreInt {
   throw new Error('Not Implemented');
   }

   isKey(value: any): value is DatastoreKey {
   throw new Error('Not Implemented');
   }

   key(pathOrOptions: DatastoreKeyPath | DatastoreKeyOptions): DatastoreKey {
   throw new Error('Not Implemented');
   }

   runQuery(query: Query, options?: QueryOptions): Promise<QueryResult>;
   runQuery(query: Query, options: QueryOptions, callback: QueryCallback): void;
   runQuery(query: Query, callback: QueryCallback): void;
   runQueryStream(query: Query, options?: QueryOptions): NodeJS.ReadableStream {
   throw new Error('Not Implemented');
   }


   update(entities: OneOrMany<object>): Promise<CommitResult> {
   throw new Error('Not Implemented');
   }

   upsert(entities: OneOrMany<object>): Promise<CommitResult> {
   throw new Error('Not Implemented');
   }
   */
}
