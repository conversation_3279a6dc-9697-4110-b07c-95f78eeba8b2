import 'isomorphic-fetch';
import util from 'util';

import ForaUser from '../../session/user';

import { InternalError } from '../../types/globals';
import { Document } from '../../types/items';
import { AuthL<PERSON>l, Uid } from '../../types/shared';

import * as functions from '../../utils/funcs';
import logging from '../../utils/logging';

import { AbstractMicrosoftGraphHelper } from '../../sources/helpers/microsoft/a_microsoft_graph_helper';
import { MicrosoftGraphConverter } from '../../sources/helpers/microsoft/microsoft_graph_conversions';
import MicrosoftGraphOneDriveHelper from '../../sources/helpers/microsoft/microsoft_graph_one_drive_helper';

import { FileFilter, IFileStore } from './i_file_store';

const LOG_NAME = 'data.storage.MicrosoftOneDriveFileStore';

/**
 * Microsoft Drive Document Store Plugin
 * -------------------------------------
 * Storage = Microsoft OneDrive
 *
 * Links:
 *  - https://docs.microsoft.com/en-us/onedrive/developer/rest-api/concepts/special-folders-appfolder?view=odsp-graph-online
 *  - https://docs.microsoft.com/en-us/onedrive/developer/rest-api/resources/driveitem?view=odsp-graph-online
 */
export default class MicrosoftOneDriveFileStore implements IFileStore {
  async clearFiles(user: ForaUser, account: Uid, filter: FileFilter, destroy_containers?: boolean): Promise<number> {
    const client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);

    let count = 0;
    const files = await MicrosoftGraphOneDriveHelper.filesList(user, account, client);
    for (const file of files) {
      const doc = MicrosoftGraphConverter.documentFromDriveItem(file);
      if (filter ? doc.props && doc.props.type === filter.type : true) {
        await MicrosoftGraphOneDriveHelper.fileDelete(user, file.id, account, client);
        count += 1;
      }
    }

    // destroy_containers is a NO-OP because we did not create any containers in this implementation
    return count;
  }

  async deleteFile(user: ForaUser, account: Uid, f_doc: Partial<Document>): Promise<void> {
    await MicrosoftGraphOneDriveHelper.fileDelete(user, account, f_doc.file_id);
  }

  async getFile(user: ForaUser, account: Uid, file_id: string): Promise<Document> {
    const client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
    const file = await MicrosoftGraphOneDriveHelper.fileMetadata(user, account, file_id, client);
    const contents = await MicrosoftGraphOneDriveHelper.fileContents(user, account, file_id, client);
    return MicrosoftGraphConverter.documentFromDriveItem(file, contents);
  }

  async loadFiles(user: ForaUser, account: Uid, filter: FileFilter): Promise<Document[]> {
    if (!user.isAuthenticated(AuthLevel.Organizer)) return [];
    logging.infoFP(LOG_NAME, 'loadFiles', user.profile, `Filter = ${util.format(filter)}`);

    let docs: Document[] = [];

    const client = await AbstractMicrosoftGraphHelper.getGraphClient(user, account);
    const files = await MicrosoftGraphOneDriveHelper.filesList(user, account, client);

    if (files.length) {
      docs = await Promise.all<Document>(
        files
          .map(file => MicrosoftGraphConverter.documentFromDriveItem(file))
          .filter(doc => (filter ? doc.props && doc.props.type === filter.type : true))
          .map(async doc => {
            doc.body = await MicrosoftGraphOneDriveHelper.fileContents(user, account, doc.file_id, client);
            return doc;
          }),
      );
    } else logging.warnFP(LOG_NAME, 'loadFiles', user.profile, 'No files in our app directory within OneDrive');

    return docs;
  }

  async newFile(user: ForaUser, account: Uid, f_doc: Document): Promise<Document> {
    if (!f_doc.id) {
      f_doc.id = f_doc.props && f_doc.props.id ? f_doc.props.id : functions.hash(JSON.stringify(f_doc));
      if (!f_doc.props) f_doc.props = { id: f_doc.id };
      else f_doc.props.id = f_doc.id;
    }

    const file = await MicrosoftGraphOneDriveHelper.fileCreate(user, account, f_doc.id, JSON.stringify(f_doc.props), f_doc.body);

    f_doc.file_id = file.id;
    f_doc.created = new Date(file.createdDateTime);

    return f_doc;
  }

  streamFile(user: ForaUser, account: Uid, doc: Document): Promise<Document> {
    throw new InternalError(500, 'Stream file to Micrsoft One Drive is not supported');
  }

  async saveFile(user: ForaUser, account, f_doc: Document): Promise<void> {
    await MicrosoftGraphOneDriveHelper.fileUpdate(user, account, f_doc.file_id, JSON.stringify(f_doc.props), f_doc.body);
  }
}
