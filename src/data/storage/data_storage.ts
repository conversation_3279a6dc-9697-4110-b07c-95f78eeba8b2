/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Datastore, PropertyFilter, and, or } from '@google-cloud/datastore';
import { setTimeout } from "timers/promises";
// import { DatastoreKey } from '@google-cloud/datastore/entity';
// import { DatastoreTransaction } from '@google-cloud/datastore/transaction';
import _ from 'lodash';
import util from 'util';
import { v4 as uuid } from 'uuid';

import * as globalTypes from '../../types/globals';
import { Group } from '../../types/group';
import * as ItemTypes from '../../types/items';
import { Analysis, Ask, Contract, Document, Event, GlobalType, Goal, IEntity, ItemCache, Message, Note, Person, Project, Task, Tutorial } from '../../types/items';
import { ANONYMOUS_ID, AuthLevel, AuthProviders, EntityType, ForaUserSettings, NotificationType, ProfileStatus, TagType, Uid } from '../../types/shared';
import { User } from '../../types/user';

import { DAYS } from '../../utils/datetime';
import { flatten, hash, uncompress } from '../../utils/funcs';
import logging from '../../utils/logging';

import config from '../../config';
import ForaUser from '../../session/user';
import { retryError } from '../../sources/helpers/google/google_retry_helper';
import { FORA_PROFILE } from '../../types/user';
import { ICachePlugin } from '../caching/i_cache_plugin';
import { FileFilter, FileUsage, IFileStore } from './i_file_store';
import { INoteStore } from './i_note_store';
import OfflineDatastore from './offline_datastore';

const DEBUG = (require('debug') as any)('fora:data:storage:gcp');
const LOG_NAME = 'data.storage.DataStorage';

type DatastoreKey = any;

export interface DatastoreEntry {
  data: any;
  excludeFromIndexes: string[];
  key: DatastoreKey;
}


export default class DataStorage {
  constructor(
    private readonly cache: ICachePlugin,
    private readonly ds: Datastore,
    private readonly google_drive: IFileStore, // File store for Google authenticated users
    private readonly google_notes: INoteStore, // Note store for Google authenticated users
    private readonly microsoft_drive: IFileStore, // File store for Microsoft authenticated users
    private readonly microsoft_notes: INoteStore, // Note store for Microsoft authenticated users
    private readonly fallback_drive: IFileStore, // File store for when a user can't use the main file store due to Group restrictions
    private readonly fallback_notes: INoteStore, // Note store for when a user can't use the main note store due to Group restrictions
  ) {}

  public init() {
    logging.infoF(LOG_NAME, 'init', 'Initialized datastore');
  }

  public async add(profile: Uid, data: IEntity, no_index: string[] = [], force_profile?: boolean): Promise<void> {
    return this.save(profile, data.type, this.serialize(force_profile || Object.values(EntityType).includes(data.type as EntityType) ? profile : null, data, no_index));
  }

  /****************************/
  /********* Promo Codes *********/
  /****************************/

  public async promoCode(code: string): Promise<globalTypes.Promotion> {
    const promo = await (await this.findAtt(undefined, GlobalType.Promotion, 'code', [code])) as globalTypes.Promotion[];
    if(promo?.length) return promo[0];
  }

  public async promoCodes(): Promise<globalTypes.Promotion[]> {
    return this.find(undefined, GlobalType.Promotion) as Promise<globalTypes.Promotion[]>;
  }

  public async delPromoCode(promo: globalTypes.Promotion): Promise<void> {
    await this.remove(undefined, promo);
    
  }

  public async savePromoCode(promo: globalTypes.Promotion): Promise<globalTypes.Promotion> {
    await this.add(undefined, promo, promo.nonIndexedFields);
    return promo;
  }

  /****************************/
  /********* Analyses *********/
  /****************************/

  public async analysisIds(user: ForaUser): Promise<Uid[]> {
    return this.findIds(user.profile, EntityType.Analysis);
  }

  public async analysisGet(user: ForaUser, id: Uid): Promise<Analysis> {
    const results = (await this.find(user.profile, EntityType.Analysis, id)) as Analysis[];
    if (results && results.length) return results[0];
    return null;
  }

  public async analysisLoad(user: ForaUser): Promise<Analysis[]> {
    return this.find(user.profile, EntityType.Analysis) as Promise<Analysis[]>;
  }

  public async analysisSave(user: ForaUser, analysis: Analysis): Promise<Analysis> {
    await this.add(user.profile, analysis, analysis.nonIndexedFields);
    return analysis;
  }

  public async analysisDelete(user: ForaUser, analysis: Partial<Analysis>) {
    return this.remove(user.profile, analysis);
  }

  /*****************************/
  /********* Tutorials *********/
  /*****************************/

  public async tutorialIds(user: ForaUser): Promise<Uid[]> {
    return this.findIds(user.profile, EntityType.Tutorial);
  }

  public async tutorialGet(user: ForaUser, id: Uid): Promise<Tutorial> {
    const results = (await this.find(user.profile, EntityType.Tutorial, id)) as Tutorial[];
    if (results && results.length) return results[0];
    return null;
  }

  public async tutorialsLoad(user?: ForaUser): Promise<Tutorial[]> {
    return this.find(user.profile, EntityType.Tutorial) as Promise<Tutorial[]>;
  }

  public async tutorialSave(user: ForaUser, tutorial: Tutorial): Promise<Tutorial> {
    await this.add(user.profile, tutorial, tutorial.nonIndexedFields);
    return tutorial;
  }

  public async userCertificates(profile: Uid): Promise<Tutorial[]> {
    return this.findAtt('certificate', EntityType.Tutorial, 'user', [profile]) as Promise<Tutorial[]>;
  }
 
  /*****************************/
  /****** Asks / Searches ******/
  /****************************/

  public async askIds(user: ForaUser): Promise<Uid[]> {
    return this.findIds(user.profile, EntityType.Ask);
  }

  public async askGet(user: ForaUser, id: Uid): Promise<Ask> {
    const results = (await this.find(user.profile, EntityType.Ask, id)) as Ask[];
    if (results && results.length) return results[0];
    return null;
  }

  public async askLoad(user: ForaUser): Promise<Ask[]> {
    return this.find(user.profile, EntityType.Ask) as Promise<Ask[]>;
  }

  public async askSave(user: ForaUser, ask: Ask): Promise<Ask> {
    await this.add(user.profile, ask, ask.nonIndexedFields);
    return ask;
  }

  public async askDelete(user: ForaUser, ask: Partial<Ask>) {
    return this.remove(user.profile, ask);
  } 

  /****************************/
  /********** Goals ***********/
  /****************************/

  public async goalIds(user: ForaUser): Promise<Uid[]> {
    return this.findIds(user.profile, EntityType.Goal);
  }

  public async goalDelete(user: ForaUser, goal: Goal) {
    return this.remove(user.profile, goal);
  }

  public async goalDeleteAll(user: ForaUser, goals: Goal[]) {
    return this.removeAll(user.profile, goals);
  }

  public async goalGet(user: ForaUser, id: Uid): Promise<Goal> {
    const results = (await this.find(user.profile, EntityType.Goal, id)) as Goal[];
    if (results && results.length) return results[0];
    return null;
  }

  public async goalsLoad(user: ForaUser): Promise<Goal[]> {
    return this.find(user.profile, EntityType.Goal) as Promise<Goal[]>
  }

  public async goalSave(user: ForaUser, goal: Goal): Promise<Goal> {
    await this.add(user.profile, goal, goal.nonIndexedFields);
    return goal;
  }

  public async goalsSave(user: ForaUser, goals: Goal[]): Promise<Goal[]> {
    await this.saveAll(user ? user.profile : undefined, goals);
    return goals;
  }

  /****************************/
  /****** Global Courses *******/
  /*************************** */

  public async courseDelete(course: globalTypes.Course) {
    return this.remove(null, course);
  }

  public async courseBySkills(skills: string[]): Promise<globalTypes.Course[]> {
    return this.findAtt(null, GlobalType.Course, 'skills', skills) as Promise<globalTypes.Course[]>;
  }

  public async courseGet(id: Uid): Promise<globalTypes.Course> {
    const results = (await this.find(undefined, GlobalType.Course, id)) as globalTypes.Course[];
    if (results && results.length) return results[0];
    return null;
  }

  public async coursesLoad(): Promise<globalTypes.Course[]> {
    return this.find(null, GlobalType.Course) as Promise<globalTypes.Course[]>
  }

  public async courseSave(course: globalTypes.Course): Promise<globalTypes.Course> {
    await this.add(null, course, course.nonIndexedFields);
    return course;
  }

  public async coursesSave(courses: globalTypes.Course[]): Promise<globalTypes.Course[]> {
    await this.saveAll(null, courses);
    return courses;
  }

  public courseSourceById(file_id: Uid): Promise<Document> {
    return this.fallback_drive.getFile(null, 'courses', file_id);
  }

  public courseSourceSave(doc: Document): Promise<Document> {
    if (!doc.props) doc.props = { type: FileUsage.Course };
    else doc.props.type = FileUsage.Course;

    return this.fallback_drive.newFile(null, 'courses', doc);
  }

  /*************************** */
  /********* Contracts *********/
  /*************************** */

  public async contractById(id: Uid): Promise<Contract> {
    try {
      const data: globalTypes.Contract[] = (await this.find(null, GlobalType.Contract, id)) as globalTypes.Contract[];
      if (data && data.length) {
        let contract: Contract = new Contract(uncompress(data[0].contract) as Contract);

        if (new Date(data[0].created) < DAYS(new Date(), -30)) {
          await this.remove(null, { id, type: GlobalType.Contract } as IEntity)
            .catch(err => logging.errorF(LOG_NAME, 'contractById', `Error removing contract ${id}`, err));
          contract = null;
        }
        return contract;
      }
    } catch (err) {
      logging.errorF(LOG_NAME, 'contractById', `Error finding contract ${id}`, err);
    }

    return null;
  }

  public async contractIds(user: ForaUser): Promise<Uid[]> {
    return this.findIds(user.profile, EntityType.Contract);
  }

  public async contracts(user: ForaUser): Promise<Contract[]> {
    return this.find(user.profile, EntityType.Contract) as Promise<Contract[]>;
  }

  public async contractDelete(contract: Partial<Contract>): Promise<void> {
    if (contract.client_id) await this.remove(contract.client_id, contract);
    if (contract.contractor_id) await this.remove(contract.contractor_id, contract);
    const key = this.key(null, {type:GlobalType.Contract, id:contract.id});                                                                    
    await this.delete(null, GlobalType.Contract, [key]);
  }

  public async contractSave(user: ForaUser, contract: Contract): Promise<Contract> {
    if (!user.profile) throw new globalTypes.InternalError(500, `Cannot save contractor with no user pfofile`, contract);
    await this.add(user.profile, contract, contract.nonIndexedFields);
    return contract;
  }

  public async delete(profile: Uid, type: EntityType | GlobalType, del_items: DatastoreKey[]): Promise<void> {
    if (del_items) {
      if (config.isEnvOffline()) {
        await this.ds.delete(del_items).catch(err => {
          logging.errorF(LOG_NAME, 'delete', 'Error committing transaction while deleting from DS', err);
        });
      } else {
        while (del_items.length) {
          let transaction = this.ds.transaction();
          try {
            if (del_items.length > 5) {
              await transaction.delete(del_items.splice(0, 500));
              transaction.commit().catch(err => {
                logging.errorF(LOG_NAME, 'delete', 'Error committing transaction while deleting from DS', err);
              });
            } else {
              transaction = null;
              await this.ds.delete(del_items.splice(0, del_items.length));
              break;
            }
          } catch (err) {
            logging.errorF(LOG_NAME, 'delete', `Error deleting ${type} for ${profile} to datastore`, err);
            if (transaction) {
              transaction.rollback().catch(err => {
                logging.errorF(LOG_NAME, 'delete', 'Error rolling back transaction while deleting from DS', err);
              });
            }
          }
        }
      }
    }
  }

  /*************************** */
  /********** Events ***********/
  /*************************** */

  public async eventById(user: ForaUser, id: Uid): Promise<Event> {
    const results = await this.find(user.profile, EntityType.Event, id);
    if (results && results.length) return results[0] as Event;
    return null;
  }

  public async eventsByDate(user: ForaUser, start: Date = null,  end: Date = null): Promise<Event[]> {
    if (start && end && start.getTime() === end.getTime()) return this.findAtt(user.profile, EntityType.Event, 'start', [start]) as Promise<Event[]>;
    else if (end && end.getTime() && !start) return this.findRange(user.profile, EntityType.Event, 'end', end, false) as Promise<Event[]>;
    else if (start && start.getTime() && !end) return this.findRange(user.profile, EntityType.Event, 'start', start, true) as Promise<Event[]>;
    else if (!start && !end) return this.events(user);
    throw new Error('Cannot search events with both start and end times');
  }

  public async eventIds(user: ForaUser, account?: Uid): Promise<Uid[]> {
    if (account) return this.findIds(user.profile, EntityType.Event, 'account', [account]);
    return this.findIds(user.profile, EntityType.Event);
  }

  public async events(user: ForaUser): Promise<Event[]> {
    return this.find(user.profile, EntityType.Event) as Promise<Event[]>;
  }

  public async eventSave(user: ForaUser, event: Event): Promise<Event> {
    const missing_ids = event.people.filter(p => !p.id);
    if (missing_ids.length) throw new Error(`Error saving event ${event.id} with people missing ids`);
    await this.add(user.profile, event, event.nonIndexedFields);
    return event;
  }

  public async word(w: string): Promise<{name: string, vector: number[]}> {
    try {
      const key = this.ds.key({path:['word', w]});
      const wv = (await this.ds.get(key) ) as {name: string, vector: number[]}[];
      return wv && wv.length ? wv[0] : null;
    } catch(e) {
      logging.errorF(LOG_NAME, 'word', `Error fetching word vector ${w}`, e);
    }
    return null;
  }

  public async words(ws: string[]): Promise<{name: string, vector: number[]}[]> {
    try {
      const keys = _.uniq(ws).map(w => this.ds.key({path:['word', w]}));
      let wv = [];

      while(keys.length) {
        const swv = (await this.ds.get(keys.splice(0,10)) ) as {name: string, vector: number[]}[][];
        if (swv && swv.length) wv = wv.concat(swv);
      }

      return wv && wv.length ? wv[0] : null;
    } catch(e) {
      logging.errorF(LOG_NAME, 'word', `Error fetching words vector ${JSON.stringify(ws)}`, e);
    }
    return null;
  }
 
  getFileStore(provider: AuthProviders): IFileStore {
    switch (provider) {
      // @formatter:off
      case AuthProviders.Google:
        return this.google_drive;
      case AuthProviders.Msal:
      case AuthProviders.Microsoft:
        return this.microsoft_drive;
      case AuthProviders.Offline:
        return this.google_drive;
      default:
        throw new Error(`Unexpected provider type - ${provider}`);
      // @formatter:on
    }
  }

  getNoteStore(provider: AuthProviders): INoteStore {
    switch (provider) {
      // @formatter:off
      case AuthProviders.Google:
        return this.google_notes;
      case AuthProviders.Msal:
      case AuthProviders.Microsoft:
        return this.microsoft_notes;
      case AuthProviders.Offline:
        return this.google_notes;
      default:
        throw new Error(`Unexpected provider type - ${provider}`);
      // @formatter:on
    }
  }


  /*************************** */
  /******** Groups **********/
  /*************************** */

  public async groupByDomain(domain: string): Promise<Group> {
    try {
      if (domain && domain.length) {
        const data: Group[] = (await this.findAtt(null, GlobalType.Group, 'email_domain', [domain], false, 1)) as Group[];
        if (data && data.length) return data[0];
      }
    } catch (err) {
      logging.errorF(LOG_NAME, 'groupByDomain', `Error finding group ${domain}`, err);
    }

    return null;
  }

  public async groupByHost(host: string): Promise<Group> {
    try {
      if (host && host.length) {
        const data: Group[] = (await this.findAtt(null, GlobalType.Group, 'host', [host], false, 1)) as Group[];
        if (data && data.length) return data[0];
      }
    } catch (err) {
      logging.errorF(LOG_NAME, 'groupByHost', `Error finding group ${host}`, err);
    }

    return null;
  }

  public async groupByAccountId(account_type: string, account_id): Promise<Group> {
    const data: Group[] = (await this.findAtt(null, GlobalType.Group, 'auth_ids', [`${account_type}_${account_id}`])) as Group[];
    if (data && data.length) return data[0];
  }

  public async groupById(id: Uid): Promise<Group> {
    if (id) {
      try {
        const data: Group[] = (await this.find(null, GlobalType.Group, id)) as Group[];
        if (data && data.length) return data[0];
      } catch (err) {
        logging.errorF(LOG_NAME, 'groupById', `Error finding group ${id}`, err);
      }
    }

    return null;
  }

  public async groupsByIds(ids: Uid[]): Promise<Group[]> {
    return (await this.findAtt(null, GlobalType.Group, 'id', ids)) as Group[];
  }

  public async groupDelete(group: Group): Promise<void> {
    return this.remove(null, group);
  }

  async groups(): Promise<Group[]> {
    const gs = (await this.find(null, GlobalType.Group)) as Group[];
    if (gs) return gs.map(g => new Group(g));
  }

  async groupsWithCategory(): Promise<Group[]> {
    return this.findAtt(null, GlobalType.Group, 'categories', [true]) as Promise<Group[]>;
  }

  public async groupSave(group: Group): Promise<Group> {
    await this.add(null, group, group.nonIndexedFields);
    return group;
  }


  /*************************** */
  /******** Profiles **********/
  /*************************** */

  public profileCard(user: ForaUser): Promise<Document> {
    return this.fallback_drive.getFile(user, user.profile, 'profile_card');
  }

  public profileCardSave(user: ForaUser, card: Document): Promise<Document> {
    if (!card.props) card.props = { type: FileUsage.Card };
    else card.props.type = FileUsage.Card;
    card.id = 'profile_card';
    return this.fallback_drive.newFile(user, user.profile, card);
  }

  public profileCardClear(user: ForaUser, destroy_containers?: boolean): Promise<number> {
    const filter: FileFilter = { type: FileUsage.Card };
    return this.fallback_drive.clearFiles(user, user.profile, filter, destroy_containers);
  }

  /*************************** */
  /******** Imports **********/
  /*************************** */

  public importByFileId(user: ForaUser, file_id: string): Promise<Document> {
    if (_.get(user, 'settings.imports.useFallback', false)) return this.fallback_drive.getFile(user, user.profile, file_id);
    else if (_.get(user, 'settings.imports.disallowed', false)) throw new Error(`Imports are disabled for ${user.profile}`);
    // else return this.getFileStore(user.provider).getFile(user, file_id);
    else return this.fallback_drive.getFile(user, user.profile, file_id);
  }

  public async importDelete(user: ForaUser, doc: Document): Promise<void> {
    if (_.get(user, 'settings.imports.useFallback', false)) return this.fallback_drive.deleteFile(user, user.profile, doc);
    else if (_.get(user, 'settings.imports.disallowed', false)) throw new Error(`Imports are disabled for ${user.profile}`);
    // else return this.getFileStore(user.provider).deleteFile(user, doc);
    else return this.fallback_drive.deleteFile(user, user.profile, doc);
  }

  public imports(user: ForaUser): Promise<Document[]> {
    const filter: FileFilter = { type: FileUsage.Import };

    if (_.get(user, 'settings.imports.useFallback', false)) return this.fallback_drive.loadFiles(user, user.profile, filter);
    else if (_.get(user, 'settings.imports.disallowed', false)) throw new Error(`Imports are disabled for ${user.profile}`);
    // else return this.getFileStore(user.provider).loadFiles(user, filter);
    else return this.fallback_drive.loadFiles(user, user.profile, filter);
  }

  public importSave(user: ForaUser, doc: Document): Promise<Document> {
    if (!doc.props) doc.props = { type: FileUsage.Import };
    else doc.props.type = FileUsage.Import;

    if (_.get(user, 'settings.imports.useFallback', false)) return this.fallback_drive.newFile(user, user.profile, doc);
    else if (_.get(user, 'settings.imports.disallowed', false)) throw new Error(`Imports are disabled for ${user.profile}`);
    // else return this.getFileStore(user.provider).newFile(user, doc);
    else return this.fallback_drive.newFile(user, user.profile, doc);
  }

  public importUpload(user: ForaUser, doc: Document): Promise<Document> {
    if (!doc.props) doc.props = { type: FileUsage.Import };
    else doc.props.type = FileUsage.Import;

    if (_.get(user, 'settings.imports.useFallback', false)) return this.fallback_drive.streamFile(user, user.profile, doc);
    else if (_.get(user, 'settings.imports.disallowed', false)) throw new Error(`Imports are disabled for ${user.profile}`);
    // else return this.getFileStore(user.provider).newFile(user, doc);
    else return this.fallback_drive.streamFile(user, user.profile, doc);
  }

  public importsClear(user: ForaUser, destroy_containers?: boolean): Promise<number> {
    const filter: FileFilter = { type: FileUsage.Import };

    if (_.get(user, 'settings.imports.useFallback', false)) return this.fallback_drive.clearFiles(user, user.profile, filter, destroy_containers);
    else if (_.get(user, 'settings.imports.disallowed', false)) throw new Error(`Imports are disabled for ${user.profile}`);
    // else return this.getFileStore(user.provider).clearFiles(user, filter, destroy_containers);
    return this.fallback_drive.clearFiles(user, user.profile, filter, destroy_containers);
  }

  public importLog(import_log: globalTypes.ImportLog[]) {
    return this.saveAll(null, import_log);
  }

  public importLogs(import_id: Uid, learn_id?: Uid): Promise<globalTypes.ImportLog[]> {
    if (learn_id) return this.findAtt(null, GlobalType.ImportLog, 'learn', [learn_id], false) as Promise<globalTypes.ImportLog[]>;
    return this.findAtt(null, GlobalType.ImportLog, 'import', [import_id], false) as Promise<globalTypes.ImportLog[]>;
  } 

  public importLogsRemove(import_log: globalTypes.ImportLog[]) {
    this.removeAll(null, import_log);
  }

  public key(namespace: string, entity: Partial<IEntity>): DatastoreKey {
    if (!entity.id) {
      logging.errorFP(LOG_NAME, 'key', namespace, `Attempt to create a key for an entity without an ID - ${util.format(entity)}`, null);
      throw new Error(`Invalid entity ${entity.type} with no id`);
    }
    const id = entity.id;
    const typeName = entity.type.split('.').pop();

    if (namespace) return this.ds.key({ namespace, path: [typeName, id] });
    else return this.ds.key([typeName, id]);
  }

  public async logMessage(message: globalTypes.Message) {
    return this.save(null, message.type, {
      key: this.key(null, {type: message.type, id: uuid() }),
      excludeFromIndexes: ['data', 'entities', 'message'],
      data: message,
    });
  }

  /*************************** */
  /******** Messages **********/
  /*************************** */

  // noinspection JSUnusedGlobalSymbols
  public async messageById(user: ForaUser, id: Uid): Promise<Message> {
    const results = await this.find(user.profile, EntityType.Message, id);
    if (results && results.length) return results[0] as Message;
    return null;
  }

  public async messagesByDate(user: ForaUser, start: Date = null, end: Date = null): Promise<Message[]> {
    if (start && end && start.getTime() === end.getTime()) return this.findAtt(user.profile, EntityType.Message, 'received', [start]) as Promise<Message[]>;
    else if (start && start.getTime() && !end) return this.findRange(user.profile, EntityType.Message, 'received', start, true) as Promise<Message[]>;
    else if (end && end.getTime() && !start) return this.findRange(user.profile, EntityType.Message, 'received', end, false) as Promise<Message[]>;
    else if (!start && !end) return this.messages(user);
    throw new Error('Cannot search messages by start and end');
  }

  // noinspection JSUnusedGlobalSymbols
  public async messageDrafts(user: ForaUser): Promise<Message[]> {
    return this.findAtt(user.profile, EntityType.Message, 'draft', [true]) as Promise<Message[]>;
  }

  public async messageIds(user: ForaUser, account?: Uid): Promise<Uid[]> {
    if (account) return this.findIds(user.profile, EntityType.Message, 'account', [account]);
    return this.findIds(user.profile, EntityType.Message);
  }

  public async messages(user: ForaUser): Promise<Message[]> {
    return this.find(user.profile, EntityType.Message) as Promise<Message[]>;
  }

  public async messageSave(user: ForaUser, message: Message): Promise<Message> {
    await this.add(user.profile, message, message.nonIndexedFields);
    return message;
  }

  /*************************** */
  /******** Notes **********/
  /*************************** */

  public noteCreate(user: ForaUser, note: Note): Promise<Note> {
    logging.infoFP(LOG_NAME, 'noteCreate', user.profile, `Creating note ${note.id}`);

    if (_.get(user, 'settings.notes.useFallback', false)) return this.fallback_notes.newNote(user, user.profile, note);
    else if (_.get(user, 'settings.notes.disallowed', false)) throw new Error(`Notes are disabled for ${user.profile}`);
    else return this.getNoteStore(user.provider).newNote(user, user.profile, note);
  }

  public noteDelete(user: ForaUser, note: Note): Promise<void> {
    logging.infoFP(LOG_NAME, 'noteDelete', user.profile, `Deleting note ${note.id}`);

    if (_.get(user, 'settings.notes.useFallback', false)) return this.fallback_notes.deleteNote(user, user.profile, note);
    else if (_.get(user, 'settings.notes.disallowed', false)) throw new Error(`Notes are disabled for ${user.profile}`);
    else return this.getNoteStore(user.provider).deleteNote(user, user.profile, note);
  }

  public async noteSave(user: ForaUser, note: Note): Promise<void> {
    logging.infoFP(LOG_NAME, 'noteSave', user.profile, `Saving note ${note.id}`);

    if (_.get(user, 'settings.notes.useFallback', false)) return this.fallback_notes.saveNote(user, user.profile, note);
    else if (_.get(user, 'settings.notes.disallowed', false)) throw new Error(`Notes are disabled for ${user.profile}`);
    else return this.getNoteStore(user.provider).saveNote(user, user.profile, note);
  }

  public notesClear(user: ForaUser, destroy_containers?: boolean): Promise<number> {
    logging.infoFP(LOG_NAME, 'notesClear', user.profile, 'Clearing notes');

    if (_.get(user, 'settings.notes.useFallback', false)) return this.fallback_notes.clearNotes(user, user.profile, destroy_containers);
    else if (_.get(user, 'settings.notes.disallowed', false)) throw new Error(`Notes are disabled for ${user.profile}`);
    else return this.getNoteStore(user.provider).clearNotes(user, user.profile, destroy_containers);
  }

  public async notesLoad(user: ForaUser): Promise<Note[]> {
    logging.infoFP(LOG_NAME, 'notesLoad', user.profile, 'Refreshing notes');
    let loaded_notes: Note[];

    if (_.get(user, 'settings.notes.useFallback', false)) loaded_notes = await this.fallback_notes.loadNotes(user, user.profile);
    else if (_.get(user, 'settings.notes.disallowed', false)) throw new Error(`Notes are disabled for ${user.profile}`);
    else return this.getNoteStore(user.provider).loadNotes(user, user.profile);

    logging.infoFP(LOG_NAME, 'notesLoad', user.profile, `Refreshed ${loaded_notes.length} Notes`);
    if (logging.isDebug(user.profile)) DEBUG('NOTES = %O', loaded_notes);
    return loaded_notes;
  }

  public async onboardingStep(template: globalTypes.TemplateType): Promise<globalTypes.OnboardingStep> {
    if (template) {
      const steps = (await this.find(null, GlobalType.OnboardingStep, template)) as globalTypes.OnboardingStep[];
      if (steps && steps.length) return steps[0];
    }
    return null;
  }

  /*************************** */
  /******** People **********/
  /*************************** */

  public async people(user: ForaUser, fields: string[] = null): Promise<Person[]> {
    // if (fields && !fields.includes('network')) fields.push('network');
    let results = (await this.findAtt(user.profile, EntityType.Person, 'network', [false, null])) /*, false, 0, fields)*/ as Person[];
    // results = results.filter(p => !p.network);
    const self = results.find(p => p.comms.includes(user.email));
    if (self) self.self = true;
    if (fields && fields.length) {
      const rem_fields = new Person().schema.fields.map(f => f.name).filter(n => !fields.includes(n));
      results.forEach(r => {
        for(const rm of rem_fields) {
          if (rm in r) delete r[rm];
        }
      });
    }
    return results;
  }

  public async networkPeople(user: ForaUser): Promise<Person[]> {
    return this.findAtt(user.profile, EntityType.Person, 'network', [true]) as Promise<Person[]>;
  }

  public async peopleByAttributeId(user: User, id: Uid[]): Promise<Person[]> {
    let results;
    if (id.length === 1) results = (await this.find(user.profile, EntityType.Person, id[0])) as Person[];
    else results = (await this.findAtt(user.profile, EntityType.Person, 'id', id)) as Person[];
    results.forEach(p => {
      if (p.comms.includes(user.email)) p.self = true;
    });
    return results;
  }

  public async peopleIds(user: ForaUser, account?: Uid): Promise<Uid[]> {
    if (account) return this.findIds(user.profile, EntityType.Person, 'account', [account]);
    return this.findIds(user.profile, EntityType.Person);
  }

  public async peopleCheckIds(user: User, ids: Uid[]): Promise<Uid[]> {
    let results = [];
    const rids = ids.slice();
    while(rids.length) {
      const r = await this.findKeysByFilter(user.profile, EntityType.Person, [{ name: 'id', op: 'IN', val: rids.splice(0,30) }]);
      if (r) results = results.concat(r);
    }
    return results.map(r => r.name);
  }

  public async peopleSave(user: ForaUser, people: Person[]): Promise<void> {
    const anonymous = people.find(p => p.id === `people/${FORA_PROFILE}`);
    if (anonymous) throw new globalTypes.InternalError(500, 'Cannot save person with anonymous id', anonymous);
    await this.saveAll(user ? user.profile : undefined, people);
  }

  public async personByAttributeComms(user: User, comms: string[]): Promise<Person[]> {
    if (!comms || !comms.length) return [];
    const people = (await this.findAtt(user.profile, EntityType.Person, 'comms', comms)) as Person[];
    if (people) {
      for (const person of people) {
        if (person) {
          if (person.comms.includes(user.email)) person.self = true;
        }
      }
      return people;
    }
    return [];
  }

  public async personById(user: User, id: Uid): Promise<Person> {
    if (!id) return null;
    const results = (await this.find(user.profile, EntityType.Person, id)) as Person[];
    if (results && results.length) {
      const person = results[0] as Person;
      if (person.comms.includes(user.email) || 
        (person.id && person.id.startsWith('people/a') && person.id !== ANONYMOUS_ID)
      ) person.self = true;
      return person;
    }
    return null;
  }

  public personDelete(user: User, person: Partial<Person>): Promise<void> {
    return this.remove(user.profile, person);
  }

  public async personSave(user: ForaUser, person: Person): Promise<Person> {
    if (user.profile === FORA_PROFILE) throw new globalTypes.InternalError(500, `Anonmous user cannot save contact ${person.id}`, person);
    if (person.id === `people/${FORA_PROFILE}`) throw new globalTypes.InternalError(500, `Cannot save person with anonymous id`, person);
    await this.add(user.profile, person, person.nonIndexedFields);
    return person;
  }

  public async introductionById(id: Uid): Promise<globalTypes.Introduction> {
    const results = (await this.find(null, GlobalType.Introduction, id)) as globalTypes.Introduction[];
    if (results && results.length) return results[0];
    return null;
  }

  public async introductionsByAtt(att: string, value: string): Promise<globalTypes.Introduction[]> {
    const intros = (await this.findAtt(null, GlobalType.Introduction, att, [value])) as globalTypes.Introduction[];
    if (intros && intros.length) return intros;
    return null;
  }

  public async introductionSave(intro: globalTypes.Introduction):  Promise<globalTypes.Introduction> {
    await this.add(null, intro, intro.nonIndexedFields);
    return intro;
  }

  public photoById(user: ForaUser, photo_id: Uid): Promise<Document> {
    if (config.isEnvOffline()) return this.getFileStore(user.provider).getFile(user, user.profile, photo_id);
    return this.fallback_drive.getFile(user, user.profile, photo_id);
  }

  public photoSave(user: ForaUser, photo: Document): Promise<Document> {
    if (!photo.props) photo.props = { type: FileUsage.Photo };
    else photo.props.type = FileUsage.Photo;

    if (config.isEnvOffline()) return this.getFileStore(user.provider).newFile(user, user.profile, photo)
    return this.fallback_drive.newFile(user, user.profile, photo);
  }

  public photoDelete(user: ForaUser, photo_id: Uid): Promise<void> {
    if (config.isEnvOffline()) return this.getFileStore(user.provider).deleteFile(user, user.profile, {file_id: photo_id})
    return this.fallback_drive.deleteFile(user, user.profile, {file_id: photo_id});
  }

  /*************************** */
  /******** Plans **********/
  /*************************** */
  public async plansLoad(group_id: Uid) {
    return (await this.find(group_id, GlobalType.Plan)) as globalTypes.Plan[];
  }

  public async planGet(group_id: Uid, id: Uid): Promise<globalTypes.Plan> {
    const results = (await this.find(group_id, GlobalType.Plan, id)) as globalTypes.Plan[];
    if (results && results.length) return results[0];
    return null;
  }

  public async planGetAll(group_id: Uid, ids: Uid[]): Promise<globalTypes.Plan[]> {
    return this.findAtt(group_id, GlobalType.Plan, 'id', ids) as Promise<globalTypes.Plan[]>;
  }

  public async planSave(group_id: Uid, plan: globalTypes.Plan): Promise<globalTypes.Plan> {
    await this.add(group_id, plan, [], true);
    return plan;
  }

  public async planDelete(group_id: Uid, plan: Partial<globalTypes.Plan>) {
    await this.remove(group_id, plan);
  }

  /*************************** */
  /******** Projects **********/
  /*************************** */
  public async projects(user: ForaUser, is_public?: boolean, groups?: Uid[]): Promise<Project[]> {
    let global_projects: globalTypes.Project[];
    if (is_public) global_projects = (await this.findAtt(null, GlobalType.Project, 'public', [true])) as globalTypes.Project[];
    else if(groups && groups.length) global_projects = (await this.findAtt(null, GlobalType.Project, 'groups', groups)) as globalTypes.Project[];
    else return this.find(user.profile, EntityType.Project) as Promise<Project[]>;

    if (global_projects) return global_projects.map(gp => new Project(uncompress(gp.project) as Project));
    return null;
  }

  public async project(user: ForaUser, id: Uid): Promise<Project> {
    const projects = await this.find(user.profile, EntityType.Project, id);
    if (projects && projects.length) return projects[0] as Project;
    return null;
  }

  public async projectById(id: Uid): Promise<Project> {
    try {
      const data: globalTypes.Project[] = (await this.find(null, GlobalType.Project, id)) as globalTypes.Project[];
      if (data && data.length) {
        return new Project(uncompress(data[0].project) as Project);
      }
    } catch (err) {
      logging.errorF(LOG_NAME, 'projectById', `Error finding project ${id}`, err);
    }

    return null;
  }

  public async projectByContract(user: ForaUser, contract_id: Uid[]): Promise<Project[]> {
    return this.findAtt(user.profile, EntityType.Project, 'contract', contract_id) as Promise<Project[]>
  }

  public projectDelete(user: ForaUser, project: Project): Promise<void> {
    return this.remove(user.profile, project);
  }

  public async projectIds(user: ForaUser): Promise<Uid[]> {
    return this.findIds(user.profile, EntityType.Project);
  }

  public async projectsOpen(user: ForaUser): Promise<Project[]> {
    return this.findAtt(user.profile, EntityType.Project, 'archived', [false]) as Promise<Project[]>;
  }

  public async projectSave(user: ForaUser, project: Project): Promise<Project> {
    await this.add(user.profile, project, project.nonIndexedFields);
    return project;
  }

  /************************************ */
  /********* Recommendations *********** */
  /************************************ */

  public async recommendationAdd(recommendation: globalTypes.Recommendation): Promise<globalTypes.Recommendation> {
    await this.add(null, recommendation, recommendation.nonIndexedFields);
    return recommendation;
  }

  public async recommendationDelete(recommendation: globalTypes.Recommendation): Promise<void> {
    await this.remove(null, recommendation);
  }

  public async recommendationsFind(from?: Uid, to?: string | string[]): Promise<globalTypes.Recommendation[]> {
    if (to) return this.findAtt(null, GlobalType.Recommendation, 'to', Array.isArray(to) ? to : [to]) as Promise<globalTypes.Recommendation[]>;
    if (from) return this.findAtt(null, GlobalType.Recommendation, 'from', [from]) as Promise<globalTypes.Recommendation[]>;
    return this.find(null, GlobalType.Recommendation) as Promise<globalTypes.Recommendation[]>;
  }

  public async recommendationGet(id: Uid): Promise<globalTypes.Recommendation> {
    const recs = await this.find(null, GlobalType.Recommendation, id);
    if (recs) return recs[0] as globalTypes.Recommendation;
  }

  /************************************ */
  /********* Referrals *********** */
  /************************************ */

  public async referralAdd(referral: globalTypes.Referral): Promise<globalTypes.Referral> {
    await this.add(null, referral, referral.nonIndexedFields);
    return referral;
  }

  public async referralFind(project?: Uid, referrer?: Uid): Promise<globalTypes.Referral[]> {
    if (project) return this.findAtt(null, GlobalType.Referral, 'project', [project]) as Promise<globalTypes.Referral[]>;
    if (referrer) return this.findAtt(null, GlobalType.Referral, 'referrer', [referrer]) as Promise<globalTypes.Referral[]>;
    return this.find(null, GlobalType.Referral) as Promise<globalTypes.Referral[]>;
  }

  public async referralGet(id: Uid): Promise<globalTypes.Referral> {
    const refs = await this.find(null, GlobalType.Referral, id);
    if (refs) return refs[0] as globalTypes.Referral;
  }

  /************************************ */
  /********* Skills *********** */
  /************************************ */

  public async skillByName(name: string): Promise<globalTypes.Skill> {
    const skill = await this.find(null, GlobalType.Skill, name) as globalTypes.Skill[];
    if (skill.length) return skill[0];
    return null;
  }

  public async skillByInitialism(initials: string): Promise<globalTypes.Skill[]> {
    const skills = await this.findAtt(null, GlobalType.Skill, 'initialisms', [initials]) as globalTypes.Skill[];
    if (skills) return skills.filter(s => s.initialisms && s.initialisms.length);
    return null;
  }

  public async skillBySynonym(synonym: string): Promise<globalTypes.Skill[]> {
    const skills = await this.findAtt(null, GlobalType.Skill, 'synonyms', [synonym]) as globalTypes.Skill[];
    if (skills) return skills.filter(s => s.synonyms && s.synonyms.length);
    return null;
  }

  public async skills(): Promise<globalTypes.Skill[]> {
    let skills  = (await this.findAtt(null, GlobalType.Skill)) as globalTypes.Skill[];
    const synonyms = (await this.findAtt(null, GlobalType.Skill, 'synonyms')) as globalTypes.Skill[];
    if (synonyms) skills = skills.concat(synonyms.filter(s => s.synonyms && s.synonyms.length));
    const initials = (await this.findAtt(null, GlobalType.Skill, 'initialisms')) as globalTypes.Skill[];
    if (initials) skills = skills.concat(initials.filter(i => i.initialisms && i.initialisms.length));

    return skills;
  }

  public async skillFind(names: string[], synonyms = false, initials = false): Promise<globalTypes.Skill[]> {
    if (!names || !names.length) return [];

    let skills = (await this.findAtt(null, GlobalType.Skill, TagType.skill as string, names)) as globalTypes.Skill[];

    if (synonyms) {
      const synonyms = (await this.findAtt(null, GlobalType.Skill, 'synonyms', names)) as globalTypes.Skill[];
      if (synonyms) skills = skills.concat(synonyms.filter(s => !names.includes(s.skill) && s.synonyms && s.synonyms.length));
    }

    if (initials) {
      const initials = (await this.findAtt(null, GlobalType.Skill, 'initialisms', names)) as globalTypes.Skill[];
      if (initials) skills = skills.concat(initials.filter(i => !names.includes(i.skill) && i.initialisms && i.initialisms.length));
    }

    return skills;
  }

  public async skillDelete(skill: Partial<globalTypes.Skill>) {
    return this.remove(null, skill);
  }

  public async skillSave(skill: globalTypes.Skill) {
    await this.add(null, skill, skill.nonIndexedFields);
    return skill;
  }

  public async skillSaveAll(skills: globalTypes.Skill[]) {
    await this.saveAll(null, skills);
    return skills;
  }

  /************************************ */
  /********* SkillCategories *********** */
  /************************************ */

  public async skillCategories(group_id: Uid) {
    return (await this.find(group_id, GlobalType.SkillCategory)) as globalTypes.SkillCategory[];
  }

  public async skillCategoriesFind(skills: string[]) {
    return (await this.findAtt(null, GlobalType.SkillCategory, 'skills', skills)) as globalTypes.SkillCategory[];
  }

  public async skillCategorySave(group_id: Uid, sc: globalTypes.SkillCategory): Promise<globalTypes.SkillCategory> {
    await this.add(group_id, sc, [], true);
    return sc;
  }

  public async skillCategorySaveAll(sc: globalTypes.SkillCategory[]) {
    await this.saveAll(null, sc);
    return sc;
  }

  public async skillCategoryDelete(group_id: Uid, sc: globalTypes.SkillCategory) {
    await this.remove(group_id, sc);
  }

  public async skillCategoryDeleteSome(sc: globalTypes.SkillCategory[]) {
    await this.removeAll(null, sc);
  }

  public async skillCategoryDeleteAll() {
    const sc_ids = await this.findIds(null, GlobalType.SkillCategory);
    if (sc_ids) {
      const sc = sc_ids.map(id => new globalTypes.SkillCategory({id}));
      await this.removeAll(null, sc);
    }
  }

  /************************************ */
  /************** Content **************** */
  /************************************ */

  public async contentByPath(path: string): Promise<globalTypes.Content> {
    const start = await this.findAtt(null, GlobalType.Content, 'path', [path], true) as globalTypes.Content[];
    if (start && start.length) return start[0];
    return null;
  }

  public async contentById(id: Uid): Promise<globalTypes.Content> {
    const start = await this.find(null, GlobalType.Content, id) as globalTypes.Content[];
    if (start && start.length) return start[0];
    return null;
  }

  public async contentSet(): Promise<string[]> {
    const start = await this.findAtt(null, GlobalType.Content, null, null, false, -1, ['path']) as globalTypes.Content[];
    if (start && start.length) return start.map(s => s.path);
    return [];
  }

  /************************************ */
  /************** Tasks **************** */
  /************************************ */

  // noinspection JSUnusedGlobalSymbols
  public async taskById(user: ForaUser, id: Uid): Promise<Task> {
    const results = await this.find(user.profile, EntityType.Task, id);
    if (results && results.length) return results[0] as Task;
    return null;
  }

  public async tasksByDate(user: ForaUser, start: Date = null, end: Date = null): Promise<Task[]> {
    if (start && end && start.getTime() === end.getTime()) return this.findAtt(user.profile, EntityType.Task, 'due', [start]) as Promise<Task[]>;
    else if (start && start.getTime() && !end) return this.findRange(user.profile, EntityType.Task, 'due', start, true) as Promise<Task[]>;
    else if (end && end.getTime() && !start) return this.findRange(user.profile, EntityType.Task, 'due', end, false) as Promise<Task[]>;
    else if (!start && !end) return this.tasks(user);
    throw new Error('Cannot search tasks by start and end');
  }

  public async taskIds(user: ForaUser, account?: Uid): Promise<Uid[]> {
    if(account) return this.findIds(user.profile, EntityType.Task, 'account', [account]);
    return this.findIds(user.profile, EntityType.Task);
  }

  public async tasks(user: ForaUser): Promise<Task[]> {
    return this.find(user.profile, EntityType.Task) as Promise<Task[]>;
  }

  async taskSave(user: ForaUser, task: Task): Promise<Task> {
    await this.add(user.profile, task, task.nonIndexedFields);
    return task;
  }

  async taskDelete(user: ForaUser, task: Task): Promise<void> {
    await this.remove(user.profile, task);
  }

  /************************************ */
  /******** JAB / Project Templates **** */
  /************************************ */

  async jab(jid: Uid): Promise<globalTypes.Jab> {
    const j = await this.find(null, GlobalType.Jab, jid);
    if (j && j.length) return j[0] as globalTypes.Jab;
    return null;
  }

  async jabs(gid: Uid[] = null): Promise<globalTypes.Jab[]> {
    if (gid) return this.findAtt(null, GlobalType.Jab, 'group', gid) as Promise<globalTypes.Jab[]>;
    return this.findAtt(null, GlobalType.Jab, 'public', [true]) as Promise<globalTypes.Jab[]>;
  }

  async jabSave(j: globalTypes.Jab) {
    return this.add(null, j, j.nonIndexedFields);
  }

  async jabDelete(id: Uid) {
    const j = new globalTypes.Jab({id});
    return this.remove(null, j);
  }

  async jabByProject(project: Uid, group: Uid): Promise<globalTypes.Jab> {
    const jabs = (await this.findAtt(null, GlobalType.Jab, 'project', [project])) as globalTypes.Jab[];
    if (jabs && jabs.length) return group ? jabs.find(j => j.group == group) : jabs[0];
    return null;
  }

  /************************************ */
  /************* Searches ***************** */
  /************************************ */

  async search(sid: Uid): Promise<globalTypes.Search> {
    const searches = await this.find(null, GlobalType.Search, sid);
    if (searches && searches.length) return searches[0] as globalTypes.Search;
    return null;
  }

  async searchSave(search: globalTypes.Search) {
    await this.add(null, search, search.nonIndexedFields);
    return search;
  }

  async searchDelete(search: Partial<globalTypes.Search>) {
    return this.remove(null, search);
  }

  /************************************ */
  /************* Templates ***************** */
  /************************************ */

  async template(tid: Uid): Promise<globalTypes.Template> {
    const templates = await this.find(null, GlobalType.Template, tid);
    if (templates && templates.length) return templates[0] as globalTypes.Template;
    return null;
  }

  async templates(): Promise<globalTypes.Template[]> {
    return this.find(null, GlobalType.Template) as Promise<globalTypes.Template[]>;
  }

  async templateSave(id: Uid, value: number): Promise<globalTypes.Template> {
    const template = new globalTypes.Template({id, value});
    await this.add(null, template, template.nonIndexedFields);
    return template;
  }

  async templateDelete(id): Promise<void> {
    const template = new globalTypes.Template({id});
    return this.remove(null, template);
  }

  async track(user: ForaUser) {
    if (user.tracking) {
      return this.add(null, user.tracking, user.tracking.nonIndexedFields);
    }
  }

  /************************************ */
  /************* Item Cache ***************** */
  /************************************ */

  public async itemInfoCacheGet(user: User, itypes?: EntityType[]): Promise<ItemCache[]> {
    if (itypes && itypes.length) return this.findAtt(user.profile, EntityType.ItemCache, 'itype', itypes) as Promise<ItemCache[]>;
    return this.find(user.profile, EntityType.ItemCache) as Promise<ItemCache[]>;
  }

  public async itemInfoCacheSet(user: User, items: ItemCache[]) {
    return this.saveAll(user.profile, items);
  }

  /************************************ */
  /************* Vanities ***************** */
  /************************************ */

  public async getVanity(vanity: string): Promise<globalTypes.Vanity> {
    // const vanities = (await this.find('', GlobalType.Vanity, vanity)) as globalTypes.Vanity[];
    const vanities = await this.findAtt(null, GlobalType.Vanity, 'vanity', [vanity]);
    if (vanities && vanities.length) return vanities[0] as globalTypes.Vanity;
    return null;
  }
  
  public async getVanities(vanities: string[]): Promise<globalTypes.Vanity[]> {
    return this.findAtt(null, GlobalType.Vanity, 'id', vanities) as Promise<globalTypes.Vanity[]>;
  }

  public async getVanitiesByStatus(status: ProfileStatus[]): Promise<string[]> {
    const vanities = (await this.findAtt(null, GlobalType.Vanity, 'status', status, false, 0, ['id'])) as globalTypes.Vanity[];
    return vanities ? vanities.map(v => v.id) : [];
  }

  public async vanityByIds(ids: Uid[]): Promise<globalTypes.Vanity[]> {
    return this.findAtt(null, GlobalType.Vanity, 'profile', ids) as Promise<globalTypes.Vanity[]>;
  }

  public async vanitySave(vanity: globalTypes.Vanity): Promise<void> {
    return this.add(null, vanity, vanity.nonIndexedFields);
  }

  public async vanityDelete(vanity: Partial<globalTypes.Vanity>): Promise<void> {
    return this.remove(null, vanity);
  }

  /************************************ */
  /************* User ***************** */
  /************************************ */
  public async userGlobalByEmail(email: string | string[]): Promise<globalTypes.User[]> {
    if (!Array.isArray(email)) email = [email];
    const email_users = (await this.findAtt(null, GlobalType.User, 'email', email.map(e => e.toLowerCase()))) as globalTypes.User[];
    const mapped_users = email_users.filter(u => u.id !== u.profile);
    const loaded_users = email_users.filter(u => u.id === u.profile);
    for (const muser of mapped_users) {
      const user = await this.userGlobalById(muser.profile);
      if (user) loaded_users.push(user);
    }
    return _.uniqBy(loaded_users as globalTypes.User[], 'id');
  }

  public async userGlobalByAffiliate(affiliate: string): Promise<globalTypes.User> {
    const guser = (await this.findAtt(null, GlobalType.User, 'affiliate', [affiliate])) as globalTypes.User[];
    if(guser?.length) return guser[0];
  } 

  public async usersMapGlobalEmail(uid: Uid, email: string) {
    const reg_user = this._serializeGlobalUser(new globalTypes.User({
      email,
      profile: uid,
    }));

    // use a prefixed id
    reg_user.key = this.key(null, {id: `f_${uid}`, type: GlobalType.User});
    reg_user.data.id = `f_${uid}`;
    await this.save(null, GlobalType.User, reg_user);
  }

  public async usersGlobalByGroup(group_ids: Uid[]): Promise<globalTypes.User[]> {
    return this.findAtt(null, GlobalType.User, 'groups', group_ids) as Promise<globalTypes.User[]>;
  }

  public async userGlobalByVanity(vanity: string): Promise<globalTypes.User> {
    const users = await this.findAtt(null, GlobalType.User, 'vanity', [vanity]);
    if (users && users.length) return users[0] as globalTypes.User;
    return null;
  }

  public async usersGlobalByVanities(vanities: string[]): Promise<globalTypes.User[]> {
    const users = await this.findAtt(null, GlobalType.User, 'vanity', vanities);
    if (users && users.length) return users as globalTypes.User[];
    return [];
  }


  public async userById(user: ForaUser, id: Uid): Promise<User> {
    const users = await this.find(user.profile, EntityType.User, id);
    if (users && users.length) return users[0] as User;
    return null;
  }

  public async userGlobalById(id: Uid): Promise<globalTypes.User> {
    const results = await this.find(null, GlobalType.User, id);
    if (results && results.length) return results[0] as globalTypes.User;
    return null;
  }

  public async userGlobalByIds(ids: Uid[]): Promise<globalTypes.User[]> {
    const results = await this.findAtt(null, GlobalType.User, 'id', ids, false);
    if (results && results.length) return results as globalTypes.User[];
    return [];
  }

  public async userGlobalReferral(user: globalTypes.User, referred_user: User): Promise<boolean> {
    const found_user = await this.userGlobalById(user.id);
    if(!found_user) return false;

    let did_refer = false;
    if(found_user.referrals) {
      if(!found_user.referrals.includes(referred_user.id)) {
        found_user.referrals.push(referred_user.id);
        did_refer = true;
      }
    } else {
      found_user.referrals = [referred_user.id];
      did_refer = true;
    }
    
    const reg_user = this._serializeGlobalUser(found_user, found_user.messages,found_user.notifications);
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'userGlobalRegister', user.profile, `Saving global user: ${JSON.stringify(reg_user)}`);
    await this.save(null, GlobalType.User, reg_user);
    return did_refer;
  }

  public async resetGlobalReferral(user: globalTypes.User) {
    const found_user = await this.userGlobalById(user.id);
    if(!found_user) return false;
    if(found_user.referrals) found_user.referrals = [];
    const reg_user = this._serializeGlobalUser(found_user, found_user.messages,found_user.notifications);
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'userGlobalRegister', user.profile, `Saving global user: ${JSON.stringify(reg_user)}`);
    await this.save(null, GlobalType.User, reg_user);
  }

  public async userGlobalUpdateVanity(user: ForaUser): Promise<globalTypes.User> {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot update vanity for anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot update vanity for guest user', user);

    const global_user = await this.userGlobalById(user.profile); // globalTypes.User = results[0] as globalTypes.User;
    global_user.vanity = user.vanity;
    const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
    await this.save(null, GlobalType.User, reg_user);
    return global_user;
  }

  public async userGlobalByAccountId(account_type: string, account_id): Promise<globalTypes.User> {
    const data: globalTypes.User[] = (await this.findAtt(null, GlobalType.User, 'auth_ids', [`${account_type}_${account_id}`])) as globalTypes.User[];
    if (data && data.length) return data[0];
  }

  public async userGlobalMessagesAndNotifications(user: ForaUser): Promise<[string[], globalTypes.Notification[]]> {
    const global_user = await this.userGlobalById(user.profile); //globalTypes.User = results[0] as globalTypes.User;
    if (global_user) {
      const now = new Date();
      const notifications: globalTypes.Notification[] = [];
      for (const notify of global_user.notifications) {
        if (!notify.when || new Date(notify.when) <= now) {
          // type change handling
          if (notify['data'] || notify['fcm_options'] || notify['notification']) {
            notifications.push({webpush:notify as globalTypes.WebPush });
          }
          else notifications.push(notify);
        }
      }
      return [global_user.messages, notifications];
    }
    return [null, null];
  }


  public async userGlobalMessageAdd(user: ForaUser, message: string): Promise<void> {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot add message for anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot add message for guest user', user);

    const global_user = await this.userGlobalById(user.profile); //found[0] as globalTypes.User;
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'userGlobalMessageAdd', user.profile, `User = ${global_user}`);
    if (!global_user) {
      logging.errorFP(LOG_NAME, 'userGlobalMessageAdd', user.profile, 'Could not find user record', null);
      return;
    }

    if (!global_user.messages) global_user.messages = [];
    global_user.messages.push(message);

    // clean copy to save
    const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
    await this.save(null, GlobalType.User, reg_user);
  }

  public async userGlobalMessages(user: ForaUser): Promise<string[]> {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot clear messages for anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot clear messages for guest user', user);

    const global_user = await this.userGlobalById(user.profile); //globalTypes.User = results[0] as globalTypes.User;
    if (global_user) return global_user.messages;
    return null;
  }

  public async userGlobalMessagesClear(user: ForaUser): Promise<void> {
    const global_user = await this.userGlobalById(user.profile); 
    if (global_user) {
      global_user.messages = [];

      const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
      await this.save(null, GlobalType.User, reg_user);
    }
  }

  public async userGlobalNotificationUpdate(user: ForaUser) {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot add notification for anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot add notification for guest user', user);

    const global_user = await this.userGlobalById(user.profile); //found[0] as globalTypes.User;
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'userGlobalMessageAdd', user.profile, `User = ${global_user}`);
    if (!global_user) {
      logging.errorFP(LOG_NAME, 'userGlobalNotificationAdd', user.profile, 'Could not find user record', new Error());
      return;
    }

    global_user.last_notify = new Date();

    const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
    await this.save(null, GlobalType.User, reg_user);
  }

  public async userGlobalNotificationAdd(user: ForaUser, notification: globalTypes.Notification, update_last: boolean): Promise<void> {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot add notification for anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot add notification for guest user', user);

    const global_user = await this.userGlobalById(user.profile); //found[0] as globalTypes.User;
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'userGlobalMessageAdd', user.profile, `User = ${global_user}`);
    if (!global_user) {
      logging.errorFP(LOG_NAME, 'userGlobalNotificationAdd', user.profile, 'Could not find user record', new Error());
      return;
    }

    if (!global_user.notifications) global_user.notifications = [];
    if (update_last) global_user.last_notify = new Date();
    global_user.notifications.push(notification);

    // Making notifications unique by link and data
    global_user.notifications = _.uniqWith(global_user.notifications.map(notify => {
      if (notify['data'] || notify['fcm_options'] || notify['notification']) return {webpush:notify as globalTypes.WebPush};
      return notify;
    }), (a: globalTypes.Notification, b: globalTypes.Notification) => {
      if (!a || !b) return false;
      if (!a.webpush || !b.webpush) return false;
      const awp = a.webpush;
      const bwp = b.webpush;
      if (awp.notification && bwp.notification && awp.notification.tag === bwp.notification.tag) return true;
      if (awp.fcm_options && bwp.fcm_options && awp.fcm_options.link === bwp.fcm_options.link) return true;
      if (awp.data && awp.data.message && bwp.data && bwp.data.message && awp.data.message === bwp.data.message) return true;
      return false;
    });

    // clean copy to save
    const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
    await this.save(null, GlobalType.User, reg_user);
  }

  public async userGlobalNotifications(user: ForaUser): Promise<globalTypes.Notification[]> {
    const global_user = await this.userGlobalById(user.profile); 
    if (global_user) {
      const now = new Date();
      const notifications: globalTypes.Notification[] = [];
      for (const notify of global_user.notifications) {
        if (!notify.when || new Date(notify.when) <= now) {
          // type change handling
          if (notify['data'] || notify['fcm_options'] || notify['notification']) {
            notifications.push({webpush:notify as globalTypes.WebPush });
          }
          else notifications.push(notify);
        }
      }
      return notifications;
    }
    return null;
  }

  public async userGlobalNotificationsClear(user: ForaUser, type: NotificationType = null): Promise<void> {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot clear notifications for anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot clear notifications for guest user', user);

    const now = new Date();
    const global_user = await this.userGlobalById(user.profile);
    global_user.notifications = global_user.notifications ? 
      global_user.notifications.filter(n => (type && n.type !== type) && (!n.when || new Date(n.when) > now) ) : [];

    const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
    await this.save(null, GlobalType.User, reg_user);
  }

  public async userGlobalNotificationClear(user: ForaUser, id: Uid): Promise<void> {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot clear notifications for anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot clear notifications for guest user', user);

    const global_user = await this.userGlobalById(user.profile);
    global_user.notifications = global_user.notifications.filter(n => n.id !== id);
    const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
    await this.save(null, GlobalType.User, reg_user);
  }

  public async userGlobalOnboarding(user: ForaUser, onboarding: globalTypes.TemplateType) {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot onboard anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot onboard guest user', user);

    const global_user = await this.userGlobalById(user.profile);
    global_user.last_notify = new Date();
    global_user.onboarding = onboarding;

    const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
    await this.save(null, GlobalType.User, reg_user);
  }

  public async userGlobalRegister(user: ForaUser, context: string = null, referred_by: string = null): Promise<globalTypes.User> {
    if (user.isAnonymousAccount()) throw new globalTypes.InternalError(500, 'Cannot register anonymous user', user);
    if (user.isGuestAccount()) throw new globalTypes.InternalError(500, 'Cannot register guest user', user);

    const found_user = await this.userGlobalById(user.id);
    const global_user = this._globalFromUser(user, found_user, context, referred_by);
    const reg_user = this._serializeGlobalUser(global_user, found_user ? found_user.messages : null,
      found_user ? found_user.notifications : null);
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'userGlobalRegister', user.profile, `Saving global user: ${JSON.stringify(reg_user)}`);
    await this.save(null, GlobalType.User, reg_user);
    return global_user;
  }

  public async bulkGlobalUserSave(users: globalTypes.User[]): Promise<void> {
    const reg_users = users.map(global_user => this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications));
    return this.bulkSave(null, reg_users);
  }

  public async userGlobalSave(global_user: globalTypes.User) {
    const reg_user = this._serializeGlobalUser(global_user, global_user.messages, global_user.notifications);
    return this.save(null, GlobalType.User, reg_user);
  }

  public async transferSave(transfer: globalTypes.Transfer): Promise<void> {
    return this.add(null, transfer, transfer.nonIndexedFields);
  }

  public async transfers(all = false): Promise<globalTypes.Transfer[]> {
    if (all) return this.find(null, GlobalType.Transfer) as Promise<globalTypes.Transfer[]>;
    else return this.findAtt(null, GlobalType.Transfer, 'status', ['incoming_payment_waiting']) as Promise<globalTypes.Transfer[]>;
  }

  public async userIds(user: ForaUser): Promise<Uid[]> {
    return this.findIds(user.profile, EntityType.User);
  }

  users(user: ForaUser): Promise<User[]> {
    return this.find(user.profile, EntityType.User) as Promise<User[]>;
  }

  async userSettings(user: ForaUser): Promise<ForaUserSettings> {
    const record = await this.find(user.profile, EntityType.User, `settings_${user.profile}`) as User[];
    if (record && record.length) {
      try {
        const settings = JSON.parse(Buffer.from(record[0].settings.toString(), 'base64').toString());
        return settings;
      } catch(e) {
        logging.errorFP(LOG_NAME, 'userSettings', user.profile, `Error getting settings`, e);
      }
    }
  }

  async userSettingsSave(user: ForaUser, settings: ForaUserSettings) {
    await this.add(user.profile, new User({
      id: `settings_${user.profile}`,
      settings: Buffer.from(JSON.stringify(settings)).toString('base64') as any as ForaUserSettings,
    }));
  }

  public async userSave(user: ForaUser, update = false, force = false, update_last = true): Promise<void> {
    if (user.isAuthenticated(AuthLevel.Demo) || user.isGuestAccount()) return;

    if (force || user.isAuthenticated()) {
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'userSave', user.profile, `Saving user ${user.id}${ update ? ' update': ''}${ force ? ' force': ''}`);
      if (update) {
        await this.add(user.profile, user.saveUpdate(), user.nonIndexedFields);
      } else {
        // check latest group membership
        const user_group_id = `group_${user.profile}`;
        const [user_group] = (await this.find(user.profile, EntityType.User, user_group_id)) as User[];
        if (user_group) {
          user.groups = user_group.groups;
          await this.remove(user.profile, new User({id: user_group_id}));
        }
        const user_settings_id = `settings_${user.profile}`;
        await this.remove(user.profile, new User({id: user_settings_id}));
        await this.add(user.profile, user.save(true, update_last), user.nonIndexedFields);
      }
    } else if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'userSave', user.profile, `NOT saving user ${user.id} - not authentiated`);
  }
  
  public async userGroupSave(user: ForaUser, groups: {[key:string]: string[]}) {
    if (user.isAuthenticated(AuthLevel.Demo) || user.isGuestAccount()) return;

    logging.infoFP(LOG_NAME, 'userGroupSave', user.profile, `Updating user groups to ${JSON.stringify(groups)}`);
    await this.add(user.profile, new User({
      id: `group_${user.profile}`,
      groups,
    }));
  }


  /***************************************************** */
  /************* Internal and Low Level **************** */
  /***************************************************** */

  private _globalFromUser(user: ForaUser, global_user: globalTypes.User, context: string = null, referred_by: string = null): globalTypes.User {
    return new globalTypes.User({
      id: user.profile,
      profile: user.profile,
      auth_ids: user.accounts ? flatten(Object.keys(user.accounts).map(provider => Object.keys(user.accounts[provider]).map(profile => `${provider}_${profile}`))) : [],
      name: user.name,
      email: user.email,
      start: user.start,
      last: user.last,
      last_notify: global_user ? global_user.last_notify : null,
      onboarding: global_user ? global_user.onboarding : null,
      promote: global_user ? global_user.promote : null,
      vanity: user.vanity,
      groups: user.groups ? (Array.isArray(user.groups) ? user.groups : Object.keys(user.groups)) : [],
      context: global_user ? global_user.context : context,
      locale: user.locale,
      affiliate: global_user && global_user.affiliate ? global_user.affiliate : hash(uuid()).slice(0,10),
      referred_by: global_user && global_user.referred_by ? global_user.referred_by : referred_by,
      referrals: global_user && global_user.referrals ? global_user.referrals : [],
    });
  }

  private _serializeGlobalUser(user: globalTypes.User, messages: string[] = null, notifications: globalTypes.Notification[] = null): DatastoreEntry {
    if(!user.email) throw new globalTypes.InternalError(500, 'Cannot register user without email', user);

    const gt_user = new globalTypes.User({
      id: user.profile,
      profile: user.profile,
      auth_ids: user.auth_ids,
      name: user.name,
      email: user.email,
      start: user.start,
      last: user.last,
      last_notify: user.last_notify,
      onboarding: user.onboarding,
      promote: user.promote,
      vanity: user.vanity,
      groups: user.groups ? _.uniq(Array.isArray(user.groups) ? user.groups : Object.keys(user.groups)) : [],
      messages: messages ? messages.slice(-3) : [],
      notifications: notifications ? notifications.slice(-3) : [],
      context: user.context,
      locale: user.locale,
      affiliate: user.affiliate ? user.affiliate : hash(uuid()).slice(0,10),
      referred_by: user.referred_by ? user.referred_by : undefined,
      referrals: user.referrals ? user.referrals : [],
    });

    for (let i = gt_user.messages.length - 1, l = 0; i >= 0; i--) {
      l += gt_user.messages[i].length;
      if (l >= 1450) {
        if (i === gt_user.messages.length -1 ) gt_user.messages = [gt_user.messages[i].slice(0, 1450)];
        else gt_user.messages = gt_user.messages.slice(i - gt_user.messages.length);
        break;
      }
    }

    return this.serialize(null, gt_user, ['messages', 'notifications']);
  }

  public async find(profile: Uid, type: EntityType | GlobalType, id?: Uid, retry = 100): Promise<IEntity[]> {
    if (id) {
      try {
        const key = this.key(profile, { id, type });
        // if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'find', profile, `Looking up ${JSON.stringify(key, null, 2)}`);
        const f_entities: any[] = [];
        const g_entities = await this.ds.get(key);
        let fEntity = undefined;
        if (g_entities) {
          for (const g_entity of g_entities) {
            if (g_entity) {
              const type_name = type.split('.').pop();
              const type_name_sc = _.startCase(_.toLower(type_name));
              // if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'find', profile, `Loading ${type_name_sc} ${g_entity.id}`);
              if (type_name_sc === 'Group') {
                fEntity = new Group(g_entity);
              } else if (type.indexOf('.') === -1) {
                fEntity = new globalTypes[type_name_sc](g_entity);
              } else {
                if(type_name_sc === 'User') {
                  fEntity = new User(g_entity);
                } else fEntity = new ItemTypes[type_name_sc](g_entity);
              }
            }

            if (fEntity) f_entities.push(fEntity);
          }
        }

        if (logging.isDebug(profile)) DEBUG(`find::${profile}::key=${logging.formatKey(key)}, result=${logging.formatEntities(f_entities)}`);
        return f_entities;
      } catch (err) {
        if (retryError(err)) {
          if(retry <= 30000) {
            logging.warnFP(LOG_NAME, 'find', profile, `Retrying in ${retry} finding ${type} id ${id}`);
            await setTimeout(retry);
            return this.find(profile, type, id, retry * 2);
          }
        }
        if (logging.isDebug(profile)) DEBUG(`find::${profile}::Error finding ${util.format(type)} for ${util.format(profile)}`, err);
        logging.errorFP(LOG_NAME, 'find', profile, `Error finding ${type} id ${id}`, err);
        return [];
      }
    } else {
      return this.findAtt(profile, type);
    }
  }

  /**
   * Find items that match attribute values
   *
   * @param profile - Google profile to use
   * @param type - Type of entity to search for
   * @param name - Name of the attribute we want to search on
   * @param vals - Values that the attribute must match
   * @param cached - Use cached results?
   * @param limit - limit number of results
   */
  public async findAtt(profile: Uid, type: EntityType | GlobalType, name: string = null, vals: any[] = null, cached = false, limit = 0, fields: string[] = null): Promise<IEntity[]> {
    // cached = false; // force cache false for now
    if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'findAtt', profile, `${type} ${name} ${vals} ${cached} ${limit} ${fields}`);
    try {
      const type_name = type.split('.').pop();
      const map_type = type.indexOf('.') !== -1;
      const op = '=';
      const qs = [];
      let entities = [];
      if (vals) {
        for (const val of vals) {
          let id = null;
          let r = null;
          if (cached) {
            id = await this.cache.lookupCacheAttVal(profile, type, name, val);
            if (id && typeof(id) === 'string') r = await this.find(profile, type, id);
          }

          if (r && r.length) entities = entities.concat(r);
          else {
            let q = this.ds.createQuery(profile, type_name).filter(new PropertyFilter(name, op, val));
            if (limit) q = q.limit(limit);
            if (!cached && fields && fields.length) {
              if (!fields.includes('id')) fields.push('id');
              q = q.select(fields).order('id');
            }
            qs.push({ q, val: val });
          }
        }
      } else {
        let q = null;
        if (profile) {
          q = this.ds.createQuery(profile, type_name);
        } else {
          q = this.ds.createQuery(type_name);
        }
        if (limit) q = q.limit(limit);
        if (fields) {
          if (!fields.includes('id')) fields.push('id');
          q = q.select(fields).order('id').groupBy('id');
        }
        qs.push({ q });
      }

      const type_name_sc = _.startCase(_.toLower(type_name));
      const type_fields = 
        type_name_sc === 'Group' ? new Group().schema.fields : 
        map_type ? 
          (type_name_sc === 'User' ? new User().schema.fields :
            new ItemTypes[type_name_sc]().schema.fields) 
        : new globalTypes[type_name_sc]().schema.fields;

      const found = [];
      for (const q_val of qs) {
        let retry = 100;
        try {
          let query_results = null;
          while (!query_results)  {
            try {query_results = await this.ds.runQuery(q_val.q); } 
            catch(err) {
              // Retry for these codes (Added 14 with #840)
              if (!retryError(err) || retry > 30000) {
                logging.errorFP(LOG_NAME, 'findAtt', profile, `Error running query ${q_val.q.namspace}.${q_val.q.kinds[0]} ${JSON.stringify(q_val.q.filters)}`, err);
                throw err;
              }

              const retry_msg = retry !== 0 ? ` retrying in ${retry}` : '';
              logging.warnFP(LOG_NAME, 'findAtt', profile, `Retry ${retry_msg}::${err.code}::${err.message}`);
              await setTimeout(retry);
              retry *= 2;
            }
          }

          const [results] = query_results;

          if (results) {
            let last_entity = null;
            for (const entity of results as IEntity[]) {
              if (!entity.id) {
                const key = entity[this.ds.KEY];
                entity.id = key ? key.name : undefined;
              }
              if (entity.id && !found.includes(entity.id)) {
                let resolved = entity;

                // need to aggregate arrays
                if (fields) {
                  let skip_assign = false;
                  if (!last_entity) {
                    last_entity = entity;
                    skip_assign = true;
                  }

                  if (entity.id === last_entity.id) {
                    for (const field of type_fields) {
                      if (field.isArray && last_entity[field.name]) {
                        if (!Array.isArray(last_entity[field.name])) last_entity[field.name] = [last_entity[field.name]];
                        if (!skip_assign && entity[field.name]) {
                          if (Array.isArray(entity[field.name])) last_entity[field.name] = last_entity[field.name].concat(entity[field.name]);
                          else last_entity[field.name].push(entity[field.name]);
                        }
                      }
                    }

                    continue;
                  } else resolved = last_entity;
                } else found.push(entity.id); // make sure no dupes if no fields

                last_entity = entity;

                if(type_name_sc === 'Group') {
                  resolved = new Group(resolved as Partial<Group>);
                } else if (map_type) {
                  if(type_name_sc === 'User') {
                    resolved = new User(resolved as Partial<User>);
                  } else resolved = new ItemTypes[type_name_sc](resolved);
                } else {
                  resolved = new globalTypes[type_name_sc](resolved);
                }

                if (q_val.val && cached) this.cache.cacheAttVal(profile, type, name, q_val.val, resolved.id);
                entities.push(resolved);
              }
            }

            // save last entity
            if (fields && last_entity) {
              let resolved = last_entity;
              if(type_name_sc === 'Group') {
                resolved = new Group(resolved as Partial<Group>);
              } else if (map_type) {
                if(type_name_sc === 'User') {
                  resolved = new User(resolved as Partial<User>);
                } else resolved = new ItemTypes[type_name_sc](resolved);
              } else {
                resolved = new globalTypes[type_name_sc](resolved);
              }

              if (q_val.val && cached) this.cache.cacheAttVal(profile, type, name, q_val.val, resolved.id);
              entities.push(resolved);
            }
          }
        } catch (err) {
          const filter = name ? `${name} = ${vals}` : '';
          if (retryError(err)) {
            qs.push(q_val);
            logging.warnFP(LOG_NAME, 'findAtt', profile, `Retrying query for ${type} ${filter}`, err);
            await setTimeout(retry);
            retry *= 2;
          } else {
            logging.errorFP(LOG_NAME, 'findAtt', profile, `Error running query for ${type} ${filter}: ${err.code}`, err);
          }
        }
      }

      return entities;
    } catch (err) {
      const filter = name ? `${name} = ${vals}` : '';
      logging.errorFP(LOG_NAME, 'findAtt', profile, `Error running query for ${type} ${filter}`, err);
      return [];
    }
  }

  public async findItemsByFilter(profile: Uid, type: EntityType | GlobalType, filters: {name: string; op: any; val: any}[], con: 'AND' | 'OR' = 'AND'): Promise<Partial<IEntity>[]> {
    let query = this.ds.createQuery(profile, type.split('.').pop()).select('id');

    if (filters.length) {
      if (filters.length === 1) query = query.filter(new PropertyFilter(filters[0].name, filters[0].op, filters[0].val));
      else {
        const pf = filters.map(filter => new PropertyFilter(filter.name, filter.op, filter.val));
        query = query.filter(con === 'AND' ? and(pf) : or(pf));
      }
    }

    const type_name = type.split('.').pop();
    const map_type = type.indexOf('.') !== -1;
    const type_name_sc = _.startCase(_.toLower(type_name));
    const new_func = map_type ? ItemTypes[type_name_sc] : globalTypes[type_name_sc];

    try {
      let retry = 100;
      let query_results = null
      while (!query_results)  {
        try { query_results = await this.ds.runQuery(query); }
        catch(err) {
          // Retry for these codes (Added 14 with #840)
          if (!retryError(err) || retry > 30000) {
            logging.errorFP(LOG_NAME, 'findItemsByFilter', profile, 'Error running query', err);
            throw err;
          }

          const retry_msg = retry !== 0 ? ` retrying in ${retry}` : '';
          logging.warnFP(LOG_NAME, 'findItemsByFilter', profile, `Retry ${retry_msg}::${err.code}::${err.message}`);
          await setTimeout(retry);
          retry *= 2;
        }
      }

      const entities = [];
      const [results] = query_results;
      if (results) {
        for (const item of results as IEntity []) {
          const found_item = new new_func({type, id:item.id});
          entities.push(found_item);
        }
      }
      return entities;
    } catch (err) {
      if (retryError(err)) {
        logging.warnFP(LOG_NAME, 'findItemsByFilter', profile, `Retrying running query for ${type}`, err);
        return this.findItemsByFilter(profile, type, filters, con);
      } else logging.errorFP(LOG_NAME, 'findItemsByFilter', profile, `Error running query for ${type}`, err);
    }
  }

  public async findKeysByFilter(profile: Uid, type: EntityType | GlobalType, filters: {name: string; op: any; val: any}[], con: 'AND' | 'OR' = 'AND'): Promise<DatastoreKey[]> {
    let query = this.ds.createQuery(profile, type.split('.').pop()).select('__key__');

    if (filters.length) {
      if (filters.length === 1) query = query.filter(new PropertyFilter(filters[0].name, filters[0].op, filters[0].val));
      else {
        const pf = filters.map(filter => new PropertyFilter(filter.name, filter.op, filter.val));
        query = query.filter(con === 'AND' ? and(pf) : or(pf));
      }
    }

    try {
      let retry = 100;
      let query_results = null
      while (!query_results)  {
        try { query_results = await this.ds.runQuery(query); }
        catch(err) {
          // Retry for these codes (Added 14 with #840)
          if (!retryError(err) || retry > 30000) {
            logging.errorFP(LOG_NAME, 'findKeysByFilter', profile, 'Error running filter query', err);
            throw err;
          }

          const retry_msg = retry !== 0 ? ` retrying in ${retry}` : '';
          logging.warnFP(LOG_NAME, 'findKeysByFilter', profile, `Retry ${retry_msg}::${err.code}::${err.message}`);
          await setTimeout(retry);
          retry *= 2;
        }
      }

      const entities = [];
      let [results] = query_results;
      if (results) {
        for (const item of results as IEntity []) entities.push(item[this.ds.KEY]);
      }

      results = null;

      return entities;
    } catch (err) {
      if (retryError(err)) {
        logging.warnFP(LOG_NAME, 'findKeysByFilter', profile, `Retrying running key filter query for ${type}`, err);
        return this.findKeysByFilter(profile, type, filters, con);
      } else {
        logging.errorFP(LOG_NAME, 'findKeysByFilter', profile, `Error running key filter query for ${type}`, err);
        throw err;
      }
    }
  }

  /**
   * Find a range of items that match attributeValue
   * @param profile - Google profile to search within
   * @param type - Type of entity to find
   * @param attributeName - Attribute to range search
   * @param attributeValue - Value to ranch search with
   * @param gr - true for greater than val, false for less than
   */
  public async findRange(profile: Uid, type: EntityType | GlobalType, name: string, val: any, gr: boolean, fields: string[] = null): Promise<IEntity[]> {
    try {
      const type_name = type.split('.').pop();
      const map_type = type.indexOf('.') !== -1;

      let q = profile ? this.ds.createQuery(profile, type_name) : this.ds.createQuery(type_name);
      q = q.filter(new PropertyFilter(name, gr ? '>' : '<', val));

      if (fields) {
        if (!fields.includes('id')) fields.push('id');
        q = q.select(fields).order(name);
      }

      try {
        const found = [];

        let retry = 100;
        let query_results = null
        while (!query_results)  {
          try { query_results = await this.ds.runQuery(q); } 
          catch(err) {
            // Retry for these codes (Added 14 with #840)
            if (!retryError(err) || retry > 30000) {
              logging.errorFP(LOG_NAME, 'findRange', profile, 'Error running range query', err);
              throw err;
            }

            const retry_msg = retry !== 0 ? ` retrying in ${retry}` : '';
            logging.warnFP(LOG_NAME, 'findRange', profile, `Retry ${retry_msg}::${err.code}::${err.message}`);
            await setTimeout(retry);
            retry *= 2;
          }
        }

        const entities: IEntity[] = [];
        const [results] = query_results;
        if (results) {
          for (let entity of results as IEntity[]) {
            if (entity.id && !found.includes(entity.id)) {
              const type_name_sc = _.startCase(_.toLower(type_name));
              if(type_name_sc === 'Group') {
                entity = new Group(entity as Partial<Group>);
              } else if (map_type) {
                if(type_name_sc === 'User') {
                  entity = new User(entity as Partial<User>);
                } else entity = new ItemTypes[type_name_sc](entity);
              } else {
                entity = new globalTypes[type_name_sc](entity);
              }

              entities.push(entity as IEntity);
              found.push(entity.id);
            }
          }
        }
        return entities;
      } catch (err) {
        logging.errorF(LOG_NAME, 'findRange', `Error running range query for ${type_name}.${name}`, err);
        if (retryError(err)) {
          return this.findRange(profile, type, name, val, gr);
        }
      }
    } catch (err) {
      logging.errorF(LOG_NAME, 'findRange', `Error running range query for ${name}`, err);
      return [];
    }
  }


  public async findIds(profile: Uid, type: EntityType | GlobalType, name: string = null, vals: any[] = null): Promise<Uid[]> {
    let query = this.ds.createQuery(profile, type.split('.').pop()).select('id');

    if (name && name.length && vals && vals.length) {
      for (const val of vals) {
        query = query.filter(new PropertyFilter(name, '=', val))
      }
    }

    try {
      let retry = 100;
      let query_results = null
      while (!query_results)  {
        try { query_results = await this.ds.runQuery(query); }
        catch(err) {
          if (!retryError(err) || retry > 30000) {
            logging.errorFP(LOG_NAME, 'findIds', profile, 'Error running id query', err);
            throw err;
          }

          const retry_msg = retry !== 0 ? ` retrying in ${retry}` : '';
          logging.warnFP(LOG_NAME, 'findIds', profile, `Retrying id query ${retry_msg}::${err.code}::${err.message}`);
          await setTimeout(retry);
          retry *= 2;
        }
      }

      const entities = [];
      const [results] = query_results;
      if (results) {
        for (const item of results as IEntity[]) entities.push(item.id);
      }
      return entities;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'findIds', profile, 'Error running id query', err);
      if (retryError(err)) {
        return this.findIds(profile, type);
      }
    }
  }

  public reload() {
    if (config.isEnvOffline()) {
      (this.ds as OfflineDatastore).reload();
    }
  }

  public async remove(profile: Uid, data: Partial<IEntity>): Promise<void> {
    return this.delete(profile, data.type, [this.key(profile, data)]);
  }

  public async removeAll(profile: Uid, entities: IEntity[]): Promise<Set<EntityType>> {
    const types = new Set<EntityType>();

    if (entities && entities.length) {
      const del_entities = _.uniqBy(entities, i => `${i.type}_${i.id}`); // .slice()
      while (del_entities.length) {
        const entities_splice = del_entities.splice(0, 500);
        const dsKeys: DatastoreKey[] = [];
        // TODO(juniorfoo) - Figure out a better way to do this
        for (const entity of entities_splice) {
          types.add(entity.type as EntityType);
          dsKeys.push(this.key(profile, entity));
        }

        // let transaction;
        try {
          /*if (dsKeys.length > 5) {
            transaction = this.ds.transaction();*/
          let results = null;
          let retry = 100;
          while (!results)  {
            ///await transaction.delete(dsKeys);
            //results = await transaction.commit().catch(async err => {
            try {
              results = await this.ds.delete(dsKeys);
            } catch(err) {
              // Retry for these codes (Added 14 with #840)
              if (!retryError(err) || retry > 30000) {
                logging.errorFP(LOG_NAME, 'removeAll', profile, 'Error committing transaction while removing from DS', err);
                throw err;
              }

              const retry_msg = retry !== 0 ? ` retrying in ${retry}` : '';
              logging.warnFP(LOG_NAME, 'removeAll', profile, `Issue removing transaction from datastore ${retry_msg}::${err.code}::${err.message}`);
              await setTimeout(retry);
              retry *= 2;
              // transaction = this.ds.transaction();
            }
          }
        } catch (err) {
          logging.errorFP(LOG_NAME, 'removeAll', profile, 'Error deleting entities from the datastore', err);
          /*if (transaction) {
            transaction.rollback().catch(err => logging.errorFP(LOG_NAME, 'removeAll', profile, 'Error rolling back transaction while deleting from DS', err));
          }*/
          throw err;
        }
      }
    }

    return types;
  }

  public async save(profile: Uid, type: EntityType | GlobalType, save_item: DatastoreEntry): Promise<void> {
    if (!type) {
      logging.errorFP(LOG_NAME, 'save', profile, `Attempt to entity without type - ${util.format(save_item)}`, null);
      throw new Error(`Missing type for save of entity for ${profile}`);
    }

    if (config.isEnvDevelopment()) {
      switch (type) {
        case EntityType.Person:
        case EntityType.User:
          if (save_item.key.name === undefined) {
            logging.errorF(LOG_NAME, 'save', `ERROR: Aborting save to store - attempting save of a '${type}' record without a key ${save_item}`, new Error());
            return;
          }
          if (save_item.data['id'].startsWith('g-')) {
            logging.errorF(LOG_NAME, 'save', `ERROR: Aborting save to store - attempting save of a guest user '${save_item.data['id']}'`, new globalTypes.InternalError(500, 'Cannot save guest user', save_item.data));
            return;
          }
          break;
      }
    }

    let saved = false;
    let retry = 100;
    while (!saved) {
      try {
        await this.ds.save(save_item);
        saved = true;
      } catch (err) {
        // Retry for these codes (Added 14 with #840)
        if (!retryError(err) || retry > 30000) {
          logging.errorFP(LOG_NAME, 'save', profile, `Error saving ${JSON.stringify(save_item)}`, err);
          const id_att = save_item.data['id'];
          const id = id_att && id_att.value ? id_att.value : id_att;
          throw new globalTypes.InternalError(err.code, err.message, {type, id}); //`Error ${err.code}: ${err.message} saving ${JSON.stringify(save_item)}`);
        }
        const retry_msg = retry !== 0 ? ` retrying in ${retry}` : '';
        logging.warnFP(LOG_NAME, 'save', profile, `Issue saving ${type} to datastore${retry_msg}::${err.code}::${err.message}`);
        await setTimeout(retry);
        retry *= 2;

        // code: 3
        // details: "The value of property "photos" is longer than 1500 bytes."
        // metadata: Metadata {_internal_repr: {…}}
        // message: "3 INVALID_ARGUMENT: The value of property "photos" is longer than 1500 bytes."
        // stack: "Error: 3 INVALID_ARGUMENT: The value of property "photos" is longer than 1500 bytes.↵    at Object.exports.createStatusError
        // (/Users/<USER>/Projects/askfora/fora/node_modules/grpc/src/common.js:87:15)↵    at Object.onReceiveStatus
        // (/Users/<USER>/Projects/askfora/fora/node_modules/grpc/src/client_interceptors.js:1188:28)↵    at InterceptingListener._callNext
        // (/Users/<USER>/Projects/askfora/fora/node_modules/grpc/src/client_interceptors.js:564:42)↵    at InterceptingListener.onReceiveStatus
        // (/Users/<USER>/Projects/askfora/fora/node_modules/grpc/src/client_interceptors.js:614:8)↵    at callback
        // (/Users/<USER>/Projects/askfora/fora/node_modules/grpc/src/client_interceptors.js:841:24)" __proto__: Object
      }
    }
  }

  public async bulkSave(profile: Uid, save_items: DatastoreEntry[]): Promise<void> {
    if (save_items) {
      const update_tids = [];
      const delay_updates: DatastoreEntry[] = [];
      const save_serialized = save_items.slice();
      while(save_serialized.length) {
        const save_splice = save_serialized.splice(0,500);
        const do_save: DatastoreEntry[] = [];

        for (const ss of save_splice) {
          if (ss.data && ss.data.id && update_tids.includes(ss.data.id)) {
            delay_updates.push(ss);
          } else {
            do_save.push(ss);
            if (ss.data && ss.data.id) update_tids.push(ss.data.id);
          }
        }

        try {
          await this.ds.save(do_save);
        } catch (err) {
          logging.errorFP(LOG_NAME, 'saveAll', profile, `Error bulk saving entities to the datastore`, err);
          // try saving one at a time
          for (const entity of do_save) {
            try {
              await this.ds.save(entity);
            } catch(e2) {
              if (entity.key && entity.key.kind) logging.errorFP(LOG_NAME, 'saveAll', profile, `Error saving entity ${entity.key.kind} ${entity.key.name} to the datastore`, e2);
              else logging.errorFP(LOG_NAME, 'saveAll', profile, `Error saving unknown entity ${JSON.stringify(entity)} to the datastore`, e2);
            }
          }
        }
      }

      if (delay_updates.length) return this.bulkSave(profile, delay_updates);
    }
  }

  public async saveAll(profile: Uid, entities: IEntity[], force_profile = false): Promise<Set<EntityType>> {
    const types = new Set<EntityType>();

    // we can't update the same entity twice in one transaction so if we run into that, we arbitrarily delay one and log it
    const delay_updates: IEntity[] = [];
    const update_tids = [];

    if (entities) {
      const save_entities = entities.slice();
      while (save_entities.length) {
        const entities_splice = save_entities.splice(0, 500);
        const entities_save: DatastoreEntry[] = [];

        for (const entity of entities_splice) {
          types.add(entity.type as EntityType);

          const tid = `${entity.schema.name}_${entity.id}`;
          if (update_tids.includes(tid)) {
            // logging.warnFP(LOG_NAME, 'saveAll', profile, `Saving duplicate updates ${tid}`);
            delay_updates.push(entity);
            continue;
          }

          const entity_serialized = this.serialize(force_profile || Object.values(EntityType).includes(entity.type as EntityType) ? profile : null, entity, entity.nonIndexedFields);
          entities_save.push(entity_serialized);
          update_tids.push(tid);
        }

        if (config.isEnvOffline()) {
          if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'saveAll', profile, `Saving ${entities_save.length} entities to offline`);
          try {
            await this.ds.save(entities_save);
          } catch (err) {
            logging.errorFP(LOG_NAME, 'saveAll', profile, 'Error committing transaction while saving to DS', err);
            throw err;
          }
        } else {
          // let transaction = this.ds.transaction();
          try {
            if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'saveAll', profile, `Calling DS.save on ${entities_save.length} entities`);
            /* if (entities_save.length > 5) {
              let results = null;
              let retry = 100;
              while (!results)  {
                transaction.save(entities_save);
                results = await transaction.commit().catch(async err => {
                  // Retry for these codes (Added 14 with #840)
                  if (!retryError(err) || retry > 30000) {
                    logging.errorFP(LOG_NAME, 'saveAll', profile, 'Error committing transaction while saving to DS', err);
                    throw err;
                  }

                  const retry_msg = retry !== 0 ? ` retrying in ${retry}` : '';
                  logging.warnFP(LOG_NAME, 'saveAll', profile, `Issue saving transaction to datastore ${retry_msg}::${err.code}::${err.message}`);
                  await setTimeout(retry);
                  retry *= 2;
                  transaction = this.ds.transaction();
                });
              }
              if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'saveAll', profile, `Transaction commit save ${entities_save.length} records ${results && results.length ? results[0].mutationResults.length : ''}`);
            } else {*/
              // transaction = null;
            while(entities_save.length) await this.ds.save(entities_save.splice(0,100));
            // }
          } catch (err) {
            logging.errorFP(LOG_NAME, 'saveAll', profile, `Error saving entities of types ${JSON.stringify(Array.from(types))} to the datastore`, err);
            /* if (transaction) {
              transaction.rollback().catch(err => {
                logging.errorFP(LOG_NAME, 'saveAll', profile, 'Error rolling back transaction while saving to DS', err);
              });
            }*/
            // try saving one at a time
            for (const entity of entities_save) {
              try {
                await this.ds.save(entity);
              } catch(e2) {
                if (entity.key && entity.key.kind) logging.errorFP(LOG_NAME, 'saveAll', profile, `Error saving entity ${entity.key.kind} ${entity.key.name} to the datastore`, e2);
                else logging.errorFP(LOG_NAME, 'saveAll', profile, `Error saving unknown entity ${JSON.stringify(entity)} to the datastore`, e2);
              }
            }
            // throw err;
          }
        }
      }
    }

    // write the duplicated updates
    if (delay_updates.length) await this.saveAll(profile, delay_updates);

    return types;
  }


  private serialize(profile: Uid, item: IEntity, no_index: string[] = []): DatastoreEntry {
    const key = this.key(profile, item);
    const data = {};
    const excludeFromIndexes = [];

    for (const field of item.schema.fields) {
      /* const att_val = {
        name: field.name,
        excludeFromIndexes: false,
        value: undefined,
      }; */
      const ftype = Array.isArray(field.type) ? field.type.find(t => t !== "null") : field.type;

      if (no_index && no_index.includes(field.name) && !excludeFromIndexes.includes(field.name)) {
        // att_val.excludeFromIndexes = true;

        if (field.isArray) {
          if(ftype === 'bytes') excludeFromIndexes.push(`${field.name}[].*`);
          else excludeFromIndexes.push(`${field.name}[]`);
        } else excludeFromIndexes.push(field.name);

        //find embedded fields
        const embedded_no_index = no_index.filter(n => n.startsWith(`${field.name}.`));
        for(const eni of embedded_no_index) {
          if(!excludeFromIndexes.includes(eni)) excludeFromIndexes.push(eni);
        }
      }

      let value = item[field.name];
      if (value === undefined) value = null;
      switch (ftype) {
        case 'string':
          // if (value !== null) value = utf8.encode(value);
          break;
        case 'long':
          if (!field.logicalType || !['date', 'timestamp-millis'].includes(field.logicalType)) {
            if (value !== null) value = parseInt(value, 10);
            if (isNaN(value)) value = null;
            break;
          }
        case 'date':
          if (value !== null) {
            if (field.isArray) {
              if (!Array.isArray(value)) value = [new Date(value)];
              else value = value.map(v => new Date(v));
            } else value = new Date(value);
          }
          break;

        case 'float':
          if (value !== null) value = parseFloat(value);
          if (isNaN(value)) value = null;
          break;
        case 'bytestring':
          if (value !== null && value.length) value = Buffer.from(value);
          else value = null;
          break;
        case 'bytes':
          if (value !== null) {
            // Everything that is Avro bytes, can just be JSON in GDS

            // if (Array.isArray(value) && value.length === 0) value = null;

            if (!no_index.includes(field.name) && !excludeFromIndexes.includes(field.name) && JSON.stringify(value).length > 1500) {
              if (field.isArray) excludeFromIndexes.push(`${field.name}[]`);
              else excludeFromIndexes.push(field.name);
              // att_val.excludeFromIndexes = true;
            }

            if (field.isArray) {
              if (!Array.isArray(value)) value = [value];
              value = value.map(v => {
                switch(typeof v) {
                  case 'string': 
                    //try { return utf8.encode(v); }
                    //catch(err) { logging.warnFP(LOG_NAME, 'serialize', `Error encoding ${field.name} => ${v}`, err) }
                    return v;
                  case 'object': {
                    const enc_value = {};
                    for (const key in v) {
                      if (typeof v[key] === 'string') {
                        //try { enc_value[key] = utf8.encode(v[key]); }
                        //catch(err) {
                        //  logging.warnFP(LOG_NAME, 'serialize', `Error encoding ${field.name}.${key} => ${v[key]}`, err);
                          enc_value[key] = v[key];
                        //}
                      }
                      else enc_value[key] = v[key];
                    }
                    return enc_value;
                  }
                  default: return v;
                }
              });
            } else if (Array.isArray(value)) {
              value = value.map(v => {
                //try { return utf8.encode(v); }
                //catch(err) { logging.warnFP(LOG_NAME, 'serialize', `Error encoding ${field.name} => ${v}`, err) }
                return v;
              });
            } else if(typeof value == 'object') {
              const enc_value = {};
              for (const key in value) {
                if (typeof value[key] === 'string') {
                  //try { enc_value[key] = utf8.encode(value[key]); }
                  //catch(err) { 
                  //  logging.warnFP(LOG_NAME, 'serialize', `Error encoding ${field.name}.${key} => ${value[key]}`, err); 
                    enc_value[key] = value[key];
                  //}
                }
                else enc_value[key] = value[key];
              }
              value = enc_value;
            }
          }

          break;
      }

      if (value !== null ) {
        data[field.name] = value;
        // att_val.value = value;
        // save_item.push(att_val);
      }
    }

    return { key, excludeFromIndexes, data };
  }
}
