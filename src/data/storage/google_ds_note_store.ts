/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import ForaUser from '../../session/user';
import { Note } from '../../types/items';
import { EntityType, Uid } from '../../types/shared';
import * as functions from '../../utils/funcs';
import { ICachePlugin } from '../caching/i_cache_plugin';
import data from '../index';
import { INoteStore } from './i_note_store';

// const LOG_NAME = 'data.storage.GoogleDataStoreNoteStore';

export class GoogleDataStoreNoteStore implements INoteStore {
  constructor(private readonly cache: ICachePlugin) {}

  async clearNotes(user: ForaUser, account: Uid, destroy_containers?: boolean): Promise<number> {
    // destroy_containers is a NO-OP because we did not create any containers in this implementation
    const notes = await data.internals.find(user.profile, EntityType.Note, null);
    await data.internals.removeAll(user.profile, notes);
    for (const note of notes) await this.cache.clearCacheAttVal(user.profile, EntityType.Note, 'note', note.id);

    return notes ? notes.length : 0;
  }

  async deleteNote(user: ForaUser, account: Uid, note: Note): Promise<void> {
    await data.internals.remove(user.profile, note);
    await this.cache.clearCacheAttVal(user.profile, EntityType.Note, 'note', note.file_id);
  }

  async getNote(user: ForaUser, account: Uid, file_id: string): Promise<Note> {
    const cached_file = await this.cache.lookupCacheAttVal(user.profile, EntityType.Note, 'note', file_id);
    if (cached_file) return new Note(cached_file);

    const results: Note[] = (await data.internals.find(user.profile, EntityType.Note, file_id)) as Note[];
    if (results && results.length) {
      await this.cache.cacheAttVal(user.profile, EntityType.Note, 'note', file_id, results[0]);
      return results[0];
    }

    return null;
  }

  async loadNotes(user: ForaUser, account: Uid): Promise<Note[]> {
    const notes = await (data.internals.find(user.profile, EntityType.Note, null) as Promise<Note[]>);
    for (const note of notes) await this.cache.cacheAttVal(user.profile, EntityType.Note, 'note', note.file_id, note);

    return notes;
  }

  async newNote(user: ForaUser, account: Uid, note: Note): Promise<Note> {
    GoogleDataStoreNoteStore.ensureIds(note, account);
    await data.internals.add(user.profile, note, note.nonIndexedFields);
    await this.cache.cacheAttVal(user.profile, EntityType.Note, 'note', note.file_id, note);
    return note;
  }

  async saveNote(user: ForaUser, account: Uid, note: Note): Promise<void> {
    GoogleDataStoreNoteStore.ensureIds(note, account);
    await data.internals.add(user.profile, note, note.nonIndexedFields);
    await this.cache.cacheAttVal(user.profile, EntityType.Note, 'note', note.file_id, note);
  }

  private static ensureIds(note: Note, account: Uid): void {
    if (!note.id) note.id = note.file_id = functions.hash(JSON.stringify(note));
    if (!note.file_id) note.file_id = note.id;
  }
}
