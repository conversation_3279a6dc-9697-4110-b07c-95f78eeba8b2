import fs from 'fs';
import path from 'path';
import ForaUser from '../../session/user';
import { InternalError } from '../../types/globals';
import { Document, Note, Person } from '../../types/items';
import { Uid } from '../../types/shared';
import * as format from '../../utils/format';
import * as funcs from '../../utils/funcs';
import { FileFilter, IFileStore } from './i_file_store';
import { INoteStore } from './i_note_store';

const CACHE_PATH = '../../../cache/';

export default class OfflineDocStore implements IFileStore, INoteStore {
  async clearFiles(user: ForaUser, account: Uid, filter: FileFilter, destroy_containers?: boolean): Promise<number> {
    return OfflineDocStore.clearDocs(user);
  }

  clearNotes(user: ForaUser, account: Uid, destroy_containers?: boolean): Promise<number> {
    return OfflineDocStore.clearDocs(user);
  }

  deleteFile(user: ForaUser, account: Uid, doc: Partial<Document>): Promise<void> {
    return OfflineDocStore.deleteDoc(user, doc.file_id);
  }

  deleteNote(user: ForaUser, account: Uid, note: Note): Promise<void> {
    return OfflineDocStore.deleteDoc(user, note.file_id);
  }

  getFile(user: ForaUser, account: Uid, file_id: string): Promise<Document> {
    return OfflineDocStore.getDoc(user, file_id);
  }

  async getNote(user: ForaUser, account: Uid, note_id: string): Promise<Note> {
    const doc = await OfflineDocStore.getDoc(user, note_id);
    return OfflineDocStore.convertDocToNote(doc);
  }

  loadFiles(user: ForaUser, account: Uid, filter: FileFilter): Promise<Document[]> {
    return OfflineDocStore.loadDocs(user);
  }

  async loadNotes(user: ForaUser, account: Uid): Promise<Note[]> {
    const notes: Note[] = [];
    const docs = await OfflineDocStore.loadDocs(user);
    for (const doc of docs) notes.push(OfflineDocStore.convertDocToNote(doc));

    return notes;
  }

  newFile(user: ForaUser, account: Uid, doc: Document): Promise<Document> {
    return OfflineDocStore.newDoc(user, doc);
  }

  streamFile(user: ForaUser, account: Uid, doc: Document): Promise<Document> {
    throw new InternalError(500, 'Stream file to Offline Doc Store is not supported');
  }

  async newNote(user: ForaUser, account: Uid, note: Note): Promise<Note> {
    const doc_in = OfflineDocStore.convertNoteToDoc(note);
    const doc_out = await OfflineDocStore.newDoc(user, doc_in);
    return OfflineDocStore.convertDocToNote(doc_out);
  }

  saveFile(user: ForaUser, account: Uid, doc: Document): Promise<void> {
    return OfflineDocStore.saveDoc(user, doc);
  }

  saveNote(user: ForaUser, account: Uid, note: Note): Promise<void> {
    const doc_in = OfflineDocStore.convertNoteToDoc(note);
    return OfflineDocStore.saveDoc(user, doc_in);
  }

  private static async clearDocs(user: ForaUser): Promise<number> {
    let count = 0;
    const doc_path = path.resolve(__dirname, CACHE_PATH, user.profile, 'Document');
    if (fs.existsSync(doc_path)) {
      const docs = fs.readdirSync(doc_path);
      for (const doc of docs) {
        fs.unlinkSync(path.resolve(doc_path, doc));
        count += 1;
      }
    }

    return count;
  }

  private static convertDocToNote(new_doc: Document) {
    const body = new_doc.body ? format.stringFromBody(new_doc.body) : '';

    const note = new Note({
      id: new_doc.id,
      file_id: new_doc.file_id,
      created: new_doc.created,
      notes: body.split('\n'),
    });

    const people: { [key: string]: Partial<Person> } = {};

    if (new_doc.props) {
      for (const index in new_doc.props) {
        const id = index.slice(1);
        const value = new_doc.props[index];
        switch (index[0]) {
          case 'p':
            if (!people[id]) people[id] = { id: value };
            else people[id].id = value;
            break;
          case 'd':
            if (!people[id]) people[id] = { displayName: value };
            else people[id].displayName = value;
            break;
        }
      }
    }

    note.people = Object.values(people);

    return note;
  }

  private static convertNoteToDoc(note: Note) {
    const props = { id: note.id, type: 'note' };

    if (note.people) {
      for (const index in note.people) {
        const person = note.people[index];
        if (person.id) {
          props[`p${index}`] = person.id;
          props[`d${index}`] = person.displayName;
        }
      }
    }

    return new Document({
      id: note.id,
      body: note.notes.join('\n'),
      mime: 'text/plain',
      props,
    });
  }

  private static async deleteDoc(user: ForaUser, file_id): Promise<void> {
    const doc_path = path.resolve(__dirname, CACHE_PATH, user.profile, 'Document', file_id);
    if (fs.existsSync(doc_path)) {
      fs.unlinkSync(doc_path);
    }
    return;
  }

  private static async getDoc(user: ForaUser, file_id: string): Promise<Document> {
    const doc_path = path.resolve(__dirname, CACHE_PATH, user.profile, 'Document', file_id);
    if (fs.existsSync(doc_path)) {
      const store_file = fs.readFileSync(doc_path);
      return funcs.uncompress(store_file) as Document;
    }

    return null;
  }

  private static async loadDocs(user: ForaUser): Promise<Document[]> {
    const docs: Document[] = [];

    const doc_path = path.resolve(__dirname, CACHE_PATH, user.profile, 'Document');
    if (fs.existsSync(doc_path)) {
      const doc_ids = fs.readdirSync(doc_path);
      for (const doc_id of doc_ids) {
        const store_file = fs.readFileSync(path.resolve(doc_path, doc_id));
        const doc: Document = funcs.uncompress(store_file) as Document;
        docs.push(doc);
      }
    }

    return docs;
  }

  private static async newDoc(user: ForaUser, doc: Document): Promise<Document> {
    if (user.profile.startsWith('g-')) throw new InternalError(500, `Invalid id ${user.profile}`);
    const doc_path = path.resolve(__dirname, CACHE_PATH, user.profile, 'Document');
    if (!fs.existsSync(doc_path)) fs.mkdirSync(doc_path, 0o744);

    if (!doc.id) doc.id = funcs.hash(JSON.stringify(doc));
    doc.file_id = path.resolve(doc_path, doc.id);

    const store_file = funcs.compress(doc);
    fs.writeFileSync(doc.file_id, store_file);

    return doc;
  }

  private static async saveDoc(user: ForaUser, doc: Document): Promise<void> {
    if (user.profile.startsWith('g-')) throw new InternalError(500, `Invalid id ${user.profile}`);
    const doc_path = path.resolve(__dirname, CACHE_PATH, user.profile, 'Document');
    if (!fs.existsSync(doc_path)) fs.mkdirSync(doc_path, 0o744);

    const store_file = funcs.compress(doc);
    fs.writeFileSync(doc.file_id, store_file);

    return;
  }
}
