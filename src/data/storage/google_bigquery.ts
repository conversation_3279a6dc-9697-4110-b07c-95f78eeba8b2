/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { BigQuery, Query, SimpleQueryRowsResponse } from '@google-cloud/bigquery';
import { Datastore } from '@google-cloud/datastore';
import { Storage } from '@google-cloud/storage';
import axios, { AxiosRequestConfig } from 'axios';
import fs from 'fs';
import { JWT } from 'google-auth-library';
import { datastore_v1, google } from 'googleapis';
import _ from 'lodash';
import { kmeans } from 'ml-kmeans';
import { format } from 'sql-formatter';
import { setTimeout } from "timers/promises";
import util from 'util';
import { cosineSimilarity } from 'vector-cosine-similarity';

import config from '../../config';
import { doExport } from '../../routes/update';

import ForaUser from '../../session/user';
import { GoogleCloudStorageHelper } from '../../sources/helpers/google/google_cloud_storage_helper';

import { Category, Course, ExtendedCategory, InternalError, ScoreStat, Skill, SkillCategory, Vanity } from '../../types/globals';
import { GlobalType, Person } from '../../types/items';
import { JobTags, SkillStat, Stats, TagType, Uid } from '../../types/shared';
import { FORA_PROFILE, User } from '../../types/user';

import { flatten, formatStack, hash, mergeTags } from '../../utils/funcs';
import logging from '../../utils/logging';
import parsers from '../../utils/parsers';
import peopleUtils from '../../utils/people';

import { internalUpdate } from '../../routes/update';
import { skillVector } from '../../skills/model';
import { retryError } from '../../sources/helpers/google/google_retry_helper';

import { ICachePlugin } from '../caching/i_cache_plugin';
import data from '../index';
import OfflineDatastore from './offline_datastore';
import OfflineQuery from './offline_query';

const dataStoreAdmin = google.datastore('v1');

const DEBUG = (require('debug') as any)('fora:data:storage:bigquery');
const LOG_NAME = 'data.storage.GoogleBigQuery';

const IGNORE_GLOBAL_TAGS = [TagType.import_id, TagType.import_type, TagType.import_group];

const TEMP_TABLES = ['lookup_comm_', 'lookup_name_org_', 'temp_global_people_', 'update_global_people_', 'temp_people_']

const COSINE_THRESHOLD = 0.75;

const LOOKUP_COMM_METADATA = {
  sourceFormat: 'NEWLINE_DELIMITED_JSON',
  schema: { fields: [
    { name: 'comm', type: 'string' },
  ]},
  writeDisposition: 'WRITE_TRUNCATE',
  location: 'US',
}

const LOOKUP_NAME_ORG_METADATA = {
  sourceFormat: 'NEWLINE_DELIMITED_JSON',
  schema: { fields: [
    { name: 'name', type: 'string' },
    { name: 'org', type: 'string' },
  ]},
  writeDisposition: 'WRITE_TRUNCATE',
  location: 'US',
}

interface PersonStat extends Person {
  // score_cnt: number,
  // score_sum: number,
  // score_max: number,
  // score_min: number,
  score_avg: number,
  score_stddev: number,
}

interface CategoryStat extends SkillCategory {
  // score_cnt: number,
  // score_sum: number,
  // score_max: number,
  // score_min: number,
  score_avg: number,
  score_stddev: number,
}

export default class GoogleBigQuery {
  private readonly big_query: BigQuery = undefined;
  private readonly storage: Storage = undefined;
  private readonly cache: ICachePlugin = undefined;

  constructor(datastore: Datastore, cache: ICachePlugin) {
    this.checkGroup = this.checkGroup.bind(this);

    if (config.isEnvOffline()) {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'constructor', 'Using offline query');
      this.big_query = new OfflineQuery(datastore as OfflineDatastore) as unknown as BigQuery;
    } else {
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'constructor', 'Using BigQuery');
      this.big_query = new BigQuery();
      this.storage = new Storage();

      this.big_query.interceptors.push({
        request: function(reqOpts) {
          reqOpts.forever = false
          return reqOpts as any;
        }
      });

      this.storage.interceptors.push({
        request: function(reqOpts) {
          reqOpts.forever = false
          return reqOpts as any;
        }
      });

    }
    this.cache = cache;
  }

  get bq(): BigQuery {
    return this.big_query;
    /*if (config.isEnvOffline()) return this.big_query;
    return {
      new BigQuery();
    }*/
  }

  async startOp(profile: Uid) {
    let op_in_progress = true;
    while (op_in_progress) {
      const res = this.cache ? await this.cache.get(`${LOG_NAME}_${profile}`) : false;
      op_in_progress = res ? res.value : false;
      if (op_in_progress) await setTimeout(1000);
    }
    if (this.cache) await this.cache.set(`${LOG_NAME}_${profile}`, 'true', {expires:10 * 60});
  }

  async endOp(profile: Uid) {
    if (this.cache) await this.cache.delete(`${LOG_NAME}_${profile}`);
  }

  safeProfile(p) {
    return p.replace(/[-. ;&'"`]/g, '_');
  }

  async checkSchema(profile: Uid = null, temp = false) {
    if (!config.isEnvOffline()) {
      let table = 'people.people_*';
      if (profile) {
        if (temp) table = `people.temp_people_${this.safeProfile(profile)}`;
        else table = `people.people_${this.safeProfile(profile)}`;
      }

      // check if tables exists first
      let query = `SELECT count(*) FROM \`${table}\`;`;
      for (let attempts = 0; attempts < 5; attempts++) {
        try {
          const results = await this.bq.createJob({configuration:{query:{ query, useLegacySql: false }}});
          const job = results[0];
          const [rows] = await job.getQueryResults(job);
          if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'checkSchema', profile, JSON.stringify(rows));
          break;
        } catch (err) {
          if (!retryError(err)) {
            logging.warnFP(LOG_NAME, 'checkSchema', profile, `no table ${query}`, err);
            return;
          }
        }
      }

      let attempts = 0;
      while(attempts < 5) {
        try {
          query = `SELECT MAX(tag.start), MAX(tag.index) FROM \`${table}\`, UNNEST(tags) AS tag;`;
          const results = await this.bq.createJob({configuration:{query:{ query, useLegacySql: false }}});
          const job = results[0];
          const [rows] = await job.getQueryResults(job);
          if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'checkSchema', profile, JSON.stringify(rows));
          return;
        } catch (err) {
          if (err.code === 404 || retryError(err)) {
            logging.warnFP(LOG_NAME, 'checkSchema', profile, `retrying ${query}`, err);
            await setTimeout(Math.pow(5, attempts) * 100);
            attempts++;
            continue;
          }

          logging.errorFP(LOG_NAME, 'checkSchema', profile, `failed ${query}`, err);
          if (err.code !== 404 && !retryError(err)) {
            try {
              query = `SELECT id FROM \`${table}\`, UNNEST(tags) AS tag WHERE tag.start.provided = 'string'`;
              const results = await this.bq.createJob({configuration:{query:{ query, useLegacySql: false }}});
              const job = results[0];
              const [rows] = await job.getQueryResults(job);
              logging.warnFP(LOG_NAME, 'checkSchema', profile, JSON.stringify(rows), err);
            } catch(e2) { 
              logging.warnFP(LOG_NAME, 'checkSchema', profile, 'cannot id bad rows', e2);
            }
          }
          throw err;
        }
      }

      throw new InternalError(404, `schema check for ${profile} ${temp ? 'temp' : ''}failed after ${attempts} attempts`);
    }
  }

  async cleanup(dryrun = true) {
    for (const prefix of TEMP_TABLES) {
      const query = `select distinct(_TABLE_SUFFIX) profile from \`people.${prefix}*\``;
      logging.infoF(LOG_NAME, 'cleanup', query);
      try {
        const [results] = await this.bq.query({query, useLegacySql: false});
        if (results) {
          for (const suffix of results) {
            logging.infoF(LOG_NAME, 'cleanup', `Deleting people.${prefix}${suffix.profile}`);
            if (!dryrun) await this.bq.dataset('people').table(`${prefix}${suffix.profile}`).delete();
          }
        }
      } catch (e) {
        if (!e.message || !e.message.includes('does not match')) logging.warnF(LOG_NAME, 'cleanup', `Error cleaning up people.${prefix}`, e);
      }
    }
  }

  /**
   * Delete the given `Person` from the global `bigquery` table
   *
   * @param profile - User profile we are running for
   * @param person - Person to delete
   */
  async deletePerson(profile: Uid, person: Partial<Person>): Promise<void> {
    await this.startOp(profile);

    if (profile && person.id) {
      const query = `DELETE FROM people.people_${this.safeProfile(profile)} WHERE id = @id`;

      const params = { id: person.id};

      if (logging.isDebug(profile)) {
        DEBUG('deletePerson: %s QP = %j', profile, params);
        DEBUG('deletePerson: %s SQL = %s', profile, format(query, { language: "bigquery" }));
      }

      await this.bq
        .query({ query, useLegacySql: false, params} as Query)
        .then(() => logging.infoFP(LOG_NAME, 'deletePerson', profile, `Person deleted - ${person.id}`))
        .catch(err => logging.errorFP(LOG_NAME, 'deletePerson', profile, `Error deleting person - ${util.format(person).replace(/\n/g, ' ')}`, err));

    } 

    await this.endOp(profile);
  }
  
  async dropImportTable(profile: string, table_name: string) {
    const results = await this.bq.createJob({configuration: {query:{ query: `DROP TABLE people.${this.safeProfile(table_name)}`, useLegacySql: false }}}).catch(err => {
      if (err.code === 404) logging.warnFP(LOG_NAME, 'dropImportTable', profile, `Issue deleting bigquery table ${table_name}`, err);
      else {
        logging.errorFP(LOG_NAME, 'dropImportTable', profile, `error dropping people table ${table_name}`, err);
        this.endOp(profile).then(() => { throw err });
      }
    });

    if (results && results.length) {
      const job = results[0];
      await job.getQueryResults(job);
    }
  }

  async dropTable(profile: string, temp = false): Promise<void> {
    if (!temp) await this.startOp(profile);

    const tableId = profile ? `people.${temp ? 'temp_' : ''}people_${this.safeProfile(profile)}` : 'people.temp_people_global_users';
    logging.infoFP(LOG_NAME, 'dropTable', profile, `dropping ${tableId}`);
    const results = await this.bq.createJob({configuration: {query:{ query: `DROP TABLE ${tableId}`, useLegacySql: false }}}).catch(err => {
      if (err.code === 404) logging.warnFP(LOG_NAME, 'dropTable', profile, `Issue deleting bigquery tables`, err);
      else {
        logging.errorFP(LOG_NAME, 'dropTable', profile, 'error dropping people table', err);
        this.endOp(profile).then(() => { throw err });
      }
    });

    if (results && results.length) {
      const job = results[0];
      await job.getQueryResults(job);
    }

    if (!temp) await this.endOp(profile);
  }

  async getNamesAndTags(user: User, tag_types: TagType[] = null, network = true): Promise<Partial<Person>[]> {
    const table = `people.people_${this.safeProfile(user.profile)}`;
    const tags = tag_types && tag_types.length ? `left join (select id tags_id, array_agg(tag) org_tags from ${table}, UNNEST(tags) tag 
      where ${network ? '' : '(not network or network is null) and '} tag.type in unnest(@tag_types) and tag.index <> -1 group by id ) on id = tags_id` : ''
    const query = `select id, displayName, names, comms, org_tags tags from ${table} ${tags} 
      ${network ? '' : 'where (not network or network is null) order by id'}`

    const params = tag_types && tag_types.length ? {tag_types} : null;

    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getNamesAndTags', user.profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));

    let attempts = 0;
    while(attempts < 5) {
      try {
        const [results] = await this.bq.query({ query, params, useLegacySql: false });
        return this.peopleResults(user, results, false).map(pr => pr.person);
      } catch (e) {
        if (e.code === 404 || retryError(e)) {
          logging.warnFP(LOG_NAME, 'getNamesAndTags', user.profile, `retrying ${query}`, e);
          await setTimeout(Math.pow(5, attempts) * 100);
          attempts++;
          continue;
        }

        if (e.message.toLowerCase().includes('not found')) logging.warnFP(LOG_NAME, 'getNamesAndTags', user.profile, 'error getting names', e);
        else logging.errorFP(LOG_NAME, 'getNamesAndTags', user.profile, 'error getting names', e);
        throw e;
      }
    }
  }

  async lookupNames(user: User, user_comms: string[], names: string[], additional_ids?: Uid[]): Promise<string[]> {
    const profiles = [...additional_ids ? additional_ids : [], ...(await this.networkProfiles(user, user_comms))];

    if (!profiles.includes(user.profile)) profiles.push(user.profile);

    const params = { profiles, names };
    const profile_query = `select distinct(name) name from people.names, unnest(profiles) profile
      where name in unnest(@names) and profile in unnest(@profiles)`

    try {
      const names = await this.retryQuery<{name:string}>(user.profile, 'lookupNames', profile_query, params);
      return names ? names.map(({name}) => name) : [];
    } catch(err) {
      logging.errorFP(LOG_NAME, 'lookupNames', user.profile, `Error looking up matching profiles for names`, err);
      return [];
    }
  }

  async lookupOrgs(user: User, user_comms: string[], orgs: string[], additional_ids?: Uid[]): Promise<string[]> {
    const profiles = [...additional_ids ? additional_ids : [], ...(await this.networkProfiles(user, user_comms))];

    if (!profiles.includes(user.profile)) profiles.push(user.profile);

    const params = { profiles, orgs };
    const profile_query = `select distinct(org) org from people.orgs, unnest(profiles) profile
      where org in unnest(@orgs) and profile in unnest(@profiles)`

    try {
      const orgs = await this.retryQuery<{org:string}>(user.profile, 'lookupNames', profile_query, params);
      return orgs ? orgs.map(({org}) => org) : [];
    } catch(err) {
      logging.errorFP(LOG_NAME, 'lookupOrgs', user.profile, `Error looking up matching profiles for names`, err);
      return [];
    }
  }

  async getIds(user: User, network = false): Promise<Partial<Person>[]> {
    const params = { alias: user.email };
    if (network) {
      const profiles = await this.networkProfiles(user, [user.email]);

      if (!profiles.length) return [];

      const profile_queries = profiles.map(p => `select concat('people/n${p}/',id), id, "${p}" profile, displayName, comms, photos, TRUE as network from 
          people.people_${this.safeProfile(p)} where
          id not in (
            select id from people.people_${this.safeProfile(p)}, unnest(comms) comm where displayName is null or length(displayName) = 0 or network or
              comm in ( select comm from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm where (network is null or not network) )
              or comm = @alias
          )`);

      let people = [];
      while(profile_queries.length) {
        const query = `select * from (${profile_queries.splice(0,50).join(' union all ')}) order by profile, id`; // order by displayName asc`;

        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getIds', user.profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));

        try { 
          const [results] = await this.bq.query({query, params, useLegacySql: false});
          people = people.concat(results);
        } catch(err) {
          logging.errorFP(LOG_NAME, 'getIds', user.profile, `Error getting ids`, err);
          return null;
        }
        return this.peopleResults(user, people, network).map(pr => pr.person);

      }
    } else {
      const query = `SELECT id, displayName, comms, photos, FALSE as network FROM people.people_${this.safeProfile(user.profile)}
      WHERE id not in (
        select id from people.people_${this.safeProfile(user.profile)}
        where @alias in unnest(comms) or displayName is null or length(displayName) = 0
      ) and (network is null or not network)
      order by id`; //displayName asc`;

      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getIds', user.profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));

      try { 
        const [results] = await this.bq.query({query, params, useLegacySql: false});
        return this.peopleResults(user, results, network).map(pr => pr.person);
      } catch(err) {
        logging.errorFP(LOG_NAME, 'getIds', user.profile, `Error getting ids`, err);
        return null;
      }
    }
  }

  async getGroupIds(user: User, group_ids: Uid[]): Promise<Partial<Person>[]> {
    const search_ids = await this.checkProfiles(group_ids);
    if (search_ids.length < group_ids.length) logging.warnFP(LOG_NAME, 'getGroupIds', user ? user.profile : null, `Missing ${group_ids.length - search_ids.length} ids`);

    group_ids = search_ids;

    const profile_queries = group_ids.map(p => `select concat('people/n${p}/',id), id, "${p}" profile, displayName, comms, photos, TRUE as network from 
        people.people_${this.safeProfile(p)} where
        id not in (
          select id from people.people_${this.safeProfile(p)}, unnest(comms) comm where displayName is null or length(displayName) = 0 or network or 
            comm in ( select comm from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm where (network is null or not network) )
            or comm = @alias
      )`);

    let people = [];
    while(profile_queries.length) {
      const query = `${profile_queries.splice(0,50).join(' union all ')} order by id, profile`;

      const params = { alias: user.email };

      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getGroupIds', user.profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));

      try { 
        const [results] = await this.bq.query({query, params, useLegacySql: false});
        if (results && results.length) people = people.concat(results);
      } catch(err) {
        logging.errorFP(LOG_NAME, 'getGroupIds', user.profile, `Error getting ids`, err);
        return null;
      }
    }

    return this.peopleResults(user, people, true).map(pr => pr.person);
  }

  async getLearned(user: User, limit = 10, age = -1, max_per_profile = 10): Promise<{[key:string]: Partial<Person>[]}> {
    const learned: {[key:string]: Partial<Person>[]} = {};
    const has_age = age !== null && age !== undefined && !isNaN(age) && age >= 0;
    const qage = has_age ? `where timestamp_diff(current_timestamp(), last_learn, day) > ${age}` : '';

    if (!user) {
      logging.warnF(LOG_NAME, 'getLearned', `No profile to learn from ${has_age ? 'older than ':''} ${has_age ? age : ''} ${has_age ? 'days' : ''}`);
      return {};
    }

    if (logging.isDebug(user ? user.profile : null)) logging.debugFP(LOG_NAME, 'getLearned', user ? user.profile : null, `Getting learned limit ${limit} age ${age}`);

    const profile = user.profile;
    const query = `select id, profile from (select id, max(learn) last_learn, "${profile}" profile from people.people_${this.safeProfile(profile)}, unnest(array_concat(learned, [timestamp("1970-01-01 00:00:00 UTC")])) as learn group by id, profile  ) ${qage} `;

    try { 
      if (logging.isDebug(user ? user.profile : null)) logging.debugFP(LOG_NAME, 'getLearned', user ? user.profile : null, query);
      const [rows] = await this.bq.query({query, useLegacySql: false});
      if (rows) {
        for (const row of rows) {
          if (learned[row.profile]) learned[row.profile].push(new Person(row))
          else learned[row.profile] = [new Person(row)];
        }
      }
    } catch(err) {
      logging.errorFP(LOG_NAME, 'getLearned', user ? user.profile : null, `Error getting people to learn ${has_age ? 'older than ':''} ${has_age ? age : ''} ${has_age ? 'days' : ''} with query ${query}`, err);
    }
    return learned;
  }

  async getStats(user: User, user_comms: string[], additional_ids?: Uid[], network = false): Promise<Stats> {
    let profiles = user ? [user.profile] : [];
    if (additional_ids) {
      const search_ids = await this.checkProfiles(additional_ids);
      if (search_ids.length < additional_ids.length) logging.warnFP(LOG_NAME, 'getGroupIds', user ? user.profile : null, `Missing ${additional_ids.length - search_ids.length} ids`);
      profiles = profiles.concat(await this.checkProfiles(search_ids));
    } 

    const net_profiles = user && network ? await this.knownProfiles(user) : [];
    profiles = _.uniq(profiles.concat(net_profiles));

    const params = { alias: user_comms};
    let exclude_comms = '';
    if (user) {
      params['profile'] = user.profile;
      exclude_comms = `OR comm in (select distinct(comm) from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm where (not network or network is null))`;
    } else params['profile'] = 'NULL';

    try { 
      const profile_tables = _.uniq(profiles).map(p => `select '${p}' profile from people.people_${this.safeProfile(p)} where id not in (
        select id from people.people_${this.safeProfile(p)}, unnest(comms) comm where lower(comm) in UNNEST(@alias) ${!user || p !== user.profile ? exclude_comms: ''})
        and (network is null or not network) group by id`);

      const [fora_count] = await this.bq.query({query: 'select count(*) as fora_count from people.users', params: {}, useLegacySql: false});
      const [global_count] = await this.bq.query({query: 'select count(*) as global_count from people.comms', params: {}, useLegacySql: false});

      const user_count = _.uniq(profiles).length;

      const stats = {
        first_count: 0,
        second_count: 0,
        group_count: 0,
        fora_count: fora_count && fora_count.length ? fora_count[0].fora_count - user_count: 0,
        user_count,
        global_count: global_count && global_count.length ? global_count[0].global_count : 0,
      }

      const query_set: Promise<SimpleQueryRowsResponse | void>[] = []

      while(profile_tables.length) {
        let query;
        if (user) {
          query = `select 
              sum(case when profile = @profile then 1 else 0 end) first_count,
              sum(case when profile <> @profile and profile in (
                select profile from people.users, unnest(comms) comm where comm in (select distinct(comm) from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm)
                union all
                select profile from people.comms, unnest(profiles) profile where comm in unnest(@alias)
                ) then 1 else 0 end) second_count,
              sum(case when profile <> @profile and profile not in (
                select profile from people.comms, unnest(profiles) profile where comm in unnest(@alias)
                ) then 1 else 0 end) group_count,
              (select count(*) from people.users) - ${_.uniq(profiles).length} as fora_count
            from (
              ${profile_tables.splice(0,10).join(' union all ')}
            )`;
        } else {
          query = `select 
              0 first_count,
              sum(case when profile in (
                select profile from people.comms, unnest(profiles) profile where comm in unnest(@alias)
                ) then 1 else 0 end) second_count,
              sum(case when profile not in (
                select profile from people.comms, unnest(profiles) profile where comm in unnest(@alias)
                ) then 1 else 0 end) group_count,
              (select count(*) from people.users) - ${_.uniq(profiles).length} as fora_count
            from (
              ${profile_tables.splice(0,10).join(' union all ')}
            )`;
        }

        if (logging.isDebug(user ? user.profile : null)) logging.debugFP(LOG_NAME, 'getStats', user ? user.profile : null, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));

        query_set.push(this.bq.query({query, params, useLegacySql: false})
          .catch(e => logging.errorFP(LOG_NAME, 'getStats', user ? user.profile : null, `Error getting stats`, e)));
      }

      // waiting on an array of [results] where each result is an array
      const row_set = (await Promise.all(query_set)) as any as {first_count: number; second_count: number; group_count: number;}[][][];

      if (row_set) {
        const rows = flatten(row_set.filter(r => r).map(r => r[0]));
        if (rows.length) {
          stats.first_count += rows[0].first_count;
          stats.second_count += rows[0].second_count;
          stats.group_count += rows[0].group_count;
        }
      }

      return stats;
    } catch(err) {
      logging.errorFP(LOG_NAME, 'getStats', user ? user.profile : null, `Error getting stats`, err);
      return null;
    }
  }

  async getUserSkills(): Promise<Person[]> {
    // const query = `select skills from people.user_skills;`;
    const query = `select array_agg(distinct(tag.value )) skills, id from people.users, unnest(tags) tag where tag.index <> -1 and tag.value is not null and tag.value not in (select * from people.ignore) and tag.type = 'skill' group by id`;
    const [results] = await this.bq.query({query, useLegacySql: false});
    return results.map(r => new Person(r));
  }

  async getPeopleSkills() {
    const query = `select value from people.skills;`;
    const [results] = await this.bq.query({query, useLegacySql: false});
    return results.map(r => r.value);
  }

  async getGlobalTags(user: User, limit = 10): Promise<SkillStat[]> {
    const query = `select * from people.skills order by weight desc, num desc limit @limit`;
    const params = { limit }
    if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getGlobalTags', user.profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));
    try { 
      const [rows] = await this.bq.query({query, params, useLegacySql: false});
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getGlobalTags', user.profile, `Found ${rows ? rows.length : '0'} tags`);
      return rows;
    } catch(err) {
      logging.errorFP(LOG_NAME, 'getGlobalTags', user.profile, `Error getting tags`, err);
      return [];
    }
  }

  async getGlobalUserTags(user: ForaUser, user_comms: string[] = undefined,  limit = 10, tag_types = [TagType.skill], sort_count = false, keep_case = false): Promise<SkillStat[]> {
    const params = { limit, tag_types};
    let exclude_comms = '';
    if (user && user.isAuthenticatedNonGuest() &&  user.vanity) {
      params['vanity'] = user.vanity;
      exclude_comms = `OR comm in (select distinct(comm) from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm where (not network or network is null))`;
    }

    const user_profile = user ? user.profile : null;

    let exclude_profiles = '';
    if (user_comms && user_comms.length) {
      params['user_comms'] = user_comms;
      exclude_profiles = `OR profile in (select distinct(profile) profile from people.comms, unnest(profiles) profile where comm in unnest(@user_comms))`
    }

    const profile = user && user.isAuthenticatedNonGuest() ? `
      and profile not in (
        select distinct(profile) profile from people.users, unnest(comms) comm where
        vanity = @vanity
        ${exclude_comms}
        ${exclude_profiles}
      )` : '';
   
      // and lower(tag.value) in ( select value from people.skills where weight >= 1000  )
    const query = `select * from (
        select count(*) num, sum(tag.index) weight, ${keep_case ? 'tag.value' : 'lower(tag.value)'} value from
          people.users, UNNEST(tags) tag
          where lower(tag.type) in UNNEST(@tag_types) and 
            tag.index <> -1 and
            tag.value is not null and 
            length(tag.value) > 0 and
            lower(tag.value) not in ( select value from people.ignore)
            ${tag_types.includes(TagType.skill) ? 'and lower(tag.value) not in (select org from people.orgs)' : ''}
            ${tag_types.includes(TagType.organization) ? 'and lower(tag.value) not in (select value from people.skills)' : ''}
            ${profile}
          group by ${keep_case ? 'tag.value' : 'lower(tag.value)'}
        ) order by ${sort_count ? '' : 'weight desc,'} num desc limit @limit`;

    if (logging.isDebug(user_profile)) logging.debugFP(LOG_NAME, 'getGlobalUserTags', user_profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));
    try { 
      const [rows] = await this.bq.query({query, params, useLegacySql: false});
      if (logging.isDebug(user_profile)) logging.debugFP(LOG_NAME, 'getGlobalUserTags', user_profile, `Found ${rows ? rows.length : '0'} tags`);
      return rows;
    } catch(err) {
      logging.errorFP(LOG_NAME, 'getGlobalUserTags', user_profile, `Error getting tags`, err);
      return [];
    }
  }

  async getTags(user: User, user_comms: string[], limit = 10, network = false, additional_ids?: Uid[], tag_types = [TagType.skill], sort_count = false, keep_case = false): Promise<SkillStat[]> {
    if (!user_comms || !user_comms.length) throw new InternalError(500, 'No user comms for getTags');
    let profiles = additional_ids ? additional_ids.slice() : [];

    if (network || profiles.length) {
      if (network) {
        const net_profiles = (await this.networkProfiles(user, user_comms)).filter(p => p !== user.profile);
        profiles = _.uniq(profiles.concat(net_profiles));
      }

      if (profiles && profiles.length) {
        let query;
        let params;
        if (tag_types.length === 1 && tag_types[0] === TagType.skill) {
          params = {profiles, limit};
          query = `select count(skill) as num, 100 as weight, skill as value from people.user_skills, unnest(profile_matches) profile where profile.profile in unnest(@profiles) 
            group by skill order by num desc limit @limit`;
        } else {
          // lower(tag.value) in ( select value from people.skills where weight >= 1000  ) and
          params = { aliases_filter: user_comms, tag_types, limit };
          const profile_queries = profiles.map(p => `create temp table temp_tags_${user.profile}_${p} as select count(distinct(id)) num, sum(tag.index) weight, ${keep_case ? 'tag.value' : 'lower(tag.value)'} value from
              people.people_${this.safeProfile(p)}, UNNEST(tags) tag
              where lower(tag.type) in UNNEST(@tag_types) and 
                tag.index <> -1 and
                tag.value is not null and 
                length(tag.value) > 0 and
                lower(tag.value) not in ( select value from people.ignore) and
                (network is null or not network) and
                ${tag_types.includes(TagType.skill) ? 'and lower(tag.value) not in (select org from people.orgs)' : ''}
                ${tag_types.includes(TagType.organization) ? 'and lower(tag.value) not in (select value from people.skills)' : ''}
                id not in (
                  select id from people.people_${this.safeProfile(p)}, unnest(comms) comm where comm in unnest(@aliases_filter)
                )
              group by ${keep_case ? 'tag.value' : 'lower(tag.value)'};`);

          const profile_tables = profiles.map(p => `select * from temp_tags_${user.profile}_${p}`);
          query = profile_queries.concat([`select * from (select sum(num) num, sum(weight) weight, value from (${profile_tables.join(' union all ')}) group by value) order by ${sort_count ? '' : 'weight desc,'} num desc limit @limit;`]).join('\n');
        }

        try {
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getTags', user.profile, `Running ${query} params ${params}`);
          const [rows] = await this.bq.query({query, params, useLegacySql: false});
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getTags', user.profile, `Found ${rows ? rows.length : '0'} network tags`);
          return rows;
        } catch(err) {
          logging.errorFP(LOG_NAME, 'getTags', user.profile, `Error getting network tags with ${query}`, err);
        } 
      }
    } else if (await this.checkProfiles([user.profile])) {
      // lower(tag.value) not in ( select value from people.skills where weight >= 1000  ) and
      const query = `select * from (
          select count(distinct(id)) num, sum(tag.index) weight, ${keep_case ? 'tag.value' : 'lower(tag.value)'} value from
            people.people_${this.safeProfile(user.profile)}, UNNEST(tags) tag
            where lower(tag.type) in UNNEST(@tag_types) and 
              tag.index <> -1 and
              tag.value is not null and 
              length(tag.value) > 0 and
              lower(tag.value) not in ( select value from people.ignore) and
              (network is null or not network) and 
              ${tag_types.includes(TagType.skill) ? 'lower(tag.value) not in (select org from people.orgs) and ' : ''}
              ${tag_types.includes(TagType.organization) ? 'lower(tag.value) not in (select value from people.skills) and' : ''}
              id not in (
                select id from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm where comm in unnest(@aliases_filter)
              )
            group by ${keep_case ? 'tag.value' : 'lower(tag.value)'}
          ) order by ${sort_count ? '' : 'weight desc,'} num desc limit @limit;`;

      const params = { tag_types, aliases_filter: user_comms, limit};
      if (user) params['profile'] = user.profile;
      if (additional_ids && additional_ids.length) params['additional_ids'] = additional_ids;

      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getTags', user.profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));

      try { 
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getTags', user.profile, `Running ${query} params ${JSON.stringify(params)}`);
        const [rows] = await this.bq.query({query, params, useLegacySql: false});
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'getTags', user.profile, `Found ${rows ? rows.length : '0'} tags`);
        return rows;
      } catch(err) {
        logging.errorFP(LOG_NAME, 'getTags', user.profile, `Error getting network tags with ${query}`, err);
      }
    }

    return [];
  }

  /*async reloadGlobalPeople(people: Partial<Person>[]) {
    // write to cloud storage
    const export_file_name = `global_${new Date()}`;
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'reloadGlobalPeople', `Reloading ${people.length} people view ${export_file_name}`);
    const export_file = await GoogleCloudStorageHelper.globalFileCreate(this.storage, export_file_name, {}, people.map(p => globalPerson(p)).join('\n'));

    // import 
    if (logging.isDebug()) logging.debugF(LOG_NAME, 'reloadGlobalPeople', 'Importing to BigQuery');
    try { 
      await this.bq.dataset('people').table('people').load(export_file, GLOBAL_METADATA);
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'reloadGlobalPeople', 'Reload complete');
    } catch (e) {
      logging.errorF(LOG_NAME, 'reloadGlobalPeople', `Error reloading`, e);
    }
  }*/

  async importPeople(source: string, profile?: string) {
    const table_id = `temp_people_${profile ? this.safeProfile(profile) : 'global_users'}`;
    logging.infoFP(LOG_NAME, 'importPeople', profile, `Importing to ${table_id}`);

    const metadata = {
      sourceFormat: 'DATASTORE_BACKUP',
      writeDisposition: 'WRITE_TRUNCATE',
      location: 'US',   // TODO: long term, make this portable / compatible with export law
    };

    const [job] = await this.bq
      .dataset('people')
      .table(table_id)
      .load(this.storage.bucket(source).file(
        profile ? `namespace_${profile}/kind_Person/namespace_${profile}_kind_Person.export_metadata` : 
        'default_namespace/kind_vanity/default_namespace_kind_vanity.export_metadata')
      , metadata);
        // `all_namespaces/kind_vanity/all_namespaces_kind_vanity.export_metadata`), metadata);


    const err = job.status.errors;
    if (err && err.length > 0) {
      logging.errorFP(LOG_NAME, 'importPeople', profile, `Import error from ${source} to ${table_id}::${err[0].reason} at ${err[0].location}`, new Error(err[0].message));
      throw err;
    }

    while(job.status.state !== 'DONE') {
      await setTimeout(1000);
      logging.infoFP(LOG_NAME, 'importPeople', profile, `Import status ${job.status.state} from ${source} to ${table_id}`);
    }

    logging.infoFP(LOG_NAME, 'importPeople', profile, `Done importing to ${table_id}`);
  }

  async tempToProfile(profile?: string): Promise<void> {
    let query;

    if (profile) {
      const suffix = this.safeProfile(profile);
      const temp_table = `temp_people_${suffix}`;
      const table = `people_${suffix}`;

      let select_learned = 'learned';
      let select_tags = 'array(select as struct type, value,  index, start from unnest(tags)) tags';

      for (let attempts = 0; attempts < 5; attempts++) {
        try {
          const [md] = await this.bq.dataset('people').table(temp_table).getMetadata();
          if (md) {
            const learned_record = md.schema.fields.find(f => f.name === 'learned');
            const tags_record = md.schema.fields.find(f => f.name === 'tags');

            const complex_learned = learned_record && learned_record.fields && learned_record.fields.find(f => f.name === 'provided') !== undefined;
            const complex_tags = tags_record && tags_record.fields && tags_record.fields.find(f => f.name === 'provided') !== undefined;

            if (complex_learned) select_learned =  `array(select l.date_time from unnest(learned) l) learned`;
            if (complex_tags) select_tags = `array(select as struct t.entity.type, t.entity.value,  t.entity.index, t.entity.start from unnest(tags) t) tags` ;
          }
          break;
        } catch(err) {
          if (retryError(err)) continue;
          logging.warnFP(LOG_NAME, 'tempToProfile', profile, `Error getting metadata ${query}`, err);
          throw err;
        }
      }

      query = `create or replace table people.${table} as select  
        comms, nickName, displayName, network, names, id, photos, urls,
        ${select_learned}, ${select_tags}
        from people.${temp_table} where network is null or not network and displayName is not null`
    } else {
      let select_comms = '[email] as comms'; //`array(select lower(comm) from unnest(comms) comm where comm like '%@%.%') comms`;
      let select_learned = `array(select TIMESTAMP('2255-06-05T23:47:34.740Z')) learned`;
      let select_tags = `array(select as struct type, value,  index, start from unnest(tags)) tags`;
      let select_nick = 'case when nickName is null then REGEXP_EXTRACT(displayName, r\'\\w+\') else nickName end as nickName';

      for (let attempts = 0; attempts < 5; attempts++) {
        try {
          const [md] = await this.bq.dataset('people').table('temp_people_global_users').getMetadata();
          if (md) {
            const tags_record = md.schema.fields.find(f => f.name === 'tags');
            const complex_tags = tags_record && tags_record.fields && tags_record.fields.find(f => f.name === 'provided') !== undefined;
            if (complex_tags) select_tags = `array(select as struct t.entity.type, t.entity.value,  t.entity.index, t.entity.start from unnest(tags) t) tags` ;
          }
          break;
        } catch(err) {
          if (retryError(err)) continue;
          logging.warnFP(LOG_NAME, 'tempToProfile', profile, `Error getting metadata ${query}`, err);
          throw err;
        }
      }

      query = `create or replace table people.users as select
         ${select_nick}, displayName, names, id, photos, profile, urls, vanity,
         ${select_comms}, ${select_learned}, ${select_tags} 
         from people.temp_people_global_users`
    }

    for (let attempts = 0; attempts < 5; attempts++) {
      try {
        const results = await this.bq.createJob({configuration: {query:{query, useLegacySql: false}}});
        const job = results[0];
        const [rows] = await job.getQueryResults(job);
        if (logging.isDebug(profile)) logging.debugFP(LOG_NAME, 'tempToProfile', profile, JSON.stringify(rows));
        if (profile) await this.dropTable(profile, true);
        else await this.dropTable(null, true);
        break;
      } catch (err) {
        if (retryError(err))  continue;
        logging.warnFP(LOG_NAME, 'tempToProfile', profile, `Error ${query}`, err);
        throw err
      }
    }
  }

  async getPeople(user: User, ignore_comms: string = null): Promise<Person[]> {
    const query = `select * from people.people_${this.safeProfile(user.profile)} where network is null or not network`;
    try {
      const [results] = await this.bq.query({ query, useLegacySql: false });
      if (logging.isDebug(user.profile)) DEBUG('getPeople: %s RESULTS = %j', user.profile, results);

      if (!results || !results.length) return [];

      return this.peopleResults(user, results.filter(p => !p.comms || !ignore_comms || !p.comms.includes(ignore_comms))).map(pr => pr.person);
    } catch (err) { 
      if (err.message && err.message.includes('Not found')) logging.warnFP(LOG_NAME, 'getPeople', user.profile, `Error querying for people`, err); 
      else logging.errorFP(LOG_NAME, 'getPeople', user.profile, `Error querying for people`, err); 
    }

  }

  async checkForMissingTable(profile: string, err: any): Promise<void> {
    if (err.message && err.message.includes('found')) {
      await doExport(profile);
    }
  }

  async exportPeople(profile?: string): Promise<string> {
    try {
      // make sure everything is good globally
      // await this.checkSchema();
      if (profile) logging.infoFP(LOG_NAME, 'exportPeople', profile, 'Exporting people');
      else logging.infoFP(LOG_NAME, 'exportPeople', profile, 'Exporting users');

      if (config.isEnvOffline()) {
        if (profile) await this.bq.createJob({id:profile});
        return 'offline';
      } 

      if (profile) await this.startOp(profile);

      const client_email = `${config.get('GOOGLE_CLOUD_PROJECT')}@appspot.gserviceaccount.com`;
      let key = config.get('SERVICE_KEY');
      if (!(key && key.length) && config.isEnvDevelopment()) {
        key = JSON.parse(fs.readFileSync('google_api.json', 'utf-8')).private_key;
      }

      const jwt = new JWT({ email: client_email, key, scopes: 'https://www.googleapis.com/auth/datastore' });
     
      let success = false;
      let wait = 500;
      while(!success) {
        try {
          await jwt.authorize();
          success = true;
        } catch(err) {
          if (retryError(err) && wait < 512000) {
            await setTimeout(wait);
            wait *= 2;
          } else {
            logging.errorFP(LOG_NAME, 'exportPeople', profile, `Error authorizing JWT ${wait}`, err);
            return;
          }
        }
      }

      // initiate the export
      const export_config: AxiosRequestConfig = {
        method: 'post',
        url: `https://datastore.googleapis.com/v1/projects/${config.get('GOOGLE_CLOUD_PROJECT')}:export`,
        headers: { Authorization: `Bearer ${jwt.credentials.access_token}` },
        data: {
          outputUrlPrefix: `gs://${config.get('GOOGLE_CLOUD_PROJECT')}-people-export`,
          entityFilter: profile ? { namespaceIds: [profile], kinds: ['Person'] } : { namespaceIds: [''], kinds: ['vanity']},
        },
      } as AxiosRequestConfig;

      let name = null;
      wait = 500;
      while (name === null || name === undefined) {
        try {
          const r: any = await axios(export_config);
          name = r.data.name;
          if (name === null || name === undefined) {
            logging.errorFP(LOG_NAME, 'exportPeople', profile, `Error initiating export - no name: ${JSON.stringify(r.data)} ${JSON.stringify(export_config)}`, null);
            return;
          }
        } catch (err) {
          // retry up to a minute
          if (retryError(err) && wait <= 512000) {
            await setTimeout(wait);
            wait *= 2;
          } else {
            logging.errorFP(LOG_NAME, 'exportPeople', profile, `Error initiating export retry ${wait} ${JSON.stringify(export_config)}`, err);
            return;
          }
        }
      }

      return name;
    } catch (err) {
      logging.errorFP(LOG_NAME, 'exportPeople', profile, `Error exporting ${profile ? 'people' : 'users'}`, err);
    }
  }

  async checkExport(name: string, profile?: string, ): Promise<string> {
    const client_email = `${config.get('GOOGLE_CLOUD_PROJECT')}@appspot.gserviceaccount.com`;
    let key = config.get('SERVICE_KEY');
    if (!(key && key.length) && config.isEnvDevelopment()) {
      key = JSON.parse(fs.readFileSync('google_api.json', 'utf-8')).private_key;
    }

    const jwt = new JWT({ email: client_email, key, scopes: 'https://www.googleapis.com/auth/datastore' });
    await jwt.authorize();

    let success = false;
    let wait = 500;
    while(!success) {
      try {
        await jwt.authorize();
        success = true;
      } catch(err) {
        if (retryError(err) && wait < 512000) {
          await setTimeout(wait);
          wait *= 2;
        } else {
          logging.errorFP(LOG_NAME, 'checkExport', profile, `Error authorizing JWT ${wait}`, err);
          return;
        }
      }
    }

    try {
      const params: datastore_v1.Params$Resource$Projects$Operations$Get = { name, auth: jwt as any };
      try {
        let results = await dataStoreAdmin.projects.operations.get(params);
        logging.infoFP(LOG_NAME, 'checkExport', profile, `Export ${name} ${results.data.name}: ${results.data.metadata.common.state}`);
        if (results.data.done) {
          // make sure state is SUCCESSFUL, waiting up to 10s
          let success_check = 10;
          while (results.data.metadata.common.state !== 'SUCCESSFUL' && success_check) {
            await setTimeout(5000);
            results = await dataStoreAdmin.projects.operations.get(params);
            logging.infoFP(LOG_NAME, 'checkExport', profile, `Export ${name} ${results.data.name}: ${results.data.metadata.common.state}`);
            success_check--;
          }
          logging.infoFP(LOG_NAME, 'checkExport', profile, `Exported ${name} to ${results.data.metadata.outputUrlPrefix}`);
          return results.data.metadata.outputUrlPrefix;
        }
      } catch (err) {
        if (retryError(err)) logging.warnFP(LOG_NAME, 'checkExport', profile, 'Socket hungup', err);
        else logging.errorFP(LOG_NAME, 'checkExport', profile, `Error checking export ${name}`, err);
      }
    } catch (err) {
      logging.errorFP(LOG_NAME, 'checkExport', profile, `Error checking people export to ${name}`, err);
    }

    return null;
  }

  async finishExport(source: string, profile?: string) {
    logging.infoFP(LOG_NAME, 'finishExport', profile, `Importing ${profile ? 'people' : 'users'}`);

    let imported = false;
    try { 
      await this.importPeople(source, profile);
      imported = true;
      await this.tempToProfile(profile);
    } catch(err) {
      logging.errorFP(LOG_NAME, 'finishExport', profile, `Error finishing people export ${source} ${profile} ${imported ? 'after' : 'during'} import`, err);
    }

    if (profile) await this.endOp(profile);
  }

  async exportGlobal(global: GlobalType, group_id?: Uid) {

    try {
      const client_email = `${config.get('GOOGLE_CLOUD_PROJECT')}@appspot.gserviceaccount.com`;
      let key = config.get('SERVICE_KEY');
      if (!(key && key.length) && config.isEnvDevelopment()) {
        key = JSON.parse(fs.readFileSync('google_api.json', 'utf-8')).private_key;
      }

      const jwt = new JWT({ email: client_email, key, scopes: 'https://www.googleapis.com/auth/datastore' });
     
      let success = false;
      let wait = 500;
      while(!success) {
        try {
          await jwt.authorize();
          success = true;
        } catch(err) {
          if (retryError(err) && wait < 512000) {
            await setTimeout(wait);
            wait *= 2;
          } else {
            logging.errorF(LOG_NAME, 'exportGlobal', `Error authorizing JWT ${wait}`, err);
            return;
          }
        }
      }

      const bucket_name = `${config.get('GOOGLE_CLOUD_PROJECT')}-${global}-export`;
      const bucket = this.storage.bucket(bucket_name);
      const [exists] = await bucket.exists();
      if (!exists) {
        await this.storage.createBucket(bucket_name, { autoclass: { enabled: true, terminalStorageClass: 'ARCHIVE' } });
        await this.storage.bucket(bucket_name).setRetentionPeriod(24 * 2600);
      }

      // initiate the export
      const export_config: AxiosRequestConfig = {
        method: 'post',
        url: `https://datastore.googleapis.com/v1/projects/${config.get('GOOGLE_CLOUD_PROJECT')}:export`,
        headers: { Authorization: `Bearer ${jwt.credentials.access_token}` },
        data: {
          outputUrlPrefix: `gs://${bucket_name}`,
          entityFilter: { namespaceIds: [group_id ? group_id : ''], kinds: [global]},
        },
      } as AxiosRequestConfig;

      if (group_id) await this.startOp(group_id);

      logging.infoF(LOG_NAME, 'exportGlobal', `Export config ${JSON.stringify(export_config)}`);

      let name = null;
      wait = 500;
      while (name === null || name === undefined) {
        try {
          const r: any = await axios(export_config);
          name = r.data.name;
          if (name === null || name === undefined) {
            logging.errorF(LOG_NAME, 'exportGlobal', `Error initiating export - no name: ${JSON.stringify(r.data)} ${JSON.stringify(export_config)}`, null);
            if (group_id) await this.endOp(group_id);
            return;
          }
        } catch (err) {
          // retry up to a minute
          if (retryError(err) && wait <= 512000) {
            await setTimeout(wait);
            wait *= 2;
          } else {
            logging.errorF(LOG_NAME, 'exportGlobal', `Error initiating export retry ${wait} ${JSON.stringify(export_config)}`, err);
            if (group_id) await this.endOp(group_id);
            return;
          }
        }
      }

      let source;
      while(!source) {
        const params: datastore_v1.Params$Resource$Projects$Operations$Get = { name, auth: jwt as any };
        try {
          let results = await dataStoreAdmin.projects.operations.get(params);
          logging.infoF(LOG_NAME, 'checkExportGlobal', `Export ${name} ${results.data.name}: ${results.data.metadata.common.state}`);
          if (results.data.done) {
            // make sure state is SUCCESSFUL, waiting up to 60s
            let success_check = 60;
            while (results.data.metadata.common.state !== 'SUCCESSFUL' && success_check) {
              await setTimeout(5000);
              results = await dataStoreAdmin.projects.operations.get(params);
              logging.infoF(LOG_NAME, 'checkExportGlobal', `Export ${name} ${results.data.name}: ${results.data.metadata.common.state}`);
              success_check--;
            }
            logging.infoF(LOG_NAME, 'checkExportGlobal', `Exported ${name} to ${results.data.metadata.outputUrlPrefix}`);
            source = results.data.metadata.outputUrlPrefix;
          }
        } catch (err) {
          if (retryError(err)) logging.warnF(LOG_NAME, 'checkExportGlobal', 'Socket hungup', err);
          else logging.errorF(LOG_NAME, 'checkExportGlobal', `Error checking export ${name}`, err);
        }

        await setTimeout(10000);
      }

      const table_id = global; 
      logging.infoF(LOG_NAME, 'importGlobal', `Importing to ${table_id}`);

      const metadata = {
        sourceFormat: 'DATASTORE_BACKUP',
        writeDisposition: 'WRITE_TRUNCATE',
        location: 'US',   // TODO: long term, make this portable / compatible with export law
      };

      const file_path = group_id ? 
        `namespace_${group_id}/kind_${global}/namespace_${group_id}_kind_${global}.export_metadata` 
        : `default_namespace/kind_${global}/default_namespace_kind_${global}.export_metadata`;
      // `all_namespaces/kind_${global}/all_namespaces_kind_${global}.export_metadata`;

      logging.infoF(LOG_NAME, 'exportGlobal', `Import file ${file_path}`);

      if (group_id) await this.createGroup(group_id);

      const [job] = await this.bq
        .dataset(group_id ? this.safeProfile(group_id) : 'people')
        .table(table_id)
        .load(this.storage.bucket(source)
        .file(file_path), metadata);
        // .file(`all_namespaces/kind_${global}/all_namespaces_kind_${global}.export_metadata`), metadata);

      const err = job.status.errors;
      if (err && err.length > 0) {
        logging.errorF(LOG_NAME, 'importGlobal', `Import error from ${source} to ${table_id}::${err[0].reason} at ${err[0].location}`, new Error(err[0].message));
        throw err;
      }

      while(job.status.state !== 'DONE') {
        await setTimeout(1000);
        logging.infoF(LOG_NAME, 'importGlobal', `Import status ${job.status.state} from ${source} to ${table_id}`);
      }

      logging.infoF(LOG_NAME, 'importGlobal', `Done importing to ${table_id}`);
 
    } catch(err) {
      logging.errorF(LOG_NAME, 'exportGlobal', `Error exporting ${global}`, err);
    }
    if (group_id) await this.endOp(group_id);
  }


  async insertUser(vanity: Vanity): Promise<void> {
    const orgs = vanity.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
    parsers.cleanTags(vanity.tags, vanity.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
    const tags = vanity.tags.map(tag => { return {
      type: tag.type,
      value: tag.value, //utf8.encode(tag.value),
      index: tag.index,
      start: BigQuery.timestamp(new Date(tag.start)),
    }; });

     const params = {
      nickName: vanity.nickName, // ? utf8.encode(person.nickName) : undefined,
      displayName: vanity.displayName, // ? utf8.encode(person.displayName) : undefined,
      names: vanity.names, // ? person.names.map(n => utf8.encode(n)) : undefined,
      id: vanity.vanity,
      photos: vanity.photos,
      profile: vanity.profile,
      urls: vanity.urls,
      vanity: vanity.vanity,
      comms: vanity.comms,
      learned: [BigQuery.timestamp('2255-06-05T23:47:34.740Z')],
      tags,
    };

    if (logging.isDebug()) {
      logging.debugF(LOG_NAME, 'insertUser', util.format('QP = %j', params));
      DEBUG('insertUser: QP = %j', params);
    }

    const options = { raw: false, ignoreUnknownValues: true };
    let retry = 100;
    while(retry < 3600) {
      try {
        await this.bq.dataset('people').table('users').insert(params, options);
        logging.infoF(LOG_NAME, 'insertUser', `::${vanity.vanity} Inserted vanity to user table successfully`);
          //.catch(err => logging.errorF(LOG_NAME, 'insertPerson', `Error inserting person ${util.inspect(person)}`, err));
        break;
      } catch(e) {
        if ((e.code === '404' || (e.message && e.message.includes('truncated'))) && retry < 3600) {
          retry *= 2;
          logging.warnF(LOG_NAME, 'insertUser', `::${vanity.vanity} Retrying insert vanity to user table`, e);
        } else if(retry >= 3600) logging.errorF(LOG_NAME, 'insertUser', `::${vanity.vanity} Error inserting vanity to user table after ${retry} retries`, e);
        else {
          logging.errorF(LOG_NAME, 'insertUser', `::${vanity.vanity} Error inserting vanity to user table ${JSON.stringify(e.errors)}`, e);
          return;
        }
      }
    }
 
  
  }

  // insert into global bigquery table - does not de-dupe
  async insertPerson(profile: Uid, person: Person): Promise<void> {
    const table = profile && profile.length ? `people_${this.safeProfile(profile)}` : 'people';
    const orgs = person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
    parsers.cleanTags(person.tags, person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
    const tags = person.tags.map(tag => { return {
      type: tag.type,
      value: tag.value, //utf8.encode(tag.value),
      index: tag.index,
      start: BigQuery.timestamp(new Date(tag.start)),
    }; });

    const params = {
      displayName: person.displayName, // ? utf8.encode(person.displayName) : undefined,
      comms: person.comms,
      tags,
      photos: person.photos,
      names: person.names, // ? person.names.map(n => utf8.encode(n)) : undefined,
      urls: person.urls,
      nickName: person.nickName, // ? utf8.encode(person.nickName) : undefined,
      learned: person.learned ? person.learned.map(l => new Date(l)).filter(l => l.getTime() > 0).map(l => BigQuery.timestamp(l)) : [],
      // id: person.id,
    };

    if (profile) params['id'] = person.id;

    if (logging.isDebug(profile)) {
      logging.debugFP(LOG_NAME, 'insertPerson', profile, util.format('QP = %j', params));
      DEBUG('insertPerson: QP = %j', params);
    }

    const options = { raw: false, ignoreUnknownValues: true };
    let retry = 100;
    while(retry < 3600) {
      try {
        await this.bq.dataset('people').table(table).insert(params, options);
        logging.infoFP(LOG_NAME, 'insertPerson', profile, `${person.id} Inserted person to global table successfully`);
        break;
      } catch(e) {
        if ((e.code === '404' || retryError(e)) && retry < 3600) {
          retry *= 2;
          logging.warnFP(LOG_NAME, 'insertPerson', profile, `${person.id} Retrying insert person to global table`, e);
        } else logging.errorFP(LOG_NAME, 'insertPerson', profile, `${person.id} Error inserting person to global table after ${retry} retries`, e);
      }
    }
  }

  // insert into global bigquery table - does not de-dupe
  async insertPersons(persons: Person[]): Promise<void> {
    const rows = [];
    for (const person of persons) {
      const orgs = person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
      parsers.cleanTags(person.tags, person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
      const tags = person.tags.map(tag => { return {
        type: tag.type,
        value: tag.value, //utf8.encode(tag.value),
        index: tag.index,
        start: BigQuery.timestamp(new Date(tag.start)),
      }; });

      rows.push({
        displayName: person.displayName, // ? utf8.encode(person.displayName) : undefined,
        comms: person.comms,
        photos: person.photos,
        names: person.names, // ? person.names.map(n => utf8.encode(n)) : undefined,
        urls: person.urls,
        nickName: person.nickName, // ? utf8.encode(person.nickName) : undefined,
        tags,
        learned: person.learned ? person.learned.map(l => new Date(l)).filter(l => l.getTime() > 0).map(l => BigQuery.timestamp(l)) : [],
      });
    }

    if (logging.isDebug()) {
      logging.debugF(LOG_NAME, 'insertPersons', util.format('ROWS = %j', rows).replace(/\n/g, ' '));
      DEBUG('insertPersons: ROWS = %j', rows);
    }

    const options = { raw: false, ignoreUnknownValues: true };
    const response = await this.bq
      .dataset('people')
      .table(`people`)
      .insert(rows, options)
      .then(() => logging.infoF(LOG_NAME, 'insertPersons', `Inserted persons successfully - ${rows.length}`))
      .catch(err => {
        logging.errorF(LOG_NAME, 'insertPersons', 'Error inserting persons', err);
        if (err.name === 'PartialFailureError') {
          // Some rows failed to insert, while others may have succeeded.
          // err.errors (object[]):
          // err.errors[].row (original row object passed to `insert`)
          // err.errors[].errors[].reason
          // err.errors[].errors[].message
        }
      });

    if (logging.isDebug()) {
      logging.debugF(LOG_NAME, 'insertPersons', util.format('Response = %j', response).replace(/\n/g, ' '));
      DEBUG('insertPersons: RESPONSE = %j', response);
    }
  }

  formatTag(t: {type: TagType; value: string; index: number; start: Date}) {
    return `STRUCT(
      '${t.type}' as type, 
      '${t.value.replace(/\n/g, '\\n').replace(/\r/g,'').replace(/([^\\])\\/g, "$1\\\\").replace(/([^\\])'/g, "$1\\'")}' as value, 
      ${t.index} as index, 
      PARSE_TIMESTAMP('%s', '${Math.floor(t.start.getTime() / 1000)}') as start
      )`;
  }

  // update in global bigquery table
  async updatePerson(profile: string, person: Person): Promise<void> {
    const orgs = person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
    parsers.cleanTags(person.tags, person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
    const tags = person.tags.filter(tag => tag.value && (profile || !IGNORE_GLOBAL_TAGS.includes(tag.type))).map(tag => { return {
      type: tag.type,
      value: tag.value, // utf8.encode(tag.value),
      index: tag.index,
      start: tag.start, //BigQuery.timestamp(new Date(tag.start).valueOf()),
    }; });

    const params: {
        displayName?: string;
        comms?: string[];
        tags?: any[];
        photos?: string[];
        names?: string[];
        urls?: string[];
        nickName?: string;
        learned?: any[];
        id?: string;
      } = {
        displayName: person.displayName,
        nickName: person.nickName,
        names: person.names,
        id: person.id,
      };

    const types:any = {
      id: 'STRING',
      displayName: 'STRING',
      nickName: 'STRING',
      comms: ['STRING'],
      names: ['STRING'],
      photos: ['STRING'],
      urls: ['STRING'],
      learned: ['TIMESTAMP'],
      tags_id: ['STRUCT<type STRING NOT NULL, value STRING NOT NULL, index INT64 NOT NULL, start TIMESTAMP NOT NULL>'],
    };

    if (person.comms && person.comms.length) {
      const comms = person.comms.filter(c => c !== person.id);
      if (comms.length) params.comms = comms;
    }

    let tag_string = '';
    if (tags && tags.length) {
      tag_string = `tags = [${tags.map(this.formatTag).join(',')}],`;
    }
    if (person.photos && person.photos.length) params.photos = person.photos;
    if (person.names && person.names.length) params.names = person.names;
    if (person.urls && person.urls.length) params.urls = person.urls;
    if (person.learned && person.learned.length) {
      const flearned = person.learned ? person.learned.map(l => new Date(l)).filter(l => l.getTime() > 0).map(l => BigQuery.timestamp(l)) : [];
      if (flearned.length) params.learned = flearned;
    }

    if (logging.isDebug(profile)) {
      DEBUG('updatePerson: %s QP = %j', profile, params);
      DEBUG('updatePerson: %s SQL = %s', profile);
    }

    let error;
    if (!person.comms || !person.comms.length) logging.infoFP(LOG_NAME, 'updatePerson', profile, `Not updating global person with no comms - ${person.id}`)
    else if (!person.self) logging.debugFP(LOG_NAME, 'updatePerson', profile, `Not updating global person not self - ${person.id}`)

    let select = null;

    if (params.id) select = 'id = @id';
    else if (params.comms && params.comms.length) select = '(select count(*) from unnest(@comms) as comm1 inner join unnest(comms) as comm2 on comm1 = comm2) > 0';

    if (profile && select) {
      // check profile table
      const profiles = await this.checkProfiles([profile]);
      if (!profiles.length) {
        logging.warnFP(LOG_NAME, 'updatePerson', profile, `Not updating person  ${person.id} in profile table because it doesn't exist yet`);
        return;
      }

      const query = `update people.people_${this.safeProfile(profile)} set
            displayName = @displayName,
            comms = ${ params.comms && params.comms.length ? '@comms' : 'null'},
            ${ tag_string }
            ${ params.photos && params.photos.length ? 'photos = @photos,' : '' }
            ${ params.names && params.names.length ? 'names = @names,' : '' }
            ${ params.urls && params.urls.length ? 'urls = @urls,' : '' }
            ${ params.learned && params.learned.length ? 'learned = @learned,' : '' }
            nickName = @nickName
          where ${select}`;

      let do_insert = false;

      try {
        const r = await this.retryQuery(profile, 'updatePerson', query, params, types);
        if (!r || !r.length) {
          do_insert = true;
          logging.infoFP(LOG_NAME, 'updatePerson', profile, `No person in profile table, inserting ${person.id}`);
        } else logging.infoFP(LOG_NAME, 'updatePerson', profile, `Updated person in profile table successfully ${person.id}`);
      } catch(err) {
        if (err.message.includes('not found')) logging.warnFP(LOG_NAME, 'updatePerson', profile, `Error updating person in profile table ${util.format('QP = %j', params)} :: ${JSON.stringify(person)}`, err)
        else if(err.code === 400) {
          // already have an update in the stream buffer, do an export
          logging.warnFP(LOG_NAME, 'updatePerson', profile, `Error updating person profile table. Re-exporting table. ${util.format('QP = %j', params)} :: ${JSON.stringify(person)}`, err)
          await internalUpdate({profile, export: true, force_internal: true});
        } else logging.errorFP(LOG_NAME, 'updatePerson', profile, `Error updating person in profile table ${util.format('QP = %j', params)} :: ${JSON.stringify(person)}`, err)
        throw err;
      }

      if (do_insert) await this.insertPerson(profile, person);
    }

    if (error) throw error;
  }

  async connectedUsers(user: User, user_comms: string[]): Promise<Person[]> {
    const query = `
      select id, displayName, names, nickName, comms, learned, photos, urls, tags from people.people_${this.safeProfile(user.profile)}
      where id in (
        select distinct(id) from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm
        where comm in (select distinct(comm) from people.users, unnest(comms) comm) and 
          comm not in unnest(@user_comms)
      )`;

    const params = { user_comms, profile: user.profile }; 

    try {
      logging.debugFP(LOG_NAME, 'connectedUsers', user.profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));
      const [results] = await this.bq.query({query, params, useLegacySql: false });
      return this.peopleResults(user, results).map(pr => pr.person);
    } catch (err) {
      logging.errorFP(LOG_NAME, 'connectedUsers', user.profile, 'Error looking up connected users', err);
    }

    return [];
  }

  async lookupConnections(user: User, conn_email: string, comms: string[]): Promise<Uid[]> {
    if (comms.length === 0) return null;

    const query = `select distinct(p1.profile) profile from (
        select profile from people.comms, unnest(profiles) profile 
        where comm in unnest(@user_comms) and profile <> @profile) p1
      join (
        select profile from people.comms, unnest(profiles) profile 
        where comm = @conn_email and profile <> @profile) p2 
      on p1.profile = p2.profile`;

    const params = { user_comms: comms };
    if (conn_email) params['conn_email'] = conn_email;
    if (user) params['profile'] = user.profile;

    if (logging.isDebug(user ? user.profile : null)) {
      logging.debugFP(LOG_NAME, 'lookupConnections', user ? user.profile : null, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));
      DEBUG('lookupConnections: %s QP = %j', user ? user.profile : null, params);
      DEBUG('lookupConnections: %s SQL = %s', user ? user.profile : null, format(query, { language: "bigquery" }));
    }

    try {
      const [results] = await this.bq.query({query, params, useLegacySql: false});
      if (logging.isDebug(user ? user.profile : null)) DEBUG('lookupConnections: %s RESULTS = %j', user ? user.profile : null, results);
      return results ? results.map(r => r.profile) : [];
    } catch (err) { logging.errorFP(LOG_NAME, 'lookupConnections', user ? user.profile : null, `Error looking up connections ${JSON.stringify(comms)}`, err); }
  }

  async lookupPeople(user: User, people: Partial<Person>[], index?: number): Promise<Partial<Person>[]> {
    if (config.isEnvOffline()) {
      const found_people = [];
      for (const person of people) {
        let found_person;
        if (person.comms) {
          const emails = parsers.findEmail(person.comms);
          if (emails && emails.length) found_person = await this.lookupPerson(user, emails);
        } else if (person.tags) {
          const orgs = person.tags.filter(t => t.type === TagType.organization && t.value).map(t => t.value.toLowerCase());
          if (orgs && orgs.length) {
            found_person = await this.lookupPersonOrgs(user, person.displayName, orgs);
          }
        }
        if (found_person) {
          found_person.id = null;
          found_person.network = true;
          found_people.push(found_person);
        }
      }
      return found_people;
    }

    // write to cloud storage
    // if (logging.isDebug(user ? user.profile : null)) 
    logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Looking up ${people.length} people`);
    const comm_data = flatten(people.filter(p => p.comms).map(p => parsers.findEmail(p.comms)));
    const comm_file_data = comm_data.length ? comm_data.map(comm => { return JSON.stringify({comm})}).join('\n') : null;
    const export_comm_file_name = `lookup_comm_${user ? this.safeProfile(user.profile) : ''}_${new Date().getTime()}${index ? '_' : ''}${index ? index : ''}`;
    const export_comm_file = comm_file_data ? await GoogleCloudStorageHelper.globalFileCreate(this.storage, export_comm_file_name, {}, comm_file_data) : null;

    const org_data = people.filter(p => p.displayName && p.tags);
    const org_file_data = org_data.length ? flatten(org_data.map(p => p.tags.filter(t => t.type === TagType.organization && t.value).map(t => {
            return JSON.stringify({
              name: `${p.displayName.toLowerCase()}`,
              org: `${t.value.toLowerCase()}`})
          }))).join('\n') : null;
    const export_name_org_file_name = `lookup_name_org_${user ? this.safeProfile(user.profile) : ''}_${new Date().getTime()}${index ? '_' : ''}${index ? index : ''}`;
    const export_name_org_file = org_file_data ? await GoogleCloudStorageHelper.globalFileCreate(this.storage, export_name_org_file_name, {}, org_file_data) : null;

    // import 
    // if (logging.isDebug(user ? user.profile : null)) 
    logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, 'Importing to BigQuery');
    try { 
      const [comm_job] = export_comm_file ? await this.bq.dataset('people').table(export_comm_file_name).load(export_comm_file, LOOKUP_COMM_METADATA) : [];
      const [org_job] = export_name_org_file ? await this.bq.dataset('people').table(export_name_org_file_name).load(export_name_org_file, LOOKUP_NAME_ORG_METADATA) : [];

      for (const job of [comm_job, org_job]) {
        if (job) {
          const err = job.status.errors;
          if (err && err.length > 0) {
            logging.errorFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Error loading query table::${err[0].reason} at ${err[0].location}`, new Error(err[0].message));
            throw err;
          }

          while(job.status.state !== 'DONE') {
            await setTimeout(1000);
            logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Loading status ${job.status.state}`);
          }
        }
      }
      if (logging.isDebug(user ? user.profile : null)) logging.debugFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, 'Load complete');
    } catch (e) {
      logging.errorF(LOG_NAME, 'lookupPeople', `Loading`, e);
    }

    const comm_profiles = export_comm_file ? `select distinct(profile) profile from people.comms, unnest(profiles) profile where comm in (
      select comm from \`people.${export_comm_file_name}\` where comm <> '<EMAIL>'
    )` : null;
    const org_profiles = export_name_org_file ? `select distinct(profile) profile from people.orgs, unnest(profiles) profile where org in (
      select org from \`people.${export_name_org_file_name}\` where name <> 'holidays in united states'
    )` : null;

    let profiles = [];

    let retry = 100;
    while(retry < 3200) {
      try {
        const comm_results = comm_profiles ? await this.profileQuery(user, comm_profiles, {}) : [];
        const org_results = org_profiles ? await this.profileQuery(user, org_profiles , {}) : [];

        if (comm_results && comm_results.length) profiles = profiles.concat(comm_results);
        if (org_results && org_results.length) profiles = profiles.concat(org_results);
        break;
      } catch(err) {
        if (err.message && err.message.includes('Not found')) {
          logging.warnFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Retrying ${retry} querying for profiles`, err); 
          await setTimeout(retry);
          retry *= 2;
        }
        else {
          logging.errorFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Error querying for profiles`, err); 
          break;
        }
      }
    }

    logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Searching ${profiles.length} profiles`);

    const comm_condition = export_comm_file ? `c.comm in unnest(p.comms)` : '';
    const comm_table = export_comm_file ? `, \`people.${export_comm_file_name}\` c` : ''
    const name_org_condition = export_name_org_file ? `(lower(u.org) = lower(t.value) and t.type in unnest(['job', 'occupation', 'organization', 'department', 'jobTitle', 'jobDescription', 'ticker']) and
      lower(u.name) = lower(p.displayName))` : '';
    const org_table = export_name_org_file ? `, \`people.${export_name_org_file_name}\` u` : '';
    const tags = export_name_org_file ? ', unnest(tags) t' : '';

    const cond = comm_condition.length || name_org_condition.length ? 'and (' : '';
    const econd = comm_condition.length || name_org_condition.length ? ')' : '';
    const bcond = comm_condition.length && name_org_condition.length ? 'or' : '';

    const queries = profiles.map(profile => `select displayName, names, nickName, comms, learned, photos, urls, tags, "${profile}" profile from \`people.people_${this.safeProfile(profile)}\` p 
      where id in (select id from \`people.people_${this.safeProfile(profile)}\` p${tags}${comm_table}${org_table} 
      where (not network or network is null) ${cond} ${comm_condition} ${bcond} ${name_org_condition} ${econd})`);

    let result_people = []; 
    let query;
    retry = 100;
    while(retry < 3200) {
      try {
        let query_set: Promise<SimpleQueryRowsResponse | void>[] = []

        while(queries.length) {
          logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Searching up to 20 of ${queries.length} profiles`);
          query = `select displayName, names, nickName, comms, learned, photos, urls, tags, profile from (${queries.splice(0,20).join(' union all ')})`;
          if (logging.isDebug(user ? user.profile : null)) {
            logging.debugFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, util.format('SQL = %s', format(query, { language: "bigquery" })).replace(/\n/g, ' '));
            DEBUG('lookupPeople: %s', user ? user.profile : null);
            DEBUG('lookupPeople: %s SQL = %s', user ? user.profile : null, format(query, { language: "bigquery" }));
          }

          query_set.push(this.bq.query({query, useLegacySql: false})
            .catch(e => logging.errorFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Error querying for people`, e)));

          if (query_set.length === 50) {
            logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Waiting for ${query_set.length} searches`);
            const row_set = await Promise.all(query_set);
            const iresult = flatten(row_set.filter(r => r).map(r => r[0]));
            logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Adding ${iresult.length} matches to ${result_people.length}`);
            result_people = result_people.concat(iresult);
            query_set = [];
          }
        }

        logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Waiting for final ${query_set.length} searches`);
        const row_set = await Promise.all(query_set);
        result_people = result_people.concat(flatten(row_set.filter(r => r).map(r => r[0])));

        logging.infoFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Found ${result_people.length} matches`);
        // const [results] = await this.bq.query({ query, useLegacySql: false });
        // if (logging.isDebug(user ? user.profile : null)) DEBUG('lookupPeople: %s RESULTS = %j', user ? user.profile : null, results);
        // if (results && results.length) result_people = result_people.concat(results);
        break;
      } catch(err) {
        if (err.message && err.message.includes('Not found')) {
          logging.warnFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Retrying ${retry} querying for people`, err); 
          await setTimeout(retry);
          retry *= 2;
        }
        else {
          logging.errorFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, `Error querying for people`, err); 
          break;
        }
      }
    }

    if (export_comm_file ) await this.dropImportTable(user ? user.profile : null, export_comm_file_name );
    if (export_name_org_file) await this.dropImportTable(user ? user.profile : null, export_name_org_file_name );

    return this.peopleResults(user, result_people).map(pr => pr.person);
  }

  async profileQuery(user: User, query: string, params: {[key:string]: any}): Promise<Uid[]> {
    let profiles;
    let retry = 100;
    while(retry < 3200) {
      try {
        const [results] = await this.bq.query({query, params, useLegacySql: false });

        profiles = results.map(p => p.profile);
        break;
      } catch(err) {
        if (err.message && err.message.includes('Not found')) {
          logging.warnFP(LOG_NAME, 'profileQuery', user ? user.profile : null, `Retrying ${retry} querying for profiles`, err); 
          await setTimeout(retry);
          retry *= 2;
        } else if(retryError(err)) {
          logging.warnFP(LOG_NAME, 'profileQuery', user ? user.profile : null, `Retrying ${retry} querying for profiles`, err); 
          await setTimeout(retry);
        } else {
          logging.errorFP(LOG_NAME, 'profileQuery', user ? user.profile : null, `Error querying for profiles`, err); 
          break;
        }
      }
    }


    return this.checkProfiles(profiles);
  }

  async lookupPerson(user: User , comms: string[], urls: string[] = null): Promise<Partial<Person>> {
    if (comms.length === 0) return null;

    const simple_query = `select distinct(profile) profile from people.comms, unnest(profiles) profile where comm in unnest(@aliases_filter)`;
    const profile_query = urls && urls.length ? `select distinct(profile) profile from (
      select id, cc, cu, ARRAY(SELECT DISTINCT * from unnest(profiles)) profiles from (
      select id, count(comms) cc, count(urls) cu, array_concat_agg(profiles) profiles from people.PDL, unnest(comms) comm, unnest(urls) url where 
      lower(comm) in unnest(@aliases_filter) or lower(url) in unnest(@urls) 
      group by id ) order by cc desc) p, unnest(profiles) profile` : simple_query;
    let profiles = [];
    const params = { aliases_filter: comms };
    if (user) params['profile'] = user.profile;
    if (urls && urls.length) params['urls'] = urls;
    if (config.isEnvOffline()) params['profiles'] = profiles;

    profiles = await this.profileQuery(user, profile_query, params);
    if ((!profiles || !profiles.length) && urls && urls.length) {
      //retry with just comms
      profiles = await this.profileQuery(user, simple_query, params);
    }

    if (!profiles || !profiles.length) return null;

    params['profiles'] = profiles;

    const queries = profiles.map(profile => `select displayName, names, nickName, comms, learned, photos, urls, tags, "${profile}" profile from \`people.people_${this.safeProfile(profile)}\` 
      where id in (select id from \`people.people_${this.safeProfile(profile)}\`, unnest(comms) as comm where (not network or network is null) and comm in unnest(@aliases_filter))`);

    let result_people = []; 
    let query;
    let retry = 100;

    while(retry < 3200) {
      try {
        const query_set: Promise<SimpleQueryRowsResponse | void>[] = []

        while(queries.length) {
          query = `select displayName, names, nickName, comms, learned, photos, urls, tags, profile from (${queries.splice(0,50).join(' union all ')})`;
          if (logging.isDebug(user ? user.profile : null)) {
            logging.debugFP(LOG_NAME, 'lookupPeople', user ? user.profile : null, util.format('SQL = %s', format(query, { language: "bigquery" })).replace(/\n/g, ' '));
            DEBUG('lookupPeople: %s', user ? user.profile : null);
            DEBUG('lookupPeople: %s SQL = %s', user ? user.profile : null, format(query, { language: "bigquery" }));
          }

          query_set.push(this.bq.query({query, params, useLegacySql: false})
            .catch(e => logging.errorFP(LOG_NAME, 'lookupPerson', user ? user.profile : null, `Error querying for people`, e)));
        }

        const row_set = await Promise.all(query_set);
        result_people = flatten(row_set.filter(r => r).map(r => r[0]));

        break;
      } catch(err) {
        if (err.message && err.message.includes('Not found')) {
          logging.warnFP(LOG_NAME, 'lookupPerson', user ? user.profile : null, `Retrying ${retry} querying for people`, err); 
          await setTimeout(retry);
          retry *= 2;
        }
        else {
          logging.errorFP(LOG_NAME, 'lookupPerson', user ? user.profile : null, `Error querying for people`, err); 
          break;
        }
      }
    }

    if (result_people.length) {
      let person = new Person(result_people[0]);
      for(const p of result_people.slice(1)) {
        peopleUtils.mergePeople(person, p, true);
      }

      [{person}] = this.peopleResults(user, [person]);
      return person;
    }

    return null;
  }

  async lookupPersonOrgs(user: User, name: string, orgs: string[]): Promise<Partial<Person>> {
    if (orgs.length === 0 || name.length < 3) return null;

    const org_profiles = `select distinct(profile) profile from people.orgs, unnest(profiles) profile where lower(org) in unnest(@orgs)`;
    let profiles = [];
    const profile_params = { orgs: orgs.map(t => t.toLowerCase()) };
    if (user) profile_params['profile'] = user.profile;

    profiles = await this.profileQuery(user, org_profiles, profile_params);

    const params = { names: name.toLowerCase().split(' '), orgs: orgs.map(t => t.toLowerCase()) };
    if (user) params['profile'] = user.profile;

    const queries = profiles.map(profile => `select displayName, names, nickName, comms, learned, photos, urls, tags, "${profile}" profile from \`people.people_${this.safeProfile(profile)}\` where id in
      (select id from \`people.people_${this.safeProfile(profile)}\`, unnest(tags) as tag where
        (not network or network is null)
        and array_length(array(select * from unnest(@names) intersect distinct select * from unnest(split(lower(displayName), ' ')))) > 0
        and tag.index <> -1
        and tag.type in unnest(['job', 'occupation', 'organization', 'department', 'jobTitle', 'jobDescription', 'ticker']) 
        and lower(tag.value) in unnest(@orgs))`);

    let result_people = []; 
    let query;
    let retry = 100;
    while(retry < 3200) {
      try {
        const query_set: Promise<SimpleQueryRowsResponse | void>[] = []

        while(queries.length) {
          query = `select displayName, names, nickName, comms, learned, photos, urls, tags, profile from (${queries.splice(0,50).join(' union all ')})`;
          if (logging.isDebug(user ? user.profile : null)) {
            logging.debugFP(LOG_NAME, 'lookupPersonOrgs', user ? user.profile : null, util.format('SQL = %s', format(query, { language: "bigquery" })).replace(/\n/g, ' '));
            DEBUG('lookupPersonOrgs: %s', user ? user.profile : null);
            DEBUG('lookupPersonOrgs: %s SQL = %s', user ? user.profile : null, format(query, { language: "bigquery" }));
          }

          query_set.push(this.bq.query({query, params, useLegacySql: false})
            .catch(e => logging.errorFP(LOG_NAME, 'lookupPersonOrgs', user ? user.profile : null, `Error querying for people orgs`, e)));
        }

        const row_set = await Promise.all(query_set);
        result_people = flatten(row_set.filter(r => r).map(r => r[0]));

        // const [results] = await this.bq.query({ query, params, useLegacySql: false });
        // if (logging.isDebug(user ? user.profile : null)) DEBUG('lookupPersonOrgs: %s RESULTS = %j', user ? user.profile : null, results);
        // if (results && results.length) result_people = result_people.concat(results);
        break;
      } catch(err) {
        if (err.message && err.message.includes('Not found')) {
          logging.warnFP(LOG_NAME, 'lookupPersonOrgs', user ? user.profile : null, `Retrying ${retry} querying for people`, err); 
          await setTimeout(retry);
          retry *= 2;
        }
        else {
          logging.errorFP(LOG_NAME, 'lookupPersonOrgs', user ? user.profile : null, `Error querying for people`, err); 
          break;
        }
      }
    }

    if (result_people.length) {
      let person = new Person(result_people[0]);
      for(const p of result_people.slice(1)) {
        peopleUtils.mergePeople(person, p, true);
      }

      [{person}] = this.peopleResults(user, [person]);
      return person;
    }

    return null;
  }

  async userPeopleByVanity(user: User, vanities: string[], enrich = false): Promise<Partial<Person>[]> {
    if (!vanities || !vanities.length) return [];
    const params = { vanities };

    let query = `select displayName, names, nickName, comms, learned, photos, urls, tags, vanity, profile as id from people.users 
      where vanity in unnest(@vanities)`;

    const profiles = await this.checkProfiles([user.profile]);

    if (enrich && profiles && profiles.length) {
      query = `with u as (${query}),
        p as (select id, comm from people.people_${profiles[0]}, unnest(comms) comm)
        select (case when p.id is not null then p.id else u.id end) id,
               (case when p.id is not null then null else u.id end) profile,
                u.displayName, u.names, u.nickName, u.comms, u.learned, u.photos, u.urls, u.tags, u.vanity from u
        left join p on p.comm in unnest(u.comms)`
    }

    try {
      const [results] = await this.bq.query({ query, params, useLegacySql: false });
      if (logging.isDebug(user ? user.profile : null)) DEBUG('userPeopleByVanity: %s RESULTS = %j', user ? user.profile : null, results);

      if (!results) return [];

      return this.peopleResults(user, results, !enrich).map(pr => pr.person);
    } catch (err) { logging.errorFP(LOG_NAME, 'userPeopleByVanity', user ? user.profile : null, `Error querying for users`, err); }
  }

  async userPeopleStats(user: User, profiles: Uid[], terms: string[], filter_skills?: string[], page?: number): Promise<{person: Partial<Person>, stats: ScoreStat}[]> {
    if (!profiles || !profiles.length || !terms || !terms.length) return [];

    const vector = await skillVector(terms);
    const vectors = await this.termVectors(terms);
    const vector_filter = vectors.map((v,i) => `1 - ml.distance(vector, @vector_${i}, 'COSINE') >= ${COSINE_THRESHOLD}`).join(' or ');

    const params = { profiles, vector, terms }; // { profiles: profiles.map(this.safeProfile) };
    if (filter_skills && filter_skills.length) params['filter_skills'] = `\\b(${filter_skills.map(f => f.toLowerCase()).join('|')})\\b`;

    for(const x in vectors) params[`vector_${x}`] = vectors[x];

    let do_page = page !== undefined ? `order by split(displayName, ' ')[array_length(split(displayName, ' ')) - 1] asc limit 1000 offset ${page * 1000}` : '';
    const skill_filter = filter_skills && filter_skills.length ? `and users.profile not in (
      select profile from people.users, unnest(tags) where tag.index <> -1 and REGEXP_CONTAINS(lower(tag.value), @filter_skills))` : '';

    const filter = `id_set as (
        select distinct profile from (
          select users.profile from people.users, unnest(tags) tag
          join people.skillcategory on tag.value in unnest(skills)
          where tag.index <> -1
          and (${vector_filter})
          and users.profile in unnest(@profiles)
          ${skill_filter}
          ${page !== undefined ? "order by 1 - ml.distance(vector, @vector, 'COSINE') desc limit 1000" : ''}
        )
      )
    `;

    const wp = `wp as (select * from people.words where name in ( select distinct lower(tag.value) from people.users, unnest(tags) tag where profile in (select profile from id_set) and tag.index <> -1))`;
    const wt = `wt as (select * from people.words where name in unnest(@terms))`;

    // count(*) score_cnt,
    // sum(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_sum, 
    // max(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_max, 
    // min(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_min, 
 

    const stats = `scores as (
      select profile sprofile, 
        array_agg(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) weight 
        from people.users, unnest(tags) tag, unnest(@terms) term
        join wt on wt.name = term
        join wp on wp.name = tag.value
        where profile in (select profile from id_set) and tag.index <> -1 and 1 - ml.distance(wt.vector, wp.vector, 'COSINE') > ${COSINE_THRESHOLD}
          group by profile
      )
    `;
 
    // array_concat(@terms, split(array_to_string(@terms, " "), " ")))
    // score_cnt, score_sum, score_max, score_min, 

    const query = `with ${filter}, ${wp}, ${wt}, ${stats}
      select displayName, names, nickName, comms, learned, photos, urls, tags, vanity, profile as id,
      (select avg(w) from unnest(weight) w) score_avg, (select stddev(w) from unnest(weight) w) score_stddev 
      from people.users, scores where profile = sprofile
      ${do_page}`;

    try {
      const results = await this.retryQuery<PersonStat>(user ? user.profile : null, 'userPeopleStats', query, params);
      if (logging.isDebug(user ? user.profile : null)) DEBUG('userPeople: %s RESULTS = %j', user ? user.profile : null, results);

      if (!results) return [];

      return this.peopleResults(user, results, true, page);
    } catch (err) { logging.errorFP(LOG_NAME, 'userPeopleStats', user ? user.profile : null, `Error querying for users`, err); }
  }

  async userPeople(user: User, profiles: Uid[], tag_types?: TagType[], tag_values?: string[], page?: number): Promise<Partial<Person>[]> {
    if (!profiles || !profiles.length) return [];
    const params = { profiles }; // { profiles: profiles.map(this.safeProfile) };

    let tags = '';
    if (tag_types && tag_types.length && tag_values && tag_values.length) {
      params['tag_types'] = tag_types;

      if (tag_values.length > 50) {
        logging.warnFP(LOG_NAME, 'userPeople', user?.profile, `Only querying first 50 terms of ${tag_values.length}`);
      }

      const term_filters = [];
      tag_values.slice(0,50).forEach((p, i) => {
        term_filters.push(`lower(tag.value) like @tag_value_${i}`);
        if (p.length > 2) params[`tag_value_${i}`] = `%${p.toLowerCase()}%`;
        else params[`tag_value_${i}`] = p.toLowerCase();
      });

      tags = `and tag.type in unnest(@tag_types) and (${term_filters.join(' or ')})`;
    }

    let do_page = page !== undefined ? `order by split(displayName, ' ')[array_length(split(displayName, ' ')) - 1] asc limit 1000 offset ${page * 1000}` : '';

    const query = `select displayName, names, nickName, comms, learned, photos, urls, tags, vanity, profile as id from people.users 
      ${tags && tags.length ? ', unnest(tags) tag' : ''}
      where profile in unnest(@profiles)
      ${tags} ${do_page}`;

    try {
      const results = await this.retryQuery<Person>(user?.profile, 'userPeopleBy', query, params);
      if (logging.isDebug(user?.profile)) DEBUG('userPeople: %s RESULTS = %j', user?.profile, results);

      if (!results) return [];

      return this.peopleResults(user, results, true, page).map(pr => pr.person);
    } catch (err) { logging.errorFP(LOG_NAME, 'userPeople', user?.profile, `Error querying for users`, err); }

  }

  async userSearchStats(user: User, group_ids: Uid[], global: boolean, user_comms: string[], terms: string[]): Promise<{person: Partial<Person>, stats: ScoreStat}[]> {
    if (!terms || !terms.length) return [];

    const vector = await skillVector(terms);
    const vectors = await this.termVectors(terms);
    const vector_filter = vectors.map((v,i) => `1 - ml.distance(vector, @vector_${i}, 'COSINE') >= ${COSINE_THRESHOLD}`).join(' or ');

    const params = { terms, vector };

    for(const x in vectors) params[`vector_${x}`] = vectors[x];

    let exclude_comms = '';
    if (user && user.vanity) {
      params['profile'] = user.profile; 
      params['vanity'] = user.vanity;
      exclude_comms = `OR comm in (select distinct(comm) from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm where (not network or network is null))`;
    }

    let exclude_profiles = '';
    if (user_comms && user_comms.length) {
      params['user_comms'] = user_comms;
      exclude_profiles = `OR profile in (select distinct(profile) profile from people.comms, unnest(profiles) profile where comm in unnest(@user_comms))`
    }

    const profile = user ? `
      and profile not in (
        select profile from people.users, unnest(comms) comm where
        vanity = @vanity
        ${exclude_comms}
        ${exclude_profiles}
      )` : '';

    let datasets = global ? ['people'] : [];
    if (group_ids) {
      const exists = await Promise.all(group_ids.map(async group_id => {
        const exists = await group_ids.map(this.checkGroup);
        return {exists, group_id};
      }));

      datasets = datasets.concat(group_ids.filter(g => exists.find(e => e.exists && e.group_id === g)).map(g => this.safeProfile(g)));
    }

    const group_cats = datasets.map(d => `select vector from \`${d}.skillcategory\``)

    const cat_union  = `cat_set as (
      ${group_cats.join(' union all ')}
    )`;

    const filter = `id_set as ( 
      select distinct(profile) from (
        select users.profile from people.users, cat_union
        where ${vector_filter}
        ${profile}
      )
    )`;

    const wp = `wp as (select * from people.words where name in ( select distinct lower(tag.value) from people.users, unnest(tags) tag where profile in (select profile from id_set) and tag.index <> -1))`;
    const wt = `wt as (select * from people.words where name in unnest(@terms))`;

    //         count(*) score_cnt,
    // sum(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_sum, 
    //  max(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_max, 
    //  min(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_min, 

    const stats = `scores as (
      select profile sprofile, 
        array_agg(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) weight
        from people.users, unnest(tags) tag, unnest(@terms) term
        join wt on wt.name = term
        join wp on wp.name = tag.value
        where profile in (select profile from id_set) and tag.index <> -1
          group by profile
      )`;

    // array_concat(@terms, split(array_to_string(@terms, " "), " ")))
    // score_cnt, score_sum, score_max, score_min, 

    const query = `with ${cat_union}, ${filter}, ${wp}, ${wt}, ${stats}
      select displayName, names, nickName, comms, learned, photos, urls, tags, vanity, profile as id,
      (select avg(w) from unnest(weight) w) score_avg, (select stddev(w) from unnest(weight) w) score_stddev 
      from people.users, scores where profile = sprofile`;
    
    try {
      const results = await this.retryQuery<PersonStat>(user ? user.profile : null, 'userSearchStats', query, params);
      if (logging.isDebug(user ? user.profile : null)) DEBUG('userSearchStats: %s RESULTS = %j', user ? user.profile : null, results);

      if (!results) return [];

      return this.peopleResults(user, results, true);
    } catch (err) { logging.errorFP(LOG_NAME, 'userSearchStats', user ? user.profile : null, `Error querying for users`, err); }
  }

  async userSearch(user: User, user_comms: string[], tag_types: TagType[], tag_values: string[]): Promise<Partial<Person>[]> {
    if (tag_types.length === 0 || tag_values.length === 0) return [];

    const params = { tag_types, tag_values };

    let exclude_comms = '';
    if (user && user.vanity) {
      params['profile'] = user.profile; 
      params['vanity'] = user.vanity;
      exclude_comms = `OR comm in (select distinct(comm) from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm where (not network or network is null))`;
    }

    let exclude_profiles = '';
    if (user_comms && user_comms.length) {
      params['user_comms'] = user_comms;
      exclude_profiles = `OR profile in (select distinct(profile) profile from people.comms, unnest(profiles) profile where comm in unnest(@user_comms))`
    }

    const profile = user ? `
      and profile not in (
        select profile from people.users, unnest(comms) comm where
        vanity = @vanity
        ${exclude_comms}
        ${exclude_profiles}
      )` : '';
 
    const query = `select displayName, names, nickName, comms, learned, photos, urls, tags, vanity from people.users where id in 
      (select distinct(id) from people.users, unnest(tags) as tag 
        where tag.index <> -1 and tag.type in unnest(@tag_types) and lower(tag.value) in unnest(@tag_values))
        ${profile}
      `;

    if (logging.isDebug(user ? user.profile : null)) {
      logging.debugFP(LOG_NAME, 'userSearch', user ? user.profile : null, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));
      DEBUG('usersSearch: %s QP = %j', user ? user.profile : null, params);
      DEBUG('userSearch: %s SQL = %s', user ? user.profile : null, format(query, { language: "bigquery" }));
    }

    try {
      const results = await this.retryQuery<Person>(user.profile, 'userSearch', query, params);
      if (logging.isDebug(user ? user.profile : null)) DEBUG('userSearch: %s RESULTS = %j', user ? user.profile : null, results);

      if (!results) return [];

      return this.peopleResults(user, results, true).map(pr => pr.person);
    } catch (err) { logging.errorFP(LOG_NAME, 'userSearch', user ? user.profile : null, `Error querying for users ${JSON.stringify(tag_types)} ${JSON.stringify(tag_values)}`, err); }
  }

  async userPeopleByEmail(email: string | string[]): Promise<Person[]> {
    // if (config.isEnvOffline()) return [];

    const query = `select displayName, names, nickName, comms, learned, photos, urls, tags, vanity from people.users, unnest(comms) comm where comm in unnest(@aliases_filter);`
    const params = { aliases_filter: Array.isArray(email) ? email : [email] };
    if (logging.isDebug()) {
      logging.debugF(LOG_NAME, 'userPeopleByEmail', `${query} :: ${JSON.stringify(params)}`);
    }

    try {
      const [results] = await this.bq.query({query, params, useLegacySql: false});
      if (results) {
        const people = (results as Partial<Person>[]).filter(p => !config.isEnvOffline() || !p.id.startsWith('people/r')).map(p => {
          const np = new Person(p);
          if (np.vanity) np.id = `profile/${np.vanity}`;
          return np;
        });
        return people;
      }
      return [];
    } catch (err) { logging.errorF(LOG_NAME, 'userPeoplebyEmail', `Error querying for users ${query}`, err); }
  }

  async connectedProfiles(user: User, terms: string[], emails: string[]): Promise<Uid[]> {
    if ( (!terms || !terms.length) &&
        (!emails && !emails.length)) {
      logging.warnFP(LOG_NAME, 'connectedProfiles', user.profile, 'skipping search with no skills');
      return [];
    }


    if (terms && terms.length > 50) {
      logging.warnFP(LOG_NAME, 'connectedProfiles', user.profile, `Only querying first 50 terms of ${terms.length}: ${terms}`);
      terms.splice(50);
    }

    const profiles = (await this.networkProfiles(user, emails)).filter(p => p !== user.profile);
    if (!profiles.length) return [];
    const params: any = { profile: user.profile, profiles };

    const qterms = [];
    if (terms) {
      for (let i = 0; i < terms.length; i++) {
        const term = terms[i];
        qterms.push(`lower(skill) like @tag_value_${i}`)
        params[`tag_value_${i}`] = `%${term.toLowerCase()}%`;
      }
    }

    const qt_query = qterms && qterms.length ?  `where (${qterms.join(' or ')})` : '';

    const query = `select profile from ( 
        select distinct(pm.profile) profile from people.user_skills, unnest(profile_matches) pm 
        ${qt_query}) 
      where profile not in unnest(@profiles);
    `;

    if (logging.isDebug(user.profile)) {
      DEBUG('connectedProfiles: %s QP = %j', user.profile, params);
      DEBUG('connectedProfiles: %s SQL = %s', user.profile, format(query, { language: "bigquery" }));
    }

    try {
      const [results] = await this.bq.query({ query, params, useLegacySql: false });
      if (!results || !results.length) return [];
      return results.map(r => r['profile']).filter(p => p && p.length); 
    } catch (err) {
      logging.errorFP(LOG_NAME, 'connectedProfiles', user.profile, `Error querying for connected people ${JSON.stringify(terms)} ${query} ${JSON.stringify(params)}`, err);
    }

    return [];
  }

  async networkProfiles(user: User, user_comms: string[]): Promise<Uid[]> {
    if (!user) throw new InternalError(500, 'No user for network profiles');
    if (!user.profile) throw new InternalError(500, 'No user profile for network profiles');
    if (!user_comms || !user_comms.length) throw new InternalError(500, 'No user comms for network profiles');

    const params = { profile: user.profile, user_comms };
    const query = `select distinct(profile) profile from people.comms, unnest(profiles) profile where comm in unnest(@user_comms)`;

    const all_profiles = await this.profileQuery(user, query, params);

    const global_users = await data.users.globalByIds(all_profiles);
    const group_ids = flatten(global_users.map(g => g.groups ? g.groups : []));
    const groups = group_ids && group_ids.length ? await data.groups.byIds(group_ids) : [];
    const blocked_groups = groups.filter(g => g.block_groups_contacts).map(g => g.id);

    const ignore_profiles = [];
    const user_groups = user.groups ? Object.keys(user.groups) : [];
    if (blocked_groups.length) {
      for (const u of global_users) {
        if (u.groups && _.intersection(u.groups, blocked_groups).length > 0 &&
          _.intersection(u.groups, user_groups).length === 0) ignore_profiles.push(u.id);
      }
    }

    return this.checkProfiles(all_profiles.filter(profile => !ignore_profiles.includes(profile))); 
  }

  async knownProfiles(user: User): Promise<Uid[]> {
    const profiles = await this.checkProfiles([user.profile]);
    if (profiles && profiles.length) {
      const profile = profiles[0];
      const params = { profile: user.profile };
      const query = `select distinct(profile) profile from people.users, unnest(comms) comm where comm in (select distinct(comm) from people.people_${profile}, unnest(comms) comm) and profile <> @profile`

      const all_profiles = await this.profileQuery(user, query, params);

      const global_users = await data.users.globalByIds(all_profiles);
      const group_ids = flatten(global_users.map(g => g.groups ? g.groups : []));
      const groups = group_ids && group_ids.length ? await data.groups.byIds(group_ids) : [];
      const blocked_groups = groups.filter(g => g.block_groups_contacts).map(g => g.id);

      const ignore_profiles = [];
      const user_groups = user.groups ? Object.keys(user.groups) : [];
      if (blocked_groups.length) {
        for (const u of global_users) {
          if (u.groups && _.intersection(u.groups, blocked_groups).length > 0 &&
            _.intersection(u.groups, user_groups).length === 0) ignore_profiles.push(u.id);
        }
      }

      return this.checkProfiles(all_profiles.filter(profile => !ignore_profiles.includes(profile))); 
    }

    return [];
  }

  async checkProfiles(all_profiles: Uid[]): Promise<Uid[]> {
    if (!all_profiles || !all_profiles.length) return [];

    let filter_profiles:{profile:string; exists: boolean}[] = [];

    while(all_profiles.length) {
      const part_filter_profiles:{profile:string; exists: boolean}[] = config.isEnvOffline() ? all_profiles.splice(0,500).map(profile => {
        return { profile, exists: true }
      }) : await Promise.all(all_profiles.splice(0,10).map(async (profile) => {
        try {
          if (profile.startsWith('g-')) {
            logging.warnF(LOG_NAME, 'checkProfiles', `Checking for guest profile from ${formatStack(new Error().stack)}`);
            return { profile, exists: false }; 
          }
          if (profile === FORA_PROFILE) {
            logging.warnF(LOG_NAME, 'checkProfiles', `Checking for anonymous profile from ${formatStack(new Error().stack)}`);
            return { profile, exists: false }; 
          }

          profile = this.safeProfile(profile);
          const [exists] = await this.bq.dataset('people').table(`people_${profile}`).exists();
          if (!exists) logging.warnF(LOG_NAME, 'checkProfiles', `No table for ${profile}`);
          return { profile, exists }
        } catch(err) {
          logging.errorF(LOG_NAME, 'checkProfiles', `Error checking profile ${profile}`, err);
        }
        return { profile, exists: false }
      }));

      filter_profiles = filter_profiles.concat(part_filter_profiles);
    }

    return filter_profiles.filter(({profile, exists}) => exists).map(({profile, exists}) => profile);
  }

  async checkGroup(group_id: Uid): Promise<boolean> {
    group_id = this.safeProfile(group_id);
    const [exists] = await this.bq.dataset(group_id).exists();
    return exists;
  }

  async createGroup(group_id: Uid): Promise<boolean> {
    group_id = this.safeProfile(group_id);
    let [exists] = await this.bq.dataset(group_id).exists();
    if (!exists) {
      const [create] = await this.bq.dataset(group_id).create();
      if (create) [exists] = await this.bq.dataset(group_id).exists();
    }
    return exists;
  }

  async queryPerson(user: User, terms: string[], names?: string[], emails?: string[], aliases?: string[], additional_ids?: Uid[], require_email = false, network_only = false): Promise<Partial<Person>[]> {
    if ( (!terms || !terms.length) &&
        (!names || !names.length) &&
        (!emails && !emails.length)) {
      logging.warnFP(LOG_NAME, 'queryPerson', user.profile, 'skipping search with no skills');
      return [];
    }

    if (terms) {
      if(!(terms instanceof Array)) terms = [terms];
      terms = terms.filter(t => t.length > 1);
    }

    if (terms && terms.length > 50) {
      logging.warnFP(LOG_NAME, 'queryPerson', user.profile, `Only querying first 50 terms of ${terms.length}: ${terms}`);
      terms.splice(50);
    }

    let network = false;
    if (aliases) network = true;
    else aliases = [user.email];

    let profiles = additional_ids ? await this.checkProfiles(additional_ids) : [];

    if (network) {
      const net_profiles = await this.networkProfiles(user, aliases);
      if (net_profiles) profiles = _.uniq(profiles.concat(net_profiles));
    }

    let exclude_comms = '';
    if (network_only) {
      profiles = profiles.filter(p => p !== user.profile);
      exclude_comms = `OR comm in (select distinct(comm) from people.people_${this.safeProfile(user.profile)}, unnest(comms) comm where (not network or network is null))`;
    } else if(!profiles.includes(user.profile)) profiles.push(user.profile);

    if (names && names.length) {
      const profile_params = { profiles, names };
      const profile_query = `select distinct(profile) profile from people.names, unnest(profiles) profile
        where name in unnest(@names) and profile in unnest(@profiles)`

      try {
        const found_profiles = await this.retryQuery<{profile:Uid}>(user.profile, 'queryPerson', profile_query, profile_params);
        profiles = found_profiles ? found_profiles.map(({profile}) => profile) : [];
      } catch(err) {
        logging.errorFP(LOG_NAME, 'queryPerson', user.profile, `Error looking up matching profiles for names`, err);
        return [];
      }
    }

    const params: any = { profile: user.profile, profiles, aliases };
    const attrs = [];
    const filters = [];

    if (names && names.length) {
      const name_filters = [];
      attrs.push('unnest(names) name');
      names.forEach((n, i) => {
        name_filters.push(`lower(name) like @name_value_${i}`);
        params[`name_value_${i}`] = `%${n.toLowerCase()}%`;
      });
      filters.push(`(${name_filters.join(' or ')})`);
    }

    if (terms && terms.length) {
      const term_filters = [];
      attrs.push('unnest(tags) tag');
      terms.forEach((p, i) => {
        term_filters.push(`tag.index <> -1 and lower(tag.value) like @tag_value_${i}`);
        if (p.length > 2) params[`tag_value_${i}`] = `%${p.toLowerCase()}%`;
        else params[`tag_value_${i}`] = p.toLowerCase();
      });
      filters.push(`(${term_filters.join(' or ')})`);
    }

    if ((emails && emails.length) || require_email) {
      const email_filters = [];
      attrs.push('unnest(comms) comm');
      if (require_email) email_filters.push("comm like '%@%'");
      if (emails) {
        emails.forEach((e, i) => {
          email_filters.push(`lower(comm) like @email_value_${i}`);
          params[`email_value_${i}`] = `%${e.toLowerCase()}%`;
        });
      }
      filters.push(`(${email_filters.join(' or ')})`);
    }

    if (!profiles.length) return [];

    const queries = profiles.map(p => `select id, tags, "${p}" profile
      from people.people_${this.safeProfile(p)}${attrs.length ? ',' : ''} ${attrs.join(', ')}
      where id not in (
        select id from people.people_${this.safeProfile(p)}, unnest(comms) comm where
        comm in unnest(@aliases) or displayName is null or length(displayName) = 0 ${exclude_comms}
      ) ${filters.length ? 'and' : ''} ${filters.join(' and ')} and (network is null or not network)
    `);

    const fetch_params: any = { profile: user.profile, profiles };
    const profile_ids = {};
    let query;
    try {
      const query_set: Promise<SimpleQueryRowsResponse | void>[] = []

      while(queries.length) {
        query = `select id, profile, array_length(array_concat_agg(tags)) tag_count from (${queries.splice(0,50).join(' union all ')}) group by id, profile order by tag_count desc limit 100`;
        if (logging.isDebug(user.profile)) {
          logging.debugFP(LOG_NAME, 'queryPerson', user.profile, util.format('QP = %j SQL = %s', params, format(query, { language: "bigquery" })).replace(/\n/g, ' '));
          DEBUG('queryPerson: %s QP = %j', user.profile, params);
          DEBUG('queryPerson: %s SQL = %s', user.profile, format(query, { language: "bigquery" }));
        }

        query_set.push(this.bq.query({query, params, useLegacySql: false})
          .catch(e => logging.errorFP(LOG_NAME, 'queryPerson', user ? user.profile : null, `Error querying for people`, e)));
      }

      // yes it's triple array: bq returns [result], we're waiting on an arry of them and result itself is an array 
      const row_set = (await Promise.all(query_set)) as any as {id: Uid; profile: Uid; tag_count: number}[][][];
      const filters = flatten(row_set.filter(r => r).map(r => r[0]));

      // const [filters] = await this.bq.query({ query, params, useLegacySql: false });
      // if (!filters || !filters.length) return [];

      for(const f of filters.filter(f => f)) {
        const param_profile = this.safeProfile(f.profile);
        if (profile_ids[f.profile]) {
          profile_ids[f.profile].push(f.id);
          fetch_params[`ids_${param_profile}`].push(f.id);
        } else {
          profile_ids[f.profile] = [f.id];
          fetch_params[`ids_${param_profile}`] = [f.id];
        }
      }
    } catch(err) {
      logging.errorFP(LOG_NAME, 'queryPerson', user.profile, `Error querying for people by skill ${JSON.stringify(terms)} ${query ? query : 'no query'} ${JSON.stringify(params)}`, err);
    }

    if (Object.keys(profile_ids).length) {
      let fetch_query;

      try {
        // Note: anyone not from this user profile or not a group profile (e.g. xxx.askfora.com) is a network contact
        const fetch_queries = Object.keys(profile_ids).map(p => `select id, displayName, names, nickName, comms, learned, photos, urls, tags,
          ${p !== user.profile && !p.endsWith('.askfora.com')} network, "${p}" profile from people.people_${this.safeProfile(p)}
          where id in unnest(@ids_${this.safeProfile(p)})
        `);

        fetch_query = `${fetch_queries.join(' union all ')} order by profile, id`;

        if (logging.isDebug(user.profile)) {
          logging.debugFP(LOG_NAME, 'queryPerson', user.profile, util.format('QP = %j SQL = %s', fetch_params, format(fetch_query, { language: "bigquery" })).replace(/\n/g, ' '));
          DEBUG('queryPerson: %s QP = %j', user.profile, fetch_params);
          DEBUG('queryPerson: %s SQL = %s', user.profile, format(fetch_query, { language: "bigquery" }));
        }

        const [results] = await this.bq.query({ query: fetch_query, params: fetch_params, useLegacySql: false });

        return this.peopleResults(user, results).map(pr => pr.person); 
      } catch (err) {
        logging.errorFP(LOG_NAME, 'queryPerson', user.profile, `Error fetching people by skill ${JSON.stringify(terms)} ${fetch_query ? fetch_query : 'no fetch query'} ${JSON.stringify(fetch_params)}`, err);
      }
    }

    return [];
  }

  async findBySkills(user: User, terms: string[], filter_skills?: string[], emails?: string[], aliases?: string[], additional_ids?: Uid[], require_email = false, network_only = false): Promise<{person: Partial<Person>, stats: ScoreStat}[]> {
    if (!terms || !terms.length) return [];

    const email_filter = emails && emails.length ? `, unnest(comms) ecomm${filter_skills && filter_skills.length ? ', unnest(tags) etag' : ''} where lower(ecomm) in unnest(@emails) or` : `${filter_skills && filter_skills.length ? ', unnest(tags) etag' : ''} where`;
    const skill_filter = filter_skills && filter_skills.length ? `(etag.index <> -1 and REGEXP_CONTAINS(lower(etag.value), @filter_skills)) or` : '';

    let network = false;
    if (aliases) network = true;
    else aliases = [user.email];

    let profiles = additional_ids ? await this.checkProfiles(additional_ids) : [];

    if (network) {
      const net_profiles = await this.networkProfiles(user, aliases);
      if (net_profiles) profiles = _.uniq(profiles.concat(net_profiles));
    }

    if (network_only) profiles = profiles.filter(p => p !== user.profile);
   
    const filter_queries = profiles.map(p => {
      const table = `people_${this.safeProfile(p)}`;
      return `select distinct("${p}") profile from people.${table}` 
    });

    let filter_profiles = network_only ? [] : [user.profile];
    while(filter_queries.length) {
      await Promise.all(filter_queries.splice(0,50).map(async query => {
        try {
          const results = await this.retryQuery<{profile:string}>(user.profile, 'findPeopleStats', query); //, user_params);
          if (results && results.length) filter_profiles.push(results[0].profile);
        } catch(e) {
          logging.errorF(LOG_NAME, 'findBySkills', `Error filtering profiles`, e);
        }
      }));
    }

    const queries = filter_profiles.map(p => {
      const table = `people_${this.safeProfile(p)}`;
      const require_email_filter = require_email ? ` and ${table}.id in ( select id from people.${table}, unnest(comms) comm where comm like "%@%" )` : '';

      const filter = `id_set as (
          select distinct id from (
            select ${table}.id from people.${table}, unnest(comms) comm
            where ${table}.id not in ( 
              select id from people.${table} ${email_filter} ${skill_filter} network
            ) 
            ${require_email_filter}
            limit 100 
          )
        )`;

      const wp = `wp as (select * from people.words where name in ( select distinct lower(tag.value) from people.${table}, unnest(tags) tag where id in (select id from id_set) and tag.index <> -1))`;
      const wt = `wt as (select * from people.words where name in unnest(array_concat(@terms, split(array_to_string(@terms, " "), " "))))`;

      //           count(*) score_cnt,
      //  sum(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_sum, 
      //  max(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_max, 
      //  min(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) score_min, 

      const stats = `scores as (
        select id sid, 
          array_agg(1 - ml.distance(wt.vector, wp.vector, 'COSINE')) weight 
          from people.${table}, unnest(tags) tag, unnest(array_concat(@terms, split(array_to_string(@terms, " "), " "))) term
          join wp on wp.name = tag.value 
          join wt on wt.name = term
          where id in (select id from id_set) and tag.index <> -1
            group by id
        )`;

      // array_concat(@terms, split(array_to_string(@terms, " "), " ")))
      // score_cnt, score_sum, score_max, score_min, 

      const query = `with ${filter}, ${wp}, ${wt}, ${stats}
        select id, displayName, names, nickName, comms, learned, photos, urls, tags, 
        ${p !== user.profile && !p.endsWith('.askfora.com')} network, "${p}" profile,
        (select avg(w) from unnest(weight) w) score_avg, (select stddev(w) from unnest(weight) w) score_stddev 
        from people.${table}, scores
        where id = sid`;
      
      return query;
    });

    const params = { terms };
    /*vectors.forEach((v,i) => {
      params[`vector${i}`] = v;
    });*/
    if (emails && emails.length) params['emails'] = emails.map(e => e.toLowerCase());
    if (filter_skills && filter_skills.length) params['filter_skills'] = `\\b(${filter_skills.map(f => f.toLowerCase()).join('|')})\\b`;

    let result_set = [];
    while(queries.length) {
      await Promise.all(queries.splice(0,50).map(async query => {
        try {
          const results = await this.retryQuery<PersonStat>(user.profile, 'findPeopleStats', query, params);
         if (results) results.forEach(r => result_set.push(r));

        } catch(e) {
          logging.errorF(LOG_NAME, 'findBySkills', `Error finding people`, e);
        }
      }));
    }

    return this.peopleResults(user, result_set);
 }

  peopleResults(user: User, results: any[], network = false, page = undefined): {person: Person, stats: ScoreStat}[] {
    const stat_people: {person: Person, stats: ScoreStat} [] = [];

    let people: {person: Person, stats: ScoreStat} [] = [];
    let last_profile;
    let last_person;
    if (results && results.length) {
      if (logging.isDebug(user ? user.profile : null)) DEBUG('peopleResults: results %o', results);
      const people_comms = {};
      // const rset = results as Person[];
      for (const p of results) {
        const person = new Person(p);

        const stats = p.score_avg ? {
          // score_cnt: p['score_cnt'],
          // score_sum: p['score_sum'],
          // score_max: p['score_max'],
          // score_min: p['score_min'],
          score_avg: p['score_avg'],
          score_stddev: p['score_stddev'],
        } : null;

        if (person.vanity) person.id = `profile/${person.vanity}`;

        if (network || !user || (p['profile'] !== undefined && p['profile'] !== null && p['profile'] !== user.profile)) person.network = true;

        if (person.id) {
          if (last_person && last_person.id === person.id) {
            if (!last_profile || last_profile === p['profile']) peopleUtils.mergePeople(last_person, person, true);
            else {
              last_profile = p['profile'];
              last_person = person;
              stat_people.push({person, stats});
            }
          } else {
            last_profile = p['profile'];
            last_person = person;
            stat_people.push({person, stats});
          }
        } else {
          last_person = person;
          stat_people.push({person, stats});
        }
      }

      for (const sp of stat_people) {
        const merge_with: Person[] = [];
        for (const comm of sp.person.comms) {
          if (comm in people_comms) {
            merge_with.push(people_comms[comm]);
          }
        }

        if (merge_with.length) {
          merge_with.push(sp.person);
          for (const comm of sp.person.comms) if (!people_comms[comm]) people_comms[comm] = sp.person;

          // separate out the in vs out of network
          const merge_first = merge_with.filter(m => !m.network);

          if (merge_first.length) {
            // grab the skills
            for (const other_match of merge_with) {
              if (!sp.person.id || !other_match.id || sp.person.id !== other_match.id) mergeTags(sp.person.tags, other_match.tags);
            }

            // only keep (and merge) first connections
            people = people.filter(p => _.intersection(sp.person.comms, p.person.comms).length === 0 || !sp.person.network);

            // add this person if they're not in the network
            if (!sp.person.network) people.push(sp);
          } else {
            // merge into the first comm match
            const merge_into = merge_with[0];
            const merged_ids = [merge_with[0].id];
            const np = new Person(sp.person);
            peopleUtils.mergePeople(merge_into, np, true);
            for (const other_match of merge_with.slice(1)) {
              if (!other_match.id || !merged_ids.includes(other_match.id)) {
                peopleUtils.mergePeople(merge_into, other_match, true);
                if (other_match.id) merged_ids.push(other_match.id);
              }
            }
          }
        } else {
          people.push(sp);
          for (const comm of sp.person.comms) people_comms[comm] = sp.person;
        }
      }
    }

    const u_people = _.uniqBy(people, p => p.person.id ? p.person.id : 
      p.person.vanity ? p.person.vanity : 
      p.person.comms && p.person.comms.length ?  p.person.comms[0] : hash(JSON.stringify(p.person)));

    for (const up of u_people) {
      if (up.person.network && !up.person.vanity) {
        up.person.id = null;
        up.person.comms = up.person.comms.filter(c => !parsers.findId(c));
      }
    }

    // u_people.sort((a,b) => `${a.displayName}_${a.id}` < `${b.displayName}_${b.id}` ? -1 : 1);

    // if (logging.isDebug(user ? user.profile : null)) 
    logging.infoFP(LOG_NAME, 'peopleResults', user ? user.profile : null, `Found ${u_people.length} people`);
    if (isNaN(page)) return u_people;
    else return u_people.slice(1000 * page, 1000 * (page + 1));
  }
 
  async findCourses(skills?: string[], limit = 100, skip_ids?: Uid[]): Promise<Course[]> {
    let query;
    let params;
    if (!skills) {
      query = `select id, title, link, skills, vector from people.course`
    } else if(skills.length) {
      // array(select float from unnest(vector)) as 
      query = `select id, title, link, skills, vector
        from people.course p where id in 
          (select distinct(id) from people.course, unnest(skills) skill, unnest(@skills) test where skill like test 
          ${skip_ids && skip_ids.length ? ' and id not in unnest(@skip_ids)' : ''}
          limit @limit)`
          // order by array_length(array(select * from p.skills intersect distinct select s from unnest(@skills) s)) desc limit @limit`;
      params = {skills: skills.map(s => `%${s}%`), limit};
      if (skip_ids && skip_ids.length) params['skip_ids'] = skip_ids;
    } else return [];

    try {
      // const [results] = await this.bq.query({query, params, useLegacySql: false });
      const results = await this.retryQuery<Course>(null, 'findCourses', query, params);
      if (results) return results.map(r => new Course(r));
    } catch(e) {
      logging.errorF(LOG_NAME, 'findCourses', `Error finding courses`, e);
    }
    return [];
  }

  async findCoursesByCategories(categories: Partial<ExtendedCategory>[], skip_ids?: Uid[], sources?: string[], level?: string[]): Promise<{id: Uid, courses: {course: Course, score: number}[]}[]> {
    const ssources = sources ? sources.map(s => `c.link like "%${s}/%"`) : undefined;
    const select_sources = sources ? 
      `and (${ssources.join(' or ')})`
      : '';

    let query = `with cats as ( select cs.category_id, cs.cskills, cs.vector from unnest(@cat_set) cs),
      courses as (
        select c.id course_id, category_id, c.skills rskills, cskills from people.course c, cats 
        where 1 - ml.distance(c.vector, cats.vector, 'COSINE') > 0.5
        ${skip_ids && skip_ids.length ? 'and id not in unnest(@skip_ids)' : ''}
        ${select_sources}
        order by 1 - ml.distance(c.vector, cats.vector, 'COSINE') desc
        limit ${20 + (skip_ids ? skip_ids.length : 0)}
      )
      
      select category_id, course_id, score, title, link, skills from (
        select category_id, course_id, avg(1 - ml.distance(ws.vector, wr.vector, 'COSINE')) score
        from courses, unnest(cskills) cskill, unnest(rskills) rskill
        join people.words ws on ws.name = cskill
        join people.words wr on wr.name = rskill
        group by category_id, course_id
      ), people.course c where course_id = c.id`;

    const cat_set = categories.map(c => {
      return {
        category_id: c.id,
        cskills: c.skills.map(s => s.toLowerCase()),
        vector: c.vector,
      }
    });

    const params = {
      cat_set,
    };

    const types: any = {
      cat_set: [{category_id: 'STRING', cskills: ['STRING'], vector: ['FLOAT64']}],
    }

    if (skip_ids && skip_ids.length) {
      params['skip_ids'] = skip_ids;
      types['skip_ids'] = ['STRING'];
    }

    try {
      const results = await this.retryQuery<{category_id: Uid, score: number, course_id: Uid, title: string, link: string, skills: string[]}>(null, 'findCoursesByCategories', query, params, types);
      const result_set: {id: Uid, courses: {course: Course, score: number}[]}[] =  [];
      for(const r of results) {
        const c = result_set.find(x => x.id === r.category_id);
        const course_score = {
            course: new Course({
              id: r.course_id,
              title: r.title,
              link: r.link,
              skills: r.skills
            }),
            score: r.score
          }

        if (c) c.courses.push(course_score);
        else {
          result_set.push({
            id: r.category_id,
            courses: [course_score],
          });
        }
      }

      return result_set;
    } catch(e) {
      logging.errorF(LOG_NAME, 'findCoursesByCategories', 'Error finding courses', e);
    }

    return [];
  }

  async findSkillCategories(skills: string[], limit = 100): Promise<SkillCategory[]>  {
    const query = `select id, skills, vector, weights
      from people.skillcategory p where id in 
        (select distinct(id) from people.skillcategory, unnest(skills) skill, unnest(@skills) test where skill like test limit @limit)`
    const params = {skills: skills.map(s => `%${s}%`), limit};

    try {
      // const [results] = await this.bq.query({query, params, useLegacySql: false });
      const results = await this.retryQuery<SkillCategory>(null, 'findSkillCategories', query, params);
      if (results) return results.map(r => new SkillCategory(r));
    } catch(e) {
      logging.errorF(LOG_NAME, 'findSkillCategories', `Error finding skill categories`, e);
    }
    return [];
  }

  async findSkillCategoryStats(group_ids: Uid[], global: boolean, terms: string[], add_terms: string[], skip_ids: Uid[], limit = 100): Promise<{category: SkillCategory, skills: string[], stats: ScoreStat}[]>  {
    if (!terms || !terms.length) {
      logging.warnF(LOG_NAME, 'findSkillCategoryStats', `Not searching because vector is empty`);
      return [];
    }

    if (!add_terms || !add_terms.length) add_terms = terms;

    let datasets = global || !group_ids || !group_ids.length ? ['people'] : [];
    if (group_ids) {
      const add_ids: Uid[] = [];
      await Promise.all(group_ids.map(async group_id => {
        const exists = await this.checkGroup(group_id);
        if (exists) add_ids.push(group_id);
      }));

      datasets = datasets.concat(group_ids.filter(g => add_ids.includes(g)).map(g => this.safeProfile(g)));
    }

    const group_cats = datasets.map(d => `select id, skills, weights, vector from \`${d}.skillcategory\``)

    const cat_union  = `cat_set as (
      ${group_cats.join(' union all ')}
    )`;

    const vector = await skillVector(terms);
    const vectors = await this.termVectors(terms, add_terms);
    const vector_filter = vectors.map((v,i) => `1 - ml.distance(vector, @vector_${i}, 'COSINE') >= 0.5`).join(' or ');

    let skip = skip_ids && skip_ids.length ? `and id not in unnest(@skip_ids)` : '';

    const params = {
      terms: terms.map(t => t.toLowerCase()), 
      vector, 
      limit,
      add_terms: _.uniq([...terms, ...add_terms].map(t => t.toLowerCase())),
    };

    for(const x in vectors) params[`vector_${x}`] = vectors[x];

    if (skip_ids && skip_ids.length) params['skip_ids'] = skip_ids;


    const filter = `id_set as (select id
      from cat_set 
      where (${vector_filter})
      ${skip}
      order by 1 - ml.distance(vector, @vector, 'COSINE') desc
      limit @limit )`;

    const wp = `wp as (select * from people.words where name in (select distinct skill from cat_set, unnest(skills) skill))`;
    const wt= `wt as (select * from people.words where name in unnest(@terms))`;

    //           count(*) score_cnt,
    //      sum(1 + (1 - ml.distance(wt.vector, wp.vector, 'COSINE')) * weights[OFFSET(offset)]) score_sum,
    //     max(1 + (1 - ml.distance(wt.vector, wp.vector, 'COSINE')) * weights[OFFSET(offset)]) score_max,
    //     min(1 + (1 - ml.distance(wt.vector, wp.vector, 'COSINE')) * weights[OFFSET(offset)]) score_min,
 
    const stats = `scores as (
        select id sid, 
          array_agg(1 + (1 - ml.distance(wt.vector, wp.vector, 'COSINE')) * weights[OFFSET(offset)]) weight
          from cat_set, unnest(skills) skill with offset, unnest(@terms) term
          join wt on wt.name = term
          join wp on wp.name = skill
          where id in (select id from id_set)
            group by id
        )`;
    
    // and instr(skill, term) > 0
    const matches = `match_set as (
      select id mid, array_agg(distinct skill) matches
      from cat_set, unnest(skills) skill, unnest(@add_terms) term
       where id in (select id from id_set)
        and term in unnest(split(skill, ' '))
        group by id
      )`;


      // score_cnt, score_sum, score_max, score_min, 
    const query = `with ${cat_union}, ${filter}, ${wp}, ${wt}, ${stats}, ${matches}
      select id_set.id id, skills, vector, weights, matches,
      (select avg(w) from unnest(weight) w) score_avg, (select stddev(w) from unnest(weight) w) score_stddev 
      from id_set
      join cat_set on cat_set.id = id_set.id
      left join scores on id_set.id = sid
      left join match_set on id_set.id = mid `
      // where array_length(array(select lower(skill) from unnest(cat_set.skills) skill intersect distinct (select lower(term) from unnest(@add_terms) term))) > 0
      // order by 1 - ml.distance(vector, @vector, 'COSINE') desc`

    try {
      // const [results] = await this.bq.query({query, params, useLegacySql: false });
      const results = await this.retryQuery<CategoryStat>(null, 'findSkillCategoryStats', query, params);
      if (results) return _.uniqBy(results.map(r => {
        const category = new SkillCategory(r as Partial<SkillCategory>);
        category.skills = _.uniqBy(category.skills, x => x.toLowerCase());
        return { 
          category,
          skills: _.uniqBy(r['matches'], x => x.toLowerCase()),
          stats: {
            // score_cnt: r['score_cnt'],
            // score_sum: r['score_sum'],
            // score_max: r['score_max'],
            // score_min: r['score_min'],
            score_avg: r['score_avg'],
            score_stddev: r['score_stddev'],
          }
        }
      }), cs => cs.category.id);
    } catch(e) {
      logging.errorF(LOG_NAME, 'findSkillCategoriesByVector', `Error finding skill categories`, e);
    }
    return [];
  }

  async relatedWords(words: string[]): Promise<string[]> {
    if (!words?.length) return [];

    const query = `select w.name from people.words w, people.words x where x.name in unnest(@words)
      and  1 - ml.distance(w.vector, x.vector, 'COSINE') > 0.55
      order by  1 - ml.distance(w.vector, x.vector, 'COSINE') desc
      limit 10`;
    
    const params = {words};

    try {
      const results = await this.retryQuery<{name: string}>(null, 'relatedWords', query, params);
      return results.map(r => r.name);
    } catch(e) {
      logging.errorF(LOG_NAME, 'mapCategories', `Error mapping categories`, e);
    }

    return [];
 
  }

  async mapCategories(user: User, categories: Partial<Category>[], people: Partial<Person>[]): Promise<{category_id: Uid, person_id: Uid, vanity: string, score: number}[] > {
    let table;

    if (user) {
      const profiles = await this.checkProfiles([user.profile]);
      if (!profiles || !profiles.length) {
        logging.warnFP(LOG_NAME, 'mapCategories', user.profile, `User table not found`);
        return [];
      }

      table = `people_${profiles[0]}`;
    }

    const vanities = people.filter(p => p.vanity && p.vanity.length).map(p => p.vanity);
    const people_ids = table ? people.filter(p => (!p.vanity || !p.vanity.length) && !p.network).map(p => p.id) : [];

    const cat_set = categories.filter(c => c.skills?.length).map(c => {
      return {
        category_id: c.id,
        cskills: parsers.expandWords(c.skills.map(s => s.toLowerCase())),
      }
    });

    /*let cat_data: {category_id: Uid, cvecs: {skill: string, vector: number[]}[]}[] = [];
    const cat_query = `select cs.category_id, array_agg(struct(cskill as skill, ws.vector as vector)) cvecs from unnest(@cat_set) cs, unnest(cskills) cskill
        join people.words ws on ws.name = cskill
        group by cs.category_id`;
    try {
      const params = { cat_set };
      const types: any = { cat_set: [{category_id: 'STRING', cskills: ['STRING']}] }
      cat_data = await this.retryQuery<{category_id: Uid, cvecs: {skill: string, vector: number[]}[]}>(null, 'mapCategories', cat_query, params, types);
    } catch(e) {
      logging.errorF(LOG_NAME, 'mapCategories', `Error fetching categories`, e);
    }*/

    const people_query = table ? ` union all
        select id as person_id, null vanity, array_agg(struct(lower(tag.value) as skill, wp.vector as vector)) pvecs from people.${table}, unnest(tags) tag, people.words wp
          where id in unnest(@people_ids) 
            and tag.type = 'skill'
            and tag.index <> -1
            and wp.name = lower(tag.value)
          group by id` : '';

    const vanity_query = `select null person_id, vanity, array_agg(struct(lower(tag.value) as skill, wp.vector as vector)) pvecs from people.users, unnest(tags) tag, people.words wp
          where vanity in unnest(@vanities) 
            and tag.type = 'skill'
            and tag.index <> -1
            and wp.name = lower(tag.value)
          group by vanity`;

    // select category_id, cvecs from unnest(@cat_data))`;
    const cats = `with cats as (
        select cs.category_id, array_agg(struct(cskill as skill, ws.vector as vector)) cvecs from unnest(@cat_set) cs, unnest(cskills) cskill
        join people.words ws on ws.name = cskill
        group by cs.category_id)`;
    
        // max(1 - ml.distance(cvec.vector, pvec.vector, 'COSINE')) - stddev(1 - ml.distance(cvec.vector, pvec.vector, 'COSINE')) score
    const select_query = `select category_id, person_id, vanity,
        (sum(1 - ml.distance(cvec.vector, pvec.vector, 'COSINE')) - 
        max(1 - ml.distance(cvec.vector, pvec.vector, 'COSINE')) -
        min(1 - ml.distance(cvec.vector, pvec.vector, 'COSINE'))) / count(pvec)
        + (2 * stddev(1 - ml.distance(cvec.vector, pvec.vector, 'COSINE'))) score
      from cats, all_people, unnest(cvecs) cvec, unnest(pvecs) pvec
      group by category_id, person_id, vanity`;

    // from cats, all_people, people.words ws, people.words wp
    // where ws.name in unnest(cskills) and ws.name not in unnest(pskills) and wp.name in unnest(pskills) and wp.name not in unnest(cskills)
    const query = `${cats}, all_people as (${vanity_query} ${people_query}) ${select_query}`;

    // array_concat(pskills, split(array_to_string(pskills, " "), " ")))

    const types: any = {
      vanities: ['STRING'],
      people_ids: ['STRING'],
      cat_set: [{category_id: 'STRING', cskills: ['STRING']}],
      // cat_data: [{category_id: 'STRING', cvecs: [ {skill: 'STRING', vector: ['NUMERIC'] } ] } ],
    };

    try {
      let all_results: {category_id: string, person_id: string, vanity: string, score: number}[][] = [];
      while(vanities.length || people_ids.length) {
        const vanity_set = vanities.splice(0,10);
        const people_set = people_ids.splice(0,10);

        const params = {
          vanities: vanity_set,
          people_ids: people_set,
          cat_set,
          // cat_data,
        };

        const results = await this.retryQuery<{category_id: string, person_id: string, vanity: string, score: number}>(null, 'mapCategories', query, params, types);
        if(results) all_results.push(results);
      }
      return flatten(all_results);
    } catch(e) {
      logging.errorF(LOG_NAME, 'mapCategories', `Error mapping categories`, e);
    }

    return [];
  }

  async peopleCategories(user: User, people: Partial<Person>[], group_ids: Uid[], global: boolean): Promise<{category_id: Uid, person_id: Uid, vanity: string, score: number, match: string[]}[] > {
     let table;

    if (user) {
      const profiles = await this.checkProfiles([user.profile]);
      if (!profiles || !profiles.length) {
        logging.warnFP(LOG_NAME, 'peopleCategories', user.profile, `User table not found`);
        return [];
      }

      table = `people_${profiles[0]}`;
    }

    let datasets = global ? ['people'] : [];
    if (group_ids) {
      const add_ids: Uid[] = [];
      await Promise.all(group_ids.map(async group_id => {
        const exists = await this.checkGroup(group_id);
        if (exists) add_ids.push(group_id);
      }));

      datasets = datasets.concat(group_ids.filter(g => add_ids.includes(g)).map(g => this.safeProfile(g)));
    }

    const group_cats = datasets.map(d => `select id, skills, weights, vector from \`${d}.skillcategory\``)

    const vanities = people.filter(p => p.vanity && p.vanity.length).map(p => p.vanity);
    const people_ids = people.filter(p => (!p.vanity || !p.vanity.length) && !p.network).map(p => p.id);

    const cat_union  = `cat_set as (
      ${group_cats.join(' union all ')}
    )`;

    const people_query = table ? ` union all
        select id as person_id, null vanity, array_agg(lower(tag.value)) as pskills from people.${table}, unnest(tags) tag
          where id in unnest(@people_ids) 
            and tag.type = 'skill'
            and tag.index <> -1
          group by id` : '';

    const query = `with ${cat_union}, all_people as (
        select null person_id, vanity, array_agg(lower(tag.value)) as pskills from people.users, unnest(tags) tag
          where vanity in unnest(@vanities) 
            and tag.type = 'skill'
            and tag.index <> -1
          group by vanity
        ${people_query}
      ),
      
      cats as (
        select id category_id, array_agg(skill) cskills
          from cat_set, unnest(skills) skill
          where skill in (select distinct(pskill) from all_people, unnest(pskills) pskill)
          group by id 
      ),

      catset as (
        select category_id, person_id, vanity,
          max(1 - ml.distance(ws.vector, wp.vector, 'COSINE')) - stddev(1 - ml.distance(ws.vector, wp.vector, 'COSINE')) score,
        
        from cats, all_people, unnest(cskills) cs, unnest(pskills) ps
        join people.words ws on ws.name = cs
        join people.words wp on wp.name = ps
        group by category_id, person_id, vanity
      )

      select catset.category_id, catset.person_id, catset.vanity, catset.score,
        array(select * from unnest(cskills) intersect distinct (select * from unnest(pskills))) match
      from catset, all_people, cats
      where (catset.person_id = all_people.person_id OR catset.vanity = all_people.vanity) 
        and cats.category_id = catset.category_id
        and array_length(array(select * from unnest(cskills) intersect distinct (select * from unnest(pskills))) ) > 0
    `;

    const params = {
      vanities,
      people_ids,
    };

    const types: any = {
      vanities: ['STRING'],
      people_ids: ['STRING'],
    };

    try {
      const results = await this.retryQuery<{category_id: string, person_id: string, vanity: string, score: number, match: string[]}>(null, 'peopleCategories', query, params, types);
      return results;
    } catch(e) {
      logging.errorF(LOG_NAME, 'peopleCategories', `Error mapping categories`, e);
    }

    return [];
 
  }

  async skillMatrix(aterms: string[], bterms: string[]): Promise<{aname: string, bname:string, score: number}[]> {
    const query = `select aw.name aname, bw.name bname, 1 - ml.distance(aw.vector, bw.vector, 'COSINE') score 
      from unnest(@aterms) aterm, unnest(@bterms) bterm
      join people.words aw on aw.name = aterm
      join people.words bw on bw.name = bterm`;

    const params = {aterms, bterms};

    try {
      const results = await this.retryQuery<{aname: string, bname:string, score: number}>(null, 'skillMatrix', query, params, undefined, true);
      return results as {aname: string, bname:string, score: number}[];
    } catch(e) {
      logging.errorF(LOG_NAME, 'skillMatrix', `Error creating matrix for ${aterms.length} x ${bterms.length} terms`, e);
    } 
  }

  async termVectors(terms: string[], add_terms: string[] = []): Promise<number[][]> {
    logging.infoF(LOG_NAME, 'termVectors', `Fitting ${terms.length} terms and ${add_terms.length} extra terms`);
    const uterms = _.uniq([...terms, ...add_terms]);
    const skill_vectors: {skill: string, vector: number[]}[] = [];
    await Promise.all(uterms.map(async skill => {
      skill_vectors.push({ skill, vector: await skillVector([skill])});
    }));

    if (skill_vectors.length <= 20) return skill_vectors.map(v => v.vector);

    const clusters = kmeans(skill_vectors.map(v => v.vector), 20, { maxIterations: 10 });
    const vmap: string[][] = clusters.centroids.map(c => []); 
    for(const svec of skill_vectors) {
      let best = 0;
      let weight = 0;
      for(let j = 0; j < clusters.centroids.length; j++) {
        const sim = cosineSimilarity(clusters.centroids[j], svec.vector);
        if (sim > weight) {
          weight = sim;
          best = j;
        }
      }

      vmap[best].push(svec.skill);
    }

    const vectors: number[][] = [];
    await Promise.all(vmap.map(async skills => vectors.push(await skillVector(skills))));

    logging.infoF(LOG_NAME, 'termVectors', `Found ${vectors.length} vectors`);
    return vectors;
  }
  
  async fetchSkills(): Promise<Skill[]> {
    const query = `select id, skill, synonyms, rates, initialisms, stem, vector, svector from people.skill ps where length(skill) > 2 and synonyms is not null and array_length(synonyms) > 1`; 
    try {
      // const [results] = await this.bq.query({query, useLegacySql: false });
      const results = await this.retryQuery<Skill>(null, 'fetchSkills', query);
      if (results) return results.map(s => new Skill(s));
    } catch(e) {
      logging.errorF(LOG_NAME, 'fetchSkills', `Error fetching skills`, e);
    }
    return [];
  }

  async loadCategories(group_id: Uid): Promise<SkillCategory[]> {
    let dataset = 'people';
    if (group_id) {
      const exists = await this.checkGroup(group_id);
      if (!exists) {
        logging.warnF(LOG_NAME, 'loadCategories', `Not loading because group ${group_id} doesn't exist`);
        return [];
      }
      else dataset = this.safeProfile(group_id);
    }

    const query = `select id, skills, vector, weights from \`${dataset}.skillcategory\``;
    try {
      // const [results] = await this.bq.query({query, useLegacySql: false});
      const results = await this.retryQuery<SkillCategory>(null, 'loadCategories', query);
      if (results) return results.map(r => new SkillCategory(r));
    } catch(e) {
      logging.errorF(LOG_NAME, 'loadCategories', `Error loading categories`, e);
    }
    return [];
  }

  async loadSkills(): Promise<Skill[]> {
    const query = `select id, skill, stem, synonyms, initialisms from people.skill as skill_table`;
    try {
      //const [results] = await this.bq.query({query, useLegacySql: false});
      const results = await this.retryQuery<Skill>(null, 'loadSkills', query);
      if (results) return results.map(r => new Skill(r));
    } catch(e) {
      logging.errorF(LOG_NAME, 'loadSkills', `Error loading skills`, e);
    }
    return [];
  }

  async retryQuery<T>(profile: Uid, method: string, query: string, params?: any, types?: {[key:string]: string|string[]}, throw_error = false): Promise<T[]> {
    let retry = 100;
    while(retry <= 1600) {
      try {
        const [results] = await this.bq.query({query, params, types, useLegacySql: false});
        return results as T[];
      } catch(err) {
        if (!retryError(err) || retry > 1600) {
          if (throw_error) throw err;
          logging.errorFP(LOG_NAME, method, profile, `Unknown error running query ${query}`, err);
          return null;
        }

        const retry_msg = retry !== 0 ? `Retrying in ${retry}` : '';
        logging.warnFP(LOG_NAME, method, profile, `${retry_msg}::${err.code}::${err.message}`);
        await setTimeout(retry);
        retry *= 2;
      }
    }
  }
}
