/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { Datastore } from '@google-cloud/datastore';
import { Storage } from '@google-cloud/storage';
import { setTimeout } from 'timers/promises';
import config from '../config';

import { COURSES, CourseKeys, IMPORTS, ImportKeys, SOURCES_OFFLINE, SOURCES_ONLINE, SourceKeys } from '../sources';

import { GlobalType, IEntity } from '../types/items';
import { EntityType, Uid } from '../types/shared';

import { ICachePlugin } from './caching/i_cache_plugin';
import { MemcacheCachePlugin } from './caching/memcache_cache_plugin';
import { OfflineCachePlugin } from './caching/offline_cache_plugin';

import logging from '../utils/logging';

import DataStorage from './storage/data_storage';
import GoogleBigQuery from './storage/google_bigquery';
import { GoogleCloudStorageFileStore } from './storage/google_cs_file_store';
import GoogleDocStore from './storage/google_doc_store';
import { GoogleDataStoreNoteStore } from './storage/google_ds_note_store';
import MicrosoftOneDriveFileStore from './storage/microsoft_one_drive_file_store';
import MicrosoftOneNoteNoteStore from './storage/microsoft_one_note_note_store';
import OfflineDatastore from './storage/offline_datastore';
import OfflineDocStore from './storage/offline_doc_store';

import { AnalysisDataWrapper } from './wrappers/analysis_data_wrapper';
import { AskDataWrapper } from './wrappers/ask_data_wrapper';
import { ContentDataWrapper } from './wrappers/content_data_wrapper';
import { ContractDataWrapper } from './wrappers/contract_data_wrapper';
import { CourseDataWrapper } from './wrappers/course_data_wrapper';
import { EventDataWrapper } from './wrappers/event_data_wrapper';
import { GoalDataWrapper } from './wrappers/goal_data_wrapper';
import { GroupDataWrapper } from './wrappers/group_data_wrapper';
import { ImportDataWrapper } from './wrappers/import_data_wrapper';
import { MessageDataWrapper } from './wrappers/message_data_wrapper';
import { NoteDataWrapper } from './wrappers/note_data_wrapper';
import { PersonDataWrapper } from './wrappers/person_data_wrapper';
import { PlanDataWrapper } from './wrappers/plan_data_wrapper';
import { ProfileDataWrapper } from './wrappers/profile_data_wrapper';
import { ProjectDataWrapper } from './wrappers/project_data_wrapper';
import { RecommendationDataWrapper } from './wrappers/recommendation_data_wrapper';
import { SessionDataWrapper } from './wrappers/session_data_wrapper';
import { SkillDataWrapper } from './wrappers/skill_data_wrapper';
import { TaskDataWrapper } from './wrappers/task_data_wrapper';
import { TemplateDataWrapper } from './wrappers/template_data_wrapper';
import { TutorialDataWrapper } from './wrappers/tutorial_data_wrapper';
import { UserDataWrapper } from './wrappers/user_data_wrapper';

const LOG_NAME = 'data';

let big_query: GoogleBigQuery;
let cache_plugin: ICachePlugin;
let data_storage: DataStorage;
function storagePlugin() { return data_storage }
let source_plugins: Map<SourceKeys, any>;
let import_plugins: Map<ImportKeys, any>;
let course_plugins: Map<CourseKeys, any>;
let datastore: Datastore;

function loadDataConfig (silent: boolean) {
  if (config.isEnvOffline()) {
    if (!cache_plugin) cache_plugin = new OfflineCachePlugin();
    if (!datastore) datastore = new OfflineDatastore();
    if (!source_plugins) source_plugins = SOURCES_OFFLINE;
    if (!import_plugins) import_plugins = IMPORTS;
    if (!course_plugins) course_plugins = COURSES;

    if (!data_storage) {
      const doc_store_plugin = new OfflineDocStore();
      data_storage = new DataStorage(
        cache_plugin,
        datastore,
        doc_store_plugin, // Google Drive file store
        doc_store_plugin, // Google Drive note store
        doc_store_plugin, // Microsoft OneDrive file store
        doc_store_plugin, // Microsoft OneNote note store
        doc_store_plugin, // Google fallback CS file store
        doc_store_plugin, // Google fallback DS note store
      );
    }
  } else {
    if (!cache_plugin) cache_plugin = new MemcacheCachePlugin();
    if (!datastore) datastore = new Datastore({ /*fallback: 'rest',*/ projectId: config.get('GOOGLE_CLOUD_PROJECT') });
    if (!source_plugins) source_plugins = SOURCES_ONLINE;
    if (!import_plugins) import_plugins = IMPORTS;
    if (!course_plugins) course_plugins = COURSES;

    if (!data_storage) {
      const google_doc_store_plugin = new GoogleDocStore(cache_plugin);
      // console.log('Data: Docstore');
      const storage = new Storage({ projectId: config.get('GOOGLE_CLOUD_PROJECT') });
      // console.log('Data: Storage');
      storage.interceptors.push({
        request: function(reqOpts) {
          reqOpts.forever = false
          return reqOpts as any;
        }
      });

      data_storage = new DataStorage(
        cache_plugin,
        datastore,
        google_doc_store_plugin, // Google Drive file store
        google_doc_store_plugin, // Google Drive note store
        new MicrosoftOneDriveFileStore(), // Microsoft OneDrive file store
        new MicrosoftOneNoteNoteStore(cache_plugin), // Microsoft OneNote note store
        new GoogleCloudStorageFileStore(storage), // Google fallback CS file store
        new GoogleDataStoreNoteStore(cache_plugin), // Google fallback DS note store
      );
    }
  }

  if (!big_query) big_query = new GoogleBigQuery(datastore, cache_plugin);
}

config.onLoad('data', async(silent: boolean) => { loadDataConfig(silent); });

const plugins = {
  /**
   * Not exposing the plugins directly (have to go through a method call) that way
   * they have time to be created by the config.onLoad method.:/
   */
  bigQueryPlugin: () => { loadDataConfig(true); return big_query },
  cachePlugin: () => { loadDataConfig(true); return cache_plugin },
  coursePlugins: () => { loadDataConfig(true); return course_plugins },
  importPlugins: () => { loadDataConfig(true); return import_plugins },
  storagePlugin,
  sourcePlugins: () => { loadDataConfig(true); return source_plugins },
}

async function dataCheck() {
  if (!config.configSilent()) {
    console.log('Loading data plugins');
    console.log(`Checking config loaded ${config.configLoaded()}`);
  }

  if (!config.configLoaded()) {
    await config.loadConfig();
    if (!config.configSilent()) console.log(`Config loaded ${config.configLoaded()}`);
    logging.infoF(LOG_NAME, 'dataCheck', `Config loaded ${config.configLoaded()}`);
  }

  logging.infoF(LOG_NAME, 'dataCheck', `Loading data plugins`);
  // force data to load
  let count = 0;
  let timeout = 10;
  while ((plugins.storagePlugin() === undefined || plugins.storagePlugin().init === undefined) && count < 12) {
    await setTimeout(timeout);
    timeout = timeout * 2;
    count++;
    console.log(`Retrying data ${count}`);
    logging.warnF(LOG_NAME, 'dataCheck', `Retrying data ${count}`);
  }

  plugins.storagePlugin().init();
  if (!config.configSilent()) console.log(`Data init took ${count * 10}ms`);
  logging.infoF(LOG_NAME, 'dataCheck', `Data init took ${count * 10}ms`);
}

export default {
  content: ContentDataWrapper,
  contracts: ContractDataWrapper,
  events: EventDataWrapper,
  groups: GroupDataWrapper,
  imports: ImportDataWrapper,
  internals: {
    add: (profile: Uid, data: IEntity, no_index: string[] = []) => data_storage.add(profile, data, no_index),
    find: (profile: Uid, type: EntityType | GlobalType, id: Uid) => data_storage.find(profile, type, id),
    remove: (profile: Uid, data: IEntity) => data_storage.remove(profile, data),
    removeAll: (profile: Uid, entities: IEntity[]) => data_storage.removeAll(profile, entities),
  },
  analyses: AnalysisDataWrapper,
  asks: AskDataWrapper,
  courses: CourseDataWrapper,
  goals: GoalDataWrapper,
  messages: MessageDataWrapper,
  notes: NoteDataWrapper,
  people: PersonDataWrapper,
  plugins,
  plans: PlanDataWrapper,
  projects: ProjectDataWrapper,
  profiles: ProfileDataWrapper,
  sessions: SessionDataWrapper,
  skills: SkillDataWrapper,
  tasks: TaskDataWrapper,
  tutorials: TutorialDataWrapper,
  testing: {
    // These should only be used by test cases!!!!
    datastore: () => datastore,
    useSourcePluginsOffline: () => (source_plugins = SOURCES_OFFLINE),
    useSourcePluginsOnline: () => (source_plugins = SOURCES_ONLINE),
  },
  templates: TemplateDataWrapper,
  users: UserDataWrapper,
  recommendations: RecommendationDataWrapper,
  dataCheck,
};
