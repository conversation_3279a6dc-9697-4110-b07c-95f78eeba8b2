/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import _ from 'lodash';
import { v4 as uuid } from 'uuid';
import data from '..';

import config from '../../config';
import lang from '../../lang';

import DataCache from '../../session/data_cache';
import ForaUser from '../../session/user';
import { Group } from '../../types/group';

import { SourceKeys } from '../../sources';
import { IPeopleSourcePlugin } from '../../sources/people/i_people_source_plugin';
import { SourceController } from '../../sources/source_controller';

import { InternalError, Introduction, Vanity } from '../../types/globals';
import { Document, Person, Tag } from '../../types/items';
import { ANONYMOUS_ID, AuthLevel, AuthProviders, EntityType, JobTags, SkillStat, Stats, TagType, Uid, findTypeIndexes, findTypeValue, findTypeValues } from '../../types/shared';
import { User } from '../../types/user';

import { camelCase } from '../../utils/format';
import { flatten, hash, saveOne } from '../../utils/funcs';
import logging from '../../utils/logging';
import parsers from '../../utils/parsers';
import peopleUtils from '../../utils/people';

import { AbstractDataWrapper } from './a_data_wrapper';

 const DEBUG = (require('debug') as any)('fora:data:wrappers:person');
 const LOG_NAME = 'data.wrappers.PersonDataWrapper';

 export class PersonDataWrapper extends AbstractDataWrapper {
  static async updatePeopleWithProfiles(user: User, people: Partial<Person>[]) {
    if (!people || !people.length) return;

    const user_groups = user.groups ? Object.keys(user.groups) : null;
    const people_comms: {[key:string]: Partial<Person>} = {};

    for (const person of people.filter(p => p && !p.self && !p.vanity)) {
      if (person && person.comms) {
        const emails = parsers.findEmail(person.comms);
        if (emails && emails.length) {
          for (const email of emails.filter(e => person.self || !user || e !== user.email)) {
            if (!people_comms[email]) people_comms[email] = null;
          }
        }
      }
    }

    const emails = Object.keys(people_comms);
    if (!emails.length) return;

    const vanity_people = await data.users.vanityPeopleByEmail(emails);
    if (vanity_people) {
      const global_users = await data.users.globalsByVanities(vanity_people.map(v => v.vanity));
      const group_ids = flatten(global_users.map(g => g.groups ? g.groups : []));
      const groups = group_ids && group_ids.length ? await data.groups.byIds(group_ids) : [];
      const blocked_groups = groups.filter(g => g.block_groups_contacts).map(g => g.id);

      for (const person of vanity_people) {
        const global = person.vanity ? global_users.find(g => g.vanity === person.vanity) : null;
        if (global && (!global.groups || _.intersection(global.groups, blocked_groups).length === 0 || _.intersection(global.groups, user_groups).length > 0)) {
          people_comms[global.email] = person;
          person.groups = global.groups && user_groups ? _.intersection(global.groups, user_groups) : null;
        }
      }
    }

    for (const person of people.filter(p => p && !p.self && !p.vanity)) {
      if (person && person.comms) {
        const emails = parsers.findEmail(person.comms);
        if (emails && emails.length) {
          for (const email of emails.filter(e => person.self || !user || e !== user.email)) {
            if (people_comms[email]) {
              const vanity_person = people_comms[email];
              peopleUtils.updateFromProfile(person, vanity_person);
              // filter out self email
              if (user) person.comms = person.comms.filter(c => c !== user.email);
              break;
            }
          }
        }
      }
    }
  }

  static async lookupNames(user: User, names: string[], aliases?: string[], additional_user_ids?: Uid[]): Promise<string[]> {
    let found_names: string[] = [];

    if (additional_user_ids && additional_user_ids.length) {
      const add_ids = additional_user_ids.slice();
      while(add_ids.length) {
        const pr = await data.plugins.bigQueryPlugin().lookupNames(user, names,  aliases, add_ids.splice(0,1000));
        found_names = found_names.concat(pr);
      }
    } else {
      found_names = await data.plugins.bigQueryPlugin().lookupNames(user, names, aliases);
    }

    return found_names; 
  }

  static async lookupOrgs(user: User, orgs: string[], aliases?: string[], additional_user_ids?: Uid[]): Promise<string[]> {
    let found_orgs: string[] = [];

    if (additional_user_ids && additional_user_ids.length) {
      const add_ids = additional_user_ids.slice();
      while(add_ids.length) {
        const pr = await data.plugins.bigQueryPlugin().lookupOrgs(user, orgs,  aliases, add_ids.splice(0,1000));
        found_orgs = found_orgs.concat(pr);
      }
    } else {
      found_orgs = await data.plugins.bigQueryPlugin().lookupOrgs(user, orgs, aliases);
    }

    return found_orgs;
  }

  static async matchPeople(user: User, people: Partial<Person>[], match_profiles = true, index?: number): Promise<Partial<Person>[]> {
    const found_people = await data.plugins.bigQueryPlugin().lookupPeople(user, people, index);
    if (match_profiles) {
      logging.infoFP(LOG_NAME, 'bigQueryMatch', user ? user.profile : null, `Updating ${found_people.length} profiles`);
      await PersonDataWrapper.updatePeopleWithProfiles(user, found_people);
    }
    logging.infoFP(LOG_NAME, 'bigQueryMatch', user ? user.profile : null, `Matched ${found_people.length} profiles`);
    return found_people;
  }

  static async matchByComms(user: User, comms: string[], urls: string[] = null, match_profiles = true): Promise<Partial<Person>> {
    const person = await data.plugins.bigQueryPlugin().lookupPerson(user, comms, urls);
    if (match_profiles) await PersonDataWrapper.updatePeopleWithProfiles(user, [person]);
    return person;
  }

  static async matchByOrgs(user: User, name: string, tag_values: string[]): Promise<Partial<Person>> {
    const person = await data.plugins.bigQueryPlugin().lookupPersonOrgs(user, name, tag_values);
    if (person) {
      const resolve_people = await PersonDataWrapper.byAttributeComms(user, person.comms);
      if (resolve_people && resolve_people.length) {
        await PersonDataWrapper.updatePeopleWithProfiles(user, resolve_people);
        return resolve_people[0]; 
      } else {
        await PersonDataWrapper.updatePeopleWithProfiles(user, [person]);
        person.network = true;
        return person;
      }
    }
  }

  static async bigQueryByTerms(user: User, terms: string[], aliases?: string[], additional_user_ids?: Uid[], require_email = false, network_only = false): Promise< Person[]>  {
    let people = [];

    if (additional_user_ids && additional_user_ids.length) {
      const add_ids = additional_user_ids.slice();
      while(add_ids.length) {
        const pr = await data.plugins.bigQueryPlugin().queryPerson(user, terms, null, null, aliases, add_ids.splice(0,1000), require_email, network_only);
        people = people.concat(pr);
      }
    } else {
      people = await data.plugins.bigQueryPlugin().queryPerson(user, terms, null, null, aliases, null, require_email, network_only);
    }

    await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return people;
  }
  
  static async bigQueryBySkills(user: User, terms: string[], filter_skills?: string[], aliases?: string[], additional_user_ids?: Uid[], require_email = false, network_only = false): Promise<{
    person: Partial<Person>, 
    weight: number,
    stddev: number,
  }[]>  {
    let people_stats = [];
    const lterms = terms ? terms.map(t => t.toLowerCase()) : [];
    const lfilter = filter_skills ? filter_skills.map(s => s.toLowerCase()) : [];

    if (additional_user_ids && additional_user_ids.length) {
      const add_ids = additional_user_ids.slice();
      while(add_ids.length) {
        const pr = await data.plugins.bigQueryPlugin().findBySkills(user, lterms, lfilter, [user.email], aliases, add_ids.splice(0,1000), require_email, network_only);
        people_stats = people_stats.concat(pr);
      }
    } else {
      people_stats = await data.plugins.bigQueryPlugin().findBySkills(user,  lterms, lfilter, [user.email], aliases, null, require_email, network_only);
    }

    await PersonDataWrapper.updatePeopleWithProfiles(user, people_stats.map(p => p.person));
    return people_stats.map(p => { return { person: p.person, weight: p.stats.score_avg, stddev: p.stats.score_stddev }});
  }

  static async bigQueryByNames(user: User, names: string[], aliases?: string[], additional_user_ids?: Uid[]) {
    let people = [];

    if (additional_user_ids && additional_user_ids.length) {
      const add_ids = additional_user_ids.slice();
      while(add_ids.length) {
        const pr = await data.plugins.bigQueryPlugin().queryPerson(user, null, names, null, aliases, add_ids.splice(0,1000), false);
        people = people.concat(pr);
      }
    } else {
      people = await data.plugins.bigQueryPlugin().queryPerson(user, null, names, null, aliases, null, false);
    }

    await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return people;
  }

  static async bigQueryByEmails(user: User, emails: string[], aliases?: string[], additional_user_ids?: Uid[]) {
    let people = [];

    if (additional_user_ids && additional_user_ids.length) {
      const add_ids = additional_user_ids.slice();
      while(add_ids.length) {
        const pr = await data.plugins.bigQueryPlugin().queryPerson(user, null, null, emails, aliases, add_ids.splice(0,1000));
        people = people.concat(pr);
      }
    } else {
      people = await data.plugins.bigQueryPlugin().queryPerson(user, null, null, emails, aliases);
    }

    await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return people;
  }

  static async bigQuerySearchUserStats(user: User, group_ids: Uid[], global: boolean, user_comms: string[], terms: string[]): Promise<Partial<Person>[]> {
    const people_stats = await data.plugins.bigQueryPlugin().userSearchStats(user, group_ids, global, user_comms, terms ? terms.map(t => t.toLowerCase()) : []);
    return people_stats.map(p => p.person);
  }

  static async bigQuerySearchUsers(user: User,user_comms: string[],  tag_types: TagType[], tag_values: string[]): Promise<Partial<Person>[]> {
    const people = await data.plugins.bigQueryPlugin().userSearch(user, user_comms, tag_types, tag_values);
    return people;
  }

  static async bigQuerySearchGroupUserStats(user: User, additional_user_ids: Uid[],  terms: string[], filter_skills?: string[]): Promise<{
      person: Partial<Person>, 
      weight: number,
      stddev: number,
    }[]> {
    let people_stats = [];
    const lterms = terms ? terms.map(t => t.toLowerCase()) : [];
    const lfilter = filter_skills ? filter_skills.map(s => s.toLowerCase()) : [];

    if (additional_user_ids && additional_user_ids.length) {
      const add_ids = additional_user_ids.slice();
      while(add_ids.length) {
        const pr = await data.plugins.bigQueryPlugin().userPeopleStats(user, add_ids.splice(0,1000), lterms, lfilter);
        people_stats = people_stats.concat(pr);
      }
    } else {
      people_stats  = await data.plugins.bigQueryPlugin().userPeopleStats(user, null, lterms, lfilter);
    }

    return people_stats.map(p => { return { person: p.person, weight: p.stats.score_avg, stddev: p.stats.score_stddev }});
  }

  static async bigQuerySearchGroupUsers(user: User, additional_user_ids: Uid[],  tag_types: TagType[], tag_values: string[]): Promise<Partial<Person>[]> {
    let people = [];

    if (additional_user_ids && additional_user_ids.length) {
      const add_ids = additional_user_ids.slice();
      while(add_ids.length) {
        const pr = await data.plugins.bigQueryPlugin().userPeople(user, add_ids, tag_types, tag_values);
        people = people.concat(pr);
      }
    } else {
      people = await data.plugins.bigQueryPlugin().userPeople(user, null, tag_types, tag_values);
    }

    return people;
  }

  static async export(profile?: string): Promise<string> {
    return data.plugins.bigQueryPlugin().exportPeople(profile);
  }

  static async checkExport(profile: string, name: string): Promise<string> {
    return data.plugins.bigQueryPlugin().checkExport(name, profile);
  }

  static async finishExport(profile: string, source: string) {
    await data.plugins.bigQueryPlugin().finishExport(source, profile);

    if (profile) {
      // it's possble that t/e account got deleted during an export, cleanup here if so
      const global_user = await data.users.globalById(profile);
      if (!global_user) {
        data.plugins.bigQueryPlugin().dropTable(profile).catch(e => {
          logging.errorFP(LOG_NAME, 'finishExport', profile, 'failed to drop table after export when user was deleted', e);
        });
      }
    }
  }

  /* static async importUsers() {
    await data.plugins.bigQueryPlugin().importUsers();
  }*/

  /*static globalInsert(person: Person) {
    return data.plugins.bigQueryPlugin().insertPerson(null, person);
  }

  static globalInsertMany(persons: Person[]): Promise<void> {
    return data.plugins.bigQueryPlugin().insertPersons(persons);
  }

  static bigQueryUpdate(profile: string, person: Person) {
    return data.plugins.bigQueryPlugin().updatePerson(profile, person);
  }*/ 

  /* static globalUpdate(person: Person) {
    return data.plugins.bigQueryPlugin().updatePerson(null, person);
  }*/

  /* static globalUpdateMany(people: Person[]) {
    return data.plugins.bigQueryPlugin().updatePeople(null, people);
  }*/

  static async byAttributeComms(user: User, comms: string[]): Promise<Person[]> {
    const people = await data.plugins.storagePlugin().personByAttributeComms(user, comms);
    people.sort((a,b) => {
      if (!a.id) return 1;
      if (!b.id) return -1;
      if (a.id.match(/^people\/[cmst]/)) return -1;
      if (b.id.match(/^people\/[cmst]/)) return 1;
      if (a.id.startsWith('people/r')) return 1;
      if (b.id.startsWith('people/r')) return -1;
      return -1;
    });

    await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return people;
  }

  static async byAttributeId(user: User, ids: string[], update_profiles = true): Promise<Partial<Person>[]> {
    const profile_links = ids.filter(id => id.startsWith('profile/'));
    const profiles = profile_links.map(id => id.split('/')[1]);
    if (profiles.length) {
      const pids = ids.filter(p => !profile_links.includes(p));
      let people = (await data.plugins.storagePlugin().peopleByAttributeId(user, pids)) as Partial<Person>[];
      const vanities = await data.users.vanities(profiles);

      ids = ids.map(id => id.startsWith('profile/') ? id.split('/')[1] : id);
      people = people.concat(vanities.map(v => peopleUtils.personFromVanity(v))).sort((a,b) => ids.indexOf(a.id) - ids.indexOf(b.id)); 
      if (update_profiles) await PersonDataWrapper.updatePeopleWithProfiles(user, people);
      return people;
    } else {
      const people = await data.plugins.storagePlugin().peopleByAttributeId(user, ids);
      if (update_profiles) await PersonDataWrapper.updatePeopleWithProfiles(user, people);
      return people;
    }
  }

  static async byAttributeLearned(user: User, limit = 10, age = 30, max_per_profile = 10): Promise<{[key: string]: Partial<Person>[]}> {
    const user_people = await data.plugins.bigQueryPlugin().getLearned(user, limit, age, max_per_profile);
    for (const people of Object.values(user_people)) await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return user_people;
  }

  static async byId(user: User, id: string): Promise<Person> {
    const person = await data.plugins.storagePlugin().personById(user, id);
    await PersonDataWrapper.updatePeopleWithProfiles(user, [person]);
    return person;
  }

  static async introductionById(id: string): Promise<Introduction> {
    return data.plugins.storagePlugin().introductionById(id);
  }

  static async introductionsByAtt(att: string, val: string): Promise<Introduction[]> {
    return data.plugins.storagePlugin().introductionsByAtt(att, val);
  }

  static saveIntroduction(intro: Introduction): Promise<Introduction> {
    if (!intro.id) intro.id = uuid();
    if (!intro.created) intro.created = new Date();
    return data.plugins.storagePlugin().introductionSave(intro);
  }

  static async byConnections(user: User, terms: string[], emails: string[]): Promise<Partial<Person>[]> {
    const ids = await data.plugins.bigQueryPlugin().connectedProfiles(user, terms, emails);
    const connections = (await data.plugins.storagePlugin().vanityByIds(ids)).map(c => peopleUtils.personFromVanity(c));
    // await PersonDataWrapper.updatePeopleWithProfiles(user, connections);
    return connections;
  }

  static async connections(user: User): Promise<Partial<Person>[]> {
    const ids = await data.plugins.bigQueryPlugin().knownProfiles(user);
    const connections = (await data.plugins.storagePlugin().vanityByIds(ids)).map(c => peopleUtils.personFromVanity(c));
    // await PersonDataWrapper.updatePeopleWithProfiles(user, connections);
    return connections;
  }

  static async connectedUserProfiles(user: User, emails: string[]): Promise<Uid[]> {
    const ids = await data.plugins.bigQueryPlugin().networkProfiles(user, emails);
    return ids;
  } 

  /*static reloadGlobal(people: Partial<Person>[]) {
    return data.plugins.bigQueryPlugin().reloadGlobalPeople(people);
  }*/

  /* static reloadUsers(people: Partial<Person>[], update = false) {
    return data.plugins.bigQueryPlugin().reloadUsers(people, update);
  }*/

  static async reload(user: User, person: Person): Promise<Person> {
    return PersonDataWrapper.sourcePlugin(user, person.account).reload(person);
  }

  static async batchReload(user: User, people: Partial<Person>[]): Promise<Person[]> {
    if (config.isEnvOffline()) return people as Person[];
    const account_people = {};
    for (const person of people) {
      let account = person.account;
      if (!account) account = user.profile;
      if (!account_people[account]) account_people[account] = [person];
      else account_people[account].push(person);
    }

    let result_people = [];
    for (const account in account_people) {
      const rpeople = await PersonDataWrapper.sourcePlugin(user, account).batchReload(account_people[account]);
      if (rpeople) result_people = result_people.concat(rpeople);
    }
    return result_people;
  }


  static async connectionsByAttributeComms(user: ForaUser, comms: string[]): Promise<Person[]> {
    if (!user.isAuthenticatedNonGuest()) return [];

    const safe_ids = await data.plugins.bigQueryPlugin().lookupConnections(user, user.email, comms);

    const people: Person[] = [];
    if (safe_ids) {
      const safe_user_id = data.plugins.bigQueryPlugin().safeProfile(user.profile);
      for (const id of safe_ids.filter(p => p)) {
        if (id !== safe_user_id) {
          if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'connectionsByAttributeComms', user.profile, `loading connection ${id}`);
          const connection = await data.plugins.storagePlugin().userGlobalById(id);
          if (connection && connection.email && connection.email !== user.email) {
            const comm_people  = await PersonDataWrapper.byAttributeComms(user, [connection.email]);
            if (comm_people && comm_people.length) {
              const person = new Person(comm_people[0]);
              person.vanity = connection.vanity;
              for (const cp of comm_people.slice(1)) peopleUtils.mergePeople(person, cp, true);
              people.push(person);
            } else {
              const user_person = await PersonDataWrapper.getUserPerson(user, id);
              if (user_person) people.push(user_person);
              else logging.warnFP(LOG_NAME, 'connectionsByAttributeComms', user.profile, `User person not found ${id}`);
            }
          } else logging.warnFP(LOG_NAME, 'connectionsByAttributeComms', user.profile, `User not found or no email or is self ${id}`);
        }
      }
    }
    await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return people;
  }

  static async delete(user: User, person: Partial<Person>, remote_delete = true) {
    if (person.self) throw new InternalError(400, `::${user.profile} (${user.email})Cannot delete self ${person.id} (${JSON.stringify(person.comms)})`, person);
    //if (remote_delete && !config.isEnvOffline()) await PersonDataWrapper.sourcePlugin(user, person.account).delete(person);
    // don't wait for bigquery
    await data.plugins.bigQueryPlugin().deletePerson(user.profile, person);
    await data.plugins.storagePlugin().personDelete(user, person);
  }

  static async getIds(user: User, network = false): Promise<Partial<Person>[]> {
    const people = await data.plugins.bigQueryPlugin().getIds(user, network);
    // await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return people;
  }

  static async getUserPeople(user: User, profiles: Uid[], page?: number): Promise<Person[]> {
    let people: Partial<Person>[];
    let user_ids: Uid[] = [];
    const users = await data.plugins.bigQueryPlugin().userPeople(user, profiles, undefined, undefined, page);
    if (users && users.length) {
      people = users;
      user_ids = users.map(u => u.id);
    }

    /*if (page === undefined) {
      const missing = profiles.filter(p => !user_ids.includes(p));
      if (missing && missing.length) {
        const vanities = await data.plugins.storagePlugin().vanityByIds(profiles);
        if (vanities && vanities.length) people = people.concat(vanities.map(peopleUtils.personFromVanity));
      }
    }*/

    const user_people = people ? people.map(p => new Person(p)) : [];
    user_people.forEach(p => p.tempId());
    return user_people;
  }

  static async getUserPerson(user: User, profile: Uid = null, group?: Group): Promise<Person> {
    if (!profile) profile = user.profile;
    if (user.profile === profile && (user.isAnonymousAccount() || user.isGuestAccount())) return lang.init.FORA_INFO();

    const [vanity] = await data.plugins.storagePlugin().vanityByIds([profile]);

    const host = group && group.host ? group.host : config.get('DEFAULT_HOST');
    const vanity_url = `https://${host}/profile/${vanity ? vanity.id : user.vanity}`;

    let person = null;
    if (profile === user.profile) {
      person = await PersonDataWrapper.byId(user, `people/a${profile}`);
      if (person) {
        if(!person.comms.includes(user.email)) person.comms.push(user.email);
        if (vanity) person.bio = vanity.bio
        person.checkSelf(user.profile, vanity ? vanity.id : user.vanity, vanity_url);
        return person;
      }
    }

    if (vanity) person = peopleUtils.personFromVanity(vanity);

    let comms;
    if (user.profile === profile && user.email) comms = [user.email];
    else comms = person && person.comms && person.comms.length ? parsers.findEmail(person.comms) : null;
    if (comms && comms.length) {
      const people = await PersonDataWrapper.byAttributeComms(user, comms);
      if (people && people.length) {
        people.sort((a,b) => {
          const a_source = !a.id.startsWith('people/t');
          const b_source = !b.id.startsWith('people/t');
          if (a_source === b_source) {
            const a_learned = a.learned && a.learned.length ? Math.max(...a.learned.map(l => new Date(l).getTime())) : -1;
            const b_learned = b.learned && b.learned.length ? Math.max(...b.learned.map(l => new Date(l).getTime())) : -1;
            if (a_learned === b_learned) {
              const a_meta = findTypeValue(a.tags, TagType.meta);
              const b_meta = findTypeValue(b.tags, TagType.meta);
              if (a_meta !== null && b_meta !== null) {
                const a_orgs = findTypeValues(a.tags, JobTags);
                const b_orgs = findTypeValues(b.tags, JobTags);
                if (a_orgs.length === b_orgs.length) {
                  const a_skills = findTypeValues(a.tags, TagType.skill);
                  const b_skills = findTypeValues(b.tags, TagType.skill);
                  const a_self = a.skills.includes('self');
                  const b_self = a.skills.includes('self');
                  if (a_self && b_self) return b_skills.length - a_skills.length
                  else if (a_self) return -1;
                  return 1;
                } else if(b_orgs.length !== a_orgs.length) return b_orgs.length - a_orgs.length;
                else if(a.id < b.id) return 1;
                return -1;
              } else if(a_meta !== null) return -1;
              return 1;
            } else return b_learned - a_learned;
          } else if (a_source) return -1;
          return 1;
        });

        for (const p of people) {
          if (person) peopleUtils.mergePeople(person, p, true);
          else person = p;
          if (p.id && (!person.id || person.id.startsWith('peoeple/r'))) person.id = p.id;
        }
      }
    }
    
    if (person) {
      person.self = user.profile === profile;
      if (person.self) {
        if(!person.comms.includes(user.email)) person.comms.push(user.email);
        if (vanity) person.bio = vanity.bio;
        person.checkSelf(user.profile, vanity ? vanity.id : user.vanity, vanity_url);
      } else if(!person.id || person.id === ANONYMOUS_ID) person.tempId();
    }

    return person;
  }

  static async getGroupIds(user: User, group_ids: Uid[]): Promise<Partial<Person>[]> {
    const people = await data.plugins.bigQueryPlugin().getGroupIds(user, group_ids);
    await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return people;
  }

  static vanityByIds(ids: Uid[]): Promise<Partial<Vanity>[]> {
    return data.plugins.storagePlugin().vanityByIds(ids);
  }

  static async getNamesAndOrgs(user: User, network = true): Promise<Partial<Person>[]> {
    const people = await data.plugins.bigQueryPlugin().getNamesAndTags(user, JobTags, network);
    await PersonDataWrapper.updatePeopleWithProfiles(user, people);
    return people;
  }

  static async getGlobalTags(user: User, limit: number) : Promise<SkillStat[]> {
    const stats = await data.plugins.bigQueryPlugin().getGlobalTags(user, limit);
    return stats ? stats.filter(s => !parsers.ignore(s.value.toLowerCase())) : [];
  }

  static async getGlobalUserTags(user: ForaUser, user_comms: string[], limit: number) : Promise<SkillStat[]> {
    const stats = await data.plugins.bigQueryPlugin().getGlobalUserTags(user, user_comms, limit);
    return stats ? stats.filter(s => !parsers.ignore(s.value.toLowerCase())) : [];
  }

  static async getTags(user: User, user_comms: string[], limit: number, network = false, additional_ids?: Uid[]): Promise<SkillStat[]> {
    const stats = await data.plugins.bigQueryPlugin().getTags(user, user_comms, limit, network, additional_ids);
    return stats ? stats.filter(s => !parsers.ignore(s.value.toLowerCase())) : [];
  }

  static async getOrgs(user: User, user_comms: string[], limit: number, network = false, additional_ids?: Uid[]): Promise<SkillStat[]> {
    const orgs = await data.plugins.bigQueryPlugin().getTags(user, user_comms, limit, network, additional_ids, JobTags, true, true);
    return orgs.filter(org => org.value && !parsers.isDomain(org.value));
  }

  static async getGlobalUserOrgs(user: ForaUser, limit: number) : Promise<SkillStat[]> {
    const stats = await data.plugins.bigQueryPlugin().getGlobalUserTags(user, null, limit, JobTags, true, true);
    return stats ? stats.filter(s => !parsers.ignore(s.value.toLowerCase())) : [];
  }

  static async getStats(user: User, user_comms: string[], additional_ids?: Uid[], network = false): Promise<Stats> {
    return data.plugins.bigQueryPlugin().getStats(user, user_comms, additional_ids, network);
  }

  static checkSkills(person: Person) {
    let skills = person.tags.filter(t => t.type === TagType.skill);
    const skill_bias: {[key:string]: Tag} = {};
    skills.forEach(skill => {
      if(skill_bias[skill.value]) {
        const a = Math.max(skill_bias[skill.value].bias, skill_bias[skill.value].index);
        const b = Math.max(skill.bias, skill.index);
        if (b > a) skill_bias[skill.value] = skill;
      } else skill_bias[skill.value] = skill;
    });
    skills = Object.values(skill_bias).sort((a,b) => Math.max(b.bias, b.index) - Math.max(a.index, a.bias));

    const non_skills = person.tags.filter(t => t.type !== TagType.skill).sort((a,b) => b.index - a.index);
    while(JSON.stringify([...non_skills, ...skills]).length >= 1024 * 1024 - 89
      || JSON.stringify(person).length >= 1024 * 1024 - 4) {
      if (skills.length) skills.pop();
      else non_skills.pop();
      person.tags = [...skills, ...non_skills];
    }
  }

  // create at source (google, msft)
  static async createSource(user: User, person: Person) {
    if (config.isEnvOffline()) {
      if (!person.id) person.id = `people/o${hash(JSON.stringify(person)).replace(/-/g, '')}`;
      await PersonDataWrapper.updatePeopleWithProfiles(user, [person]);
      return person;
    }

    const comms = person.comms ? parsers.findEmail(person.comms) : null;
    if (!comms || !comms.length) throw new InternalError(500, 'Cannot create or update source contact without email', person);

    let rperson = person;
    try { 
      /*const [cperson, cdeletes] = await PersonDataWrapper.sourcePlugin(user, person.account).create(person);
      rperson = cperson;
      rperson.self = person.self;
      rperson.vanity = person.vanity
      rperson.recommendation = person.recommendation;
      rperson.bio = person.bio;
      if (cdeletes && cdeletes.length) await data.plugins.storagePlugin().removeAll(user, cdeletes);*/
      await PersonDataWrapper.updatePeopleWithProfiles(user, [rperson]);
      return rperson;
    } catch(err) {
      if (rperson) {
        logging.errorFP(LOG_NAME, 'createSource', user.profile, 'Error removing ancillary deletes from person creation', err);
        await PersonDataWrapper.updatePeopleWithProfiles(user, [rperson]);
        return rperson;
      }
      logging.errorFP(LOG_NAME, 'createSource', user.profile, `Error creating person ${person.id} at source`, err)
      await PersonDataWrapper.updatePeopleWithProfiles(user, [person]);
      return person;
    }
  }

  static async newPerson(user: ForaUser, person: Partial<Person>, cache: DataCache): Promise<Person> {

    // create a copy to update, save and return
    let save_person = new Person(person);
    await PersonDataWrapper.updatePeopleWithProfiles(user, [save_person]);

    // check for matching comms in datastore
    const email = person.comms ? parsers.findEmail(person.comms) : null;
    const match_people = email && email.length ? (await data.plugins.storagePlugin().personByAttributeComms(user, person.comms)).filter(c => c.comms && !c.comms.includes(user.email)) : [];
    let global_person;

    if (email && email.length) {
      const global = await data.users.globalByEmail(email.filter(e => e !== user.email));
      if (global && global.length) {
        const profiles = global.map(g => g.profile).filter(p => p !== user.profile);
        // pick first profile
        if (profiles && profiles.length) {
          const [vanity] = await data.plugins.storagePlugin().vanityByIds(profiles);
          if (vanity) global_person = peopleUtils.personFromVanity(vanity);
        }
      }
    }

    if (global_person) match_people.push(global_person);

    // found a match in our data
    if (match_people) {
      for (const match_person of match_people) {
        // create a combined entity to work with, overriding with new info
        peopleUtils.mergePeople(match_person, save_person, true);
        save_person = match_person;
      }
    }

    // don't save people to datastore in demo mode
    if (user.isAuthenticated(AuthLevel.Demo)) return save_person;
    
    if (!save_person.id) save_person.tempId();

    let oindex = 0;
    const is = findTypeIndexes(save_person.tags);
    if (is.length) oindex = is[is.length - 1] + 1;

    const porgs = findTypeValues(save_person.tags, TagType.organization).map(o => o.toLowerCase());
    for (const index in save_person.tags) {
      const tag = save_person.tags[index];
      tag.start = tag.start ? new Date(tag.start) : new Date(0);
      if (tag.type as TagType === TagType.skill) {
        const name = await cache.peopleName(tag.value);
        if (name) {
          tag.del = true;
        } else {
          const org = await cache.orgPeople(tag.value);
          if (org) {
            if (porgs.includes(tag.value) || tag.bias < 20) {
              tag.del = true;
            } else {
              tag.value = camelCase(tag.value);
              tag.type = TagType.organization;
              tag.index = oindex;
              tag.start = new Date(0);
              oindex++;
            }
          }
        }
      }
    }

    save_person.tags = save_person.tags.filter(t => !t.del);

    // don't create people with the primary users's email
    if (!person.self || !person.comms || !person.comms.includes(user.email)) {
      save_person.comms = save_person.comms.filter(c => c !== user.email);
    }

    const new_person = await PersonDataWrapper.savePerson(user, save_person, save_person.network !== true);
    const del_people = match_people ? match_people.filter(p => p.id && p.id !== new_person.id) : [];
    for (const del of del_people) await PersonDataWrapper.delete(user, del, true);
    return new_person;
  }

  static async savePerson(user: ForaUser, save_person: Person, new_person = false): Promise<Person> {
    // check for empty people
    if (!save_person.displayName || !save_person.displayName.length) throw new InternalError(500, 'Cannot create person with no name', save_person);
    // save initial pid and comms for comparison later in case we need to remove a duplicate
    let pid = save_person.id;
    const rcomms = save_person.comms.slice();

    const orgs = save_person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
    parsers.cleanTags(save_person.tags, save_person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
    save_person.comms = save_person.comms.filter(c => c && !c.startsWith('people/'));
    if (save_person.learned) save_person.learned = save_person.learned.map(l => new Date(l));

    const comms = save_person.comms ? parsers.findEmail(save_person.comms) : null;
    if (new_person && comms && comms.length) {
      const create_person = await PersonDataWrapper.createSource(user, save_person);
      if (create_person) {
        const save_self = save_person.self ? save_person : null;
        logging.infoFP(LOG_NAME, 'savePerson', user.profile, `Updating person id from ${save_person.id} to ${create_person.id}`);
        save_person = create_person;
        if (save_self) {
          save_person.self = true;
          save_person.bio = save_self.bio;
        if (!save_person.learned || !save_person.learned.length) save_person.learned = [new Date(0)];
        }
      }
    } else if (new_person) {
      logging.warnFP(LOG_NAME, 'savePerson', user.profile, `Trying to save person to source with no comms ${JSON.stringify(save_person)}`, new Error());
    }

    if (!save_person.id) save_person.tempId();

    saveOne(save_person.comms, save_person.id);

    // save the google person in the datastore
    await PersonDataWrapper.save(user, save_person);
    // TODO: bigquery
    /*if (new_person) {
      if (config.isEnvOffline()) await PersonDataWrapper.export(user.profile);
    } else if (SourceController.firstRunDone(user, EntityType.Person) && !save_person.network) {
      try { 
        await PersonDataWrapper.bigQueryUpdate(user.profile, save_person);
      } catch (e) { 
        logging.warnFP(LOG_NAME, 'savePerson', user.profile, `Error updating bigquery with person ${save_person.id}`, e);
      }
    }*/

    // fix pids and comm caches
    if (pid && save_person.id !== pid) {
      for (const comm of rcomms) data.plugins.cachePlugin().clearCacheAttVal(user.profile, EntityType.Person, 'comms', comm);
      await data.plugins.storagePlugin().remove(user.profile, { id: pid, type: EntityType.Person } as Person);
      const idx = save_person.comms.indexOf(pid);
      if (idx !== -1) save_person.comms.splice(idx, 1);
      pid = null;
    }

    return save_person;
  }

  static async load(user: ForaUser, fields: string[] = null, filter_self = true, update_profiles = true) {
    let people_result;
    if ((fields && fields.length) || config.isEnvOffline() || !SourceController.firstRunDone(user)) people_result = await data.plugins.storagePlugin().people(user, fields);
    else people_result = await data.plugins.bigQueryPlugin().getPeople(user, filter_self ? user.email : null);
    // make sure self is from data store
    const self_set = filter_self ? await PersonDataWrapper.byAttributeComms(user, [user.email]) : null;
    if (update_profiles) await PersonDataWrapper.updatePeopleWithProfiles(user, people_result);
    if (self_set && people_result) return people_result.filter(p => !p.comms || !p.comms.includes(user.email)).concat(self_set);
    else return people_result;
  }

  static async savePhoto(user: ForaUser, photo: Document): Promise<Document> {
    return data.plugins.storagePlugin().photoSave(user, photo);
  }

  static async photoById(user: ForaUser, photo_id: Uid): Promise<Document> {
    return data.plugins.storagePlugin().photoById(user, photo_id);
  }

  static async deletePhoto(user: ForaUser, photo_id: Uid): Promise<void> {
    return data.plugins.storagePlugin().photoDelete(user, photo_id);
  }

  // look for people who share the same comms and resolve
  static async resolveSavePerson(user: User, save_person: Person, people: {[key: string]: Person } = null, people_comms: {[key: string]: Uid[]} = null, source_duplicates: Uid[] = null): Promise<{ remove: Person; save: Person }> {
    // check if there are existing people who match using email
    const emails: string[] = parsers.findEmail(save_person.comms);

    let remove_person = null;
    // TODO: right now, this just checks for matches from non-google save people
    if (/*save_person.id.includes('people/r') &&*/ emails && emails.length) {
      // don't use the cache in matchPerson
      let persons: Person[] = [];
      if (people_comms) {
        for (const email of emails) {
          const ids = people_comms[email];
          if (ids && ids.length) {
            for (const id of ids) {
              const id_person = people ? people[id] : await PersonDataWrapper.byId(user, id);
              if (id_person) persons = persons.concat(new Person(id_person));
              else logging.warnFP(LOG_NAME, 'resolveSavePerson', user.profile, `No person with id ${id}`);
            }
          } else logging.warnFP(LOG_NAME, 'resolveSavePerson', user.profile, `No person with comm ${email}`);
        }
      } else if(save_person.self) persons = (await data.plugins.storagePlugin().findAtt(user.profile, EntityType.Person, 'comms', [user.email], false)) as Person[];
      else persons = (await data.plugins.storagePlugin().findAtt(user.profile, EntityType.Person, 'comms', emails, false)) as Person[];

      persons.sort((a,b) => {
        if (!a.id) return 1;
        if (!b.id) return -1;
        if (a.id.startsWith('people/a')) return -1;
        if (b.id.startsWith('people/a')) return 1;
        if (a.id.startsWith('people/c') || a.id.startsWith('people/m') || a.id.startsWith('people/s')) return -1;
        if (b.id.startsWith('people/c') || b.id.startsWith('people/m') || b.id.startsWith('people/s')) return 1;
        if (a.id.startsWith('people/r')) return 1;
        if (b.id.startsWith('people/r')) return -1;
        return -1;
      });

      if (logging.isDebug(user.profile)) DEBUG('resolveSavePerson: persons = %j', persons);

      let reloaded_save_person;
      let did_reload = false;

      for (const person of persons) {
        if (!person.tags) person.tags = [];
        if (!person.urls) person.urls = [];
        if (!person.learned) person.learned = [];
        if (!person.photos) person.photos = [];

        if (person.id === save_person.id) {
          // fully update the person we have saved
          peopleUtils.mergePeople(person, save_person, true);
          save_person = person;
        } else if(person.id && person.id.startsWith('people/a') && person.id !== ANONYMOUS_ID) {
          if (save_person.id && save_person.id.startsWith('people/a') && save_person.id !== ANONYMOUS_ID) {
            if (save_person.id !== person.id) {
              logging.warnFP(LOG_NAME, 'resolveSavePerson', user.profile, `Duplicate self contacts ${person.id} and ${save_person.id}`);
              if (source_duplicates) {
                if(!source_duplicates.includes(person.id)) source_duplicates.push(person.id);
                if(!source_duplicates.includes(save_person.id)) source_duplicates.push(save_person.id);
              }

              // merge and keep save_person
              peopleUtils.mergePeople(save_person, person, true);
              remove_person = person;
            } else remove_person = null;
          } else {
            // fold save_person into person, extending person, deleting save_person
            if (!person.self || save_person.self) peopleUtils.mergePeople(person, save_person, true);
            remove_person = save_person;
            save_person = person;
          }
        } else if (person.id && person.id.startsWith('people/c') || person.id.startsWith('people/m') || person.id.startsWith('people/s')) {
          if (save_person.id && save_person.id.startsWith('people/a') && save_person.id !== ANONYMOUS_ID) {
            // if (!save_person.self) peopleUtils.mergePeople(save_person, person, true);
            remove_person = person;
          } else if (save_person.id && (save_person.id.startsWith('people/c') || save_person.id.startsWith('people/m') || save_person.id.startsWith('people/s'))) {
            logging.warnFP(LOG_NAME, 'resolveSavePerson', user.profile, `Duplicate contacts ${person.id} and ${save_person.id}`);
            if (source_duplicates) {
              if(!source_duplicates.includes(person.id)) source_duplicates.push(person.id);
              if(!source_duplicates.includes(save_person.id)) source_duplicates.push(save_person.id);
            } else {
              //check if any have been deleted
              try {
                const reloaded_person = await PersonDataWrapper.reload(user, person);
                if (!reloaded_person) {
                  logging.warnFP(LOG_NAME, 'resolveSavePerson', user.profile, `Duplicate contact ${person.id} was removed from source`);
                  peopleUtils.mergePeople(save_person, person, true);
                  remove_person = person;
                  if (people && people[person.id]) delete people[person.id];
                } else if(!did_reload) {
                  did_reload = true;
                  reloaded_save_person = await PersonDataWrapper.reload(user, save_person);
                  if (!reloaded_save_person) {
                    logging.warnFP(LOG_NAME, 'resolveSavePerson', user.profile, `Duplicate contact ${save_person.id} was removed from source`);
                    if (!person.self || save_person.self) {
                      peopleUtils.mergePeople(person, save_person, true);
                      if (people && people[save_person.id]) delete people[person.id];
                    }
                    remove_person = save_person;
                    save_person = person;
                  }
                }
              } catch(e) {
                if (e.message.includes('No access, refresh token, API key or refresh handler callback is set.')) logging.warnFP(LOG_NAME, 'resolveSavePerson', user.profile, `Error resolving duplicate contacts ${person.id} and ${save_person.id}`, e);
                else logging.errorFP(LOG_NAME, 'resolveSavePerson', user.profile, `Error resolving duplicate contacts ${person.id} and ${save_person.id}`, e);
              }
            }
         } else {
            // fold save_person into person, extending person, deleting save_person
            if (!person.self || save_person.self) peopleUtils.mergePeople(person, save_person, true);
            remove_person = save_person;
            save_person = person;
          }
        } else {
          if (save_person.id.startsWith('people/a') && save_person.id !== ANONYMOUS_ID) {
            if (!save_person.self) peopleUtils.mergePeople(save_person, person, true);
            remove_person = person;
          } else if (save_person.id.startsWith('people/c') || save_person.id.startsWith('people/m') || save_person.id.startsWith('people/s')) {
            // fold person into save_person, save_person overrides, delete person
            if (!save_person.self) peopleUtils.mergePeople(save_person, person, true);
            remove_person = person;
          } else {
            // merge save_person and person, delete save_person
            if (!person.self) peopleUtils.mergePeople(person, save_person, true);
            remove_person = save_person;
            save_person = person;
          }
        }
      }
    }

    return {
      save: save_person,
      remove: remove_person,
    };
  }

  static async save(user: ForaUser, person: Person) {
    if(person.self && (!person.id || !person.id.startsWith('people/a') || person.id === ANONYMOUS_ID)) throw new InternalError(500, `Cannot create person with self with invalid id ${person.id}`, person);
    if(person.comms.includes(user.email) && !person.self) throw new InternalError(500, `Cannot create person with self email who is not self`, person);
    if (!person.displayName || !person.displayName.length) throw new InternalError(500, 'Cannot create person with no name', person);
    if(config.isEnvOffline() && person.self && person.id.startsWith('people/r')) throw new InternalError(500, `Cannot create person with self email with people/r id`, person);

    const orgs = person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
    parsers.cleanTags(person.tags, person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
    if (person.learned) person.learned = person.learned.map(l => new Date(l));

    PersonDataWrapper.checkSkills(person);

    if (!person.self) await PersonDataWrapper.updatePeopleWithProfiles(user, [person]);
    return data.plugins.storagePlugin().personSave(user, person);
  }

  static async saveAll(user: ForaUser, people: Person[]) {
    const update_people: Person[] = [];
    for (const person of people) {
      if (!person.displayName || !person.displayName.length) {
        logging.errorFP(LOG_NAME, 'saveAll', user.profile, `Error saving person ${person.id}`, new InternalError(500, 'Cannot create person with no name', person));
      } else if(person.comms.includes(user.email) && !person.self) {
        logging.errorFP(LOG_NAME, 'saveAll', user.profile, `Error saving person ${person.id} with user email who is not self`, new InternalError(500, 'Cannot create person with user email who is not self', person));
      } else if(config.isEnvOffline() && person.self && person.id.startsWith('people/r')) {
        logging.errorFP(LOG_NAME, 'saveAll', user.profile, `Cannot create person with self email with people/r id`, new InternalError(500, `Cannot create person with self email with people/r id`, person));
      } else {
        const orgs = person.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
        parsers.cleanTags(person.tags, person.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));
        if (person.learned) person.learned = person.learned.map(l => new Date(l));
        if (!person.self) update_people.push(person);
      }
      PersonDataWrapper.checkSkills(person);
    }

    await PersonDataWrapper.updatePeopleWithProfiles(user, update_people);

    return data.plugins.storagePlugin().peopleSave(user, people.filter(p => p.displayName && p.displayName.length));
  }

  static sourcePlugin(user: User, account: Uid): IPeopleSourcePlugin {
    switch (user.provider) {
      case AuthProviders.Msal:
      case AuthProviders.Microsoft:
        return new (data.plugins.sourcePlugins().get(SourceKeys.MicrosoftPeople))(user, account);
      case AuthProviders.Google:
      default:
        return new (data.plugins.sourcePlugins().get(SourceKeys.GooglePeople))(user, account);
    }
  }

  static tempPerson(person: Partial<Person>): Person {
    const temp_person = new Person(person);
    if (!temp_person.id) temp_person.tempId();
    return temp_person;
  }
}
