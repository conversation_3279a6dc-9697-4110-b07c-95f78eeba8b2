/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import data from '..';
import config from '../../config';

import Dialog, { Profile } from '../../session/dialog';

import * as globalTypes from '../../types/globals';
import { GlobalType } from '../../types/items';

import { DAYS } from '../../utils/datetime';
import { compress, uncompress } from '../../utils/funcs';
import logging from '../../utils/logging';

// const DEBUG = require('debug')('fora:data:wrappers:session');
const LOG_NAME = 'data.wrappers.SessionDataWrapper';

export class SessionDataWrapper {
  static async byId(id: Profile): Promise<Dialog> {
    let dialog: Dialog = null;
    const sessions = (await data.plugins.storagePlugin().find(null, GlobalType.Session, id)) as globalTypes.Session[];

    if (config.isEnvOffline()) {
      switch (sessions.length) {
        case 0:
          break;
        case 1: 
          {
            if (logging.isDebug(id)) logging.debugFP(LOG_NAME, 'byId', id, `sessionDialog:sessions[0] = ${logging.formatEntity(sessions[0])}`);
            const cdialog = uncompress(sessions[0].dialog);
            if (id === sessions[0].id) dialog = cdialog;

            break;
          }
        default:
          throw new Error('More than one session found');
      }

      return dialog;
    } else {
      switch (sessions.length) {
        case 0:
          break;
        case 1:
          if (logging.isDebug(id)) logging.debugFP(LOG_NAME, 'byId', id, `sessionDialog:sessions[0] = ${logging.formatEntity(sessions[0])}`);
          dialog = uncompress(sessions[0].dialog);

          if (new Date(sessions[0].last) < DAYS(new Date(), -30)) {
            await data.plugins.storagePlugin().remove(null, { id, type: GlobalType.Session });
            dialog = null;
          }

          break;
        default:
          throw new Error('More than one session found');
      }

      return dialog;
    }
  }

  static async cleanup(): Promise<void> {
    const now = new Date();

    const sessions: any[] = await data.plugins.storagePlugin().findRange(null, GlobalType.Session, 'last', DAYS(now, -30), false);
    if (sessions) {
      const del_set = [];
      sessions.forEach((session: any) => {
        const key = data.plugins.storagePlugin().key(null, { id: session.id, type: GlobalType.Session });
        logging.infoFP(LOG_NAME, 'cleanup', session.profile, `Removing stale session ${key.name}`);
        del_set.push(key);
      });
      await data.plugins.storagePlugin().delete(null, GlobalType.Session, del_set);
    }

    // cleanup contracts
    const contracts: globalTypes.Contract[] = (await data.plugins.storagePlugin().findRange(null, GlobalType.Contract, 'created', DAYS(now, -30), false)) as globalTypes.Contract[];

    if (contracts) {
      const del_set = [];
      contracts.forEach((contract: globalTypes.Contract) => {
        const key = data.plugins.storagePlugin().key(null, { id: contract.id, type: GlobalType.Contract });
        const cobj = uncompress(contract.contract);
        logging.infoFP(LOG_NAME, 'cleanup', null, `Removing stale contract ${key.name} for ${cobj.client_id} and ${cobj.contractor_id}`);
        del_set.push(key);
      });

      // for(var index in del_sessions) ds.delete(del_sessions[index]);
      await data.plugins.storagePlugin().delete(null, GlobalType.Contract, del_set);
    }
  }

  static async delete(session_id: string): Promise<void> {
    const key = data.plugins.storagePlugin().key(null, { id: session_id, type: GlobalType.Session });
    return data.plugins.storagePlugin().delete(null, GlobalType.Session, [key]);
  }

  static async save(session_id, save_session): Promise<void> {
    const key = data.plugins.storagePlugin().key(null, { id: session_id, type: GlobalType.Session });

    if (logging.isDebug(save_session.profile)) logging.debugFP(LOG_NAME, 'save', save_session.profile, `Saving session ${session_id} to datastore`);

    // don't save context
    save_session.context = {};

    const comp = compress(save_session);

    const convertedSession = {
      key,
      excludeFromIndexes: ['dialog'],
      data: { id: session_id , profile: save_session.profile , last: save_session.last || new Date(),  dialog: comp },
    };

    // debugFP('save: converted session = %j', convertedSession);
    return data.plugins.storagePlugin().save(null, GlobalType.Session, convertedSession);
  }
}
