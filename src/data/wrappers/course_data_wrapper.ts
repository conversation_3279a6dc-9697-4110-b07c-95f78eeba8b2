/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { v4 as uuid } from 'uuid';

import data from '..';


import { Course, ExtendedCategory } from '../../types/globals';
import { Document } from '../../types/items';
import { Uid } from '../../types/shared';

import { filterRelated } from '../../skills/index';

import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.CourseDataWrapper';

export class CourseDataWrapper extends AbstractDataWrapper {
  static async get(id: Uid): Promise<Course> {
    return data.plugins.storagePlugin().courseGet(id);
  }

  static async load(): Promise<Course[]> {
    // return data.plugins.storagePlugin().coursesLoad();
    return data.plugins.bigQueryPlugin().findCourses();
  }

  static async save(course: Course): Promise<Course> {
    if (!course.id) course.id = uuid();
    return data.plugins.storagePlugin().courseSave(course);
  }

  static async saveCourses(courses: Course[]): Promise<Course[]> {
    courses.forEach(course => { if (!course.id) course.id = uuid(); });
    return data.plugins.storagePlugin().coursesSave(courses);
  }

  static async delete(course: Course): Promise<void> {
    return data.plugins.storagePlugin().courseDelete(course);
  }

  static async assignSkills(course: Partial<Course>, skills: string[], remap = false): Promise<string[]> {
    if (course.skills?.length)  {
      const related = 
        remap ? await filterRelated(skills, course.skills, 0, true) :
        await filterRelated(skills, course.skills, 0.3);
      return related;
    } 
    return [];
  }

  static assignWeight(course: Partial<Course>) {
    if (course.link?.length) {
      // TODO: create proper mapping
      if (course.link.includes('alison.com')) return 60;
      if (course.link.includes('linkedin.com')) return 50;
      if (course.link.includes('skillshare.com')) return 60;
      if (course.link.includes('edx.org')) return 80;
      if (course.link.includes('phoenix.edu')) return 80;
      if (course.link.includes('ted.com')) return 40;
      if (course.link.includes('masterclass.com')) return 60;
      if (course.link.includes('udemy.com')) return 50;
      if (course.link.includes('coursera.com')) return 50;
    } 
    return 50;
  }

  static async findBySkills(skills: string[], limit?: number, skip_ids?: Uid[]): Promise<Course[]> {
    // return data.plugins.storagePlugin().courseBySkills(skills);
    return data.plugins.bigQueryPlugin().findCourses(skills.map(s => s.toLowerCase()), limit, skip_ids);
  }

  /*static async findByVector(vector: number[], limit?: number, skip_ids?: Uid[]): Promise<Course[]> {
    return data.plugins.bigQueryPlugin().findCoursesByVector(vector, limit, skip_ids);
  }*/

  static async findByCategory(categories: Partial<ExtendedCategory>[], skip_ids?: Uid[], sources?: string[], level?: string[]): Promise<{id: Uid, courses: {course: Course, score: number}[]}[]> {
    return data.plugins.bigQueryPlugin().findCoursesByCategories(categories, skip_ids, sources, level);

  }

  static async storeCourse(doc: Document) {
    return data.plugins.storagePlugin().courseSourceSave(doc);
  }

  static async loadCourse(file_id: Uid): Promise<Document> {
    return data.plugins.storagePlugin().courseSourceById(file_id);
  }
}