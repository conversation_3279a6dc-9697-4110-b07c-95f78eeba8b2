/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import data from '..';
import config from '../../config';
import { OnboardingStep, Template, TemplateType } from '../../types/globals';
import { NotificationType } from '../../types/shared';

// const DEBUG = require('debug')('fora:data:wrappers:group');
// const LOG_NAME = 'data.wrappers.GroupDataWrapper';

export class TemplateDataWrapper {
  static get(notification: NotificationType, template: TemplateType): Promise<Template> {
    const template_id  = `${notification}${template ? '.' : ''}${template ? template : ''}`;
    return data.plugins.storagePlugin().template(template_id);
  }

  static delete(notification: NotificationType, template: TemplateType): Promise<void> {
    if (!config.isRunningOnGoogle()) {
      const template_id  = `${notification}${template ? '.' : ''}${template ? template : ''}`;
      return data.plugins.storagePlugin().templateDelete(template_id);
    }
  }

  static onboarding(template: TemplateType): Promise<OnboardingStep> {
    return data.plugins.storagePlugin().onboardingStep(template);
  }

  static save(notification: NotificationType, template: TemplateType, id: number): Promise<Template> {
    if (!config.isRunningOnGoogle()) {
      const template_id  = `${notification}${template ? '.' : ''}${template ? template : ''}`;
      return data.plugins.storagePlugin().templateSave(template_id, id);
    }
  }

  static templates(): Promise<Template[]> {
    return data.plugins.storagePlugin().templates();
  }
}
