import _ from 'lodash';

import config from '../../config';

import { Category, ScoreStat, Skill, SkillCategory } from '../../types/globals';
import { Group } from '../../types/group';
import { GlobalType, Person } from '../../types/items';
import { Uid } from '../../types/shared';
import { User } from '../../types/user';

import { MINUTES } from '../../utils/datetime';
import { compress, flatten, uncompress } from '../../utils/funcs';
import logging from '../../utils/logging';
import parsers from '../../utils/parsers';

import data from '..';

import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.SkillDataWrapper';

export class SkillDataWrapper extends AbstractDataWrapper {
  static word_map: {[key:string]: {name: string, vector: number[], cached: Date}} = {};

  static byName(name: string): Promise<Skill> {
    return data.plugins.storagePlugin().skillByName(name);
  }

  static byInitialism(initials: string): Promise<Skill[]> {
    return data.plugins.storagePlugin().skillByInitialism(initials);
  }

  static bySynonym(synonym: string): Promise<Skill[]> {
    return data.plugins.storagePlugin().skillBySynonym(synonym);
  }

  static find(names: string[], synonyms = false, initials = false): Promise<Skill[]> {
    return data.plugins.storagePlugin().skillFind(names, synonyms, initials);
  }

  static async load(): Promise<Skill[]> {
    const s = config.isEnvOffline() ? null : await data.plugins.bigQueryPlugin().loadSkills();
    if (s && s.length) return s;
    return data.plugins.storagePlugin().skills();
  }

  static loadUser(): Promise<Person[]> {
    return data.plugins.bigQueryPlugin().getUserSkills();
  }

  static loadPeople(): Promise<string[]> {
    return data.plugins.bigQueryPlugin().getPeopleSkills();
  }

  static async loadSynonyms(): Promise<Skill[]> {
    const skills = await data.plugins.bigQueryPlugin().fetchSkills();
    skills.forEach(s => {
      s.skill = parsers.skillExtract(parsers.remap(s.skill));
      if (s.skill && s.skill.length) {
        s.synonyms = s.synonyms.map(syn => parsers.skillExtract(parsers.remap(syn)));
      }
    });

    return skills.filter(s => s.skill && s.skill.length);
  }

  static async delete(skill: Partial<Skill>) {
    return data.plugins.storagePlugin().skillDelete(skill);
  }

  static async save(skill: Skill): Promise<Skill> {
    return data.plugins.storagePlugin().skillSave(skill);
  }

  static async saveAll(skill: Skill[]): Promise<Skill[]> {
    return data.plugins.storagePlugin().skillSaveAll(skill);
  }

  static async mapCategories(user: User, categories: Partial<Category>[], people: Partial<Person>[]): Promise<{category_id: Uid, person_id: Uid, vanity: string, score: number}[] > {
    return data.plugins.bigQueryPlugin().mapCategories(user, categories, people);
  }

  static async deleteCategory(group: Group, sc: SkillCategory) {
    await data.plugins.storagePlugin().skillCategoryDelete(group.id, sc);
    this.exportSkillCategories(group);
  }

  static async findCategories(group_ids: Uid[], global, terms: string[], add_terms: string[] = [], skip_ids?: Uid[], limit = 100): Promise<{category: SkillCategory, skills: string[], stats: ScoreStat}[]> {
    return data.plugins.bigQueryPlugin().findSkillCategoryStats(group_ids, global, terms, add_terms, skip_ids, limit);
  }

  static async peopleCategories(user: User, people: Partial<Person>[], group_ids: Uid[], global: boolean): Promise<{category_id: Uid, person_id: Uid, vanity: string, score: number, match: string[]}[] > {
    return data.plugins.bigQueryPlugin().peopleCategories(user, people, group_ids, global);
  }

  static async loadCategories(group_id: Uid): Promise<SkillCategory[]> {
    const sc = config.isEnvOffline() ? null : await data.plugins.bigQueryPlugin().loadCategories(group_id);
    if (sc && sc.length) return sc;
    return data.plugins.storagePlugin().skillCategories(group_id);
  }

  static async deleteCategories(sc: SkillCategory[]) {
    return data.plugins.storagePlugin().skillCategoryDeleteSome(sc);
  }

  static async saveCategories(sc: SkillCategory[], reset = true): Promise<SkillCategory[]> {
    if (reset) await data.plugins.storagePlugin().skillCategoryDeleteAll();
    return data.plugins.storagePlugin().skillCategorySaveAll(sc);
  }

  static async saveCategory(group: Group, sc: SkillCategory): Promise<SkillCategory> {
    return data.plugins.storagePlugin().skillCategorySave(group?.id, sc);
  }

  static async exportSkills() {
    return data.plugins.bigQueryPlugin().exportGlobal(GlobalType.Skill);
  }

  static async exportSkillCategories(group?: Group) {
    return data.plugins.bigQueryPlugin().exportGlobal(GlobalType.SkillCategory, group?.id);
  }

  static async relatedWords(words: string[]): Promise<string[]> {
    return data.plugins.bigQueryPlugin().relatedWords(words);
  }

  static async word(w: string): Promise<{name: string, vector: number[]}> {
    if (!w || !w.length) return null;
    const cached = new Date();

    const dw = SkillDataWrapper.word_map[w];
    if (dw) {
      if (dw.vector && dw.vector.length) return dw;
      if (dw.cached > MINUTES(cached, -5)) return null;
    }

    const f = await data.plugins.cachePlugin().get(`word_${w}`);
    let mw = f ? f.value : null;
    if (mw) {
      mw = uncompress(mw);
      SkillDataWrapper.word_map[w] = {name: mw.name, vector: mw.vector, cached};
      return mw;
    }
    mw = await data.plugins.storagePlugin().word(w);
    if (mw) {
      await data.plugins.cachePlugin().set(`word_${w}`, compress({name: mw.name, vector: mw.vector}), { expires: 30 * 24 * 3600 });
      SkillDataWrapper.word_map[w] = {name: mw.name, vector: mw.vector, cached};
      return mw;
    }
    SkillDataWrapper.word_map[w] = {name: w, vector: null, cached};
    return null;
  }

  static async words(ws: string[], expand = false): Promise<{name: string, vector: number[]}[]> {
    const mp = []; 
    const found = [];
    const cached = new Date();
    const expired = MINUTES(cached, -5);

    if (expand) ws = [...ws, ...flatten(ws.map(s => s.split(' ')).filter(s => s.length > 1))]
    ws = _.uniq(ws.filter(w => w && w.length).map(w => w.toLowerCase()).filter(w => !parsers.ignore(w) && !parsers.name(w)));

    const memmap = ws.map(w => SkillDataWrapper.word_map[w]).filter(x => x);
    const has_mem = memmap.filter(w => (w.vector && w.vector.length) || (w.cached > expired)).map(w => w.name);

    const lookup = _.uniq(ws.filter(w => !has_mem.includes(w)));

    while(lookup.length) {
      await Promise.all(lookup.splice(0,10).map(async w=> {
        let mw = await data.plugins.cachePlugin().get(`word_${w}`);
        if (mw) {
          try { 
            const f = uncompress(mw.value);
            if (f && f.name) {
              mp.push(f);
              found.push(f.name);
              SkillDataWrapper.word_map[f.name] = f;
            }
          } catch(e) {
            // ignore and refetch
            logging.warnF(LOG_NAME, 'words', `Error with ${w}`, e);
            await data.plugins.cachePlugin().delete(`word_${w}`);
          }
        }
      }));
    }

    const missing = ws.filter(w => !found.includes(w) && !has_mem.includes(w));
    if (missing.length) {
      const mps = await data.plugins.storagePlugin().words(missing);
      if (mps && mps.length) {
        const save = mps.slice();
        while(save.length) {
          await Promise.all(save.splice(0,50).map(async w => {
            found.push(w.name);
            const mw = {name: w.name, vector: w.vector};
            await data.plugins.cachePlugin().set(`word_${w.name}`, compress(mw), { expires: 30 * 24 * 3600 });
            SkillDataWrapper.word_map[w.name] = {name: w.name, vector: w.vector, cached}
          }));
        }

        const empty = ws.filter(w => !found.includes(w) && !has_mem.includes(w)); 
        empty.forEach(name => SkillDataWrapper.word_map[name] = {name, vector: null, cached});
        return [...memmap.filter(m => m.vector && m.vector.length), ...mp, ...mps.filter(x => x)];
      }
    }

    const empty = ws.filter(w => !found.includes(w) && !has_mem.includes(w)); 
    empty.forEach(name => SkillDataWrapper.word_map[name] = {name, vector: null, cached});
    return [...memmap.filter(m => m.vector && m.vector.length), ...mp];
  }

  static async skillMatrix(aterms: string[], bterms: string[]): Promise<{[key:string]: {[key:string]: number}}> {
    if (!aterms || !aterms.length || !bterms || !bterms.length) return {};
    const matrix: {[key:string]: {[key:string]: number}} = {};
    const mat_set =  await data.plugins.bigQueryPlugin().skillMatrix(aterms, bterms);
    if (mat_set) {
      for(const mat of mat_set) {
        if(!matrix[mat.aname]) matrix[mat.aname] = {};
        matrix[mat.aname][mat.bname] = mat.score;
      }
    }

    return matrix;
  }
}
 