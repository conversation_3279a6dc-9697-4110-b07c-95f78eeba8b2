/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import data from '..';

import ForaUser from '../../session/user';

import { ImportLog } from '../../types/globals';
import { Document } from '../../types/items';
import { Uid } from '../../types/shared';

import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.ImportDataWrapper';

export class ImportDataWrapper extends AbstractDataWrapper {
  static async byFileId(user: ForaUser, file_id: string): Promise<Document> {
    try {
      return await data.plugins.storagePlugin().importByFileId(user, file_id);
    } catch (err) {
      await ImportDataWrapper.handleErrorWithFallback(user, err, 'imports', LOG_NAME, 'byFileId', `Error finding import by file ID ${file_id}`);
      return ImportDataWrapper.byFileId(user, file_id);
    }
  }

  static async delete(user: ForaUser, doc: Document): Promise<void> {
    try {
      return await data.plugins.storagePlugin().importDelete(user, doc);
    } catch (err) {
      await ImportDataWrapper.handleErrorWithFallback(user, err, 'imports', LOG_NAME, 'delete', `Error deleting ${doc.file_id}`);
      return ImportDataWrapper.delete(user, doc);
    }
  }

  static async deleteAll(user: ForaUser, destroy_containers?: boolean): Promise<number> {
    try {
      return await data.plugins.storagePlugin().importsClear(user, destroy_containers);
    } catch (err) {
      await ImportDataWrapper.handleErrorWithFallback(user, err, 'imports', LOG_NAME, 'deleteAll', 'Error deleting all imports');
      return ImportDataWrapper.deleteAll(user);
    }
  }

  static async load(user: ForaUser): Promise<Document[]> {
    try {
      return await data.plugins.storagePlugin().imports(user);
    } catch (err) {
      await ImportDataWrapper.handleErrorWithFallback(user, err, 'imports', LOG_NAME, 'imports', 'Error loading imports');
      return ImportDataWrapper.load(user);
    }
  }

  static async save(user: ForaUser, doc: Document): Promise<Document> {
    try {
      return await data.plugins.storagePlugin().importSave(user, doc);
    } catch (err) {
      await ImportDataWrapper.handleErrorWithFallback(user, err, 'imports', LOG_NAME, 'save', `Error saving ${doc.file_id}`);
      return ImportDataWrapper.save(user, doc);
    }
  }

  static async upload(user: ForaUser, doc: Document): Promise<Document> {
    try {
      return await data.plugins.storagePlugin().importUpload(user, doc);
    } catch(err) {
      await ImportDataWrapper.handleErrorWithFallback(user, err, 'imports', LOG_NAME, 'save', `Error saving ${doc.file_id}`);
      return ImportDataWrapper.upload(user, doc);
    }
  }

  static async importLog(import_logs: ImportLog[]) {
    const now = new Date();
    import_logs.forEach(log => log.created = now);
    return data.plugins.storagePlugin().importLog(import_logs);
  }

  static async importLogs(import_id: Uid, learn_id?: Uid): Promise<ImportLog[]> {
    return data.plugins.storagePlugin().importLogs(import_id, learn_id);
  }

  static async removeImportLogs(import_logs: ImportLog[]) {
    return data.plugins.storagePlugin().importLogsRemove(import_logs);
  }

  static async completeImportLogs(import_logs: ImportLog[]) {
    import_logs.forEach(log => log.complete = true);
    return data.plugins.storagePlugin().importLog(import_logs);
  }
}
