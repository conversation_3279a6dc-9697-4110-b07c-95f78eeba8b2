/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { dehydrate<PERSON>erson, IEntity, Message, Person } from '../../types/items';
import { AuthLevel, AuthProviders, Uid } from '../../types/shared';

import { createRawMessage } from '../../utils/format';
import * as functions from '../../utils/funcs';
import logging from '../../utils/logging';
import peopleUtils from '../../utils/people';

import data from '..';
import config from '../../config';
import ForaUser from '../../session/user';
import { SourceKeys } from '../../sources';
import { IMessagesSourcePlugin } from '../../sources/messages/i_messages_source_plugin';
import { AbstractDataWrapper } from './a_data_wrapper';

// const DEBUG = require('debug')('fora:data:wrappers:message');
const LOG_NAME = 'data.wrappers.MessageDataWrapper';

export class MessageDataWrapper extends AbstractDataWrapper {
  static async archive(user: ForaUser, message: Message) {
    let deletes = [];

    if (!config.isEnvOffline()) [message, deletes] = await MessageDataWrapper.sourcePlugin(user, message.account).archive(message);

    data.plugins.storagePlugin().remove(user.profile, message)
      .catch(err => logging.errorFP(LOG_NAME, 'archive', user.profile, 'Error removing archived message', err));
    MessageDataWrapper.handleAncillary(user, deletes, 'archive', LOG_NAME);
    return message;
  }

  static async draft(user: ForaUser, recipients: Partial<Person>[], from: Person, subject: string, text: string, cc: Person[] = []) {
    let message = new Message({
      id: 'offline',
      threadId: 'offline',
      received: new Date(),
      read: false,
      draft: false,
      recipient: recipients.concat(cc),
      sender: from,
      subject,
    });
    message.id = functions.hash(JSON.stringify(message));
    let deletes = [];

    if (config.isEnvOffline()) {
      message.link = 'offline_msg_link';
      message.raw = createRawMessage(recipients, from, user.email, subject, text, cc).raw;
    } else {
      [message, deletes] = await MessageDataWrapper.sourcePlugin(user, message.account).draft(user, recipients, from, user.email, subject, text, cc);
    }

    data.plugins.storagePlugin().messageSave(user, message)
      .catch(err => logging.errorFP(LOG_NAME, 'draft', user.profile, 'Error saving draft message', err));
    MessageDataWrapper.handleAncillary(user, deletes, 'draft', LOG_NAME);
    return message;
  }

  static drafts(user: ForaUser): Promise<Message[]> {
    return data.plugins.storagePlugin().messageDrafts(user);
  }

  static load(user: ForaUser): Promise<Message[]> {
    return data.plugins.storagePlugin().messages(user);
  }

  static messagesByDate(user: ForaUser, start: Date = null, end: Date = null): Promise<Message[]> {
    return data.plugins.storagePlugin().messagesByDate(user, start, end);
  }

  static async migratePeople(user: ForaUser, self: Partial<Person>, resolve_map: {[key: string]: Uid}, people_ids: {[key: string]: Person }, people_comms: {[key: string]: Uid[]}): Promise<IEntity[]> {
    const update_messages: Message[] = [];

    const messages = await MessageDataWrapper.load(user);

    for (const message of messages) {
      let message_updated = false;
      if (message.sender) {
        const [sender, updated] = peopleUtils.updatePeople(self, [message.sender], resolve_map, people_ids, people_comms);
        if (updated) {
          message.sender = dehydratePerson(sender[0]);
          message_updated = true;
        }
      }

      if (message.recipient) {
        const [recipients, updated] = peopleUtils.updatePeople(self, message.recipient, resolve_map, people_ids, people_comms); 
        if (updated) {
          message.recipient = recipients.map(r => dehydratePerson(r));
          message_updated = true;
        }
      }

      if (message_updated) update_messages.push(message);
    }

    return update_messages;
  }

  static async read(user: ForaUser, message: Message) {
    let deletes = [];

    if (config.isEnvOffline()) message.read = true;
    else [message, deletes] = await MessageDataWrapper.sourcePlugin(user, message.account).markRead(message);

    data.plugins
      .storagePlugin()
      .messageSave(user, message)
      .catch(err => logging.errorFP(LOG_NAME, 'read', user.profile, 'Error saving read message', err));
    MessageDataWrapper.handleAncillary(user, deletes, 'read', LOG_NAME);

    return message;
  }
 
  static async reload(user: ForaUser, message: Message): Promise<Message> {
    if (!config.isEnvOffline() && user.isAuthenticated(AuthLevel.Email)) {
      const rmessage = MessageDataWrapper.sourcePlugin(user, message.account).reload(message);
      if (rmessage) return rmessage;
      data.plugins.storagePlugin().remove(user.profile, message)
        .catch(err => logging.errorFP(LOG_NAME, 'reload', user.profile, 'Error removing reloaded message', err));

      return null;
    }
    else return message;
  }


  static save(user: ForaUser, message: Message): Promise<Message> {
    return data.plugins.storagePlugin().messageSave(user, message);
  }

  static async send(user: ForaUser, message: Message): Promise<Message> {
    let deletes = [];
    if (config.isEnvOffline()) message.draft = false;
    else [message, deletes] = await MessageDataWrapper.sourcePlugin(user, message.account).send(message);

    // draft may be gone
    if (message) {
      data.plugins
        .storagePlugin()
        .remove(user.profile, message)
        .catch(err => logging.errorFP(LOG_NAME, 'send', user.profile, 'Error removing sent message', err));
    }

    MessageDataWrapper.handleAncillary(user, deletes, 'send', LOG_NAME);

    return message;
  }

  static sourcePlugin(user: ForaUser, account: Uid): IMessagesSourcePlugin {
    switch (user.provider) {
      case AuthProviders.Msal:
      case AuthProviders.Microsoft:
        return new (data.plugins.sourcePlugins().get(SourceKeys.MicrosoftMessages))(user, account);
      case AuthProviders.Google:
      default:
        return new (data.plugins.sourcePlugins().get(SourceKeys.GoogleMessages))(user, account);
    }
  }
}
