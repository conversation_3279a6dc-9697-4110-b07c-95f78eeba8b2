/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import data from '..';
import { <PERSON>rro<PERSON><PERSON><PERSON><PERSON> } from '../../errors/error_handler';
import ForaUser from '../../session/user';
import { IEntity } from '../../types/items';
import logging from '../../utils/logging';

export abstract class AbstractDataWrapper {
  protected static handleAncillary(user: Fora<PERSON><PERSON>, deletes: IEntity[], method: string, log_name: string): void {
    // data.plugins.storagePlugin().saveAll(user, updates)
    //  .catch(err => logging.errorFP(log_name, method, user.profile, 'Error saving ancillary updates', err));
    data.plugins.storagePlugin().removeAll(user.profile, deletes)
      .catch(err => logging.errorFP(log_name, method, user.profile, 'Error removing ancillary deletes', err));
  }

  protected static async handleErrorWithFallback(user: <PERSON><PERSON><PERSON><PERSON>, err: Error, setting_key: string, log_name: string, method: string, message: string): Promise<void> {
    if (AbstractDataWrapper.isErrorUnauthorized(err)) {
      const setting = user.settings[setting_key];

      if (setting && !setting.useFallback) {
        // Enable the fallback
        setting.useFallback = true;
        setting.unauthorized = true;
        await data.users.quickSave(user);

        return;
      }
    }

    // We are already on the fallback, normal error handling. All of our fallbacks are Google (as of 201901)
    const handled = await ErrorHandler.handleError(user, user.provider, err, data.users.message, data.users.logoutUser);
    if (!handled) {
      logging.errorFP(log_name, method, user.profile, message, err);
      throw err;
    }
  }

  protected static isErrorUnauthorized(err: Error) {
    return err['statusCode'] === 403 && err['code'] === 'accessDenied'; // Microsoft access denied
  }
}
