/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import data from '..';

import ForaUser from '../../session/user';

import { Search } from '../../types/globals';
import { Ask, GlobalType } from '../../types/items';
import { Uid } from '../../types/shared';

import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.AskDataWrapper';

export class AskDataWrapper extends AbstractDataWrapper {
  static async get(user: ForaUser, id): Promise<Ask> {
    return data.plugins.storagePlugin().askGet(user, id);
  }

  static async load(user: ForaUser): Promise<Ask[]> {
    return data.plugins.storagePlugin().askLoad(user);
  }

  static async create(user: Fora<PERSON><PERSON>, ask: Ask): Promise<Ask> {
    if (!ask.id) ask.id = uuid();
    ask.last_update = new Date();
    await data.users.cacheItemInfo(user, [ask]);
    return data.plugins.storagePlugin().askSave(user, ask);
  }

  static async update(user: ForaUser, ask: Ask) {
    ask.last_update = new Date();
    await data.users.cacheItemInfo(user, [ask]);
    return data.plugins.storagePlugin().askSave(user, ask);
  }

  static async delete(user: ForaUser, ask: Partial<Ask>): Promise<void> {
    try {
      await data.users.removeItemInfoCache(user, [ask]);
      await data.plugins.storagePlugin().searchDelete({id: ask.id, type: GlobalType.Search});
    } catch(e) {
      //ignore error
    }

    return data.plugins.storagePlugin().askDelete(user, ask);
  }

  static async getShared(user: ForaUser, id: Uid): Promise<Search> {
    const search = await data.plugins.storagePlugin().search(id);
    if (search) {
      if (search.public) return search;
      else if (search.groups && user.groups && _.intersection(search.groups, Object.keys(user.groups)).length) return search;
      return null;
    }
  }

  static async share(user: ForaUser, ask: Partial<Ask>, is_public?: boolean, groups?: Uid[]): Promise<Search> {
    const search = new Search({
      id: ask.id,
      profile: user.profile,
      public: is_public,
      groups,
    });

    await data.plugins.storagePlugin().searchSave(search);

    return search;
  }
} 