/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import { Plan } from '../../types/globals';
import { Uid } from '../../types/shared';

import data from '..';

import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.PlanDataWrapper';

export class PlanDataWrapper extends AbstractDataWrapper {
  static async get(group_id: Uid, id: Uid): Promise<Plan> {
    return data.plugins.storagePlugin().planGet(group_id, id);
  }

  static async getAll(group_id: Uid, ids: Uid[]): Promise<Plan[]> {
    return data.plugins.storagePlugin().planGetAll(group_id, ids);
  }

  static async load(group_id: Uid): Promise<Plan[]> {
    return data.plugins.storagePlugin().plansLoad(group_id);
  }

  static async create(group_id: Uid, plan: Plan): Promise<Plan> {
    if (!plan.id) plan.id = uuid();
    plan.last_update = new Date();
    return data.plugins.storagePlugin().planSave(group_id, plan);
  }

  static async update(group_id: Uid, plan: Plan) {
    plan.last_update = new Date();
    plan.assigned = plan.assigned ? _.uniq(plan.assigned) : [];
    plan.unassigned = plan.unassigned ? _.uniq(plan.unassigned) : [];
    return data.plugins.storagePlugin().planSave(group_id, plan);
  }

  static async delete(group_id: Uid, plan: Partial<Plan>): Promise<void> {
    return data.plugins.storagePlugin().planDelete(group_id, plan);
  }

}