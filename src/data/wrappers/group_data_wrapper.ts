/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import data from '..';
import ForaUser from '../../session/user';
import { Group } from '../../types/group';
import { GlobalType } from '../../types/items';
import { Uid } from '../../types/shared';

// const DEBUG = require('debug')('fora:data:wrappers:group');
// const LOG_NAME = 'data.wrappers.GroupDataWrapper';

export class GroupDataWrapper {
  static async byDomain(domain: string): Promise<Group> {
    let group = await data.plugins.cachePlugin().lookupCacheAttVal(null, GlobalType.Group, 'domain', domain);
    if (group) return group;

    group = await data.plugins.storagePlugin().groupByDomain(domain);
    if (group) await data.plugins.cachePlugin().cacheAttVal(null, GlobalType.Group, 'domain', domain, group, 60);

    return group;
  }

  static async byHost(host: string): Promise<Group> {
    let group = await data.plugins.cachePlugin().lookupCacheAttVal(null, GlobalType.Group, 'host', host);
    if (group) return group;

    group = await data.plugins.storagePlugin().groupByHost(host);
    if (group) await data.plugins.cachePlugin().cacheAttVal(null, GlobalType.Group, 'host', host, group, 60);

    return group;
  }

  static async byId(id: Uid): Promise<Group> {
    if (!id) return undefined;
    let group = await data.plugins.cachePlugin().lookupCacheAttVal(null, GlobalType.Group, 'id', id);
    if (group) return new Group(group);

    group = await data.plugins.storagePlugin().groupById(id);
    if (group) await data.plugins.cachePlugin().cacheAttVal(null, GlobalType.Group, 'id', id, group, 60);

    return group;
  }

  static async byIds(ids: Uid[]): Promise<Group[]> {
    let groups: Group[] = await Promise.all(ids.map(async id => 
      data.plugins.cachePlugin().lookupCacheAttVal(null, GlobalType.Group, 'id', id)));
    groups = groups.filter(g => g);
    const have_ids = groups.map(g => g.id);
    const load_ids = ids.filter(id => !have_ids.includes(id));
    if (load_ids.length) {
      const loaded_groups = await data.plugins.storagePlugin().groupsByIds(load_ids);
      if (loaded_groups.length) {
        await Promise.all(loaded_groups.map(async group => data.plugins.cachePlugin().cacheAttVal(null, GlobalType.Group, 'id', group.id, group, 60)));
        groups = [...groups.map(g => new Group(g)), ...loaded_groups];
      }
    }
    return groups;
  }

  static byAccount(account_type: string, account_id): Promise<Group> {
    return data.plugins.storagePlugin().groupByAccountId(account_type, account_id);
  }

  static async delete(group: Group): Promise<void> {
    await data.plugins.cachePlugin().clearCacheAttVal(null, GlobalType.Group, 'id', group.id);
    await data.plugins.cachePlugin().clearCacheAttVal(null, GlobalType.Group, 'host', group.host);
    await data.plugins.cachePlugin().clearCacheAttVal(null, GlobalType.Group, 'domain', group.email_domain);
    return data.plugins.storagePlugin().groupDelete(group);
  }

  static groups(): Promise<Group[]> {
    return data.plugins.storagePlugin().groups();
  }

  static async groupUserIds(user: ForaUser, group_search_ids?: Uid[]): Promise<Uid[]> {
    const additional_user_ids: Uid[] = [];
    if (!group_search_ids && user?.loaded_groups) group_search_ids = Object.keys(user.loaded_groups);

    if (group_search_ids && group_search_ids.length) {
      for (const group_id of group_search_ids) {
        const filter = {name: 'groups', op: '=', val: group_id};
        const keys = await data.plugins.storagePlugin().findKeysByFilter(null, GlobalType.User, [filter]);
        keys.forEach(k => { if(k.name !== user?.profile) additional_user_ids.push(k.name) });
      }
    }

    return additional_user_ids;
  }

  static categoryGroups(): Promise<Group[]> {
    return data.plugins.storagePlugin().groupsWithCategory();
  }

  static save(group: Group): Promise<Group> {
    return data.plugins.storagePlugin().groupSave(group);
  }
}
