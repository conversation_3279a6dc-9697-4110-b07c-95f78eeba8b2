/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { v4 as uuid } from 'uuid';

import data from '..';
import ForaUser from '../../session/user';
import { Analysis } from '../../types/items';
import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.AnalysisDataWrapper';

export class AnalysisDataWrapper extends AbstractDataWrapper {
  static async get(user: ForaUser, id): Promise<Analysis> {
    return data.plugins.storagePlugin().analysisGet(user, id);
  }

  static async load(user: ForaUser): Promise<Analysis[]> {
    return data.plugins.storagePlugin().analysisLoad(user);
  }

  static async create(user: ForaUser, analysis: Analysis): Promise<Analysis> {
    if (!analysis.id) analysis.id = uuid();
    analysis.last_update = new Date();
    await data.users.cacheItemInfo(user, [analysis]);
    return data.plugins.storagePlugin().analysisSave(user, analysis);
  }

  static async update(user: ForaUser, analysis: Analysis) {
    analysis.last_update = new Date();
    await data.users.cacheItemInfo(user, [analysis]);
    return data.plugins.storagePlugin().analysisSave(user, analysis);
  }

  static async delete(user: ForaUser, analysis: Partial<Analysis>): Promise<void> {
    await data.users.removeItemInfoCache(user, [analysis]);
    return data.plugins.storagePlugin().analysisDelete(user, analysis);
  }
} 