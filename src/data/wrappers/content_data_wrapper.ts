/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import data from '..';
import { Content } from '../../types/globals';
import { Uid } from '../../types/shared';

export class ContentDataWrapper {
  static async contentByPath(path: string): Promise<Content> {
    return data.plugins.storagePlugin().contentByPath(path);
  }

  static async contentById(id: Uid): Promise<Content>  {
    return data.plugins.storagePlugin().contentById(id);
  }

  static async contentSet(): Promise<string[]> {
    return data.plugins.storagePlugin().contentSet();
  }
}