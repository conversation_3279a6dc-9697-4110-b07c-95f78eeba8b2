/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import data from '..';
import ForaUser from '../../session/user';

import { SourceKeys } from '../../sources';
import { IEventsSourcePlugin } from '../../sources/events/i_events_source_plugin';

import { Event, IEntity, Person, dehydratePerson, personRef } from '../../types/items';
import { AuthProviders, Uid } from '../../types/shared';

import peopleUtils from '../../utils/people';
import { AbstractDataWrapper } from './a_data_wrapper';

// const DEBUG = require('debug')('fora:data:wrappers:event');
const LOG_NAME = 'data.wrappers.EventDataWrapper';

export class EventDataWrapper extends AbstractDataWrapper {
  static async cancel(user: ForaUser, event: Event) {
    // if (!config.isEnvOffline()) event = await EventDataWrapper.sourcePlugin(user, event.account).cancel(event);
    return data.plugins.storagePlugin().remove(user.profile, event);
  }

  static async create(user: ForaUser, event: Event) {
    let deletes = [];

    // if (!config.isEnvOffline()) [event, deletes] = await EventDataWrapper.sourcePlugin(user, event.account).create(event);
    EventDataWrapper.handleAncillary(user, deletes, 'create', LOG_NAME);
    if (event.people) event.people = event.people.map(personRef);
    return data.plugins.storagePlugin().eventSave(user, event);
  }

  static load(user: ForaUser): Promise<Event[]> {
    return data.plugins.storagePlugin().events(user);
  }

  static eventsByDate(user: ForaUser, start: Date = null,  end: Date = null): Promise<Event[]> {
    return data.plugins.storagePlugin().eventsByDate(user, start, end);
  }

  static async migratePeople(user: ForaUser, self: Partial<Person>, resolve_map: {[key: string]: Uid}, people_ids: {[key: string]: Person }, people_comms: {[key: string]: Uid[]}): Promise<IEntity[]> {
    const update_events: Event[] = [];

    const events = await EventDataWrapper.load(user);

    for (const event of events) {
      let updated = false;
      if (event.people) {
        const [new_people, updated] = peopleUtils.updatePeople(self, event.people, resolve_map, people_ids, people_comms);
        if (updated) {
          event.people = new_people.map(p => dehydratePerson(p));
          if (updated) update_events.push(event);
        }
      }
    }

    return update_events;
  }

  static reload(user: ForaUser, event: Event): Promise<Event> {
    return EventDataWrapper.sourcePlugin(user, event.account).reload(event);
  }

  static save(user: ForaUser, event: Event): Promise<Event> {
    if (event.people) event.people = event.people.map(personRef);
    return data.plugins.storagePlugin().eventSave(user, event);
  }

  static sourcePlugin(user: ForaUser, account: Uid): IEventsSourcePlugin {
    switch (user.provider) {
      case AuthProviders.Msal:
      case AuthProviders.Microsoft:
        return new (data.plugins.sourcePlugins().get(SourceKeys.MicrosoftEvents))(user, account);
      case AuthProviders.Google:
      default:
        return new (data.plugins.sourcePlugins().get(SourceKeys.GoogleEvents))(user, account);
    }
  }
}
