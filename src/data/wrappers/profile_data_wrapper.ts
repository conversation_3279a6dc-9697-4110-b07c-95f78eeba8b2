/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

// import puppeteer from 'puppeteer';
import { setTimeout } from "timers/promises";

import data from '..';
import config from '../../config';

import ForaUser from '../../session/user';

import { Document } from '../../types/items';

import logging from '../../utils/logging';

import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.ProfileDataWrapper';

let browser;

async function cleanup() {
  if (browser) {
    await browser.close();
    browser = null;
  }
  process.exit();
}

interface ScrapeURL {
  loaded: any;
  profile_url: string;
  vanity: string;
  image?: Buffer;
}

let scrape_urls: ScrapeURL[] = [];

let thread_count = 0;

async function browserThread(c) {
  thread_count++;
  while(browser) {
    while(scrape_urls.length === 0) await setTimeout(1000);
    if (scrape_urls.length > thread_count) new Promise<void>(browserThread)
    const url = scrape_urls.pop();

    let page = await browser.newPage();
    try {
      const surl = new URL(url.profile_url);
      
      page.setRequestInterception(true);
      page.on('request', (request) => {
        const rurl = new URL(request.url());
        if (!rurl.host.startsWith(surl.host)) request.abort();
        else request.continue();
      });

      await page.setViewport({width: 350, height: 200});
      if (logging.isDebug()) logging.debugF(LOG_NAME, 'get', `Feching profile card ${url.profile_url}`);
      await page.goto(url.profile_url, {waitUntil: 'networkidle0', timeout: 0});
      await page.evaluateHandle('document.fonts.ready', { timeout: 1000});
      await page.waitForSelector(`#profile_${url.vanity.replace(/[/\\%'-.+&]/g, '_')}`, { visible: true, timeout: 1000});
      await page.waitForSelector('img', { visible: true, timeout: 1000});
      url.image = await page.screenshot({
        type: 'png',
        clip: { x: 0, y: 0, width: 352, height: 202 }
      });
    } catch(err) {
      logging.warnF(LOG_NAME, 'browser', `Error rendering profile card`, err);
    }
    url.loaded();
    await page.close().catch(e => logging.warnF(LOG_NAME, 'browser', `Catching closer error`, e));
    page = null;

    if (thread_count > 1) {
      thread_count--;
      break;
    }
  } 
  c();
}

config.onLoad('profiles', async (silent: boolean) => {
  /*if (!browser && !config.isBackgroundProcess()) {
    try {
      if (config.isRunningOnGoogle()) {
        process.env.PUPPETEER_CACHE_DIR = '/workspace/.cache/puppeteer';
        puppeteer['configuration'].cacheDirectory = '/workspace/.cache/puppeteer';
      }
      if (!silent) logging.infoF(LOG_NAME, 'onLoad', `Puppeteer config: ${JSON.stringify(puppeteer['configuration'])}`);
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--single-process'
        ]
      });
    } catch(e) {
      logging.errorF(LOG_NAME, 'onLoad', 'Error loading browser', e);
      logging.warnF(LOG_NAME, 'onLoad', `Evn: ${process.env}`);
      throw e;
    }
  */
 
  process.on('exit', cleanup);
  process.on('SIGINT', cleanup);
  process.on('SIGUSR1', cleanup);
  process.on('SIGUSR2', cleanup);
  process.on('SIGQUIT', cleanup);
  process.on('SIGTERM', cleanup);
});

export class ProfileDataWrapper extends AbstractDataWrapper {
  static async get(user: ForaUser, vanity: string, force = false): Promise<Buffer> {
    if (!force) {
      try {
        const doc = await data.plugins.storagePlugin().profileCard(user);
        if (doc && doc.body) return Buffer.from(doc.body as any);
      } catch(e) {
        logging.warnFP(LOG_NAME, 'get', user.profile, `Error getting profile card from storage. Creating a new one`, e);
      }
    }

    /*try { 
      const profile_url = config.isEnvOffline() ?  `http://localhost:${config.get('PORT', 3175)}/profile/${vanity}?embed=card&width=350&height=200`
        : funcs.mapURL(`/profile/${vanity}?embed=card&width=350&height=200`);

      let loaded;
      const loader = new Promise(c => loaded = c);

      const url: ScrapeURL = {
        loaded,
        profile_url,
        vanity,
      };

      scrape_urls.push(url);

      if (thread_count < 1) new Promise<void>(browserThread);

      await loader;
      
      if (url.image) {
        await ProfileDataWrapper.save(user, url.image);  
        return url.image;
      }
    } catch (err) {
      logging.warnFP(LOG_NAME, 'get', user.profile, `Error finding profile card`, err);
    }*/

    return null;
  }

  static async deleteAll(user: ForaUser, destroy_containers?: boolean): Promise<number> {
    try {
      return await data.plugins.storagePlugin().profileCardClear(user, destroy_containers);
    } catch (err) {
      await ProfileDataWrapper.handleErrorWithFallback(user, err, 'profiles', LOG_NAME, 'deleteAll', 'Error deleting all profile cards');
      return ProfileDataWrapper.deleteAll(user);
    }
  }

  static async save(user: ForaUser, card: Buffer): Promise<Document> {
    try {
      const doc = new Document({
        body: card,
        mime: 'image/png',
        created: new Date(),
        props: {
          originalname: 'profile_card.png',
          size: card.length.toString(),
        },
      });
      return await data.plugins.storagePlugin().profileCardSave(user, doc);
    } catch (err) {
      await ProfileDataWrapper.handleErrorWithFallback(user, err, 'profiles', LOG_NAME, 'save', `Error saving profile card`);
      return ProfileDataWrapper.save(user, card);
    }
  }
}
