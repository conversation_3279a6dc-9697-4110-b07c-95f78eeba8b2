/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import _ from 'lodash';
import { v4 as uuid } from 'uuid';

import data from '..';
import config from '../../config';
import lang from '../../lang';

import ForaUser from '../../session/user';
import { FORA_PROFILE } from '../../types/user';

import { Jab, Referral } from '../../types/globals';
import { Group } from '../../types/group';
import { Candidate, GlobalType, IEntity, Person, Project, projectPerson } from '../../types/items';
import { ANONYMOUS_ID, AuthLevel, CandidateStateOrder, ProjectCandidateState, Uid } from '../../types/shared';

import * as functions from '../../utils/funcs';
import logging from '../../utils/logging';
import peopleUtils from '../../utils/people';

// const DEBUG = require('debug')('fora:data:wrappers:project');
const LOG_NAME = 'data.fora.ProjectDataWrapper';

export class ProjectDataWrapper {
  static checkProject(profile: Uid, me: Partial<Person>, project: Project, save: boolean, resolve_map: {[key: string]: Uid} = null) {
    if (config.isEnvOffline() && project.client.self && save) {
      if (project.client.id !== `people/t${project.client.askfora_id}` &&
          project.client.id !== `people/s${project.client.askfora_id}` ) {
        throw new Error(`Bad client id ${project.client.id}`);
      }
      for (const candidate of project.candidates) {
        if (candidate.askfora_id && candidate.id === `people/t${candidate.askfora_id}`) {
          throw new Error(`Bad contractor id ${candidate.id}`);
        }
      }
    }

    let missing = null;
    if (save && !project.id) missing = 'id';
    if (!project.title || !project.title.length) missing = 'title';
    if (!project.skills || !project.skills.length) missing = 'skills';
    // if (!project.notes || !project.notes.length) missing = 'notes';
    if (!project.rate || !project.rate.length) missing = 'rate';
    if (!project.duration) missing = 'duration';
    if (!project.client) missing = 'client';
    else if (save && !project.client.id) {
      if (project.proposal) {
       if (project.escrow && project.escrow.id !== lang.project.SKIP_ESCROW.id) missing = 'client id';
      } else missing = 'client id';
    }
    if (!new Date(project.start).getTime()) missing = 'start';
    if (!new Date(project.end).getTime()) missing = 'end';

    if (missing) throw new Error(`Project ${project.id} for ${profile} validation error: missing ${missing}`);

    let self_person;
    let self_from;
    let contractor: Partial<Person> = null;
    const askfora_ids: Uid[] = [];
    const candidate_ids: Uid[] = [];
    let multi_select = false;
    if (project.candidates) {
      for (const candidate of project.candidates) {
        if (candidate_ids.includes(candidate.id)) throw new Error(`Project ${project.id} for ${profile} validation error: duplicate candidate id ${candidate.id}`);
        if (askfora_ids.includes(candidate.askfora_id)) throw new Error(`Project ${project.id} for ${profile} validation error: duplicate candidate askfora_id ${candidate.askfora_id}`);
        if (candidate.id) candidate_ids.push(candidate.id);
        if (candidate.askfora_id) askfora_ids.push(candidate.askfora_id);
        if (functions.checkState(candidate, ProjectCandidateState.SELECTED) >= 0) {
          if (contractor) {
            if (save) {
              throw new Error(`Project ${project.id} for ${profile} validation error: two selected candidates ${contractor.id} and ${candidate.id}`);
            } else {
              logging.warnFP(LOG_NAME, 'checkProject', profile, `Project ${project.id} for ${profile} validation error: two selected candidates ${contractor.id} and ${candidate.id}`);
              multi_select = true;
            }
          }
          else contractor = candidate;
          if (save && contractor.askfora_id === profile && !contractor.self) throw new Error(`Project ${project.id} Contractor candidate ${profile} isn't self`);
        }
        if (candidate.comms && candidate.comms.find(c => c === null)) throw new Error(`Project ${project.id} for ${profile} validation error: candidate ${candidate.id} with null comms`);
        if (candidate.self) {
          if (self_person) throw new Error(`Project ${project.id} for ${profile} validation error: two selfs candidate ${self_person.id} and candidate ${candidate.id}`);
          self_person = candidate;
          self_from = 'candidate';
        }
      }
    }

    if (project.contractor) {
      if (project.contractor.self) {
        if (self_person && self_person.askfora_id !== project.contractor.askfora_id) {
          throw new Error(`Project ${project.id} for ${profile} validation error: two selfs ${self_from} ${self_person.id} and contractor ${project.contractor.id}`);
        }

        if (save) {
          if (project.contractor.askfora_id !== profile) {
            throw new Error(`Project ${project.id} for ${profile} validation error: self contractor askfora_id ${project.contractor.askfora_id} doesn't match self askfora_id ${profile}`);
          }
          if (project.contractor.id !== me.id) {
            if (resolve_map && resolve_map[project.contractor.id] === me.id) logging.warnFP(LOG_NAME, 'checkProject', profile, `Expecting contractor id ${project.contractor.id} to resovle to self ${me.id}`);
            else throw new Error(`Project ${project.id} for ${profile} validation error: self contractor id ${project.contractor.id} doesn't match self id ${me.id} and won't resolve.`);
          }
        }
        self_person = contractor;
        self_from = 'contractor';
      } 
      else if (save && project.contractor.askfora_id === profile) throw new Error(`Contractor ${profile} isn't self`);

      if (!contractor) throw new Error(`Project ${project.id} for ${profile} validation error: contractor candidate ${project.contractor.id} is not selected`);
      if (!multi_select) {
        if (contractor.id !== project.contractor.id) throw new Error(`Project ${project.id} for ${profile} validation error: contractor id ${project.contractor.id} and selected candidate id ${contractor.id} do not match`);
        if (contractor.askfora_id !== project.contractor.askfora_id) throw new Error(`Project ${project.id} for ${profile} validation error: contractor ${project.contractor.askfora_id} and selected candidate askfora_id ${contractor.askfora_id} do not match`);
      }
      if (functions.checkState(project.contractor, ProjectCandidateState.SELECTED) < 0) throw new Error(`Project ${project.id} for ${profile} validation error: contractor ${project.contractor.id} is not selected`);
      if (project.contractor.askfora_id === project.client.askfora_id) throw new Error(`Project ${project.id} for ${profile} validation error: contractor has same askfora_id as client: ${project.contractor.askfora_id}`);
      if ((project.contractor.askfora_id === profile || project.client.askfora_id === profile) && project.contractor.id === project.client.id) throw new Error(`Project ${project.id} for ${profile} validation error: contractor has same id as client: ${project.contractor.id}`);
      if (project.contractor.self && project.client.self) throw new Error(`Project ${project.id} for ${profile} validation error: both client and contractor are self`);
      if (project.contractor.network || contractor.network) throw new Error(`Project ${project.id} for ${profile} contractor isn't a first connection`);
    } else if(save && project.client.self && project.contract) {
      throw new Error(`Project ${project.id} for ${profile} validation error: contract ${project.contract} with no contractor`);
    }

    if (project.client.self) {
      if (self_person) {
        throw new Error(`Project ${project.id} for ${profile} validation error: two selfs ${self_from} ${self_person.id} and client ${project.client.id}`);
      }
      if (save) {
        if (project.client.id !== ANONYMOUS_ID && profile !== FORA_PROFILE) {
          if (project.client.askfora_id !== profile) {
            throw new Error(`Project ${project.id} for ${profile} validation error: client askfora_id ${project.client.askfora_id} doesn't match self askfora_id ${profile}`);
          }
          if(project.client.id !== me.id) {
            if (resolve_map && resolve_map[project.client.id] === me.id) logging.warnFP(LOG_NAME, 'checkProject', profile, `Expecting contractor id ${project.client.id} to resovle to self ${me.id}`);
            else throw new Error(`Project ${project.id} for ${profile} validation error: client id ${project.client.id} doesn't match self id ${me.id} and won't resolve`);
          }
        }
      }
    }
  }

  static async create(user: ForaUser, project: Project, shared = true): Promise<Project> {
    if (!user || !user.profile) {
      logging.errorFP(LOG_NAME, 'create', user.profile, `Error creating project ${project.id} for unknown user`, null);
      return;
    }

    if (!project.id) project.id = functions.hash(JSON.stringify(project) + new Date());

    // don't save projects to datastore in demo mode
    if (user.isAuthenticated(AuthLevel.Demo) || user.isGuestAccount()) return project;

    if (shared) {
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'create', user.profile, `Saving shared project ${project.id}`);
      const shared_project = new Project(project);
      await ProjectDataWrapper.saveShared(shared_project);
    }

    ProjectDataWrapper.prepProject(project, false);

    if (config.isEnvOffline() && project.client.self) {
      if (project.client.id !== `people/t${project.client.askfora_id}` &&
          project.client.id !== `people/s${project.client.askfora_id}` ) {
        throw new Error(`Bad client id ${project.client.id}`);
      }
      for (const candidate of project.candidates) {
        if (candidate.askfora_id && candidate.id === `people/t${candidate.askfora_id}`) {
          throw new Error(`Bad contractor id ${candidate.id}`);
        }
      }
    }

    await data.users.cacheItemInfo(user, [project]);
    await data.plugins.storagePlugin().projectSave(user, project);

    return project;
  }

  static async delete(user: ForaUser, project: Project): Promise<void> {
    await data.users.removeItemInfoCache(user, [project]);
    return data.plugins.storagePlugin().projectDelete(user, project);
  }

  static async byContract(user: ForaUser, contract_id: Uid[]): Promise<Project[]> {
    return data.plugins.storagePlugin().projectByContract(user, contract_id);
  }

  static async migratePeople(user: ForaUser, self: Partial<Person>, resolve_map: {[key: string]: Uid}, people_ids: {[key: string]: Person}, people_comms: {[key: string]: Uid[]}): Promise<IEntity[]> {
    const update_projects: Project[] = [];
    const new_people: Person[] = [];

    logging.infoFP(LOG_NAME, 'migratePeople', user.profile, `Migrating projects for ${self.id} self resolves to: ${resolve_map[self.id]}`);

    const projects = await ProjectDataWrapper.load(user);

    if (people_ids && !people_ids[self.id]) people_ids[self.id] = new Person(self);

    if (people_comms) {
      if (!people_comms[user.email]) {
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Adding mapping from ${user.email} to ${self.id}`);
        people_comms[user.email] = [self.id]
      }
      else if (!people_comms[user.email].includes(self.id)) {
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Updating mapping from ${user.email} to ${self.id}`);
        people_comms[user.email].splice(0,0, self.id);
      }
    }

    for (const project of projects) {
      // make sure self is mapped
      let project_updated = false;

      if(project.client && project.client.askfora_id == user.profile && project.client.id !== self.id) {
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Mapping client from ${project.client.id} to ${self.id}`);
        resolve_map[project.client.id] = self.id;
      }

      if (project.contractor && project.contractor.askfora_id === user.profile && project.contractor.id !== self.id) {
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Mapping contractor from ${project.client.id} to ${self.id}`);
        resolve_map[project.contractor.id] = self.id;
      } 
 
      try { 
        ProjectDataWrapper.checkProject(user.profile, self, project, true, resolve_map);
      } catch(e) {
        logging.errorFP(LOG_NAME, 'migratePeople', user.profile, `Cannot update project ${project.id}`, e);
        continue;
      }

      if (project.client) {
        if(project.client.askfora_id !== user.profile && project.client.id === self.id) {
          //  try to match on comms
          project.client.id = null;
          const [client, updated] = peopleUtils.updatePeople(self, [project.client], null, people_ids, people_comms);
          if (updated && client.length) {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Mapped client from ${project.client.id} to ${client[0].id} by comms`);
            project.client = projectPerson(project, client[0], project.client);
          } else {
            const client = new Person(project.client);
            client.tempId();
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Created client from ${project.client.id} to ${client.id} by comms`);
            resolve_map[project.client.id] = client.id;
            project.client = projectPerson(project, client, project.client);
            new_people.push(client);
          }
          project_updated = true;
        } else {
          const [client, updated] = peopleUtils.updatePeople(self, [project.client], resolve_map, people_ids, people_comms);
          if (updated) {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Updating contractor from ${project.client.id} to ${client[0].id}`);
            client[0].askfora_id = project.client.askfora_id;
            client[0].self = project.client.self;
            project.client = projectPerson(project, client[0], project.client);
            // project.client = peopleUtils.dehydratePerson(client[0]);
            project_updated = true;
          } else if (project.client.askfora_id === user.profile) {
            project.client = projectPerson(project, self, project.client);
            project_updated = true;
          }
        }
      }

      if (project.contractor) {       
        if(project.contractor.askfora_id !== user.profile && project.contractor.id === self.id) {
          //  try to match on comms
          project.contractor.id = null;
          const [contractor, updated] = peopleUtils.updatePeople(self, [project.contractor], null, people_ids, people_comms);
          if (updated && contractor.length) {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Mapped contractor from ${project.contractor.id} to ${contractor[0].id} by comms`);
            project.contractor = projectPerson(project, contractor[0], project.contractor);
          } else {
            const contractor = new Person(project.contractor);
            contractor.tempId();
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Created contractor from ${project.contractor.id} to ${contractor.id} by comms`);
            resolve_map[project.contractor.id] = contractor.id;
            project.contractor = projectPerson(project, contractor, project.client);
            new_people.push(contractor);
          }
          project_updated = true;
        } else {
          const [contractor, updated] = peopleUtils.updatePeople(self, [project.contractor], resolve_map, people_ids, people_comms);
          if (updated) {
            if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'migratePeople', user.profile,  `Updating contractor from ${project.contractor.id} to ${contractor[0].id}`);
            project.contractor = projectPerson(project, contractor[0], project.contractor);
            project_updated = true;
          } else if (project.contractor.askfora_id === user.profile) {
            project.client = projectPerson(project, self, project.contractor);
            project_updated = true;
          }
        }
      }

      if (project.candidates) {
        const [candidates, updated] = peopleUtils.updatePeople(self, project.candidates, resolve_map, people_ids, people_comms, (n,p) => {
          if (n.id === project.client.id) return projectPerson(project, p);
          return projectPerson(project, n, p)
        });

        if (updated) {
          project.candidates = candidates; //.map(c => projectPerson(project, c));
          project_updated = true;
        }
      }

      if (project_updated) {
        project.last_update = new Date();
        try { 
          ProjectDataWrapper.checkProject(user.profile, self, project, true);
          update_projects.push(project);
        } catch(e) {
          logging.errorFP(LOG_NAME, 'migratePeople', user.profile, `Error updating project ${project.id}`, e);
        }
      }
    }

    return (update_projects as IEntity[]).concat(new_people);
  }

  static openProjects(user: ForaUser): Promise<Project[]> {
    return data.plugins.storagePlugin().projectsOpen(user);
  }

  static prepProject(project: Project, shared: boolean) {
    // clean up people
    if (project.client) project.client = projectPerson(project, project.client);

    if (project.contractor) project.contractor = projectPerson(project, project.contractor);

    if (project.candidates) {
      const prep_candidates: Partial<Person>[] = [];
      for (const candidate of project.candidates) {
        const prep_candidate = projectPerson(project, candidate);
        prep_candidates.push(prep_candidate);
      }

      project.candidates = prep_candidates;
    }

    if (project.me_candidate) delete project.me_candidate;

    if (shared) functions.slimEntity(project);
  }

  static projectById(id: Uid): Promise<Project> {
    return data.plugins.storagePlugin().projectById(id);
  }

  static load(user: ForaUser, is_public?: boolean, groups?: Uid[]): Promise<Project[]> {
    return data.plugins.storagePlugin().projects(user, is_public, groups);
  }

  static get(user: ForaUser, id: Uid): Promise<Project> {
    return data.plugins.storagePlugin().project(user, id);
  }

  static saveReferral(user: ForaUser, project: Uid, candidate: Partial<Candidate>): Promise<Referral> {
    const referral = new Referral({
      id: uuid(),
      project,
      referrer: user.profile,
      candidate,
      when: new Date(),
    });

    return data.plugins.storagePlugin().referralAdd(referral);
  }

  static async findReferral(user?: ForaUser, project?: Uid): Promise<Referral[]> {
    const referrals = await data.plugins.storagePlugin().referralFind(project, user.profile);

    if (project) {
      const matches = referrals.filter(r => r.referrer === user.profile || (r.candidate && r.candidate.askfora_id === user.profile));
      const to_check = referrals.filter(r => r.referrer !== user.profile && (!r.candidate || r.candidate.askfora_id !== user.profile));
      for (const referral of to_check) {
        const project = referral.project ? await data.plugins.storagePlugin().projectById(referral.project) : null;
        if (project && project.client && project.client.askfora_id === user.profile) matches.push(referral);
      }
      return matches;
    } 
    
    return referrals;
  }

  static async getReferral(user: ForaUser, id: Uid): Promise<Referral> {
    const referral = await data.plugins.storagePlugin().referralGet(id);
    if(referral.referrer === user.profile || (referral.candidate && referral.candidate.askfora_id === user.profile)) return referral;

    const project = referral.project ? await data.plugins.storagePlugin().projectById(referral.project) : null;
    if (project && project.client && project.client.askfora_id === user.profile) return referral;

    return null;
  }

  static async save(user: ForaUser, project: Project): Promise<Project> {

    if (config.isEnvOffline() && project.client.self) {
      if (project.client.id !== `people/t${project.client.askfora_id}` &&
          project.client.id !== `people/s${project.client.askfora_id}` ) {
        throw new Error(`Bad client id ${project.client.id}`);
      }
      for (const candidate of project.candidates) {
        if (candidate.askfora_id && candidate.id === `people/t${candidate.askfora_id}`) {
          throw new Error(`Bad contractor id ${candidate.id}`);
        }
      }
    }

    ProjectDataWrapper.prepProject(project, user === null);
    await data.users.cacheItemInfo(user, [project]);
    return data.plugins.storagePlugin().projectSave(user, project);
  }

  static async saveShared(project: Project): Promise<void> {
    const shared_project = new Project(project);
    ProjectDataWrapper.prepProject(shared_project, true);

    // check that candidate states are up to date
    // highest index state wins unless the candidate is selected but the shared is note
    if (shared_project.candidates) {
      for (const candidate of shared_project.candidates) {
        if (candidate.askfora_id) {
          const candidate_project = await this.get(new ForaUser(candidate.askfora_id), shared_project.id);
          if (candidate_project && candidate_project.candidates) {
            for (const candidate_candidate of candidate_project.candidates) {
              if (candidate_candidate.askfora_id === candidate.askfora_id) {
                const shared_state = candidate.state;
                const candidate_state = candidate_candidate.state;
                if (CandidateStateOrder.indexOf(candidate_state) > CandidateStateOrder.indexOf(shared_state)
                  && candidate_state !== ProjectCandidateState.SELECTED) {
                  candidate.state = candidate_candidate.state;
                }
                break;
              }
            }
          }
        }
      }
    }

    const key = data.plugins.storagePlugin().key(null, { id: shared_project.id, type: GlobalType.Project });
    const now = new Date();
    const comp = functions.compress(shared_project);
    await data.plugins.storagePlugin().save(null, GlobalType.Project, { key, excludeFromIndexes: ['project'], data: { id: shared_project.id, created: now , project: comp, public: project.public, groups: project.groups } });
    logging.infoF(LOG_NAME, 'saveShared', `Saved shared project ${shared_project.id}`);
  }

  static async getJab(project: Partial<Project>, group: Group): Promise<Jab> {
    return data.plugins.storagePlugin().jabByProject(project.id, group ? group.id : undefined);
  }

  static async jabProjects(groups: Group[]): Promise<{public: boolean, project: Project}[]> {
    // TODO check for restrictive groups that don't show public jab
    const public_jabs = await data.plugins.storagePlugin().jabs();
    const group_jabs = groups && groups.length ? await data.plugins.storagePlugin().jabs(groups.map(g => g.id)) : [];
    const all_jabs = _.uniqBy((public_jabs ? public_jabs : []).concat(group_jabs ? group_jabs : []), 'project');
    return Promise.all(all_jabs.map(async (j) => { return { public: j.public, group: j.group, project: await ProjectDataWrapper.projectById(j.project)}}));
  }

  static async deleteJab(project: Partial<Project>, group: Group) {
    const jab = await data.plugins.storagePlugin().jabByProject(project.id, group.id);
    if (jab && ((!group && !jab.group) || (group && jab.group === group.id))) await data.plugins.storagePlugin().jabDelete(jab.id);
  }

  static async saveJab(jab: Jab): Promise<void> {
    await data.plugins.storagePlugin().jabSave(jab);
  }
}
