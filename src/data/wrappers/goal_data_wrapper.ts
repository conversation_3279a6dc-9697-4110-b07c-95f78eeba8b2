/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import gini from 'gini';
import { v4 as uuid } from 'uuid';

import data from '..';

import ForaUser from '../../session/user';

import { filterRelated } from '../../skills';
import { skillVector } from '../../skills/model';


import { ExtendedCategory } from '../../types/globals';
import { Goal } from '../../types/items';
import { Uid } from '../../types/shared';

import logging from '../../utils/logging';

import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.GoalDataWrapper';

export class GoalDataWrapper extends AbstractDataWrapper {
  static async get(user: ForaUser, id: Uid): Promise<Goal> {
    return data.plugins.storagePlugin().goalGet(user, id);
  }

  static async load(user: ForaUser): Promise<Goal[]> {
    return data.plugins.storagePlugin().goalsLoad(user);
  }

  static async update(user: ForaUser, goal: Goal): Promise<Goal> {
    // doesn't mark update or create ids, etc.
    if (goal.id && goal.created && !goal.deleted) {
      await data.users.cacheItemInfo(user, [goal]);
      return data.plugins.storagePlugin().goalSave(user, goal);
    }
  }

  static async save(user: ForaUser, goal: Goal): Promise<Goal> {
    if (!goal.id) goal.id = uuid();
    const now = new Date();
    goal.last_update = now;
    if (!goal.created) goal.created = now;
    if (goal.deleted) await data.users.removeItemInfoCache(user, [goal]);
    else await data.users.cacheItemInfo(user, [goal]);
    return data.plugins.storagePlugin().goalSave(user, goal);
  }

  static async saveGoals(user: ForaUser, goals: Goal[]): Promise<Goal[]> {
    const now = new Date();
    goals.forEach(goal => { 
      if (!goal.id) goal.id = uuid(); 
      goal.last_update = now;
     if (!goal.created) goal.created = now;
    });
    const deleted = goals.filter(g => g.deleted);
    const saved = goals.filter(g => !g.deleted);
    await data.users.removeItemInfoCache(user, deleted);
    await data.users.cacheItemInfo(user, saved);
    return data.plugins.storagePlugin().goalsSave(user, goals);
  }

  static async delete(user: ForaUser, goal: Goal): Promise<void> {
    await data.users.removeItemInfoCache(user, [goal]);
    return data.plugins.storagePlugin().goalDelete(user, goal);
  }

  static async deleteAll(user: ForaUser, goals: Goal[]): Promise<void> {
    await data.users.removeItemInfoCache(user, goals);
    data.plugins.storagePlugin().goalDeleteAll(user, goals);
  }

  static goalFromCategory(category: ExtendedCategory): Goal {
    return new Goal({
      id: uuid(),
      category
    });
  }

  static async recommend(goals: Goal[], skip_course_ids: Uid[] = [], sources?: string[]) {
    logging.infoF(LOG_NAME, 'recommend', `Recommending goals for ${goals ? goals.length : 0} categories`);

    if (goals && goals.length) {
      await Promise.all(goals.map(async goal => {
        if (!goal.category.vector) goal.category.vector = await skillVector(goal.category.skills);
      }));

      const category_courses = await data.courses.findByCategory(goals.map(g => g.category), skip_course_ids, sources);
      logging.infoF(LOG_NAME, 'recommend', `Found courses for ${category_courses ? category_courses.length : 0} categories`);
      if(category_courses) {
        const course_cats: {[key:string]: { id: Uid, score: number }[]} = {};
        for(const cc of category_courses) {
          for (const cc_course of cc.courses) {
            if (course_cats[cc_course.course.id]) {
              course_cats[cc_course.course.id].push({id: cc.id, score: cc_course.score});
            } else {
              course_cats[cc_course.course.id] = [{id: cc.id, score: cc_course.score}]
            }
          }
        }

        for(const cid in course_cats) {
          course_cats[cid] = course_cats[cid].sort((a,b) => b.score - a.score);
        }

        for(const goal of goals.sort((a,b) => b.category.score - a.category.score)) {
          const cc = category_courses.find(c => c.id === goal.category.id);
          if (cc) {
            const course_set = cc.courses.filter(c => {
                const link = new URL(c.course.link);
                const domain = link.hostname.split('.').slice(-2).join('.');

                return !sources || sources.includes(domain);
              })
              .sort((a,b) => b.score - a.score)
              .map(gc => {
                return {
                  id: gc.course.id,
                  link: gc.course.link,
                  skills: gc.course.skills,
                  title: gc.course.title,
                  score: gc.score,
                  assign_skills: [],
                  assign_weight: 50,
                }
              }).filter(course => course.id in course_cats);

            // take top 5, ideally from different providers
            let targets = [];

            const skills:{[key:string]: number} = {}; 
            const domains: {[key:string]: number} = {};

            goal.category.skills.forEach(s => {
              skills[s.toLowerCase()] = 0;
            });

            course_set.forEach(c => {
              const link = new URL(c.link);
              const domain = link.hostname.split('.').slice(-2).join('.');
              if (!sources || sources.includes(domain)) domains[domain] = 0;
            });

            const course_related: {[key:string]: {related: string[], course_related: string[]}} = {};

            await Promise.all(course_set.map(async cs => {
              course_related[cs.id] = {related: [], course_related: []};
              course_related[cs.id].related = await data.courses.assignSkills(cs, goal.category.skills, true);
              course_related[cs.id].course_related = await data.courses.assignSkills(cs, goal.category.skills);
            }));

            for(const c of course_set) {
              const link = new URL(c.link);
              const domain = link.hostname.split('.').slice(-2).join('.');

              if (sources && !sources.includes(domain)) continue;

              const related = course_related[c.id].related;
              // await data.courses.assignSkills(c, goal.category.skills, true);
              // check if related and domain are already overrepresented

              const rel_skills:{[key:string]: number} = {}; 
              Object.assign(rel_skills, skills);

              for(const rel of related.map(s => s.toLowerCase())) {
                if(rel in rel_skills) rel_skills[rel]++;
                else rel_skills[rel] = 1;
              }

              const sg = gini.unordered(Object.values(skills));
              const rg = gini.unordered(Object.values(rel_skills));

              if (sg !== 0 && sg <= rg) continue;

              Object.assign(skills, rel_skills);

              const new_domains: {[key:string]: number} = {};
              Object.assign(new_domains, domains);
 
              if (domain in new_domains) new_domains[domain]++;
              else new_domains[domain] = 1;

              const dg = gini.unordered(Object.values(domains));
              const ng = gini.unordered(Object.values(new_domains));

              if (dg !== 0 && dg <= ng) continue;

              targets.push(c);

              // const course_related = await data.courses.assignSkills(c, goal.category.skills);
              c.assign_skills = [...related, ...course_related[c.id].course_related];
              c.assign_weight = data.courses.assignWeight(c);

              if (targets.length === 5) break;
            }

            if (targets.length < 5 && course_set.length >= 5) {
              const ids = targets.map(c => c.id);
              targets = targets.concat(course_set.filter(c => !ids.includes(c.id)));
            }

            goal.courses = targets.slice(0,5);

            goal.courses.forEach(async c => {
              if (course_cats[c.id]) delete course_cats[c.id];
              c.skills = await filterRelated(goal.category.skills, c.skills);
            });
          }
        }
      }
    } 
  }
}