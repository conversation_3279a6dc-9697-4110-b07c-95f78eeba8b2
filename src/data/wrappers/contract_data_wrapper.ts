/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import data from '..';
import config from '../../config';
import ForaUser from '../../session/user';
import { Contract, GlobalType } from '../../types/items';
import { Uid } from '../../types/shared';
import * as functions from '../../utils/funcs';
import logging from '../../utils/logging';
import { getContract, getDocument } from '../../utils/sign';

// const DEBUG = require('debug')('fora:data:wrappers:contract');
const LOG_NAME = 'data.wrappers.ContractDataWrapper';

export class ContractDataWrapper {
  static async byId(id: Uid): Promise<Contract> {
    const contract = await data.plugins.storagePlugin().contractById(id);
    if (!config.isEnvOffline() && contract && contract.hash && (!contract.client_signed || !contract.contractor_signed)) {
      const ref_doc = await getContract(contract.hash);
      if (ref_doc) {
        for (const signer of ref_doc.getSigners()) {
          const email = signer.getEmail();
          if (email === contract.client_email) {
            contract.client_signed = signer.getSigned();
            contract.client_declined = signer.getDeclined();
          }
          else if (email === contract.contractor_email) {
            contract.contractor_signed = signer.getSigned();
            contract.contractor_declined = signer.getDeclined();
          }
        }
      }
    }
    return contract;
  }

  static async load(user: ForaUser): Promise<Contract[]> {
    return data.plugins.storagePlugin().contracts(user);
  }

  static async create(user: ForaUser, contract: Partial<Contract>, shared = true): Promise<Contract> {
    const save_contract = new Contract(contract);
    if (!save_contract.id) save_contract.id = functions.hash(JSON.stringify(contract) + new Date() + Math.random());

    const key = data.plugins.storagePlugin().key(null, { id: save_contract.id, type: GlobalType.Contract });
    const now = new Date();

    if (shared) {
      const shared_contract = new Contract(save_contract);
      functions.slimEntity(shared_contract);
      const comp = functions.compress(shared_contract);

      await data.plugins.storagePlugin().save(null, GlobalType.Contract, { key, excludeFromIndexes: ['contract'], data: { created: now, contract: comp }});
      logging.infoF(LOG_NAME, 'create', `Saved shared contract ${save_contract.id} for ${user.profile}`);
    }
    await data.plugins.storagePlugin().contractSave(user, save_contract);

    logging.infoFP(LOG_NAME, 'create', user.profile, `Saved contract ${save_contract.id}`);

    return save_contract;
  }

  static async delete(contract: Partial<Contract>): Promise<void> {
    await data.plugins.storagePlugin().contractDelete(contract);
  }

  static async fetch(user: ForaUser, contract: Contract, refetch = false): Promise<Buffer> {
    if (!contract.hash) return null;
    let doc = null;
    // data.plugins.storagePlugin
    if (!doc || refetch) doc = await getDocument(contract.hash);
    return doc;
  }
}
