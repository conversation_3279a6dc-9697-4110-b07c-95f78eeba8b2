/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import util from 'util';
import data from '..';
import ForaUser from '../../session/user';
import { Note } from '../../types/items';
import { AbstractDataWrapper } from './a_data_wrapper';

const LOG_NAME = 'data.wrappers.NoteDataWrapper';

export class NoteDataWrapper extends AbstractDataWrapper {
  static async create(user: ForaUser, note: Note): Promise<Note> {
    try {
      return await data.plugins.storagePlugin().noteCreate(user, note);
    } catch (err) {
      await NoteDataWrapper.handleErrorWithFallback(user, err, 'notes', LOG_NAME, 'create', `Error creating ${util.format(note)}`);
      return NoteDataWrapper.create(user, note);
    }
  }

  static async delete(user: ForaUser, note: Note): Promise<void> {
    try {
      return await data.plugins.storagePlugin().noteDelete(user, note);
    } catch (err) {
      await NoteDataWrapper.handleErrorWithFallback(user, err, 'notes', LOG_NAME, 'delete', `Error deleting ${note.file_id}`);
      return NoteDataWrapper.delete(user, note);
    }
  }

  static async deleteAll(user: ForaUser, destroy_containers?: boolean): Promise<number> {
    try {
      return await data.plugins.storagePlugin().notesClear(user, destroy_containers);
    } catch (err) {
      await NoteDataWrapper.handleErrorWithFallback(user, err, 'notes', LOG_NAME, 'deleteAll', 'Error deleting all notes');
      return NoteDataWrapper.deleteAll(user);
    }
  }

  static async load(user: ForaUser): Promise<Note[]> {
    try {
      return data.plugins.storagePlugin().notesLoad(user);
    } catch (err) {
      await NoteDataWrapper.handleErrorWithFallback(user, err, 'notes', LOG_NAME, 'notes', 'Error loading notes');
      return NoteDataWrapper.load(user);
    }
  }

  static async save(user: ForaUser, note: Note): Promise<void> {
    try {
      return await data.plugins.storagePlugin().noteSave(user, note);
    } catch (err) {
      await NoteDataWrapper.handleErrorWithFallback(user, err, 'notes', LOG_NAME, 'save', `Error saving ${note.file_id}`);
      return NoteDataWrapper.save(user, note);
    }
  }
}
