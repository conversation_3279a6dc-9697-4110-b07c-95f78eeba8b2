/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { v4 as uuid } from 'uuid';

import data from '..';
import config from '../../config';
import ForaUser from '../../session/user';

import { SourceKeys } from '../../sources';
import { ITasksSourcePlugin, TaskStatus } from '../../sources/tasks/i_tasks_source_plugin';

import { dehydrate<PERSON>erson, IEntity, Person, Task } from '../../types/items';
import { AuthLevel, AuthProviders, Uid } from '../../types/shared';

import logging from '../../utils/logging';
import peopleUtils from '../../utils/people';

import { AbstractDataWrapper } from './a_data_wrapper';

// const DEBUG = require('debug')('fora:data:wrappers:task');
const LOG_NAME = 'data.wrappers.TaskDataWrapper';

export class TaskDataWrapper extends AbstractDataWrapper {
  static byId(user: ForaUser, id: Uid): Promise<Task> {
    return data.plugins.storagePlugin().taskById(user, id);
  }

  static async complete(user: ForaUser, task: Task): Promise<Task> {
    let deletes = [];

    task.completed = new Date();
    if (config.isEnvOffline() || !user.isAuthenticated(AuthLevel.Organizer)) task.status = TaskStatus.completed;
    //else [task, deletes] = await TaskDataWrapper.sourcePlugin(user, task.account).complete(task);

    await TaskDataWrapper.handleAncillary(user, deletes, 'complete', LOG_NAME);
    await data.plugins.storagePlugin().taskSave(user, task);

    return task;
  }

  static async uncomplete(user: ForaUser, task: Task): Promise<Task> {
    let deletes = [];

    task.completed = null;
    if (config.isEnvOffline() || !user.isAuthenticated(AuthLevel.Organizer)) task.status = TaskStatus.needsAction;
    // else [task, deletes] = await TaskDataWrapper.sourcePlugin(user, task.account).uncomplete(task);

    await TaskDataWrapper.handleAncillary(user, deletes, 'uncomplete', LOG_NAME);
    await data.plugins.storagePlugin().taskSave(user, task);

    return task;
  }

  static async create(user: ForaUser, task: Task) {
    let deletes = [];

    if (config.isEnvOffline() || !user.isAuthenticated(AuthLevel.Organizer)) {
      if (!task.id) task.id = uuid();
      task.status = TaskStatus.needsAction;
    } else {
      /*try {
        [task, deletes] = await TaskDataWrapper.sourcePlugin(user, task.account).create(task);
      } catch(e) {
        logging.errorFP(LOG_NAME, 'create', user.profile, `Error creating source task`, e);
      }*/
    }

    await TaskDataWrapper.handleAncillary(user, deletes, 'create', LOG_NAME);
    return data.plugins.storagePlugin().taskSave(user, new Task(task));
  }

  static async delete(user: ForaUser, task: Task) {
    //if (!config.isEnvOffline() || !user.isAuthenticated(AuthLevel.Organizer)) await TaskDataWrapper.sourcePlugin(user, task.account).delete(task);
    return data.plugins.storagePlugin().taskDelete(user, task);
  }
  
  static async reload(user: ForaUser, task: Task): Promise<Task> {
    if (!config.isEnvOffline() && user.isAuthenticated(AuthLevel.Organizer)) {
      try {
        return TaskDataWrapper.sourcePlugin(user, task.account).reload(task);
      } catch(e) {
        logging.errorFP(LOG_NAME, 'reload', user.profile, `Error reloading source task`, e);
      }
    }
    else return task;
  }

  static save(user: ForaUser, task: Task): Promise<Task> {
    return data.plugins.storagePlugin().taskSave(user, task);
  }

  static async migratePeople(user: ForaUser, self: Partial<Person>, resolve_map: {[key: string]: Uid}, people_ids: {[key: string]: Person }, people_comms: {[key: string]: Uid[]}): Promise<IEntity[]> {
    const update_tasks: Task[] = [];

    const tasks = await TaskDataWrapper.load(user);

    for (const task of tasks) {
      if (task.people) {
        const [new_people, updated] = peopleUtils.updatePeople(self, task.people, resolve_map, people_ids, people_comms);
        if (updated) {
          task.people = new_people.map(p => dehydratePerson(p));
          if (updated) update_tasks.push(task);
        }
      }
    }

    return update_tasks;
  }

  static sourcePlugin(user: ForaUser, profile: Uid): ITasksSourcePlugin {
    switch (user.provider) {
      case AuthProviders.Msal:
      case AuthProviders.Microsoft:
        return new (data.plugins.sourcePlugins().get(SourceKeys.MicrosoftTasks))(user, profile);
      case AuthProviders.Google:
      default:
        return new (data.plugins.sourcePlugins().get(SourceKeys.GoogleTasks))(user, profile);
    }
  }

  static async load(user: ForaUser): Promise<Task[]> {
    return data.plugins.storagePlugin().tasks(user);
  }

  static async tasksByDate(user: ForaUser, start: Date = null, end: Date = null) {
    return data.plugins.storagePlugin().tasksByDate(user, start, end);
  }

  static async update(user: ForaUser, task: Task): Promise<Task> {
    let deletes = [];

    // if (!config.isEnvOffline() && user.isAuthenticated(AuthLevel.Organizer)) [task, deletes] = await TaskDataWrapper.sourcePlugin(user, task.account).update(task);
    await TaskDataWrapper.handleAncillary(user, deletes, 'update', LOG_NAME);
    if (task) await data.plugins.storagePlugin().taskSave(user, new Task(task));

    return task;
  }
}
