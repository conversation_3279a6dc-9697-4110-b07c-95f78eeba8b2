/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { v4 as uuid } from 'uuid';

import { Recommendation } from '../../types/globals';
import { EntityType, RecommendationInfo, Uid } from '../../types/shared';

import { formatComms } from '../../utils/format';
import { arraysIntersect } from '../../utils/funcs';

import data from '..';

const DEBUG = (require('debug') as any)('fora:data:wrappers:recommendation');
const LOG_NAME = 'data.wrappers.RecommendationDataWrapper';

export class RecommendationDataWrapper {
  static create(recommendation: Recommendation): Promise<Recommendation> {
    recommendation.id = uuid();
    return data.plugins.storagePlugin().recommendationAdd(recommendation);
  }

  static delete(recommendation: Recommendation): Promise<void> {
    return data.plugins.storagePlugin().recommendationDelete(recommendation);
  }

  static async format(recommendation: Recommendation, locale: string): Promise<RecommendationInfo> {
    const from_user = await data.users.globalById(recommendation.from);
    const rec_info = {
      id: recommendation.id,
      type: EntityType.Recommendation,
      from: {type: EntityType.Person, comms: formatComms([from_user.email], locale), name: from_user.name },
      to: {type: EntityType.Person, comms: formatComms(recommendation.to, locale) },
      when: recommendation.when,
      text: recommendation.text,
      public: recommendation.public,
    } as RecommendationInfo;
    return rec_info;
  }

  static async get(id: Uid): Promise<Recommendation> {
    return data.plugins.storagePlugin().recommendationGet(id);
  }

  static async find(from?: Uid, to?: string | string[]): Promise<Recommendation[]> {
    const recommendations = await data.plugins.storagePlugin().recommendationsFind(from, to);
    if (from && to) {
      if (!Array.isArray(to)) to = [to];
      return recommendations.filter(r => r.from === from && arraysIntersect(to as string[], r.to));
    }
    return recommendations;
  }

  static update(recommendation: Recommendation): Promise<Recommendation> {
    recommendation.when = new Date();
    return data.plugins.storagePlugin().recommendationAdd(recommendation);
  }
}