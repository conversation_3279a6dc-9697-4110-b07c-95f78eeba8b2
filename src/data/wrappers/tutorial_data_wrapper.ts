/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import { v4 as uuid } from 'uuid';

import data from '..';
import config from '../../config';

import lang from '../../lang';
import ForaUser from '../../session/user';

import { customerCredit, getProducts } from '../../sources/hyperline_controller';
import { PersonDataWrapper } from './person_data_wrapper';

import { User as GUser, Promotion } from '../../types/globals';
import { Person, Tutorial } from '../../types/items';
import { NotificationType, Uid } from '../../types/shared';

import { proximity } from '../../utils/funcs';
import notifyUser, { NotifyType } from '../../utils/notify';


import { AbstractDataWrapper } from './a_data_wrapper';

// const DEBUG = require('debug')('fora:data:wrappers:task');
const LOG_NAME = 'data.wrappers.TutorialDataWrapper';

export class TutorialDataWrapper extends AbstractDataWrapper {
  static async get(user: ForaUser, id: Uid): Promise<Tutorial> {
    return data.plugins.storagePlugin().tutorialGet(user, id);
  }

  static async load(user: ForaUser): Promise<Tutorial[]> {
    return data.plugins.storagePlugin().tutorialsLoad(user);
  }

  static async save(user: ForaUser, tutorial: Partial<Tutorial>): Promise<Tutorial> {
    if (!tutorial.id) tutorial.id = uuid();
    return data.plugins.storagePlugin().tutorialSave(user, new Tutorial(tutorial));
  }

  static async getCertificate(id: Uid): Promise<{certificate: Tutorial, person: Person}> {
    const cert_user = new ForaUser('certificate');
    const certificate = await data.plugins.storagePlugin().tutorialGet(cert_user, id);
    if(certificate?.user) {
      const fora_user = new ForaUser(certificate.user);
      if(await data.users.init(fora_user, false)) {
        const person = await PersonDataWrapper.getUserPerson(fora_user);
        return {certificate, person};
      }
    }
  }

  static async getCertificatesByUser(profile: Uid): Promise<Tutorial[]> {
    const cert_user = new ForaUser('certificate');
    const certificates = await data.plugins.storagePlugin().userCertificates(profile);
    return certificates;
  }

  static async saveCertificate(user: ForaUser, tutorial: Partial<Tutorial>): Promise<Tutorial> {
    if(tutorial.status?.practiced && tutorial.practice?.hash) {
      const certificate = new Tutorial(tutorial);
      certificate.id = tutorial.practice.hash;
      certificate.user = user.profile;

      const skills: string[] = [];

      const answer_map: {[key:string]: string[]} = {};

      certificate.lesson_set.forEach(lesson => {
        lesson.assessment.prompts.forEach(p => {
          p.answers.forEach(a => {
            answer_map[a.answer.toLowerCase()] = a.skills;
          });  
        });
      });

      certificate.assessments.forEach(a => {
        a.value.forEach(v => {
          if (v.parts?.length && v.parts[0].text) {
            const m = v.parts[0].text.toLowerCase();
            let answer_skills = answer_map[m];
            if(!answer_skills) {
              let askills: string[];
              let lowest = Number.MAX_SAFE_INTEGER;
              for(const answer in answer_map) {
                let ml = proximity(answer, m);
                if (ml < lowest) {
                  lowest = ml;
                  askills = answer_map[answer];
                }
              }
              answer_skills = askills;
            }

            for(const skill of answer_skills) {
              if(!skills.includes(skill)) skills.push(skill);
            }
          }
        });
      });
  
      certificate.skills = skills;

      const cert_user = new ForaUser('certificate');

      return data.plugins.storagePlugin().tutorialSave(cert_user, certificate);
    }
  }

  static async getPromo(code: string) {
    return data.plugins.storagePlugin().promoCode(code);
  }

  static async delPromo(promo: Partial<Promotion>) {
    await data.plugins.storagePlugin().delPromoCode(new Promotion(promo));
  }

  static async loadPromos() {
    return data.plugins.storagePlugin().promoCodes();
  }

  static async savePromo(promo: Partial<Promotion>) {
    const save_promo = new Promotion(promo);
    if(!save_promo.id) save_promo.id = uuid();
    return data.plugins.storagePlugin().savePromoCode(save_promo);
  }

  static async credit(referral_user: GUser, new_user: ForaUser, url?: URL): Promise<void> {
    const did_credit = await data.users.globalCredit(referral_user, new_user);
    if(did_credit) {
      const products = await getProducts();
      const tutorial_product = products.find(x => x.id === config.getPricing('TUTORIAL'));

      const ruser = new ForaUser(referral_user.profile);
      await data.users.init(ruser, false);
      const credit = await customerCredit(ruser, tutorial_product, 1);
      if(url) {
        const tutorials = await TutorialDataWrapper.load(ruser);
        let tutorial;
        if(tutorials.length) tutorial = tutorials.sort((a,b) => {
          if(a.status && !a.status.practiced && !b.status) return -1;
          if(b.status && !b.status.practiced && !a.status) return 1;
          if(a.status.practiced && !b.status.practiced) return 1;
          if(b.status.practiced && !a.status.practiced) return -1;
          if(a.status.assessed) {
            if(b.status.assessed) return a.status.assessed.length - b.status.assessed.length;
            return -1;
          } else if(b.status.assessed) {
            return 1;
          }
          if(a.status.unlocked) {
            if(b.status.unlocked) return a.status.unlocked.length - b.status.unlocked.length;
            return -1;
          } else if(b.status.unlocked) {
            return 1;
          }
          return -1;
        })[0];

        const notification = {
          email: { 
            message: lang.tutorial.TUTORIAL_REFERRAL(`https://${url.host}/?r=${ruser.affiliate}`),
            subject: lang.tutorial.TUTORIAL_REFERRAL_SUBJECT,
            rcpts: [{Email: ruser.email, Name: ruser.name}]
          },
          type: credit.balance_after >= 10 ? NotificationType.ReferralCredit : NotificationType.Referral,
          variables: { 
            name: referral_user.name, 
            referred: new_user.name, 
            required: 10 - credit.balance_after, 
            url: `https://${url.host}/?r=${ruser.affiliate}`,
            tutorial: tutorial ? `https://${url.host}/?t=${tutorial.id}` : `https://${url.host}/t`,
          }
        }

        await notifyUser(ruser, notification, NotifyType.EmailOnly);
      }
    }
  }
}