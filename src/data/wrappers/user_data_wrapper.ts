/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { PubSub } from '@google-cloud/pubsub';
import _ from 'lodash';
import util from 'util';
import { v4 as uuid } from 'uuid';

import { cancel, checkSubscription, lookupCustomer, setCustomerId } from '../../sources/hyperline_controller';
import { SourceController } from '../../sources/source_controller';

import * as SessionCache from '../../session/session_cache';
import ForaUser from '../../session/user';

import { NormalizedProviderToken } from '../../types/auth';
import { User as GUser, InternalError, Notification, TemplateType, Vanity } from '../../types/globals';
import { Group } from '../../types/group';
import { GlobalType, ICacheEntity, ItemCache, Person } from '../../types/items';
import { AuthProviders, EntityType, ForaUserSettings, JobTags, NotificationType, ProfileStatus, Uid } from '../../types/shared';
import { User } from '../../types/user';

import { avroEntity, flatten } from '../../utils/funcs';
import logging from '../../utils/logging';
import notify, { NotifyType } from '../../utils/notify';
import parsers from '../../utils/parsers';
import peopleUtils from '../../utils/people';

import data from '..';
import { AuthProvider } from '../../auth/auth_provider';
import config from '../../config';
import { ErrorHandler } from '../../errors/error_handler';
import lang from '../../lang';
import { PersonDataWrapper } from './person_data_wrapper';

const DEBUG = (require('debug') as any)('fora:data:wrappers:user');
const LOG_NAME = 'data.wrappers.UserDataWrapper';

let tracking_pub;

config.onLoad('user_data_wrapper', async (silent: boolean) => {
  if (!config.isEnvOffline()) {
    const projectId = config.get('GOOGLE_CLOUD_PROJECT');
    if (!silent) logging.infoF(LOG_NAME, 'onLoad', `Tracking requests to ${projectId}`);
    const ps = new PubSub({ projectId });
    tracking_pub = ps.topic(`tracking`);
  }
});

export class UserDataWrapper {
  static byId(user: ForaUser, id: Uid): Promise<User> {
    if (!user || !user.profile || !id) return null;
    return data.plugins.storagePlugin().userById(user, id);
  }

  static checkProfiles(all_profiles: Uid[]): Promise<Uid[]> {
    return data.plugins.bigQueryPlugin().checkProfiles(all_profiles);
  }

  static globalByAccount(account_type: string, account_id): Promise<GUser> {
    if (!account_type || !account_id) return null;
    return data.plugins.storagePlugin().userGlobalByAccountId(account_type, account_id);
  }


  static globalByEmail(email: string | string[]): Promise<GUser[]> {
    if (!email || !email.length || (Array.isArray(email) && !email[0].length)) return null;
    return data.plugins.storagePlugin().userGlobalByEmail(email);
  }

  static globalByAffiliate(affiliate: string): Promise<GUser> {
    if(!affiliate) return null;
    return data.plugins.storagePlugin().userGlobalByAffiliate(affiliate);
  }

  static globalById(id: Uid): Promise<GUser> {
    if (!id) return null;
    return data.plugins.storagePlugin().userGlobalById(id);
  }

  static async globalByIds(ids: Uid[]): Promise<GUser[]> {
    if (!ids) return [];
    return data.plugins.storagePlugin().userGlobalByIds(ids);
  }

  static globalByGroup(group_ids: Uid | Uid[]): Promise<GUser[]> {
    if (!group_ids || !group_ids.length) return null;
    if (!Array.isArray(group_ids)) group_ids = [group_ids];
    return data.plugins.storagePlugin().usersGlobalByGroup(group_ids);
  }

  static globalByVanity(vanity: string): Promise<GUser> {
    if (!vanity || !vanity.length) return null;
    return data.plugins.storagePlugin().userGlobalByVanity(vanity);
  }

  static async globalsByVanities(vanities: string[]): Promise<GUser[]> {
    if (!vanities || !vanities.length) return [];
    return data.plugins.storagePlugin().usersGlobalByVanities(vanities);
  }

  static mapUserEmail(user: Partial<ForaUser>, email: string): Promise<void> {
    return data.plugins.storagePlugin().usersMapGlobalEmail(user.id, email);
  }

  static vanity(vanity: string):  Promise<Vanity> {
    if (!vanity || !vanity.length) return null;
    return data.plugins.storagePlugin().getVanity(vanity);
  }

  static vanitiesByStatus(status: ProfileStatus[]  = null): Promise<string[]> {
    return data.plugins.storagePlugin().getVanitiesByStatus(status);
  }

  static vanityPeopleByEmail(email: string | string[]): Promise<Person[]> {
    return data.plugins.bigQueryPlugin().userPeopleByEmail(email);
  }

  static vanities(vanity_ids?:string|string[]): Promise<Vanity[]> {
    if (vanity_ids) {
      if (Array.isArray(vanity_ids)) return data.plugins.storagePlugin().getVanities(vanity_ids);
      else  return data.plugins.storagePlugin().getVanities([vanity_ids]);
    }
    return data.plugins.storagePlugin().findAtt(null, GlobalType.Vanity) as Promise<Vanity[]>;
  }

  static async globalVanities(user: User, vanity_ids?:string|string[], enrich = false): Promise<Partial<Person>[]> {
    if (!vanity_ids) return (await (data.plugins.storagePlugin().findAtt(null, GlobalType.Vanity) as Promise<Vanity[]>)).map(peopleUtils.personFromVanity);

    if (Array.isArray(vanity_ids)) {
        let bq_vanities = await data.plugins.bigQueryPlugin().userPeopleByVanity(user, vanity_ids, enrich);
        const found_vanities = bq_vanities ? bq_vanities.map(v => v.vanity) : [];
        const missing_ids = vanity_ids.filter(v => !found_vanities.includes(v));
        if (missing_ids.length) {
          const globals = (await data.plugins.storagePlugin().getVanities(missing_ids)).map(peopleUtils.personFromVanity);
          if (globals && globals.length) bq_vanities = bq_vanities.concat(globals);
        }
        return bq_vanities;
    }

    const globals = (await data.plugins.storagePlugin().getVanities([vanity_ids])).map(peopleUtils.personFromVanity);
    if (enrich) {
      const people = await data.people.byAttributeComms(user, flatten(globals.map(g => g.comms)));
      const people_comms: {[key:string]: Person} = {};
      people.forEach(p => {
        p.comms.forEach(c => {
          if (people_comms[c]) peopleUtils.mergePeople(people_comms[c], p);
          else people_comms[c] = p;
        });
      });

      globals.forEach(g => {
        g.comms.forEach(c => {
          if (people_comms[c]) {
            peopleUtils.mergePeople(g, people_comms[c]);
          }
        })
      });   
    } 

    return globals;
  }

  static async generateVanity(user: User, me: Person, group: Group) {
    const names = me.names.slice().sort((a,b) => b.length - a.length).map(n => n.replace(/[, ]/g, ''));
    const handles = parsers.findEmail(me.comms).map(n => n.split('@')[0]);
    let vanities = _.uniq<string>([...names, ...handles].filter(n => n.length > 4).map(n => 
      encodeURI(n.replace(/[,./\\#@!? ]/g, '').toLowerCase())));

    if (!vanities.length && group && group.host) {
      vanities = _.uniq<string>([...names.map(n => `${n}_${group.host}`), ...handles.map(n => `${n}_${group.host}`)].filter(n => n.length > 4).map(n => 
        encodeURI(n.replace(/[,./\\#@!? ]/g, '').toLowerCase())));
    }

    if (!vanities.length) {
      vanities = _.uniq<string>([...names.map(n => `${n}0000`), ...handles.map(n => `${n}0000`)].filter(n => n.length > 4).map(n => 
        encodeURI(n.replace(/[,. ]/g, '').toLowerCase())));
    }

    for(let index = 0; !user.vanity && index < 1000; index++) {
      for (let check_vanity of vanities) {
        if (index) check_vanity = `${check_vanity}${index}`;
        const vanity_user = await data.users.globalByVanity(check_vanity);
        if (!vanity_user) {
          user.vanity = check_vanity;
          break;
        }
      }
    }
  }

  static async cacheItemInfo(user: User, items: Partial<ICacheEntity>[], append = true) {
    const itypes = _.uniq(items.map(i => i.type)) as EntityType[];
    const type_items: {[key:string]: Partial<ICacheEntity>[]} = {};
    for(const itype of itypes) {
      type_items[itype] = items.filter(i => i.type === itype).map(i => { 
          return {
            id: i.id,
            title: i.title,
            last_update: i.last_activity ? i.last_activity : i.last_update,
            score: i.score,
            expert: i.expert,
            rate: i.rate,
            template: i.template,
            deleted: i.deleted,
            archived: i.archived,
          } as Partial<ICacheEntity>
        });
    }

    let user_cache = await data.plugins.storagePlugin().itemInfoCacheGet(user, itypes);
    if (user_cache && user_cache.length) {
      for(const itype in type_items) {
        const type_cache = user_cache.find(c => c.itype === itype);
        if (!type_cache) {
          user_cache.push(new ItemCache({
            id: itype,
            itype,
            cache: type_items[itype],
          }));
        } else if (append) {
          const new_ids = type_items[itype].map(i => i.id);
          const old = type_cache.cache ? type_cache.cache.filter(c => !new_ids.includes(c.id)) : [];
          type_cache.cache = [...old, ...type_items[itype]];
        } else {
          type_cache.cache = type_items[itype];
        }
      }
    } else {
      user_cache = [];
      for(const itype in type_items) {
        user_cache.push(new ItemCache({
          id: itype,
          itype,
          cache: type_items[itype],
        }));
      }
    }

    await data.plugins.storagePlugin().itemInfoCacheSet(user, user_cache);
  }

  static async itemInfoCache(user: User, itypes?: EntityType[]) {
    return data.plugins.storagePlugin().itemInfoCacheGet(user, itypes);
  }

  static async removeItemInfoCache(user: User, items: Partial<ICacheEntity>[]) {
    const itypes = _.uniq(items.map(i => i.type)) as EntityType[];
    const type_ids: {[key:string]: Uid[]} = {};
    for(const itype of itypes) {
      type_ids[itype] = items.filter(i => i.type === itype).map(i => i.id);
    }

    const user_cache = await data.plugins.storagePlugin().itemInfoCacheGet(user, itypes);
    if (user_cache && user_cache.length) {
      for (const itype in type_ids) {
        const type_cache = user_cache.find(c => c.itype == itype);
        if (type_cache && type_cache.cache) {
          type_cache.cache = type_cache.cache.filter(c => !type_ids[itype].includes(c.id));
        }
      }

      await data.plugins.storagePlugin().itemInfoCacheSet(user, user_cache);
    }
  }

  static async deleteAccount(profile: string, provider: AuthProviders, account?: Uid): Promise<void> {
    const user = new ForaUser(profile, provider);
    if (!(await UserDataWrapper.init(user, false))) {
      logging.errorFP(LOG_NAME, 'deleteAccount', profile, "Error initializing user, therefore we can't proceed with deletion", null);
      return;
    }

    if (account && !user.hasAccount(provider, account)) {
      logging.warnFP(LOG_NAME, 'deleteAccount', user.profile, `Not deleting unknown account ${provider}.${account}`);
      return;
    }

    logging.warnFP(LOG_NAME, 'deleteAccount', user.profile, 'Deleting account');

    notify(user, {
      type: NotificationType.Delete,
      email:{
        rcpts: [{Name:user.name, Email: user.email}],
        subject: 'Why did you leave?',
      },
      variables: {firstname: user.name}
    }, NotifyType.EmailOnly, null, false, false);

    const global_user_key = data.plugins.storagePlugin().key(null, { id: user.profile, type: GlobalType.User });
    const global_vanity_key = user.vanity ? data.plugins.storagePlugin().key(null, { id: user.vanity, type: GlobalType.Vanity }) : [];

    // User
    const user_ids = await data.plugins.storagePlugin().userIds(user);
    const user_keys = user_ids && !account ? user_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.User })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${user_keys.length} User records`);

    // Events
    const event_ids = await data.plugins.storagePlugin().eventIds(user, account);
    const event_keys = event_ids ? event_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Event })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${event_keys.length} Event records`);

    // Tasks
    const task_ids = await data.plugins.storagePlugin().taskIds(user, account);
    const task_keys = task_ids ? task_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Task })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${task_keys.length} Task records`);

    // Messages
    const message_ids = await data.plugins.storagePlugin().messageIds(user, account);
    const message_keys = message_ids ? message_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Message })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${message_keys.length} Message records`);

    // People
    const person_ids = await data.plugins.storagePlugin().peopleIds(user, account);
    const person_keys = person_ids ? person_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Person })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${person_keys.length} Person records`);

    // Contract
    const contract_ids = await data.plugins.storagePlugin().contractIds(user);
    const contract_keys = contract_ids && !account ? contract_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Contract })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${contract_keys.length} Contract records`);

    // Project
    const project_ids = await data.plugins.storagePlugin().projectIds(user);
    const project_keys = project_ids && !account ? project_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Project })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${project_keys.length} Project records`);

    // Recommendations
    const recommendations = await data.recommendations.find(profile);
    const recommendation_keys = recommendations ? recommendations.map(r => data.plugins.storagePlugin().key(null, r)) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${recommendation_keys.length} Recommendation records`);

    // Asks
    const ask_ids = await data.plugins.storagePlugin().askIds(user);
    const ask_keys = ask_ids ? ask_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Ask })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${ask_keys.length} Ask records`);

    // Analysis
    const analysis_ids = await data.plugins.storagePlugin().analysisIds(user);
    const analysis_keys = analysis_ids ? analysis_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Analysis })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${analysis_keys.length} Analysis records`);

    // Goals
    const goal_ids =  await data.plugins.storagePlugin().goalIds(user);
    const goal_keys = goal_ids ? goal_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Goal })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${goal_keys.length} Goal records`);

    // Tutorials
    const tutorial_ids =  await data.plugins.storagePlugin().tutorialIds(user);
    const tutorial_keys = tutorial_ids ? tutorial_ids.map(id => data.plugins.storagePlugin().key(user.profile, { id, type: EntityType.Tutorial })) : [];
    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, `Deleting ${tutorial_keys.length} Tutorial records`);


    // Check for plans
    const group_ids = Object.keys(user.groups);
    await Promise.all(group_ids.map(async group_id  => {
      const plans = await data.plans.getAll(group_id, goal_ids);
      if (plans) {
        for(const plan of plans) {
          let update = false;
          if(plan.assigned?.includes(user.profile)) {
            plan.assigned = plan.assigned.filter(a => a !== user.profile);
            update = true;
          }

          if(plan.unassigned?.includes(user.profile)) {
            plan.unassigned = plan.unassigned.filter(a => a !== user.profile);
            update = true;
          }

          if (update) await data.plans.update(group_id, plan);
        }
      }
    }));

    let delete_ops: Promise<void | number>[];
    if (!account) {
      // Notes
      data.notes.deleteAll(user, true).catch(err => logging.errorFP(LOG_NAME, 'deleteAccount', user.profile, 'Error deleting account notes', err));

      // Imports
      data.imports.deleteAll(user, true).catch(err => logging.errorFP(LOG_NAME, 'deleteAccount', user.profile, 'Error deleting account imports', err));

      // Profile Card
      data.profiles.deleteAll(user, true).catch(err => logging.errorFP(LOG_NAME, 'deleteAccount', user.profile, 'Error deleting account imports', err));

      // Sessions
      SessionCache.logoutSessions(user).catch(err => logging.errorFP(LOG_NAME, 'deleteAccount', user.profile, 'Error deleting account sessions', err));

      const customer = await lookupCustomer(user);
      delete_ops = [
        data.plugins.storagePlugin().delete(null, GlobalType.User, [global_user_key]),
        global_vanity_key ? data.plugins.storagePlugin().delete(null, GlobalType.Vanity, [global_vanity_key]) : new Promise(c => c()),
        data.plugins.bigQueryPlugin().dropTable(user.profile),
        cancel(customer),
      ];
    }
    if (user_keys && user_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.User, user_keys));
    if (event_keys && event_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Event, event_keys));
    if (task_keys && task_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Task, task_keys));
    if (message_keys && message_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Message, message_keys));
    if (person_keys && person_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Person, person_keys));
    if (contract_keys && contract_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Contract, contract_keys));
    if (project_keys && project_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Project, project_keys));
    if (recommendation_keys && recommendation_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, GlobalType.Recommendation, recommendation_keys));
    if (ask_keys && ask_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Ask, ask_keys));
    if (analysis_keys && analysis_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Analysis, analysis_keys));
    if (goal_keys && goal_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Goal, goal_keys));
    if (tutorial_keys && tutorial_keys.length) delete_ops.push(data.plugins.storagePlugin().delete(user.profile, EntityType.Tutorial, tutorial_keys));

    await Promise.all(delete_ops).catch(err => {
      logging.errorFP(LOG_NAME, 'deleteAccount', user.profile, 'Error deleting account', err);
    });

    logging.infoFP(LOG_NAME, 'deleteAccount', user.profile, 'Deleted account');
  }

  static globalRegister(user: ForaUser, context: string = null, referred_by?: string): Promise<GUser> {
    return data.plugins.storagePlugin().userGlobalRegister(user, context, referred_by);
  }

  static bulkGlobalSave(users: GUser[]): Promise<void> {
    return data.plugins.storagePlugin().bulkGlobalUserSave(users);
  }

  static async saveVanity(user: ForaUser, person: Partial<Person>): Promise<Vanity> {
    const old_vanities = await PersonDataWrapper.vanityByIds([user.profile]);

    const guser = await UserDataWrapper.globalById(user.id);

    if (!guser.vanity) guser.vanity = user.vanity;

    const vanity = Vanity.fromUser(guser, person, old_vanities && old_vanities.length ? flatten(old_vanities.map(v => v.templates)) : []);

    // delete old vanities
    for (const old_vanity of old_vanities) {
      if (vanity.vanity !== old_vanity.vanity) await data.plugins.storagePlugin().vanityDelete(old_vanity);
    }

    await data.plugins.storagePlugin().userGlobalUpdateVanity(user);
    await data.plugins.storagePlugin().vanitySave(vanity);
    return vanity;
  }

  static async updateVanity(_user, vanity: Partial<Vanity>): Promise<Vanity> {
    const existing_vanity = await UserDataWrapper.vanity(vanity.vanity);
    if (vanity.comms) existing_vanity.comms = vanity.comms;
    if (vanity.tags) existing_vanity.tags = vanity.tags;
    if (vanity.photos) existing_vanity.photos = vanity.photos;
    if (vanity.names) existing_vanity.names = vanity.names;
    if (vanity.nickName) existing_vanity.nickName = vanity.nickName;
    if (vanity.displayName) existing_vanity.displayName = vanity.displayName;
    if (vanity.urls) existing_vanity.urls = vanity.urls;
    if (vanity.bio) existing_vanity.bio = vanity.bio;
    if (vanity.templates) existing_vanity.templates = vanity.templates;

    const orgs = existing_vanity.tags.filter(t => JobTags.includes(t.type)).map(x => x.value);
    parsers.cleanTags(existing_vanity.tags, existing_vanity.names.concat(orgs).filter(n => n).map(n => n.toLowerCase()));

    existing_vanity.vanityStatus();
    await data.plugins.storagePlugin().vanitySave(existing_vanity);
    return existing_vanity;
  }

  static globals(since: Date = null): Promise<GUser[]> {
    if (since) return data.plugins.storagePlugin().findRange(null, GlobalType.User, 'last', since, true) as Promise<GUser[]>;
    return data.plugins.storagePlugin().findAtt(null, GlobalType.User) as Promise<GUser[]>;
  }

  static globalIds(since: Date = null): Promise<Partial<GUser>[]> {
    if (since) return data.plugins.storagePlugin().findRange(null, GlobalType.User, 'last', since, true /*, ['profile', 'start', 'last', 'onboarding']*/) as Promise<Partial<GUser>[]>;
    return data.plugins.storagePlugin().findAtt(null, GlobalType.User, null, null, false, 0, ['profile', 'start', 'last']) as Promise<Partial<GUser>[]>;
  }

  static newUserIds(): Promise<Partial<GUser>[]> {
    return data.plugins.storagePlugin().findAtt(null, GlobalType.User, 'last', [new Date(0)], false, 0, ['profile']) as Promise<Partial<GUser>[]>;
  }

  static onboarding(states: TemplateType[]): Promise<Partial<GUser>[]> {
    return data.plugins.storagePlugin().findAtt(null, GlobalType.User, 'onboarding', states, false, 0, ['profile']) as Promise<Partial<GUser>[]>;
  }

  // TODO: don't reload if not needed
  static async init(user: ForaUser, check_tokens = true, update_last = false, tokens: NormalizedProviderToken = null, full_refresh?: EntityType[]): Promise<boolean> {
    if (!user.profile || user.profile === lang.init.FORA_PROFILE) {
      logging.warnFP(LOG_NAME, 'init', user.profile, `Cannot initialized user with no profile or anonymous`);
      return false;
    }
    try {
      if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'init', user.profile, `Init ${user.profile} at ${new Date()} last ${user.last}`);

      let global_user = await UserDataWrapper.globalById(user.id);
      let save_affiliate_code = false;
      if (global_user) {
        if (global_user.groups) {
          for (const group of global_user.groups) {
            if (!user.groups[group]) user.groups[group] = [];
          }
        }
        save_affiliate_code = true;
        user.affiliate = global_user.affiliate;
        user.start = global_user.start;

        if(!user.name) user.name = global_user.name;
        if(!user.email) user.email = global_user.email;
      }

      // We always restore all the data (if its an existing user)
      let records = await data.plugins.storagePlugin().users(user);
      // if (logging.isDebug(user.profile)) DEBUG(`init::${user.profile}::user tokens before ${util.format(user.tokens)}`);
      const restore = new User();
      let group_roles;
      let settings;
      for (const record of records) {
        for (const att in record) {
          if (record[att] === null) delete record[att];
        }

        const restore_lr = new Date(restore.last_refresh);
        _.merge(restore, record);
        if (new Date(restore.last_refresh) < new Date(record.last_refresh)) restore.last_refresh = record.last_refresh;
        if (new Date(restore.last_refresh) < restore_lr) restore.last_refresh = restore_lr;
        if (record.id === `group_${user.profile}`) group_roles = record.groups;
        if (record.id === `settings_${user.profile}`) settings = record.settings;
      }

      if (full_refresh && full_refresh.length && 
        (!restore.full_refresh || !restore.full_refresh.length) && 
        (!user.full_refresh || !user.full_refresh.length)) restore.full_refresh = full_refresh;

      if (group_roles) restore.groups = group_roles;
      if (settings) restore.settings = settings;

      user.restore(restore);

      if (logging.isDebug(user.profile)) DEBUG(`init::${user.profile}::user tokens after ${util.format(user.tokens)}`);

      if (user.groups && Object.keys(user.groups).length) {
        await user.loadGroupsFromDatastore();
      }

      // Only do token/provider work if requested to
      if (tokens) user.tokens = tokens;

      if (check_tokens) {
        if (logging.isDebug(user.profile)) logging.debugFP(LOG_NAME, 'init', user.profile, `user ${user.profile} tokens = ${logging.formatToken(user.tokens)}`);
        if (user.tokens && user.tokens['access_token']) {
          if (AuthProvider.isExpired(user.profile, user.tokens)) {
            let refreshed = false;
            logging.warnFP(LOG_NAME, 'init', user.profile, `Re-authenticating expired tokens`);
            try { 
              user.tokens = await AuthProvider.reAuthenticate(user.tokens);
              refreshed = true;
            } catch(e) {
              logging.warnFP(LOG_NAME, 'init', user.profile, `Error reauthenticating - revoking tokens`, e);
              await user.revokeTokens();
            }
            user.updatePermissions();
            await data.plugins.storagePlugin().userSave(user, false, true, update_last);
            await data.plugins.storagePlugin().userGlobalRegister(user);
            return refreshed;
          }

          if(save_affiliate_code) global_user = await data.plugins.storagePlugin().userGlobalRegister(user);
          return true; // Successfully reloaded the data and did our token work
        }
      } else {
        if(save_affiliate_code) global_user = await data.plugins.storagePlugin().userGlobalRegister(user);
        return true; // Successfully reloaded just the data
      }
    } catch (err) {
      const handled = await ErrorHandler.handleError(user, user.provider, err, UserDataWrapper.message, UserDataWrapper.logoutUser);
      if (handled) logging.infoFP(LOG_NAME, 'init', user.profile, 'Error handled, moving on');
      else {
        // console.log('Auth Init Error! - %O', stringify(err));
        logging.errorFP(LOG_NAME, 'init', user.profile, 'Error in auth init', err);
      }

      // Save any "accounts" and "data_sources" changes from anything above
      await UserDataWrapper.saveBoth(user, true).catch(e => logging.errorFP(LOG_NAME, 'init', user.profile, 'Error saving user after error in init', e));
    }

    return false; // Unable to properly initialize the user
  }

  static async merge(merge_into: GUser, merge_from: GUser, force = false) {
    if (merge_into.email !== merge_from.email) {
      if(!force)  logging.warnFP(LOG_NAME, 'merge', merge_into.profile, `${merge_from.profile} merging accounts with mismatched emails ${merge_into.email} ${merge_from.email}`);
      else {
        logging.warnFP(LOG_NAME, 'merge', merge_into.profile, `${merge_from.profile} not merging accounts with mismatched emails ${merge_into.email} ${merge_from.email}`);
        return;
      }
    }

    const user = new ForaUser(merge_into.profile);
    await data.users.init(user, false);

    const merge = new ForaUser(merge_from.profile);
    await data.users.init(merge, false);

    // merge users

    // merge accounts
    for(const provider in merge.accounts) {
      for(const profile in merge.accounts[provider]) {
        if (!(provider in user.accounts)) user.accounts[provider] = {};
        if (!(profile in user.accounts[provider])) user.setTokens(merge.getTokens(provider as AuthProviders, profile), provider as AuthProviders, profile);
        else {
          logging.warnFP(LOG_NAME, 'merge', user.profile, `${merge.profile} skipping duplicate account ${provider}.${profile}`);
        }
      }
    }

    // merge groups
    if (!user.groups) user.groups = {};
    for(const group_id in merge.groups) {
      if (!(group_id in user.groups)) user.groups[group_id] = [];
      user.groups[group_id] = _.uniq([...user.groups[group_id], ...merge.groups[group_id]]);
    }

    // merge settings
    user.saveSettings(merge.settings);

    // merge subscriptions
    const merge_sub = await checkSubscription(merge);
    if (merge_sub) {
      const user_sub = await checkSubscription(user);
      if (!user_sub) {
        const customer = await lookupCustomer(merge);
        await setCustomerId(customer, user.profile);
      }
    }

    // merge vanities
    const me = await data.people.getUserPerson(user);
    const merge_vanity = await data.users.vanity(merge.vanity);

    if (merge_vanity) {
      const merge_me = peopleUtils.personFromVanity(merge_vanity);
      peopleUtils.mergePeople(me, merge_me, true);
      await data.users.saveVanity(user, me);
    }



    // merge types: events, tasks, messages, persons, contracts, projects, recommendations, asks, analyses, goals, notes, imports
    const events = await data.events.load(merge);
    const tasks = await data.tasks.load(merge);
    const messages = await data.messages.load(merge);
    const people = await data.people.load(merge);
    const contracts = await data.contracts.load(merge);
    const projects = await data.projects.load(merge);
    const recs = await data.recommendations.find(null, merge.id);
    const asks = await data.asks.load(user);
    const analyses = await data.analyses.load(user);
    const goals = await data.goals.load(user);
    const notes = await data.notes.load(user);
    const imports = await data.imports.load(user);

    // migrate ids
    // contracts
    // projects

    /*
    await data.events.saveAll(user, events);
    await data.tasks.saveAll(user, tasks);
    await data.messages.saveAll(user, messages);
    await data.people.saveAll(user, people);
    await data.contracts.saveAll(user, contracts);
    await data.projects.saveAll(user, projects);
    await data.recommendations.saveAll(user, recs);
    await data.asks.saveAll(user, asks);
    await data.analyses.saveAll(user, analyses);
    await data.goals.saveAll(user, goals);
    await data.notes.saveAll(user, notes);
    await data.imports.saveAll(user, imports);
    */

    // refresh
    await SourceController.refresh(user, me);

    // update profile
  }

  static async messagesAndNotifications(user: ForaUser): Promise<[string[], Notification[]]> {
    return data.plugins.storagePlugin().userGlobalMessagesAndNotifications(user);
  }

  static async message(user: ForaUser, message: string): Promise<void> {
    await user.setUpdate();
    return data.plugins.storagePlugin().userGlobalMessageAdd(user, message);
  }

  static async messages(user: ForaUser, read_only: boolean): Promise<string[]> {
    if (!read_only) await user.setUpdate();
    return data.plugins.storagePlugin().userGlobalMessages(user);
  }

  static async messagesClear(user: ForaUser): Promise<void> {
    await user.setUpdate();
    return data.plugins.storagePlugin().userGlobalMessagesClear(user);
  }

  static async notification(user: ForaUser, notification: Notification, update_last: boolean): Promise<void> {
    await user.setUpdate();
    if (!notification.id) notification.id = uuid();
    return data.plugins.storagePlugin().userGlobalNotificationAdd(user, notification, update_last);
  }

  static async notifications(user: ForaUser, read_only: boolean): Promise<Notification[]> {
    return data.plugins.storagePlugin().userGlobalNotifications(user);
  }

  static async notificationsClear(user: ForaUser, type: NotificationType = null): Promise<void> {
    await user.setUpdate();
    return data.plugins.storagePlugin().userGlobalNotificationsClear(user, type);
  }

  static async notificationClear(user: ForaUser, id: Uid): Promise<void> {
    await user.setUpdate();
    return data.plugins.storagePlugin().userGlobalNotificationClear(user, id);
  }

  static async notificationUpdate(user: ForaUser): Promise<void> {
    await user.setUpdate();
    return data.plugins.storagePlugin().userGlobalNotificationUpdate(user);
  }

  static async refreshClear(user: ForaUser, type: EntityType): Promise<void> {
    await data.plugins.cachePlugin().clearCacheAttVal(user.profile, type, 'refreshed', '');
    const index = user.refreshed.indexOf(type);
    if (index !== -1) {
      user.refreshed.splice(index, 1);
      logging.infoFP(LOG_NAME, 'refreshClear', user.profile, `refresh ${type}`);
    }
    user.resave = true;
    return;
  }

  static async refreshNeeded(user: ForaUser, type: EntityType):  Promise<Date> {
    if (user.refreshed.includes(type)) return new Date(user.last_refresh);
    return new Date(await data.plugins.cachePlugin().lookupCacheAttVal(user.profile, type, 'refreshed', ''));
  }

  static async refreshSet(user: ForaUser, type: EntityType | EntityType[]): Promise<void> {
    const now = new Date();
    logging.infoFP(LOG_NAME, 'refreshSet', user.profile, `refresh ${type} at ${now.getTime()}`);
    if (Array.isArray(type)) {
      for (const t of type) {
        if (!user.refreshed.includes(t)) user.refreshed.push(t);
        await data.plugins.cachePlugin().cacheAttVal(user.profile, t, 'refreshed', '', now);
      }
    } else {
      if (!user.refreshed.includes(type)) user.refreshed.push(type);
      await data.plugins.cachePlugin().cacheAttVal(user.profile, type as EntityType, 'refreshed', '', now);
    }
   
    user.last_refresh = now;
    user.resave = true;
    return;
  }

  static async onboardingSet(user: ForaUser, onboarding: TemplateType): Promise<void> {
    await data.plugins.storagePlugin().userGlobalOnboarding(user, onboarding); 
  }

  static async quickSave(user: ForaUser) {
    return UserDataWrapper.save(user, false, false, false);
  }

  static async save(user: ForaUser, update = false, force = false, update_last = true): Promise<void> {
    if (user.isAnonymousAccount()) throw new InternalError(500, 'Cannot register anonymous user', user);
    if (user.isGuestAccount()) throw new InternalError(500, 'Cannot register guest user', user);

    await user.setUpdate();
    await data.plugins.storagePlugin().userSave(user, update, force, update_last);
    if (update_last) await data.plugins.storagePlugin().userGlobalRegister(user);
  }

  static async saveGroup(user: ForaUser, group_membership: {[key:string]: string[]}) {
    await data.plugins.storagePlugin().userGroupSave(user, group_membership);
  }

  static async saveSettings(user: ForaUser, settings: ForaUserSettings) {
    await data.plugins.storagePlugin().userSettingsSave(user, settings);
  }

  static async saveBoth(user: ForaUser, force = false, update_last = true): Promise<void[]> {
    await user.setUpdate();
    return Promise.all([UserDataWrapper.save(user, false, force, update_last), UserDataWrapper.save(user, true, force)]);
  }

  static async saveGlobal(vanity: Vanity): Promise<void> {
    return data.plugins.bigQueryPlugin().insertUser(vanity);
  }

  static async globalCredit(referred_by: GUser, referred_user: User): Promise<boolean> {
    if(referred_by.profile === referred_user.profile) return false;
    return await data.plugins.storagePlugin().userGlobalReferral(referred_by, referred_user); 
  }

  static async track(user: ForaUser) {
    if (config.isEnvOffline()) return;
    if (user.isAuthenticatedNonGuest()) {
      if (user.tracking) {
        user.track(null); // updates profile, vanity, etc
        if (tracking_pub) {
          const data = Buffer.from(avroEntity(user.tracking));
          await tracking_pub.publishMessage({data});
        }
        else await data.plugins.storagePlugin().track(user);
      }
      user.clearTrack();
    }
  }

  static users(user: ForaUser): Promise<User[]> {
    return data.plugins.storagePlugin().users(user);
  }

  static async userSettings(user: ForaUser): Promise<ForaUserSettings> {
    return data.plugins.storagePlugin().userSettings(user);
  }

  static async logoutUser(user: ForaUser, error: Error): Promise<void> {
    // #566 - revoke the users tokens and log them out of all sessions

    logging.warnFP(LOG_NAME, 'logoutUser', user.profile, 'revoking tokens');
    await user.setUpdate();

    await user.revokeTokens().catch(err => logging.errorFP(LOG_NAME, 'logoutUser', user.profile, 'Error revoking  tokens', err));
    await data.users.save(user, false, true);

    logging.warnFP(LOG_NAME, 'logoutUser', user.profile, 'logging out sessions');
    await SessionCache.logoutSessions(user).catch(err => logging.errorFP(LOG_NAME, 'logoutUser', user.profile, 'Error deleting sessions for expired tokens', err));
  }
}
