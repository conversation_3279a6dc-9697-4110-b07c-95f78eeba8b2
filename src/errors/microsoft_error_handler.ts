/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { stringify } from 'flatted';
import _ from 'lodash';
import ForaUser from '../session/user';
import logging from '../utils/logging';
import { AbstractErrorHandler } from './a_error_handler';

export class MicrosoftErrorHandler extends AbstractErrorHandler {
  log_name = 'errors.MicrosoftErrorHelper';

  // NOTE: If the `code` is also handled in the RETRY handler and we get it here, it means that our retry attempts were exceeded.
  // TODO: The text needs to be moved to LANG!!!
  async handleError(user: ForaUser, error: Error, sendMessage: any, logoutUser: any): Promise<boolean> {
    if (!error) return false;

    const code = error['code'] ? error['code'] : '';
    const message = error['message'] ? error['message'] : '';

    logging.infoFP(this.log_name, 'handleError', user.profile, `Evaluating Error - '${code}' - '${message}'`);
    if (logging.isDebug(user.profile)) logging.debugFP(this.log_name, 'handleError', user.profile, `Error as json string - ${stringify(error)}`);

    // Some parts of the Microsoft client throw "Boom" errors; silly Microsoft
    if (error['isBoom']) return this.handleBoomError(user, error, sendMessage, logoutUser);

    // Code can be null sometimes, take a look at how the ResponseHandler.buildGraphErrorFromErrorObject method works - Code will ALWAYS be null
    if (!code) {
      if (this.isTokenRelated(message)) {
        await logoutUser(user, error);
        return true;
      }
    }

    // see ./readme.md for examples of structure of messages
    // see https://docs.microsoft.com/en-us/graph/onenote-error-codes for OneNote errors
    // see https://docs.microsoft.com/en-us/graph/errors#code-property for generic Graph errors
    switch (code) {
      case 400: // Bad Request
      case 401:
        if (this.isTokenRelated(message)) {
          await logoutUser(user, error);
          return true;
        }
        break;

      case '10001': // An unexpected error occurred and the request failed.
      case '10002': // The service is not currently available.
      case '10003': // The current user's account has exceeded the maximum number of active requests. Your app will have to repeat the request.
      case '10004': // The service can't create a page in the requested section because that section is protected by a password.
      case '10005': // The request contains more than the maximum number of image tags in which the data-render-src attribute contains a PDF. See Add images and files.
      case '10006': // The OneNote API was unable to create a page in the specified section because that section is corrupt.
      case '10007': // The server is too busy to handle the incoming request at this moment. Please try again later.
      case '10008': // One or more of the document libraries on the user or group's OneDrive contains more than 5000 OneNote items (notebooks, sections, section groups), and cannot be queried using the API.
      case '10012': // Unable to create or update the entity because the library that contains the notebook requires items to be checked out before they can be edited.
      case '10013': // One or more of the document libraries on the user or group's OneDrive contains more than 20,000 items and cannot be indexed for querying using the API.
      case '10014': // Azure Key Vault is too busy to handle the incoming request at this moment. Please try again later.
      case '10015': // SharePoint is currently unavailable. Please try again later.
      case '10016': // Document library on the user or group’s OneDrive exceeded unique security scopes threshold limit. The maximum number of unique security scopes set for a library cannot exceed 50,000.
      case '10017': // Bad Request.
      case '19999': // The request failed because an undetermined error occurred.
        sendMessage(user, message);
        return true;

      case '30101': // The user account has exceeded its OneDrive quota. See OneDrive.
      case '30102': // Nothing more can be added to the requested section because it has reached its maximum size.
      case '30103': // Resource consumption is too high for the request.
      case '30104': // The user account has been suspended.
      case '30105': // The user's personal OneDrive for Business site is not provisioned, which is required to access notebooks. The OneNote service will provision the site now. This process may take several minutes.
      case '30106': // OneDrive for Business is being provisioned for the user.
      case '30108': // The user's personal OneDrive for Business could not be retrieved.
      case '30109': // Some users in the request do not exist.
      case '30110': // Student Information Services has not been registered for this tenant.
      case '30111': // There is a generic error with Student Information Services.
      case '30112': // Multiple users affected by the request had the same username.
      case '30113': // The notebook is not configured to allow invites.
      case '30114': // There is a required parameter missing.
        sendMessage(user, `We ran into the following error while working with Microsoft. ${message}`);
        return true;

      case 'accessDenied': // The caller doesn't have permission to perform the action.
      case '40001': // The request doesn't contain a valid OAuth token. See Notes permissions.
      case '40002': // The user doesn't have permission to write to the requested location.
      case '40003': // The user doesn't have permission to access the requested resource.
      case '40004': // The OAuth token doesn't have the required scopes to perform the requested action. See Notes permissions.
      case '40006': // The OAuth token doesn't have the required scopes to perform the requested action. Specifically the edit permission. See Notes permissions.
      case '40007': // The user does not have permissions to access this resource.
      case '40008': // Access is Forbidden for this resource.
      case '40009': // The container is already in use by another resource.
        sendMessage(user, 'We ran into a permissions error while working with Microsoft. Please logout/login and try again');
        return true;

      default:
        // Code can be null sometimes, take a look at how the ResponseHandler.buildGraphErrorFromErrorObject method works. Code will ALWAYS be null

        // For Microsoft OneNote errors, code will always be a string
        if (typeof code === 'string') {
          // 20001 to 29999 are all calling application level errors
          if (code > '20000' && code < '30000') sendMessage(user, 'I ran into an error. Please send feedback!');
          else sendMessage(user, message);
          return true;
        }
    }

    return false;
  }

  private async handleBoomError(user: ForaUser, error: Error, sendMessage: any, logoutUser: any): Promise<boolean> {
    if (this.isTokenRelated(_.get(error, 'data.payload.error'))) {
      const hack = new Error(_.get(error, 'data.payload.error')); // hack - make a regular error out of it
      await logoutUser(user, hack);
      return true;
    }

    if (this.isTokenRelated(_.get(error, 'data.payload.error_description'))) {
      const hack = new Error(_.get(error, 'data.payload.error_description')); // hack - make a regular error out of it
      await logoutUser(user, hack);
      return true;
    }

    if (this.isTokenRelated(_.get(error, 'output.payload.message'))) {
      const hack = new Error(_.get(error, 'output.payload.message')); // hack - make a regular error out of it
      await logoutUser(user, hack);
      return true;
    }

    const message = error['message'] ? error['message'] : '';

    if (message.includes('Response Error: 503 Service Unavailable')) {
      // Retry logic should have already retried, so we just flag that we handled this and move on
      return true;
    }

    return false;
  }
  // Here is how the microsoft client remaps errors coming from the graph
  // ResponseHandler.defaultGraphError = function (statusCode) {
  //   return {
  //     statusCode: statusCode,
  //     code: null,
  //     message: null,
  //     requestId: null,
  //     date: new Date(),
  //     body: null
  //   };
  // };
  // ResponseHandler.buildGraphErrorFromErrorObject = function (errObj) {
  //   var error = ResponseHandler.defaultGraphError(-1);
  //   error.body = errObj.toString();
  //   error.message = errObj.message;
  //   error.date = new Date();
  //   return error;
  // };
  // ResponseHandler.buildGraphErrorFromResponseObject = function (errObj, statusCode) {
  //   return {
  //       statusCode: statusCode,
  //       code: errObj.code,
  //       message: errObj.message,
  //       requestId: (errObj.innerError !== undefined) ? errObj.innerError["request-id"] : "",
  //       date: (errObj.innerError !== undefined) ? new Date(errObj.innerError.date) : new Date(),
  //       body: errObj
  //   };
  // };
}
