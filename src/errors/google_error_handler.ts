/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import { stringify } from 'flatted';
import ForaUser from '../session/user';
import logging from '../utils/logging';
import { AbstractError<PERSON>and<PERSON> } from './a_error_handler';

export class GoogleErrorHandler extends AbstractErrorHandler {
  log_name = 'errors.GoogleErrorHelper';

  async handleError(user: ForaU<PERSON>, error: Error, sendMessage: any, logoutUser: any): Promise<boolean> {
    if (!error) return false;

    const code = error['code'] ? error['code'] : '';
    const message = error['message'] ? error['message'] : '';

    logging.infoFP(this.log_name, 'handleError', user.profile, `Evaluating Error - '${code}' - '${message}'`);
    if (logging.isDebug(user.profile)) logging.debugFP(this.log_name, 'handleError', user.profile, `Error as json string - ${stringify(error)}`);

    // NOTE: If the code is in the RETRY handler and we get it here, it means that our retry attempts were exceeded.

    switch (code) {
      case 400:
      case '400': // invalid grant
      case 401:
      case '401': // unauthorized client - stale token
        if (this.isTokenRelated(message)) {
          await logoutUser(user, error);
          return true;
        }
        break;
      case 403:
      case' 403':
      case 429:
      case '429':
        logging.warnFP(this.log_name, 'handleError', user.profile, `Ignoring quota error - '${code}' - '${message}'`);
        return true;
      case null:
      case undefined:
      case '':
        if (message === 'No refresh token is set.') {
          // This error happens when we try to re-authenticate a user with no refresh tokens
          await logoutUser(user, error);
          return true;
        }
        break;
    }

    return false;
  }
}
