/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */

import ForaUser from '../session/user';
import * as functions from '../utils/funcs';

export abstract class AbstractErrorHandler {
  private static readonly SYNC_TOKEN_MESSAGES = [
    'invalid_grant',
    'invalid_token',
    'unauthorized_client',
    'scopes requested are unauthorized or expired',
    'No access, refresh token or API key is set',
    'Sync token is expired',
    'Access token validation',
    'Invalid Credentials',
  ];
  protected abstract log_name: string;

  abstract handleError(user: ForaUser, error: Error, sendMessage: any, logoutUser: any): Promise<boolean>;

  isTokenRelated(message: string): boolean {
    return functions.stringIncludesAnyRegex(message, AbstractErrorHandler.SYNC_TOKEN_MESSAGES, false);
  }


}
