/**
 * @license
 * AskFora Inc. All Rights Reserved.
 *
 * Use of this source code is governed by the terms expressed in the README
 */
import _ from 'lodash';
import ForaUser from '../session/user';
import { AuthProviders } from '../types/shared';
import { GoogleErrorHandler } from './google_error_handler';
import { MicrosoftErrorHandler } from './microsoft_error_handler';

export class ErrorHandler {
  public static google = new GoogleErrorHandler();
  public static microsoft = new MicrosoftErrorHandler();
  private static readonly ERRORS_CONFIGURATION = ['invalid_client', 'invalid_request', 'invalid_resource', 'unsupported_grant_type'];

  // invalid_grant is in here, lazy way to trigger re-auth
  // unauthorized_client is in here to handle stale tokens
  private static readonly ERRORS_DENIED = ['access_denied', 'consent_required', 'invalid_grant', 'unauthorized_client']; 

  /**
   *
   * @param user - User the error occurred for
   * @param provider - Provider the error occurred for. It is not always the same as user.provider as
   *                   we sometimes fallback from Microsoft to Google (i.e., Notes, Imports)
   * @param error - The error in question
   */
  static async handleError(user: ForaUser, provider: AuthProviders, error: Error, sendMessage: any, logoutUser: any): Promise<boolean> {
    if (!error) return false;

    switch (provider) {
      case AuthProviders.Google:
        return ErrorHandler.google.handleError(user, error, sendMessage, logoutUser);
      case AuthProviders.Msal:
      case AuthProviders.Microsoft:
        return ErrorHandler.microsoft.handleError(user, error, sendMessage, logoutUser);
      case AuthProviders.Offline:
        {
          const handled = await ErrorHandler.google.handleError(user, error, sendMessage, logoutUser);
          if (!handled) return ErrorHandler.microsoft.handleError(user, error, sendMessage, logoutUser);
          return handled;
        }
    }
  }

  static isOAuthConfigurationError(error: Error): boolean {
    if (!error) return false;
    return _.includes(ErrorHandler.ERRORS_CONFIGURATION, error.message);
  }

  static isOAuthDeniedError(error: Error): boolean {
    if (!error) return false;
    return _.includes(ErrorHandler.ERRORS_DENIED, error.message);
  }
}
