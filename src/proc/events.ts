


import Dialog from '../session/dialog';

function process(dialog: Dialog) {
  const message = dialog.message.message;

  return;

  /* if (message.entities) {
    for (const index in message.entities) {
      const entity = message.entities[index];
      if (entity && entity.type === EntityType.Event) dialog.addAction(ActionType.EVENT, entity.name);
    }
  }

  const curr_dialog = dialog.currentDialog();
  if (curr_dialog && curr_dialog.replies.length) {
    const reply = curr_dialog.replies[0];
    if (reply.type === EntityType.Event) dialog.addAction(ActionType.EVENT, reply);
  } */
}

export default process;
