import _ from 'lodash';

import { ActionType } from '../types/globals';
import { DashboardState } from '../types/plugins';
import { EntityType, PersonInfo, Uid } from '../types/shared';

import { MINUTES, sortTasks } from '../utils/datetime';
import logging from '../utils/logging';

import Dialog from '../session/dialog';

const LOG_NAME = 'proc.people';

async function process(dialog: Dialog) {
  const names = dialog.actionValues(ActionType.ENTITY).map(e => e.name).filter(n => n);

  if (!dialog.context.dashboard) {
    let ids: Uid[] = [];
    if (names.length && names[0]) {
      if (names[0].indexOf('people/') !== -1) ids = names;
      else ids = Object.keys(await dialog.people.findByName(names));
    }

    if (!ids.length) return;

    let when = null; //new Date();
    const act_when = dialog.actionValues(ActionType.DATE);
    if (act_when && act_when.length && act_when[0].start) {
      when = new Date(act_when[0].start);
    }

    dialog.context.dashboard = {
      ids,
      when,
      info: {} as PersonInfo,
      person: null,
      related: null,
      loading: false,
      loaded: false,
      timeout: MINUTES(new Date(), 1),
    } as DashboardState;
  }

  const dashboard = dialog.context.dashboard;
  dashboard.when = new Date(dashboard.when);

  if (dashboard) {
    const ids: Uid[] = dashboard.ids;

    if (ids.length) {
      if (!dashboard.loading) {
        dashboard.loading = true;
        dialog.runAsync('dashboard', async () => {
          logging.infoFP(LOG_NAME, 'process', dialog.user.profile, 'ids loading');
          const people = await dialog.people.byId(ids);
          logging.infoFP(LOG_NAME, 'process', dialog.user.profile, 'ids loaded');
          if (people && people.length) {
            if (!dashboard.person && ids.length === 1) {
              dashboard.person = people[0];
              if (!dashboard.person.self) {
              logging.infoFP(LOG_NAME, 'process', dialog.user.profile, 'finding connections');
                const connections = await dialog.people.findConnections(dashboard.person.comms);
                if (connections) dashboard.connections = _.uniq(connections.map(p => p.id).filter(id => id !== dashboard.person.id));
              }
            } else {
              dashboard.related = {};
              for (const index in people) {
                const other = people[index];
                dashboard.related[other.id] = other;
              }
            }
          }
          dashboard.loaded = true;
        });
      }

      if (new Date(dialog.context.dashboard.timeout) < new Date()) {
        logging.infoFP(LOG_NAME, 'process', dialog.user.profile, 'times up');
        dialog.addInfo(dashboard.info, true);
        dialog.reset('dashboard', true);
        return;
      } else if (!dashboard.loaded) {
        // dialog.rePing();
        return;
      }
    }
  }

  if (dashboard.person) {
    let events = null;
    let messages = null;
    let tasks = null;

    if (dashboard.person.self) {
      // dialog.messages.filter(m => funcs.SAME_DAY(new Date(m.received), dashboard.when, dialog.message.offset))
      messages = [];
      events = [];
      tasks = [];
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'process', dialog.user.profile, 'Whats Next');
      const whats_next = dialog.activitySummary(
        [EntityType.Event, EntityType.Task, EntityType.Message],
        dialog.cache.events,
        sortTasks(Object.values(dialog.cache.tasks)),
        dialog.cache.messages,
        dashboard.when,
        null,
        true,
      );

      for (const item of whats_next.next) {
        switch (item.type) {
          case EntityType.Event:
            events.push(item);
            break;
          case EntityType.Message:
            messages.push(item);
            break;
          case EntityType.Task:
            tasks.push(item);
            break;
        }
      }
    }

    logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `getPersonInfo ${dashboard.when}`);
    dashboard.info = dialog.getPersonInfo(dashboard.person, dashboard.when, events, tasks, messages);
    if (dashboard.connections) {
      if (!dashboard.info.related_ids) dashboard.info.related_ids = dashboard.connections;
      else dashboard.info.related_ids = _.uniq(dashboard.info.related_ids.concat(dashboard.connections));
    }
  }

  logging.infoFP(LOG_NAME, 'process', dialog.user.profile, 'get related info');
  if (!dashboard.info.related) dashboard.info.related = [];
  for (const index in dashboard.related) {
    const related = dashboard.related[index];
    if ((dialog.context.dashboard.person == null || related.id !== dashboard.person.id) && !related.self) {
      const related_info = dialog.getPersonInfo(related, dashboard.when);
      dashboard.info.related.push(related_info);
      if (dashboard.info.orgs) dashboard.info.orgs = dashboard.info.orgs.concat(related_info.orgs);
      else dashboard.info.orgs = related_info.orgs;
    }
  }

  logging.infoFP(LOG_NAME, 'process', dialog.user.profile, 'sort related');
  if (dashboard.info.related) {
    dashboard.info.related.sort((a, b) => {
      return dashboard.ids.indexOf(a.id) - dashboard.ids.indexOf(b.id);
    });
  }

  logging.infoFP(LOG_NAME, 'process', dialog.user.profile, 'done');
  dialog.addInfo(dashboard.info, true);
  dialog.reset('dashboard', true);
}

export default process;
