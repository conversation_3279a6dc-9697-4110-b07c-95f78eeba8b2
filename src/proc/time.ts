import _ from 'lodash';
import pos from 'pos';

import { ActionType } from '../types/globals';
import { TimeVal } from '../types/shared';

import { compareTimeVals } from '../utils/datetime';
import { stripPuncs } from '../utils/funcs';
import parsers from '../utils/parsers';

import Dialog from '../session/dialog';

const LOG_NAME = 'proc.time';

const LEXER = new pos.Lexer(); // Moved here because the constructors take a lot of time
const TAGGER = new pos.Tagger(); // Moved here because the constructors take a lot of time

export default function process(dialog: Dialog) {
  const message: string = dialog.message.message;
  const now: Date = new Date();
  const offset = dialog.user.offset;
  const lang = dialog.user && dialog.user.locale ? dialog.user.locale.slice(0,2) : 'en';

  const parts = message.split(' ').map(p => stripPuncs(p));

  const tags = TAGGER.tag(parts.filter(p => p.length));

  let time_set: TimeVal[] = [];

  for (let i = tags.length - 1; i >= 0; i--) {
    const m = tags.slice(i).map(i => i[0]).join(' ');
    const time_vals = parsers.parseTime(m, now, offset, lang, dialog.user.locale, dialog.user.timeZone);
  
    if (time_vals && time_vals.length) {
      if (time_set.length) {
        const overlap = _.intersectionWith(
          time_set.filter(t=> t.start && !isNaN(new Date(t.start).getTime())), 
          time_vals.filter(t=> t.start && !isNaN(new Date(t.start).getTime())), 
            (a,b) => compareTimeVals(a,b,offset));

        // remove overlap from last
        if (overlap.length) {
          time_set = time_set.filter(t => 
            !overlap.find(o => compareTimeVals(o, t, offset))
          );
        }
      } 
      time_set = time_set.concat(time_vals);
    }
  }

  time_set.forEach(r => dialog.addAction(ActionType.DATE, r, r.text));
}
