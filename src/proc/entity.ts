
import _ from 'lodash';

import KeywordTypes from '../lang/types';

import { ActionType, Message } from '../types/globals';
import { EntityType, OrganizationInfo, UndefinedPersonInfo } from '../types/shared';

import { stripPuncs, substrings } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

import * as plugins from '../plugins';
import DataCache from '../session/data_cache';
import Dialog from '../session/dialog';

const DEBUG = (require('debug') as any)('fora:proc:entity');
const LOG_NAME = 'proc.entity';

let reserved = KeywordTypes.slice();
for (const index in plugins) {
  const plugin = plugins[index];
  if (plugin.reserved) {
    reserved = reserved.concat(plugin.reserved.map(r => r.split(' ')[0]));
  }
}

async function process(dialog: Dialog) {
  const message: Message = dialog.message;

  let res = _.uniq([...reserved.slice(), ...parsers.expandWords(dialog.actions.map(a => a.context).filter(c => c?.length))]);

  // don't match on days of the week
  const act_time = dialog.actionValues(ActionType.DATE);
  if (act_time) {
    for (const tt of act_time) {
      res = res.concat(tt.text.toLowerCase().split(' '));
    }
  }

  // filter empty entities, bad data
  if (message.entities && message.entities.includes(null)) {
    logging.warnFP(LOG_NAME, 'process', dialog.user.profile, `Request with null entities - session ${dialog.session.id}`);
    message.entities = message.entities.filter(e => e);
  }

  await analyzeEntities(message, dialog.cache, res);

  if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'process', dialog.user.profile, `analyzeEntities ${JSON.stringify(message.entities)}`);

  const emails = parsers.findEmail(message.message);
  if (emails && emails.length) dialog.addAction(ActionType.COMMS, emails);
  const phones = parsers.findPhone(message.message, message.locale);
  if (phones && phones.length) dialog.addAction(ActionType.COMMS, phones);

  if (message.entities !== undefined && message.entities.length > 0) {
    for (const entity of message.entities) {
      // people and location names sometimes match
      // !!! @todo get these
      let actionType;
      let context;
      let fix_skills = false;

      switch (entity.type) {
        case EntityType.UnresolvedPerson:
          actionType = ActionType.ENTITY;
          context = entity.name;
          /*fix_skills = entity.people && entity.people.filter(p => {
            if (!p.name || !p.name.length) return false;
            const parts = p.name.toLowerCase().split(' ');
            const mparts = message.message.toLowerCase().split(' ');
            return _.intersection(parts, mparts).length > 0;
          }).length > 0;*/
          break;
        case EntityType.UnresolvedOrg:
          actionType = ActionType.ORG;
          context = entity.name;
          break;
        case EntityType.Organization:
          actionType = ActionType.ORG;
          context = entity.name;
          fix_skills = true;
          break;
        case EntityType.Calendar:
          actionType = ActionType.CALENDAR;
          break;
        case EntityType.Person:
          actionType = ActionType.PERSON;
          context = entity.name;
          fix_skills =  true;
          break;
        case EntityType.Task:
          actionType = ActionType.TASK;
          break;
        case EntityType.Note:
          actionType = ActionType.NOTE;
          break;
        case EntityType.Expert:
          entity.type = EntityType.Project;
        case EntityType.Project:
          actionType = ActionType.PROJECT;
          break;
        case EntityType.Contract:
          actionType = ActionType.CONTRACT;
          break;
        case EntityType.Message:
          actionType = ActionType.MESSAGE;
          break;
        case EntityType.Event:
          actionType = ActionType.EVENT;
          break;
        case EntityType.Location:
          actionType = ActionType.LOCATION;
          break;
        case EntityType.Ask:
          actionType = ActionType.ASK;
          break;
        case EntityType.Analysis:
          actionType = ActionType.ANALYSIS;
          break;
        default:
          throw new Error(`Don't know how to handle entities of type ${entity.type}: ${JSON.stringify(entity)}`);
      }
      if (logging.isDebug(dialog.user.profile)) logging.debugFP(LOG_NAME, 'process', dialog.user.profile, `add action ${actionType} ${JSON.stringify(entity)}`);
      if (actionType) {
        // remove skills that match entities
        if (fix_skills && context && context.length) {
          substrings(context).forEach(s => 
            dialog.removeActions(ActionType.SKILL, s, true)
          );
        }

        dialog.addAction(actionType, entity, context);
      }
    }
  }
}

interface EntityMatch {
  name: string;
  word_index: number;
  people: {[key:string]: UndefinedPersonInfo};
  match: boolean;
}

export async function analyzeEntities(message: Message, cache: DataCache, reserved: string[] = []) {
  // look for people and orgs

  if (!message.entities) message.entities = [];
  if (!message.message) return;

  if (logging.isDebug()) DEBUG('analyzeEntities', 'message', message.message);
  // get all words and look for pairs that match people or substrings that match orgs
  const msg = stripPuncs(message.message);
  const lmsg = msg.toLowerCase();
  const words = lmsg.split(' ');
  const names = msg.split(' ');
  let pos = 0;
  const matches = reserved.slice();
  const added: string[] = [];
  const add_all: EntityMatch[] = [];
  const orgs: string[] = [];

  if (logging.isDebug()) DEBUG('analyzeEntities', 'words', words);

  for (let index = 0; index < words.length; index++) {
    // dictionary of id:full name
    const candidates: {[key: string]: string} = await cache.peopleName(words[index]);
    // const org_candidates: {[key: string]: string} = await cache.orgPeople(words[index]);

    if (logging.isDebug()) {
      DEBUG('analyzeEntities', 'candidates', index, words[index], candidates);
     // DEBUG('analyzeEntities', 'org_candidates', index, words[index], org_candidates);
    }

    let found = false;

    /*for (const id in org_candidates) {
      if (!candidates || !candidates[id]) {
        if (!orgs.includes(words[index])) {
          orgs.push(words[index]);
          const people = {};
          people[id] = {id, name: org_candidates[id]};

          add_all.push({
            name: words[index],
            word_index: index,
            people,
            match: true,
          });
        }
      }
    }*/

    if (candidates) {
      let add_index = 0;
      let add_pos = 0;

      for (const id in candidates) {
        const cmp_name: string = candidates[id]; // .toLowerCase();
        const cmp_parts = cmp_name.split(' ');
        if (cmp_parts.length === 1) continue;

        // check candidates for exact matches (full name or first/last)
        let cmp_short = cmp_parts[0];
        if (cmp_parts.length > 1) cmp_short += ` ${cmp_parts[cmp_parts.length - 1]}`;

        let name = null;
        const name_index = msg.indexOf(cmp_name, pos);
        const short_index = msg.indexOf(cmp_short, pos);
        if (name_index === pos && (msg.length === name_index + cmp_name.length || msg[name_index + cmp_name.length] === ' ')) name = cmp_name;
        else if (short_index === pos && (msg.length === short_index + cmp_short.length || msg[short_index + cmp_short.length] === ' ')) name = cmp_short;
        const candidate = {};
        candidate[id] = { id, name: candidates[id] };

        if (name) {
          if (logging.isDebug()) DEBUG('analyzeEntities', 'candidate name', index, id, name);
          let add_to = null;
          if (added.includes(name.toLowerCase())) add_to = add_all.filter(a => a.name === name);

          if (add_to && add_to.length) Object.assign(add_to[0].people, candidate);
          else {
            if (logging.isDebug()) DEBUG('analyzeEntities', '\tadded', name);
            added.push(name.toLowerCase());
            for (const lname of name.toLowerCase().split(' ')) added.push(lname);
            add_all.push({
              name,
              word_index: index,
              people: candidate,
              match: true,
            });

            for (const match_name of cmp_name.toLowerCase().split(' ')) {
              if (!matches.includes(match_name)) matches.push(match_name);
            }

            const cmp_index = cmp_name.split(' ').length - 1;
            add_index = add_index > cmp_index ? add_index : cmp_index;

            const cmp_pos = cmp_name.length + 1;
            add_pos = add_pos > cmp_pos ? add_pos : cmp_pos;

            found = true;
          }
        }
      }

      index += add_index;
      pos += add_pos;

      // if no candidates were exact matches, track the name and index to add all candidates
      if (!found) {
        const name = names[index];
        const res = reserved.includes(name.toLowerCase());
        const fchar = name[0];
        if (!res || index > 1 || fchar === fchar.toUpperCase()) {
          if (logging.isDebug()) DEBUG('analyzeEntities', '\tnot found added', name);
          const add_candidates = {};
          for (const id in candidates) add_candidates[id] = { id, name: candidates[id] };
          added.push(name.toLowerCase());
          for (const lname of name.toLowerCase().split(' ')) added.push(lname);
          add_all.push({
            name: names[index],
            word_index: index,
            people: add_candidates,
            match: false,
          });
          matches.push(name.toLowerCase());
          pos += words[index].length + 1;
          found = true;
        }
      }
    }

    if (!found) pos += words[index].length + 1;

    // skip the word following indefinite artciles
    if (index < words.length - 1 && (words[index] === 'a' || words[index] === 'an')) {
      index++;
      pos += words[index].length + 1;
      matches.push(words[index]); // force ignore
    }
  }

  pos = 0;
  let lindex = 0;

  // get all likely names, add longest strings seperated by spaces and not matched
  const fnames = parsers.findNames(msg);
  if (fnames && fnames.length) {
    if (logging.isDebug()) DEBUG('analyzeEntities', 'likely names', fnames);
    for (let index = 0; index < fnames.length; index++) {
      const name = fnames[index];
      const lname = name.toLowerCase();
      if (!matches.includes(lname) || (!added.includes(lname) && !reserved.includes(lname) && name[0].toUpperCase() === name[0])) {
        // let found = false;
        for (let reach = fnames.length - index; reach >= 0; reach--) {
          const cmp_long = fnames.slice(index, index + reach).join(' ');
          const cmpl_i = msg.indexOf(cmp_long, pos);
          if (cmpl_i > -1) {
            lindex = names.indexOf(fnames[index], lindex);
            const end_i = cmpl_i + cmp_long.length;
            let tail = ' ';
            if (end_i < msg.length - 1) tail = msg.slice(end_i, end_i + 2);
            else if (end_i < msg.length) tail = msg[end_i];
            if (lindex > -1 && tail.includes(' ')) {
              if (logging.isDebug()) DEBUG('analyzeEntities', '\tadded', cmp_long);
              added.push(name.toLowerCase());
              add_all.push({
                name: cmp_long,
                word_index: lindex,
                people: {},
                match: false,
              });
              matches.push(cmp_long.toLowerCase());

              index += reach - 1;
              pos += cmp_long.length + 1;
              // found = true;
              break;
            }
          }
        }

        /*if(!found) {
          message.entities.push({type:'PERSON',name:name});
          matches.push(name);
          pos += name.length + 1;
          }*/
      }
    }
  }

  add_all.sort((a, b) => { return a.word_index - b.word_index; });

  if (logging.isDebug()) DEBUG('analyzeEntities', 'sorted', add_all);

  // remap known orgs
  const add_orgs = [];
  let last_org: OrganizationInfo;
  const del_names = [];
  for (const index in add_all) {
    const match = add_all[index];
    const lname = match.name.toLowerCase();
    for(const org_name of lname.split(' ')) {
      const org = await cache.orgPeople(org_name);
      if (org) {
        const pn = await cache.peopleName(lname);
        if (!pn && (!match.people || !Object.keys(match.people).length)) del_names.push(index);

        if (match.people && Object.keys(match.people).length) {
          // check for context "in" or "at"
          let found_context = false;

          if(match.name.toLowerCase() === message.message.toLowerCase()) found_context = true;
          else {
            const lparts = message.message.split(' ').map(p => p.toLowerCase());
            for (let i = 1; i < lparts.length; i++) {
              if (lparts.slice(i).join(' ').startsWith(lname)) {
                if (['at', 'in', 'to', 'of'].includes(lparts[i - 1])) {
                  found_context = true;
                  break;
                }
              }
            }
          }

          if (!found_context) {
            if (last_org) {
              const lon = last_org.name.toLowerCase();
              if (lon.trim() !== org_name.trim() && !lon.endsWith(` ${org_name}`)) last_org.name += ` ${org_name}`;
            } else logging.infoF(LOG_NAME, 'analyzeEntities', `Not matching org ${match.name} because of matching people and no context`);
            continue;
          }
        }

        if (logging.isDebug()) DEBUG('analyzeEntities', 'org', match.name);

        if (last_org) {
          const lon = last_org.name.toLowerCase();
          if (lon.trim() !== org_name.trim() && !lon.endsWith(` ${org_name}`)) last_org.name += ` ${org_name}`;
        } else {
          last_org = {
            id: 'found_orgs',
            name: match.name,
            type: EntityType.UnresolvedOrg,
          } as OrganizationInfo

          add_orgs.push(last_org);
        }
      } else if(['at', 'in', 'of', 'to'].includes(org_name)) {
        if (last_org) {
          const lon = last_org.name.toLowerCase();
          if (lon.trim() !== org_name.trim() && !lon.endsWith(` ${org_name}`)) last_org.name += ` ${org_name}`;
        }
      } else last_org = null;
    }
  }

  del_names.sort((a, b) => b - a);
  for (const index of del_names) add_all.splice(index, 1);

  message.entities = message.entities.concat(add_orgs);

  // add entities, combining adjacent names
  for (let index = 0; index < add_all.length; index++) {
    let name: string = add_all[index].name;
    const people: { [key: string]: UndefinedPersonInfo } = add_all[index].people;

    // if there wasn't a perfect mactch, look for adjacent names to combine
    if (!add_all[index].match) {
      // combine adjacent names but keep whole candidate list
      for (let cindex = index; cindex < add_all.length - 1; cindex++) {
        // check for adjacent words (word_index)
        if (add_all[index].word_index + 1 === add_all[cindex + 1].word_index) {
          const add_name = add_all[cindex + 1].name;
          if (name.length < add_name.length || name.slice(-1 * add_name.length) !== add_name) {
            name += ` ${add_name}`;
          }
          Object.assign(people, add_all[cindex + 1].people);
          index++; // skip words in the outer loop
        }
      }
    }

    if (logging.isDebug()) DEBUG('analyzeEntities', 'push', name, people);
    if (Object.values(people).length) message.entities.push({ id: 'found_people', type: EntityType.UnresolvedPerson, name, people: Object.values(people) } as UndefinedPersonInfo);
    // matches = matches.concat(name.toLowerCase().split(' '));
  }
}

export default process;
