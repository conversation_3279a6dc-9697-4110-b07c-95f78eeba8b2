import Dialog from '../session/dialog';

import { ActionType } from '../types/globals';

function process(dialog: Dialog) {
  const message = dialog.message.message;
  const lmessage = message.toLowerCase();

  const conjuctions = lmessage.match(/ (and|or) /g);

  if (conjuctions) {
    let index = 0;
    for (const conj of conjuctions) {
      const left = lmessage.indexOf(conj, index);
      const context = lmessage.slice(index, left);
      index = left + conj.length;
      dialog.addAction(ActionType.CONJUNCTION, conj.trim(), context); 
    }

    const context = lmessage.slice(index);
    dialog.addAction(ActionType.CONJUNCTION, 'and', context); 
  }
}

export default process;
