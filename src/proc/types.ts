import * as KeywordTypes from '../lang/types';

import Dialog from '../session/dialog';

import { ActionType } from '../types/globals';
import { GlobalType } from '../types/items';
import { EntityType } from '../types/shared';

import parsers from '../utils/parsers';

function process(dialog: Dialog) {
  const message: string = dialog.message.message.toLowerCase();
  const keywords: string[] = [];

  // legacy
  if (parsers.checkKeyword(message, KeywordTypes.EVENT)) keywords.push(EntityType.Event);
  if (parsers.checkKeyword(message, KeywordTypes.TASK)) keywords.push(EntityType.Task);
  if (parsers.checkKeyword(message, KeywordTypes.MESSAGE)) keywords.push(EntityType.Message);
  if (parsers.checkKeyword(message, KeywordTypes.NOTE)) keywords.push(EntityType.Note);
  if (parsers.checkKeyword(message, KeywordTypes.PERSON)) keywords.push(EntityType.Person);
  if (parsers.checkKeyword(message, KeywordTypes.CONTRACT)) keywords.push(EntityType.Contract);
  if (parsers.checkKeyword(message, KeywordTypes.PROJECT)) keywords.push(EntityType.Project);
  if (parsers.checkKeyword(message, KeywordTypes.USER)) keywords.push(EntityType.User);
  if (parsers.checkKeyword(message, KeywordTypes.GROUP)) keywords.push(GlobalType.Group);

  // dialog.addAction(ActionType.ENTITY_TYPE, keywords);

  // v2
  const event_keywords  = parsers.keywords(message, KeywordTypes.EVENT);
  const task_keywords  = parsers.keywords(message, KeywordTypes.TASK);
  const message_keywords  = parsers.keywords(message, KeywordTypes.MESSAGE);
  const note_keywords  = parsers.keywords(message, KeywordTypes.NOTE);
  const person_keywords  = parsers.keywords(message, KeywordTypes.PERSON);
  const contract_keywords  = parsers.keywords(message, KeywordTypes.CONTRACT);
  const project_keywords  = parsers.keywords(message, KeywordTypes.PROJECT);
  const user_keywords  = parsers.keywords(message, KeywordTypes.USER);
  const group_keywords  = parsers.keywords(message, KeywordTypes.GROUP);
  // const skill_keywords  = parsers.keywords(message, KeywordTypes.SKILL);

  event_keywords.forEach(kwd =>  dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Event, kwd));
  task_keywords.forEach(kwd =>  dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Task, kwd));
  message_keywords.forEach(kwd =>  dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Message, kwd));
  note_keywords.forEach(kwd =>  dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Note, kwd));
  person_keywords.forEach(kwd =>  dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Person, kwd));
  contract_keywords.forEach(kwd =>  dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Contract, kwd));
  project_keywords.forEach(kwd =>  dialog.addAction(ActionType.ENTITY_TYPE, EntityType.Project, kwd));
  user_keywords.forEach(kwd =>  dialog.addAction(ActionType.ENTITY_TYPE, EntityType.User, kwd));
  group_keywords.forEach(kwd => dialog.addAction(ActionType.ENTITY_TYPE, GlobalType.Group, kwd));
  // skill_keywords.forEach(kwd => dialog.addAction(ActionType.ENTITY_TYPE, GlobalType.Skill, kwd));
}

export default process;
