import Dialog from '../session/dialog';

import { ActionType } from '../types/globals';

import { flatten } from '../utils/funcs';
import logging from '../utils/logging';
import parsers from '../utils/parsers';

const LOG_NAME = 'proc.skills';

function process(dialog: Dialog) {
  const message = dialog.message.message;
  const parts = message.split(/[.,/#!$%^&*;:{}=\-_`~()]/).filter(x => x && x.length).map(s => s.trim());
  const meaning = flatten(parts.map(parsers.findMeaning));

  const people = dialog.actionValues(ActionType.PERSON).map(e => e.name.toLowerCase());
  const orgs = dialog.actionValues(ActionType.ORG).map(e => e.name.toLowerCase());

  const ignore_set = new Set(
    [...people.concat(orgs), ...dialog.actions.filter(a => a.context).map(a => a.context.toLowerCase())] //, ...wordSets(stripPuncs(dialog.message.message.toLowerCase(), true))]
  );

  // ignore entities
  for (const index in meaning) {
    const mean = meaning[index];
    if (!ignore_set.has(mean) && !parsers.ignore(mean)) dialog.addAction(ActionType.SKILL, mean, mean);
    else {
      const p = people.includes(mean);
      const o = orgs.includes(mean);
      if (p) logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Skipping ${mean} because of matching person name`);
      else if(o) logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Skipping ${mean} because of matching org name`);
      else {
        const act = dialog.actions.filter(a => a.context).find(a =>
          a.context.toLowerCase() === mean);

        if (act) logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Skipping ${mean} because of matching action ${JSON.stringify(act)}`);
      }
    }
  }
}

export default process;
