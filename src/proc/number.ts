import Dialog from '../session/dialog';
import { ActionType } from '../types/globals';
import parsers from '../utils/parsers';

function findMinPart(part: string, cb: (s) => boolean) {
  // find minimum part that matches
  const ps = part.split(' ');
  for (let i = 1; i < ps.length; i++) {
    if (cb(ps.slice(0,i).join(' '))) {
      for (let j = i - 1; j >= 0; j--) {
        if (cb(ps.slice(j,i).join(' '))) {
          return ps.slice(j,i).join(' '); 
        }
      }
      return ps.slice(0,i).join(' ');
    }
  }
  return part;
}

function process(dialog: Dialog) {
  const message = dialog.message.message;
  const parts: string[] = message.toLowerCase().split(/[*!()'";?]+/);
  for (const part of parts) {
    if (part !== '') {
      const part_num = parsers.findNum(part, true);
      if (part_num && part_num.length) {
        const alt = part_num[0].replace(/,/g, '');
        if (typeof part_num[0] === 'number') {
          const num = Math.min(part_num[0], Number.MAX_SAFE_INTEGER)

          const min_part = findMinPart(part, s => {
            const psn = parsers.findNum(s, true);
            return psn && psn.length && Math.min(psn[0], Number.MAX_SAFE_INTEGER) === num;
          });

          dialog.addAction(ActionType.NUMBER, num, min_part);
        } else if (alt.length && !isNaN(alt)) {
          const num = Math.min(parseFloat(alt), Number.MAX_SAFE_INTEGER);

          const min_part = findMinPart(part, s => {
            const psn = parsers.findNum(s, true);
            return psn && psn.length &&  Math.min(parseFloat(psn[0].replace(/,/g, '')), Number.MAX_SAFE_INTEGER) === num;
          });

          dialog.addAction(ActionType.NUMBER, num, min_part);
        }
      }

      const part_num_round = parsers.findNum(part, false);
      if (part_num_round && part_num_round.length) {
        const alt = part_num_round[0].replace(/,/g, '');
        if (typeof part_num_round[0] === 'number') {
          const num = Math.min(Math.round(part_num_round[0]), Number.MAX_SAFE_INTEGER);

          const min_part = findMinPart(part, s => {
            const pnr = parsers.findNum(s, false);
            return pnr && pnr.length && Math.min(Math.round(pnr[0]), Number.MAX_SAFE_INTEGER) === num;
          });

          dialog.addAction(ActionType.NUMBER_ROUNDED, num, min_part);
        } else if (alt.length && !isNaN(alt)) {
          const num = Math.min(Math.round(alt), Number.MAX_SAFE_INTEGER);

          const min_part = findMinPart(part, s => {
            const pnr = parsers.findNum(s, false);
            return pnr && pnr.length && Math.min(Math.round(pnr[0].replace(/,/g, '')), Number.MAX_SAFE_INTEGER) === num;
          });

          dialog.addAction(ActionType.NUMBER_ROUNDED, num, min_part);
        }
      }
    }
  }
}

export default process;
