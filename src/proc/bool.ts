import lang from '../lang';

import Dialog from '../session/dialog';

import { ActionType } from '../types/globals';

function process(dialog: Dialog) {
  const message = dialog.message.message;
  const lrs: string[] = message.toLowerCase().split(/[ ,*-.+!()'";]+/);
  for (const lr of lrs) {
    const yes_index: number = lang.bool.AFFIRMATIVES.indexOf(lr);
    const no_index: number = lang.bool.NEGATIVES.indexOf(lr);
    if (yes_index > -1) dialog.addAction(ActionType.BOOL, true, lang.bool.AFFIRMATIVES[yes_index]);
    if (no_index > -1) dialog.addAction(ActionType.BOOL, false, lang.bool.NEGATIVES[no_index]);
  }
}

export default process;
