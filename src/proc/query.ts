import { setTimeout } from "timers/promises";

import config from '../config';

import Dialog, { Topics } from '../session/dialog';

import { Plugin } from '../types/plugins';
import { ServerChatResponse } from '../types/shared';

import { hash } from '../utils/funcs';
import logging from '../utils/logging';

import * as plugins from '../plugins';

const LOG_NAME = 'proc.query';

const special_plugins: Plugin[] = [/*plugins.ping,*/ plugins.admin, plugins.feedback];

const plugin_order: Plugin[] = [
  // plugins.privacy,
  // plugins.tos,
  plugins.help,
  plugins.init,
  plugins.settings,
  plugins.take_notes,
  plugins.whats_next,
  plugins.lookup_when,
  plugins.introduce,
  plugins.connect,
  plugins.add_reminder,
  plugins.ask_intro,
  plugins.mail,
  plugins.doit,
  plugins.review,
  plugins.contract,
  plugins.contractor,
  plugins.project_create,
  plugins.project,
  plugins.project_candidates,
  plugins.create,
  plugins.imports,
  // plugins.person_info,
  plugins.find,
  plugins.payment,
  plugins.stripe,
  plugins.wise,
  plugins.slack,
  plugins.about,
  plugins.welcome,
  plugins.defaultplugin,
];

function runPlugins(dialog: Dialog, message: string, raw_message: string, plugin_set: Plugin[]) {
  const auth = dialog.isAuthenticated();
  for (const index in plugin_set) {
    const plugin: Plugin = plugin_set[index];
    if ((auth || !plugin.requiresAuth) && 
      (plugin.isActive(dialog) || plugin.keywordMatch(dialog.actions, message, raw_message, dialog.group_host))) {
      logging.infoFP(LOG_NAME, 'runPlugins', dialog.user.profile, `Setting action with Topic ${dialog.topic} Plugin ${plugin.name}`);
      const acted = plugin.setAction(dialog, message, raw_message);
      if (acted) {
        logging.infoFP(LOG_NAME, 'runPlugins', dialog.user.profile, `Action set with Topic ${dialog.topic} Plugin ${plugin.name}`);
        return true;
      }
    }
  }

  return false;
}

function sendReply(dialog: Dialog, sender: (ServerChatResponse) => Promise<void>) {

  const curr_dialog = dialog.currentDialog();

  const response = {
    id: curr_dialog  && (curr_dialog.info?.length || curr_dialog.replies?.length) ? curr_dialog.id : hash(new Date().getTime().toString()),
    reply: undefined,
    info: undefined,
    answers: [],
    page: 0,
    hint: null,
    suggestion: null,
  } as ServerChatResponse;

  if (dialog.topic !== Topics.NOOP && curr_dialog !== null && !curr_dialog.sent) {
    if (curr_dialog.prompt?.length) {
      response.reply = curr_dialog.prompt;
      curr_dialog.sent = true;
    }

    if (curr_dialog.info?.length) {
      response.info = curr_dialog.info;
      const info_ids = [response.id];
      for (const info of response.info) info_ids.push(info.id);
      response.id = hash(info_ids.join('-'));
      curr_dialog.sent = true;
    }

    if (curr_dialog.answers?.length) {
      response.answers = curr_dialog.answers;
      curr_dialog.sent = true;
    }

    if (curr_dialog.page) {
      response.page = curr_dialog.page;
      curr_dialog.sent = true;
    }

    if (curr_dialog.hint) {
      response.hint = curr_dialog.hint;
      curr_dialog.sent = true;
    }

    if (curr_dialog.suggestion) {
      response.suggestion = curr_dialog.suggestion;
      curr_dialog.sent = true;
    }

    if (curr_dialog.quick_replies) {
      response.quick_replies = curr_dialog.quick_replies;
      curr_dialog.sent = true;
    }

    if(curr_dialog.open) {
      response.open = { type: curr_dialog.open.type, id: curr_dialog.open.id };
      curr_dialog.sent = true;
    }

    const command = dialog.getCommand();
    if (command !== null && Object.keys(command).length) {
      Object.assign(response, command);

      // used for feedback
      if (curr_dialog) {
        curr_dialog.last_command = {};
        Object.assign(curr_dialog.last_command, command);
      }

      curr_dialog.sent = true;

      logging.infoFP(LOG_NAME, 'sendReply', dialog.user.profile, `Command ${JSON.stringify(command)}`);
    }

    dialog.clearCommand();

    if (curr_dialog.sent) {
      dialog.last_run = true;

      if (config.isEnvOffline()) {
        logging.infoFP(LOG_NAME, 'reply', dialog.user.profile, JSON.stringify(response));
      } else if (dialog.user.debug || config.isEnvDevelopment()) {
        logging.infoFP(LOG_NAME, 'reply', dialog.user.profile, JSON.stringify(response));
      }

      sender(response);
    }
  }
}

export default async function runProcess(dialog: Dialog, sender: (ServerChatResponse) => Promise<void>) {
  if (dialog.isProcessing(false)) {
    // force stop
    const command = dialog.getCommand();
    dialog.message.message = '';
    dialog.message.ping = command ? command.ping : 0;
    dialog.clearContext();
    dialog.clearCommand();
    dialog.newDialog({topic: Topics.DEFAULT});
  }

  const raw_message = dialog.message.message;
  const message = raw_message.toLowerCase().trim();

  logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Query with Topic ${dialog.topic}`);

  let did_prep = runPlugins(dialog, message, raw_message, special_plugins);

  // special plugins
  if (!did_prep) {
    // active plugin
    const plugin: Plugin = dialog.currentPlugin();
    if (plugin && (dialog.isAuthenticated() || !plugin.requiresAuth) && plugin.isActive(dialog)) {
      logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Setting action Topic ${dialog.topic} with active Plugin ${plugin.name}`);
      did_prep = plugin.setAction(dialog, message, raw_message);
      if (did_prep) logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Action set Topic ${dialog.topic} with active Plugin ${plugin.name}`);
    }

    // everything else
    if (!did_prep) did_prep = runPlugins(dialog, message, raw_message, plugin_order);
  }

  if (did_prep) dialog.reRun();

  while (dialog.isProcessing(false) || dialog.doReRun) {
    dialog.clearRun();

    logging.infoFP(LOG_NAME, 'runProcess', dialog.user.profile, `Running action with Topic ${dialog.topic}`);
    await dialog.currentPlugin().runAction(dialog);
    logging.infoFP(LOG_NAME, 'runProcess', dialog.user.profile, `Setting prompt with Topic ${dialog.topic}`);
    await dialog.currentPlugin().setPrompt(dialog);
    logging.infoFP(LOG_NAME, 'runProcess', dialog.user.profile, `Exit Topic is ${dialog.topic}`);
  
    await sendReply(dialog, sender);

    // let async jobs complete
    if (dialog.isProcessing(false)) {
      await dialog.saveSession(true).catch(e => dialog.asyncError(e));
      await setTimeout(1000);
      await dialog.runSession();
      dialog.clearCommand();
    }
  }

  await sendReply(dialog, sender);

  dialog.clearActions();
  dialog.restoreTopic();

  await dialog.saveSession(true).catch(e => dialog.asyncError(e));
}