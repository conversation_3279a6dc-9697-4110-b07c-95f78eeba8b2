import Dialog from '../session/dialog';

import { ActionType } from '../types/globals';
import { InvRelation, Relation } from '../types/shared';

import logging from '../utils/logging';
import parsers from '../utils/parsers';

const range_map = {
  "does not have": Relation['<>'],
  "does'nt have": Relation['<>'],
  "doesn't have": Relation['<>'],
  "greater than": Relation['>'],
  "bigger than": Relation['>'],
  "fewer than": Relation['<'],
  "not before": Relation['>'],
  "not after": Relation['<'],
  "more than": Relation['>'],
  "less than": Relation['<'],
  "at least": Relation['>'],
  "have not": Relation['<>'],
  "at most": Relation['<'],
  "did not": Relation['<>'],
  "haven't": Relation['<>'],
  "have'nt": Relation['<>'],
  "doesn't": Relation['<>'],
  "does'nt": Relation['<>'],
  "didn't": Relation['<>'],
  "is not": Relation['<>'],
  upcoming: Relation['!'],
  recently: Relation['~'],
  between: Relation['><'],
  greater: Relation['>'],
  "isn't": Relation['<>'],
  "is'nt": Relation['<>'],
  shorter: Relation['<'],
  passed: Relation['~'],
  future: Relation['!'],
  longer: Relation['>'],
  recent: Relation['~'],
  bigger: Relation['>'],
  before: Relation['<'],
  equals: Relation['='],
  equal: Relation['='],
  first: Relation['^'],
  fewer: Relation['<'],
  start: Relation['^'],
  after: Relation['>'],
  been: Relation['<'],
  have: Relation['='],
  next: Relation['!'],
  past: Relation['~'],
  last: Relation['$'],
  more: Relation['>'],
  less: Relation['<'],
  not: Relation['<>'],
  now: Relation['='],
  has: Relation['='],
  did: Relation['='],
  end: Relation['$'],
  is: Relation['='],
}

const inv_suffx = [
  "greater than",
  "bigger than",
  "fewer than",
  "not before",
  "not after",
  "more than",
  "less than",
  "at most",
  "at least",
  "greater",
  "shorter",
  "longer",
  "buffer",
  "before",
  "fewer",
  "after",
  "more",
  "less",
]

const LOG_NAME = 'proc/range';

const range_kwd = Object.keys(range_map);

export default function process(dialog: Dialog) {
  const message: string = dialog.message.message.toLowerCase();

  let index = 0;
  while (index < message.length) {
    const prefix = parsers.prefix(range_kwd, message, index);
    const suffix = message.slice(index + prefix.length);
    if (logging.isVerbose()) logging.verboseF(LOG_NAME, 'process', `Prefix: "${prefix}" Suffix: "${suffix}"`);
    if (suffix.trim().length) {
      const i = parsers.firstKeyword(suffix, range_kwd);
      if (logging.isVerbose()) logging.verboseF(LOG_NAME, 'process', `first keyword at ${i}`);
      if (i >= 0 && i < suffix.length) {
        const kwd = parsers.findKeyword(suffix, range_kwd);
        const kwd_i = suffix.indexOf(kwd);
        if (logging.isVerbose()) logging.verboseF(LOG_NAME, 'process', `findKeyword ${kwd} at ${kwd_i}`);
        if(prefix && prefix.length) {
          dialog.addAction(ActionType.RANGE, range_map[kwd], `${prefix} ${suffix.trim()}`);
          index += prefix.length + kwd_i + kwd.length + 1;
        } else {
          index += prefix.length + kwd_i + kwd.length + 1;
          // const next_prefix = parsers.prefix(range_kwd, message, index);
          const ir = inv_suffx[kwd] ? InvRelation[range_map[kwd]] : range_map[kwd];
          if (ir) dialog.addAction(ActionType.RANGE, ir, suffix);
        }
      } else break;
    } else break;
  }
}