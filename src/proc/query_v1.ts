import * as plugins from '../plugins';
import Dialog from '../session/dialog';
import { Plugin } from '../types/plugins';
import logging from '../utils/logging';

const LOG_NAME = 'proc.query';

const special_plugins = [plugins.ping, plugins.admin, plugins.feedback];

const plugin_order = [
  // plugins.privacy,
  // plugins.tos,
  plugins.help,
  plugins.init,
  plugins.settings,
  plugins.take_notes,
  plugins.whats_next,
  plugins.lookup_when,
  plugins.introduce,
  plugins.connect,
  plugins.ask_intro,
  plugins.add_reminder,
  plugins.doit,
  plugins.review,
  plugins.contract,
  plugins.contractor,
  plugins.project_create,
  plugins.project,
  plugins.project_candidates,
  plugins.imports,
  plugins.person_info,
  plugins.payment,
  plugins.stripe,
  plugins.wise,
  plugins.slack,
  plugins.about,
  plugins.welcome,
  plugins.defaultplugin,
];

function runPlugins(dialog: Dialog, message: string, raw_message: string, plugin_set: Plugin[]) {
  const auth = dialog.isAuthenticated();
  for (const index in plugin_set) {
    const plugin: Plugin = plugin_set[index];
    if ((auth || !plugin.requiresAuth) && 
      (plugin.isActive(dialog) || plugin.keywordMatch(dialog.actions, message, raw_message, dialog.group_host))) {
      logging.infoFP(LOG_NAME, 'runPlugins', dialog.user.profile, `Setting action with Topic ${dialog.topic} Plugin ${plugin.name}`);
      const acted = plugin.setAction(dialog, message, raw_message);
      if (acted) {
        logging.infoFP(LOG_NAME, 'runPlugins', dialog.user.profile, `Action set with Topic ${dialog.topic} Plugin ${plugin.name}`);
        return true;
      }
    }
  }

  return false;
}

function process(dialog: Dialog) {
  const raw_message = dialog.message.message;
  const message = raw_message.toLowerCase().trim();

  logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Query with Topic ${dialog.topic}`);

  // special plugins
  if (runPlugins(dialog, message, raw_message, special_plugins)) return true;

  // active plugin
  const plugin: Plugin = dialog.currentPlugin();
  if (plugin && (dialog.isAuthenticated() || !plugin.requiresAuth) && plugin.isActive(dialog)) {
    logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Setting action Topic ${dialog.topic} with active Plugin ${plugin.name}`);
    if (plugin.setAction(dialog, message, raw_message)) {
      logging.infoFP(LOG_NAME, 'process', dialog.user.profile, `Action set Topic ${dialog.topic} with active Plugin ${plugin.name}`);
      return;
    }
  }

  // everything else
  return runPlugins(dialog, message, raw_message, plugin_order);
}

export default process;
