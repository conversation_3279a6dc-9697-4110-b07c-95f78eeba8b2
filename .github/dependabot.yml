version: 2
updates:
- package-ecosystem: npm
  directory: "/"
  schedule:
    interval: daily
  open-pull-requests-limit: 10
  ignore:
  - dependency-name: typescript
    versions:
    - 4.2.4
  - dependency-name: firebase
    versions:
    - 8.4.1
  - dependency-name: npm
    versions:
    - 7.10.0
    - 7.8.0
    - 7.9.0
  - dependency-name: "@google-cloud/firestore"
    versions:
    - 4.9.6
    - 4.9.8
    - 4.9.9
  - dependency-name: chrono-node
    versions:
    - 2.2.5
  - dependency-name: "@types/node"
    versions:
    - 14.14.37
  - dependency-name: libphonenumber-js
    versions:
    - 1.9.13
  - dependency-name: "@types/react-dom"
    versions:
    - 17.0.1
    - 17.0.2
  - dependency-name: "@grpc/grpc-js"
    versions:
    - 1.2.10
    - 1.2.9
  - dependency-name: "@octokit/rest"
    versions:
    - 18.3.1
    - 18.3.2
    - 18.3.3
    - 18.3.4
  - dependency-name: react
    versions:
    - 17.0.1
  - dependency-name: mobx
    versions:
    - 6.1.8
  - dependency-name: mocha
    versions:
    - 8.3.0
    - 8.3.1
  - dependency-name: "@types/react"
    versions:
    - 17.0.2
  - dependency-name: mini-css-extract-plugin
    versions:
    - 1.3.7
  - dependency-name: "@slack/types"
    versions:
    - 2.0.0
  - dependency-name: "@slack/interactive-messages"
    versions:
    - 2.0.0
  - dependency-name: less
    versions:
    - 4.1.0
