#!/bin/bash
tools/exp/json_check.js src/files/remap.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/ignore.json src/files/remap.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/mask.json src/files/remap.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/boost.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/names.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/skills.json
if [ $? = 1 ]; then exit; fi
