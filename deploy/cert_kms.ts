import KMS = require('@google-cloud/kms');
import forge = require('node-forge');

let key_ring = 'saml';
let key_name = 'askfora';
let version = '1';

async function _getPublicKey(kms_client: KMS.v1.KeyManagementServiceClient, kms_path: string) {
  // @ts-ignore - method exists, type definition is lacking
  return kms_client.getPublicKey({ name: kms_path });
}

async function _sign(kms_client: KMS.v1.KeyManagementServiceClient, kms_path: string, cert) {
  cert.md = forge.md.sha256.create();

  // const algorithmOid = forge.pki.oids[cert.md.algorithm + 'WithRSAEncryption'];
  const algorithmOid = forge.pki.oids[cert.md.algorithm];
  if (!algorithmOid) {
    const error = new Error('Could not compute certificate digest. Unknown message digest algorithm OID.');
    // @ts-ignore
    error.algorithm = cert.md.algorithm;
    throw error;
  }
  cert.signatureOid = cert.siginfo.algorithmOid = algorithmOid;

  // get TBSCertificate, convert to DER
  cert.tbsCertificate = forge.pki.getTBSCertificate(cert);
  const bytes = forge.asn1.toDer(cert.tbsCertificate);

  // digest
  cert.md.update(bytes.getBytes());
  const digest = { sha256: forge.util.encode64(cert.md.digest().getBytes()) };

  // Sign
  // @ts-ignore - method exists, type definition is lacking
  cert.signature = await kms_client.asymmetricSign({ name: kms_path, digest });
}

// a hexString is considered negative if it's most significant bit is 1
// because serial numbers use ones' complement notation
// this RFC in section 4.1.2.2 requires serial numbers to be positive
// http://www.ietf.org/rfc/rfc5280.txt
function toPositiveHex(hexString) {
  let mostSignificantHexAsInt = parseInt(hexString[0], 16);
  if (mostSignificantHexAsInt < 8) return hexString;

  mostSignificantHexAsInt -= 8;
  return mostSignificantHexAsInt.toString() + hexString.substring(1);
}


async function generate(attrs, options) {
  options = options || {};

  const kms_client = new KMS.v1.KeyManagementServiceClient({ projectId: process.env.GOOGLE_CLOUD_PROJECT });
  const kms_path = kms_client.cryptoKeyVersionPath(process.env.GOOGLE_CLOUD_PROJECT, 'global', key_ring, key_name, version);
  const kms_public_key = await _getPublicKey(kms_client, kms_path);

  const cert = forge.pki.createCertificate();
  cert.serialNumber = toPositiveHex(forge.util.bytesToHex(forge.random.getBytesSync(9))); // the serial number can be decimal or hex (if preceded by 0x)
  cert.validity.notBefore = new Date();
  cert.validity.notAfter = new Date();
  cert.validity.notAfter.setDate(cert.validity.notBefore.getDate() + (options.days || 365));

  attrs = attrs || [
    {
      name: 'commonName',
      value: 'askfora.com',
    },
    {
      name: 'countryName',
      value: 'US',
    },
    {
      shortName: 'ST',
      value: 'Vermont',
    },
    {
      name: 'localityName',
      value: 'Norwich',
    },
    {
      name: 'organizationName',
      value: 'AskFora',
    },
    {
      shortName: 'OU',
      value: 'AskFora',
    },
  ];

  cert.setSubject(attrs);
  cert.setIssuer(attrs);

  cert.publicKey = forge.pki.publicKeyFromPem(kms_public_key[0].pem);

  cert.setExtensions(
    options.extensions || [
      {
        name: 'basicConstraints',
        cA: true,
      },
      {
        name: 'keyUsage',
        keyCertSign: true,
        digitalSignature: true,
        nonRepudiation: true,
        keyEncipherment: true,
        dataEncipherment: true,
      },
      {
        name: 'subjectAltName',
        altNames: [
          {
            type: 6, // URI
            value: 'https://askfora.com',
          },
        ],
      },
    ],
  );

  await _sign(kms_client, kms_path, cert);

  const fingerprint = forge.md.sha1
    .create()
    .update(forge.asn1.toDer(forge.pki.certificateToAsn1(cert)).getBytes())
    .digest()
    .toHex()
    .match(/.{2}/g)
    .join(':');

  const pem = {
    // private: forge.pki.privateKeyToPem(keyPair.privateKey),
    public: kms_public_key[0].pem,
    cert: forge.pki.certificateToPem(cert),
    fingerprint,
    pkcs7: undefined,
    clientPrivate: undefined,
    clientPublic: undefined,
    clientCert: undefined,
    clientPkcs7: undefined,
  };

  if (options && options.pkcs7) {
    const p7 = forge.pkcs7.createSignedData();
    p7.addCertificate(cert);
    pem.pkcs7 = forge.pkcs7.messageToPem(p7);
  }

  if (options && options.clientCertificate) {
    const client_keys = forge.pki.rsa.generateKeyPair(1024);
    const client_cert = forge.pki.createCertificate();
    client_cert.serialNumber = toPositiveHex(forge.util.bytesToHex(forge.random.getBytesSync(9)));
    client_cert.validity.notBefore = new Date();
    client_cert.validity.notAfter = new Date();
    client_cert.validity.notAfter.setFullYear(client_cert.validity.notBefore.getFullYear() + 1);

    const clientAttrs = JSON.parse(JSON.stringify(attrs));

    for (let i = 0; i < clientAttrs.length; i++) {
      if (clientAttrs[i].name === 'commonName') {
        if (options.clientCertificateCN) clientAttrs[i] = { name: 'commonName', value: options.clientCertificateCN };
        else clientAttrs[i] = { name: 'commonName', value: 'askfora.com' };
      }
    }

    client_cert.setSubject(clientAttrs);

    // Set the issuer to the parent key
    client_cert.setIssuer(attrs);

    client_cert.publicKey = client_keys.publicKey;

    // Sign client cert with KMS
    await _sign(kms_client, kms_path, client_cert);

    pem.clientPrivate = forge.pki.privateKeyToPem(client_keys.privateKey);
    pem.clientPublic = forge.pki.publicKeyToPem(client_keys.publicKey);
    pem.clientCert = forge.pki.certificateToPem(client_cert);

    if (options.pkcs7) {
      const clientp7 = forge.pkcs7.createSignedData();
      clientp7.addCertificate(client_cert);
      pem.clientPkcs7 = forge.pkcs7.messageToPem(clientp7);
    }
  }

  const caStore = forge.pki.createCaStore();
  caStore.addCertificate(cert);

  /*try {
    forge.pki.verifyCertificateChain(caStore, [cert], (vfd, depth, chain) => {
      if (vfd !== true) throw new Error('Certificate could not be verified.');
      return true;
    });
  } catch (ex) {
    throw new Error(ex);
  }*/

  return pem;
}

async function go() {
  if (!process.env.GOOGLE_CLOUD_PROJECT) {
    console.log('You must set the GOOGLE_CLOUD_PROJECT environment variable so I know where our KMS is');
    process.exit();
  }

  // specify a KMS signing key that is 2048 bit RSA with SS adding and SHA256 Digest
  if (process.argv[2]) key_name = process.argv[2];

  if (key_name.includes('/')) {
    [key_ring, key_name, version] = key_name.split('/');
    if (!version) version = '1';
  }

  const pems = await generate([{ name: 'commonName', value: 'askfora.com' }], { days: 365 });
  for (const att in pems) {
    console.log(att);
    console.log(pems[att]);
  }
}

go().catch(err => console.error(`Error -> ${err.message} :: ${err.stack}`));
