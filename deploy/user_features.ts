#!/usr/bin/env -S node --require ts-node/register

import yargs from 'yargs';

import config from '../src/config';
import ForaUser from '../src/session/user';
import data from '../src/data';
import { ForaUserSettings, Widget } from '../src/types/shared';

let profile = (yargs.argv['profile'] || yargs.argv['p']) as string;
const add = (yargs.argv['add'] || yargs.argv['a']) as string;
const remove = (yargs.argv['remove'] || yargs.argv['r']) as string;

if (!['development', 'production'].includes(process.env.NODE_ENV)) {
  console.log('NODE_ENV must be set to development or production');
  process.exit();
}

if (!profile || !profile.length) {
  console.log(`Usage: ${process.argv[1]} -p profile -a add,features -r remove,features\n\tWidgets: ${Object.values(Widget)},learning`);
  process.exit();
}


async function main() {
  await config.loadConfig(false, {MEMCACHE_URL: 'localhost:11211'});

  if (profile.includes('@')) {
    const global = await data.users.globalByEmail(profile);
    if (global && global.length) profile = global[0].id;
    else {
      console.log(`User ${profile} not found`);
      process.exit();
    }
  }

  const user = new ForaUser(profile);
  const did_init = await data.users.init(user, false);

  if (!did_init) {
    console.error(`Error loading user ${profile}`);
    process.exit();
  }

  const settings: ForaUserSettings = {} as ForaUserSettings;
  Object.assign(settings, user.settings);
    
  if (add) {
    if (!settings.widgets) settings.widgets = [];

    const add_set = add.split(',') as Widget[];
    for (const ads of add_set) {
      if(Object.values(Widget).includes(ads)
        && !settings.widgets.includes(ads)) settings.widgets.push(ads);

      if ((ads as string) === 'learning') {
        if (!settings.learning) settings.learning = { enabled: true, disallowed: false }
        else {
          settings.learning.enabled = true;
        }
      }
    }
  }
  

  if (remove) {
    if (!settings.widgets) settings.widgets = [];

    const remove_set = remove.split(',') as Widget[];
    settings.widgets = settings.widgets.filter(w => !remove_set.includes(w));

    if ((remove_set as string[]).includes('learning') && settings.learning) settings.learning.enabled = false;
  }

  if (add || remove) {
    if (settings) user.saveSettings(settings, null, true);
    if (user.isAuthenticatedNonGuest()) {
      await data.users.save(user);
      await data.users.saveSettings(user, settings);
    }
  }

  console.log(JSON.stringify(settings, null, 2));

  process.exit();
}

main();
