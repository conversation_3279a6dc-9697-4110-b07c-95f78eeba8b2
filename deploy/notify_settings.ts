import config from '../src/config';
import data from '../src/data';
import { NotificationEmail, NotificationType } from '../src/types/shared';
import ForaUser from '../src/utils/user';

if (!['development', 'production'].includes(process.env.NODE_ENV)) {
  console.log('NODE_ENV must be development or production');
  process.exit();
}

function usage() {
  console.log(`${process.argv[1]} group_id|user_id [type] [email|background|chat] [freq|t|f]`);
  console.log('Notfication Type');
  console.log(`\t${JSON.stringify(Object.values(NotificationType))}`);
  console.log('Frequency');
  console.log(`\t${JSON.stringify(Object.values(NotificationEmail))}`);
  process.exit();
}

if (process.argv.length < 3) usage();

const id = process.argv[2];
const ntype = process.argv[3] as NotificationType;
const arg = process.argv[4];
const set_tf = process.argv[5];

async function main() {
  await config.loadConfig();

  const group = await data.groups.byId(id);
  const user = !group ? new ForaUser(id) : null;

  if (!group && !user) {
    console.log(`Unkown group or user ${id}`);
    process.exit();
  }

  const select = group ? 'Group' : 'User';

  if (user) await data.users.init(user, false);

  if (ntype) {
    let notify = null;

    if (group) notify = group.notifications ? group.notifications.filter(n => n.type === ntype)[0] : null;
    else if (user) notify = user.settings.notifications ? user.settings.notifications.filter(n => n.type === ntype)[0] : null;

    if (notify || set_tf) {
      if (!notify) {
        notify = {
          type:ntype,
          email:NotificationEmail.Now,
          background:true,
          chat:true,
        };

        if (group) {
          if (group.notifications) group.notifications.push(notify);
          else group.notifications = [notify];
        } else if (user) {
          if (user.settings.notifications) user.settings.notifications.push(notify);
          else user.settings.notifications = [notify];
        }
      }

      if (arg) {
        if (set_tf) {
          let did_set = false;
          switch (arg) {
            case 'email':
              if (Object.values(NotificationEmail).includes(set_tf)) {
                notify.email = set_tf as NotificationEmail;
                did_set = true;
              } else console.log(`Invalid value for email: ${set_tf}`);
              break;
            case 'background':
            case 'chat':
              if (set_tf.toLowerCase().slice(0, 1) === 't') {
                notify[arg] = true;
                did_set = true;
              } else if (set_tf.toLowerCase().slice(0, 1) === 'f') {
                notify[arg] = false;
                did_set = true;
              } else console.log(`Invalid value for ${arg}: ${set_tf}`);
              break;
            default:
              console.log(`Unknown parameter ${arg}`);
          }

          if (did_set) {
            if (group) await data.groups.save(group);
            else if (user) await data.users.save(user, false);
            console.log(`${select} ${id} ${ntype} ${arg} = ${set_tf}`);
          }
        } else {
          const value = notify[set_tf];
          if (value) console.log(`${select} ${id} ${ntype} ${arg} = ${value}`);
        }
      } else console.log(JSON.stringify(notify, null, 2));
    } else console.log(`${select} ${id} has no notification settings for ${ntype}`);
  } else {
    let notifications = null;
    if (group) notifications = group.notifications;
    else if (user) notifications = user.settings.notifications;

    if (notifications) console.log(JSON.stringify(notifications, null, 2));
    else console.log(`${select} ${id} has no notification settings`);
  }

  process.exit();
}

main().catch(e=> console.error(e));
