#!/bin/bash
PROJECT=$1
SKIP_BUILD=$2
SERVICE=$3

if [ -z "$PROJECT" ]; then
  echo "Please specifiy test, or production"
  exit
fi

source deploy/${PROJECT}_env.sh
echo "Deploying to "$GOOGLE_CLOUD_PROJECT

GIT_CLEAN=`git status | grep clean`

if [ -z "$GIT_CLEAN" ]; then
  echo "Changed files are not all committed"
  exit
fi

GIT_CLEAN=`git status | grep ahead`

if [ ! -z "$GIT_CLEAN" ]; then
  echo "Changed files are not all pushed"
  exit
fi

if [ -z "$SKIP_BUILD" ]; then
  #NODE_ENV=development npm i #npx npm-force-resolutions && npm i
  if [[ "$PROJECT" = "test" || "$PROJECT" = "development" ]]; then
    npm run full-build:dev
  else
    npm run full-build
  fi
fi

if [ ! -d "lib" ]; then
  echo "Missing lib"
  exit
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "default" ]; then
  if [ ! -d "lib/public" ]; then
    echo "Missing lib/public"
    exit
  fi

  if [ ! -f "lib/public/index.html" ]; then
    echo "Missing lib/public/index.html"
    exit
  fi
fi

if [ "$SERVICE" = "home" ]; then
  if [ ! -d "lib/homepage" ]; then
    echo "Missing lib/homepage"
    exit
  fi

  if [ ! -f "lib/homepage/index.html" ]; then
    echo "Missing lib/homepage/index.html"
    exit
  fi
fi


VERSION=`grep version package.json | sed -r 's/\s*"version": "([0-9]+\.[0-9]+\.[0-9]+)",/\\1/'` 
echo Version $VERSION
LAST_TAG=`git tag | grep $VERSION | tail -1`
if [ ! -z "$LAST_TAG" ]; then
  echo Last $LAST_TAG
  NEW_TAG=`git tag | grep $LAST_TAG | sort -r | head -1 | sed -rE 's/(v[0-9.]*-beta.)([0-9]+)$/echo \1$((\2+1))/e'`
else
  NEW_TAG=v$VERSION
fi

echo "Tagging "$NEW_TAG
git tag $NEW_TAG

PROJECT_ID=askfora-$PROJECT

gcloud datastore indexes cleanup deploy/index.yaml --project=$PROJECT_ID --quiet
gcloud datastore indexes create deploy/index.yaml --project=$PROJECT_ID --quiet

tools/exp/json_check.js src/files/remap.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/ignore.json src/files/remap.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/mask.json src/files/remap.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/boost.json
if [ $? = 1 ]; then exit; fi
tools/exp/json_check.js src/files/names.json
if [ $? = 1 ]; then exit; fi

deploy/load_ignore.js

echo

#check that indexes are ready
echo "Updating indexes"
deploy/wait_indexes.sh $PROJECT_ID
echo

#cleanup errors
echo "Clearing old errors"
gcloud beta error-reporting events delete --project=$PROJECT_ID --quiet
echo

deploy/do_deploy.sh $PROJECT_ID ~/settings/google/google_api.json.fora_$PROJECT $SERVICE
