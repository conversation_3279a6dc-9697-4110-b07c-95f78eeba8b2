import config from '../src/config';
import { DOMParser } from '@xmldom/xmldom';
import * as fs from 'fs';
import { v4 as uuid } from 'uuid';
import * as urlParser from 'fast-url-parser';
import * as funcs from '../src/utils/funcs';
import { AuthProviders } from '../src/types/auth';
import { Group } from '../src/types/globals';

if (!process.env.NODE_ENV) {
  console.log("NODE_ENV must be set to 'production' or 'development'");
  process.exit();
}

if (process.argv.length < 3) {
  console.log(`Usage: ${process.argv[1]} <FederationMetadta.xml> [company name] [domain]`);
  process.exit();
}

const metadata = process.argv[2];

async function main() {
  await config.loadConfig(true);

  const doc = new DOMParser().parseFromString(fs.readFileSync(metadata).toString());
  const entity_id = doc.getElementsByTagName('EntityDescriptor').item(0).getAttribute('entityID');
  const entryPoint = doc.getElementsByTagName('SingleSignOnService').item(0).getAttribute('Location');
  let raw_cert = doc.getElementsByTagName('X509Certificate').item(0).childNodes.item(0)['data'];

  const url = urlParser.parse(entity_id);

  const name = process.argv.length > 3 ? process.argv[3] : funcs.startCase(url.host);
  const email_domain = process.argv.length > 4 ? process.argv[4] : `@${url.hostname}`;

  const host = `${name.toLowerCase().replace(/ /g, '-')}.askfora.com`;

  const cert_array = ['-----BEGIN CERTIFICATE-----'];
  while (raw_cert.length) {
    cert_array.push(raw_cert.slice(0, 76));
    raw_cert = raw_cert.slice(76);
  }
  cert_array.push('-----END CERTIFICATE-----');
  const cert = cert_array.join('\n');

  const group = new Group({
    host,
    company_name: name,
    debug: false,
    disallowed: [],
    email_domain, 
    name,
    provider: AuthProviders.Saml,
    id: uuid(),
    shared_key: uuid(),
    search_groups_contacts: true,
    provider_settings: {
      provider: AuthProviders.Saml,
      oauth: null,
      saml: {
        entryPoint,
        issuer: `https://${host}`,
        callbackUrl: `https://${host}/saml/callback`,
        cert,
        privateCert: `projects/${process.env.GOOGLE_CLOUD_PROJECT}/locations/global/keyRings/saml/cryptoKeys/askfora/cryptoKeyVersions/1`,
        authnContext: 'http://schemas.microsoft.com/ws/2008/06/identity/authenticationmethod/password',
        identifierFormat: null,
      }
    }
  });

  delete (group as any)['type'];
  delete (group as any)['nonIndexedFields'];
  delete (group as any)['schema'];

  console.log(JSON.stringify(group, null, 2));

  process.exit();
}

main();
