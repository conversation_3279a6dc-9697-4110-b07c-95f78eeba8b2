import config from '../src/config';
import data from '../src/data';
import { Group } from '../src/types/globals';
import { NotificationType, NotificationEmail } from '../src/types/shared';

if (!['development', 'production'].includes(process.env.NODE_ENV)) {
  console.error('NODE_ENV must be set to development or production');
  process.exit();
}

function usage() {
  console.log(`Usage: ${process.argv[1]} group [type] [background|chat|email|remove] [true|false] `);
  process.exit();
}

if (process.argv.length < 3) usage();

const group_id = process.argv[2];
const notify_type = process.argv[3];
const method = process.argv[4];
const enable = process.argv[5];

const notify_types = Object.values(NotificationType);
if (notify_type && notify_type.length && !notify_types.includes(notify_type)) {
  console.log(`Valid notification types are ${notify_types.join(', ')}`);
  process.exit();
}

if (method && method.length && !['background', 'chat', 'email', 'remove'].includes(method)) {
  console.log('Valid methods are background, chat, email, remove');
  process.exit();
}

let background = method === 'background' ? ( enable == 'true' ? true : false ) : true;
let chat = method === 'chat' ? ( enable === 'true' ? true : false ) : true;
let email = method === 'email' ? ( enable === 'true' ? 'now' : 'never' ) : 'now';

async function main() {
  await config.loadConfig(true);
  const group:Group = await data.groups.byId(group_id);
  if (group) {
    if (notify_type && notify_type.length) {
      const existing = group.notifications ? group.notifications.find(n => n.type === notify_type) : null;
      if (method === 'remove') {
        if (group.notifications) group.notifications = group.notifications.filter(n => n.type !== notify_type);
      } else if(method && method.length) {
        if (existing) {
          switch(method) {
            case 'background':
              if (enable && enable.length) existing.background = background;
              else console.log(JSON.stringify(existing, null, 2));
              break;
            case 'chat':
              if (enable && enable.length) existing.chat = chat;
              else console.log(JSON.stringify(existing, null, 2));
              break;
            case 'email':
              if (enable && enable.length) existing.email = email as NotificationEmail;
              else console.log(JSON.stringify(existing, null, 2));
              break;
          }
        } else {
          if (!group.notifications) group.notifications = [];
          group.notifications.push({
            type: notify_type as NotificationType,
            background,
            chat,
            email: email as NotificationEmail,
          });
        }
      } else if(existing) {
        console.log(JSON.stringify(existing, null, 2));
      } else {
        console.log(`Cannot find ${notify_type}`);
      }
    } else console.log(JSON.stringify(group.notifications, null, 2));
    
    await data.groups.save(group);
    // console.log(group);
  } else console.error(`Group ${group_id} not found`);

  process.exit();
}

main().catch(e => console.error(e));
