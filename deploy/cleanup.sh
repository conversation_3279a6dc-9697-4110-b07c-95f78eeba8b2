#!/bin/bash
PROJECT=$1

#get old versions that have 0 traffic
DEFAULT_VERSION=`gcloud app versions list --project=askfora-$PROJECT --service=default | grep STOPPED$ | sort -k 3 | head -1 | awk '{print $2}'`
UPDATE_VERSION=`gcloud app versions list --project=askfora-$PROJECT --service=update | grep STOPPED$ | sort -k 3 | head -1 | awk '{print $2}'`
LEARN_VERSION=`gcloud app versions list --project=askfora-$PROJECT --service=learn | grep STOPPED$ | sort -k 3 | head -1 | awk '{print $2}'`

#clean up old versions
if [ ! -z "$DEFAULT_VERSION" ]; then
  gcloud app versions delete $DEFAULT_VERSION --service=default --project=askfora-$PROJECT --quiet
fi

if [ ! -z "$UPDATE_VERSION" ]; then
  gcloud app versions delete $UPDATE_VERSION --service=update --project=askfora-$PROJECT --quiet
fi

if [ ! -z "$LEARN_VERSION" ]; then
  gcloud app versions delete $LEARN_VERSION --service=learn --project=askfora-$PROJECT --quiet
fi
