#!/usr/bin/env node
const fs = require('fs');
const path = require('path');
const ignore = JSON.parse(fs.readFileSync(path.resolve(__dirname, '..', 'src', 'files', 'ignore.json'), 'utf-8'));

const BigQuery = require('@google-cloud/bigquery');
const bigquery = new BigQuery.BigQuery({ projectId:process.env.GOOGLE_CLOUD_PROJECT });

const Storage = require('@google-cloud/storage');
const storage = new Storage.Storage({projectId: process.env.GOOGLE_CLOUD_PROJECT});

async function main() {
  console.log('Loading ignore words');
  try { 
    const bucket = storage.bucket(`${process.env.GOOGLE_CLOUD_PROJECT}-files`);
    const file = bucket.file('ignore.json');
    
    console.log('Loading file');
    await file.save(ignore.join('\n'));

    const dataset = bigquery.dataset('people');
    const [ds_check] = await dataset.exists();
    if (!ds_check) {
      console.log('Not people dataset');
      process.exit();
    }

    const metadata = {
      sourceFormat: 'CSV',
      schema: {
        fields: [
          { name: 'value', type: 'STRING' },
        ],
      },
      writeDisposition: 'WRITE_TRUNCATE',
      location: 'US',
    };

    console.log('Importing');
    const [job] = await dataset.table('ignore').load(file, metadata);

    const errs = job.status.errors;
    if (errs && errs.length) console.error(JSON.stringify(errs));
    else console.log(job.status.state);
  } catch(e) {
    console.log('Error loading ignore');
    console.log(JSON.stringify(e));
    if (e.response && e.response.loadErrors) for (const err of e.response.loadErrors.slice(0,10)) console.error(err.errors);
  }

  process.exit();
}

main();
