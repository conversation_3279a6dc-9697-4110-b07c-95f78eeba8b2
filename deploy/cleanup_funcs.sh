#!/bin/bash
PROJECT_ID=$1
SERVICE=$2

echo $PROJECT_ID

COURSE=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep course | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)
RELOAD=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep reload | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)
PROCESS=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep process | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)
IMPORT=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep import | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)
LEARN=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep learn | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)
UPDATE=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep update | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)
SUBSCRIPTION=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep subscription | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)
TUTOR=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep tutor | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)
NOTIFY=(`gcloud run revisions list --project=$PROJECT_ID --region=us-central1 | grep notify | grep -v yes | sed 's/^[^ ]* *//;s/ .*//'`)

if [ -z "$SERVICE" ] || [ "$SERVICE" = "course" ]; then
  echo $COURSE
  for VER in ${COURSE[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "reload" ]; then
  echo $RELOAD
  for VER in ${RELOAD[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "process" ]; then
  echo $PROCESS
  for VER in ${PROCESS[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "import" ]; then
  echo $IMPORT
  for VER in ${IMPORT[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "learn" ]; then
  echo $LEARN
  for VER in ${LEARN[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "update" ]; then
  echo $UPDATE
  for VER in ${UPDATE[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "subscription" ]; then
  echo $SUBSCRIPTION
  for VER in ${SUBSCRIPTION[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "tutor" ]; then
  echo $TUTOR
  for VER in ${TUTOR[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "notify" ]; then
  echo $NOTIFY
  for VER in ${NOTIFY[@]}; do
    gcloud run revisions delete $VER --project=$PROJECT_ID --quiet --region=us-central1 
  done
fi
