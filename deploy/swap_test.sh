#!/bin/sh
#get new versions that have zero traffic
DEFAULT_VERSION=`gcloud app versions list --project=askfora-test --service=default | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`
UPDATE_VERSION=`gcloud app versions list --project=askfora-test --service=update | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`
LEARN_VERSION=`gcloud app versions list --project=askfora-test --service=learn | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`

#test that it lights up with a direct request
while :
do
  curl -k -f -s "https://"$DEFAULT_VERSION".askfora-test.appspot.com/msg?mesage=about"
  if [ $? -eq 0 ]; then
    break
  fi
  sleep 5
done

#migrate to new verions
echo "Migrating to..."

echo "default "$DEFAULT_VERSION
gcloud app services set-traffic default --splits=$DEFAULT_VERSION=1 --split-by=cookie --project=askfora-test --quiet

echo "update "$UPDATE_VERSION
gcloud app services set-traffic update --splits=$UPDATE_VERSION=1 --project=askfora-test --quiet

echo "learn "$LEARN_VERSION
gcloud app services set-traffic learn --splits=$LEARN_VERSION=1 --project=askfora-test --quiet

#disable health checks
gcloud app update --split-health-checks --project=askfora-test --quiet

#get old versions that have 0 traffic
DEFAULT_VERSION=`gcloud app versions list --project=askfora-test --service=default | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`
UPDATE_VERSION=`gcloud app versions list --project=askfora-test --service=update | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`
LEARN_VERSION=`gcloud app versions list --project=askfora-test --service=learn | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`

#clean up old versions
gcloud app versions stop $DEFAULT_VERSION --service=default --project=askfora-test --quiet
gcloud app versions stop $UPDATE_VERSION --service=update --project=askfora-test --quiet
gcloud app versions stop $LEARN_VERSION --service=learn --project=askfora-test --quiet

