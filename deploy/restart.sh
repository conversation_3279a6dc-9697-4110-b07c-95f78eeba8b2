#/bin/sh
VER=`gcloud app versions list --service=default --project=$GOOGLE_CLOUD_PROJECT | grep SERVING$ | awk '{print $2}'`
gcloud app versions stop $VER --service=default --project=$GOOGLE_CLOUD_PROJECT --quiet
gcloud app versions start $VER --service=default --project=$GOOGLE_CLOUD_PROJECT --quiet

#test that it lights up
while :
do
  curl -k -f -s "https://"$VER"."$GOOGLE_CLOUD_PROJECT".appspot.com/msg?mesage=about"
  if [ $? -eq 0 ]; then
    break
  fi
  sleep 5
done


gcloud app services set-traffic default --splits=$VER=1 --split-by=cookie --project=$GOOGLE_CLOUD_PROJECT --quiet
