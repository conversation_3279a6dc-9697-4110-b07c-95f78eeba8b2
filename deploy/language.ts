import axios from 'axios';
import * as fs from 'fs';
import path from 'path';

async function main() {
  const lang_data = await axios.get('http://www.iana.org/assignments/language-subtag-registry/language-subtag-registry');
  const lang_list = lang_data.data.split('%%');
  const lang_map: {[key:string]: string} = {}
  for (const lang of lang_list) {
    if (lang_list.length) {
      const entry = lang.split('\n');
      let tag = null;
      let language = null;
      let etype = null;
      for (const key of entry) {
        const [a,v] = key.split(':').map(i => i.trim())
        switch(a) {
          case 'Type': etype = v; break;
          case 'Subtag': tag = v; break;
          case 'Description': language = v; break;
        }
      }

      console.log(`${etype} ${tag} ${language}`);
      if (etype === 'language' && tag && language) lang_map[tag] = language;
    }
  }
  fs.writeFileSync(path.resolve(__dirname, '..', 'src', 'files', 'language.json'), JSON.stringify(lang_map));
  process.exit();
}

main();
