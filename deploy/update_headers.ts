import * as fs from 'fs';
import path from 'path';
import headers from '../src/utils/headers';

const target = process.argv[2];
const test = target.includes('test');

const app = test ?  fs.readFileSync(path.resolve(__dirname, 'test.yaml.t'), 'utf-8').split('\n')
  : fs.readFileSync(path.resolve(__dirname, 'app.yaml.t'), 'utf-8').split('\n');


const send_headers = headers(test ? 'https://test.askfora.com' : 'https://askfora.com', 'GET');

let i;
for (i = 0; i < app.length; i++) {
  if (app[i].includes('http_headers')) break;
}

for (const h of send_headers) {
  app.splice(i + 1, 0, `    ${h[0]}: ${h[1]}`);
  i++;
}

fs.writeFileSync(test ? 'test.yaml' : 'app.yaml', app.join('\n'));
