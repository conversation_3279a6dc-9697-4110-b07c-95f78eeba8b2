#!/usr/bin/env node

const fs = require('fs');

source_package = JSON.parse(fs.readFileSync('package.json', {encoding:'utf-8'}));
route_package = {
  name: 'askfora',
  version: source_package.version,
  engines: source_package.engines,
  dependencies: source_package.dependencies,
  optionalDependencies: source_package.optionalDependencies,
  overrides: source_package.overrides,
  scripts: {
    postinstall: "PUPPETEER_CACHE_DIR=/workspace/.cache/puppeteer node node_modules/puppeteer/install.mjs",
    start: "DEBUG_LEVEL=info NODE_ENV=production node --max_old_space_size=8192 ./www.js"
  }
}

fs.writeFileSync('lib/www_package.json', JSON.stringify(route_package, null, 2));

route_package['main'] = 'functions.js';
route_package['scripts'] = { start: 'functions-framework --target=process' };
fs.writeFileSync('lib/process_package.json', JSON.stringify(route_package, null, 2));
delete route_package['main'];
delete route_package['scripts'];

// route_package['main'] = 'lib/run.js';
// route_package['scripts'] = { "start": "DEBUG_LEVEL=info NODE_ENV=production node --max_old_space_size=8192 ./run.js" };
// fs.writeFileSync('lib/run_package.json', JSON.stringify(route_package, null, 2));
// delete route_package['main'];
// delete route_package['scripts'];

//route_package['main'] = 'api.js';
//route_package['scripts'] = { start: 'functions-framework --target=slack' };
//fs.writeFileSync('lib/slack_package.json', JSON.stringify(route_package, null, 2));
//delete route_package['main'];
//delete route_package['scripts'];


// route_package['scripts'] = { start: "DEBUG_LEVEL=info NODE_ENV=production node --max-old-space-size=4000 ./run.js", };
// fs.writeFileSync('lib/run_package.json', JSON.stringify(route_package, null, 2));
// delete route_package['scripts'];
