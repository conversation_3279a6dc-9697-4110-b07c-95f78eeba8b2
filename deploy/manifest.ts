import config from '../src/config';
import { Profile } from '../src/types/saml';
import { getStrategy, getSamlOptions } from '../src/routes/saml';
import * as fs from 'fs';
import * as express from 'express';
const saml = require('passport-saml');

if(!process.env.NODE_ENV) {
  console.log('You must set the NODE_ENV environment variable to \'development\' or \'production\'');
  process.exit();
}

const cert = fs.readFileSync(process.argv[2], 'utf-8');

config.onLoad(async () => {
  console.log(cert);

  let options = await new Promise(c => {
    getSamlOptions({hostname:'saml.offline.askfora.com'} as express.Request, c);
  });
  const _saml = new saml.SAML(options);
  const strategy = new saml.Strategy({
      options, 
      entryPoint: 'http://saml.offline.askfora.com:8000/saml/login',
      issuer: 'askfora.com'
    },
    (profile: Profile, done) => done(null, profile),
  );
  const manifest = strategy.generateServiceProviderMetadata({hostname:'saml.offline.askfora.com'}, cert);

  console.log(manifest);
  process.exit();
});

config.loadConfig();
