#!/bin/sh
# curl -L -o src/web/js/eversign.js "https://s3.amazonaws.com/eversign-embedded-js-library/eversign.embedded.latest.js"
# curl -L -o src/web/js/eversign.js "https://static.eversign.com/js/embedded-signing.js"
# echo "\n\nmodule.exports = eversign;\n" >> src/web/js/eversign.js

contract_source=src/files/contract.json
contract_target=src/web/contract.pdf
if [ ! -f $contract_target ] || [ $contract_source -nt $contract_target ]; then
  node_modules/.bin/ts-node deploy/gen_contract.ts
fi

#curl -L -o src/web/js/react.js "https://unpkg.com/react@16/umd/react.production.min.js"
#curl -L -o src/web/js/react-dom.js "https://unpkg.com/react-dom@16/umd/react-dom.production.min.js"
