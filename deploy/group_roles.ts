import config from '../src/config';
import data from '../src/data';
import { Group } from '../src/types/globals';
import { NotificationType } from '../src/types/shared';

if (!['development', 'production'].includes(process.env.NODE_ENV)) {
  console.error('NODE_ENV must be set to development or production');
  process.exit();
}

function usage() {
  console.log(`Usage: ${process.argv[1]} group [add|remove role admin|notify] `);
  process.exit();
}

if (process.argv.length < 3) usage();

const group_id = process.argv[2];
const cmd = process.argv[3];
const role = process.argv[4];
const mod = process.argv[5];

const MODS = (Object.values(NotificationType) as any).concat('admin');
if (mod && !MODS.includes(mod)) {
  console.log(`Valid role values are ${MODS}`);
  process.exit();
}

async function main() {
  await config.loadConfig(true);
  let group:Group = await data.groups.byId(group_id);
  if (!group) {
    if (group_id.split('.').length > 1) group = await data.groups.byHost(group_id);
    else group = await data.groups.byHost(`${group_id}.askfora.com`);
  }
 
  if (group) {
    switch (cmd) {
      case 'add':
        if (role in group.roles) {
          if (mod === 'admin') {
            group.roles[role].admin = true;
            console.log(`Made ${role} of ${group_id} an admin`);
          } else if (mod) {
            if (!group.roles[role].notify.includes(mod as NotificationType)) {
              group.roles[role].notify.push(mod as NotificationType);
              console.log(`Added ${mod} to notify in ${role} of ${group_id}`);
            } else console.log(`${mod} is already notified in ${role} of ${group_id}`);
          }
        } else {
          if (mod === 'admin') {
            group.roles[role] = { notify:[], admin:true};
            console.log(`Added admin ${role} to ${group_id}`);
          } else {
            const notify = mod ? [mod as NotificationType] : [];
            group.roles[role] = {notify, admin:false};
            if (mod) console.log(`Added ${role} with notify ${mod} to ${group_id}`);
            else console.log(`Added ${role} to ${group_id}`);
          }
        }
        break;
      case 'remove':
        if (role in group.roles) {
          if (mod) {
            if (mod === 'admin') {
              group.roles[role].admin = false;
              console.log(`Removed admin from ${role} of ${group_id}`);
            } else {
              if (group.roles[role].notify.includes(mod as NotificationType)) {
                group.roles[role].notify = group.roles[role].notify.filter(n => n !== mod as NotificationType);
                console.log(`Removed notify ${mod} from ${role} from ${group_id}`);
              } else console.log(`Notify ${mod} doesn't exst in ${role} of ${group_id}`);
            }
          } else {
            delete group.roles[role];
            console.log(`Removed ${role} from ${group_id}`);
          }
        } else console.log(`Role ${role} doesn't exist in group ${group_id}`);
        break;
      default:
        console.log(JSON.stringify(group.roles, null, 2));
    }
    await data.groups.save(group);
  } else console.error(`Group ${group_id} not found`);

  process.exit();
}

main().catch(e => console.error(e));
