#!/usr/bin/env node
// saves any entity and writes to stdout
const Datastore = require('@google-cloud/datastore').Datastore;

if (!process.env.GOOGLE_CLOUD_PROJECT) {
  console.log('You must set the GOOGLE_CLOUD_PROJECT environment variable so I know where to save the configuration');
  process.exit();
}

if (!process.env.NODE_ENV) {
  console.log('You must set the NODE_ENV environment variable to \'development\' or \'production\'');
  process.exit();
}

if (process.argv.length < 2) {
  console.log(`Usage: ${process.argv[1]} [profile]`);
  process.exit();
}

const EXCLUDE_INDEXES = ['data_sources', 'settings', 'sync_tokens'];

const ds = new Datastore({projectId: process.env.GOOGLE_CLOUD_PROJECT});

async function reset(profile) {
  try {
    const key = ds.key(['User', `update_${profile}`]);
    key.namespace = profile;

    const [user] = await ds.get(key);

    if (user) {
      let reset_new = false;
      if (user.data_sources) {
        const data_sources = JSON.parse(Buffer.from(user.data_sources, 'base64').toString());
        for (const source in data_sources) {
          if (data_sources[source].newDone) {
            data_sources[source].newDone  = false;
            reset_new = true;
          }

          if (data_sources[source].calendars) {
            for (cal in data_sources[source].calendars) {
              if (data_sources[source].calendars[cal].newDone) {
                data_sources[source].calendars[cal].newDone = false;
                reset_new = true;
              }
            }
          }
        }

        if (reset_new) {
          user.data_sources = Buffer.from(JSON.stringify(data_sources)).toString('base64');
        }
      }

      if (user.refreshing || reset_new) {
        user.refreshing = false;

        const data = [];

        for (const att in user) {
          data.push({
            name: att,
            value: user[att],
            excludeFromIndexes:EXCLUDE_INDEXES.includes(att),
          });
        }
    
        process.stdout.write('\n');
        console.log(profile);
        await ds.save({ key, data });
        return true;
      }
    }

  } catch (err) {
    console.error(err, err.stack);
  }

  return false;
}

async function main() {
  let profile = process.argv[2];
  if (profile) await reset(profile);
  else {
    const query = ds.createQuery('user');
    const results = await ds.runQuery(query);
    let i = 0;
    let x = 0;
    if (results[0]) {
      console.log(`Checking ${results[0].length} accounts`);
      var resetters = [];
      for (const item of results[0]) {
        if (i % 10 === 0) process.stdout.write('.');
        if (i % 100 === 0) {
          await Promise.all(resetters);
          resetters.splice(0);
        }

        i++;
        resetters.push(new Promsise(async (c) => {
          const did_reset = await reset(item.id);
          if (did_reset) x++;
          c();
        }));
      }
    }
    process.stdout.write('\n');
    console.log(`Reset ${x}/${i} accounts`);
  }

  process.exit();
}

main().catch(err => `Unable to save entity ${err.message}`);
