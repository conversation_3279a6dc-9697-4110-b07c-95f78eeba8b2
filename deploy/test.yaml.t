# [START runtime]
runtime: custom
env: flex
runtime_config:
  operating_system: ubuntu20
  runtime_version: 20
resources:
  cpu: 1
  memory_gb: 4
  disk_size_gb: 20
automatic_scaling:
  min_num_instances: 1
  max_num_instances: 1
  target_concurrent_requests: 50
  cpu_utilization:
    target_utilization: 0.7
handlers:
- url: /$
  secure: always
  static_files: lib/public/index.html
  upload: lib/public/index.html
- url: /favicon\.icon
  secure: always
  static_files: lib/public/favicon.ico
  upload: lib/public/favicon.ico
- url: /(app|teams|start|learn|about|security|privacy|data|terms|okta|list|webinar|breadwinners|apply|skills|professional|network|talent|pricing|signup|login|info)$
  secure: always
  static_files: lib/public/index.html
  upload: lib/public/index.html
- url: /job/.*
  secure: always
  static_files: lib/public/index.html
  upload: lib/public/index.html
- url: /app/.*
  secure: always
  static_files: lib/public/app.html
  upload: lib/public/app.html
- url: /share
  secure: always
  static_files: lib/public/share.html
  upload: lib/public/share.html
- url: /robots.txt
  secure: always
  static_files: lib/files/robots.txt
  upload: lib/files/robots.txt
- url: /sitemap([0-9])*.xml
  secure: always
  script: auto
- url: /browserconfig.xml
  secure: always
  static_files: lib/public/browserconfig.xml
  upload: lib/public/browserconfig.xml
- url: /fonts/(.*)\.(ttf|woff|woff2)$
  secure: always
  static_files: lib/public/fonts/\1\.\2
  upload: lib/public/fonts/.*\..*
- url: /icons/(.*)\.png$
  secure: always
  static_files: lib/public/icons/\1\.png
  upload: lib/public/icons/.*\.png
- url: /images/(.*)\.png$
  secure: always
  static_files: lib/public/images/\1\.png
  upload: lib/public/images/.*\.png
- url: /images/about/(.*)\.png$
  secure: always
  static_files: lib/public/images/about/\1\.png
  upload: lib/public/images/about/.*\.png
- url: /images/v2/(.*)\.png$
  secure: always
  static_files: lib/public/images/v2/\1\.png
  upload: lib/public/images/v2/.*\.png
- url: /api/contacts/default/([a-z]+)/photo
  secure: always
  static_files: lib/files/initials/\1\.png
  upload: lib/files/initials/.*\.png
- url: /mask\.json
  secure: always
  static_files: lib/files/mask.json
  upload: lib/files/mask.json
- url: /ignore\.json
  secure: always
  static_files: lib/files/ignore.json
  upload: lib/files/ignore.json
- url: /remap\.json
  secure: always
  static_files: lib/files/remap.json
  upload: lib/files/remap.json
- url: /(.*\.(html|ico|json|pdf|js|map))$
  secure: always
  static_files: lib/public/\1
  upload: lib/public/.*\.(html|ico|json|pdf|js|map)$
  http_headers:
- url: /(.*LICENSE)$
  secure: always
  static_files: lib/public/.*\.LICENSE.txt
  upload: lib/public/.*.LICENSE.txt
- url: /(.*\.LICENSE.txt)$
  secure: always
  static_files: lib/public/.*\.LICENSE.txt
  upload: lib/public/.*.LICENSE.txt
- url: /manifest.webmanifest
  secure: always
  static_files: lib/public/manifest.webmanifest
  upload: lib/public/manifest.webmanifest
- url: /(.*\.(php|xml|env|txt|alfa))$
  secure: always
  static_files: lib/public/404.html
  upload: lib/public/404.html
- url: /wp-.*
  secure: always
  static_files: lib/public/404.html
  upload: lib/public/404.html
- url: /([0-9][0-9][0-9][0-9]/?.*)$
  secure: always
  static_files: lib/public/404.html
  upload: lib/public/404.html
- url: /\/?(\.git|.*\.tar\.gz|\.index\.php|\.DS_Store|gtag\/js|\/.git\/config|OLD|SITE|sito|sitio|Shop|Www|WWW|TEST|Test|Wp|server-status|_profiler|\.aws|lotteryHall|dev|Site|Old|Demo|123|copy|Askfora|askfora|sapi|frontend|debug|.circleci|singup|password|telescope|xxxss|v1|bin|feeds|tmp|temp|files|uploads|admin|site|sites|.hg|sr-db|Search-Replace-DB|search-replace-db|SearchReplaceWp|searchreplacewp|search-replace|find-replace|search|replace|sr|SR|rdb|srdb|SRDB|Search-Replace-DB-master-ohiu|blog|cms|web|wordpress|shop|test|www|WP|WORDPRESS|WordPress|test|STORE|store|SHOP|oldsite|old-site|old|login\.action|login\.php|newfolder|NEW|New|newfolder|main|home|FORUM|forum|demo|BLOG|Blog|bk|bc|bak|BACKUP|backup|Backup|bac|phpinfo|phpinfo\.php|wodpress|\.well-known|view-source|misc|\.vscode|magento_version|\.env|sftp-config.json|wp-content)\/?.*
  secure: always
  static_files: lib/public/404.html
  upload: lib/public/404.html
- url: /apple-touch-icon\.png
  secure: always
  static_files: lib/pubic/icons/icon-152.png
  upload: lib/pubic/icons/icon-152.png
network:
  session_affinity: true
  name: default
vpc_access_connector:
  name: "projects/askfora-test/locations/us-central1/connectors/test-session-cache"
# [END runtime]
