import config from '../src/config';
import data from '../src/data';
import { Tag } from '../src/types/items';

if (!['development', 'production'].includes(process.env.NODE_ENV)) {
  console.log('NODE_ENV must be set to development or production');
  process.exit();
}

function usage() {
  console.log(`Usage: ${process.argv[1]} group [add|remove tag value] `);
  process.exit();
}

if (process.argv.length < 3) usage();

const group_id = process.argv[2];
const cmd = process.argv[3];
const tag_type = process.argv[4];
const value = process.argv[5];

async function main() {
  await config.loadConfig(true);

  const group = await data.groups.byId(group_id);
  if (group) {
    if (cmd) {
      switch (cmd) {
        case 'add':
          console.log(`Adding ${tag_type} ${value}`);
          if (!group.search_mandatory_tags) group.search_mandatory_tags = [];
          group.search_mandatory_tags.push({
            type:tag_type,
            value,
          } as Tag);
          break;
        case 'remove':
          console.log(`Removing ${tag_type} ${value}`);
          if (group.search_mandatory_tags) {
            for (let index = 0; index < group.search_mandatory_tags.length; index++) {
              const tag = group.search_mandatory_tags[index];
              if (tag.type === tag_type && tag.value === value) {
                group.search_mandatory_tags.splice(index, 1);
                break;
              }
            }
          }
          break;
        default:
          console.log(`Unknown cmd ${cmd}`);
      }
      await data.groups.save(group);
    } else if (group.search_mandatory_tags) console.log(JSON.stringify(group.search_mandatory_tags, null, 2));
    else console.log(`Group ${group.id} (${group.name}) has no tags`);
  } else console.log(`Group ${group_id} not found`);

  process.exit();
}

main();
