#!/usr/bin/env -S node --require ts-node/register

import config from '../src/config';
import data from '../src/data';
import ForaUser from '../src/utils/user';

process.env.MEMCACHE_URL="localhost:11211";

if (!['development', 'production'].includes(process.env.NODE_ENV)) {
  console.log('NODE_ENV must be set to development or production');
  process.exit();
}

function usage() {
  console.log(`Usage: ${process.argv[1]} user [provider] [account] [group add|rem]`);
  process.exit();
}

if (process.argv.length < 3) usage();

let user_id = process.argv[2];
const provider = process.argv[3];
const account = process.argv[4];
const group = process.argv[5];
const act = process.argv[6];

async function main() {
  await config.loadConfig(true, {MEMCACHE_URL:'localhost:11211'});
  if (user_id.includes('@')) {
    const global = await data.users.globalByEmail(user_id);
    if (global && global.length) user_id = global[0].id;
    else {
      console.log(`User ${user_id} not found`);
      process.exit();
    }
  }
  const user_data = await data.users.byId(new ForaUser(user_id), user_id);
  const user = new ForaUser(user_id);
  user.restore(user_data);

  if (provider) {
    if(account) {
      if (group && act) {
        if (act[0] === 'r') {
          user.accounts[provider][account].group = '';
        } else {
          user.accounts[provider][account].group = group;
        }
        await data.users.save(user);
        console.log(provider);
          console.log(`\t${account} group: ${user.accounts[provider][account].group}`);
      } else {
        console.log(provider);
          console.log(`\t${account} group: ${user.accounts[provider][account].group}`);
      }
    } else {
      console.log(provider);
      for (const account in user.accounts[provider]) {
        console.log(`\t${account} group: ${user.accounts[provider][account].group}`);
      }
    }
  } else {
    for (const provider in user.accounts) {
      console.log(provider);
      for (const account in user.accounts[provider]) {
        console.log(`\t${account} group: ${user.accounts[provider][account].group}`);
      }
    }
  }
  process.exit()
}

main();
