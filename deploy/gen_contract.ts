const fs = require('fs');
const path = require('path');
const contract = require('../src/utils/contract');
const funcs = require('../src/utils/funcs');
const datetime = require('../src/utils/datetime');

const argv = require('yargs').argv;

if (argv.help) {
  console.log(`Usage: ${process.argv[1]} --client --client_email --contractor --contractor_email --context --date --outfile`);
  process.exit();
}

async function main() {
  const contract_source = path.resolve(__dirname, '../src/files/contract.json');
  const contract_target = path.resolve(__dirname, argv.outfile ? argv.outfile : '../src/web/contract.pdf');

  if (!fs.existsSync(contract_target) || 
    new Date(fs.statSync(contract_source).mtime) > new Date(fs.statSync(contract_target).mtime)) { 

    const cvars = {
      CLIENT          : argv.client ? argv.client : 'AskFora Co',
      CLIENT_EMAIL    : argv.client_email ? argv.client_email : '<EMAIL>',
      CONTRACTOR      : argv.contractor ? argv.contractor : 'Demo Contractor',
      CONTRACTOR_EMAIL: argv.contractor_email ? argv.contractor_email : '<EMAIL>',
      CONTEXT         : argv.context ? argv.context : 'writing contracts',
      DATE            : new Date(argv.date ? argv.date : '2018-02-24').toLocaleString('en-US', datetime.LOCALE_MONTH_DAY_YEAR('America/New_York')),
    };

    const signers = [
      {
        id   : '1',
        name : cvars.CLIENT,
        email: cvars.CLIENT_EMAIL,
      },
      {
        id   : '2',
        name : cvars.CONTRACTOR,
        email: cvars.CONTRACTOR_EMAIL,
      },
    ];

    const outs = fs.createWriteStream(contract_target);
    const flush = new Promise(c => outs.on('close', c));
    await contract.createContract(JSON.parse(fs.readFileSync(contract_source)),
                                  cvars, signers, {}, outs);
    await flush;

  }
  process.exit();
}

main();
