#!/bin/bash
PROJECT_ID=$1
CREDS=$2
SERVICE=$3

if [ -z "$SERVICE" ]; then
  rm askfora_deploy_*.out
fi

###
# Setup
### 

#if [[ -z "$SERVICE" || "$SERVICE" = "default" ]]; then
#  #get old versions that have 0 traffic
#  DEFAULT_VERSION=`gcloud app versions list --project=$PROJECT_ID --service=default | grep STOPPED$ | sort -k 3 | head -1 | awk '{print $2}'`
#
#  #clean up old versions
#  if [ ! -z "$DEFAULT_VERSION" ]; then
#    gcloud app versions delete $DEFAULT_VERSION --service=default --project=$PROJECT_ID --quiet
#  fi
#fi
#
###
# deploy
###
# special case for test
# APP_YAML=app.yaml
# UPDATE_YAML=update.yaml
VPC_CONNECTOR=production-session-cache
if [ "$PROJECT_ID" = "askfora-test" ]; then
#  APP_YAML=test.yaml
  # UPDATE_YAML=update_test.yaml
  VPC_CONNECTOR=test-session-cache
fi

echo
date

DEFAULT_ID=''
if [ -z "$SERVICE" ] || [ "$SERVICE" = "default" ]; then
  echo "Deploying $SERVICE"
  # echo Updating headers with $PROJECT_ID
  # ./ts-node deploy/update_headers.ts $PROJECT_ID

#  ln -sf ../$APP_YAML lib/.
#  ln -sf ../deploy/index.yaml lib/.
#  ln -sf ../deploy/dispatch.yaml lib/.
#  ln -sf ../deploy/Dockerfile lib/.
#  ln -sf ../deploy/nginx.conf lib/.
  ln -sf ../node_modules lib/.
  ln -sf www_package.json lib/package.json
  ln -sf ../.gcloudignore lib/.
  ln -sf ../.cache lib/.

  cd lib

  pushd public
  if [[ -f "t" ||  -d "t" ]]; then rm -rf t; fi
  mkdir t
  cp index.html t/.
  ../../deploy/sync_catalog.ts t

  ln -sf index.html p
  ln -sf index.html c
  ln -sf index.html terms 
  ln -sf index.html faq 
  ln -sf index.html data 
  ln -sf index.html privacy 
  ln -sf index.html security 
  ln -sf index.html about 
  popd

#  if [ -z "$SERVICE" ]; then
#    gcloud app deploy $APP_YAML ../deploy/dispatch.yaml --project=$PROJECT_ID --quiet --no-promote &> ../askfora_deploy_default.out &
#    DEFAULT_ID=$!
#  else
#    gcloud app deploy $APP_YAML ../deploy/dispatch.yaml --project=$PROJECT_ID --quiet --no-promote &> ../askfora_deploy_default.out &
#    DEFAULT_ID=$!
#  fi
#
  if [ "$PROJECT_ID" = "askfora-test" ]; then
    gsutil -m rsync -d -r -J public/ gs://askfora-test-www &> ../askfora_deploy_default.out & 
    DEFAULT_ID=$!
  else
    gsutil -m rsync -d -r -J public/ gs://askfora-tutor &> ../askfora_deploy_default.out & 
    DEFAULT_ID=$!
  fi

  cd ..
fi

HOME_ID=''
if [ "$SERVICE" = "home" ]; then
  echo "Deploying $SERVICE"

  ln -sf ../node_modules lib/.
  ln -sf www_package.json lib/package.json
  ln -sf ../.gcloudignore lib/.
  ln -sf ../.cache lib/.

  cd lib

  pushd homepage
  ln -sf index.html terms 
  ln -sf index.html faq 
  ln -sf index.html data 
  ln -sf index.html privacy 
  ln -sf index.html security 
  ln -sf index.html about 
  popd

  gsutil -m rsync -d -r -J homepage/ gs://askfora-www &> ../askfora_deploy_home.out & 
  HOME_ID=$!

  cd ..
fi


###
# functions
###

#if [[ -z "$SERVICE" || "$SERVICE" = "proc" ]]; then
  #bleed subscriptions
#  PROC_SUB=`gcloud pubsub subscriptions list --project=$PROJECT_ID  | grep name | grep proc | tail -1 | awk -F\/ '{print $4}'`
#  gcloud pubsub subscriptions seek $PROC_SUB --project=$PROJECT_ID --time=`date -Idate`

#  ln -sf funcs_package.json ./lib/package.json

 # gcloud functions deploy proc --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID --trigger-topic proc-production --source=lib --runtime nodejs20 --region us-central1 --memory=4096MB --timeout=540s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet

 # if [ -f "$CREDS" ]; then
 #   NODE_ENV=production GOOGLE_CLOUD_PROJECT=$PROJECT_ID GOOGLE_APPLICATION_CREDENTIALS=$CREDS deploy/reset_refreshing.js
 # else
 #   NODE_ENV=production deploy/reset_refreshing.js
 # fi
#fi

PROCESS_ID=''
RELOAD_ID=''
IMPORT_ID=''
LEARN_ID=''
COURSE_ID=''
UPDATE_ID=''
SUBSCRIPTION_ID=''
TUTOR_ID=''
NOTIFY_ID=''

rm -rf slib
cp -r lib slib
rm -rf slib/public
ln -sf process_package.json ./slib/package.json

#if [ -z "$SERVICE" ] || [ "$SERVICE" = "process" ] || [ "$SERVICE" = "functions" ]; then
#  echo
#  echo Upgrading $PROJECT_ID Process
#  PROC_SUB=`gcloud pubsub subscriptions list --project=$PROJECT_ID  | grep name | grep process | tail -1 | awk -F\/ '{print $4}'`
#  if [[ ! -z "$PROC_SUB" ]]; then gcloud pubsub subscriptions seek $PROC_SUB --project=$PROJECT_ID --time=`date -Idate`; fi
#
#  gcloud beta functions deploy process --gen2 --memory=8Gi --cpu=2 --entry-point process --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=8192" --trigger-topic process-production --source=slib --runtime nodejs20 --region us-central1 --timeout=540s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet &> askfora_deploy_process.out &
#  PROCESS_ID=$!
#fi
#
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "reload" ] || [ "$SERVICE" = "functions" ]; then
#  echo
#  echo Upgrading $PROJECT_ID Reload
#
#  gcloud functions deploy reload --gen2 --entry-point reload --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=8192" --trigger-http --source=slib --runtime nodejs20 --region us-central1 --memory=8Gi --timeout=3600s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet &> askfora_deploy_reload.out &
#  RELOAD_ID=$!
#fi
#
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "import" ] || [ "$SERVICE" = "functions" ]; then
#  echo
#  echo Upgrading $PROJECT_ID Import
#
#  gcloud beta functions deploy import --gen2 --entry-point import --cpu=4 --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=8192" --trigger-http --source=slib --runtime nodejs20 --region us-central1 --memory=8Gi --timeout=3600s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet &> askfora_deploy_import.out &
#  IMPORT_ID=$!
#
#  #ln -s deploy/run.yaml .
#  #ln -s ../run.yaml slib/.
#  #ln -sf run_package.json slib/package.json
#  #ln -sf ../.gcloudignore slib/.
#
#  #cd slib
#  #gcloud app deploy run.yaml ../deploy/dispatch.yaml --project=$PROJECT_ID --quiet &> ../askfora_deploy_import.out &
#  #IMPORT_ID=$!
#  #cd ..
#fi
#
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "learn" ] || [ "$SERVICE" = "functions" ]; then
#  echo
#  echo Upgrading $PROJECT_ID Learn
#
#  gcloud functions deploy learn --gen2 --entry-point learn --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=8192" --trigger-http --source=slib --runtime nodejs20 --region us-central1 --memory=8Gi --timeout=3600s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet &> askfora_deploy_learn.out &
#  LEARN_ID=$!
#fi
#
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "update" ] || [ "$SERVICE" = "functions" ]; then
#  echo
#  echo Upgrading $PROJECT_ID Update
#
#  gcloud functions deploy update --gen2 --entry-point update --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=8192" --trigger-http --source=slib --runtime nodejs20 --region us-central1 --memory=8Gi --timeout=3600s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet &> askfora_deploy_update.out &
#  UPDATE_ID=$!
#fi
#
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "course" ] || [ "$SERVICE" = "functions" ]; then
#  echo
#  echo Upgrading $PROJECT_ID Course
#
#  gcloud functions deploy course --gen2 --entry-point course --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=8192" --trigger-http --source=slib --runtime nodejs20 --region us-central1 --memory=8Gi --cpu=4 --timeout=3600s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet &> askfora_deploy_course.out &
#  COURSE_ID=$!
#fi
#
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "subscription" ] || [ "$SERVICE" = "functions" ]; then
#  echo
#  echo Upgrading $PROJECT_ID Subscription
#
#  gcloud functions deploy subscription --gen2 --entry-point subscription --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=8192" --trigger-http --source=slib --runtime nodejs20 --region us-central1 --memory=8Gi --cpu=4 --timeout=3600s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet &> askfora_deploy_subscription.out &
#  SUBSCRIPTION_ID=$!
#fi
#
if [ -z "$SERVICE" ] || [ "$SERVICE" = "tutor" ] || [ "$SERVICE" = "functions" ]; then
  echo
  echo Upgrading $PROJECT_ID Tutor

  gcloud functions deploy tutor --gen2 --entry-point tutor --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=4096" --trigger-http --source=slib --runtime nodejs20 --region us-central1 --memory=3Gi --timeout=3600s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --allow-unauthenticated --quiet &> askfora_deploy_tutor.out &
  TUTOR_ID=$!
fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "notify" ] || [ "$SERVICE" = "functions" ]; then
  echo
  echo Upgrading $PROJECT_ID Notify

  gcloud functions deploy notify --gen2 --entry-point notify --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID,NODE_OPTIONS="--max_old_space_size=4096" --trigger-http --source=slib --runtime nodejs20 --region us-central1 --memory=3Gi --timeout=3600s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --quiet &> askfora_deploy_notify.out &
  NOTIFY_ID=$!
fi

tail -f askfora_deploy_*.out &
TAIL_ID=$!

if [ ! -z "$PROCESS_ID" ] || [ ! -z "$RELOAD_ID" ] || [ ! -z "$IMPORT_ID" ] || [ ! -z "$LEARN_ID" ] || [ ! -z "$UPDATE_ID" ] || [ ! -z "$COURSE_ID" ] || [ ! -z "$SUBSCRIPTION_ID" ] || [ ! -z "$TUTOR_ID" ] || [ ! -z "$NOTIFY_ID" ]; then
  echo Waiting on $PROCESS_ID $RELOAD_ID $IMPORT_ID $LEARN_ID $UPDATE_ID $COURSE_ID $SUBSCRIPTION_ID $TUTOR_ID $NOTIFY_ID $HOME_ID
  wait $PROCESS_ID $RELOAD_ID $IMPORT_ID $LEARN_ID $UPDATE_ID $COURSE_ID $SUBSCRIPTION_ID $TUTOR_ID $NOTIFY_ID $HOME_ID
fi

#if [ -z "$SERVICE" ] || [ "$SERVICE" = "process" ] || [ "$SERVICE" = "functions" ]; then
#  if [ "$PROJECT_ID" = "askfora-test" ]; then
#    gcloud run services update process --concurrency=15 --max-instances=15 --region us-central1 --project=$PROJECT_ID --quiet
#  else
#    gcloud run services update process --concurrency=20 --max-instances=20 --region us-central1 --project=$PROJECT_ID --quiet
#  fi
#
#  #clean up refreshing flags
#  if [ -f "$CREDS" ]; then
#    NODE_ENV=production GOOGLE_CLOUD_PROJECT=$PROJECT_ID GOOGLE_APPLICATION_CREDENTIALS=$CREDS deploy/reset_refreshing.js
#  else
#    NODE_ENV=production deploy/reset_refreshing.js
#  fi
#fi

#if [ -z "$SERVICE" ] || [ "$SERVICE" = "reload" ] || [ "$SERVICE" = "functions" ]; then
#  gcloud run services update reload --concurrency=5 --max-instances=5 --region us-central1 --project=$PROJECT_ID --quiet
#fi
#
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "course" ] || [ "$SERVICE" = "functions" ]; then
#  gcloud run services update course --concurrency=100 --max-instances=100 --region us-central1 --project=$PROJECT_ID --quiet
#fi
#
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "subscription" ] || [ "$SERVICE" = "functions" ]; then
#  gcloud run services update subscription --concurrency=100 --max-instances=1 --region us-central1 --project=$PROJECT_ID --quiet
#fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "tutor" ] || [ "$SERVICE" = "functions" ]; then
  gcloud run services update tutor --concurrency=50 --max-instances=10 --region us-central1 --project=$PROJECT_ID --quiet
fi

#if [ -z "SERVICE" ] || [ "$SERVICE" = "import" ] || [ "$SERVICE" = "functions" ]; then
#  gcloud run services update import --no-cpu-throttling --project=$PROJECT_ID --quiet
#fi

if [ -z "$SERVICE" ] || [ "$SERVICE" = "functions" ]; then
  deploy/cleanup_funcs.sh $PROJECT_ID
else
  deploy/cleanup_funcs.sh $PROJECT_ID $SERVICE
fi

###########
# Temp disable slack
####
#if [ -z "$SERVICE" ] || [ "$SERVICE" = "slack" ]; then
#  echo
#  echo Upgrading $PROJECT_ID Slack

#  ln -sf slack_package.json ./lib/package.json

#  gcloud beta functions deploy slack --gen2 --entry-point slack --set-env-vars GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GCLOUD_PROJECT=$PROJECT_ID --trigger-http --source=lib --runtime nodejs20 --region us-central1 --memory=4096MB --timeout=540s --project=$PROJECT_ID --vpc-connector $VPC_CONNECTOR --allow-unauthenticated --quiet
#fi
###########

if [ ! -z "$DEFAULT_ID" ]; then
  echo Waiting on $DEFAULT_ID
  wait $DEFAULT_ID
  if [ "$PROJECT_ID" = "askfora-test" ]; then
    gsutil -m setmeta -h "content-type:application/json" "gs://askfora-test-www/t/*"
    gsutil -m setmeta -h "content-type:text/html" "gs://askfora-test-www/t/index.html"
  else
    gsutil -m setmeta -h "content-type:application/json" "gs://askfora-tutor/t/*"
    gsutil -m setmeta -h "content-type:text/html" "gs://askfora-tutor/t/index.html"
  fi
fi

if [ ! -z "$HOME_ID" ]; then
  echo Waiting on $HOME_ID
  wait $HOME_ID
fi

#if [ -z "$SERVICE" ] || [ "$SERVICE" = "default" ]; then
#  rm *.yaml
#  rm Dockerfile
#fi

date

kill $TAIL_ID

###
# Migrate
###
#get new versions that have zero traffic
#DEFAULT_VERSION=`gcloud app versions list --project=$PROJECT_ID --service=default | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`
#DEFAULT_VERSION=`gcloud app versions list --project=$PROJECT_ID --service=default | sort -r -k 4 | head -2 | tail -1 | awk '{print $2}'`

#test that it lights up with a direct request
#if [[ -z "$SERVICE" || "$SERVICE" = "default" ]]; then
#  echo
#  echo "Checking for new version" $DEFAULT_VERSION
#
#  while :
#  do
#    curl -k -f -s "https://"$DEFAULT_VERSION"."$PROJECT_ID".appspot.com/msg?mesage=about"
#    if [ $? -eq 0 ]; then
#      break
#    fi
#    sleep 5
#  done
#
#  echo
#  echo "Warming up new version with a few pings"
#  curl -k -f -s "https://"$DEFAULT_VERSION"."$PROJECT_ID".appspot.com/ping"
#  curl -k -f -s "https://"$DEFAULT_VERSION"."$PROJECT_ID".appspot.com/ping"
#  curl -k -f -s "https://"$DEFAULT_VERSION"."$PROJECT_ID".appspot.com/ping"
#
#  #migrate to new verions
#  echo
#  echo "Migrating to" $DEFAULT_VERSION
#  gcloud app services set-traffic default --splits=$DEFAULT_VERSION=1 --split-by=cookie --project=$PROJECT_ID --quiet
#
#  OLD_VERSIONS=(`gcloud app versions list --project=$PROJECT_ID --service=default | sort -r -k 4 | grep -v $DEFAULT_VERSION | grep -v VERSION | awk '{print $2}'`)
#  if [ ! -z "$OLD_VERSIONS" ]; then
#    gcloud app versions stop $OLD_VERSIONS --project=$PROJECT_ID --service=default --quiet
#  fi
#
#  echo "Deployed "$DEFAULT_VERSION
#fi
