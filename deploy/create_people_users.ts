import config from '../src/config';
import data from '../src/data';

import ForaUser from '../src/session/user';

import { User } from '../src/types/globals';

import parsers from '../src/utils/parsers';

const profile = process.argv[2];
const group = process.argv[3];

console.log(profile);

async function main() {
  await config.loadConfig(false, {MEMCAHCE_URL: 'localhost:11211'});

  const user = new ForaUser(profile);
  await data.users.init(user, false);

  const people: Person[] = await data.plugins.storagePlugin().people(user);

  for(const person of people) {
    const emails = parsers.findEmail(person.comms);
    if (!emails || !emails.length) {
      console.log(`No email for ${person.id}`);
      continue;
    }

    // look for a global user
    let global_users = await data.users.findByEmail(emails);
    let global_user;
    if (!global_users || !global_users.length) {
      global_user = new User({
        id: profile,
        email,
        profile,
        name: person.displayName.split(' ')[0],
        groups: user_groups[id],
        auth_ids: [`${AuthProviders.Email}_${profile}`],
      });

      await data.users.globalRegister(global_user, 'group');
    } else if(global_users.length > 1) {
      console.log(`Matched multiple users for ${person.id}: ${JSON.stringify(global_users.map(g => g.profile))}`);
      continue;
    } else global_user = global_users[0];

    // init the user without tokens
    const new_user = new ForaUser(global_user.profile);
    new_user.name = global_user.name;
    new_user.email = global_user.email;
    await data.users.init(new_user, false);
    
    // update groups
    if (!new_user.groups) new_user.groups = {};
    global_user.groups.forEach(group_id => { new_user.groups[group_id] = [] });

    // save user
    await data.users.save(new_user, true, true, false);
    await data.users.globalRegister(new_user, 'group');
    await data.users.saveGroup(new_user, new_user.groups);

    // save self
    const self = new Person(person);
    self.self = true;
    await data.people.savePerson(new_user, self);

    let user_group_ids = user.groups && global_user.groups ? _.intersection(Object.keys(user.groups), global_user.groups) : [];
    if(!user_group_ids.length && global_user.groups) {
      for (const group of groups) {
        if (group.email_domain && global_user.groups.includes(group.id)) {
          const match = group.email_domain.find(d => new_user.email.endsWith(d));
          if (match) {
            user_group_ids= [group.id];
            break;
          }
        }
      }
    }

    await data.users.generateVanity(new_user, self, user_group_ids ? groups.find(g => user_group_ids.includes(g.id)) : undefined);
    await data.users.saveVanity(new_user, self);
    await data.users.save(new_user, false, false, false);
    setTimeout(() => data.profiles.get(new_user, new_user.vanity, true), 1000);
  }
}

// main();
