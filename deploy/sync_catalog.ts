#!/usr/bin/env -S node --require ts-node/register

import fs from 'fs';
import path from 'path';

import config from '../src/config';
import data from '../src/data';
import ForaUser from '../src/session/user';

import { tutorialInfo } from '../src/utils/info';

async function main() {
  await config.loadConfig(true, {MEMCACHE_URL: 'localhost:11211'});

  const guest_user = new ForaUser('tutorial');
  const catalog = await data.tutorials.load(guest_user);

  console.log(`Writing ${catalog.length} tutorials to ${process.argv[2]}`);

  const tutorial_info = catalog.map(tutorialInfo);
    // don't include  lessons
  tutorial_info.forEach(ti => delete ti.lesson_set);
 

  fs.writeFileSync(path.resolve(process.argv[2], 'catalog'), JSON.stringify(tutorial_info));
  catalog.forEach(c => {
    fs.writeFileSync(path.resolve(process.argv[2], c.id), JSON.stringify(c));
  });

  process.exit();
}

main();
