#!/bin/sh
tools/remote/load_entity.ts tutorial src/files/tutorial_accountability.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_boss.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_collaboration.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_facilitating.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_goals.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_manager.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_meetings.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_opportunity.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_presentations.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_professional.json Tutorial
tools/remote/load_entity.ts tutorial src/files/tutorial_team.json Tutorial

# tools/remote/load_entity.ts tutorial src/files/tutorial_ml.json
# tools/remote/load_entity.ts tutorial src/files/tutorial_sample.json
