#!/usr/bin/env node
// saves any entity and writes to stdout
const fs = require('fs');
const path = require('path');
const uuid = require('uuid/v4');
const Datastore = require('@google-cloud/datastore');

if (!process.env.GOOGLE_CLOUD_PROJECT) {
  console.log('You must set the GOOGLE_CLOUD_PROJECT environment variable so I know where to save the configuration');
  process.exit();
}

if (!process.env.NODE_ENV) {
  console.log('You must set the NODE_ENV environment variable to \'development\' or \'production\'');
  process.exit();
}

const ds = new Datastore({projectId: process.env.GOOGLE_CLOUD_PROJECT});

async function main() {
  const key = ds.key(['config', process.env.NODE_ENV]);
  const configs = await ds.get(key);
  let config = configs && configs.length ? configs[0] : null;
  if (config) {
    fs.writeFileSync(path.resolve(__dirname, '..', 'config.bak'), JSON.stringify(config, null, 2));
  }
  else {
    config = {
      MEMCACHE_URL: 'localhost:6570',
      NOTIFY_TOKEN: uuid(),
      LEARN_TOKEN: uuid(),
      UPDATE_TOKEN: uuid(),
      COOKIE: 'ForaLocal',
      PROJECT_URL: 'http://localhost:8000/job',
      CONTRACT_URL: 'http://localhost:8000/contract',
      SECRET: uuid(),
    }
  }

  delete config.SERVICE_KEY;

  try {
    if (!config.SERVICE_KEY) config.SERVICE_KEY = JSON.parse(fs.readFileSync(path.resolve(__dirname, '..', 'google_api.json'))).private_key;
  } catch (e) {
    console.log('Not setting SERVICE_KEY');
  }

  const data = [];
  for (const name in config) {
    data.push({
      name,
      value: config[name],
      excludeFromIndexes:true
    })
  }


  await ds.save({key, data});
  process.exit();
}

main();