#!/usr/bin/env -S node --require ts-node/register

import yargs from 'yargs';

import config from '../src/config';
import ForaUser from '../src/session/user';
import data from '../src/data';

import { 
  checkSubscription,
  createCustomer,
  getCustomer,
  deleteCustomer,
  lookupUser,
  subscribe,
  cancelSubscription,
  subscriptionToken,
} from '../src/sources/hyperline_controller';

let profile = (yargs.argv['profile'] || yargs.argv['p']) as string;
const check_sub = (yargs.argv['check'] || args.argv['h']) as boolean;
const create = (yargs.argv['create'] || args.argv['n']) as boolean;
const info = (yargs.argv['info'] || args.argv['i']) as boolean;
const delete_customer = (yargs.argv['delete'] || args.argv['d']) as boolean;
const lookup = (yargs.argv['lookup'] || args.argv['l']) as boolean;
const subscribe = (yargs.argv['subscribe'] || args.argv['s']) as boolean;
const cancel = (yargs.argv['cancel'] || args.argv['c']) as boolean;
const token = (yargs.argv['token'] || args.argv['t']) as boolean;

if (!['development', 'production'].includes(process.env.NODE_ENV)) {
  console.log('NODE_ENV must be set to development or production');
  process.exit();
}


async function main() {
  await config.loadConfig(false, {MEMCACHE_URL: 'localhost:11211'});

  const key = config.get('HYPERLINE_API_KEY');

  if (!key) {
    console.log(`Subscriptions not enabled on ${config.get('GOOGLE_CLOUD_PROJECT')}`);
    process.exit(); 
  }

  if (profile.includes('@')) {
    const global = await data.users.globalByEmail(profile);
    if (global && global.length) profile = global[0].id;
    else {
      console.log(`User ${profile} not found`);
      process.exit();
    }
  }

  const user = new ForaUser(profile);
  const did_init = await data.users.init(user, false);

  if (!did_init) {
    console.error(`Error loading user ${profile}`);
    process.exit();
  }

  const me = await data.people.getUserPerson(user);

  if (check_sub) {
    const sub = await checkSubscription(user, me);
    console.log(sub ? 'Subscribed' : 'Not subscribed');
  } else if(create) {
    const info = await createCustomer(user, me);
    console.log(JSON.stringify(info));
  } else if(info) {
    const info = await getCustomer(
  } else if(delete_customer) {
  } else if(lookup) {
  } else if(subscribe) {
  } else if(cancel) {
  } else if(token) {
  } else {
  }

  process.exit();

}

main();

