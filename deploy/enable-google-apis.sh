#! /bin/bash

set -e

echo "Enabling contacts API"
gcloud services enable contacts.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling calendar API"
gcloud services enable calendar-json.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling custom search API"
gcloud services enable customsearch.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling google+ API"
gcloud services enable plus.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling drive API"
gcloud services enable drive.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling gmail API"
gcloud services enable gmail.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling people API"
gcloud services enable people.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling tasks API"
gcloud services enable tasks.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling bigquery API"
gcloud services enable bigquery-json.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling cloud API"
gcloud services enable cloudapis.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling cloud datastore API"
gcloud services enable datastore.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling cloud pub/sub API"
gcloud services enable pubsub.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling cloud sql API"
gcloud services enable sql-component.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling cloud storage API"
gcloud services enable storage-component.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling cloud storage json API"
gcloud services enable storage-api.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling service management API (for deploying)"
gcloud services enable servicemanagement.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling stackdriver API (for deploying)"
gcloud services enable stackdriver.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling stackdriver debugger API (for deploying)"
gcloud services enable clouddebugger.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling stackdriver logging API (for deploying)"
gcloud services enable logging.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling stackdriver monitoring API (for deploying)"
gcloud services enable monitoring.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling stackdriver tracing API (for deploying)"
gcloud services enable cloudtrace.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling cloud vision API"
gcloud services enable vision.googleapis.com --project=$GOOGLE_CLOUD_PROJECT

echo "Enabling KMS API"
gcloud services enable cloudkms.googleapis.com --project=$GOOGLE_CLOUD_PROJECT
