#get old versions that have 0 traffic
DEFAULT_VERSION=`gcloud app versions list --project=askfora-production --service=default | grep STOPPED$ | sort -k 3 | head -1 | awk '{print $2}'`
UPDATE_VERSION=`gcloud app versions list --project=askfora-production --service=update | grep STOPPED$ | sort -k 3 | head -1 | awk '{print $2}'`
LEARN_VERSION=`gcloud app versions list --project=askfora-production --service=learn | grep STOPPED$ | sort -k 3 | head -1 | awk '{print $2}'`

#start the old verions
echo "Starting..."
echo "default "$DEFAULT_VERSION
gcloud app versions start $DEFAULT_VERSION --service=default --project=askfora-production --quiet

gcloud app versions start $UPDATE_VERSION --service=update --project=askfora-production --quiet
echo "update "$UPDATE_VERSION

echo "learn "$LEARN_VERSION
gcloud app versions start $LEARN_VERSION --service=learn --project=askfora-production --quiet

#migrate to old verions
echo "Migrating to..."

echo "default "$DEFAULT_VERSION
gcloud app services set-traffic default --splits=$DEFAULT_VERSION=1 --split-by=cookie --project=askfora-production --quiet

echo "update "$UPDATE_VERSION
gcloud app services set-traffic update --splits=$UPDATE_VERSION=1 --project=askfora-production --quiet

echo "learn "$LEARN_VERSION
gcloud app services set-traffic learn --splits=$LEARN_VERSION=1 --project=askfora-production --quiet

#get new versions that now have 0 traffic
DEFAULT_VERSION=`gcloud app versions list --project=askfora-production --service=default | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`
UPDATE_VERSION=`gcloud app versions list --project=askfora-production --service=update | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`
LEARN_VERSION=`gcloud app versions list --project=askfora-production --service=learn | grep SERVING$ | grep "0\.00" | sort -r -k 4 | head -1 | awk '{print $2}'`

#clean up old versions
gcloud app versions stop $DEFAULT_VERSION --service=default --project=askfora-production --quiet
gcloud app versions stop $UPDATE_VERSION --service=update --project=askfora-production --quiet
gcloud app versions stop $LEARN_VERSION --service=learn --project=askfora-production --quiet

